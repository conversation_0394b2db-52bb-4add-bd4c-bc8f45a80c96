<?php

namespace App\Console\Commands;

use App\DrmProduct;
use App\Models\ChannelProduct;
use App\Models\DrmCategory;
use App\Services\DRMProductService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class BackupProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'backup:restore';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Executing manual script';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     *
     * execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->restoreImages();
    }

    private function backupMain()
    {
        $user_id = 3738;

        $products = DB::connection('backup')->table('drm_products')->where([
            'user_id' => $user_id
        ])->cursor();

        $count = 0;
        foreach ($products as $product) {
            $data = (array)$product;

            $exists = DB::table('drm_products')->where('id',$data['id'])->exists();
            if(!$exists){
                DB::table('drm_products')->updateOrInsert($data);
            }

            $productCategories = DB::connection('backup')->table('drm_product_categories')->where([
                'product_id' => $product->id
            ])->get();


            DB::table('drm_product_categories')->where('product_id',$product->id)->delete();

            foreach ($productCategories as $productCategory) {
                DB::table('drm_product_categories')->updateOrInsert((array)$productCategory);
            }

            echo "\r Transferred ".++$count." products";
        }
    }


    private function backupChannel()
    {
        $user_id = 3738;

        $products = DB::connection('backup')->table('channel_products')->where([
            'user_id' => $user_id
        ])->cursor();

        foreach ($products as $product) {
            $data = (array)$product;

            $exists = DB::table('channel_products')->where('id',$data['id'])->exists();
            if(!$exists){
                DB::table('channel_products')->updateOrInsert($data);
            }

            $productCategories = DB::connection('backup')->table('channel_product_categories')->where([
                'channel_product_id' => $product->id
            ])->get();

            DB::table('channel_product_categories')->where('channel_product_id',$product->id)->delete();
            foreach ($productCategories as $productCategory) {
                DB::table('channel_product_categories')->updateOrInsert((array)$productCategory);
            }
        }
    }

    private function restoreImages()
    {
        $user_id = 3649;
        $shop_id = 1025;

        $products = ChannelProduct::where([
            'user_id' => $user_id,
            'shop_id' => $shop_id
        ])->get();

        $count = 0;
        foreach ($products as $product) {
            $drmProduct = DrmProduct::where(['id' => $product->drm_product_id])->first();

            $description = $drmProduct->description;
            $description['de'] = $product->description['de'];

            $productCategories = $product->channel_categories;

            $drmCategories = [];
            foreach ($productCategories as $productCategory) {
                $categoryName = $productCategory->categoryName($product->channel);
                $drmId = DrmCategory::where(['category_name_de' => $categoryName->category_name, 'user_id' => $user_id])->value('id');
                if($drmId){
                    $drmCategories[] = $drmId;
                }
            }

            try{
                (new DRMProductService())->update($drmProduct->id,[
                    'image' => $product->images,
                    'description' => $description,
                    'category' => $drmCategories
                ],'de','manual');
            }catch(\Exception $e){
            }



            echo "\r Transferred ".++$count." products";
        }
    }

}
