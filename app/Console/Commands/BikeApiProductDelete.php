<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\DrmProduct;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Services\DRMProductService;

class BikeApiProductDelete extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bike-api:delete';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Executing manual script';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {

        $mp_products = Product::where('api_id',1)
                        ->where('is_api_available',0)->cursor();


        $count = 0;
        foreach($mp_products as $product){
            $drmProducts = DrmProduct::where('marketplace_product_id',$product->id)->select('id','user_id')->get();

            if(count($drmProducts) > 0){

                MpCoreDrmTransferProduct::where('marketplace_product_id',$product->id)->delete();

                foreach($drmProducts as $drmProduct){
                    app(DRMProductService::class)->destroy($drmProduct->id,$drmProduct->user_id);
                }
            }

            $product->delete();

            echo "\r Deleted " . $count++ . " Products";
        }

        return "All bike api remove product delete successfully";

    }
}

