<?php

namespace App\Console\Commands;

use App\Http\Controllers\Marketplace\BikeApiController;
use Illuminate\Console\Command;

class BikeApiUpdatedProductSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mpBikeApi:UpdatedProductsSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        app(BikeApiController::class)
            ->buildChangesProductsSyncJobs(1);
        return 0;
    }
}
