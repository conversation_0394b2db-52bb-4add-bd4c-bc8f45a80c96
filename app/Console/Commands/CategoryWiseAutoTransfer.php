<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Marketplace\AutoTransferSubscription;

class CategoryWiseAutoTransfer extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mpCategorySubscription:CategoryWiseProductsAutoTransfer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public  $autoTransferSubscription;
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $autoTransferSubscription = AutoTransferSubscription::select('user_id', 'category_id', 'transfered_products_limit', 'transfered_products_count')->get();
        foreach($autoTransferSubscription as $categorySubscription){
            if($categorySubscription->transfered_products_limit && ($categorySubscription->transfered_products_limit >= $categorySubscription->transfered_products_count)){
                app(\App\Http\Controllers\Marketplace\AutoTransferController::class)->transferProductsInstantly($categorySubscription->user_id, $categorySubscription->category_id);
            }
        }
    }
}
