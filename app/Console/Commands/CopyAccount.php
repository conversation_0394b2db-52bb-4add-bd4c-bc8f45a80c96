<?php

namespace App\Console\Commands;

use App\DrmProduct;
use Illuminate\Console\Command;

class CopyAccount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'copy:start {from?} {to?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Auto Transfer';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $from = $this->argument('from');
        $to = $this->argument('to');

        $this->copyProducts($from,$to);
    }

    private function copyProducts($from,$to)
    {
        $drm_products = DrmProduct::with('drm_categories')->where(['user_id' => $from])->cursor();

        foreach ($drm_products as $drm_product)
        {
            $productData = json_decode(json_encode($drm_product),true);
            dd(json_encode($drm_product));
//            dd($productData);
//            $productData['user_id'] = $to;
//            unset($productData['id']);
//
//            dd($productData);
        }
    }
}
