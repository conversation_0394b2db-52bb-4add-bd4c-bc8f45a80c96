<?php

namespace App\Console\Commands;

use App\DrmProduct;
use App\Enums\Product;
use App\Models\ChannelProduct;
use App\Services\ChannelProductService;
use App\Shop;
use Illuminate\Console\Command;

class CopyChannelProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'channel-products:copy';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Executing manual script';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     *
     * execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->copyChannelProducts();
    }

    private function copyChannelProducts()
    {
        return true;
        $from = 3420;
        $to = 4175;

        $sourceShop = Shop::find(1369);
        // Fetch all shops for the `from` user, excluding eBay channel
        $newShop = Shop::find(1393);

            // Get all EANs already in the target user's channel
            $existingEans = ChannelProduct::where('shop_id', $newShop->id)
                ->pluck('ean')
                ->toArray();

            // Get all EANs from the source shop
            $sourceEans = ChannelProduct::where('shop_id', $sourceShop->id)
                ->pluck('ean')
                ->toArray();

            // Filter out already existing EANs
            $eansToTransfer = array_diff($sourceEans, $existingEans);

            if (empty($eansToTransfer)) {
                return;
            }

            $count = 0;

            $field = Product::ALL_FIELDS;
            $field[] = 'category';

            // Process EANs in chunks to avoid SQL placeholders limit
            $chunkSize = 1000; // Adjust the chunk size to avoid exceeding limits
            foreach (array_chunk($eansToTransfer, $chunkSize) as $eanChunk) {
                // Fetch DRM products for the current chunk
                $drmProducts = DrmProduct::where('user_id', $to)
                    ->whereIn('ean', $eanChunk)
                    ->select('id', 'ean')
                    ->get();

                // Process DRM products
                $drmProducts->each(function ($product) use ($newShop, &$count, $field) {
                    app(ChannelProductService::class)->transferProduct(
                        $product->id,
                        $newShop->channel,
                        $field,
                        'de',
                        'create',
                        true,
                        0,
                        ['shop_id' => $newShop->id]
                    );

                    echo "\rTransferred " . ++$count . " products";
                });
            }
    }

}
