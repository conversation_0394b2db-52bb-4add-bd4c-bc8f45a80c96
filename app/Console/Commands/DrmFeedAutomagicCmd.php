<?php

namespace App\Console\Commands;

use App\Jobs\DrmFeedAutomagic;
use Carbon\Carbon;
use Illuminate\Console\Command;

class DrmFeedAutomagicCmd extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:import_feed_automagic';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import feed automagic retry';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $retry_imports = DB::table('track_automagic')->where('created_at', '<', Carbon::now()->subDays(1))->get();
        foreach ($retry_imports as $key => $retry_import) {
            DrmFeedAutomagic::dispatch($retry_import->user_id, json_decode($retry_import->product_ids), $retry_import->id)->onQueue('feed_automagic');
        }
    }
}
