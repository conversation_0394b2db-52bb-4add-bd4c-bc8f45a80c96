<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;
use App\Helper\EbayApi;
use Carbon\Carbon;

class EbayRefreshUserToken extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ebay:refreshAccessToken';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Ebay Refresh User Access Token';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $shops = \App\Shop::where('channel',4)->where('username','<>','')->get();

        foreach($shops as $shop){

            //if (Carbon::now()->diffInHours($shop->updated_at) >= 1) {
                EbayApi::refreshUserToken($shop);
            //}

        }

    }
}
