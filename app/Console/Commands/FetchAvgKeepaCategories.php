<?php

namespace App\Console\Commands;

use App\Jobs\FetchAvgKeepaCategory;
use App\KeepaCategory;
use App\Services\Keepa\Keepa;
use App\TrendCategories;
use Illuminate\Console\Command;

class FetchAvgKeepaCategories extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:fetchAvgKeepaCategories {range?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch Best Seller ASIN list (30 days avg / regular 0 days avg) for Keepa Categories.\nThis list will be used to generate monthy csv report.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $range = $this->argument('range') ?? 0;
        print("Started fetching keepa categories (AVG $range) \n");
        try {
            $trend_cats = TrendCategories::select('keepa_cat_id')->get();
            foreach ($trend_cats as $key => $cat) {
                print("Despatched FetchKeepaCategory: $cat->keepa_cat_id | Range: $range\n");
                FetchAvgKeepaCategory::dispatch($cat->keepa_cat_id, $range)->onQueue('keepa');;
            }
        } catch (\Throwable $th) {
            print("Failed fetching keepa categories (AVG $range)\n");
            print_r($th);
        }
    }
}
