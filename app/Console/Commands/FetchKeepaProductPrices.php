<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\KeepaProduct;
use App\Models\Marketplace\KeepaProductPrice;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\DrmProduct;
use App\Jobs\FetchKeepaProductPrice;

class FetchKeepaProductPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fetchkeepaproductprices:cron';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function fetchKeepaProductPrice($eans){

        $asin_chunk_size = 100; #max size keepa supports is 100
        
        # chunk product asin's to keepa limit for parsing information
        $asin_chunks = array_chunk($eans, $asin_chunk_size);

        foreach ($asin_chunks as $key => $parsable_list) {
            FetchKeepaProductPrice::dispatch($eans, $parsable_list, $key);
        }
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        print "Started keepa regular products fetching\n";
        try {

            $ids = MpCoreDrmTransferProduct::pluck('marketplace_product_id')->toArray();
            $eans = Product::whereIn('id',$ids)->pluck('ean')->toArray();
            $this->fetchKeepaProductPrice(array_unique($eans));
            // $keepa_cats = KeepaCategory::where(['range' => 0])->get();
            // foreach ($keepa_cats as $key => $cat) {
            //     $this->fetchKeepaProducts($cat);
            // }
            print "Fetchng keepa products.\n";
        }catch( \Exception $e){
            print "Keepa regular products fetching failed.\n";
        }finally {
            print "Keepa regular products fetching finished.\n";
        }
        //return 0;
        //\Log::info("Cron is working fine!");
    }
}
