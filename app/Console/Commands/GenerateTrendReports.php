<?php

namespace App\Console\Commands;

use App\Jobs\GenerateTrendReport;
use App\Services\Keepa\Keepa;
use App\TrendCategories;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use League\Csv\Writer;

class GenerateTrendReports extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'keepa:generateTrendReports';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate monthly reports for each trend category from keepa data.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        print "Keepa monthly csv generation started.\n";
        try{
            $trend_cats = TrendCategories::select('drm_app_id')->get();
            foreach ($trend_cats as $key => $cat) {
                GenerateTrendReport::dispatch($cat->drm_app_id)->onQueue('keepa');
            }
        }catch( \Exception $e){
            print "Error occured while building report.\n";
        }
    }
}
