<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

class ImportEbayCategories extends Command
{
    protected $signature = 'import:ebay-categories';

    protected $description = 'Import eBay categories into the channel_categories table';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $data = file_get_contents('public/categories-77.json');
        $data = json_decode($data,true);

        $parentName = '';

        $this->insertCategory($data['rootCategoryNode'], $parentName, 0);

        $this->info('eBay categories imported successfully.');
    }

    private function insertCategory($category, $parentName, $parentId)
    {
        $categoryName = $category['category']['categoryName'];

        $categoryFullName = empty($parentName) ? $categoryName : $parentName . ' > ' . $categoryName;

        $categoryFullName = str_replace('Root > ','',$categoryFullName);


        $categoryId = DB::table('channel_categories_temp')->insertGetId([
            'category_id' => $category['category']['categoryId'],
            'category_name' => $categoryFullName,
            'channel' => 4,
            'status' => 0,
            'misc' => json_encode(['parent' => $parentId, 'aspects' => []]),
            'created_at' => now(),
            'updated_at' => now(),
        ]);


        if (isset($category['childCategoryTreeNodes']) && is_array($category['childCategoryTreeNodes'])) {
            foreach ($category['childCategoryTreeNodes'] as $childCategory) {
                $this->insertCategory($childCategory, $categoryFullName, $categoryId);
            }
        }
    }
}
