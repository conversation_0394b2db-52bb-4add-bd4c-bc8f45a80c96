<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\DeactiveUser\CustomerInactivityCheck;
use App\Services\DeactiveUser\TableMapper;

class InactiveUserActionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'inactive-user:action';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Inactive user action';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        TableMapper::init();
        usleep(50); //wait 50 microseconds
        CustomerInactivityCheck::dispatch();
    }
}
