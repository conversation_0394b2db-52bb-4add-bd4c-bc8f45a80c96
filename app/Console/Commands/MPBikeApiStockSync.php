<?php

namespace App\Console\Commands;

use App\Http\Controllers\Marketplace\BikeApiController;
use Illuminate\Console\Command;

class MPBikeApiStockSync extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mpBikeApi:StockSync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        app(\App\Http\Controllers\Marketplace\BikeApiController::class)
            ->fetchStockChangesInTheLastNMinutes(5);
        return 0;
    }
}
