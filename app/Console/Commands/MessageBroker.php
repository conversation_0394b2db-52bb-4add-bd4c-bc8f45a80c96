<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class MessageBroker extends Command
{
/**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'message-broker:listen';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Receive messages from Message Broker queue';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        (new \App\Services\MessageBroker\QueueListener)->listen();

        echo 'Waiting for messages. To exit press CTRL+C'.PHP_EOL;

        return 1;
    }
}
