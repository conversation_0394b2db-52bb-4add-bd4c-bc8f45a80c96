<?php

namespace App\Console\Commands;

use App\Enums\Channel;
use App\Models\ChannelCategory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class MigrateChannelCategory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:channel-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Transfer categories from yatego_categories, amazon_categories, ebay_categories, etsy_categories to channel_categories table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    private function transferYatigoCategories()
    {
        $this->info('Transfer yatego_categories started');
        $count = 0;
        DB::table('yatego_categories')
            ->select(['category_id', 'category_name'])
            ->orderBy('id')
            ->chunk(100, function ($categories) use (&$count) {
                foreach ($categories as $category) {
                    ChannelCategory::updateOrCreate(
                        ['category_id' => $category->category_id],
                        ['category_name' => $category->category_name, 'channel' => Channel::YATEGO]
                    );
                    echo "\rTransferred Yatigo Categories: " . ++$count;
                }
            });
        echo "\n";
    }

    private function transferEbayCategories()
    {
        $this->info('Transfer ebay_categories started');
        $count = 0;
        DB::table('ebay_categories')
            ->orderBy('id')
            ->chunk(100, function ($categories) use (&$count) {
                foreach ($categories as $category) {
                    ChannelCategory::updateOrCreate(
                        ['category_id' => $category->category_id],
                        [
                            'category_name' => $category->category_name,
                            'channel' => Channel::EBAY,
                            'status' => $category->status,
                            'misc' => [
                                'aspect_required' => $category->aspect_required,
                                'aspects' => $category->aspects,
                                'parent' => $category->parent,
                            ],
                        ]
                    );
                    echo "\rTransferred Ebay Categories: " . ++$count;
                }
            });
        echo "\n";
    }

    private function transferAmazonCategories()
    {
        $this->info('Transfer amazon_categories started');
        $count = 0;
        DB::table('amazon_categories')
            ->orderBy('id')
            ->chunk(100, function ($categories) use (&$count) {
                foreach ($categories as $category) {
                    ChannelCategory::updateOrCreate(
                        ['category_id' => $category->node_id],
                        [
                            'category_name' => $category->node_path,
                            'channel' => Channel::AMAZON,
                            'status' => $category->status,
                            'misc' => [
                                'node_id_de' => $category->node_id_de,
                            ],
                        ]
                    );
                    echo "\rTransferred Amazon Categories: " . ++$count;
                }
            });
        echo "\n";
    }

    private function transferEtsyCategories()
    {
        $this->info('Transfer etsy_categories started');
        $count = 0;
        DB::table('etsy_categories')
            ->orderBy('id')
            ->chunk(100, function ($categories) use (&$count) {
                foreach ($categories as $category) {
                    ChannelCategory::updateOrCreate(
                        ['category_id' => $category->taxonomy_id],
                        [
                            'category_name' => $category->name,
                            'channel' => Channel::ETSY,
                            'status' => $category->status,
                            'misc' => [
                                'level' => $category->level,
                                'parent_id' => $category->parent_id,
                            ],
                        ]
                    );
                    echo "\rTransferred Etsy Categories: " . ++$count;
                }
            });
        echo "\n";
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->transferYatigoCategories();
        $this->transferEbayCategories();
        $this->transferAmazonCategories();
        $this->transferEtsyCategories();

        return 0;
    }
}
