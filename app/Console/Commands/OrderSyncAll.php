<?php

namespace App\Console\Commands;

use crocodicstudio\crudbooster\helpers\CRUDBooster as HelpersCRUDBooster;
use Illuminate\Console\Command;

use App\Jobs\OrderSyncJob;

class OrderSyncAll extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Order Synchronization';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    // public function __call($method, $args)
    // {
    //     $this->crud->$method($args[0]);
    // }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        print "Order Sync Start\n";

        try{
            // \DB::table('drm_task_members')->insert([
            //     'cms_user_id' => 11,
            //     'drm_project_task_id' => 22,
            // ]);

            $shop_arr = \App\Shop::where('status', 1)->get();

            // dd($shop_arr);
            $shop_arr->each(function($shop){
                
                if($shop->url == "")
                {
                    return true;
                }

                if($shop->channel == 1)
                {
                    OrderSyncJob::dispatch($shop, 1);
                }
                else if($shop->channel == 2)
                {
                    OrderSyncJob::dispatch($shop, 2); 
                }
                else if($shop->channel == 3){
                    
                    OrderSyncJob::dispatch($shop, 3);
                }
                else if($shop->channel == 4){
                    
                    OrderSyncJob::dispatch($shop, 4);
                }
                else if($shop->channel == 6){

                    OrderSyncJob::dispatch($shop, 6);
                }

                // dd($shop->channel);
            });
        }catch( \Exception $e){
            print "Order Sync Failed\n";
        }finally {
          print "Order Sync End\n";
        }
    }
}
