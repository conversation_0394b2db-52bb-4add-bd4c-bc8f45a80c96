<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\Marketplace\BulkMarketplaceProductDelete;
use App\Models\Marketplace\Product;


class RemoveMarketplaceZeroStockProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'marketplace:remove-marketplace-zero-stock-product';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace zero stock product remove';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $startDate = now()->subDays(30)->toDateString();
        $stockUpdateStartDate = now()->subDays(10)->toDateString();

        $products = Product::with([
                'MpCoreDrmTransferProduct:marketplace_product_id,drm_product_id,user_id',
                'core_products:marketplace_product_id,id,user_id'
            ])
            ->select('id', 'image')
            ->whereDate('created_at', '>=', $startDate)
            ->whereDate('stock_updated_at', '<', $stockUpdateStartDate)
            ->where('stock', '<', 2)
            ->where('shipping_method', 1)
            ->whereNotIn('status', [0, 1])
            ->get();

        if(count($products) > 0){
            foreach ($products->chunk(100) as $product) {
                dispatch(new BulkMarketplaceProductDelete($product));
            }
        }
    }
}
