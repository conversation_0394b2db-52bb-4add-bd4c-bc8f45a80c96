<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\Marketplace\CategoryDiscountRemoveSync;

class RemoveMpCategoryDiscount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mpCategory:remove-discount';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Marketplace category discount remove';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        CategoryDiscountRemoveSync::dispatch();
    }
}
