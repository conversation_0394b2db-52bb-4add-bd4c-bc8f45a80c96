<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\NewOrder;
use App\Models\Marketplace\Product;


class ResendOutStockOrderAutomatically extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'resendOrder:ResendOutStockOrderAutomatically';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Resend order when out stock product stock avaailable';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public  $out_stock_orders;
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $out_stock_orders = NewOrder::where('cms_user_id', 2455)
            ->where('insert_type', 8)
            ->where('status', 'stock_unavailable')
            ->whereNull('credit_ref')
            ->whereNull('mp_api_id')
            ->whereDate('created_at', '>', '2024-03-01')
            ->select('id', 'cart', 'cms_client')
            ->get();

        foreach($out_stock_orders as $out_stock_order){
            $order_cart = reset(json_decode($out_stock_order->cart));
            $mp_product = Product::where('id', $order_cart->marketplace_product_id)
                ->select('id', 'ean', 'stock')
                ->first();

            if( !blank($mp_product) && $mp_product->stock >= $order_cart->qty){
                app('\App\Services\Marketplace\InternelSyncService')->transferOrderToInternel($out_stock_order->id);
            }
        }

    }
}
