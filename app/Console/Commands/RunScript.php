<?php

namespace App\Console\Commands;

use App\DrmProduct;
use App\DropmatixProductBrand;
use App\Enums\CategoryType;
use App\Enums\Channel;
use App\Enums\ChannelProductConnectedStatus;
use App\Enums\Product;
use App\Http\Controllers\Marketplace\MarketPlaceController;
use App\Http\Controllers\ProductTranslateController;
use App\Models\ChannelCategory;
use App\Models\ChannelCategoryTemp;
use App\Models\ChannelProduct;
use App\Models\ChannelProductCategory;
use App\Models\ChannelUserCategory;
use App\Models\Export\ChannelCsvCredential;
use App\Models\Product\ProfitCalculation;
use App\Services\ChannelProductService;
use App\Services\DRMProductService;
use App\Services\Modules\Export\Mirakl\Decathlon;
use App\Shop;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class RunScript extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'script:run';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Executing manual script';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     *
     * execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $this->restoreProducts();
    }

    public function restoreProducts() {
        $userId = 4072;
        $backupProducts = DB::connection('backup')->table('drm_products')
            ->where([
                'user_id' => $userId
            ])->get();

        foreach ($backupProducts as $backupProduct) {
            $data = json_decode(json_encode($backupProduct),true);

            DB::table('drm_products')->insert($data);
            dd('ok');
        }
    }

    private function fixMarketplaceConnection() {
        $userId = 2599;
        $products = DrmProduct::where([
            'user_id' => $userId,
        ])->select(['id','ean','marketplace_product_id','created_at','updated_at'])->get()->map(function ($item) use ($userId){
            return [
                'user_id' => $userId,
                'drm_product_id' => $item->id,
                'marketplace_product_id' => $item->marketplace_product_id,
                'mp_product_ean' => $item->ean,
                'created_at' => $item->created_at,
                'updated_at' => $item->updated_at,
            ];
        })->toArray();

        DB::connection('marketplace')->table('mp_core_drm_transfer_products')->insert($products);
    }

    private function recalculateProducts()
    {
        $productsGroup = ChannelProduct::where('marketplace_product_id', '>', 0)->where('calculation_id', '>', 0)->get()->groupBy('calculation_id');

        $count = 0;
        foreach ($productsGroup as $calculation_id => $products) {
            $calculation = ProfitCalculation::where('id', $calculation_id)->first();
            foreach ($products as $product) {
                $price = (new ChannelProductService())->calculatePrice($product, $calculation);

                if ($price != $product->vk_price) {
                    (new ChannelProductService())->update($product->id, [
                        'vk_price' => $price,
                        'calculation_id' => $product->calculation_id
                    ]);
                    echo "\r Found " . ++$count . " products";
                }
            }
        }
    }

    private function removeDuplicateBrands()
    {
        $duplicateBrands = DropmatixProductBrand::select('id', 'user_id', 'brand_name')
            ->groupBy('user_id', 'brand_name')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        $count = 0;
        foreach ($duplicateBrands as $duplicate) {
            $toDelete = DropmatixProductBrand::where('user_id', $duplicate->user_id)
                ->where('brand_name', $duplicate->brand_name)
                ->where('id', '!=', $duplicate->id)
                ->pluck('id')->toArray();

            DrmProduct::whereIn('brand', $toDelete)->where('user_id', $duplicate->user_id)->update([
                'brand' => $duplicate->id
            ]);

            ChannelProduct::whereIn('brand', $toDelete)->where('user_id', $duplicate->user_id)->update([
                'brand' => $duplicate->id
            ]);

            DropmatixProductBrand::destroy($toDelete);

            echo "\r Processed ".++$count." brands";
        }
    }

    private function changeProductLang()
    {
        $products = ChannelProduct::where([
            'shop_id' => 1157,
            'user_id' => 2454,
            'channel' => 19
        ])->cursor();
        $count = 0;
        foreach ($products as $product) {
            $product->country_id = 8;
            $titles = $product->title;
            $descriptions = $product->description;
            $short_descriptions = $product->short_description;

            $titles['es'] = $titles['de'];
            unset($titles['de']);

            $descriptions['es'] = $descriptions['de'];
            unset($descriptions['de']);

            $short_descriptions['es'] = $short_descriptions['de'];
            unset($short_descriptions['de']);

            $product->title = $titles;
            $product->description = $descriptions;
            $product->short_description = $short_descriptions;

            $product->save();

            echo "\r Transferred " . ++$count . " products";
        }
    }


    private function deleteFromMainLevel()
    {
        $products = DrmProduct::whereNotNull('marketplace_product_id')->whereDate('updated_at', '<', '2024-03-13')->groupBy('user_id')->get();

        dd($products);
    }


    private function transferFromMP()
    {
        $ids = ChannelProduct::where([
            'user_id' => 3675,
            'shop_id' => 1173
        ])->pluck('marketplace_product_id')->toArray();

        app(MarketPlaceController::class)->transferAllFilteredProductsToDrm($ids, [], null, 3675);
    }

    private function attachChannelProducts()
    {
        $products = ChannelProduct::where([
            'user_id' => 3738,
            'shop_id' => 1094
        ])->cursor();

        $notFound = [];
        foreach ($products as $product) {
            $main_id = DrmProduct::where([
                'user_id' => 3738,
                'ean' => $product->ean
            ])->value('id');

            if (!$main_id) {
                $notFound[] = $product->id;
//                app(ChannelProductService::class)->destroy($product->id,$product->shop_id,3738);
            } else {
                $product->drm_product_id = $main_id;
                $product->save();
            }
        }

        dd(count($notFound));
    }

    private function copyShops() {
        $from = 3420;
        $to = 4175;

        $shops = Shop::where([
            'user_id' => $from
        ])->where('channel','!=',Channel::EBAY)->get();

        foreach ($shops as $shop) {
            $data = $shop->toArray();

            unset($data['id']);

            $data['user_id'] = $to;
            $data['username'] = 'fake';
            $data['password'] = 'fake';

            Shop::create($data);
        }
    }

    private function copyProducts()
    {
        $from = 3420;
        $to = 4175;

        $products = DrmProduct::where([
            'user_id' => $from
        ])->pluck('marketplace_product_id')->toArray();

        app(MarketPlaceController::class)->transferAllFilteredProductsToDrm($products, [], null, $to);
    }


    private function copyChannelProducts()
    {
        $from = 3420;
        $to = 4175;

        // Fetch all shops for the `from` user, excluding eBay channel
        $shops = Shop::where('user_id', $from)
            ->where('channel', '!=', Channel::EBAY)
            ->get()
            ->keyBy('id');

        // Map channels to target shops for the `to` user
        $targetShops = Shop::where('user_id', $to)
            ->whereIn('channel', $shops->pluck('channel'))
            ->get()
            ->keyBy('channel');

        foreach ($shops as $shop) {
            $newShop = $targetShops->get($shop->channel);
            if (!$newShop) {
                // Skip if no corresponding target shop is found
                continue;
            }

            // Get all EANs already in the target user's channel
            $existingEans = ChannelProduct::where('shop_id', $newShop->id)
                ->pluck('ean')
                ->toArray();

            // Get all EANs from the source shop
            $sourceEans = ChannelProduct::where('shop_id', $shop->id)
                ->pluck('ean')
                ->toArray();

            // Filter out already existing EANs
            $eansToTransfer = array_diff($sourceEans, $existingEans);

            if (empty($eansToTransfer)) {
                // Skip if no new products to transfer
                continue;
            }

            $count = 0;

            // Process EANs in chunks to avoid SQL placeholders limit
            $chunkSize = 1000; // Adjust the chunk size to avoid exceeding limits
            foreach (array_chunk($eansToTransfer, $chunkSize) as $eanChunk) {
                // Fetch DRM products for the current chunk
                $drmProducts = DrmProduct::where('user_id', $to)
                    ->whereIn('ean', $eanChunk)
                    ->select('id', 'ean')
                    ->get();

                // Process DRM products
                $drmProducts->each(function ($product) use ($newShop, &$count) {
                    app(ChannelProductService::class)->transferProduct(
                        $product->id,
                        $newShop->channel,
                        Product::ALL_FIELDS,
                        'de',
                        'create',
                        true,
                        0,
                        ['shop_id' => $newShop->id]
                    );

                    echo "\rTransferred " . ++$count . " products";
                });
            }
        }
    }


    private function forceTransferProduct()
    {
        $shopId = 581;

        $incompleteProducts = ChannelProduct::where([
            'shop_id' => $shopId,
            'connection_status' => ChannelProductConnectedStatus::MANDATORY_FIELD_MISSING
        ])->pluck('drm_product_id')->toArray();

        $fields = Product::ALL_FIELDS;
        $fields[] = 'category';

        foreach ($incompleteProducts as $product) {
            app(ChannelProductService::class)->transferProduct(
                $product,
                Channel::DROPTIENDA,
                $fields, 'de', 'update', true, 0, ['shop_id' => $shopId]);
        }
    }

    public function transferShop($from, $to)
    {
        $shop = Shop::where([
            'user_id' => $from,
            'channel' => Channel::DROPTIENDA
        ])->first();
    }

    public function removeDuplicates($shopId)
    {
        $duplicateCategories = ChannelUserCategory::select('id', 'category_name', 'shop_id', 'parent')
            ->groupBy('category_name', 'shop_id', 'parent')
            ->where('shop_id', 581)
            ->havingRaw('COUNT(*) > 1')
            ->get();

        foreach ($duplicateCategories as $category) {
            $duplicates = ChannelUserCategory::where([
                'category_name' => $category->category_name,
                'shop_id' => $category->shop_id,
                'parent' => $category->parent,
            ])->get();


            $firstCategory = $duplicates->first();
            $duplicateIds = $duplicates->pluck('id')->except($firstCategory->id);

            foreach ($duplicates as $duplicate) {
                if ($duplicate->id !== $firstCategory->id) {
                    ChannelUserCategory::where([
                        'parent' => $duplicate->id,
                    ])->update(['parent' => $firstCategory->id]);
                }
            }

            ChannelUserCategory::whereIn('id', $duplicateIds)->delete();

            ChannelProductCategory::where([
                'category_id' => $category->id,
                'category_type' => CategoryType::USER
            ])
                ->update(['category_id' => $firstCategory->id]);
        }

        $this->info('Duplicate categories removed successfully.');
    }

    private function deleteProducts()
    {
        $products = DrmProduct::whereNotNull('marketplace_product_id')
            ->where('country_id','<>',1)
            ->whereIn('user_id',
                [67 ,2766 ,2872 ,2900 ,3236 ,3037 ,3423, 2911 ,3586
                ,3584
                ,2773
                ,3750
                ,3738
                ,3977
                ,2976
                ,2718
                ,3984
                ,3932
                ,2698
                ,3110
                ,3993
                ,3602
                ,3553
                ,4082
                ,2266,2619,2829,3109,3288,3420,3514,4072,4099,4127
            ])->get()->groupBy('user_id');


        foreach ($products as $userId => $userProducts) {
            $userProducts->chunk(1000, function ($chunkedProducts) use ($userId){
                (new DRMProductService())->destroy($chunkedProducts->pluck('id')->toArray(),$userId);
            });
        }
    }

    private function updateHandlingTime()
    {
        $count = 0;
        DrmProduct::whereNotNull('marketplace_product_id')
            ->select('channel_products.*')
            ->join('channel_products', 'channel_products.drm_product_id', '=', 'drm_products.id')
            ->where('channel_products.delivery_days', '>=', 14)
            ->whereIn('channel', [10, 13, 12])
            ->where('handling_time_updated', 1)->chunk(10000, function ($items) use (&$count) {
                foreach ($items as $product) {
//                DB::table('channel_products')->where([
//                    'id' => $product->id
//                ])->update([
//                    'delivery_days' => (int)$product->delivery_days - 14
//                ]);

                    app(ChannelProductService::class)->update(
                        $product->id,
                        ['delivery_days' => (int)$product->delivery_days - 14]
                    );
                    echo "\r" . ++$count;
                }
            });
    }

    /**
     * @throws Exception
     */
    private function updateShopId()
    {
        $credentials = ChannelCsvCredential::all();

        foreach ($credentials as $credential) {
            $shop = Shop::where([
                'user_id' => $credential->user_id,
                'channel' => $credential->channel
            ])->exists();

            if (!$shop) {
                $credential->delete();
            }
        }
    }

    private function deleteMpIncompleteProducts()
    {
        $products = DrmProduct::whereNotNull('marketplace_product_id')->select('id', 'user_id')->where('ek_price', '=', 0)->cursor();
        $count = 0;
        foreach ($products as $product) {
            app(DRMProductService::class)->destroy($product->id, $product->user_id);

            echo "\r Deleted: " . ++$count . " products";
        }
    }

    private function transferTradeByteProducts()
    {
        $products = DrmProduct::leftJoin('channel_products', function ($join) {
            $join->on('drm_products.id', '=', 'channel_products.drm_product_id')
                ->where('channel_products.shop_id', '=', 1017);
        })
            ->where('drm_products.user_id', 2454)
            ->whereNull('channel_products.drm_product_id')
            ->pluck('drm_products.id')
            ->toArray();

        $count = 0;
        foreach (array_chunk($products, 500) as $products) {
            foreach ($products as $product) {
                app(ChannelProductService::class)->transferProduct(
                    $product,
                    Channel::TRADEBYTE,
                    Product::ALL_FIELDS, 'de', 'create', false, 0, ['shop_id' => 1017]);
                echo "\r Transferred " . ++$count . " products";
            }
        }
    }

    private function fixDecathlonStatus()
    {
        $channelProducts = ChannelProduct::where([
            'channel' => Channel::DECATHLON
        ])->whereNotIn('connection_status', [1, 2])->cursor();

        foreach ($channelProducts as $channelProduct) {
            $service = new Decathlon($channelProduct->shop_id, $channelProduct->user_id);
            $ean_is_valid = $service->is_valid_ean($channelProduct->ean);

            if ($ean_is_valid) {
                $mpProduct = \App\Models\Marketplace\Product::where('ean', $channelProduct->ean)->exists();
                if ($mpProduct) {
                    $connection_status = ChannelProductConnectedStatus::AVAILABLE_IN_MP;
                } else {
                    $connection_status = ChannelProductConnectedStatus::NOT_AVAILABLE_IN_MP;
                }
            } else {
                $connection_status = ChannelProductConnectedStatus::MANDATORY_FIELD_MISSING;
            }

            $channelProduct->connection_status = $connection_status;
            $channelProduct->save();
        }
    }

    private function transferChannelCategories()
    {
        $count = 0;
        $categories = ChannelCategoryTemp::where([
            'channel' => Channel::EBAY,
//            'status' => 1
        ])->cursor();

        foreach ($categories->chunk(500) as $items) {
            $categoryData = [];
            foreach ($items as $item) {
                $category = $item->toArray();
                $category['misc'] = json_encode($category['misc']);
                unset($category['id']);
                $categoryData[] = $category;
                $count++;
            }
            echo "\r Transferred " . $count . " categories";
            ChannelCategory::insert($categoryData);
        }
    }

    private function freeTranslation()
    {
        $ids = DrmProduct::where([
            'user_id' => 3777
        ])->orderBy('id', 'desc')->limit(4000)->pluck('id')->toArray();

        $data['user_id'] = 3777;
        $data['language_id'] = '2,8';
        $data['product_id'] = implode(',', $ids);
        $data['source'] = 'de';
        $data['translate_category'] = 'yes';

        app(ProductTranslateController::class)->productTranslationSCA($data);
    }

    private function checkMiraklOffers() {
        $shop = Shop::where([
            'id' => 1391
        ])->first();

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://novaclub-prod.mirakl.net/api/offers',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'Authorization: '.$shop->username,
                'Accept: application/json'
            ),
        ));

        $response = json_decode(curl_exec($curl),true);

        curl_close($curl);

        dd($response);
    }

}
