<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Modules\Import\UserImportSync;
use App\Jobs\ShopOrderSyncJob;
class ShopOrderSyncBronze extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shop:order-bronze';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Shop order Sync - Bronze';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        print "Shop Orders Sync Start - Bronze\n";
        try{
            $app_id = config('global.interval_app_id');
            $plan_id = config('global.interval_bronze_plan');

            $user_has_plan =  \DB::table('purchase_apps')->where(['app_id' => $app_id, 'plan_id' => $plan_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->pluck('cms_user_id')->toArray();
            $user_has_assign = \DB::table('app_assigns')->where(['app_id'=>$app_id, 'plan_id'=>$plan_id])
            ->where(function($amnu){
                $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
            })
            ->select('user_id')->pluck('user_id')->toArray();
            $users = array_unique(array_merge($user_has_plan, $user_has_assign));
            $shop_arr = \App\Shop::where('status', 1)->whereIn('user_id', $users)->get();
            
            $shop_arr->each(function($shop){
                if ($shop->url == "") return true;

                if($shop->channel == 1)
                {
                    ShopOrderSyncJob::dispatch($shop, 1);
                }
                else if($shop->channel == 2)
                {
                    ShopOrderSyncJob::dispatch($shop, 2);
                }
                else if($shop->channel == 3){

                    ShopOrderSyncJob::dispatch($shop, 3);
                }
                else if($shop->channel == 4){

                    ShopOrderSyncJob::dispatch($shop, 4);
                }
                else if($shop->channel == 6){

                    ShopOrderSyncJob::dispatch($shop, 6);
                }
            });
            // UserImportSync::syncAllImport($users);
            // UserImportSync::syncAllManualUrl($users);
        }catch( \Exception $e){
            print "Shop Orders Sync Failed - Bronze\n";
        }finally {
          print "Shop Orders Sync End - Bronze\n";
        }
    }
}
