<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;

class ShopValidation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'shop:validation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Shop Validation';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $shops = \App\Shop::get();
        foreach($shops as $shop){
            \App\Http\Controllers\AdminShopSettingController::shopValidation($shop,'yes'); 
        }
    }
}
