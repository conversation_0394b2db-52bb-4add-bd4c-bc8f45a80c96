<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SyncEtsyCategory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:etsy-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            app(\App\Services\Modules\Export\Etsy\EtsyService::class)->syncCategories();
        } catch (\Exception $e) {
            dd($e->getMessage());
        }
    }
}
