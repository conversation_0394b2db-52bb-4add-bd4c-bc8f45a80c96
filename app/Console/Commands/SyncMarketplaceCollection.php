<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class SyncMarketplaceCollection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:collection ';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync collection product';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


    }
}
