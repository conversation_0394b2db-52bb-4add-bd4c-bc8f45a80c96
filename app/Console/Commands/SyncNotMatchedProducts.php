<?php

namespace App\Console\Commands;

use App\Models\Marketplace\Product;
use App\Models\Marketplace\StockSyncReport;
use App\Services\DRMProductService;
use Illuminate\Console\Command;

class SyncNotMatchedProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'mp:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Executing manual script';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $count = 0;

        StockSyncReport::where('fixed', 0)
            ->where(function ($q) {
                $q->whereRaw('marketplace_price != drm_price')
                    ->orWhereRaw('marketplace_stock != drm_stock');
            })
            ->chunk(1000, function ($chunk) use (&$count) {
                $mpProducts = Product::whereIn('id', $chunk->pluck('marketplace_product_id')->toArray())
                    ->select(
                        'id',
                        'stock',
                        'vk_price as ek_price'
                    )->get();

                foreach ($chunk as $product) {
                    $mpProduct = $mpProducts->where('id', $product->marketplace_product_id)->first()->toArray();
                    unset($mpProduct['id']);
                    app('App\Services\DRMProductService')->update(
                        $product->drm_product_id,
                        $mpProduct
                    );
                    $product->fixed = true;
                    $product->save();
                    echo "\r Processed " . $count++ . " Products";
                }
            });
    }
}
