<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Console\Command;

class SyncOttoCategory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:otto-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle()
    {
        $user = 'sandbox_dl10134';
        $pass = 'tzPF48lBC6OQ#';
        $token = $this->getToken($user,$pass);
        $categories = $this->getCategories($token);
        dd($categories);
    }

    private function getToken($user,$pass)
    {
        $client = new Client();
        $headers = [
            'Content-Type' => 'application/x-www-form-urlencoded',
            'cache-control' => 'no-cache'
        ];
        $params = [
            'grant_type' => 'password',
            'client_id' => 'token-otto-api',
            'username' => $user,
            'password' => $pass
        ];
        $url = 'https://sandbox.api.otto.market/v1/token';
        $options = ['headers' => $headers, 'form_params' => $params];
        $response = $client->post($url, $options);
        $response = json_decode($response->getBody(), true);
        return $response['access_token'];
    }

    private function getCategories($token)
    {
        $client = new Client();
        $headers = [
            'Content-Type' => 'application/json',
            'cache-control' => 'no-cache',
            'X-Request-Timestamp' => Carbon::now()->toDateTimeString(),
            'Authorization' => "Bearer $token"
        ];
        $url = "https://sandbox.api.otto.market/v2/products/categories";
        $client = new Client(['headers' => $headers]);
        $response = $client->get($url);
        return json_decode($response->getBody(), true);
    }
}
