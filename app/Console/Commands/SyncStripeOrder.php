<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use App\Jobs\OrderStripeSyncJob;
class SyncStripeOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'stripe:sync';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Stripe Order Sync';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // OrderStripeSyncJob::dispatch();
    }
}
