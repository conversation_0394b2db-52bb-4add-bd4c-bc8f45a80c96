<?php

namespace App\Console\Commands;

use App\Enums\Channel;
use App\Models\ChannelCategory;
use App\YategoCategory;
use Illuminate\Console\Command;
use PhpOffice\PhpSpreadsheet\IOFactory;

class SyncYategoCategory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:yatego-categories';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync Yatego categories from excel file.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $spreadsheet = IOFactory::load('public/csv/yatego_kategorien_v2.xlsx');
        $data_arr = collect($spreadsheet->getActiveSheet()->toArray());
        unset($data_arr[0]);
        $count = 0;

        foreach($data_arr as $arr) {
            ChannelCategory::updateOrCreate(
                [
                    'category_id' => $arr[0],
                    'channel'     => Channel::YATEGO,
                    'status'      => 1
                ],
                ['category_name' => $arr[1]]
            );
            echo "\r Processed: ". $count++ ." items";
        }

        return 0;
    }
}
