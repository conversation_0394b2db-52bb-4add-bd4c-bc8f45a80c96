<?php

namespace App\Console\Commands;

use App\Enums\Channel;
use App\Models\ChannelCategoryTemp;
use App\Services\Modules\Export\Ebay\Api;
use Illuminate\Console\Command;

class getEbayAspects extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get:ebay-aspects';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->addOptionalAspects();
    }

    public function addOptionalAspects()
    {
        $count = 0;
        $categories = ChannelCategoryTemp::where([
            'channel' => Channel::EBAY,
            'status' => 0
        ])->cursor();

        $service = new Api();

        foreach ($categories as $category) {
            $aspects = $service->getCategoryAspects($category->category_id);

            if($aspects){
                $required_aspects = $aspects->where('aspectConstraint.aspectRequired',true);
                $optional_aspects = $aspects->where('aspectConstraint.aspectRequired',false);

                $misc = $category->misc;
                $misc['required_aspects'] = json_decode(json_encode($required_aspects),true);
                $misc['optional_aspects'] = json_decode(json_encode($optional_aspects),true);

                $category->misc = $misc;
                $category->status = 1;

                $category->save();
                echo "\r Updated  ".++$count." categories";
            }
        }

    }
}
