<?php

namespace App\Console;

// use App\Console\Commands\OttoProductCommand;
// use App\Console\Commands\ShopValidation;
// use App\Console\Commands\EbayRefreshUserToken;
// use App\Console\Commands\OrderSyncAll;
// use App\Console\Commands\SyncStripeOrder;

// use App\Console\Commands\ShopOrderSync;
// use App\Console\Commands\ShopOrderSyncBronze;
// use App\Console\Commands\ShopOrderSyncSilver;
// use App\Console\Commands\ShopOrderSyncGold;
// use App\Console\Commands\ShopOrderSyncPlatinum;
// use App\Console\Commands\CategoryWiseAutoTransfer;


// use App\Console\Commands\KlickTippCustomerSync;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
// use Illuminate\Support\Facades\Log;

// use App\Traits\CronApiReport;


class Kernel extends ConsoleKernel
{
    // use CronApiReport;

    protected $commands = [
        // ShopOrderSync::class,
        // ShopOrderSyncBronze::class,
        // ShopOrderSyncSilver::class,
        // ShopOrderSyncGold::class,
        // ShopOrderSyncPlatinum::class,

        // SyncStripeOrder::class,
        // OrderSyncAll::class,
        // ShopValidation::class,
        // EbayRefreshUserToken::class,
        // KlickTippCustomerSync::class,
        // OttoProductCommand::class,
        Commands\FetchKeepaProductPrices::class,
    ];

    protected function schedule(Schedule $schedule)
    {

        $schedule->command('fetchkeepaproductprices:cron')
                 ->everyMinute();
//         $schedule->command('order:sync')
//         ->everyFifteenMinutes();

        // $schedule->command('stripe:sync')
        // ->everyTenMinutes()
        // ->runInBackground()
        // ->onSuccess(function () {
        //     $this->sendReportTo('Order by Stripe API', 'order_stripe_api', 'success');
        // })
        // ->before(function () {
        //     $this->sendReportTo('Order by Stripe API', 'order_stripe_api', 'start');
        // })
        // ->after(function () {
        //     $this->sendReportTo('Order by Stripe API', 'order_stripe_api', 'finish');
        // })
        // ->onFailure(function () {
        //     $this->sendReportTo('Order by Stripe API', 'order_stripe_api', 'failed');
        // })->withoutOverlapping();

        //New Shop Order Sync Job
        // if (!app()->environment('development')) {
        //     // //Normal 24 hour sync
        //     $schedule->command('shop:order')
        //     ->cron('0 */24 * * *')
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         Log::channel('command')->info('Success');
        //      })
        //     ->before(function () {
        //         Log::channel('command')->info('Start');
        //      })
        //     ->after(function () {
        //         Log::channel('command')->info('finished');
        //     })
        //     ->onFailure(function () {
        //         Log::channel('command')->info('failed');
        //     })->withoutOverlapping();

        //     // //Bronze 12 hour
        //     $schedule->command('shop:order-bronze')
        //     ->cron('0 */12 * * *')
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         Log::channel('command')->info('Success');
        //      })
        //     ->before(function () {
        //         Log::channel('command')->info('Start');
        //      })
        //     ->after(function () {
        //         Log::channel('command')->info('finished');
        //     })
        //     ->onFailure(function () {
        //         Log::channel('command')->info('failed');
        //     })->withoutOverlapping();

        //     // //Silver 6 hour
        //     $schedule->command('shop:order-silver')
        //     ->cron('0 */6 * * *')
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         Log::channel('command')->info('Success');
        //      })
        //     ->before(function () {
        //         Log::channel('command')->info('Start');
        //      })
        //     ->after(function () {
        //         Log::channel('command')->info('finished');
        //     })
        //     ->onFailure(function () {
        //         Log::channel('command')->info('failed');
        //     })->withoutOverlapping();

        //     // //Gold 3 hour
        //     $schedule->command('shop:order-gold')
        //     ->cron('0 */3 * * *')
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         Log::channel('command')->info('Success');
        //      })
        //     ->before(function () {
        //         Log::channel('command')->info('Start');
        //      })
        //     ->after(function () {
        //         Log::channel('command')->info('finished');
        //     })
        //     ->onFailure(function () {
        //         Log::channel('command')->info('failed');
        //     })->withoutOverlapping();

        //     //Platinum 30 min
        //     $schedule->command('shop:order-platinum')
        //     ->everyThirtyMinutes()
        //     ->runInBackground()
        //     ->onSuccess(function () {
        //         $this->sendReportTo('Order API', 'order_api', 'success');
        //         Log::channel('command')->info('Success');
        //      })
        //     ->before(function () {
        //         $this->sendReportTo('Order API', 'order_api', 'start');
        //         Log::channel('command')->info('Start');
        //      })
        //     ->after(function () {
        //         $this->sendReportTo('Order API', 'order_api', 'finish');
        //         Log::channel('command')->info('finished');
        //     })
        //     ->onFailure(function () {
        //         $this->sendReportTo('Order API', 'order_api', 'failed');
        //         Log::channel('command')->info('failed');
        //     })->withoutOverlapping();
        // }

        //KlickTipp Subsrciber Sync Scheduler
        // $schedule->command('sync:customer')->daily()->runInBackground()
        // ->onSuccess(function () {
        //     Log::channel('command')->info('Success');
        // })
        // ->before(function () {
        //     Log::channel('command')->info('Start');
        // })
        // ->after(function () {
        //     Log::channel('command')->info('finished');
        // })
        // ->onFailure(function () {
        //     Log::channel('command')->info('failed');
        // })->withoutOverlapping();

        // $schedule->command('ebay:refreshAccessToken')->hourly();

        //Marketplaec
        // Bike Api Scheduler
        /*
        $schedule->command('mpBikeApi:ProductSync')->before(function () {
            Log::channel('command')->info('Scheduler Starts . ... ');
        })->after( function () {
            Log::channel('command')->info('Scheduler finnished . ... ');
        })->everyThirtyMinutes();

        $schedule->command('mpBikeApi:StockSync')->before(function(){
            Log::channel('command')->info('Stock sync Scheduler Starts . ... ');
        })->after( function ( ) {
            Log::channel('command')->info('Stock sync Scheduler finished . ... ');
        })->hourly();

        $schedule->command('mpBikeApi:UpdatedProductsSync')->before(function () {
            Log::channel('command')->info('Api Updated product sync starts ... ');
        })->after(function () {
            Log::channel('command')->info('Api Updated product sync finished . . ');
        })->everyMinute();

        */


        // $schedule->call('App\Http\Controllers\AdminCommonController@getDrmUpdateAll')->everyMinute();
        // $schedule->call('App\Http\Controllers\Marketplace\BikeApiController@getAllProducts')->everyMinute();

//         $schedule->command('keepa:fetchKeepaProducts')->everySixHours();
        // $schedule->command('keepa:fetchAvgKeepaCategories', [30])->everyMinute();    #for avg 30
        // $schedule->command('keepa:fetchAvgKeepaCategories', [0])->everyMinute();     #for regular/avg 0

//         $schedule->command('mpCategorySubscription:CategoryWiseProductsAutoTransfer')->daily();
        // $schedule->command('mpCategory:remove-discount')->daily() ;
        // $schedule->command('marketplace:remove-marketplace-product-discount')->daily() ;

    }

    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
