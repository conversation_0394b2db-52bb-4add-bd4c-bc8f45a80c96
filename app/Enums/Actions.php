<?php

namespace App\Enums;

abstract class Actions
{
    const IMPORT_PRODUCT = 1;
    const EXPORT_PRODUCT = 2;
    const DISCONNECT_PRODUCT = 3;
    const DELETE_PRODUCT = 4;
    const UPDATE_STATUS = 5;

    const ACTIONS_LABELS = [
        self::IMPORT_PRODUCT     => 'Import Product',
        self::EXPORT_PRODUCT    => 'Export Product',
        self::DISCONNECT_PRODUCT => 'Disconnect Product',
        self::DELETE_PRODUCT => 'Delete Product',
        self::UPDATE_STATUS => 'Delete Product'
    ];

    const ACTION_MAPPING = [
        Channel::_CREATE => self::EXPORT_PRODUCT,
        Channel::_DISCONNECT => self::DISCONNECT_PRODUCT,
        Channel::_DEL => self::DELETE_PRODUCT,
        Channel::_UPDATE => self::EXPORT_PRODUCT,
    ];
}
