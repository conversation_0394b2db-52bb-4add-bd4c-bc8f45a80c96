<?php

namespace App\Enums;

abstract class Apps
{
    const FTP = 52;
    const IMAGE_BACKUP = 51;
    const EBAY_TEMPLATE = 47;
    const PRODUCT_TEMPLATE = 42;
    const EAN_MANAGER = 46;
    const SEARCH_N_REPLACE = 17;
    const UNIVERSAL_EXPORT = 55;
    const DT_AUTO_EXPORT = 59;
    const MAGIC_PRODUCT_UPDATE = 60;
    const EASY_PRICING = 108;
    const PLAN_TRIAL = "trial";
    const PLAN_DELUXE = "deluxe";
    const PLAN_PROFESSIONAL = "professional";
    const PLAN_ENTERPRISE = "enterprise";
    const EXPORT_LEVEL1_MONTH = 26;
    const EXPORT_LEVEL1_YEAR = 27;
    const EXPORT_LEVEL2_MONTH = 28;
    const EXPORT_LEVEL2_YEAR = 29;
    const EXPORT_UNLIMITED_MONTH = 30;
    const EXPORT_UNLIMITED_YEAR = 31;

    const EASY_39 = 8;
    const EASY_49 = 38;
    const EASY_89 = 4;
    const EASY_199 = 39;
    const EASY_349 = 44;
    const EASY_499 = 58;
    const EASY_749 = 59;
    const EASY_999 = 60;
    const EASY_1299 = 61;
    const EASY_1499 = 62;
    const EASY_1999 = 63;
    const EASY_2399 = 64;
    const EASY_2699 = 65;
    const EASY_3399 = 66;

    const CHANNEL_AUTO_BRONZE_MONTH = 24;
    const CHANNEL_AUTO_SILVER_MONTH = 68;
    const CHANNEL_AUTO_GOLD_MONTH = 4;
    const CHANNEL_AUTO_PLATINUM_MONTH = 69;

    const CHANNEL_AUTO_BRONZE_YEAR = 3;
    const CHANNEL_AUTO_SILVER_YEAR = 70;
    const CHANNEL_AUTO_GOLD_YEAR = 5;
    const CHANNEL_AUTO_PLATINUM_YEAR = 71;


    const DROPMATIX_ID      = 2455;
    const DROPCAMPUS_ID     = 2454;
    const DROPTIANDA_ID     = 2439;
    const EXPERTISEROCKS_ID = 98;

    const APPOINTMENT_LAST_ID = 1419;

    const LIMITS = [
        self::UNIVERSAL_EXPORT => [
            self::PLAN_TRIAL => 1,
            self::EXPORT_LEVEL1_MONTH => 3,
            self::EXPORT_LEVEL1_YEAR => 3,
            self::EXPORT_LEVEL2_MONTH => 7,
            self::EXPORT_LEVEL2_YEAR => 7,
            self::EXPORT_UNLIMITED_MONTH => -1,
            self::EXPORT_UNLIMITED_YEAR => -1,
            self::PLAN_DELUXE => 1,
            self::PLAN_PROFESSIONAL => 3,
            self::PLAN_ENTERPRISE => -1,
        ],
        self::EASY_PRICING => [
            self::PLAN_TRIAL => 500,
            self::EASY_39 => 500,
            self::EASY_49 => 1000,
            self::EASY_89 => 2000,
            self::EASY_199 => 5000,
            self::EASY_349 => 10000,
            self::EASY_499 => 15000,
            self::EASY_749 => 30000,
            self::EASY_999 => 50000,
            self::EASY_1299 => 75000,
            self::EASY_1499 => 100000,
            self::EASY_1999 => 150000,
            self::EASY_2399 => 200000,
            self::EASY_2699 => 250000,
            self::EASY_3399 => 9999999999,
        ]
    ];
}
