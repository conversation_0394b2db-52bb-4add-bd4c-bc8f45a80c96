<?php


namespace App\Enums;


abstract class ChannelProductConnectedStatus
{
    const CONNECTED = 1;
    const READY_TO_EXPORT = 2;
    const MANDATORY_FIELD_MISSING = 3;
    const ERROR = 4;
    const BLOCKED = 5;
    const AVAILABLE_IN_MP = 6;
    const NOT_AVAILABLE_IN_MP = 7;
    const PENDING = 8;

    const CHANGES_REQUIRED = 9;

    const LABEL = [
        self::CONNECTED => 'Connected',
        self::READY_TO_EXPORT => 'Ready',
        self::MANDATORY_FIELD_MISSING => 'Incomplete',
        self::ERROR => 'Error Occurred',
        self::BLOCKED => 'Blocked',
        self::AVAILABLE_IN_MP => 'In Marketplace',
        self::NOT_AVAILABLE_IN_MP => 'Not In Marketplace',
        self::PENDING => 'Pending',
        self::CHANGES_REQUIRED => 'Changes Required'
    ];

    const BUTTON_COLOR = [
        self::CONNECTED => 'rgb(46, 205, 111)',
        self::READY_TO_EXPORT => '#5c738699',
        self::MANDATORY_FIELD_MISSING => 'rgb(229, 0, 0)',
        self::ERROR => '#B00020',
        self::BLOCKED => '#B00020',
        self::AVAILABLE_IN_MP => '#ffbc00',
        self::NOT_AVAILABLE_IN_MP => '#800080',
        self::PENDING => 'rgb(175 147 136)',
        self::CHANGES_REQUIRED => 'rgb(231 137 69)'
    ];
    const  ACTION_ICON = [
        self::CONNECTED => '<i class="fa fa-caret-left"></i>',
        self::READY_TO_EXPORT => '<i class="fa fa-caret-right"></i>',
        self::MANDATORY_FIELD_MISSING => '<i class="fa fa-info-circle"></i>',
        self::ERROR => '<i class="fa fa-info-circle"></i>',
        self::BLOCKED => '<i class="fa fa-ban"></i>',
        self::AVAILABLE_IN_MP => '<i class="fa fa-info-circle"></i>',
        self::NOT_AVAILABLE_IN_MP => '<i class="fa fa-info-circle"></i>',
        self::PENDING => '<i class="fa fa-hourglass-end"></i>',
        self::CHANGES_REQUIRED => '<i class="fa fa-info-circle"></i>'
    ];

    const OPTIONS = [
        "available" => ['text' => "Ready to Export",'color' => self::BUTTON_COLOR[self::READY_TO_EXPORT]],
        "connected" => ['text' => "Connected",'color' => self::BUTTON_COLOR[self::CONNECTED]],
        "unavailable" => ['text' => "Incomplete",'color' => self::BUTTON_COLOR[self::MANDATORY_FIELD_MISSING]],
        "error" => ['text' => "Error",'color' => self::BUTTON_COLOR[self::ERROR]],
        "blocked" => ['text' => "Blocked",'color' => self::BUTTON_COLOR[self::BLOCKED]],
//        "in_mp" => ['text' => "In Marketplace",'color' => self::BUTTON_COLOR[self::AVAILABLE_IN_MP]],
//        "not_in_mp" => ['text' => "Not In Marketplace",'color' => self::BUTTON_COLOR[self::NOT_AVAILABLE_IN_MP]],
        "pending" => ['text' => "Pending",'color' => self::BUTTON_COLOR[self::PENDING]],
        "changes_required" => ['text' => "Changes Required",'color' => self::BUTTON_COLOR[self::CHANGES_REQUIRED]],
    ];
}
