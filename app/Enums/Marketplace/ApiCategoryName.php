<?php

namespace App\Enums\Marketplace;

class ApiCategoryName {

    const BIKE_API_ID     = 1; // Don't change
    const B2BUHREN_API_ID = 2; // DOn't change
    const BDROPPY_API_ID  = 3; // Don't change
    const BIGBUY_API_ID   = 4; // Don't change
    const VIDAXL_API_ID   = 5; // Don't change
    const BINO_MERTENS_API_ID = 6; // Don't change
    const BTS_WHOLESALE_API_ID = 7; // Don't change
    const FLORA_LOGISTICS_API_ID = 8; // Don't change
    const VAN_DER_MEER_API_ID = 9; // Don't change

    public static function getApiNameById ($key) {

        if ( $key == self::BIKE_API_ID ) {
            return 'BIKE API';
        } else if ( $key == self::B2BUHREN_API_ID ) {
            return 'B2BUHREN API';
        } else if ( $key == self::BDROPPY_API_ID ) {
            return 'BDROPPY API';
        } else if ( $key == self::BIGBUY_API_ID ) {
            return 'BIGBUY API';
        } else if ( $key == self::VIDAXL_API_ID ) {
            return 'VIDAXL API';
        }
    }
    // if here add new color for new supplier then add new color in this array
    // and mp frontend ShippingGroupColorList enums class
    const SHIPPING_GROUP_COLOR = [
        'Blue'      => '14297', // ISL GmbH
        'Black'     => '13543', // b2bhuren supplier
        'Crimson'   => '13565', // BDroppy Supplier
        'Gold'      => '15035', // ISL GmbH
        'Indigo'    => '15049', // Labona GmbH
        'Yellow'    => '15003', // BigBuy
        'Thistle'     => '13156', // BikeApi Supplier
        'Violet'    => '14406', // GW nature cosmetic GmbH
        'Pink'      => '50034', // BTSWholesaler
        'Orange'    => '15039', // IEA International Trading GmbH
        'Coral'     => '13058', // DROPMATIX SYSTEMA SL
        'Wheat'     => '13062', // Dropmatix Systema SL
        'Springgreen'   => '14379', // eLADY Azure
        'Darkred'  => '13077', // 2636
        'Green'      => '50101', // Flora Logistics
        'Burlywood'     => '50180', // FBBoxx GmbH
        'Blueviolet'     => '14342', // Goodsport
        'Aqua'     => '50152', // eccentro GmbH
        'Tomato'      => '15015', // ILA Uhren GmbH
        'Orchid'      => '14409', // IEA International Trading GmbH
        'Peru'      => '14956', // Underwood and Carlson Co
        'Plum'      => '50131', // Camperfriend GbR
        'Silver'    => '50211', // Woru GmbH
        'Lime'    => '14396', // pagra natur GbR
        'Cadetblue'  => '50198', // Brine GmbH  Navy Blue
        'Olive'    => '49997', // Mertens GmbH
        'Turquoise' => '15004', // Winch GmbH
        'Sienna'    => '13072', // ABATEC Sp. z o.o. Lavender
        'Fuchsia'     => '14998', // ISL GmbH
        'Cyan'     => '14398', // plus H GmbH & Co. KG
        'Bisque'  => '13054', // Demo LTD
        'Teal'    => '50040', // ">
        'Tan'     => '50073', // Friedmann Test
        'Silver'   => '50164', // Stray Camp GmbH Mintcream
        'Brown'      => '14386', // Terra Vegane
        'Teal'      => '14366', // trends4cents Groß- und Einzelhandels-GmbH
        'Gray'      => '50271', // VAN DER MEER
        'Purple'    => '15021', // VidaXL
        'Salmon'     => '13504', // ztrada.com GmbH Beige
        'Maroon'    => '15009', // Plush
        'Red'       =>'2', // fulfilment
    ];

    //  const SHIPPING_GROUP_COLOR = [
    //     'Blue'      => 13156, // bike API
    //     'Black'     => 1188, // b2buhren
    //     //''        => 13565, // bdroppy
    //     'Yellow'    => 9, // bigbuy
    //     'Purple'    => 532, // vidaXL
    //     'Orange'    => 49997, // BinoMertens
    //     'Pink'      => 1055, // BTSWholesaler
    //     'Green'     => 1072, // FloraLogistics
    //     'Gray'      => 1090, //  van der meer
    //     'Maroon'     => 1052, // Plush
    //     'Red'       => 2, // fulfilment   
    //  ];
}
