<?php

namespace App\Enums\Marketplace;

class ProductStatus {
    const PENDING = 0;
    const INTERNAL_SYNC_APPROVED = 0;
    const ACTIVE = 1;
    const INACTIVE = 2;
    const INCOMPLETE = 3;
    const BLOCKED = 4;
    const WAITING_SYNC = 5;
    const GOODS_RECEIVED = 6;
    const QUALITY_DEFECT = 7;

    public static function getStatus ($key) {
        if ( $key == self::PENDING ) {
            return '<i class="text-warning">Pending</i>';
        } else if ( $key == self::ACTIVE ) {
            return '<i class="text-success">Approved</i>';
        } else if ( $key == self::INACTIVE ) {
            return '<i class="text-danger">Rejected</i>';
        } else if ( $key == self::INCOMPLETE ) {
            return '<i class="text-warning">Incomplete</i>';
        } else if ( $key == self::BLOCKED ) {
            return '<i class="text-danger">Blocked</i>';
        }
    }
}
