<?php

namespace App\Events;

use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Contracts\Broadcasting\ShouldBroadcastNow;

class OffersSyncEvent extends Event implements ShouldBroadcastNow
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($message)
    {
        $this->message = $message;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array
     */
    public function broadcastOn(): array
    {
        return ['offers_sync_progress'];
    }
    public function broadcastAs(): string
    {
      $user_id = CRUDBooster::myParentId();
      return 'offers_sync_progress'.$user_id;
    }
}
