<?php

namespace App\Exceptions;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class DropmatixException extends \Exception
{
    /**
     * Render the exception into an HTTP response.
     */
    public function render(Request $request): JsonResponse
    {
        $status_code = $this->getCode();
        $status_code = $status_code > 0 ? $status_code : JsonResponse::HTTP_BAD_REQUEST;

        $data = ['success' => false, 'message' => $this->getMessage()];

        if ($request->expectsJson()) {
            return response()->json($data, $status_code);
        }
    }
}
