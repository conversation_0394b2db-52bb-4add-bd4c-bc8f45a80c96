<?php
namespace App\Exports;

use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithHeadings;
use App\UpcomingInvoice;
use App\NewOrder;
use ZipArchive;
use Exception;
use File;

class InvoicesExport implements FromView
{
    use Exportable {
        Exportable::download as exportDownload;
    }

	protected $invoices;
	protected $orders;
	protected $page;

    public function __construct(array $data)
    {
        $this->invoices = UpcomingInvoice::with(['supplier', 'invoice_category'])->find($data['invoice']);
        $this->orders = NewOrder::with(['customer', 'client', 'logs'])->find($data['order']);
        $this->page = $data['page'] ?? '';
    }

    public function view(): View
    {
        return view('admin.accounting.exports.xls', [
            'invoices' => $this->invoices,
            'orders' => $this->orders,
            'page' => $this->page,
        ]);
    }


    public function getLogs(): string
    {
        return app(\App\Services\Accounting\ArchiveLog::class)->logs($this->orders, $this->invoices);
    }


    public function download(string $fileName, string $writerType = null)
    {
        $logs = $this->getLogs();
        return $logs ? $this->downloadWithLog($fileName, $writerType, $logs) : $this->exportDownload($fileName, $writerType);
    }

    private function downloadWithLog(string $fileName, string $writerType = null, string $logs = '')
    {
        $zip = new ZipArchive(); // Load zip library
        $filename = pathinfo($fileName, PATHINFO_FILENAME).'.zip';

        $path = public_path().'/accounting_xml_archives';
        File::makeDirectory($path, 0777, true, true);

        $zip_name = "accounting_xml_archives/{$filename}"; // Zip name

        try {

            $content = $this->raw($writerType);

            if ($zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE)) {

                $zip->addFromString($fileName, $content);
                $zip->addFromString('logs.txt', $logs);

                $zip->close();

                if(!file_exists($zip_name)) throw new Exception('Sorry, Failed to download file.');

                ob_end_clean();
                ob_start();
                $response = response(file_get_contents(realpath($zip_name)), 200);
                $response->header('Content-Type', 'application/zip');
                $response->header('Content-Disposition', 'attachment; filename="'.$filename.'"');

                return $response;

            }else {
                throw new Exception('Sorry, Failed to download your file.');
            }
            
        }finally {
            @unlink(realpath($zip_name));
        }
    }
}