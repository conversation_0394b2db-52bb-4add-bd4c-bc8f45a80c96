<?php

namespace App\Exports;
use DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;

use Maatwebsite\Excel\Concerns\FromCollection;

class ProjectTimeExport implements FromCollection
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return DB::table('drm_project_time_tracker')
        ->leftJoin('drm_projects','drm_project_time_tracker.project_id','drm_projects.id')
        ->leftJoin('drm_customers','drm_projects.drm_customer_id','drm_customers.id')
        ->join('cms_users','cms_users.id','drm_project_time_tracker.user_id')
        ->select('cms_users.name','drm_projects.title as ptitle','drm_project_time_tracker.title','drm_customers.full_name','drm_project_time_tracker.note','drm_project_time_tracker.date','drm_project_time_tracker.duration','drm_project_time_tracker.start_time')
        ->where('drm_project_time_tracker.user_id',CRUDBooster::myParentId())
        //->select('drm_project_time_tracker.*','cms_users.*','drm_projects.title as ptitle','drm_projects.drm_customer_id','drm_customers.full_name')
        ->get();
    }
}
