<?php
namespace App\Helper;
use DB;
use App\NewOrder;
use App\InvoiceTranslation;
use Illuminate\Support\Facades\Storage;

class DRMInvoice{
	private $order;
	private $customer;
	private $products;


	private $tax_id = null;
	private $is_german = false;
	private $tax_rate = 16;

	private $default_tax = 16;
	

	private $page_title = 'DRM Invoice';
	private $setting = null;
	private $blade_file= 'invoice.daily';
	private $is_local = false;

	private $INV_TYPE = 'GENERAL';

	private $user_id;
	private $order_id;
	private $insert_type;

	private $translate;


	public function __construct($order_id){
		$this->init($order_id);
	}


	//Return pdf stream
	public function invoice($stream = false){
		$data = $this->getCompactData();

		$pdf = \PDF::loadView($this->blade_file, $data)->setWarnings(false)->stream();

		if($stream){
			return $pdf;
		}else{
			$pdf_path = 'order_invoice_new/invoice_'.$this->order_id.'.pdf';
			Storage::disk('spaces')->put($pdf_path, $pdf, 'public');
			if(Storage::disk('spaces')->exists($pdf_path)){
				return Storage::disk('spaces')->url($pdf_path);
			}
			return null;
		}
	}

	//set tax related data by coustomer country name
	private function setTaxData($customer_country){
		$tax_data = null;
		if($customer_country){
			$tax_data = DB::table('tax_rates')
			->select('id', 'charge')
			->where('country_code', 'like', $customer_country)
			->orWhere('country', 'like', $customer_country)
			->orWhere('country_de', 'like', $customer_country)
			->first();
		}

		$tax_rate = ($tax_data)? (float)$tax_data->charge : (float) $this->default_tax;

		$this->tax_id = $tax_id = ($tax_data)? $tax_data->id : null;
		$this->is_german = $is_german = ( is_null($tax_id) || ($tax_id == '') || ($tax_id == 4) )? true : false;

		$order_date = strtotime($this->order->order_date);
		$date = strtotime("07/01/2020");
		$tax_rate = ($is_german && $order_date < $date)? 19 : $tax_rate;

		$this->tax_rate = ( $is_german && ($this->setting->small_business == 1) )? 0 : $tax_rate;

		$this->setBladeFile();
	}

	//Initialize invoice data
	private function init($order_id){
		$this->order_id = $order_id;
		$this->order = NewOrder::with('customer')->find($this->order_id);
		$this->setting = DB::table('drm_invoice_setting')->where('cms_user_id', $this->order->cms_user_id)->first();
		$this->insert_type = $this->order->insert_type;
		// $this->is_local = $local;

		$this->customer = $this->order->customer;
		$this->user_id = $this->order->cms_user_id;
		$this->products = json_decode($this->order->cart);

		$this->default_tax = ($this->order->cms_user_id == 98)? 21 : 16;

		//Initialize tax data
		$this->setTaxData($this->customer->country); //Call tax function
	}

	private function getCompactData(){
		$data = [];
		$data['page_title'] = $this->page_title;
		$data['order'] = $this->order;
		$data['product_list'] = $this->products;
		$data['customer'] = $this->customer;
		$data['setting'] = $this->setting;

		$data['tax_rate'] = $this->tax_rate;

		$data['LABEL'] = (object)$this->translate;
		return $data;
	}

	private function setBladeFile(){

		if($this->user_id == 98){
			// $this->blade_file = ($this->insert_type == 4)? 'invoice.charge_inv' : 'invoice.daily';
			// $this->INV_TYPE = ($this->insert_type == 4)? 'CHARGE' : 'DAILY';
			// $this->blade_file = ($this->insert_type == 4)? 'invoice.charge_inv' : 'invoice.daily';

			if($this->insert_type == 4){
				$this->INV_TYPE = 'CHARGE';
				$this->blade_file = 'invoice.charge_inv';
			}else{
				$this->INV_TYPE = 'DAILY';
			}
		}

		// else{
		// 	// $this->blade_file = 'invoice.general';
		// 	$this->INV_TYPE = 'GENERAL';
		// }

		

		// $pdf_view = ($this->user_id == 98)? 'invoice.daily' : 'admin.';
		// $this->blade_file = ($this->insert_type == 4)? 'admin.invoice.charge_inv' : $pdf_view;
		$this->setTranslationData();
	}

	private function setTranslationData(){
		$default = config('inv_tr.'.$this->INV_TYPE);
		$translation_data = ($this->tax_id)? InvoiceTranslation::where('tax_id', $this->tax_id)->first() : null;
		$this->translate = ($translation_data)? $translation_data->data : $default;
	}

}