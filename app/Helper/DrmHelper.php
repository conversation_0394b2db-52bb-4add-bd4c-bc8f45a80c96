<?php

// use App\\App\TrakerPageView;
// use App\\App\TrakerTimeSpent;
// use App\Models\\App\Models\SubUserPermission;
// use App\\App\UserNotificationSetting;
// use Carbon\Carbon;
// use crocodicstudio\crudbooster\helpers\CRUDBooster;
// use Illuminate\Support\Facades\DB;
// use Session;
// use NotificationChannels\Telegram\TelegramChannel;

use App\Enums\Actions;
use App\Services\ProcessManager\ProductProcess;
use App\User;
use App\DrmProduct;
use App\DrmUserCredit;
use App\Enums\CreditType;
use App\DrmUserCreditAddLog;
use App\Jobs\UpdateProductRq;
use App\DrmUserCreditRemoveLog;
use App\Traits\TariffEligibility;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use GuzzleHttp\Exception\ClientException;
use App\Http\Controllers\tariffController;
use App\Services\Tariff\Credit\ChargeCredit;
use App\Services\Tariff\Credit\CreditService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;

function drm_convert_european_to_decimal($number, $format = 1)
{
    $sign_normal = array('£', '€', '$');
    $signs_utf8 = makeArrayUtf8($sign_normal);
    $number = str_replace($signs_utf8, '', $number);
    $number = str_replace($sign_normal, '', $number);
    $number = str_replace(' ', '', $number);
    try {
        if ($format == 1) {
            $fmt = new NumberFormatter('de_DE', NumberFormatter::DECIMAL);
        } else {
            $fmt = new NumberFormatter('en_EN', NumberFormatter::DECIMAL);
        }
        // if($price == 0){
        //   if($format == 1){
        //     $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
        //     $price = $fmt->parse($number);
        //   }
        //   else {
        //     $fmt = new NumberFormatter( 'en_EN', NumberFormatter::DECIMAL );
        //     $price = $fmt->parse($number);
        //   }
        // }
        return $fmt->parse($number);
    } catch (Exception $e) {
        $val = str_replace(",", ".", $number);
        $val = preg_replace('/\.(?=.*\.)/', '', $val);
        return floatval($val);
    }
}

function deNumberFormatterAuto($number, $format = 1)
{
    $sign_normal = array('£', '€', '$', 'kg');
    $signs_utf8 = makeArrayUtf8($sign_normal);
    $number = str_replace($signs_utf8, '', $number);
    $number = str_replace($sign_normal, '', $number);
    $number = str_replace(' ', '', $number);
    try {
        if ($format == 1) {
            $fmt = new NumberFormatter('de_DE', NumberFormatter::DECIMAL);
        } else {
            $fmt = new NumberFormatter('en_EN', NumberFormatter::DECIMAL);
        }
        $price = $fmt->parse($number);

        if ($price == 0) {
            if ($format == 1) {
                $fmt = new NumberFormatter('en_EN', NumberFormatter::DECIMAL);
                $price = $fmt->parse($number);
            } else {
                $fmt = new NumberFormatter('en_EN', NumberFormatter::DECIMAL);
                $price = $fmt->parse($number);
            }
        }
        return $price;
    } catch (Exception $e) {
        $val = str_replace(",", ".", $number);
        $val = preg_replace('/\.(?=.*\.)/', '', $val);
        return floatval($val);
    }
}

function parse_shop_url($url)
{
    $url = rtrim($url, '/') . '/';
    if (strpos($url, '://') === false) {
        $url = 'https://' . $url;
    }

    $parsedUrl = parse_url($url);
    $parsedUrl['scheme'] = 'https';

    $finalUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
    if (isset($parsedUrl['path'])) {
        $finalUrl .= $parsedUrl['path'];
    }
    if (isset($parsedUrl['query'])) {
        $finalUrl .= '?' . $parsedUrl['query'];
    }
    if (isset($parsedUrl['fragment'])) {
        $finalUrl .= '#' . $parsedUrl['fragment'];
    }
    return $finalUrl;
}

function sanitize_ftp_url($url) {
    $parsedUrl = parse_url($url);
    if (isset($parsedUrl['host'])) {
        return $parsedUrl['host'];
    } else {
        return $parsedUrl['path'] ?? null;
    }
}



function makeInt($str): int
{
    return (int)$str;
}

function jsonFixer($json)
{
    $patterns = [];
    /** garbage removal */
    $patterns[0] = "/([\s:,\{}\[\]])\s*'([^:,\{}\[\]]*)'\s*([\s:,\{}\[\]])/"; //Find any character except colons, commas, curly and square brackets surrounded or not by spaces preceded and followed by spaces, colons, commas, curly or square brackets...
    $patterns[1] = '/([^\s:,\{}\[\]]*)\{([^\s:,\{}\[\]]*)/'; //Find any left curly brackets surrounded or not by one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[2] = "/([^\s:,\{}\[\]]+)}/"; //Find any right curly brackets preceded by one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[3] = "/(}),\s*/"; //JSON.parse() doesn't allow trailing commas
    /** reformatting */
    $patterns[4] = '/([^\s:,\{}\[\]]+\s*)*[^\s:,\{}\[\]]+/'; //Find or not one or more of any character except spaces, colons, commas, curly and square brackets followed by one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[5] = '/["\']+([^"\':,\{}\[\]]*)["\']+/'; //Find one or more of quotation marks or/and apostrophes surrounding any character except colons, commas, curly and square brackets...
    $patterns[6] = '/(")([^\s:,\{}\[\]]+)(")(\s+([^\s:,\{}\[\]]+))/'; //Find or not one or more of any character except spaces, colons, commas, curly and square brackets surrounded by quotation marks followed by one or more spaces and  one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[7] = "/(')([^\s:,\{}\[\]]+)(')(\s+([^\s:,\{}\[\]]+))/"; //Find or not one or more of any character except spaces, colons, commas, curly and square brackets surrounded by apostrophes followed by one or more spaces and  one or more of any character except spaces, colons, commas, curly and square brackets...
    $patterns[8] = '/(})(")/'; //Find any right curly brackets followed by quotation marks...
    $patterns[9] = '/,\s+(})/'; //Find any comma followed by one or more spaces and a right curly bracket...
    $patterns[10] = '/\s+/'; //Find one or more spaces...
    $patterns[11] = '/^\s+/'; //Find one or more spaces at start of string...

    $replacements = [];
    /** garbage removal */
    $replacements[0] = '$1 "$2" $3'; //...and put quotation marks surrounded by spaces between them;
    $replacements[1] = '$1 { $2'; //...and put spaces between them;
    $replacements[2] = '$1 }'; //...and put a space between them;
    $replacements[3] = '$1'; //...so, remove trailing commas of any right curly brackets;
    /** reformatting */
    $replacements[4] = '"$0"'; //...and put quotation marks surrounding them;
    $replacements[5] = '"$1"'; //...and replace by single quotation marks;
    $replacements[6] = '\\$1$2\\$3$4'; //...and add back slashes to its quotation marks;
    $replacements[7] = '\\$1$2\\$3$4'; //...and add back slashes to its apostrophes;
    $replacements[8] = '$1, $2'; //...and put a comma followed by a space character between them;
    $replacements[9] = ' $1'; //...and replace by a space followed by a right curly bracket;
    $replacements[10] = ' '; //...and replace by one space;
    $replacements[11] = ''; //...and remove it.

    return preg_replace($patterns, $replacements, $json);
}

function drm_fix_image($string, $assoc = false, $fixNames = true)
{

    // $string='[{id:1,src:VSPOQ0618.jpg},];';
    // $string = str_replace("id",'"id"',$string);
    // $string = str_replace("src",'"src"',$string);
    // $string= preg_replace('!(http|https|ftp|scp)(s)?:\/\/[a-zA-Z0-9.?%=&_/]+!', "\"\\0\"", $string);
    // $string = str_replace(",];",']',$string);

    // //$s = str_replace(":",':"',$s);
    // $s = str_replace(',"',',',$s);
    // $s = str_replace('}','"}',$s);
    // $s = str_replace(';','',$s);
    // dd($string);
    $string = str_replace("\r\n", '', $string);
    //  dd($string);
    return json_decode($string, $assoc);
}

function drm_fallback_image(): string
{
    return app()->getlocale() == 'de' ? asset('../images/picture-not-available-de.jpg') : asset('../images/picture-not-available-en.jpg');
}

function getProductListByUserId($user_id, $limit = null)
{
    $products_list = DB::table('drm_products')
        ->join('drm_imports', 'drm_products.drm_import_id', '=', 'drm_imports.id')
        ->select('drm_products.*')
        ->where('drm_imports.user_id', $user_id);

    if ($limit != null)
        $products_list->limit($limit);

    // $products_list->get();

    return $products_list->get();

}

function checkImageUrl($external_link): bool
{
    $curl = curl_init($external_link);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    $data = curl_exec($curl);
    curl_close($curl);
    return $data;
}


function checkRemoteFile($url): bool
{
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    // don't download content
    curl_setopt($ch, CURLOPT_NOBODY, 1);
    curl_setopt($ch, CURLOPT_FAILONERROR, 1);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

    $result = curl_exec($ch);
    curl_close($ch);
    if ($result !== FALSE) {
        return true;
    } else {
        return false;
    }
}

/**
 * URL before:
 * https://example.com/orders/123?order=ABC009
 *
 * 1. add_query_params(['status' => 'shipped'])
 * 2. add_query_params(['status' => 'shipped', 'coupon' => 'CCC2019'])
 *
 * URL after:
 * 1. https://example.com/orders/123?order=ABC009&status=shipped
 * 2. https://example.com/orders/123?order=ABC009&status=shipped&coupon=CCC2019
 */
function drm_helper_add_query_params(array $params = []): string
{
    $query = array_merge(
        request()->query(),
        $params
    ); // merge the existing query parameters with the ones we want to add

    return url()->current() . '?' . http_build_query($query); // rebuild the URL with the new parameters array
}

function csvToArray($url): array
{
    ini_set('memory_limit', -1);
    $csvData = file_get_contents_utf8($url);
    $lines = explode(",", $csvData);
    if ($lines[0] != null) {
        $key = str_getcsv($lines[0]);
    }
    unset($lines[0]);
    $array = array();
    $chunks = array_chunk($lines, 10);
    foreach ($chunks as $chunk) {
        foreach ($chunk as $line) {
            $spreadsheet = str_getcsv($line);
            $diff = count($key) - count($spreadsheet);
            if ($diff > 0) {
                for ($i = 0; $i < $diff; $i++) {
                    $spreadsheet[] = "";
                }
            }
            if (!containsOnlyNull($spreadsheet)) {
                if (count($key) == count($spreadsheet)) {
                    $array[] = array_combine($key, $spreadsheet);
                }
            }

        }
    }
    return $array;
}


function getCsvHeader($url)
{
    ini_set('memory_limit', -1);
    $csvData = file_get_contents_utf8($url);
    $lines = explode(PHP_EOL, $csvData);
    if ($lines[0] != null) {
        $key = str_getcsv($lines[0]);
    }
    $key_utf8 = makeArrayUtf8($key ?? array());
    return json_encode($key_utf8);
}

function file_get_contents_utf8($fn)
{
    try {
        $content = file_get_contents($fn);
        $text = mb_convert_encoding($content, "UTF-8", "WINDOWS-1252");
        return mb_convert_encoding($text, 'UTF-8',
            mb_detect_encoding($text, 'UTF-8, ISO-8859-15', true));
    } catch (\Throwable $th) {
        return null;
    }

}

function getDemoData($url)
{
    ini_set('memory_limit', -1);
    $csvData = file_get_contents_utf8($url);
    $lines = explode(PHP_EOL, $csvData);
    $demo = str_getcsv($lines[1]);
    return json_encode($demo);
}

function makeArrayUtf8($array = [])
{
    if (is_array($array)) {
        $array = trimArray($array);
        $keys = array_keys($array);
        $keys_utf8 = array_map('makeUtf8', $keys);
        $keys_utf8 = array_map('removeDots', $keys_utf8);
        $utf8 = array_map('makeUtf8', $array);
        $utf8 = array_combine($keys_utf8, $utf8);
        return $utf8;
    } else {
        return $array;
    }
}

function removeDots($key)
{
    return str_replace('.', '', $key);
}

function makeUtf8($text)
{
    if (!is_array($text)) {
        $incov = iconv('UTF-8', 'WINDOWS-1252//TRANSLIT', $text);

        if ($incov == false) {
            $incov = iconv('UTF-8', 'ISO-8859-15', $text);
        }
        if ($incov == false) {
            return $text;
        } else {
            $utf8 = mb_convert_encoding($incov, 'UTF-8',
                mb_detect_encoding($incov, 'UTF-8, ISO-8859-15', true));
        }
        return $utf8;
    } else {
        return makeArrayUtf8($text);
    }
}

function checkArrayKey($key, $array)
{
    array_walk($array, function (&$item) use ($key) {
        $item = array_combine($key, $item);
    });

    $valid = true;
    array_walk($array, function (&$item) use (&$valid) {
        foreach ($item as $key => $value) {
            if ($key == "" && $value != null && $value != "") {
                $valid = false;
            }
        }
    });
    return $valid;
}

function build_query_parameter(array $params = [])
{
    $old_param = request()->all();
    return url()->current() . '?' . http_build_query(array_merge(request()->all(), $params));
}

function containsOnlyNull($input)
{
    if ($input != null) {
        return empty(array_filter($input, function ($a) {
            return $a !== null;
        }));
    } else {
        return true;
    }
}

function removeNullKeys($array = [])
{
    $finalArray = [];
    foreach ($array as $key => $value) {
        if ($key != "" && $key != null) {
            $finalArray[$key] = $value;
        }
    }
    return $finalArray;
}

function trimArray($array)
{
    if (is_array($array)) {
        $a = array_map('trimRecursive', array_keys($array));
        $b = array_map('trimRecursive', $array);
        $array = array_combine($a, $b);
    }
    return $array;
}

function trimRecursive($value)
{
    if (!is_array($value)) {
        $value = trim($value);
    } else {
        $value = array_map('trim', $value);
    }
    return $value;
}

function generateTags($array = [])
{
    $tags = "";
    $i = 0;
    $array = removeNulls($array);
    foreach ($array as $value) {
        $value = str_replace(' - ', '_', $value);
        $value = str_replace('-', '_', $value);
        $i++;
        if ($i == count($array)) {
            if (strpos($value, '#') !== false) {
                $tags .= $value;
            } else {
                $tags .= "#" . $value;
            }
        } else {
            if (strpos($value, '#') !== false) {
                $tags .= $value . ",";
            } else {
                $tags .= "#" . $value . ",";
            }
        }
    }
    return $tags;
}

function removeNulls($array = [])
{
    if (is_array($array)) {
        foreach ($array as $key => $value) {
            $i++;
            if ($value == null && $value == "") {
                unset($array[$key]);
            }
        }
    }
    return $array;
}

function arrayNullCount($array = [])
{
    $i = 0;
    foreach ($array as $key => $value) {
        if ($value == null || $value == "") {
            $i++;
        }
    }
    return $i;
}


function drmPriceCalculationNew($config)
{
    $profit = false;
    if ($config['vk_field'] == null) {
        $profit = true;
    }
    $ek_price = drm_convert_european_to_decimal($config['ek_price'], $config['money_format']);

    if ($profit) {
        $profit_margin = $config['profit_margins']
            ->where('price_from', '<=', $ek_price)
            ->where('price_to', '>=', $ek_price)
            ->first();
        if ($profit_margin != null) {
            $price = $ek_price + $ek_price
                * (floatval($profit_margin->profit_percent) / 100)
                + drm_convert_european_to_decimal($profit_margin->shipping_cost, 2)
                + drm_convert_european_to_decimal($profit_margin->additional_charge_fixed, 2);

            if ($profit_margin->round_scale != null) {
                $has_price = explode('.', $price);
                $first_item_array = $has_price[0];
                $last_item_array = $has_price[1];
                if ($last_item_array == 0) {
                    $price;
                } else {
                    $price = $first_item_array + $profit_margin->round_scale;
                }
            }
        }
    } else {
        $price = drm_convert_european_to_decimal($config['vk_price'], $config['money_format']);
    }
    if ((!$profit && $price == 0) || ($profit && $profit_margin == null)) {
        $fallback = $config['fallback'];
        // dd($fallback);
        if ($fallback != null) {
            $price = $ek_price + $ek_price
                * (floatval($fallback->profit_percent) / 100)
                + drm_convert_european_to_decimal($fallback->shipping_cost, 2)
                + drm_convert_european_to_decimal($fallback->additional_charge, 2);
        }
    }
    $price = (float)str_replace(',', '', number_format($price, 2));
    return $price;
}


function files_are_equal($a, $b)
{
    if (filesize($a) !== filesize($b))
        return false;
    $ah = fopen($a, 'rb');
    $bh = fopen($b, 'rb');
    $result = true;
    while (!feof($ah)) {
        if (fread($ah, 8192) != fread($bh, 8192)) {
            $result = false;
            break;
        }
    }
    fclose($ah);
    fclose($bh);

    return $result;
}

function pathIsUrl($path)
{
    if (filter_var($path, FILTER_VALIDATE_URL)) {
        return true;
    } else {
        return false;
    }
}


function hasBigString($array)
{
    foreach ($array as $key => $value) {
        if (strlen($value) > 100) {
            return true;
        }
    }
    return false;
}

function formatBytes($bytes, $precision = 2)
{
    $units = array("b", "kb", "mb", "gb", "tb");
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= (1 << (10 * $pow));
    return round($bytes, $precision) . " " . $units[$pow];
}

function insertChangeLog($product_id, $value, $status)
{
    $product_id = (int)$product_id;
    $user_id = CRUDBooster::myId();
    $ldate = date('Y-m-d H:i:s');
    $log = DB::table('product_change_logs')->where('product_id', $product_id)->first();
    $log_json = json_decode($log->log, true);
    $insert_log = [
        'time' => $ldate,
        'ip' => $_SERVER['SERVER_ADDR'],
        'field' => $value,
        'status' => $status,
        'user' => $user_id
    ];
    if (is_array($log_json)) {
        if (count($log_json) >= 5) {
            unset($log_json[0]);
            $log_json = array_values($log_json);
        }
    }
    $log_json[] = $insert_log;
    $data = [
        'log' => json_encode($log_json)
    ];
    if ($log != null) {
        DB::table('product_change_logs')->where('product_id', $product_id)->update($data);
    } else {
        $data['product_id'] = $product_id;
        DB::table('product_change_logs')->insert($data);
    }
}

function drmTotalProduct($user_id)
{
    return Cache::remember('user_products_' . $user_id, 10, function () use ($user_id) {
        $user = User::find($user_id,['id', 'id_cms_privileges']);
        // if ( User::isSupplier($user_id) ) {
        if ($user->id_cms_privileges == 4) {
            return \App\Models\Marketplace\Product::where('supplier_id', $user->id)->count();
        } else {
            return $user ? @$user->products()->count() : 0;
        }
    });

    $user = User::find($user_id);
    // if ( User::isSupplier($user_id) ) {
    if ($user->id_cms_privileges == 4) {
        return \App\Models\Marketplace\Product::where('supplier_id', $user_id)->count();


        // return $user->suppliersProducts ? $user->suppliersProducts()->count() : 0;
    } else {
        return Cache::remember('user_products_' . $user_id, 05.0, function () use ($user) {
            return $user ? @$user->products()->count() : 0;
        });
    }
}

function format_price($price, $format = 2)
{
    if ($format == 2) {
        return $price;
    } else {
        return number_format($price, 2, ',', '.');
    }
}

function removeCommaFromPrice($amount)
{
    $total = $amount;
    if (strpos($total, ",")) {
        $have = [".", ","];
        $will_be = ["", "."];
        $total = str_replace($have, $will_be, $total);
    }
    return $total;
}

function getGlobalorderstatus($val)
{
    $order_status = [
        0 => 'Canceled',
        1 => 'Shipped',
    ];
    return isset($order_status[$val]) ? $order_status[$val] : '';
}

//drm insert type codes
function getInsertTypeName($id)
{
    $arr = [
        1 => 'API', //Shop order sync
        2 => 'VOD', //Daily order sync
        3 => 'Stripe',
        4 => 'Charge', //Not used - paywall charge
        5 => 'Import',
        6 => 'Manual',
        7 => 'Marketplace',
        8 => 'Marketplace sell',
        17 => 'MP Manual',

        9 => 'App',
        10 => 'Import Plan',
        11 => 'Template',
        12 => 'DT licence',
        13 => 'Translate',
        14 => 'Appointment',
        15 => 'Quiz',
        16 => 'MP Category',


//1 => app, 2=> 'translate', 3 => 'appointment', 4 => 'marketplace_product', 5=> import, 6 => monthly_paywall, 7=> drm_subscription, 8 => invoice_payment, 9 => quiz, 10 =>dt, 11 => dtlicense, 12 => mp_category

    ];
    return (isset($arr[$id])) ? $arr[$id] : '';
}

//Get email conent, subject from email page
function DRMParseMailTemplate($tags, $slug, $lang = 'de', $page = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    // $tags = [
    //  'app_name' => 'app TEST',
    //  'app_price' => 30,
    //  'subscription_interval' => 'Monthly',
    //  'period_start' => 'pp',
    //  'period_end' => 'p end',
    //  'INTERVAL' => false,
    //  'FIXED' => false,
    //  'FREESCOUT' => true,
    // ];
    // $slug = 'app_purchase_confirmation';
    $content = null;
    $subject = null;

    if(empty($page))
    {
        $page = Cache::rememberForever($slug.'-'.$lang, function () use ($slug, $lang) {
            $lang = (in_array( $lang, ['de','en'])) ? $lang : 'de';
            return DB::table('cms_email_templates')
                ->where('slug', $slug)
                ->select(['subject', DB::raw('IF(`content_'.$lang.'` IS NOT NULL, `content_'.$lang.'`, `content`) `content`')])
                ->first();
        });
    }


    if ($page) {
        $content = $page->content;
        $subject = $page->subject;

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content];
}


function orderTaxRate($order_id)
{
    $order = DB::table('drm_orders_new')->join('drm_customers', 'drm_orders_new.drm_customer_id', '=', 'drm_customers.id')->where('drm_orders_new.id', $order_id)->select('drm_customers.country as country')->first();
    return (float)countryCodeTax($order->country);
}


//Return tax by country name
function countryCodeTax($country_code, $country_id = false)
{
    $tax_rate = 16;
    $tax_rate_data = null;

    $id = null;
    if ($country_code) {
        // $country_code = strtoupper($country_code);
        $tax_rate_data = DB::table('tax_rates')
            ->select('id', 'charge')
            ->where('country_code', 'like', $country_code)
            ->orWhere('country', 'like', $country_code)
            ->orWhere('country_de', 'like', $country_code)
            ->first();
        $id = $tax_rate_data->id;
    }


    // $tax_rate_data = Cache::rememberForever($country_code, function() use($country_code){
    //   return DB::table('tax_rates')->where('country_code', $country_code)->first();
    // });
    if ($country_id) return $id;
    return ($tax_rate_data) ? (float)$tax_rate_data->charge : (float)$tax_rate;

}

function strParse($string, $data){
    return strtr($string, $data);
}

function removeNullArray($array)
{
    if (is_array($array)) {
        if (!containsOnlyNull($array)) {
            return $array;
        }
    }
}

//Param amount, rate
function orderTaxForNet($amount, $tax_rate)
{
    return $amount / (100 + $tax_rate) * $tax_rate; // Tax.php
}


// Net to brutto (our standard way)
// Net price * 1.19 = gross price (with VAT)
// = 100 EUR net x 1.19 EUR = 119 EUR brutto


// Brutto to Net (new way for the feature)
// = 100 EUR brutto / 1.19 = 84.03 EUR net
//Param amount, rate
function orderNetForNet($amount, $tax_rate)
{
    return $amount / (100 + $tax_rate) * 100;
}

//shop exist check
function isShopExist($shop_id)
{
    $shop = \App\Shop::find((int)$shop_id, ['id']);
    return ($shop) ? true : false;
}

//Billing address format
function formatBillingAddress($address, $one_line = false, $order_id = null, $countryFlag = false)
{
    $tax_number = ($order_id) ? orderTaxNumber($order_id) : null;
    $countries = DB::table('tax_rates')->pluck('country', 'country_code');
    if (drmIsJSON($address)) {
        $address_json = json_decode($address);
        if ($address_json) {
            $html = '';
            $html .= ($address_json->name && !$one_line) ? '<b>' . $address_json->name . '</b><br>' : '';
            // $html .= ($address_json->name)? ($one_line)? ', ': '<br>' : '';
            $html .= ($address_json->company) ? '<b>' . $address_json->company . '</b>' : '';
            $html .= ($address_json->company) ? ($one_line) ? ', ' : '<br>' : '';

            $html .= ($tax_number) ? 'Umsatzsteuernummer: <b>' . $tax_number . '</b>' : '';
            $html .= ($tax_number) ? ($one_line) ? ', ' : '<br>' : '';

            $html .= ($address_json->street) ? $address_json->street : '';
            $html .= ($address_json->street) ? ($one_line) ? ', ' : '<br>' : '';
            // $html .= ($address_json->address)? $address_json->address : '';
            // $html .= ($address_json->address)? ($one_line)?  ', ': '<br>' : '';
            // $html .= ($address_json->state)? $address_json->state : '';
            // $html .= ($address_json->state)? ($one_line)?  ', ': '<br>' : '';
            $html .= ($address_json->zip_code) ? $address_json->zip_code . ' ' : '';
            $html .= ($address_json->city) ? $address_json->city : '';
            $html .= ($address_json->city) ? ($one_line) ? ', ' : '<br>' : '';

            $country_name = ($address_json->country) ? $address_json->country : null;
            if ($countries && $country_name && (strlen($country_name) < 5)) {
                foreach ($countries as $iso => $value) {
                    if (strcasecmp($iso, $country_name) == 0) {
                        $country_name = $value;
                        break;
                    }
                }
            }

            $country = ($country_name) ? $country_name : 'Germany';
            if($countryFlag && $country_code = getCountryCode($country))
            {
                $country = '<img class="order-p-country-flag" src="https://flagsapi.com/'.$country_code.'/shiny/64.png">'.$country;
            }

            // dd($country_name);
            $html .= $country;
            $html .= '<br>';
            return $html;
        }
    } else {
        if ($countries) {
            foreach ($countries as $iso => $value) {
                $iso_len = strlen($iso);
                $pos = strripos($address, '>' . $iso);
                if ($pos === false) {
                    continue;
                } else {
                    $sub = trim(strip_tags(substr($address, ($pos + $iso_len + 1))));
                    if (strlen($sub) === 0) return preg_replace('~' . $iso . '(?!.*' . $iso . ')~i', $value, $address);
                }
            }
        }
    }
    return $address;
}

//Stripe billing address
function stripeBillingAddress($customer, $one_line = false, $order_id = null)
{
    $tax_number = ($order_id) ? orderTaxNumber($order_id) : null;
    $countries = DB::table('tax_rates')->pluck('country', 'country_code');
    $html = '';
    $html .= ($customer->full_name) ? '<b>' . $customer->full_name . '</b><br>' : '';
    $html .= ($customer->company_name) ? '<b>' . $customer->company_name . '</b><br>' : '';
    $html .= ($tax_number) ? 'Tax.php Number: <b>' . $tax_number . '</b><br>' : '';
    $html .= ($customer->address) ? $customer->address . '<br>' : '';
    // $html .= ($customer->state)? $customer->state .'<br>': '';
    $html .= ($customer->zip_code) ? $customer->zip_code . ' ' : '';
    $html .= ($customer->city) ? $customer->city . '<br>' : '';

    $country_name = ($customer->country) ? $customer->country : null;

    if ($countries && $country_name && (strlen($country_name) < 5)) {
        foreach ($countries as $iso => $value) {
            if (strcasecmp($iso, $country_name) == 0) {
                $country_name = $value;
                break;
            }
        }
    }
    $html .= ($country_name) ? $country_name : 'Germany';
    $html .= '<br>';
    return $html;
}


//Billing address format
function formatCustomerInfo($customer)
{
    $countries = DB::table('tax_rates')->pluck('country', 'country_code');
    if ($customer) {

        $html = '';
        $html .= ($customer->full_name) ? 'Name: <b>' . $customer->full_name . '</b><br>' : '';
        $html .= ($customer->company_name) ? 'Company Name: <b>' . $customer->company_name . '</b>' : '';

        $html .= '<br>';
        $html .= ($customer->address) ? 'Address: ' . $customer->address : '';
        // $html .= ($customer->state)? ', '.$customer->state : '';
        $html .= ($customer->zip_code) ? ', ' . $customer->zip_code : '';
        $html .= ($customer->city) ? ', ' . $customer->city : '';

        $country_name = $customer->country;
        if ($country_name) {
            if ($countries && $country_name && (strlen($country_name) < 5)) {
                foreach ($countries as $iso => $value) {
                    if (strcasecmp($iso, $country_name) == 0) {
                        $country_name = $value;
                        break;
                    }
                }
            }
        }


        $html .= ($country_name) ? ', ' . $country_name : 'Germany';
        $html .= '<br>';

        $html .= '<p>';
        $html .= ($customer->email) ? 'E-mail: ' . $customer->email . '<br>' : '';
        $html .= ($customer->phone) ? 'Phone: ' . $customer->phone . '<br>' : '';
        $html .= '</p>';

        return $html;
    }
}

//Formate currency
function formatCurrency($currency = null)
{
    $currency_symbols = array(
        'AED' => '&#1583;.&#1573;', // ?
        'AFN' => '&#65;&#102;',
        'ALL' => '&#76;&#101;&#107;',
        'AMD' => '',
        'ANG' => '&#402;',
        'AOA' => '&#75;&#122;', // ?
        'ARS' => '&#36;',
        'AUD' => '&#36;',
        'AWG' => '&#402;',
        'AZN' => '&#1084;&#1072;&#1085;',
        'BAM' => '&#75;&#77;',
        'BBD' => '&#36;',
        'BDT' => '&#2547;', // ?
        'BGN' => '&#1083;&#1074;',
        'BHD' => '.&#1583;.&#1576;', // ?
        'BIF' => '&#70;&#66;&#117;', // ?
        'BMD' => '&#36;',
        'BND' => '&#36;',
        'BOB' => '&#36;&#98;',
        'BRL' => '&#82;&#36;',
        'BSD' => '&#36;',
        'BTN' => '&#78;&#117;&#46;', // ?
        'BWP' => '&#80;',
        'BYR' => '&#112;&#46;',
        'BZD' => '&#66;&#90;&#36;',
        'CAD' => '&#36;',
        'CDF' => '&#70;&#67;',
        'CHF' => '&#67;&#72;&#70;',
        'CLF' => '', // ?
        'CLP' => '&#36;',
        'CNY' => '&#165;',
        'COP' => '&#36;',
        'CRC' => '&#8353;',
        'CUP' => '&#8396;',
        'CVE' => '&#36;', // ?
        'CZK' => '&#75;&#269;',
        'DJF' => '&#70;&#100;&#106;', // ?
        'DKK' => '&#107;&#114;',
        'DOP' => '&#82;&#68;&#36;',
        'DZD' => '&#1583;&#1580;', // ?
        'EGP' => '&#163;',
        'ETB' => '&#66;&#114;',
        'EUR' => '&#8364;',
        'FJD' => '&#36;',
        'FKP' => '&#163;',
        'GBP' => '&#163;',
        'GEL' => '&#4314;', // ?
        'GHS' => '&#162;',
        'GIP' => '&#163;',
        'GMD' => '&#68;', // ?
        'GNF' => '&#70;&#71;', // ?
        'GTQ' => '&#81;',
        'GYD' => '&#36;',
        'HKD' => '&#36;',
        'HNL' => '&#76;',
        'HRK' => '&#107;&#110;',
        'HTG' => '&#71;', // ?
        'HUF' => '&#70;&#116;',
        'IDR' => '&#82;&#112;',
        'ILS' => '&#8362;',
        'INR' => '&#8377;',
        'IQD' => '&#1593;.&#1583;', // ?
        'IRR' => '&#65020;',
        'ISK' => '&#107;&#114;',
        'JEP' => '&#163;',
        'JMD' => '&#74;&#36;',
        'JOD' => '&#74;&#68;', // ?
        'JPY' => '&#165;',
        'KES' => '&#75;&#83;&#104;', // ?
        'KGS' => '&#1083;&#1074;',
        'KHR' => '&#6107;',
        'KMF' => '&#67;&#70;', // ?
        'KPW' => '&#8361;',
        'KRW' => '&#8361;',
        'KWD' => '&#1583;.&#1603;', // ?
        'KYD' => '&#36;',
        'KZT' => '&#1083;&#1074;',
        'LAK' => '&#8365;',
        'LBP' => '&#163;',
        'LKR' => '&#8360;',
        'LRD' => '&#36;',
        'LSL' => '&#76;', // ?
        'LTL' => '&#76;&#116;',
        'LVL' => '&#76;&#115;',
        'LYD' => '&#1604;.&#1583;', // ?
        'MAD' => '&#1583;.&#1605;.', //?
        'MDL' => '&#76;',
        'MGA' => '&#65;&#114;', // ?
        'MKD' => '&#1076;&#1077;&#1085;',
        'MMK' => '&#75;',
        'MNT' => '&#8366;',
        'MOP' => '&#77;&#79;&#80;&#36;', // ?
        'MRO' => '&#85;&#77;', // ?
        'MUR' => '&#8360;', // ?
        'MVR' => '.&#1923;', // ?
        'MWK' => '&#77;&#75;',
        'MXN' => '&#36;',
        'MYR' => '&#82;&#77;',
        'MZN' => '&#77;&#84;',
        'NAD' => '&#36;',
        'NGN' => '&#8358;',
        'NIO' => '&#67;&#36;',
        'NOK' => '&#107;&#114;',
        'NPR' => '&#8360;',
        'NZD' => '&#36;',
        'OMR' => '&#65020;',
        'PAB' => '&#66;&#47;&#46;',
        'PEN' => '&#83;&#47;&#46;',
        'PGK' => '&#75;', // ?
        'PHP' => '&#8369;',
        'PKR' => '&#8360;',
        'PLN' => '&#122;&#322;',
        'PYG' => '&#71;&#115;',
        'QAR' => '&#65020;',
        'RON' => '&#108;&#101;&#105;',
        'RSD' => '&#1044;&#1080;&#1085;&#46;',
        'RUB' => '&#1088;&#1091;&#1073;',
        'RWF' => '&#1585;.&#1587;',
        'SAR' => '&#65020;',
        'SBD' => '&#36;',
        'SCR' => '&#8360;',
        'SDG' => '&#163;', // ?
        'SEK' => '&#107;&#114;',
        'SGD' => '&#36;',
        'SHP' => '&#163;',
        'SLL' => '&#76;&#101;', // ?
        'SOS' => '&#83;',
        'SRD' => '&#36;',
        'STD' => '&#68;&#98;', // ?
        'SVC' => '&#36;',
        'SYP' => '&#163;',
        'SZL' => '&#76;', // ?
        'THB' => '&#3647;',
        'TJS' => '&#84;&#74;&#83;', // ? TJS (guess)
        'TMT' => '&#109;',
        'TND' => '&#1583;.&#1578;',
        'TOP' => '&#84;&#36;',
        'TRY' => '&#8356;', // New Turkey Lira (old symbol used)
        'TTD' => '&#36;',
        'TWD' => '&#78;&#84;&#36;',
        'TZS' => '',
        'UAH' => '&#8372;',
        'UGX' => '&#85;&#83;&#104;',
        'USD' => '&#36;',
        'UYU' => '&#36;&#85;',
        'UZS' => '&#1083;&#1074;',
        'VEF' => '&#66;&#115;',
        'VND' => '&#8363;',
        'VUV' => '&#86;&#84;',
        'WST' => '&#87;&#83;&#36;',
        'XAF' => '&#70;&#67;&#70;&#65;',
        'XCD' => '&#36;',
        'XDR' => '',
        'XOF' => '',
        'XPF' => '&#70;',
        'YER' => '&#65020;',
        'ZAR' => '&#82;',
        'ZMK' => '&#90;&#75;', // ?
        'ZWL' => '&#90;&#36;',
    );

    $currency = strtoupper($currency);

    if (array_key_exists($currency, $currency_symbols)) {
        return $currency_symbols[$currency];
    }
    return $currency_symbols['EUR'];

}


function drmIsJSON($string)
{
    return is_string($string) && is_array(json_decode($string, true)) && (json_last_error() == JSON_ERROR_NONE) ? true : false;
}

//Return customer info as json
function customerInfoJson($customer_info)
{
    $country = isset($customer_info['country']) ? $customer_info['country'] : null;
    $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
        'name' => isset($customer_info['customer_full_name']) ? $customer_info['customer_full_name'] : null,
        'company' => isset($customer_info['company_name']) ? $customer_info['company_name'] : null,
        'address' => isset($customer_info['address']) ? $customer_info['address'] : null,
        'zip_code' => isset($customer_info['zip_code']) ? $customer_info['zip_code'] : null,
        'city' => isset($customer_info['city']) ? $customer_info['city'] : null,
        'state' => isset($customer_info['state']) ? $customer_info['state'] : null,
        'country' => $country,
    ]);
}

function billingInfoJson($customer_info, $strict = false)
{
    $country = isset($customer_info['country_billing']) ? $customer_info['country_billing'] : (!$strict && isset($customer_info['country']) ? $customer_info['country'] : null);
    // $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
        'name' => isset($customer_info['billing_name']) ? $customer_info['billing_name'] : (!$strict && isset($customer_info['customer_full_name']) ? $customer_info['customer_full_name'] : null),
        'company' => isset($customer_info['billing_company']) ? $customer_info['billing_company'] : (!$strict && isset($customer_info['company_name']) ? $customer_info['company_name'] : null),
        'street' => isset($customer_info['street_billing']) ? $customer_info['street_billing'] : (!$strict && isset($customer_info['address']) ? $customer_info['address'] : null),
        'address' => isset($customer_info['address_billing']) ? $customer_info['address_billing'] : null,
        'zip_code' => isset($customer_info['zipcode_billing']) ? $customer_info['zipcode_billing'] : (!$strict && isset($customer_info['zip_code']) ? $customer_info['zip_code'] : null),
        'city' => isset($customer_info['city_billing']) ? $customer_info['city_billing'] : (!$strict && isset($customer_info['city']) ? $customer_info['city'] : null),
        'state' => isset($customer_info['state_billing']) ? $customer_info['state_billing'] : (!$strict && isset($customer_info['state']) ? $customer_info['state'] : null),
        'country' => $country,
    ]);
}

function shippingInfoJson($customer_info, $strict = false)
{
    $country = isset($customer_info['country_shipping']) ? $customer_info['country_shipping'] : (!$strict && isset($customer_info['country']) ? $customer_info['country'] : null);
    // $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
        'name' => isset($customer_info['shipping_name']) ? $customer_info['shipping_name'] : (!$strict && isset($customer_info['customer_full_name']) ? $customer_info['customer_full_name'] : null),
        'company' => isset($customer_info['shipping_company']) ? $customer_info['shipping_company'] : (!$strict && isset($customer_info['company_name']) ? $customer_info['company_name'] : null),
        'street' => isset($customer_info['street_shipping']) ? $customer_info['street_shipping'] : (!$strict && isset($customer_info['address']) ? $customer_info['address'] : null),
        'address' => isset($customer_info['address_shipping']) ? $customer_info['address_shipping'] : null,
        'zip_code' => isset($customer_info['zipcode_shipping']) ? $customer_info['zipcode_shipping'] : (!$strict && isset($customer_info['zip_code']) ? $customer_info['zip_code'] : null),
        'city' => isset($customer_info['city_shipping']) ? $customer_info['city_shipping'] : (!$strict && isset($customer_info['city']) ? $customer_info['city'] : null),
        'state' => isset($customer_info['state_shipping']) ? $customer_info['state_shipping'] : (!$strict && isset($customer_info['state']) ? $customer_info['state'] : null),
        'country' => $country,
    ]);
}

function customerExportBillShippJson($customer)
{
    $country = isset($customer['country']) ? $customer['country'] : null;
    $country = $country? drmCountryNameFull($country) : $country;

    return json_encode([
        'name' => isset($customer['full_name']) ? $customer['full_name'] : null,
        'company' => isset($customer['company_name']) ? $customer['company_name'] : null,
        'street' => isset($customer['address']) ? $customer['address'] : null,
        'address' => null,
        'zip_code' => isset($customer['zip_code']) ? $customer['zip_code'] : null,
        'city' => isset($customer['city']) ? $customer['city'] : null,
        'state' => isset($customer['state']) ? $customer['state'] : null,
        'country' => $country
    ]);
}

//Stripe payment
function userToBillingJson($user_id)
{
    $user = DB::table('cms_users')->find($user_id, ['name']);
    $billilg_details = DB::table('billing_details')->where('user_id', $user_id)->first();
    if ($billilg_details) {
        $country = DB::table('countries')->find($billilg_details->country_id, ['name']);
        $data = [
            'customer_full_name' => $user->name,
            'company_name' => $billilg_details->company_name,
            'street_billing' => $billilg_details->address,
            'zipcode_billing' => $billilg_details->zip,
            'city_billing' => $billilg_details->city,
            'country_billing' => $country->name,
        ];
        return billingInfoJson($data);
    }
    return null;
}

//Customer to billing
function customerToBillingJson($customer)
{
    if ($customer) {
        $data = [
            'customer_full_name' => $customer->full_name,
            'company_name' => $customer->company_name,
            'street_billing' => $customer->address,
            'zipcode_billing' => $customer->zip_code,
            'city_billing' => $customer->city,
            'state_billing' => $customer->state,
            'country_billing' => $customer->country,
        ];
        return billingInfoJson($data);
    }
    return null;
}

//Customer to billing
function customerToShippingJson($customer)
{
    if ($customer) {
        $data = [
            'customer_full_name' => $customer->full_name,
            'company_name' => $customer->company_name,
            'street_shipping' => $customer->address,
            'zipcode_shipping' => $customer->zip_code,
            'city_shipping' => $customer->city,
            'state_shipping' => $customer->state,
            'country_shipping' => $customer->country,
        ];
        return shippingInfoJson($data);
    }
    return null;
}

//update billing / shipping address json
function updateBillingShippingAddress($new_value, $old_value)
{
    if (drmIsJSON($new_value) && drmIsJSON($old_value)) {
        $json_data = [];
        $new_json = json_decode($new_value, true);
        $old_json = json_decode($old_value, true);

        foreach ($new_json as $key => $value) {
            $json_data[$key] = ((is_null($new_json[$key]) || ($new_json[$key] == '')) && isset($old_json[$key])) ? $old_json[$key] : $new_json[$key];
        }
        foreach ($old_json as $key => $value) {
            if (!isset($json_data[$key])) $json_data[$key] = $old_json[$key];
        }
        return json_encode($json_data);
    }
    return drmIsJSON($new_value) ? $new_value : (drmIsJSON($old_value) ? $old_value : billingInfoJson([]));
}

function vodCustomerAddress($customer)
{
    if ($customer) {
        return json_encode([
            'name' => $customer->full_name,
            'company' => $customer->company_name,
            'street' => $customer->address,
            'zip_code' => $customer->zip_code,
            'city' => $customer->city,
            'state' => $customer->state,
            'country' => $customer->country,
        ]);
    } else {
        return null;
    }
}

function vodCustomerInfo($customer)
{
    if ($customer) {
        return json_encode([
            'name' => $customer->full_name,
            'company' => $customer->company_name,
            'address' => $customer->address,
            'zip_code' => $customer->zip_code,
            'city' => $customer->city,
            'state' => $customer->state,
            'country' => $customer->country,
        ]);
    } else {
        return null;
    }
}

function billingAddressFields()
{
    return [
        'name',
        'company',
        'country',
        'zip_code',
        'city',
        'street',
        // 'state',
    ];
}

function customerInfoFields()
{
    return [
        'full_name',
        'company_name',
        'country',
        'zip_code',
        'city',
        'address',
        // 'state',
        'vat_number',
        'tax_number',
    ];
}

function invCustomerInputlabel($field){
    $data = [
        'full_name' => 'name',
        'company_name' => 'company',
        'address' => 'street',
        'city' => 'city',
        'zip_code' => 'zip_code',
    ];

    return $data[$field]?? $field;
}

//Customer  to customer_info json data generate -- After update customer - order update
function customerToCustomerInfoJson($customer)
{
    $country = $customer->country; //? drmCountryNameFull($customer->country) : null;
    return json_encode([
        'name' => $customer->full_name,
        'company' => $customer->company_name,
        'address' => $customer->address,
        'zip_code' => $customer->zip_code,
        'city' => $customer->city,
        'state' => $customer->state,
        'country' => $country,
    ]);
}


function getStatusText($id)
{
    if ($id == 1) {
        $text = "Created a new category";
    }
    if ($id == 2) {
        $text = "Changed";
    }
    if ($id == 3) {
        $text = "Enabled update for";
    }
    return $text;
}

function makeUpdateStatusJson()
{
    $status = [
        'title' => 1,
        'description' => 1,
        'image' => 1,
        'ek_price' => 1,
        'stock' => 1,
        'status' => 1,
        'gender' => 1,
        'item_weight' => 1,
        'item_color' => 1,
        'production_year' => 1,
        'materials' => 1,
        'brand' => 1,
        'item_size' => 1,
        'category' => 1,
        'uvp' => 1,
        'delivery_days' => 1,
        'shipping_cost' => 1,
        'industry_template_data' => 1,
    ];
    return json_encode($status);
}

function getUniqueCategories($ids)
{
    $category_array = array_map('json_decode', $ids);
    $flattened = Illuminate\Support\Arr::flatten($category_array);
    $category_id = array_unique($flattened);
    return $category_id;
}

function sentProgress($message, $type)
{
    try {
        if ($type == 'import') {
            event(new App\Events\ProgressEvent($message));
        }
        if ($type == 'importSync') {
            event(new App\Events\SyncProgressEvent($message));
        }
        if ($type == 'manualUpdate') {
            event(new App\Events\ManualUpdateEvent($message));
        }
    } catch (\Exception $e) {
        // echo "BroadcastException";
    }
}

function create_process($user_id,$event,$data)
{
    $processSvc = new ProductProcess($user_id);
    return $processSvc->create([
        'action_id' => $event,
        'metadata' => $data['metadata'] ?? array(),
        'model_id' => $data['model_id']
    ]);
}

function peakMemory()
{
    return (memory_get_peak_usage(true) / 1024 / 1024) . " MB";
}

function getCpuUsage() {
    $usages = getrusage();
    $cpuTime = ($usages['ru_utime.tv_sec'] * 1000) + ($usages['ru_utime.tv_usec'] / 1000);
    return $cpuTime . " milliseconds";
}


function orderStatisticsArrData()
{
    $is_key_account = \CRUDBooster::isKeyAccount();
    $key_users = [];
    if($is_key_account){
        $key_users = \DRM::keyAccountUsersId(CRUDBooster::myId());
        // $key_users[] = \CRUDBooster::myId();
    }

    $currency = $performa = $credit_note_amount = $order_average_currency = $offer = [];
    $currency['EUR'] = $performa['EUR'] = $credit_note_amount['EUR'] = $offer['EUR'] = 0;

    $orders_group = null;
    $total = $other = $shipped_order = $proforma_invoice = $offer_invoice = $credit_note_count = 0;
    $total_sum = $pro_sum = 0;

    $eur_total_sum = $eur_total_sum_count = 0;
    $eur_credit_note_sum = 0;
    $eur_prof_sum = 0;
    $eur_offer_sum = 0;

    $best_selling_shop = null;
    $best_selling_shop_item = 0;

    $orders_group = \App\NewOrder::select('currency', 'invoice_number', 'total', 'test_order', 'credit_number', 'eur_total', 'offer_number', 'status');

    if(CRUDBooster::isDroptiendaSupport())
    {
        $orders_group->where('new_orders.eur_total', '<=', 500); //Droptienda support orders
    }

    $order_top_shop = \App\NewOrder::has('shop');
    if (!CRUDBooster::isSuperadmin()) {
        if(!empty($key_users)) {
            $order_top_shop->whereIntegerInRaw('cms_user_id', $key_users);
        }else{
           $order_top_shop->where('cms_user_id', \CRUDBooster::myParentId());
        }
    }

    $order_top_shop = $order_top_shop->selectRaw(DB::raw('count(*) as order_count, shop_id'))->groupBy('shop_id')->orderBy('order_count', 'desc')->first();

    if ($order_top_shop) {
        $best_selling_shop_type = \App\Shop::where('id', $order_top_shop->shop_id)->first(['channel']);
        $best_selling_shop = drm_shop_type_name($best_selling_shop_type->channel);
        $best_selling_shop_item = $order_top_shop->order_count;
    }

    if (!CRUDBooster::isSuperadmin()) {
        if(!empty($key_users)) {
            $orders_group->whereIntegerInRaw('cms_user_id', $key_users);
        }else{
           $orders_group->where('cms_user_id', '=', CRUDBooster::myParentId());
        }
    }

    $orders_group = $orders_group->get()->groupBy('currency');

    if ($orders_group->isNotEmpty()) {

        foreach ($orders_group as $key => $orders) {
            $order_sum = $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->whereNull('offer_number')->where('credit_number', 0)->sum("total");
            $total += $orders->count(); //whereNotIn('status',['Storniert','Canceled'])->
            $total_sum += $order_sum;

            //EUR converted value
            $eur_total_sum += $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->where('credit_number', 0)->sum("eur_total");

            $eur_total_sum_count += $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->where('credit_number', 0)->count();


            $eur_credit_note_sum += abs($orders->where('test_order', '!=', 1)->where('credit_number', '>', 0)->sum("eur_total"));
            $eur_prof_sum += $orders->where('invoice_number', -1)->sum("eur_total");

            $credit_note_sum = $orders->where('test_order', '!=', 1)->where('credit_number', '>', 0)->sum("total");

            $proforma_invoice += $orders->where('invoice_number', -1)->count();
            $prof_sum = $orders->where('invoice_number', -1)->sum("total");
            $pro_sum += $prof_sum;


            //Offer document
            $eur_offer_sum += $orders->whereNotNull('offer_number')->where('offer_number', '>', 0)->where('test_order', '!=', 1)->where('status', '!=', 'offer_rejected')->sum("eur_total");
            $offer_invoice += $orders->whereNotNull('offer_number')->where('offer_number', '>', 0)->where('test_order', '!=', 1)->count();
            $offer_sum = $orders->whereNotNull('offer_number')->where('offer_number', '>', 0)->where('test_order', '!=', 1)->where('status', '!=', 'offer_rejected')->sum("total");

            if (($key == '') || (strtolower($key) == 'eur')) {
                $performa['EUR'] += $prof_sum;
                $currency['EUR'] += $order_sum;
                $credit_note_amount['EUR'] += abs($credit_note_sum);
                $offer['EUR'] += $offer_sum;
            } else {
                $currency[$key] = $order_sum;
                $performa[$key] = $prof_sum;
                $credit_note_amount[$key] = abs($credit_note_sum);
                $offer[$key] += $offer_sum;
            }
            $shipped_order = 0;

            // $shipped_order +=  $orders->filter(function ($item) use ($attribute, $value) {
            //     return strtolower($item['status']) == 'shipped';
            // })->count();

            $credit_note_count += $orders->filter(function ($item) {
                return ($item['credit_number'] > 0);
            })->count();

        }
    }

    //$others_order = $total - $shipped_order; // - $proforma_invoice;
    $performa_statt = ['count' => $proforma_invoice, 'amount' => $performa]; //Remove soon

    try {
        $order_average_value = $eur_total_sum / $eur_total_sum_count;
        foreach ($currency as $k => $val) {
            $order_average_currency[$k] = $val / $eur_total_sum_count;
        }
    } catch (\Exception $e) {
        $order_average_value = 0;
    }


    return (object)[
        'total_order' => $total,
        'total_proforma' => $proforma_invoice,
        'total_offer' => $offer_invoice,
        // 'shipped_order' => $shipped_order,
        // 'others_order' => $others_order,
        'credit_note_count' => $credit_note_count,
        'best_selling_shop' => $best_selling_shop,
        'best_selling_shop_item' => $best_selling_shop_item,


        'order_average_value' => [
            'type' => 'amount',
            'total' => $order_average_value,
            'details' => $order_average_currency,
        ],

        'eur_total_sum' => [
            'type' => 'amount',
            'total' => $eur_total_sum,
            'details' => $currency,
        ],
        'eur_credit_note_sum' => [
            'type' => 'credit',
            'total' => $eur_credit_note_sum,
            'details' => $credit_note_amount,
        ],
        'eur_prof_sum' => [
            'type' => 'proforma',
            'total' => $eur_prof_sum,
            'details' => $performa,
            'count' => $proforma_invoice,
        ],

        'eur_offer_sum' => [
            'type' => 'offer',
            'total' => $eur_offer_sum,
            'details' => $offer,
            'count' => $offer_invoice,
        ],

        'total_amount' => $total_sum, 'currency' => $currency, 'performa' => $performa_statt, 'credit_note' => $credit_note_amount]; //remove soon
}


if (!function_exists('customerGrandTotalOrder')) {
    function customerGrandTotalOrder($customer_id)
    {
        $currency = [];
        $currency['EUR'] = 0;
        $eur_total_sum = 0;

        $orders_group = \App\NewOrder::select('currency', 'invoice_number', 'total', 'test_order', 'credit_number', 'eur_total')->where('drm_customer_id', $customer_id)->get()->groupBy('currency');

        if ($orders_group->isNotEmpty()) {
            foreach ($orders_group as $key => $orders) {

                $order_sum = $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->sum("total");
                //EUR converted value
                $eur_total_sum += $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->sum("eur_total");

                if (($key == '') || (strtolower($key) == 'eur')) {
                    $currency['EUR'] += $order_sum;
                } else {
                    $currency[$key] = $order_sum;
                }

            }
        }

        return (object)[
            'total' => $eur_total_sum,
            'details' => $currency,
        ];
    }
}

//ORDER GGRAND TOTAL - Overview
function orderGrandTotalData($paginator)
{
    try {
        $result = null;
        $eur_total = 0;
        $currency = [];
        $currency['EUR'] = 0;

        if(!empty($paginator)) {
            $result = $paginator->items();
        }

        $result =  collect($result);
        $orders_group = $result->filter(function($item) {
            return empty($item->credit_number) && empty($item->credit_ref);
        })->groupBy('currency');

        if ($orders_group->isNotEmpty()) {
            foreach ($orders_group as $key => $orders) {
                $order_sum = $orders->sum("total");
                $eur_total = $orders->sum('eur_total');
                if (($key == '') || (strtolower($key) == 'eur')) {
                    $currency['EUR'] += $order_sum;
                } else {
                    $currency[$key] = $order_sum;
                }
            }
        }

        return [
            'total_eur' => $eur_total,
            'currency' => $currency
        ];
    }catch(\Exception $e) {
        return [];
    }
}

function orderGoupDataToCurrency($orders_group)
{
    $currency['EUR'] = 0;
    $orders_group = $orders_group->get()->groupBy('currency');
    if ($orders_group->isNotEmpty()) {

        foreach ($orders_group as $key => $orders) {
            $order_sum = $orders->sum("total");
            if (($key == '') || (strtolower($key) == 'eur')) {
                $currency['EUR'] += $order_sum;
            } else {
                $currency[$key] = $order_sum;
            }
        }
    }
    return $currency;
}

function orderStatisticsData()
{
    return Cache::remember('order_statt_' . CRUDBooster::myId(), 05.0, function () {
        return orderStatisticsArrData();
    });
}

function removeCommaPrice($rate)
{
    if (strpos($rate, ",")) {
        $have = [".", ","];
        $will_be = ["", "."];
        $rate = str_replace($have, $will_be, $rate);
    }
    return round($rate, 2);
}

function orderTaxNumber($order_id)
{
    return \App\NewOrder::where('id', '=', $order_id)->value('vat_number');
}

function test_order_btn()
{
    return ((userTestOrderCount() < userTestOrderLimit()) || CRUDBooster::isSuperadmin() || (CRUDBooster::myId() == 98)) ? true : false;
}

function testOrderYesBtn()
{
    if (CRUDBooster::isSuperadmin() || (CRUDBooster::myId() == 98)) return 'Yes';
    $btn = 'Yes';
    $last_val = userTestOrderLimit();
    $order = userTestOrderCount();
    if ($order < $last_val) return $order . '/' . $last_val . ' ' . $btn;
    return $last_val . '/' . $last_val . ' ' . $btn;
}

function testOrderNoBtn()
{
    if (CRUDBooster::isSuperadmin() || (CRUDBooster::myId() == 98)) return 'No';
    $btn = 'No';
    $last_val = userTestOrderLimit();
    $order = userTestOrderCount();
    if ($order < $last_val) return $order . '/' . $last_val . ' ' . $btn;
    return $last_val . '/' . $last_val . ' ' . $btn;
}

function testOrderYesBtnShow()
{
    $order = userTestOrderCount();
    return (($order < userTestOrderLimit()) || CRUDBooster::isSuperadmin() || (CRUDBooster::myId() == 98)) ? 'true' : 'false';
}

function userTestOrderCount()
{
    if (CRUDBooster::isSuperadmin()) {
        return 0;
    }
    // return Cache::rememberForever('test_order_count_' . CRUDBooster::myParentId(), function () {
        return DB::table('new_orders')->where('cms_user_id', CRUDBooster::myParentId())->where('test_order', 1)->where('order_id_api', '!=', 'test_order')->where('test_item', '<>', 1)->count();
    // });
}

function userTestOrderLimit()
{
    // return Cache::rememberForever('user_test_order_limit_' . CRUDBooster::myParentId(), function () {
        $user = DB::table('cms_users')->where('id', CRUDBooster::myParentId())->select('test_order_limit as test_limit')->first();
        return ($user && $user->test_limit) ? $user->test_limit : 5;
    // });
}

function extendedTrialDay()
{
    // return (in_array(\CRUDBooster::myId(), [52, 61]))? env('TRIAL_DAYS', '65') : 14;
    // return env('TRIAL_DAYS', '65');
    return env('TRIAL_DAYS', '14');
}

function importFirstDate($date)
{
    // $time=strtotime("2020-5-11");
    // return (in_array(\CRUDBooster::myId(), [52, 61]))? $time : strtotime($date);
    return strtotime($date);
}

function ddtest($var)
{
    if (!app()->environment('production')) {
        dd($var);
    }
}

function DRM_Inverval_App_Minute($user_id, $app_id)
{
    $user_plan = app_user_plan_id($user_id, $app_id);

    // NEW TARIFF INTERVAL
    if(checkTariffEligibility($user_id))
    {
        $time = 720;
        switch ($user_plan) {
            case 25:
                $time = 360;
                break;
            case 26:
                $time = 180;
                break;
            case 27:
                $time = 30;
                break;
        }

        return $time;
    }

    $time = 1440;
    switch ($user_plan) {
        case config('global.interval_bronze_plan'):
            $time = 720;
            break;
        case config('global.interval_silver_plan'):
            $time = 360;
            break;
        case config('global.interval_gold_plan'):
            $time = 180;
            break;
        case config('global.interval_platinum_plan'):
            $time = 30;
            break;
    }
    return $time;
}

function app_user_plan_id($user_id, $app_id)
{

    // NEW TARIFF INTERVAL
    if(checkTariffEligibility($user_id) && in_array($app_id, [config('global.interval_app_id'), config('global.csv_interval_app_id'), config('global.webshop_analysis_app_id')]))
    {
        return cache()->remember("new_tariff_plan_{$user_id}", now()->addMinutes(5), function() use ($user_id) {
            $today = now();
            $trial = DB::table('app_trials')
            ->where(['user_id' => $user_id, 'app_id' => 0])
            ->whereRaw("DATE_ADD(start_date, INTERVAL trial_days DAY) >= '$today'")
            ->exists();

            if($trial) return 27;

            return DB::table('purchase_import_plans')
            ->where('cms_user_id', $user_id)
            ->whereIn('import_plan_id', [24, 25, 26, 27])
            ->whereDate('end_date', '>=', $today)
            ->value('import_plan_id');
        });
    }

    $user_has_plan = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('plan_id')->first();
    $user_has_assign = \DB::table('app_assigns')->where(['app_id' => $app_id, 'user_id' => $user_id])
    ->where(function($amnu){
        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
    })
    ->select('plan_id')->first();
    $purchase_plan = ($user_has_plan) ? $user_has_plan->plan_id : 0;
    $assign_plan = ($user_has_assign) ? $user_has_assign->plan_id : 0;
    $fast_plan = ($purchase_plan > $assign_plan) ? $purchase_plan : $assign_plan;

    $user_has_trial = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'status' => 'active', 'is_free_trail' => 1, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->first();

    return ($fast_plan) ? $fast_plan : (($user_has_trial) ? config('global.interval_platinum_plan') : null);
}

function app_user_interval($plan = null)
{
    // NEW TARIFF INTERVAL
    if(checkTariffEligibility(\CRUDBooster::myParentId()))
    {
        $text = '12 ' . __('menu.HOURS');
        switch ($plan) {
            case 25:
                $text = '6 ' . __('menu.HOURS');
                break;
            case 26:
                $text = '3 ' . __('menu.HOURS');
                break;
            case 27:
                $text = '30 ' . __('menu.MINUTES');
                break;
        }
        return $text;
    }


    $text = '24 ' . __('menu.HOURS');
    switch ($plan) {
        case config('global.interval_bronze_plan'):
            $text = '12 ' . __('menu.HOURS');
            break;
        case config('global.interval_silver_plan'):
            $text = '6 ' . __('menu.HOURS');
            break;
        case config('global.interval_gold_plan'):
            $text = '3 ' . __('menu.HOURS');
            break;
        case config('global.interval_platinum_plan'):
            $text = '30 ' . __('menu.MINUTES');
            break;
    }
    return $text;
}


//Order status mail send
function send_order_email($order_id)
{
  try{
    app('App\Http\Controllers\AdminDrmAllOrdersController')->orderStatusMailSend($order_id);
  }catch(\Exception $e){}
}

//Tracking email template
function DRMParseTrackingEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

    if($channel){
        $page = Cache::rememberForever('order_tracking_email_by_channel_' . $user_id . '_' . $channel, function () use ($user_id, $channel) {
            return DB::table('order_tracking_email_by_channels')->where(['cms_user_id' => $user_id, 'channel' => $channel])->first();
        });
    }else{
    $page = Cache::rememberForever('order_tracking_email_' . $user_id, function () use ($user_id) {
        return DB::table('order_tracking_emails')->where('cms_user_id', $user_id)->first();
    });
    }

    if ($page) {
        $content = empty($page->email_template) ? config('system_email_settings.tracking_email_settings') : $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;

        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}


//DRM Order status email parse
//get status email template
function getStatusMailTemplate($user_id, $order_status, $channel = null)
{
    if($channel){
        $cache_name = $order_status . '_email_' . $user_id . '_' . $channel;
    }else{
        $cache_name = $order_status . '_email_' . $user_id;
    }

    // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
        return Cache::rememberForever($cache_name, function () use ($user_id, $order_status, $channel) {
            if($channel){
                return DB::table('order_mail_templates')->where('order_status', $order_status)->where('user_id', $user_id)->where('channel', $channel)->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')->first();
            }else{
                return DB::table('order_mail_templates')->where('order_status', $order_status)->where('user_id', $user_id)->whereNull('channel')->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')->first();
            }
        });
    // }else{
    // $cache_name = $order_status . '_email_' . $user_id;
    // return Cache::rememberForever($cache_name, function () use ($user_id, $order_status) {
    //     return DB::table('order_mail_templates')->where('order_status', $order_status)->where('user_id', $user_id)->select('email_template', 'mail_subject', 'bcc_email', 'auto_mail', 'send_invoice', 'sender_email')->first();
    // });
    // }
}

//Get email conent, subject from email page
function DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $send_invoice = 0;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

    if($channel){
        $page = getStatusMailTemplate($user_id, $order_status, $channel);
    }else{
    $page = getStatusMailTemplate($user_id, $order_status);
    }

    if ($page) {
        $content = empty($page->email_template) ? config('system_email_settings.email_settings') : $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;
        $send_invoice = $page->send_invoice;
        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'send_invoice' => $send_invoice, 'senderEmail' => $senderEmail];
}

//get combine status email template
function getCombineStatusMailTemplate($user_id, $channel = null)
{
    if($channel){
        $cache_name = 'combine_status_email_settings_' . $user_id . '_' . $channel;
    }else{
        $cache_name = 'combine_status_email_settings_' . $user_id;
    }

    // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
        return Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
            return findSystemMail('combine_status_email_settings', $user_id, $channel);
        });
    // }else{
    // return Cache::rememberForever('combine_status_email_settings_' . $user_id, function () use ($user_id) {
    //     return DB::table('combine_status_email_settings')->where('cms_user_id', $user_id)->first();
    // });
    // }
}

//Combine Status email parse
//Get email conent, subject from email page
function DRMParseOrderCombineStatusEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $send_invoice = 0;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

    if($channel){
        $page = getCombineStatusMailTemplate($user_id, $channel);
    }else{
    $page = getCombineStatusMailTemplate($user_id);
    }

    if ($page) {
        $content = empty($page->email_template) ? config('system_email_settings.email_settings') : $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;
        $send_invoice = $page->send_invoice;
        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'send_invoice' => $send_invoice, 'senderEmail' => $senderEmail];
}


//DRM Order email parse
//Get email conent, subject from email page
function DRMParseOrderEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

    if(!empty($channel)){
        $cache_name = 'drm_order_mail_' . $user_id . '_' . $channel;
    }else{
        $cache_name = 'drm_order_mail_' . $user_id;
    }

    // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
        $page = Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
            return findSystemMail('drm_order_mail', $user_id, $channel);
        });
    // }else{
    // $page = Cache::rememberForever('drm_order_mail_' . $user_id, function () use ($user_id) {
    //     return DB::table('drm_order_mail')->where('cms_user_id', $user_id)->first();
    // });
    // }

    if ($page) {
        $content = empty($page->email_template) ? config('system_email_settings.email_settings') : $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;
        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }
        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}

function DRMParseAppointmentEmailTemplate($appointment) // , $userId, $customerId, $flag = ''
{

    $template = config('system_email_settings.appointment_mail_setting');
    $subject = config('system_email_settings.appointment_mail_subject');
    $userId = $appointment->user_id;

    $emailSetting = Cache::rememberForever('appointment_email_settings_' . $userId, function () use ($userId) {
        return DB::table('appointment_email_settings')->where('status', 1)->where('cms_user_id', $userId)->first();
    });

    $senderEmail = DB::table('cms_users')->where('id', $userId)->value('email');
    $invoiceSetting = DB::table('drm_invoice_setting')->select('logo', 'store_name')->where('cms_user_id', $userId)->orderBy('id', 'desc')->first();
    $toEmail = $appointment->appointment[0]['email'];
    $bcc = null;
    if (!empty($emailSetting)) {
        $subject = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
        $senderEmail = empty($emailSetting->sender_email) ? $senderEmail : $emailSetting->sender_email;
        $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
        $bcc = empty($emailSetting->bcc_email) ? $bcc : $emailSetting->bcc_email;
    }
    $img = $invoiceSetting->logo ? $invoiceSetting->logo : '';
    $logo = '<img id="display_logo" width="150" src="' . $img . '" alt="' . $invoiceSetting->store_name . '">';
    $template = preg_replace('/\[logo]/', $logo, $template);
    $template = preg_replace('/\[customer_name]/', explode(' - ', $appointment->appointment[0]['title'])[0], $template);
    $template = preg_replace('/\[company_name]/', '', $template);

    $appDatePatt = '/\[appointment_date]/';
    $appTimePatt = '/\[appointment_time]/';
    $template = preg_replace($appDatePatt, $appointment->appointment[0]['date'], $template);
    $template = preg_replace($appTimePatt, $appointment->appointment[0]['start_time'], $template);

    $subject = preg_replace($appDatePatt, $appointment->appointment[0]['date'], $subject);
    $subject = preg_replace($appTimePatt, $appointment->appointment[0]['start_time'], $subject);

    // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])) ){

        $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();
        $signature_tags = [];

        if($email_signatures){
            foreach($email_signatures as $key => $signature){
                // $signature_tags['drm-sign-'.$key] = $signature;
                $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
            }
        }

    // }

    return ['subject' => $subject, 'body' => $template, 'bcc' => $bcc, 'senderEmail' => $senderEmail, 'toEmail' => $toEmail];
}

function DRMParseAppointmentCancelEmailTemplate($appointment) // , $userId, $customerId, $flag = ''
{

    $template = config('system_email_settings.appointment_cancel_mail');
    $subject = 'Appointment Cancellation Email';
    $userId = $appointment->user_id;
    $emailSetting = Cache::rememberForever('appointment_cancel_email_settings_' . $userId, function () use ($userId) {
        return DB::table('appointment_email_settings')->where('status', 0)->where('cms_user_id', $userId)->first();
    });

    $senderEmail = DB::table('cms_users')->where('id', $userId)->value('email');
    $toEmail = $appointment->appointment[0]['email'];
    $bcc = null;
    if (!empty($emailSetting)) {
        $subject = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
        $senderEmail = empty($emailSetting->sender_email) ? $senderEmail : $emailSetting->sender_email;
        $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
        $bcc = empty($emailSetting->bcc_email) ? $bcc : $emailSetting->bcc_email;
    }

    $invoiceSetting = DB::table('drm_invoice_setting')->select('logo', 'store_name')->where('cms_user_id', $userId)->orderBy('id', 'desc')->first();
    $img = $invoiceSetting->logo ? $invoiceSetting->logo : '';
    $logo = '<img id="display_logo" width="150" src="' . $img . '" alt="' . $invoiceSetting->store_name . '">';
    $template = preg_replace('/\[logo]/', $logo, $template);

    $template = preg_replace('/\[customer_name]/', $appointment->appointment[0]['title'], $template);
    $template = preg_replace('/\[company_name]/', '', $template);
    $appDatePatt = '/\[appointment_date]/';
    $appTimePatt = '/\[appointment_time]/';
    $template = preg_replace($appDatePatt, $appointment->appointment[0]['date'], $template);
    $template = preg_replace($appTimePatt, $appointment->appointment[0]['start_time'], $template);

    // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])) ){

        $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();
        $signature_tags = [];

        if($email_signatures){
            foreach($email_signatures as $key => $signature){
                // $signature_tags['drm-sign-'.$key] = $signature;
                $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
            }
        }

    // }

    return ['subject' => $subject, 'body' => $template, 'bcc' => $bcc, 'senderEmail' => $senderEmail, 'toEmail' => $toEmail];
}

function DRMParseCustomerVerificationEmailTemplate($data)
{

    $template = config('system_email_settings.email_verification_body');
    $subject = config('system_email_settings.email_verification_subject');
    $userId = $data['user_id'];
    $emailSetting = Cache::rememberForever('customer_email_verification_' . $userId, function () use ($userId) {
        return DB::table('customer_email_verification')->where('cms_user_id', $userId)->first();
    });

    $senderEmail = DB::table('cms_users')->where('id', $userId)->value('email');
    $toEmail = $data['to_email'];
    $bcc = null;
    if (!empty($emailSetting)) {
        $subject = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
        $senderEmail = empty($emailSetting->sender_email) ? $senderEmail : $emailSetting->sender_email;
        $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
        $bcc = empty($emailSetting->bcc_email) ? $bcc : $emailSetting->bcc_email;
    }

    $url = url('customer-email-verification') . '/' . $data['slug'];
    $link = '<a target="_blank" href="' . $url . '">' . $url . '</a>';;

    $template = preg_replace('/\[link]/', $link, $template);
    $template = preg_replace('/\[customer_name]/', $data['customer_name'], $template);

    // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])) ){

        $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();
        $signature_tags = [];

        if($email_signatures){
            foreach($email_signatures as $key => $signature){
                // $signature_tags['drm-sign-'.$key] = $signature;
                $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
            }
        }

    // }

    return ['subject' => $subject, 'body' => $template, 'bcc' => $bcc, 'senderEmail' => $senderEmail, 'toEmail' => $toEmail];
}
// Droptienda order confirmation email parse
// Get email conent, subject from email page
function DRMParseDroptiendaOrderEmailTemplate($tags, $user_id)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');
    $default_template = config('default_mail_templates.droptienda_order_confirmation_template');
    $content = $default_template['body'];
    $subject = $default_template['subject'];
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
    $page = Cache::rememberForever('droptienda_order_confirmation_template_' . $user_id, function () use ($user_id) {
        return DB::table('droptienda_order_confirmation_template')->where('cms_user_id', $user_id)->first();
    });

    if ($page) {
        $content = $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;
        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }
    }

    foreach ($tags as $k => $value) {
        $find = '[' . $k . ']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
            $content = str_replace($find, $value, $content);
        }
        if ($tags[$tag] == 'true') {
            $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
        } else {
            $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
            // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
            $subject = str_replace($find, $value, $subject);
        }
        if ($tags[$tag] == 'true') {
            $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
        } else {
            $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
            // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}

function DRMParseRemainderEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

    if($channel){
        $cache_name = 'remainder_email_settings_' . $user_id . '_' . $channel;
    }else{
        $cache_name = 'remainder_email_settings_' . $user_id;
    }

    // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
        $page = Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
            return findSystemMail('remainder_email_settings', $user_id, $channel);
        });
    // }else{
    // $page = Cache::rememberForever('remainder_email_settings_' . $user_id, function () use ($user_id) {
    //     return DB::table('remainder_email_settings')->where('cms_user_id', $user_id)->first();
    // });
    // }

    if ($page) {
        $content = empty($page->email_template) ? config('system_email_settings.remainder_email_settings') : $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;

        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}

// DRM Handling time email parse

function DRMParseHandlingTimeEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $bcc = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');

    if($channel){
        $cache_name = 'marketing_email_settings_' . $user_id . '_' . $channel;
    }else{
        $cache_name = 'marketing_email_settings_' . $user_id;
    }

    // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
        $page = Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
            return findSystemMail('marketing_email_settings', $user_id, $channel);
        });
    // }else{
    // $page = Cache::rememberForever('marketing_email_settings_' . $user_id, function () use ($user_id) {
    //     return DB::table('marketing_email_settings')->where('cms_user_id', $user_id)->first();
    // });
    // }

    if ($page) {
        $content = empty($page->email_template) ? config('system_email_settings.handling_time_email_settings') : $page->email_template;
        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;

        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}


//DRM Supplier email parse
//Get email conent, subject from email page
function DRMParseSupplierEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
    $bcc = null;

    if($channel){
        $cache_name = 'drm_supplier_mail_' . $user_id . '_' . $channel;
    }else{
        $cache_name = 'drm_supplier_mail_' . $user_id;
    }

    // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
        $page = Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
            return findSystemMail('drm_supplier_mail', $user_id, $channel);
        });
    // }else{
    // $page = Cache::rememberForever('drm_supplier_mail_' . $user_id, function () use ($user_id) {
    //     return DB::table('drm_supplier_mail')->where('cms_user_id', $user_id)->first();
    // });
    // }


    $content = empty($page->email_template)? config('system_email_settings.supplier_email_body') : $page->email_template;
    $subject = empty($page->mail_subject)? config('system_email_settings.supplier_email_subject') : $page->mail_subject;
    $bcc = $page->bcc_email;

    if (!empty($page->sender_email)) {
        $senderEmail = $page->sender_email;
    }

    foreach ($tags as $k => $value) {
        $find = '[' . $k . ']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
            $content = str_replace($find, $value, $content);
        }
        if ($tags[$tag] == 'true') {
            $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
        } else {
            $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
            // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
            $subject = str_replace($find, $value, $subject);
        }
        if ($tags[$tag] == 'true') {
            $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
        } else {
            $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
            // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}


//Droptienda product stock mail
function DRMParseDtProductStockTemplate($tags, $user_id, $table = 'drm_dt_stock_mail', $channel = null)
{
    if(!in_array($table, ['drm_dt_stock_mail', 'drm_dt_stock_mail_entry', 'mp_chat_email_template', 'df_csv_email_template', 'mp_chat_email_template_by_channels'])) throw new \Exception('Invalid email template!');
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
    $bcc = null;

    if($channel){
        $cache_name = $table.'_' . $user_id . '_' . $channel;
    }else{
        $cache_name = $table.'_' . $user_id;
    }

    // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])){
        if($channel){
            $page = Cache::rememberForever($cache_name, function () use ($user_id, $table, $channel) {
                return findSystemMail($table, $user_id, $channel);
            });
        }else{
            $page = Cache::rememberForever($cache_name, function () use ($user_id, $table) {
                return DB::table($table)->where('cms_user_id', $user_id)->first();
            });
        }
    // }else{
    // $page = Cache::rememberForever($table.'_' . $user_id, function () use ($user_id, $table) {
    //     return DB::table($table)->where('cms_user_id', $user_id)->first();
    // });
    // }


    $content = empty($page->email_template)? config('system_email_settings.'.$table.'_body') : $page->email_template;
    $subject = empty($page->mail_subject)? config('system_email_settings.'.$table.'_subject') : $page->mail_subject;
    $bcc = $page->bcc_email;

    if (!empty($page->sender_email)) {
        $senderEmail = $page->sender_email;
    }

    foreach ($tags as $k => $value) {
        $find = '[' . $k . ']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
            $content = str_replace($find, $value, $content);
        }
        if ($tags[$tag] == 'true') {
            $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
        } else {
            $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
            // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
            $subject = str_replace($find, $value, $subject);
        }
        if ($tags[$tag] == 'true') {
            $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
        } else {
            $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
            // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}



//DRM Offer email parse
//Get email conent, subject from email page
function DRMParseOfferEmailTemplate($tags, $user_id, $table = 'drm_offer_mail', $channel = null)
{
    if(!in_array($table, ['drm_offer_mail', 'drm_offer_remainder_mail', 'drm_offer_mail_by_channels', 'drm_offer_remainder_mail_by_channels', 'drm_email_templete_mp_return', 'drm_email_templete_return_received', 'drm_email_templete_return_shipped', 'drm_email_templete_mp_rejected', 'drm_email_templete_stock_unavailable'])) throw new \Exception('Invalid email!');
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
    $bcc = null;

    // if($channel){
    //     $page = DB::table($table)->where('cms_user_id', $user_id)->where('channel', $channel)->first();
    // }else{
        $page = Cache::rememberForever($table.'_' . $user_id, function () use ($user_id, $table) {
            return DB::table($table)->where('cms_user_id', $user_id)->first();
        });
    // }


    $content = empty($page->email_template)? config('system_email_settings.'.$table.'_body') : $page->email_template;
    $subject = empty($page->mail_subject)? config('system_email_settings.'.$table.'_subject') : $page->mail_subject;
    $bcc = $page->bcc_email;

    if (!empty($page->sender_email)) {
        $senderEmail = $page->sender_email;
    }

    foreach ($tags as $k => $value) {
        $find = '[' . $k . ']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
            $content = str_replace($find, $value, $content);
        }
        if ($tags[$tag] == 'true') {
            $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
        } else {
            $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
            // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
            $subject = str_replace($find, $value, $subject);
        }
        if ($tags[$tag] == 'true') {
            $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
        } else {
            $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
            // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}

//Panyment Progress email parse
//Get email conent, subject from email page
function DRMParsePaymentProgressEmailTemplate($tags, $user_id, $channel = null)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');

    $content = null;
    $subject = null;
    $senderEmail = DB::table('cms_users')->where('id', $user_id)->value('email');
    $bcc = null;

    if($channel){
        $cache_name = 'payment_progress_email_settings_' . $user_id . '_' . $channel;
    }else{
        $cache_name = 'payment_progress_email_settings_' . $user_id;
    }

    $page = Cache::rememberForever($cache_name, function () use ($user_id, $channel) {
        return findSystemMail('payment_progress_email_settings', $user_id, $channel);
    });

    if ($page) {

        $content = empty($page->email_template) ? config('system_email_settings.email_settings') : $page->email_template;

        $subject = $page->mail_subject;
        $bcc = $page->bcc_email;
        $send_invoice = $page->send_invoice;

        if (!empty($page->sender_email)) {
            $senderEmail = $page->sender_email;
        }

        foreach ($tags as $k => $value) {
            $find = '[' . $k . ']';
            $tag = $k;
            if (strpos($content, $find) !== false) {
                $content = str_replace($find, $value, $content);
            }
            if ($tags[$tag] == 'true') {
                $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
            } else {
                $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
                // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
            }

            if (strpos($subject, $find) !== false) {
                $subject = str_replace($find, $value, $subject);
            }
            if ($tags[$tag] == 'true') {
                $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
            } else {
                $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
                // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
            }
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'send_invoice' => $send_invoice, 'senderEmail' => $senderEmail];
}

function DRMParseStockShortageTemplate($tags)
{
    $tags['DRM_ADMIN_URL'] = CRUDBooster::adminPath();
    $tags['DRM_REGISTATION_URL'] = url('registration/sign-up');
    $default_template = config('default_mail_templates.drm_stock_shortage_template');
    $content = $default_template['body'];
    $subject = $default_template['subject'];
    $bcc = null;
    $senderEmail = $tags['user']->email;

    foreach ($tags as $k => $value) {
        $find = '[' . $k . ']';
        $tag = $k;
        if (strpos($content, $find) !== false) {
            $content = str_replace($find, $value, $content);
        }
        if ($tags[$tag] == 'true') {
            $content = preg_replace('~\{\/?' . $tag . '\}~', '', $content);
        } else {
            $content = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $content);
            // $content = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $content);
        }

        if (strpos($subject, $find) !== false) {
            $subject = str_replace($find, $value, $subject);
        }
        if ($tags[$tag] == 'true') {
            $subject = preg_replace('~\{\/?' . $tag . '\}~', '', $subject);
        } else {
            $subject = preg_replace('/{' . $tag . '}[\s\S]+?{' . $tag . '}/', '', $subject);
            // $subject = preg_replace('/' . preg_quote('{'.$tag.'}') .'.*?' .preg_quote('{'.$tag.'}') . '/', '', $subject);
        }
    }

    return ['subject' => $subject, 'body' => $content, 'bcc' => $bcc, 'senderEmail' => $senderEmail];
}

//Get shop type name by type id value
function drm_shop_type_name($value)
{
    $data = drm_shop_channels();
    return $data[$value] ?? '';
}

//drm shops
function drm_shop_channels()
{
    $collection = collect(config('channel.list'))->pluck('name','type')->toArray();
    $collection[200] = 'VOD';
    $collection[201] = 'Marketplace';
    return $collection;

}

//Check if purchased app
function checkUserPurchasedApp($id, $user_id)
{
    $purchased = DB::table('purchase_apps')->where(['app_id' => $id, 'cms_user_id' => $user_id])->first();
    return ($purchased) ? true : false;
}

function isLocal()
{
    if (!app()->environment('production')) {
        return true;
    } else {
        return false;
    }
}

/**
 * @param task_id for furter tracking.
 *
 * @param users array of user id you are allowing this features
 */
function isDev($task_id, $users = null)
{
    if(is_array($users)){
        if(in_array(CRUDBooster::myParentId(), $users)) return true;
    }
    if (!app()->environment('production')) return true;

    return false;
}

if(!function_exists('variable_to_word')){
    function variable_to_word($variable): string
    {
        return ucfirst(strtolower(str_replace('_', ' ', $variable)));
    }
}

function templateMarketplaceCategory($template_name){
    $mapping = [
        'juvely' => 35,
        'wine' => 29,
        'supplements' => 29,
        'spielwaren' => 33
    ];
    if( isset($mapping[$template_name]) ) return $mapping[$template_name];
    return false;
}

//Is checked paywall term by user
function isCheckedPaywallTerm()
{
    if(CRUDBooster::isSupportAccount()) return true;

    $has_orders_count = DB::table('new_orders')->where(['cms_user_id' => CRUDBooster::isSubUser() ? CRUDBooster::myParentId() : CRUDBooster::myId(), 'insert_type' => 6])->count();
    if($has_orders_count) return (bool) $has_orders_count;
    $checked_paywall_term = DB::table('cms_users')->where(['id' => CRUDBooster::isSubUser() ? CRUDBooster::myParentId() : CRUDBooster::myId(), 'checked_paywall_term' => 1])->count();
    return (bool)($has_orders_count || $checked_paywall_term);
}

function getProductTitle($product)
{
    $template_purchased = AppStore::ActiveFeature('Import-Template');

    if ($template_purchased) {
        $import = new App\Http\Controllers\AdminDrmImportsController;
        $template = $import->generateTemplate($product, 'title');
        if ($template) {
            return $template;
        } else {
            return $product->title;
        }
    } else {
        return $product->title;
    }
}

function getProductDesc($product)
{
    $template_purchased = AppStore::ActiveFeature('Import-Template');

    if ($template_purchased) {
        $import = new App\Http\Controllers\AdminDrmImportsController;
        $template = $import->generateTemplate($product, 'description');
        if ($template) {
            return $template;
        } else {
            return $product->TransDescription;
        }
    } else {
        return $product->TransDescription;
    }
}

//Assign credit note number
// function assignCreditNote($order_id){
//   $order = \App\NewOrder::where(['id' => $order_id, 'credit_number' => 0])->whereIn('status', ['Storniert','Canceled'])->first();
//   if($order){
//     $credit_number = DB::table('new_orders')->where('cms_user_id', $order->cms_user_id)->max('credit_number') + 1;
//     if( $order->update(['credit_number' => $credit_number]) ){
//       return true;
//     }
//   }
//   return false;
// }

function set_option($key, $group, $data = null, $user_id = null)
{
    if(!$user_id) $user_id = CRUDBooster::myId();
    $op = DB::table('options')->select('*');
    $op->where('option_key', '=', $key);
    $op->where('option_group', '=', $group);
    $op->where('user_id', '=', $user_id);

    if(!$op->get()->count()){
        $option_data = [
            'option_key' => $key,
            'option_group' => $group,
            'option_value' => $data,
            'user_id' => $user_id
        ];
        return DB::table('options')->insert($option_data);
    }else{
        $option_data = [
            'option_value' => $data,
        ];
        return $op->update($option_data);
    }
    return false;
}

//Charge_Token method

function charge_token($data = null, $user_id = null)
{
    $flag = 'credit_deduct';
    $message = 'Magic Function called';
    $type = \App\Enums\CreditType::MAGIC_METHOD;
    $status = \App\Enums\CreditType::CREDIT_REMOVE;

    // app('App\Http\Controllers\tariffController')->CreditUpdate($user_id,$data,$flag);
    // // app('App\Http\Controllers\tariffController')->drmUserCreditRemove($user_id,$data,$message,$type);
    // app('App\Http\Controllers\tariffController')->drmUserCreditAdd($user_id,$data,$message,$type, $status);

    (new ChargeCredit)->charge($user_id, $data, \App\Services\Tariff\Credit\CreditType::MAGIC_FUNCTION_CALLED);
}

function charge_token_trial($data = null, $user_id = null)
{
    $flag = 'credit_deduct';
    $message = 'Magic Function called';
    $type = \App\Enums\CreditType::MAGIC_METHOD;
    $status = \App\Enums\CreditType::CREDIT_REMOVE;

    // //app('App\Http\Controllers\tariffController')->CreditUpdate($user_id,$data,$flag);
    // // app('App\Http\Controllers\tariffController')->drmUserCreditRemove($user_id,$data,$message,$type);
    // app('App\Http\Controllers\tariffController')->drmUserCreditAdd($user_id,$data,$message,$type, $status);

    (new ChargeCredit)->charge($user_id, $data, \App\Services\Tariff\Credit\CreditType::MAGIC_FUNCTION_CALLED);
}



/**
 * Return single option
 */
function get_option($key, $group, $user_id = null)
{
    $op = DB::table('options')->select('*');
    $op->where('option_key', '=', $key);
    $op->where('option_group', '=', $group);
    if($user_id){
        $op->where('user_id', '=', $user_id);
    }

    return $op->first();
}

function get_token_credit($user_id = null)
{
    // $total_credit = DrmUserCreditAddLog::where('user_id', $user_id)
    // ->where('status', 1)
    // ->sum('credit') ?? 0;

    // $remain_credit = DrmUserCredit::where('user_id', $user_id)
    //     ->value('credit') ?? 0;

    // return [
    //     'total_credit' => $total_credit,
    //     'remain_credit' => $remain_credit,
    //     'used_credit' => max(0, $total_credit - $remain_credit),
    // ];

    $creditService = new CreditService;

    return [
        'total_credit' => $creditService->allCredit(),
        'used_credit' => $creditService->usedCredit(),
        'remain_credit' => $creditService->remainingCredit(),
    ];
}


function generateNotificationMessage($title, $message = '')
{
    $find = '[message]';
    if (strpos($title, $find) !== false) {
        $title = str_replace($find, $message, $title);
    }
    return $title;
}

function getSeperateNotificationHooks($position)
{
    $settings = DB::table('notification_settings')
        ->join('notification_trigger as nt', 'notification_settings.trigger_id', '=', 'nt.id')
        ->where('notification_settings.position', '=', $position)
        ->select('notification_settings.*', 'nt.hook as notification_hook')
        ->get();
    $hook = [];
    if ($settings->isNotEmpty()) {
        foreach ($settings as $setting) {
            $hook[] = $setting->notification_hook;
        }
    }
    return $hook;
}

function getHeaderNewNotification($type = '')
{
    //Get hooks
    $position_hooks = CRUDBooster::isKeyAccount()? [] : \DRM::sidebarNotificationPositionHooks();
    $user_id = \CRUDBooster::myParentId() == 2455 ? \CRUDBooster::myParentId() : \CRUDBooster::myId();

    //Nestesd to single array
    $hooks = collect($position_hooks)->flatten()->unique();

    $notifications = DB::table('notifications')
        ->where('notifiable_type', 'App\User')
        ->where('notifiable_id', $user_id);


    if(!empty($hooks)){
        $notifications->whereNotIn('data->hook', $hooks);
    }

    if($type == 'count'){
        return $notifications->whereNull('read_at')->count();
    }

    return $notifications->latest()->take(5)->get();
}

function getNotificationClearReminder()
{
    $clear_history = DB::table('notification_clear_history')->orderBY('id', 'desc')->first();
    $next_clear = \Carbon\Carbon::parse($clear_history->last_clear)->addDay(7);
    $current_time = \Carbon\Carbon::now();
    $remainder = $next_clear->diff($current_time);
    $days = ($remainder->d > 1) ? 's' : '';
    $hours = ($remainder->h > 1) ? 's' : '';
    $remainder_time = " " . $remainder->h . " Hour" . $hours;
    $remainder_time = ($remainder->d > 0) ? $remainder->d . " Day" . $days .$remainder_time : $remainder_time;

    return [
        "reminder_time" => $remainder_time,
        "show_alert" => ($remainder->d <= 2) ? true : false
    ];
}

function isHookRemainOnSidebar($hook)
{
    $notificationTrigger = DB::table('notification_trigger as nt')
        ->join('notification_settings as ns', 'nt.id', '=', 'ns.trigger_id')
        ->where('nt.hook', '=', $hook)
        ->select('nt.*', 'ns.position as sidebar_pos')
        ->first();

    if (!empty($notificationTrigger))
        return $notificationTrigger; //$notificationTrigger;

    return null; //null;
}

function getHookIds($hooks)
{
    $hook = DB::table('notification_trigger')->whereIn('hook', $hooks)->get();
    if ($hook->isNotEmpty()) {
        return $hook->pluck('id');
    }
    return collect();
}

function invoiceLabel($label, $data = '')
{
    $find = '[_]';
    if (strpos($label, $find) !== false) {
        $label = str_replace($find, $data, $label);
    }
    return $label;
}

function getNotificationDriver($hook, $id)
{
    $sidebar = isHookRemainOnSidebar($hook);
    if (!empty($sidebar)) {
        $sidebarDriver = [];
        $setting = \App\UserNotificationSetting::where('user_id', '=', $id)->where('sidebar_id', '=', $sidebar->sidebar_pos)->first();

        if ($setting->is_telegram){
            $sidebarDriver['telegram'] = true;
        }

        if ($setting->is_email){
            $sidebarDriver['mail'] = true;
        }

        // if ($setting->is_mobile_notify){
        //     $sidebarDriver['mobile_app'] = true;
        // }

        return $sidebarDriver;
    } else {
        $user = App\User::find($id);
        $drivers = [];
        if (!empty($user)) {

            if(!empty($user->email_notification) && !empty($user->notify_email)){
                $drivers['header_email'] = true;
            }

            if (!empty($user->is_telegram)) {
                $drivers['header_telegram'] = true;
            }

            // if (!empty($user->is_mobile_notify)) {
            //     $drivers['mobile_app'] = true;
            // }
        }
        return $drivers;
    }
}

function isActiveEmail($sidebar_id)
{
    $user_settings = \App\UserNotificationSetting::where('user_id', '=', CRUDBooster::myId())->where('sidebar_id', '=', $sidebar_id)->whereNotNull('notify_email')->get();
    if ($user_settings->isNotEmpty()) {
        return true;
    }
    return false;
}

function isActiveMobile(){
    return DB::table('user_firebase_device_tokens')->where('user_id', \CRUDBooster::myId())->exists();
}

function isActiveTelegram()
{
    return DB::table('cms_users')->where('id', CRUDBooster::myId())->whereNotNull('telegram_user_token')->whereNotNull('telegram_channel_id')->exists();
}

//has Invoice translate access
function hasInvoiceTranlateAccess($user_id)
{
    $app_id = config('global.invoice_translate_app_id');
    $assigned = DB::table('app_assigns')->where(['app_id' => $app_id, 'user_id' => $user_id])
    ->where(function($amnu){
        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
    })
    ->count();
    $purchased = DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->count();
    return ($assigned || $purchased) ? true : false;
}

function usedInvoiceTranlateAPP($user_id)
{
    $app_id = config('global.invoice_translate_app_id');
    $assigned = DB::table('app_assigns')->where(['app_id' => $app_id, 'user_id' => $user_id])
    ->where(function($amnu){
        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
    })
    ->count();
    $purchased = DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->count();
    return ($assigned || $purchased) ? true : false;
}

function validateEan($ean)
{
    $ean_original = strip_tags($ean);

    $sanitize = explode('.', $ean_original);
    $ean_original = $sanitize[0];
    $ean = trim($ean_original);
    $int_ean = (int)$ean;

    if (CRUDBooster::myId() == 485) {
        if (strlen($ean) > 6 && strlen($ean) < 14 && $int_ean && strpos($ean, '+') === FALSE && strpos($ean, 'E') === FALSE) {
            return $ean;
        } else {
            return null;
        }
    }

    if (strlen($ean) > 7 && strlen($ean) < 14 && $int_ean && strpos($ean, '+') === FALSE && strpos($ean, 'E') === FALSE) {
        return $ean;
    } else {
        return null;
    }
}

//Tax.php Rate calculate NewOrder, country, is_small
function drm_order_tax_rate($order, $customer_country = 'de', $small_business = null)
{
    $res = app(\App\Services\Order\CalculatedTax::class)->getData($order);
    return ['tax_rate' => $res['tax_rate'], 'tax' => round($res['total_tax'], 2), 'total' => $order->total];
}

function getLastActivity($id, $date)
{
    $traker = \App\TrakerTimeSpent::where('user_id', '=', $id)->whereDate('created_at', $date)->latest()->first();
    if (!empty($traker)) {
        $now = \Carbon\Carbon::now();
        $comp = $traker->created_at;
        $diff = $now->diff($comp);
        if ($diff->y)
            return "$diff->y Years Ago";
        if ($diff->m)
            return "$diff->m Months Ago";
        if ($diff->d)
            return "$diff->d Days Ago";
        if ($diff->h)
            return "$diff->h Hours Ago";
        if ($diff->i)
            return "$diff->i Minutes Ago";

        return 'Few seconds Ago';
    }
    return '';
}

function getCountry($data)
{
    $result = '';
    if (!empty($data)) {
        $json = json_decode($data);
        if (!empty($json->country)) {
            if (array_key_exists($json->country, config('global.countries'))) {
                $result = config('global.countries')[$json->country];
            }
        }
        if (!empty($json->city)) {
            $result .= ' - ' . $json->city;
        }
    }
    return $result;
}

function getPageViews($id, $date)
{
    return \App\TrakerPageView::where('user_id', '=', $id)->whereDate('created_at', $date)->count();
}

function getTotalTimeSpent($id, $date)
{
    $spentTime = \App\TrakerTimeSpent::where('user_id', '=', $id)->whereDate('created_at', $date)->selectRaw('SUM(seconds) total_seconds')->first();
    if (!empty($spentTime)) {
        // $minutes = $spentTime->total_seconds / 60;
        // return number_format($minutes / 60, 2, '.', '');
        return convertSecondsToHourMinuteSecond($spentTime->total_seconds);
    }
    return '';
}

function getOnlineUsersCount()
{
    // return \App\TrakerTimeSpent::whereDate('created_at', Carbon::now())
    //   ->select(DB::raw('count(*) as count, HOUR(created_at) as hour'))
    //   // ->whereDate('created_at', '=', Carbon::now()->toDateString())
    //   ->groupBy('user_id')
    //   ->count(); <i class="fa fa-circle" aria-hidden="true"></i>

    return DB::table('sessions')->count();
}

function _dd($var, $die = false){
    echo '<pre>';
    if(empty($var)){
        var_dump($var);
    }else{
        print_r($var);
    }
    echo '</pre>';
    if($die) die();
}

function isUserOnline($id, $date, $ipaddress)
{
    if ($date->isToday()) {
        if (getIpAddress() == $ipaddress) {
            $session = DB::table('sessions')->where('user_id', '=', $id)->first();
            if (!empty($session)) {
                return true;
            }
        }
    }
    return false;
}

function getIpAddress()
{
    if (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
    } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else if (isset($_SERVER['HTTP_X_FORWARDED'])) {
        $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
    } else if (isset($_SERVER['HTTP_FORWARDED_FOR'])) {
        $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
    } else if (isset($_SERVER['HTTP_FORWARDED'])) {
        $ipaddress = $_SERVER['HTTP_FORWARDED'];
    } else {
        $ipaddress = $_SERVER['REMOTE_ADDR'];
    }
    return $ipaddress;
}

function getUserName($data)
{
    $name = 'Guest';
    $user = $data->user()->first();
    if (!empty($user)) {
        $name = $user->name;
        if (isUserOnline($data->user_id, $data->created_at, $data->ip_address)) {
            $name .= ' <i class="fa fa-circle" style="color:green" aria-hidden="true"></i>';
        }
    }
    return $name;
}

//Recurring invoices purchased or not
function hasRecurringInvoiceAccess($user_id)
{
    $app_id = config('global.recuring_invoice_app_id');
    $assigned = DB::table('app_assigns')->where(['app_id' => $app_id, 'user_id' => $user_id])
    ->where(function($amnu){
        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
    })
    ->count();
    $purchased = null;
    $purchased = DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->count();
    return ($assigned || $purchased) ? true : false;
}

//Recurring invoices purchased or not
function DrmUserHasPurchasedApp($user_id, $app_id)
{
    $assigned = DB::table('app_assigns')->where(['app_id' => $app_id, 'user_id' => $user_id])
    ->where(function($amnu){
        $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
    })
    ->count();
    $purchased = null;
    $purchased = DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->count();
    return ($assigned || $purchased) ? true : false;
}


function customerTagNameByType($input_type)
{
    $data = config('global.tag_types');
    return (isset($data[$input_type])) ? $data[$input_type] : null;
}

function getGrandTotalOfTurnOver($turnOvers)
{
    $turnOverTotal = [];
    if (!empty($turnOvers)) {
        foreach ($turnOvers as $turnOver) {
            $turnOverTotal[] = $turnOver->total_amount;
        }
        return array_sum($turnOverTotal);
    }
    return 0;
}


function messageFromException($e): string
{
    if ($e instanceof ClientException) {
        $err = curl_client_response($e);
        return $err['errors'][0]['longMessage'] ?? $err['errors'][0]['message'] ?? $e->getMessage();
    }
    return $e->getMessage();
}

if (!function_exists('curl_client_response')) {
    function curl_client_response(Exception $e): string
    {
        try {
            return json_decode((string)$e->getResponse()->getBody(), true);
        } catch (\Throwable $th) {
            return $e->getMessage();
        }
    }
}

function sub_account_access_permission($userId)
{
    $permissions = \App\Models\SubUserPermission::query()
        ->select('sub_user_permissions.feature_id', 'module_features.module_id', 'feature_identifier')
        ->join('module_features', 'module_features.id', '=', 'sub_user_permissions.feature_id')
        ->where('cms_user_id', $userId)
        ->where('enable', true)
        ->get()
        ->groupBy('module_id');

    $permissions = $permissions->map(function ($items) {
        return $items->pluck('feature_identifier')->toArray();
    })->toArray();

    return $permissions;
}

function sub_account_can($featureIdentifier = 'view', $moduleId = null)
{
    if (!CRUDBooster::isSubUser()) {
        return false;
    }

    if (is_null($moduleId)) {
        $moduleId = CRUDBooster::currentModuleID();
    }

    if (empty($moduleId)) {
        return false;
    }

    $permissions = \Session::get('sub_account_permissions');
    if (array_key_exists($moduleId, $permissions) && in_array($featureIdentifier, $permissions[$moduleId])) {
        return true;
    }

    return false;
}

function getDirectoryName($data, $len)
{
    $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    $index = null;
    $name = '';
    foreach ($months as $key => $value) {
        $search = array_search($value, $data);
        if ($search != false) {
            $index = $search;
            break;
        }
    }

    if ($index != false) {
        if (empty($data[$index + 1]))
            $start = $index + 4;
        else
            $start = $index + 3;
        $end = $len - 1;
        for ($i = $start; $i <= $end; $i++) {
            $name .= $data[$i] . ' ';
        }
    }
    return trim($name);
}

function isNameSelected($name)
{
    // $files = Session::get('ftp_credential.ftp_path') ?? [];
    $files = Session::get('ftp_credential_' . CRUDBooster::myId() . '.ftp_path') ?? [];
    foreach ($files as $file) {
        foreach ($file as $key => $value) {
            if ($value == $name) {
                return true;
            }
        }
    }
    return false;
}

if (!function_exists('trace')) {
    function trace(string $message, array $data = [])
    {
        Log::info(
            'IP ' . request()->ip() . 'Message ' . $message,
            $data
        );
    }
}

if (!function_exists('debug_log')) {
    function debug_log(string $message, string $flag)
    {
        \App\Models\Log::create([
            'message' => $message,
            'module' => $flag
        ]);
    }
}

function getListWithPath($rawList, $pathList)
{
    $files = $folders = [];
    if (!empty($rawList)) {
        foreach ($rawList as $value) {
            $array = explode(' ', trim($value));
            $len = count($array);
            $exists = str_replace('.', '', $array[$len - 1]);
            $directory = getDirectoryName($array, $len);
            if (strlen($exists)) {
                if ((strpos($array[0], 'd') !== false)) {
                    $index = getSearchString($pathList, $directory);
                    if (!is_null($index)) {
                        array_push($folders, ['name' => $directory, 'path' => $pathList[$index]]);
                    }
                } else {
                    $files[] = $directory;
                }
            }
        }
    }

    return ['files' => $files, 'folders' => $folders];
}

function getSearchString($pathList, $directory)
{
    foreach ($pathList as $key => $value) {
        $arr = explode('/', $value);
        $search = array_search($directory, $arr);
        if ($search !== false) {
            return $key;
        }
    }
    return null;
}

function getWidgetFilter($widget_id){
    $filter = DB::table('widget_filters')->where([
        'user_id' => CRUDBooster::myParentId(),
        'widget_id' => $widget_id
    ])
    ->value('filter');
    return $filter;
}

function getParentPath($data)
{
    $array = explode('/', trim($data['path']));
    $len = count($array);
    $path = '';
    for ($i = 0; $i < $len - 1; $i++) {
        if (empty($array[$i]) || ($array[$i] == '.')) {
            $path .= '/';
        } else {
            $path .= $array[$i] . '/';
        }
    }

    return strlen($path) == 1 ? '/' : trim(substr($path, 0, -1)); // eliminate last char and spaces
}

function getBreadCrumbParentPath($breadcrumb, $index)
{
    $len = count($breadcrumb);
    $path = '';
    for ($i = 0; $i < $len - 1; $i++) {
        if ($index == $i) {
            break;
        }
        if (empty($breadcrumb[$i])) {
            $path .= '/';
        } else {
            $path .= $breadcrumb[$i] . '/';
        }

    }
    return strlen($path) == 1 ? '/' : trim(substr($path, 0, -1)); // eliminate last char and spaces
}

function uploadImage($file, $path = 'uploads', $fetch = true)
{
    $name = $file->hashName();
    $file_name = $path . '/' . microtime(true).$name;
    $fileContent = $file;
    if ($fetch) {
        $fileContent = file_get_contents($file);
    }
    Storage::disk('spaces')->put($file_name, $fileContent, 'public');
//    if (Storage::disk('spaces')->exists($file_name)) {
//        return Storage::disk('spaces')->url($file_name);
//    }
//    return null;
    return Storage::disk('spaces')->url($file_name);
}

function getDrmProductsByTag($tag_string, $user_id = null)
{
    if (is_null($user_id)) {
        $user_id = CRUDBooster::myId();
    }
    return DB::table('drm_products')->where('drm_products.tags', 'LIKE', '%' . $tag_string . '%')->where('drm_products.user_id', $user_id)->whereRaw('cast(drm_products.stock as SIGNED) > 0')->select('drm_products.image as image', 'drm_products.vk_price as vk_price', 'drm_products.tags', 'drm_products.id as id')->get();
}

function dropFunnelCount($tag, $user_id, $flag = null, $lang = 'de')
{
    $query = DrmProduct::whereJsonLength('title->' . $lang, '>', 0)
        ->whereRaw('cast(stock as SIGNED) > 0')
        ->where('user_id', $user_id)
        ->whereRaw('FIND_IN_SET(?,tags)', [$tag]);
    if ($flag === 'data') {
        $query = $query->get();
    } else {
        $query = $query->count();
    }
    return $query;
}

function downloadFtpFile($server, $username, $password, $filepath)
{
    $file = 'ftp://' . $username . ':' . $password . '@' . $server . '/' . $filepath;
    if (file_exists($file)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));
        readfile($file);
        exit;
    } else {
        return 'File Not Found';
    }
}

function getFtpContents($server, $username, $password, $filepath)
{
    $file = 'ftp://' . $username . ':' . $password . '@' . $server . '/' . $filepath;
    if (file_exists($file)) {
        return file_get_contents_utf8($file);
    } else {
        return 'File Not Found';
    }
}

function getChannelLogo($channel)
{
    $logo = [
        1 => asset('images/shop_logo/gambio.jpg'),
        2 => asset('images/shop_logo/lengow.jpg'),
        3 => asset('images/shop_logo/yatego.jpg'),
        4 => asset('images/shop_logo/ebay.jpg'),
        5 => asset('images/shop_logo/amazon.jpg'),
        6 => asset('images/shop_logo/shopify.jpg'),
        7 => "https://woocommerce.com/wp-content/themes/woo/images/logo-woocommerce.svg",
        8 => asset('images/shop_logo/clousale.jpg'),
        9 => asset('images/shop_logo/chrono-24.png'),
        10 => asset('images/shop_logo/droptienda.png'),
        11 => asset('images/shop_logo/etsy.png'),
        12 => asset('images/shop_logo/otto.png'),
        13 => asset('images/shop_logo/kaufland_drm.png'),
        14 => asset('images/shop_logo/check24.png'),
        15 => asset('images/shop_logo/decathlon.webp'),
        16 => asset('images/shop_logo/trade_byte.png'),
        17 => asset('images/shop_logo/sprinter.png'),
        18 => asset('images/shop_logo/tiendanimal.png'),
        19 => asset('images/shop_logo/mediamarkt.png'),
        20 => asset('images/shop_logo/colizey.png'),
        21 => asset('images/shop_logo/limango_drm.png'),
        22 => asset('images/shop_logo/conrad.png'),
        23 => asset('images/shop_logo/fressnapf.png'),
        24 => asset('images/shop_logo/csv.png'),
        25 => asset('images/shop_logo/volkner.png'),
        26 => asset('images/shop_logo/manor.png'),
        27 => asset('images/shop_logo/xxxlutz.png'),
        28 => asset('images/shop_logo/perfumes_club.png'),
        29 => asset('images/shop_logo/home24.png'),
        30 => asset('images/shop_logo/alltricks.png'),
        32 => asset('images/shop_logo/clube_fashion.png'),
        33 => asset('images/shop_logo/zooplus.png'),
        34 => asset('images/shop_logo/pss.png'),
        35 => asset('images/shop_logo/bigbang.png'),
        36 => asset('images/shop_logo/bricodepot.jpg'),
        37 => asset('images/shop_logo/hornbach.png'),
        38 => asset('images/shop_logo/planetahuerto.png'),
        39 => asset('images/shop_logo/carrefour.png'),
        200 => asset('images/menu-icon/drop-campus.png'),
    ];

    return $logo[$channel] ?? "";
}

function getChannelName($channel)
{
    $channels = config('channel.list');
    $channelName = collect($channels)->where('type', $channel)->first();
    return ucfirst(strtolower($channelName['name']));
}

// <NAME_EMAIL>
function updateOrderHistory($order, $status, $message, $options = [])
{
    $payload = [
        'status' => $status,
        'time' => date('Y-m-d H:i:s'),
        'action_by' => CRUDBooster::myId(),
        'user_name' => CRUDBooster::myName(),
        'message' => $message,
    ];

    if( isset($options['files']) && !empty($options['files']) )
    {
        $payload['files'] = $options['files'];
    }

    // $history = $order->order_history ?? [];
    // $history[] = $payload;
    // $order->update(['order_history' => $history]);

    // TODO:: DROPMATIX
    DB::table('order_logs')->insert([
        'order_id' => $order->id,
        'payload' => json_encode($payload),
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    //clear cms client inkasso log
    if(!empty($order->cms_client)){
        Cache::forget('user_inkasso_invoice_list_' . $order->cms_client);
    }
    // app(App\Services\OrderService::class)->sycnOrderStatus($order);

    app(App\Helper\OrderHelper::class)::sendOrderStatusToDt($status, $order);
}

//Order history label
function drmHistoryLabel($status)
{
    $drm_status = config('global.drm_order_status');
    return isset($drm_status[$status]) ? $drm_status[$status] : $status;
}

//Order label by group name
function drmOrderLabelByGroupId($status)
{

    try {
        $group = \App\OrderStatusGroup::has('statuses')
            ->select('id')
            ->whereNotNull('group_name')
            ->where('group_name', 'LIKE', '%' . $status . '%')
            ->orWhereHas('statuses', function ($q) use ($status) {
                $q->where('status_name', 'LIKE', $status);
            })
            ->first();
        if ($group && $group->id) {
            $statuses = config('order_status.statuses');
            $group_id = $group->id;
            return isset($statuses[$group_id]) ? $statuses[$group_id] : $status;
        }
    } catch (\Exception $e) {
    }

    return $status;
}

function excerpt($str, $limit = 50){
    if(strlen($str) > $limit) return substr($str, 0, $limit)."...";
    return $str;
}

//Order created first time history
function drmOrderFirstHistory($order)
{
    $insert_type_name = getInsertTypeName($order->insert_type);
    $shop_channel_name = null;

    $history = $order->order_history ?? [];

    $shop_id = $order->shop_id;
    $shop_type = ($shop_id) ? \App\Shop::find($shop_id, ['channel'])->channel : null;
    if ($shop_type) {
        $shop_channel_name = drm_shop_type_name($shop_type);
    }

    $insert_message = '';
    if ($insert_type_name) {
        $insert_message = $insert_type_name . ' ';
    }

    $insert_message .= (in_array($order->insert_type, [1, 2, 7])) ? 'Order sycronized' : ($order->offer_number > 0 ? 'Offer document' : 'Order' ).' inserted';

    $insert_message .= ($shop_channel_name) ? ' from ' . $shop_channel_name . '.' : '.';
    $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->created_at)), 'action_by' => null, 'message' => $insert_message];

    $invoice_create_message = ($order->offer_number > 0 ? 'Offer' : 'Invoice').' number ' . inv_number_string($order->invoice_number, $order->inv_pattern) . ' created.';
    $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->created_at)), 'action_by' => null, 'message' => $invoice_create_message];

    if($order->status === 'test_order')
    {
        $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->created_at)), 'action_by' => null, 'message' => 'Test order created successfully!'];
    } else if($order->status === 'mp_return') {
        $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->created_at)), 'action_by' => null, 'message' => 'Marketplace Return created successfully!'];

        UpdateProductRq::dispatch(json_decode($order->cart), $order->cms_user_id, $order->shop_id);
    } else if($order->status === 'return_shipped') {
        $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->created_at)), 'action_by' => null, 'message' => 'Marketplace Return Shipped successfully!'];
    }

    if ($order->mail_sent) {
        $email_send_message = 'Invoice number ' . inv_number_string($order->invoice_number, $order->inv_pattern) . ' has been sent to customer by email.';
        $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->mail_sent)), 'action_by' => null, 'message' => $email_send_message];
    }

    if ($order->supplier_time) {
        $order_placed_message = 'Order placed successfully!';
        $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->supplier_time)), 'action_by' => null, 'message' => $order_placed_message];
    }

    if ($order->package_number) {
        $order_tracking_message = 'Order tracking successfully. Tracking number: ' . $order->package_number;
        $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s'), 'action_by' => null, 'message' => $order_tracking_message];
    }

    if($order->insert_type == 9 && str_starts_with($order->order_id_api, 'mp-return'))
    {
        $mp_return_message = 'Shipcloud return payment successfully (Stripe).';
        $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s'), 'action_by' => null, 'message' => $mp_return_message];

        UpdateProductRq::dispatch(json_decode($order->cart), $order->cms_user_id, $order->shop_id);
    }

    if(!empty($order->payment_type) && $order->status == 'paid' && $order->intend_id)
    {
        $payment_message = 'Payment completed successfully by '.$order->payment_type.'!';
        $history[] = ['status' => 'paid', 'time' => date('Y-m-d H:i:s'), 'action_by' => null, 'message' => $payment_message];
    }

    // STORE_ORDER_HISTORY_ON_DATABASE
    $order->update(['order_history' => $history]);

    // TODO:: DROPMATIX
    $historyPayload = array_map(function($payload) use ($order) {

        // Action by name
        if (empty($payload['action_by']))
        {
            $payload['action_by'] = CRUDBooster::myId();
        }

        $payload['user_name'] = CRUDBooster::myName();

        return [
            'order_id' => $order->id,
            'payload' => json_encode($payload),
            'created_at' => now(),
            'updated_at' => now(),
        ];

    }, $history);

    DB::table('order_logs')->insert($historyPayload);
}

//Country name full
function drmCountryNameFull($country_code)
{
    $tax_country = null;
    if ($country_code) {
        $tax_country = DB::table('tax_rates')
            ->where('country_code', 'like', $country_code)
            ->orWhere('country', 'like', $country_code)
            ->orWhere('country_de', 'like', $country_code)
            ->select('country')->first();
    }
    return ($tax_country && $tax_country->country) ? $tax_country->country : $country_code;
}


function channel_manager_user()
{
    return true;
}


//help video track params
function drm_video_params($video_id)
{
    $data = [];
    $random_str = \Illuminate\Support\Str::random(15);
    $user_id = \CRUDBooster::myId();
    return '?user=' . $user_id . '&video=' . $video_id . '&session=' . $random_str;
}

//Drm help video
function drm_help_video($default = [])
{
    return \App\Services\HelpVideo\Video::init()->helpVideo($default);
}

//Drm channel video
function drm_channel_video($channel_id, $options = [])
{
    return \App\Services\HelpVideo\Video::init()->typeVideo(2, $channel_id, $options);
}

//Drm ebay policy video
function drm_ebay_policy_video($policy_id, $options = [])
{
    $options['css'] = 'video-play-single-btn';
    $options['el'] = 'style="display: inline;"';
    return \App\Services\HelpVideo\Video::init()->typeVideo(3, $policy_id, $options);
}

//DRM video
function drm_video_url($video_id, $default = [])
{
    return \App\Services\HelpVideo\Video::init()->getVideoById($video_id, $default);
}

// uses regex that accepts any word character or hyphen in last name
function split_name($name)
{
    $name = trim($name);
    $name = preg_replace('/(_|-)/', ' ', $name);
    $name = trim($name);

    $last_name = (strpos($name, ' ') === false) ? '' : preg_replace('#.*\s([\w-]*)$#', '$1', $name);
    $first_name = trim(preg_replace('#' . preg_quote($last_name, '#') . '#', '', $name));

    if(empty($first_name))
    {
        $parts = explode(' ', $last_name);
        $first_name = @$parts[0];
        $last_name = trim(str_replace($first_name, ' ', $last_name));
    }

    return ['first_name' => $first_name, 'last_name' => $last_name];
}

//Clear remote cache
function clear_remote_cache($name)
{
    try {
        $ch = curl_init();

        $url = 'http://*************/api/clear-cache-data/' . $name;
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_exec($ch);

        curl_close($ch);
    } catch (\Exception $ee) {
    }
}

// Rate Calculation for Email marketing
function dropFunnelAverageRateCalculation($emailCampaign, $customerCount, $flag, $onlyCount = false)
{
    $rate = 0;
    if (!empty($emailCampaign)) {
        $totalCampaign = $emailCampaign->steps()->count() + 1;
        $webhookEventCount = $emailCampaign->webhooks()->whereNotNull($flag)->count();

        if ($onlyCount) {
            return $webhookEventCount;
        }

        if (!empty($customerCount)) {
            $total = $customerCount * $totalCampaign;
            $rate = ($webhookEventCount * 100) / $total;
        }
    }
    return number_format($rate, 2);
}

function isChannelExists($channel, $userId)
{
    return \App\Shop::where(['channel' => $channel, 'user_id' => $userId])->first();
}


//drm invoice prefix generate
function drm_invoice_number_format($inv, $format = null)
{
    $format = trim($format);
    if (empty($format)) return $inv;

    $find = '[inv]';
    return (strpos($format, $find) !== false) ? str_replace($find, $inv, $format) : $inv . '-' . $format;
}

//invoice number format -- used to show inv number
function inv_number_string($inv, $inv_number_string = null)
{
    return ($inv_number_string) ? $inv_number_string : $inv;
}

function convertSecondsToHourMinuteSecond($seconds, $format = ':')
{
    $hours = floor($seconds / 3600);
    $minutes = ($seconds / 60) % 60;
    $second = $seconds % 60;
    return sprintf("%02d%s%02d%s%02d", $hours, $format, $minutes, $format, $second);
//    return "$hours:$minutes:$second";
}


function isDRMUserHasSuperPower()
{
    $is_admin_login = (\Session::get('logged_as') == 1) ? true : false;
    $is_has_old_id = (\Session::get('logged_as_old')) ? true : false;
    return ($is_admin_login || $is_has_old_id || \CRUDBooster::isSuperadmin()) ? true : false;
}


function isDRMUserHasSuperPowerStr()
{
    return isDRMUserHasSuperPower() ? 'yes' : 'no';
}

if (!function_exists('mime_to_ext')) {
    function mime_to_ext($mime)
    {
        $mime_map = [
            'video/3gpp2' => '3g2',
            'video/3gp' => '3gp',
            'video/3gpp' => '3gp',
            'application/x-compressed' => '7zip',
            'audio/x-acc' => 'aac',
            'audio/ac3' => 'ac3',
            'application/postscript' => 'ai',
            'audio/x-aiff' => 'aif',
            'audio/aiff' => 'aif',
            'audio/x-au' => 'au',
            'video/x-msvideo' => 'avi',
            'video/msvideo' => 'avi',
            'video/avi' => 'avi',
            'application/x-troff-msvideo' => 'avi',
            'application/macbinary' => 'bin',
            'application/mac-binary' => 'bin',
            'application/x-binary' => 'bin',
            'application/x-macbinary' => 'bin',
            'image/bmp' => 'bmp',
            'image/x-bmp' => 'bmp',
            'image/x-bitmap' => 'bmp',
            'image/x-xbitmap' => 'bmp',
            'image/x-win-bitmap' => 'bmp',
            'image/x-windows-bmp' => 'bmp',
            'image/ms-bmp' => 'bmp',
            'image/x-ms-bmp' => 'bmp',
            'application/bmp' => 'bmp',
            'application/x-bmp' => 'bmp',
            'application/x-win-bitmap' => 'bmp',
            'application/cdr' => 'cdr',
            'application/coreldraw' => 'cdr',
            'application/x-cdr' => 'cdr',
            'application/x-coreldraw' => 'cdr',
            'image/cdr' => 'cdr',
            'image/x-cdr' => 'cdr',
            'zz-application/zz-winassoc-cdr' => 'cdr',
            'application/mac-compactpro' => 'cpt',
            'application/pkix-crl' => 'crl',
            'application/pkcs-crl' => 'crl',
            'application/x-x509-ca-cert' => 'crt',
            'application/pkix-cert' => 'crt',
            'text/css' => 'css',
            'text/x-comma-separated-values' => 'csv',
            'text/comma-separated-values' => 'csv',
            'application/vnd.msexcel' => 'csv',
            'text/csv; charset=utf-8' => 'csv',
            'application/x-director' => 'dcr',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
            'application/x-dvi' => 'dvi',
            'message/rfc822' => 'eml',
            'application/x-msdownload' => 'exe',
            'video/x-f4v' => 'f4v',
            'audio/x-flac' => 'flac',
            'video/x-flv' => 'flv',
            'image/gif' => 'gif',
            'application/gpg-keys' => 'gpg',
            'application/x-gtar' => 'gtar',
            'application/x-gzip' => 'gzip',
            'application/mac-binhex40' => 'hqx',
            'application/mac-binhex' => 'hqx',
            'application/x-binhex40' => 'hqx',
            'application/x-mac-binhex40' => 'hqx',
            'text/html' => 'html',
            'image/x-icon' => 'ico',
            'image/x-ico' => 'ico',
            'image/vnd.microsoft.icon' => 'ico',
            'text/calendar' => 'ics',
            'application/java-archive' => 'jar',
            'application/x-java-application' => 'jar',
            'application/x-jar' => 'jar',
            'image/jp2' => 'jp2',
            'video/mj2' => 'jp2',
            'image/jpx' => 'jp2',
            'image/jpm' => 'jp2',
            'image/jpeg' => 'jpeg',
            'image/pjpeg' => 'jpeg',
            'application/x-javascript' => 'js',
            'application/json' => 'json',
            'text/json' => 'json',
            'application/vnd.google-earth.kml+xml' => 'kml',
            'application/vnd.google-earth.kmz' => 'kmz',
            'text/x-log' => 'log',
            'audio/x-m4a' => 'm4a',
            'audio/mp4' => 'm4a',
            'application/vnd.mpegurl' => 'm4u',
            'audio/midi' => 'mid',
            'application/vnd.mif' => 'mif',
            'video/quicktime' => 'mov',
            'video/x-sgi-movie' => 'movie',
            'audio/mpeg' => 'mp3',
            'audio/mpg' => 'mp3',
            'audio/mpeg3' => 'mp3',
            'audio/mp3' => 'mp3',
            'video/mp4' => 'mp4',
            'video/mpeg' => 'mpeg',
            'application/oda' => 'oda',
            'audio/ogg' => 'ogg',
            'video/ogg' => 'ogg',
            'application/ogg' => 'ogg',
            'font/otf' => 'otf',
            'application/x-pkcs10' => 'p10',
            'application/pkcs10' => 'p10',
            'application/x-pkcs12' => 'p12',
            'application/x-pkcs7-signature' => 'p7a',
            'application/pkcs7-mime' => 'p7c',
            'application/x-pkcs7-mime' => 'p7c',
            'application/x-pkcs7-certreqresp' => 'p7r',
            'application/pkcs7-signature' => 'p7s',
            'application/pdf' => 'pdf',
            'application/octet-stream' => 'pdf',
            'application/x-x509-user-cert' => 'pem',
            'application/x-pem-file' => 'pem',
            'application/pgp' => 'pgp',
            'application/x-httpd-php' => 'php',
            'application/php' => 'php',
            'application/x-php' => 'php',
            'text/php' => 'php',
            'text/x-php' => 'php',
            'application/x-httpd-php-source' => 'php',
            'image/png' => 'png',
            'image/x-png' => 'png',
            'application/powerpoint' => 'ppt',
            'application/vnd.ms-powerpoint' => 'ppt',
            'application/vnd.ms-office' => 'ppt',
            'application/msword' => 'ppt',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation' => 'pptx',
            'application/x-photoshop' => 'psd',
            'image/vnd.adobe.photoshop' => 'psd',
            'audio/x-realaudio' => 'ra',
            'audio/x-pn-realaudio' => 'ram',
            'application/x-rar' => 'rar',
            'application/rar' => 'rar',
            'application/x-rar-compressed' => 'rar',
            'audio/x-pn-realaudio-plugin' => 'rpm',
            'application/x-pkcs7' => 'rsa',
            'text/rtf' => 'rtf',
            'text/richtext' => 'rtx',
            'video/vnd.rn-realvideo' => 'rv',
            'application/x-stuffit' => 'sit',
            'application/smil' => 'smil',
            'text/srt' => 'srt',
            'image/svg+xml' => 'svg',
            'application/x-shockwave-flash' => 'swf',
            'application/x-tar' => 'tar',
            'application/x-gzip-compressed' => 'tgz',
            'image/tiff' => 'tiff',
            'font/ttf' => 'ttf',
            'text/plain' => 'txt',
            'text/x-vcard' => 'vcf',
            'application/videolan' => 'vlc',
            'text/vtt' => 'vtt',
            'audio/x-wav' => 'wav',
            'audio/wave' => 'wav',
            'audio/wav' => 'wav',
            'application/wbxml' => 'wbxml',
            'video/webm' => 'webm',
            'image/webp' => 'webp',
            'audio/x-ms-wma' => 'wma',
            'application/wmlc' => 'wmlc',
            'video/x-ms-wmv' => 'wmv',
            'video/x-ms-asf' => 'wmv',
            'font/woff' => 'woff',
            'font/woff2' => 'woff2',
            'application/xhtml+xml' => 'xhtml',
            'application/excel' => 'xl',
            'application/msexcel' => 'xls',
            'application/x-msexcel' => 'xls',
            'application/x-ms-excel' => 'xls',
            'application/x-excel' => 'xls',
            'application/x-dos_ms_excel' => 'xls',
            'application/xls' => 'xls',
            'application/x-xls' => 'xls',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' => 'xlsx',
            'application/vnd.ms-excel' => 'xlsx',
            'application/xml' => 'xml',
            'text/xml' => 'xml',
            'text/xsl' => 'xsl',
            'application/xspf+xml' => 'xspf',
            'application/x-compress' => 'z',
            'application/x-zip' => 'zip',
            'application/zip' => 'zip',
            'application/x-zip-compressed' => 'zip',
            'application/s-compressed' => 'zip',
            'multipart/x-zip' => 'zip',
            'text/x-scriptzsh' => 'zsh',
        ];
        return $mime_map[$mime] ?? "csv";
    }
}

if (!function_exists('getRemoteFile')) {
    function getRemoteFile($url): array
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $data = trim(curl_exec($ch));
        $mime = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        if ($mime) {
            $ext = mime_to_ext($mime);
        } else {
            $ext = pathinfo($url, PATHINFO_EXTENSION);
        }
        curl_close($ch);

        return [
            'data' => makeUtf8($data),
            'ext' => $ext
        ];
    }
}


if (!function_exists('drmGetBarcodeEAN13')) {
    function drmGetBarcodeEAN13($code, $type = 'C39+', $w = 2, $h = 30, $color = array(0, 0, 0), $showCode = true)
    {
        if (is_null($code)) return;
        try {
            return '<img src="data:image/png;base64,' . \Milon\Barcode\Facades\DNS1DFacade::getBarcodePNG($code, $type, $w, $h, $color, $showCode) . '" alt="' . $code . '"/>';
        } catch (\Exception $e) {
            return $code;
        }
    }
}


function determineVodTagByWatchPercentage($percentage)
{
    switch ($percentage) {
        case ($percentage <= 5):
            $tag = 'bellow or equal 5';
            break;
        case ($percentage <= 70):
            $tag = 'Bellow or equal 70';
            break;
        default :
            $tag = "Default";
    };
    return $tag;
}

function getStepMailSendTime($campaign, $step)
{
    $campaignStart = $campaign->created_at;
    $campaignMinutes = $campaignHour = $campaignDay = null;
    if (!empty($campaign->day_value_type) && isset($campaign->day_value)) {
        switch ($campaign->day_value_type) {
            case 'minutes':
                $campaignMinutes = $campaignStart->addMinutes($campaign->day_value);
                break;
            case 'hours':
                $campaignHour = $campaignStart->addHours($campaign->day_value);
                break;
            case 'days':
                $campaignDay = $campaignStart->addDays($campaign->day_value);
                break;
        }
    }

    $stepStart = \Carbon\Carbon::parse($step->created_at);
    $stepMinutes = $stepHour = $stepDay = null;
    if (!empty($step->type) && isset($step->value)) {
        switch ($step->type) {
            case 'minutes':
                $stepMinutes = $stepStart->addMinutes($step->value);
                break;
            case 'hours':
                $stepHour = $stepStart->addHours($step->value);
                break;
            case 'days':
                $stepDay = $stepStart->addDays($step->value);
                break;
        }
    }
    if ($campaignMinutes && $stepMinutes) {
        return $campaignMinutes->diffInMinutes($stepMinutes) . ' Minutes';
    }
    if ($campaignHour && $stepHour) {
        return $campaignHour->diffInHours($stepHour) . ' Hours';
    }
    if ($campaignDay && $stepDay) {
        return $campaignDay->diffInDays($stepDay) . ' Days';
    }
    $diff = $campaignStart->diff($stepStart);
    if ($diff->d)
        return $diff->d . ' Days';
    if ($diff->m)
        return $diff->m . ' Minutes';

    return $diff->h . ' Hours';
}


//Get product current lang
function user_product_current_lang()
{
    return app('App\Services\UserService')->getLang(CRUDBooster::myParentId());
}


// Rate Calculation for Email marketing Step
function dropFunnelStepRateCalculation($emailCampaign, $customerCount, $flag, $stepId, $onlyCount = false)
{
    $rate = 0;
    if (!empty($emailCampaign)) {
        $webhookEventCount = $emailCampaign->webhooks()->where('step_id', $stepId)->whereNotNull($flag)->count();
        if ($onlyCount) {
            return $webhookEventCount;
        }
        if (!empty($customerCount)) {
            $rate = ($webhookEventCount * 100) / $customerCount;
        }
    }
    return number_format($rate, 2);
}

function dropFunnelStepRateCalculationNew($emailCampaign, $customers, $flag, $stepId, $onlyCount = false)
{
    $rate = 0;
    if (!empty($emailCampaign)) {
        $customerIds = $customers->pluck('id')->toArray();
        $webhookEventCount = $emailCampaign->webhooks()->where('step_id', $stepId)->whereNotNull($flag)->whereIn('customer_id', $customerIds)->count();
        if ($onlyCount) {
            return $webhookEventCount;
        }
        if ($customers->isNotEmpty()) {
            $customerCount = $customers->count();
            $rate = ($webhookEventCount * 100) / $customerCount;
        }
    }
    return number_format($rate, 2);
}


//DT STORE plan label
function dtStorePlanLabel($plan_id){
    $plans = config('dt_store.dt_plan_labels') ?? [];
    return $plans[$plan_id] ?? null;
}

function notificationToTelegram($txt)
{
    return;
    try {
        $telegramUrl = 'https://api.telegram.org/bot';
        $token = '**********************************************'; // new_demo_chan channel
        $chatId = **********;
        file_get_contents($telegramUrl . $token . '/sendMessage?chat_id=' . $chatId . '&text=' . $txt . '&parse_mode=HTML');
    } catch(Exception $exception) {

    }
}

function can_access($feature_id,$module_id){
    if (!CRUDBooster::isSuperadmin() && CRUDBooster::isSubUser()) {
        if(sub_account_can($feature_id,$module_id) || sub_account_can('all_modules', 122)){
            return true;
        }
    }
    else{
        return true;
    }
    return false;
}

function getDrmAllCustomerChannelLogo($channel)
{
    $logo = [
        1 => asset('images/shop_logo/drm_all_customer_channel_logo/gambio.jpg'),
        2 => asset('images/shop_logo/drm_all_customer_channel_logo/lengow.jpg'),
        3 => asset('images/shop_logo/drm_all_customer_channel_logo/yatego.jpg'),
        4 => asset('images/shop_logo/drm_all_customer_channel_logo/ebay.jpg'),
        5 => asset('images/shop_logo/drm_all_customer_channel_logo/amazon.jpg'),
        6 => asset('images/shop_logo/drm_all_customer_channel_logo/shopify.jpg'),
        7 => asset('images/shop_logo/drm_all_customer_channel_logo/woocommerce.png'),
        8 => asset('images/shop_logo/drm_all_customer_channel_logo/clousale.jpg'),
        9 => asset('images/shop_logo/drm_all_customer_channel_logo/chrono-24.png'),
        10 => asset('images/shop_logo/drm_all_customer_channel_logo/droptienda.png'),
        11 => asset('images/shop_logo/drm_all_customer_channel_logo/etsy.png'),
        12 => asset('images/shop_logo/drm_all_customer_channel_logo/otto.png'),
        14 => asset('images/shop_logo/check24.png'),
        15 => asset('images/shop_logo/decathlon.webp'),
        21 => asset('images/shop_logo/drm_all_customer_channel_logo/limango_drm.png'),
        22 => asset('images/shop_logo/conrad.png'),
        23 => asset('images/shop_logo/fressnapf.png'),
        25 => asset('images/shop_logo/volkner.png'),
        26 => asset('images/shop_logo/manor.png'),
        27 => asset('images/shop_logo/xxxlutz.png'),
        28 => asset('images/shop_logo/perfumes_club.png'),
        29 => asset('images/shop_logo/home24.png'),
        30 => asset('images/shop_logo/alltricks.png'),
        32 => asset('images/shop_logo/clube_fashion.png'),
        33 => asset('images/shop_logo/zooplus.png'),
        34 => asset('images/shop_logo/pss.png'),
        35 => asset('images/shop_logo/bigbang.png'),
        36 => asset('images/shop_logo/bricodepot.jpg'),
        37 => asset('images/shop_logo/hornbach.png'),
        38 => asset('images/shop_logo/planetahuerto.png'),
        39 => asset('images/shop_logo/carrefour.png'),
    ];

    return $logo[$channel] ?? asset('images/shop_logo/drm_customer_dashboard_channel_logo/dropmatix-logo.png');
}

function getCustomerDashboardChannelLogo($channel)
{
    $logo = [
        1 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/gambio.jpg'),
        2 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/lengow.jpg'),
        3 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/yatego.jpg'),
        4 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/ebay.jpg'),
        5 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/amazon.jpg'),
        6 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/shopify.jpg'),
        7 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/woocommerce.png'),
        8 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/clousale.jpg'),
        9 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/chrono-24.png'),
        10 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/droptienda.png'),
        11 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/etsy.png'),
        12 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/otto.png'),
        14 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/check24.png'),
        15 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/decathlon.png'),
        21 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/limango_drm.png'),
        22 => asset('images/shop_logo/conrad.png'),
        23 => asset('images/shop_logo/fressnapf.png'),
        25 => asset('images/shop_logo/volkner.png'),
        26 => asset('images/shop_logo/manor.png'),
        27 => asset('images/shop_logo/xxxlutz.png'),
        28 => asset('images/shop_logo/perfumes_club.png'),
        29 => asset('images/shop_logo/home24.png'),
        30 => asset('images/shop_logo/alltricks.png'),
        32 => asset('images/shop_logo/clube_fashion.png'),
        33 => asset('images/shop_logo/zooplus.png'),
        34 => asset('images/shop_logo/pss.png'),
        35 => asset('images/shop_logo/bigbang.png'),
        36 => asset('images/shop_logo/bricodepot.jpg'),
        37 => asset('images/shop_logo/hornbach.png'),
        38 => asset('images/shop_logo/planetahuerto.png'),
        39 => asset('images/shop_logo/carrefour.png'),
        200 => asset('images/shop_logo/drm_customer_dashboard_channel_logo/vod.png'),
    ];

    return $logo[$channel] ?? asset('images/shop_logo/drm_customer_dashboard_channel_logo/dropmatix-logo.png');
}

function getCountryImage($countryCode)
{

    if(strcasecmp('BE', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/BE.png');
    }else if(strcasecmp('BG', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/BG.png');
    }else if(strcasecmp('Dk', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/Dk.png');
    }else if(strcasecmp('DE', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/DE.png');
    }else if(strcasecmp('EE', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/EE.png');
    }else if(strcasecmp('FI', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/FI.png');
    }else if(strcasecmp('FR', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/FR.png');
    }else if(strcasecmp('EL', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/EL.png');
    }else if(strcasecmp('IE', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/IE.png');
    }else if(strcasecmp('IT', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/IT.png');
    }else if(strcasecmp('HR', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/HR.png');
    }else if(strcasecmp('LV', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/LV.png');
    }else if(strcasecmp('LT', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/LT.png');
    }else if(strcasecmp('LU', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/LU.png');
    }else if(strcasecmp('MT', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/MT.png');
    }else if(strcasecmp('NL', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/NL.png');
    }else if(strcasecmp('AT', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/AT.png');
    }else if(strcasecmp('PL', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/PL.png');
    }else if(strcasecmp('PT', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/PT.png');
    }else if(strcasecmp('RO', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/RO.png');
    }else if(strcasecmp('SE', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/SE.png');
    }else if(strcasecmp('SK', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/SK.png');
    }else if(strcasecmp('SI', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/SI.png');
    }else if(strcasecmp('ES', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/ES.png');
    }else if(strcasecmp('CZ', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/CZ.png');
    }else if(strcasecmp('HU', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/HU.png');
    }else if(strcasecmp('GB', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/GB.png');
    }else if(strcasecmp('CY', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/CY.png');
    }else if(strcasecmp('CH', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/CH.png');
    }else if(strcasecmp('USA', trim($countryCode)) == 0){
        return asset('images/flags/country_flag/USA.png');
    }else{
        return "";
    }
}

/**
 * If all array keys has a value
 *      return: false
 * Else
 *      return: list of keys which have empty value
 */
function hasEmptyValue($arr){
    $empty = [];
    foreach ($arr as $key => $value) {
        // $value = trim($value);
        if( is_numeric($value) ){
            if( empty($value) || $value <= 0 ) $empty[] = $key;
        }else{
            if( empty($value) ) $empty[] = $key;
        }
    }
    if (empty($empty)) return false;
    return $empty;
}


function getElementName($id)
{
    $element = [
        'cus_1' => __('dashboard.latest_orders'),
        'cus_2' => __('dashboard.business_development'),
        'cus_3' => __('dashboard.app_store_tittle'),
        'cus_4' => __('dashboard.get_manual'),
        'cus_5' => __('dashboard.nue'),
        'cus_6' => __('dashboard.TOP_FIVE_CHANNELS_SALE'),
        'cus_7' => __('dashboard.bestSellingProduct'),
        'cus_8' => __('dashboard.news'),
        'cus_9' => 'Dropshipping Magazine',
        'cus_10' => 'Calendar Booking',
        'cus_11' => __('dashboard.ALL_CONTACTS'),
        'cus_12' => __('dashboard.SHOPPING_CART'),
        'cus_13' => __('dashboard.TOP_TEN_CUSTOMER'),
        'cus_14' => __('dashboard.TOP_FIVE_COUNTRIES_CUSTOMER'),
        'cus_15' => __('dashboard.MOST_POPULAR_TAGS'),
        'cus_16' => __('dashboard.ORDER_CANCELLATION_LIST'),
        'cus_17' => __('dashboard.PERFORMANCE_CHECKLIST'),
        'cus_18' => 'VOD watchlist',
        'cus_19' => 'Latest Droptienda installations',
        'cus_20' => 'Order chat history',
        'cus_21' => __('Marketplace New Porducts'),
        'cus_22' => __('New Orders'),
        'cus_23' => __('Customers / Leads'),
        'cus_24' => __('Average Order'),
        'cus_25' => __('Order Sum'),
        'cus_26' => __('Invoice Sum'),
        'cus_27' => __('Recall Appointments'),
        'cus_28' => __('Stock'),
        'cus_29' => __('Products Sold'),
        'cus_30' => __('Stock Report'),
        'cus_31' => __('Order Report'),
        'cus_32' => __('Sold Product'),
        'cus_33' => __('Turnover'),
        'cus_34' => __('Marketplace Category Chart'),
        'cus_35' => __('Marketplace Category List'),
        'cus_36' => __('marketplace.Marketplace Campaigns Product List'),
        'cus_37' => __('Channel Recommendation'),
        'cus_38' => __('Product Metrics Graph'),
        'cus_39' => __('Best Seller Graph'),
        'cus_40' => __('Marketplace New Products Statistics'),
        'cus_41' => __('Highest RQ Products'),
        'cus_42' => __('MP Agreement Payment Target'), // only for 2455
        'cus_43' => __('Rainforest Scanned Products'), // only for 2455
        'cus_44' => __('Latest Masterclass Videos'),
        'cus_45' => 'Expertiserocks® ' . __('New products without Dropmatix seller'), // special widget alternative to 21
        'cus_46' => 'Expertiserocks® ' . __('Best selling niche products'), // special widget activated from admin through detail_user_management view
        'cus_47' => __('Customer Country'),
        'cus_48' => __('Most Frequenyz Resellers'),
        'cus_49' => __('Top 10 Most Viewed Users'),
        'cus_50' => __('Top 10 Most Viewed Videos'),
        'cus_51' => __('Virtual Wallet'),
        'cus_52' => __('Order Status'),
        'cus_53' => __('Most Activated Subcategory'),
        'cus_54' => __('Most Sales Subcategory'),

        'admin_1' => 'Monthly TurnOver',
        'admin_2' => 'Top 10 Sellers',
        'admin_3' => 'Latest DRM Users',
        'admin_4' => 'Favourite Users',
        'admin_5' => 'Paywall',
        'admin_6' => 'Total Users',
        'admin_7' => 'Total Product Sold',
        'admin_8' => 'Total Product Sold Value',
        'admin_9' => 'Neueste Bestellungen',
        'admin_10' => __('dashboard.business_development'),
        'admin_11' => 'Top 5 Highest Order Cancellation Accounts',
        'admin_12' => 'Top 10 Viewed Videos',
        'admin_13' => 'Top 10 Most Viewed Users',
        'admin_14' => 'Wochenplaner',
        'admin_15' => 'Managed clients with direct access',
        'admin_16' => 'Daily Account Appointment',
        'admin_17' => 'Dropmatix Account Appointment',
        'admin_18' => 'Geteiltes Projektmanagement',
        'admin_19' => 'Marketplace Chat History',

        'sup_1' => __('dashboard.latest_orders'),
        'sup_2' => __('dashboard.business_development'),
        'sup_3' => __('dashboard.bestSellingProduct'),
        'sup_4' => 'Dropshipping Magazine',
        'sup_5' => __('dashboard.SHOPPING_CART'),
        'sup_6' => __('dashboard.ORDER_CANCELLATION_LIST'),
        'sup_7' => __('dashboard.PERFORMANCE_CHECKLIST'),
    ];

    return $element[$id] ?? "element";
}


function getUserSavedLang($email)
{
    return DB::table('cms_users')->where('email', $email)->value('last_lang');
}


if(!function_exists('create_agb_log')){
    function create_agb_log(int $user_id, array $agb = [], array $change_log = [], string $message = "", $ip = null)
    {
        \App\AgbLogs::create([
            'user_id'       => $user_id,
            'agb'           => $agb,
            'change_log'    => $change_log,
            'message'       => $message,
            'ip_address'    => $ip ?? get_client_ip()
        ]);
    }
}


if(!function_exists('get_client_ip')){
    function get_client_ip() {
        $ipaddress = '';
        if (isset($_SERVER['HTTP_CLIENT_IP']))
            $ipaddress = $_SERVER['HTTP_CLIENT_IP'];
        else if(isset($_SERVER['HTTP_X_FORWARDED_FOR']))
            $ipaddress = $_SERVER['HTTP_X_FORWARDED_FOR'];
        else if(isset($_SERVER['HTTP_X_FORWARDED']))
            $ipaddress = $_SERVER['HTTP_X_FORWARDED'];
        else if(isset($_SERVER['HTTP_FORWARDED_FOR']))
            $ipaddress = $_SERVER['HTTP_FORWARDED_FOR'];
        else if(isset($_SERVER['HTTP_FORWARDED']))
            $ipaddress = $_SERVER['HTTP_FORWARDED'];
        else if(isset($_SERVER['REMOTE_ADDR']))
            $ipaddress = $_SERVER['REMOTE_ADDR'];
        else
            $ipaddress = 'UNKNOWN';
        return $ipaddress;
    }
}


if(!function_exists('is_fabian_or_local'))
{
    function is_fabian_or_local()
    {
        return isLocal() || \CRUDBooster::myId() == 98;
    }
}

if (!function_exists('notify_stock_update')) {
    function notify_stock_update($product_id, $user_id)
    {
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => "http://*************/api/notify_stock_update",
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_POSTFIELDS => [
                'user_id' => $user_id,
                'product_id' => $product_id
            ]
        ));
        curl_exec($ch);
        curl_close($ch);
    }
}

if (!function_exists('notify_uvp_update')) {
    function notify_uvp_update($product_id, $user_id)
    {
        try {
            $ch = curl_init();
            curl_setopt_array($ch, array(
                CURLOPT_URL => "http://*************/api/notify_uvp_update",
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => [
                    'user_id' => $user_id,
                    'product_id' => $product_id
                ]
            ));
            curl_exec($ch);
            curl_close($ch);
        }catch (Exception $e){}

    }
}

if(!function_exists('_log')){
    function _log($string, $filename = 'logs.txt'){
        $string=print_r($string, true);
        file_put_contents($filename, "[".date('Y-m-d H:i:s')."] ".$string.PHP_EOL , FILE_APPEND | LOCK_EX);
    }
}
if(!function_exists('strip_tags_with_whitespace')){
    function strip_tags_with_whitespace($string, $allowable_tags = null){
        $string = str_replace('<', ' <', $string);
        $string = strip_tags($string, $allowable_tags);
        $string = str_replace('  ', ' ', $string);
        $string = trim($string);

        return $string;
    }

}

//SS ENV
if(!function_exists('_ssENV')){
    function _ssENV($key, $default = null){
        $string =  env($key);

        if(env('APP_ENV') != 'production') return $string;

        if(is_null($string)) return $default;

        $ciphering = "AES-128-CTR"; //Cipher method
        $iv_length = openssl_cipher_iv_length($ciphering); // Use OpenSSl Encryption method
        $options = 0;
        $encryption_iv = '1234567341011121'; // Non-NULL Initialization Vector for encryption
        $encryption_key = "mcXaqcTOFqG"; //encryption key
        return openssl_decrypt ($string, $ciphering, $encryption_key, $options, $encryption_iv);
    }
}

// SED -> ll
function addProtectedShopCharge($amount){
    return $amount < 250 ? $amount += 7.99 : $amount;
}
// SED -> ll

if(!function_exists('user_photo')){
    function user_photo($url){
        if(empty($url)) return null;
        $search = '.com/';
        return strpos($url, $search) ? $url : asset($url);
    }
}

if(!function_exists('droptiendaSubscriptionMail')){
    function droptiendaSubscriptionMail($data, $uid){
        $cms_user_id = $uid;
        $emailSetting = DB::table('dt_subscription_mails')->where('cms_user_id', $cms_user_id)->first();

        $toEmail = $data['user_mail'];
        $bcc = null;
        $subject = "Droptienda Subscription Email";
        $template = "Hi, Your have successfully subscribed.";
        $senderEmail = DB::table('cms_users')->where('id', $cms_user_id)->value('email');

        if (!empty($emailSetting)) {
            $subject = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
            $senderEmail = empty($emailSetting->sender_email) ? $senderEmail : $emailSetting->sender_email;
            $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
            $bcc = empty($emailSetting->bcc_email) ? $bcc : $emailSetting->bcc_email;
        }

        $template = preg_replace('/\[customer_name]/', $data['username'], $template);
        $template = preg_replace('/\[company_name]/', '', $template);
        $template = preg_replace('/\[agreement_id]/', $data['agreement_id'], $template);

        // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])) ){

            $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();
            $signature_tags = [];

            if($email_signatures){
                foreach($email_signatures as $key => $signature){
                    // $signature_tags['drm-sign-'.$key] = $signature;
                    $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
                }
            }

        // }

        return ['subject' => $subject, 'body' => $template, 'bcc' => $bcc, 'senderEmail' => $senderEmail, 'toEmail' => $toEmail];
    }
}

if(!function_exists('droptiendaSubscriptionCancelMail')){
    function droptiendaSubscriptionCancelMail($data, $uid){
        $cms_user_id = $uid;
        $emailSetting = DB::table('subscription_cancel_mail')->first();
        // dd($cms_user_id);
        $toEmail = $data['user_mail'];
        $bcc = null;
        $subject = "Subscription Order Cancel Email";
        $template = "Hi, Your subscription is cancelled.";
        $senderEmail = DB::table('cms_users')->where('id', $cms_user_id)->value('email');
        if (!empty($emailSetting)) {
            $subject = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
            $senderEmail = empty($emailSetting->sender_email) ? $senderEmail : $emailSetting->sender_email;
            $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
            $bcc = empty($emailSetting->bcc_email) ? $bcc : $emailSetting->bcc_email;
        }

        $template = preg_replace('/\[customer_name]/', $data['username'], $template);
        $template = preg_replace('/\[company_name]/', '', $template);
        $template = preg_replace('/\[agreement_id]/', $data['agreement_id'], $template);

        // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])) ){

            $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();
            $signature_tags = [];

            if($email_signatures){
                foreach($email_signatures as $key => $signature){
                    // $signature_tags['drm-sign-'.$key] = $signature;
                    $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
                }
            }

        // }

        return ['subject' => $subject, 'body' => $template, 'bcc' => $bcc, 'senderEmail' => $senderEmail, 'toEmail' => $toEmail];
    }
}

if(!function_exists('new_module_tester')){
    function new_module_tester(): bool
    {
        return true;
        $user_id = CRUDBooster::myParentId();
        if(isLocal() || in_array($user_id,[212,62])){
            return true;
        }
        return false;
    }
}

if(!function_exists('have_dublicate')){
    function have_dublicate($ean): bool
    {
        $country_id = Session::get('filter_country_' . CRUDBooster::myParentId()) ?? 1;
        $duplicate_product = \App\MarketplaceProducts::where('ean', $ean)->where('country_id', $country_id)->limit(2)->count();
        return $duplicate_product > 1;
    }
}

if(!function_exists('isSidebarCollapsed')){
    function isSidebarCollapsed(): bool
    {
        $key = 'collapse_sidebar_'.CRUDBooster::myId();
        return Cache::get($key, 'no') === 'yes';
    }
}

if(!function_exists('branding_slug')){
    function branding_slug($slash = ''): string
    {
        if(is_dt_entry_user() || isset($_GET['droptienda-login'])) return 'droptienda'.$slash;

        if(is_relavida() || is_relavida_login())
        {
            return 'relavida'.$slash;
        }
        return '';
    }
}

if(!function_exists('is_relavida_login'))
{
    function is_relavida_login(): bool
    {
        return isset($_GET['relavida-login']);
    }
}

if(!function_exists('branding_css_name')){
    function branding_css_name(): string
    {
        if(is_dt_entry_user())
        {
            return 'drm_color_droptienda.css?v=36';
        }

        if(is_relavida())
        {
            return 'drm_color_rilavida.css?v=36';
        }

        return 'drm_color.css?v=36';
    }
}

if(!function_exists('hide_help_buttons')){
    function hide_help_buttons(): bool
    {
        return is_relavida();
    }
}

if(!function_exists('is_relavida')){
    function is_relavida(): bool
    {
        if(\Session::has('user_theme') && Session::get('user_theme') == 'relavida')
        {
            return true;
        }

        if(\Session::has('relavida_user') && \Session::get('relavida_user')) return true;

        return is_relavida_user();
    }
}

if(!function_exists('is_relavida_user')){
    function is_relavida_user(): bool
    {
        return CRUDBooster::myId() && CRUDBooster::myParentId() == 2817;
    }
}


if(!function_exists('is_relavida_user_db')){
    function is_relavida_user_db($user): bool
    {
        return !empty($user) && ($user->id == 2817 || $user->user_theme == 'relavida');
    }
}


if(!function_exists('drm_login_url')){
    function drm_login_url($user = null): string
    {
        $arg = null;
        if(array_key_exists('droptienda-login', request()->query() ?? []) || is_dt_entry_user())
        {
            $arg = 'droptienda-login';
        }

        return is_relavida() || is_relavida_user() || is_relavida_login() || is_relavida_user_db($user) ? 'https://www.relavida.com/haendlerlogin' : route('getLogin', $arg);
    }
}

if(!function_exists('is_dt_entry_user')){
    function is_dt_entry_user(): bool
    {
        if(!checkTariffEligibility(CRUDBooster::myParentId())) return false;

        // if (CRUDBooster::myParentId() < 3110) return false;

        if(!is_dt_user()) return false;

        $hasImportPlan = \Session::has('has_import_plan_record') && \Session::get('has_import_plan_record');
        if(!$hasImportPlan) return true;

        return false;
    }
}

if(!function_exists('is_dt_user')){
    function is_dt_user(): bool
    {
        return \Session::has('is_dt_user') && \Session::get('is_dt_user');
    }
}

if(!function_exists('ex_time')){
    function ex_time(): string
    {
        return round(microtime(true) - $_SERVER["REQUEST_TIME_FLOAT"],2);
    }
}

if(!function_exists('upload_url_image')){
    function upload_url_image($images = []): array
    {
        if (!is_array($images)) {
            $images = [$images];
        }
        if(!empty($images)){
           foreach($images as $image){
                $path = 'marketplace-products/' . CRUDBooster::myParentId();
                $extension = pathinfo(parse_url($image, PHP_URL_PATH), PATHINFO_EXTENSION);
                $randomStr = substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', mt_rand(1, 5))), 1, 40);
                $fileName = !empty($extension)
                        ? $path . '/' . $randomStr . "." . $extension
                        : $path . '/' . $randomStr;
                $fileContent = file_get_contents($image);
                Storage::disk('spaces')->put($fileName, $fileContent, 'public');
                $imagesUrl[] = Storage::disk('spaces')->url($fileName) ?? null;
           }
        }
        return $imagesUrl;
    }
}

if(!function_exists('orderCountryName')){
    function orderCountryName($order): string
    {
        if(@$order->customer_info)
        {
            return @json_decode($order->billing, true)['country'] ?? "de";
        }
        return "";
    }
}

if(!function_exists('orderCustomerName')){
    function orderCustomerName($order): string
    {
        if(@$order->customer_info)
        {
            return @json_decode($order->billing, true)['name'] ?? "";
        }
        return "";
    }
}

if(!function_exists('marketplaceSupplierName')){
    function marketplaceSupplierName($productId): string
    {
        $product = \App\Models\Marketplace\Product::select('id','delivery_company_id','supplier_id')->find($productId);
        if ($product->delivery_company_id) {
            return \App\DeliveryCompany::find($product->delivery_company_id)->name ?? '-';
        } elseif ($product->supplier_id) {
            return \App\User::where('id', $product->supplier_id)->first()->name ?? '-';
        } else {
            return '-';
        }
    }
}

function userWiseVkPriceCalculate($vk_price, $user_id, $product_view = false, $discount = 0.0, $api_id = 0)
{
    $agreement = DB::table('mp_payment_agreements')
                ->where('user_id', '=', $user_id)
                ->where('type', '=', 1)
                ->select('price_markup')
                ->first();

    $is_demo_fifteen = DB::table('cms_users')
        ->where('id', $user_id)
        ->where('id_cms_privileges', 13)
        ->exists();

    $price_markup = $agreement ? $agreement->price_markup : 0.0;
    $vk_price     = $vk_price + (($price_markup * $vk_price) / 100);
    $vk_price = $vk_price - (($discount * $vk_price) / 100);

    if($user_id == 3938 || (in_array($user_id, [3602, 4293]) && $api_id == 5)){
        $vk_price = $vk_price - ($vk_price * 0.05);
    }

    if($user_id == 2872){
        $vk_price = $vk_price - ($vk_price * 0.08);
    }

    if($is_demo_fifteen){
        $vk_price = $vk_price - ($vk_price * 0.15);
    }

    if($product_view){
        $vk_price = number_format($vk_price, 2, ',', '.');
    }

    return $vk_price;
}

if(!function_exists('getNextPreviousLinks')){
    function getNextPreviousLinks($current_url, $default_url): array
    {
        $previous = '';
        $next = '';
        $array_key = array_search($current_url, $default_url);
        $total_link = count($default_url);
        if($array_key == 0){
            $next = $default_url[$array_key + 1];
        }else if( ($array_key + 1) == $total_link){
            $previous = $default_url[$array_key - 1];
        }else{
            $previous = $default_url[$array_key - 1];
            $next = $default_url[$array_key + 1];
        }

        return [
            'previous' => $previous,
            'next' => $next
        ];
    }
}

if(!function_exists('findSystemMail')){
    function findSystemMail($table, $user_id, $channel = null)
    {
        $mail_temp = DB::table($table)
                ->where('cms_user_id', $user_id)
                ->where('channel', $channel)
                ->where(function($q) use ($channel){
                    $q->where('channel', $channel);
                    $q->orWhereNull('channel');
                })
                ->orderBy('channel', 'desc')
                ->first();

        return $mail_temp;
    }
}

if(!function_exists('localDevelopment')){
    function localDevelopment($string): bool
    {
        $module = [
            'widget_iframe',
            'drm_tariff',
            'dropfunnle_new_step'
        ];

        if(in_array($string, $module)){
            if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2455])){
                return true;
            }else{
                return false;
            }
        }else{
            return false;
        }
    }
}


if(!function_exists('getMpTurnoverCreditBasedOnRange')){
    function getMpTurnoverCreditBasedOnRange($value)
    {
        if ($value >= 1 && $value < 5) {
            return 2;
        } elseif ($value >= 5 && $value < 15) {
            return 5;
        } elseif ($value >= 15 && $value < 30) {
            return 7;
        } elseif ($value >= 30 && $value < 100) {
            return 15;
        } elseif ($value >= 100) {
            return 30;
        } else {
            return 1;
        }

    }
}

if(!function_exists('dtTariffPurchased')){
    function dtTariffPurchased($user_id): bool
    {
        $dt_tariff_purchased = DB::table('dt_tariff_purchases')->where('user_id', $user_id)->where('end_date', '>=', \Carbon\Carbon::now())->exists();
        return $dt_tariff_purchased;
    }
}

if(!function_exists('userTotalChannels')){
    function userTotalChannels($user_id): int{
        $total_channel = 0;

        $channel_count = DB::table('shops')->where('user_id', $user_id)->where('status', 1)->count();

        if($channel_count > 0){
            $total_channel =  $channel_count;
        }

        return $total_channel;
    }
}

if(!function_exists('hascustomerDrmOrDtTariff')){
  function hascustomerDrmOrDtTariff($customer_id){
    $userPlan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($customer_id);
    $planIds = is_dt_user() ? [31] :  [26, 27];
    return in_array($userPlan['import_plan_id'], $planIds);
  }
}

function MarketplaceProductDiscount($product,$user_id){

    $product_discount = 0.0;
    $cat_discount     = 0.0;

    if($product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
        $product_discount = $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage / 4) : 0.0;
    }

    if($product->mainCategory->start_date <= now() && $product->mainCategory->end_date >= now() && $product->mainCategory->is_offer_active){
        $cat_discount = $product->mainCategory->discount_percentage ?? 0.0;
    }
    $discount = $product_discount + $cat_discount;

    $discount_price =  $product->vk_price > 0 ? userWiseVkPriceCalculate($product->vk_price, $user_id, false ,$discount, $product->api_id) : 0.00;
    return round($discount_price, 2);
}

if (!function_exists('QuantityWiseMarketplaceScaledPriceDiscount')) {
    function QuantityWiseMarketplaceScaledPriceDiscount($quantity, $vk_price, $scaled_price_id){

        $scaled_price_discount_list = \App\Models\Marketplace\ScaledPriceDiscountForDirectOrder::where('id', $scaled_price_id)->first();
        $quantity_wise_discount = $scaled_price_discount_list->quantity_wise_discount ?? [];

        if(array_key_exists($quantity, $quantity_wise_discount)) {
            return round(($vk_price * $quantity_wise_discount[$quantity])/100 ?? 0.0, 2);

        } else {
            krsort($quantity_wise_discount);
            foreach ($quantity_wise_discount as $key => $discount) {
                if ($quantity >= $key) {
                    return round(($vk_price * $discount) / 100, 2);
                }
            }
        }

        return 0.0;
    }
}

if (!function_exists('QuantityWiseApiScaledPriceDiscount')) {
    function QuantityWiseApiScaledPriceDiscount($product, $quantity, $price, $product_discount = null){

        $api_scaled_price = \App\Models\Marketplace\MpApiScaledPrice::where('item_number', $product->item_number)
                    ->where('stock', '<=', $quantity)->select('price', 'stock')->orderBy('stock', 'desc')->first();
        if(!blank($api_scaled_price)){
            $scaled_price = userWiseVkPriceCalculate($api_scaled_price->price * 1.10, CRUDBooster::myParentId(), false, $product_discount ?? SingleMarketplaceProductDiscount($product), $product->api_id);
            $discount_percentage = (($price - $scaled_price) / $price ) * 50;
            return  round($price - ($scaled_price + ($scaled_price * $discount_percentage / 100)), 2);
        }

        return 0.0;
    }
}

if (!function_exists('SingleMarketplaceProductDiscount')) {
    function SingleMarketplaceProductDiscount($product){
        $product_discount = 0.0;
        $cat_discount     = 0.0;

        if($product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
            $product_discount = $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage / 4) : 0.0;
        }

        if($product->mainCategory->start_date <= now() && $product->mainCategory->end_date >= now() && $product->mainCategory->is_offer_active){
            $cat_discount = $product->mainCategory->discount_percentage ?? 0.0;
        }

        return $product_discount + $cat_discount;
    }
}

if (!function_exists('getPlanTypeOrHigher')) {
    function getPlanTypeOrHigher($user_id = 0, $plan_type = 'deluxe'): bool {
        $plan_or_higher = false;
        $user_id = !empty($user_id) ? $user_id : CRUDBooster::myParentId();
        $session_var = 'has_' . $plan_type . '_or_higher_' . $user_id;

        if (checkTariffEligibility($user_id)) {
            if (Session::has($session_var)) {
                $plan_or_higher = Session::get($session_var);
            } else {
                $plan_ids = $plan_type === 'deluxe' ? [25, 26, 27] : ($plan_type === 'professional' ? [26, 27] : [27]);

                $plan_or_higher = DB::table('purchase_import_plans')
                    ->where([
                        ['cms_user_id', $user_id],
                        ['status', 1],
                        ['end_date', '>=', date('Y-m-d')],
                    ])
                    ->whereIn('import_plan_id', $plan_ids)
                    ->exists();

                Session::put($session_var, $plan_or_higher);
            }
        }
        return $plan_or_higher;
    }
}


// user who has deluxe or higher import plan
if (!function_exists('deluxeOrHigher')) {
    function deluxeOrHigher($user_id = 0): bool {
        return getPlanTypeOrHigher($user_id,'deluxe');
    }
}

if (!function_exists('professionalOrHigher')) {
    function professionalOrHigher($user_id = 0): bool {
        return getPlanTypeOrHigher($user_id,'professional');
    }
}

if (!function_exists('deluxeOrHigherPackId')) {
    function deluxeOrHigherPackId($user_id): string {
        $packId = 0;
        if (checkTariffEligibility($user_id)) {
            $planNames = [
                25 => 'deluxe',
                26 => 'professional',
                27 => 'enterprise',
            ];
            $packId = DB::table('purchase_import_plans')
                ->where([
                    ['cms_user_id', $user_id],
                    ['status', 1],
                    ['end_date', '>=', date('Y-m-d')],
                ])
                ->whereIn('import_plan_id', [25, 26, 27])
                ->value('import_plan_id');
        }
        return $planNames[$packId] ?? "";
    }
}

if(!function_exists('checkDtUser')){
    function checkDtUser($user_id){
        return Cache::rememberForever('check_is_dt_user_' . $user_id, function () use ($user_id){
            return DB::table('user_group_relations')
            ->where('user_id', $user_id)
            ->where('group_id', 2)
            ->exists();
        });
    }
}

if(!function_exists('checkDtEligibleUser'))
{
    function checkDtEligibleUser($user_id)
    {
        $is_dt_new_user = is_dt_user() && (checkTariffEligibility($user_id));

        // if(!(DB::table('dt_tariff_purchases')->where('user_id', $user_id)->exists())) return true;


        // $user_import_plan_id = DB::table('dt_tariff_purchases')->where('user_id', $user_id)->where('end_date', '>=', \Carbon\Carbon::now())->value('plan_id');

        $user_import_plan_status = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);

        $eligible_plan_ids = [29, 30, 31];
        $eligible_user = false;

        if(!$is_dt_new_user){

            $eligible_user = true;

        }else if($is_dt_new_user){

            if(in_array($user_import_plan_status['plan'], ['Free', 'Trial'])){

                $eligible_user = true;

            }else if($user_import_plan_status['plan'] == "500 Free Products"){

                $eligible_user = false;

            }else if(in_array($user_import_plan_status['plan'], ['Purchased', 'Assigned', 'Unlimited'])){

                if(in_array($user_import_plan_status['import_plan_id'], $eligible_plan_ids)){
                    $eligible_user = true;
                }

            }

        }

        return $eligible_user;
    }
}

function checkTariffEligibility($id){

    // if($id > config('global.greater_user_id') || DB::table('old_users_tariff')->where('user_id', $id)->exists()) return true;
    // else return false;

    $sessionKey = 'tariff_eligibility_result_' . $id;

    // Check if the session has a stored result for the current $id
    if (Session::has($sessionKey)) {
        // If the result is already in the session, return it
        return Session::get($sessionKey);
    }

    // If $id is the same as the current user's parent ID, set the result and return it
    if ($id == CRUDBooster::myParentId()) {
        $result = $id > config('global.greater_user_id') || DB::table('old_users_tariff')->where('user_id', $id)->exists();
        Session::put($sessionKey, $result);
        return $result;
    }

    // If $id is different, execute the query and set the result in the session
    $result = ($id > config('global.greater_user_id') || DB::table('old_users_tariff')->where('user_id', $id)->exists());

    Session::put($sessionKey, $result);
    return $result;
}

function checkOldUsersTarrif($id){

    $oldUserTrial = DB::table('old_users_tariff')->where('user_id',$id)->exists();

    if($oldUserTrial){
        return true;
    }else{
        return false;
    }
}

function getOldUsersTarrifIds(){
    $users = DB::table('old_users_tariff')->pluck('user_id')->toArray();
    return $users;
}

if(!function_exists('getCountryNameById'))
{
    function getCountryNameById($country_id)
    {
        return DB::table('countries')->where('id', $country_id)->value('name');
    }
}

// is special ids or key account user
if (!function_exists('isSuperOrKeyUser')) {
    function isSuperOrKeyUser() {
        $can_see_auto_deals = false;

        if (CRUDBooster::isKeyAccount() || in_array(CRUDBooster::myParentId(), [2455, 2439, 98, 71, 3351])) {
            $can_see_auto_deals = true;
        }

        return $can_see_auto_deals;
    }
}

if (!function_exists('isAutoDeal')) {
    function isAutoDeal($deal_source = null) {
        $is_auto_deal = false;

        if (in_array($deal_source, ['registration', 'calendar', 'contact', 'zapier'])) {
            $is_auto_deal = true;
        }

        return $is_auto_deal;
    }
}

if (!function_exists('autoDealOwnerId')) {
    function autoDealOwnerId($deal_id = null) {
        $user_id = CRUDBooster::myParentId();

        if (isSuperOrKeyUser()) {
            $user_id = \App\OfferDeal::withTrashed()->where('id', $deal_id)->value('user_id');
        }

        return $user_id;
    }
}

if (!function_exists('isNewPlanPurchasedUser')) {
    function isNewPlanPurchasedUser($user_id = null) {
        $user_id            = !is_null($user_id) ? $user_id : CRUDBooster::myParentId();

        $new_plan_purchased = false;
        $session_var        = 'new_plan_purchased_user_' . $user_id;

        if (Session::has($session_var) ) {
            $new_plan_purchased = Session::get($session_var);
        } else {
            if (is_dt_user()) {
                $new_plan_purchased = DB::table('dt_tariff_purchases')->where('user_id', $user_id)->exists();
            } else {
                $new_plan_purchased = DB::table('purchase_import_plans')->where('cms_user_id', $user_id)->exists();
            }

            Session::put($session_var, $new_plan_purchased);
        }

        return $new_plan_purchased;
    }
}

// if order is from sales pipeline, then no shop name. so make it DRM Sales Pipeline
if (!function_exists('retrieveOrderShopName')) {
    function retrieveOrderShopName($order_id_api, $order_id = 0) {
        $order_source ="DRM Manual";
        return $order_source;

        if (Str::startsWith($order_id_api, 'doff')) {
            $order_source ="DRM Sales Pipeline";
        } else if($order_id > 0) {
            $deal_source = \App\OfferDeal::where('offer_id', $order_id)->value('source'); // rejected offer -> already deleted deal
            if (!empty($deal_source)) {
                $order_source ="DRM Sales Pipeline";
            }

            // if deal_source empty means deal deleted, now have to check order status= offer_rejected etc.
        }

        return $order_source;
    }
}

// if order is from auto deal, then hide send email button
if (!function_exists('showEmailBtnOfOrder')) {
    function showEmailBtnOfOrder($order_id, $order_id_api) {
        $show_email_btn = true;
        return $show_email_btn;

        $deal_source = \App\OfferDeal::where('offer_id', $order_id)->value('source');
        if (isAutoDeal($deal_source)) {
            $show_email_btn = false;
        }

        return $show_email_btn;
    }
}

//Remove extra whitespace
if (! function_exists('removeExtraWhitespace')) {
    function removeExtraWhitespace($attribute)
    {
        $attribute = preg_replace('/\s+/', ' ', $attribute);

        return trim($attribute);
    }
}

// admin set user blocked channels
if (! function_exists('userBlockedChannels')) {
    function userBlockedChannels($user_id = 0, $country_id = 0)
    {
        $user_id = !empty($user_id) ? $user_id : CRUDBooster::myParentId();
        $country_id = !empty($country_id) ? $country_id : app('App\Services\UserService')->getProductCountry($user_id);

        $blocked_channels = \App\Models\BlockedChannel::where([
                'user_id' => $user_id,
                'country_id' => $country_id,
                'status' => 1,
            ])
            ->value('channel_ids');

       return !empty($blocked_channels) ? $blocked_channels : [];
    }
    /**
     * Check user has plan id access
     * @param int $userId, @param array planIds
     * @return success
     */
    if (!function_exists('CheckPurchesedPlanAccess')) {
        function CheckPurchesedPlanAccess($userId, $planIds = []){
            return DB::table('purchase_import_plans')->where('cms_user_id',$userId)->whereIn('import_plan_id',$planIds)->exists();
        }
    }
    /**
     * Check user has App id access
     * @param int $userId, @param array appIds
     * @return success
     */
    if (!function_exists('CheckPurchesedAppAccess')) {
        function CheckPurchesedAppAccess($userId, $appIds = []){
            $count = DB::table('app_assigns')
            ->where('user_id' ,$userId)
            ->whereIn('app_id', $appIds)
            ->where(function ($query) {
                $query->whereNull('end_date')->orWhereDate('end_date', '>=', now());
            })->count() +
            DB::table('purchase_apps')
            ->where('cms_user_id' ,$userId)
            ->whereIn('app_id', $appIds)
            ->whereDate('subscription_date_end', '>=', now())->count();

            return  $count > 0;
        }
    }

    if(!function_exists('userWiseUvpPriceCalculate')){
        function userWiseUvpPriceCalculate($user_id, $uvp_price)
        {
            $agreement = DB::table('mp_payment_agreements')
                ->where('user_id', '=', $user_id)
                ->where('type', '=', 1)
                ->select('price_markup')
                ->first();

            $price_markup = $agreement ? $agreement->price_markup : 0.0;
            return $uvp_price + (($price_markup * $uvp_price) / 100);
        }
    }


    /**
     * Get country code
     **/
    if(!function_exists('getCountryCode')){
        function getCountryCode($code)
        {
            $countryList = \Storage::disk('local')->get('country/country.json');
            $countryArr = json_decode($countryList, true);

            return collect($countryArr)
            ->filter(function($val, $key) use ($code) {
                return strtolower($key) == strtolower($code);
            })
            ->map(function($item) {
                return strtoupper($item);
            })
            ->first();
        }
    }


    /**
     * Auth user login
     */
    if(!function_exists('authUserLogin')){
        function authUserLogin($userId)
        {
            Auth::guard('web')->logout();
            session()->invalidate();
            session()->regenerateToken();
            return Auth::loginUsingId($userId, true);
        }
    }

    /**
     * Auth user logout
     */
    if(!function_exists('authUserLogout')){
        function authUserLogout()
        {
            Auth::guard('web')->logout();
            session()->invalidate();
            session()->regenerateToken();
        }
    }

    /**
     * Get user tariff name
     **/
    if(!function_exists('getUserTariffName')){
        function getUserTariffName($userId)
        {
            $import = new \App\Http\Controllers\AdminDrmImportsController;
            $plan = $import->importProductCheck($userId);
            if(isset($plan['import_plan_id']) && in_array($plan['import_plan_id'], [24, 25, 26, 27, 28, 29, 30, 31]))
            {
                $newPlanName = DB::table('import_plans')->where('id', $plan['import_plan_id'])->value('plan');
                $plan['plan'] = $plan['plan']. ' ('.$newPlanName.')';
            }

            return $plan['plan'] ?? null;
        }
    }

    function getAddressCoordinates(string $address)
    {
        $client = new \App\Services\Address\Transformer;
        return $client->addressCoordinates($address);
    }

    function addLineBreaksHtml($text)
    {
        // Define a regular expression pattern to match HTML tags
        $htmlTagsPattern = '/<[^>]+>/';

        // Replace HTML tags with a line break followed by the tag
        $formattedText = preg_replace($htmlTagsPattern, "\n$0", $text);

        return $formattedText;
    }

    /**
     * Get User Current Purchase plan
     * @param mixed $userId
     * @return string $planName
     */
    if(!function_exists('userCurrentPlan')){
        function userCurrentPlan($userId){
            $keyName = 'currentPlan'.$userId;
            if(Session::has($keyName)){
                return Session::get($keyName);
            }else{
                $isDT = checkDtUser($userId);
                if($isDT){
                    $dtTariff = DB::table('dt_tariff_purchases')
                                    ->where('user_id', $userId)
                                    ->where('end_date', '>=', \Carbon\Carbon::now())
                                    ->select('plan_id', 'start_date')
                                    ->first();

                    $planId = $dtTariff->plan_id ?? 0;
                    $planStartDate = $dtTariff->start_date;

                }else{
                    $drmTariff = DB::table('purchase_import_plans')
                            ->where([
                                ['cms_user_id', $userId],
                                ['status', 1],
                                ['end_date', '>=', date('Y-m-d')],
                            ])
                            ->select('import_plan_id', 'start_date')
                            ->first();

                    $planId = $drmTariff->import_plan_id ?? 0;
                    $planStartDate = $drmTariff->start_date;
                }

                $return = [
                    'id' => $planId,
                    'name' => $isDT? config('global.dt_tariff_plans')[$planId] : config('global.drm_tariff_plans')[$planId],
                    'isDT' => $isDT,
                    'planStartAt' => $planStartDate,
                    'limit' => !$isDT ? (array) DB::table('import_plans')->find($planId,['product_amount','mp_category','credit','no_of_online_channels']) : [
                        'product_amount' => 500,
                        'mp_category' => 10,
                        'credit' => 0,
                        'no_of_online_channels' => 1
                    ]

                ];

                Session::put($keyName, $return);
                return $return;
            }

        }
    }

    if(!function_exists('getMacAddress')){
        function getMacAddress()
        {
            $client_mac = exec('getmac');

            // Storing 'getmac' value in $MAC
            $client_mac = strtok($client_mac, ' ');

            return $client_mac;
        }
    }
}

if(!function_exists('job_exists')){
    function job_exists($jobId,$queueName,$searchData = array()): bool
    {
        $jobs = \Illuminate\Support\Facades\Redis::connection()->lrange('queues:' . $queueName, 0, -1);
        foreach ($jobs as $job) {
            $decodedJob = json_decode($job, true);

            // Check if the job matches the provided ID
            if ($decodedJob['id'] === $jobId) {
                // Check if the job data matches the provided data
                if (isset($decodedJob['data']['data'])) {
                    $jobData = json_decode($decodedJob['data']['data'], true);
                    if ($jobData === $searchData) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
}

if (!function_exists('appointmentContactFormInfo')) {
    function appointmentContactFormInfo($type_id, $user_id = null) {
        $form_info = DB::table('appointment_contact_forms')
            ->where([
                'user_id' => !empty($user_id) ? $user_id : CRUDBooster::myId(),
                'type_id' => $type_id,
            ])
            ->first();

        return $form_info;
    }
}

if(!function_exists('is_video_play_switch_checked')){
    function is_video_play_switch_checked(): bool
    {
        $user_selected_option = get_option('video_play_switch', 'video_play_switch', CRUDBooster::myParentId());
        if(isset($user_selected_option) && $user_selected_option->option_value === 'checked'){
            return true;
        }
        return false;
    }
}

if (!function_exists('calculateUserSalesAmount')) {
    function calculateUserSalesAmount($user_id = null)
    {
        $user_id = !empty($user_id) ? $user_id : (isPatrickSpecial() ? 4219 : CRUDBooster::myParentId());

        $sales_amount = DB::table('new_orders')
            ->join('cms_users as users', 'new_orders.cms_user_id', '=', 'users.id')
            ->whereNull('new_orders.deleted_at')
            ->where('new_orders.test_order', '!=', 1)
            ->whereNull('new_orders.offer_number')
            ->where('new_orders.credit_number', 0)
            ->where('new_orders.invoice_number', '!=', -1)
            ->where('users.id', $user_id)
            ->where('users.is_tester', '!=', 1)
            ->value(DB::raw('sum(total) as total_sum'));

        if ($user_id == 3984) {
            $sales_amount += 138593.02; // vitus had previous shop turnover 138593.02
        }

        return $sales_amount;
    }
}

if(!function_exists('purchaseImportPlansCheck')){
    function purchaseImportPlansCheck($user_id)
    {
        return DB::table('purchase_import_plans')->where('cms_user_id', $user_id)->first();
    }
}

if(!function_exists('isTrialUser')){
    function isTrialUser($user_id){
        $today = now();
        return DB::table('app_trials')
            ->where(['user_id' => $user_id, 'app_id' => 0])
            ->whereRaw("DATE_ADD(start_date, INTERVAL trial_days DAY) >= '$today'")
            ->exists();
    }
}

if(!function_exists('isEnterpriceOrTrialUser')){
    function isEnterpriceOrTrialUser($user_id)
    {
        $has_import_plan = purchaseImportPlansCheck($user_id);

        return ($has_import_plan && $has_import_plan->import_plan_id == 27 && $has_import_plan->end_date >= now())
            || (!$has_import_plan && isTrialUser($user_id));
    }
}

if (!function_exists('updateUserTopInfos')) {
    function updateUserTopInfos($user_id, $payload)
    {
        return true;
        if (! DB::table('user_top_infos')->where('user_id', $user_id)->exists()) {
            $payload['created_at'] = now();
        }

        $payload['updated_at'] = now();
        DB::table('user_top_infos')->updateOrInsert(
            ['user_id' => $user_id],
            $payload
        );
    }
}

if (!function_exists('updateUserAppsDate')) {
    function updateUserAppsDate($user_id, $app_id, $plan_id, $period_end) {
        if ($app_id == 42) {
            $payload = [
                'import_template_app_id' => $app_id,
                'import_template_plan_id' => $plan_id,
                'import_template_minutes' => DRM_Inverval_App_Minute($user_id, $app_id),
                'import_template_end_date' => $period_end,
            ];
        } else {
            $payload = [
                'csv_interval_app_id' => $app_id,
                'csv_interval_plan_id' => $plan_id,
                'csv_interval_minutes' => DRM_Inverval_App_Minute($user_id, $app_id),
                'csv_interval_end_date' => $period_end,
            ];
        }

        updateUserTopInfos($user_id, $payload);
    }
}


if (!function_exists('redirectToV2')) {
    function redirectToV2(string $to = '')
    {
        if(\DRM::hasV9Access())
        {
            $url = \DRM::redirectTov9FromV7($to);
            $resp = redirect()->away($url);
            \Session::driver()->save();
            $resp->send();
            exit;
        }
    }
}

if (! function_exists('hasAllRightsPermission')) {
    function hasAllRightsPermission($user_id = null)
    {
        if (!CRUDBooster::isSubUser()) {
            return true;
        }

        $user_id = !empty($user_id) ? $user_id : CRUDBooster::myId();
        $session_key = 'has_all_rights_permission_' . $user_id;

        if (Session::has($session_key)) {
            return Session::get($session_key);;
        }

        $hasAllRightsPermission = DB::table('sub_user_permissions')->where('cms_user_id', $user_id)
            ->where('feature_id', 53)->where('enable', 1)->exists();

        Session::put($session_key, $hasAllRightsPermission);

        return $hasAllRightsPermission;
    }
}

if (! function_exists('isPatrickSpecial')) {
    function isPatrickSpecial()
    {
        return (CRUDBooster::myId() == 4219) ? true : false;
    }
}
