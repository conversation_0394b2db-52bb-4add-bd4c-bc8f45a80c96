<?php

namespace App\Helper;

use App\Notifications\DRMNotification;
use App\Notifications\DRMTelegramNotification;
use App\Services\Modules\Export\Etsy\EtsyService;
use App\User;
use DB;
use Ebay;
use GuzzleHttp\Client;
use Illuminate\Support\Str;
use \DTS\eBaySDK\Constants;
use \DTS\eBaySDK\Trading\Enums;
use \DTS\eBaySDK\Trading\Services;
use \DTS\eBaySDK\Trading\Types;
use \Hkonnet\LaravelEbay\EbayServices;

class EtsyApi
{
    public $products_id, $shop_details, $lang, $user_id;

    function __construct($products_id, $shop_details, $lang, $user_id)
    {
        $this->products_id = $products_id;
        $this->shop_details = $shop_details;
        $this->lang = $lang;
        $this->user_id = $user_id;
    }

    public static function etsyDeleteProduct($shop, $item_ids)
    {
        $delete_item_ids = [];

        foreach ($item_ids as $item_id) {
            $ebay_service = new EtsyService();
            $service = $ebay_service->createTrading();
            $siteId = Constants\SiteIds::DE;
            $request = new Types\EndItemRequestType();
            $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
            $request->RequesterCredentials->eBayAuthToken = $shop->password;

            $request->ItemID = $item_id;
            $request->EndingReason = Enums\EndReasonCodeType::C_NOT_AVAILABLE;
            $response = $service->endItem($request);
            if ($response->Ack == 'Success') {
                $delete_item_ids[] = $item_id;
            }

        }
        DB::table('ebay_products')->whereIn('item_id',$delete_item_ids)->where('shop_id',$shop->id)->delete();

            // dd($response);
    }

}
