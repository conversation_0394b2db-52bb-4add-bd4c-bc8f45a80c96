<?php

namespace App\Helper;

use GuzzleHttp\Client;
use Illuminate\Support\Str;
use DB;
use Illuminate\Support\Collection;
use App\Notifications\DRMNotification;
use App\User;

class GambioCodeBackup {
    public function productCategoriesProcess($category)
    {
        $trans_category = 'category_name_' . $this->lang;
        foreach ($category as $value) {
            $category_name_translation = trim($value->$trans_category);
            
            if(!empty($category_name_translation)){
                if (preg_match("/( - |>)/", $category_name_translation)){
                    $category_items = array_map('trim', preg_split("/( - |>)/", trim($category_name_translation)));
                    
                    foreach ((array)$category_items as $key => $category_item_value) {
                        $category_info = self::gambioSearchCategories($this->shop_details, $category_item_value);
                        
                        if (empty($category_info)) {
                            sleep(1);
                            $category_info = self::gambioSearchCategories($this->shop_details, $category_item_value);
                            $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
                            $category_key_info = self::gambioSearchCategories($this->shop_details, $category_items[$key - 1]);
    
                            if (empty($category_info) && empty($category_first_info)) {
                                $this->postProductcategories($this->lang, trim($category_item_value), 0);
                            } elseif (empty($category_info) && !empty($category_key_info) && count((array)$category_key_info) > 1) {
                                
                                $category_info_collection = collect($category_key_info);
    
                                if (isset($category_first_info[0]['id']) && $category_info_collection->contains('parentId',$category_first_info[0]['id'])  && !$category_info_collection->contains('name',$category_item_value) || !$category_info_collection->contains('name',trim($category_item_value))) {
    
                                    $category_id = $category_info_collection->where('parentId', $category_first_info[0]['id'])->first();
    
                                    if (!empty($category_id)) {
                                        $this->postProductcategories($this->lang, trim($category_item_value), $category_id['id']);
                                    } elseif (empty($category_id)) {
                                        $category_second_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[$key - 2]));
                                        $category_second_info_id = $category_second_info->where('parentId', $category_first_info[0]['id'])->first();
                                        $category_id = $category_info_collection->where('parentId', $category_second_info_id['id'])->first();
                                        if (!empty($category_id)) {
                                            $this->postProductcategories($this->lang, trim($category_item_value), $category_id['id']);
                                        }
                                    }
    
                                }
    
                            } elseif (empty($category_info) && !empty($category_key_info)) {
    
                                $this->postProductcategories($this->lang, trim($category_item_value), $category_key_info[0]['id']);
                            }
    
                        } else {
    
                            $category_info_collection = collect($category_info);
    
                            if ($key == 1) {
                                $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
    
                                if(isset($category_first_info[0]['id']) && !$category_info_collection->contains('parentId',$category_first_info[0]['id'])){
    
                                    $this->postProductcategories($this->lang, trim($category_item_value), $category_first_info[0]['id']);
                                }
    
                            }elseif ($key == 2) {
                                $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
                                $category_second_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[1]));
                                $category_second_info_id = $category_second_info->where('parentId',$category_first_info[0]['id'])->first();
    
                                if(!empty($category_second_info_id) && !$category_info_collection->contains('parentId',$category_second_info_id['id'])){
    
                                    $this->postProductcategories($this->lang, trim($category_item_value), $category_second_info_id['id']);
                                }
    
                            } elseif ($key == 3) {
                                $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
                                $category_second_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[1]));
                                $category_third_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[2]));
                                $category_second_info_id = (object)$category_second_info->where('parentId',$category_first_info[0]['id'])->first();
                                $category_third_info_id = $category_third_info->where('parentId',$category_second_info_id->id)->first();
    
                                if(!empty($category_third_info_id) && !$category_info_collection->contains('parentId',$category_third_info_id['id'])){
    
                                    $this->postProductcategories($this->lang, trim($category_item_value), $category_third_info_id['id']);
                                }
    
                            } elseif ($key == 4) {
                                $category_first_info = self::gambioSearchCategories($this->shop_details, $category_items[0]);
                                $category_second_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[1]));
                                $category_third_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[2]));
                                $category_four_info = collect(self::gambioSearchCategories($this->shop_details, $category_items[3]));
                                $category_second_info_id = (object)$category_second_info->where('parentId',$category_first_info[0]['id'])->first();
                                $category_third_info_id = (object)$category_third_info->where('parentId',$category_second_info_id->id)->first();
                                $category_four_info_id = $category_four_info->where('parentId',$category_third_info_id->id)->first();
    
                                if(!empty($category_four_info_id) && !$category_info_collection->contains('parentId',$category_four_info_id['id'])){
    
                                    $this->postProductcategories($this->lang, trim($category_item_value), $category_four_info_id['id']);
                                }
    
                            }
    
                        }
                    }
                } else {
    
                    $category_info = self::gambioSearchCategories($this->shop_details, $category_name_translation);
    
                    if (empty($category_info)) {
    
                        sleep(1);
                        $category_info = self::gambioSearchCategories($this->shop_details, $category_name_translation);
                       
                        if (empty($category_info)) {
                            $this->postProductcategories($this->lang, $category_name_translation, 0);
                        }
                    }
                }
    
            }
    
        }
    
    }


    public function productLinkWithCategories($category)
    {
        $trans_category = 'category_name_' . $this->lang;
        foreach ($category as $value) {
            $category_name_trans = $value->$trans_category;
            if (!empty($category_name_trans)) {

                if (preg_match("/( - |>)/", $category_name_trans)) {
                    $category_collection = new Collection(array_map('trim', preg_split("/( - |>)/", trim($category_name_trans))));

                    $category_info = self::gambioSearchCategories($this->shop_details, $category_collection->last());
                    if (empty($category_info) && is_array($category_info)) {
                        sleep(1);
                        $category_info = self::gambioSearchCategories($this->shop_details, $category_collection->last());
                    }


                    if (count((array)$category_info)>1) {
                        $category_new_info = collect(self::gambioSearchCategories($this->shop_details, $category_collection->get($category_collection->count() - 2)));
                        $category_key_info = self::gambioSearchCategories($this->shop_details, $category_collection->get(0));

                        if (empty($category_new_info) && is_array($category_new_info)) {
                            sleep(1);
                            $category_new_info = collect(self::gambioSearchCategories($this->shop_details, $category_collection->get($category_collection->count() - 2)));
                        }

                        $category_info_list = collect($category_info);

                        $category_new_info_id = (object)$category_new_info->where('parentId',$category_key_info[0]['id'])->first();

                        $find_category_info = $category_info_list->where('parentId',$category_new_info_id->id)->first();

                        if (empty($find_category_info)) {

                            $find_category_info = $category_info_list->where('parentId',$category_key_info[0]['id'])->first();
                            if(empty($find_category_info)){
                                $find_category_info = $category_info_list->where('parentId',$category_new_info[0]['id'])->first();
                                $category_id[] = $find_category_info['id'];
                            } elseif (!empty($find_category_info)) {
                                $category_id[] = $find_category_info['id'];
                            }


                        } elseif (!empty($find_category_info)) {
                            $category_id[] = $find_category_info['id'];
                        }

                    } elseif (count((array)$category_info) == 1) {
                        $category_id[] = $category_info[0]['id'];
                    }
                } else {
                    $category_info = self::gambioSearchCategories($this->shop_details, $category_name_trans);

                    if (empty($category_info) && is_array($category_info)) {
                        sleep(1);
                        $category_info = self::gambioSearchCategories($this->shop_details, $category_name_trans);
                    }

                    if (count((array)$category_info) == 1) {
                        $category_id[] =$category_info[0]['id'];
                    } elseif (count((array)$category_info) > 1) {

                        $category_info_list = collect($category_info);
                        $find_category_info = $category_info_list->where('parentId',0)->first();

                        if (!empty($find_category_info)) {
                            $category_id[] = $find_category_info['id'];
                        }

                    }
                }
            }

        }
        return $category_id;

    }
    
} 
