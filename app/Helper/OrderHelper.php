<?php

namespace App\Helper;

use PDF;
use App\NewOrder;
use Exception;
use App\OrderStatusGroup;
use App\NewCustomer;
use App\MonthlyPaywall;
use App\DeliveryCompany;
use App\User;
use Illuminate\Support\Facades\Cache;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\HandlingTimeEmailHistory;
use App\Jobs\UpdateProductRq;
use App\Models\ChannelProduct;
use App\OrderTrackings;
use App\DropfunnelCustomerTag;
use App\Notifications\DRMNotification;
use Carbon\Carbon;
use App\Services\Order\MpOrderPlace;
use App\Traits\InvoiceNumber;
use Illuminate\Support\Facades\Http;

class OrderHelper
{

    use InvoiceNumber;

    static public function callerOfTrigger($status, $order, $options = [])
    {
        try {

            $return_value = false;
            $obj = new OrderHelper();

            static::sendOrderStatusToDt($status, $order);

            $statusGroup = static::drmStatus($status);
            if ($statusGroup && $statusGroup->trigger_name) {
                $trigger_name = strtolower($statusGroup->trigger_name);
                if (method_exists($obj, $trigger_name)) {
                    $call = call_user_func('\App\Helper\OrderHelper' . '::' . $trigger_name, $order, $status, $options);
                    Cache::forget('order_statt_' . $order->cms_user_id);
                    Cache::forget('best_8_products_' . $order->cms_user_id);
                    Cache::forget('user_inkasso_invoice_list_' . $order->cms_client);
                    return $call;
                }
            }

            return static::defaultStatusAction($order, $status, $options);
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public static function sendOrderStatusToDt($status, $order) {
        $dt_order_id = null;
        $o_a_pid = $order->order_id_api;
        if ($o_a_pid && str_starts_with($o_a_pid, 'drop_t')) {
            $dt_order_id = end(explode('_', $o_a_pid));
        }

        if ($dt_order_id) {
            $shop = \App\Shop::where('status', 1)->find($order->shop_id);
            $shop_url = rtrim($shop->url, '/').'/api/v1/update-order-status';

            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'userToken' => $shop->username,
                'userPassToken' => $shop->password,
            ])->post($shop_url, [
                'dt_order_id' => $dt_order_id,
                'status' => $status,
                'provider_name' => $order->parcel_id,
                'tracking_number' => $order->package_number,
            ]);
    
            if ($response->ok()) {
                return $response->json();
            }
        }
    }

    //Return group status
    public static function drmStatus($status)
    {
        return OrderStatusGroup::has('statuses')->whereNotNull('group_name')->where('group_name', 'LIKE', '%' . $status . '%')->orWhereHas('statuses', function ($q) use ($status) {
            $q->where('status_name', 'LIKE', $status);
        })->select('id', 'group_name', 'trigger_name', 'group_color')->first();
    }

    //Get all order status data
    public static function statusData()
    {
        return Cache::rememberForever('drm_order_statuses', function () {
            return OrderStatusGroup::with('statuses')->select('id', 'group_name', 'trigger_name', 'group_color')->get();
        });
    }

    static public function create_credit_note($order, $status, $options = [])
    {
        try {
            $check['cms_user_id'] = $order->cms_user_id;
            $check['insert_type'] = $order->insert_type;
            $check['shop_id'] = $order->shop_id;
            $check['order_id_api'] = $order_id_api = 'inv_id_' . $order->id;
            $check['drm_customer_id'] = $order->drm_customer_id;

            $row['order_date'] = date('Y-m-d H:i:s');

            $row['total'] = -1 * abs($order->total);
            $row['sub_total'] = -1 * abs($order->sub_total);
            $row['total_tax'] = -1 * abs($order->total_tax);
            $row['discount'] = -1 * abs($order->discount);

            $row['discount_type'] = $order->discount_type;
            $row['adjustment'] = $order->adjustment;
            $row['payment_type'] = null;
            $row['payment_date'] = null;
            $row['currency'] = $order->currency;
            $row['shipping_cost'] = -1 * abs($order->shipping_cost);
            $row['customer_info'] = $order->customer_info;
            $row['billing'] = $order->billing;
            $row['shipping'] = $order->shipping;
            $row['client_note'] = $order->client_note;
            $row['status'] = 'Canceled';
            $row['cms_client'] = $order->cms_client;
            $row['marketplace_paid_status'] = $order->marketplace_paid_status;
            $row['currency_rate'] = $order->currency_rate;
            $row['eur_total'] = -1 * abs($order->eur_total);
            $row['tax_exclude'] = $order->tax_exclude;
            $row['inv_pattern'] = $order->inv_pattern;
            $row['tax_rate'] = $order->tax_rate;

            $row['order_info'] = $order->order_info;

            $row['vat_number'] = $order->vat_number;
            $row['tax_version'] = $order->tax_version;
            $row['tax_number'] = $order->tax_number;

            if (!empty($order->transfer_date)) {
                $row['transfer_date'] = $order->transfer_date;
            }

            $carts = json_decode($order->cart);
            $new_carts = [];

            if ($carts) {
                foreach ($carts as $cart) {
                    $cart->rate = -1 * abs($cart->rate);
                    $cart->amount = -1 * abs($cart->amount);
                    $cart->product_discount = -1 * abs($cart->product_discount);

                    if (isset($cart->service_price)){
                        $cart->service_price = -1 * abs($cart->service_price);
                    }

                    $new_carts[] = $cart;
                }
            }

            $row['cart'] = json_encode($new_carts);

            if (DB::table('new_orders')->where($check)->count()) {
                throw new Exception("Credit note already exist.");
            }

            if (DB::table('new_orders')->where(['order_id_api' => $order_id_api])->count() > 0) {
                throw new Exception("Credit note already exist.");
            }

            $row['invoice_number'] = $order->invoice_number;
            $row['credit_number'] = self::generateCreditNumber($order->cms_user_id);

            if(isset($options['refund_amount']) && $options['refund_amount'] && (int)$options['refund_amount'] != (int)$order->eur_total)
            {
                $refund_order = app(\App\Services\Order\PartialAmountCredit::class)->getRefundOrder($order, $options['refund_amount']);

                $row['total'] = $refund_order['total'];
                $row['sub_total'] = $refund_order['sub_total'];
                $row['total_tax'] = $refund_order['total_tax'];
                $row['discount'] = $refund_order['discount'];
                $row['shipping_cost'] = $refund_order['shipping_cost'];
                $row['eur_total'] = $refund_order['eur_total'];
                $row['currency'] = 'EUR';
                $row['currency_rate'] = 1;
                $row['cart'] = json_encode($refund_order['cart']);
            }

            $row['dropmatix_sub_total'] = -1 * abs($order->dropmatix_sub_total);
            $row['dropmatix_total_tax'] = -1 * abs($order->dropmatix_total_tax);
            $row['dropmatix_discount'] = -1 * abs($order->dropmatix_discount);
            $row['dropmatix_shipping_cost'] = -1 * abs($order->dropmatix_shipping_cost);
            $row['dropmatix_tax_rate'] = $order->dropmatix_tax_rate;

            // STORE_ORDER_ON_DATABASE
            $credit_order = NewOrder::updateOrCreate($check, $row);

            if ($credit_order) {

                if(isset($options['refund_amount']) && $options['refund_amount']) {
                    DB::table('new_orders')->where('id', $order->id)->update(['credit_ref' => $credit_order->id]);
                }else {
                    DB::table('new_orders')->where('id', $order->id)->update(['credit_ref' => $credit_order->id, 'status' => 'credit_note_created']);
                }

                updateOrderHistory($credit_order, 'canceled', 'Credit note created from Order ID: ' . $order->id);

                $order = NewOrder::where('id', '=', $order->id)->first();
                updateOrderHistory($order, $order->status, 'Credit note created successfully! Credit number: ' . $credit_order->credit_number);

                //Order cancel on API
                \App\Helper\OrderHelper::sendCancelRequestToApi($order->id);

                #regenerate rq
                // UpdateProductRq::dispatch($carts, $order->cms_user_id, $order->shop_id);


                $hasOrder = NewCustomer::whereHas('orders', function($q){
                    $q->where('new_orders.invoice_number', '!=', -1)
                    ->where('new_orders.test_order', '!=', 1)
                    ->where('new_orders.credit_number', '=', 0)
                    ->whereNull('new_orders.credit_ref')
                    ->where('new_orders.eur_total', '>', 0);
                })
                ->where('id', $order->drm_customer_id)
                ->exists();

                if(!$hasOrder) {
                    DropfunnelCustomerTag::insertLeads($order->cms_user_id, $order->drm_customer_id, 1);
                }

                static::sendOrderStatusMail($credit_order->id);

                //Cancel marketplace order
                if(in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID]) && !empty($order->marketplace_order_ref))
                {
                    app(\App\Services\Order\MpOrderCancel::class)->cancel($order->marketplace_order_ref, $order->cms_client);
                }

                if(CRUDBooster::isSupplier()){
                    app(\App\Services\Order\MpOrderCancel::class)->mpSupplierOrderCancel($order->dropmatix_order_id, $order->cms_client);
                }

                return [
                    'success' => true,
                    'message' => 'Credit note created successfully!'
                ];
            } else {
                throw new Exception('Credit note created failed!');
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    // /**
    //  * On order status change to cancle
    //  * Loop through all products and update RQ
    //  */
    // public static function udpate_product_rq($carts){
    //     foreach ($carts as $key => $cart) {
    //         $cart = (object) $cart;
    //         $total_prod_order = NewOrder::whereJsonContains('cart', ['ean' => $cart->ean])->get()->count();
    //         $total_cancled_order = NewOrder::where('credit_number', '>', 0)
    //                                 ->whereJsonContains('cart', ['ean' => $cart->ean])
    //                                 ->get()->count();

    //         $rq = 0;
    //         if($total_prod_order && $total_cancled_order){
    //             $rq = round( ($total_cancled_order * 100) / $total_prod_order, 2 );
    //         }
    //         $product = ChannelProduct::where([
    //             'ean' => $cart->ean
    //         ]);
    //         $product->update(['rq' => $rq]);
    //     }
    // }

    //Order placed
    public static function order_placed($order, $status, $options = [])
    {
        try {

            $new_val = 'order_placed';
            $supplier_id = $options['supplier_id'];
            if ($supplier_id) {

                //Dropmatix carnot supplied replace with Wohlauf carnot supplier
                $supplier_id = (int) $supplier_id === 14383 ? 13040 : $supplier_id;

                $supplier = DeliveryCompany::find($supplier_id);
                if (is_null($supplier)) throw new Exception('Invalid supplier!');
                $supplier_name = $supplier->name;
                $supplier_user_id = (int)$supplier_id === 13040 ? $supplier->user_id : $order->cms_user_id;

                $product_list = (isset($options['product_list']) && $options['product_list']) ? $options['product_list'] : json_decode($order->cart);

                $data = [];
                $data['page_title'] = 'Delivery Note';
                $data['order'] = $order;
                $data['product_list'] = $product_list;
                $data['customer'] = $customer = $order->customer;


                $data['setting'] = $invoice_data = app('App\Http\Controllers\AdminDrmAllOrdersController')->getDelveriNoteTemplate($order, $supplier_user_id);
//              $data['setting'] = $invoice_data = DB::table('delivery_note_settings')->where('cms_user_id', $supplier_user_id)->orderBy('id', 'desc')->first();

                if($order->marketplace_order_ref && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                  $data['shipping_details'] = DB::table("new_orders")
                    ->where('new_orders.id', $order->marketplace_order_ref)
                    ->select("new_orders.id as mp_order_id", "new_orders.shipping")
                    ->first();
                }else if($order->credit_number && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
                    $marketplace_credit = DB::table('new_orders')->where('credit_ref', '=', $order->id)->value('marketplace_order_ref');

                    if($marketplace_credit->marketplace_order_ref)
                    {
                        $data['shipping_details'] = DB::table("new_orders")
                      ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
                      ->select("new_orders.id as mp_order_id", "new_customers.*")
                      ->where('new_orders.id', $marketplace_credit->marketplace_order_ref)->first();
                    }
                }
                //Marketplace credit end

                $pdf_stream = PDF::loadView('admin.invoice.delivery_note', $data)->setWarnings(false)->stream();

                $html_stream = view('admin.invoice.delivery_note', $data)->render();
                $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

                if(isset($data['shipping_details'])) {
                  $shipping = '<p>' . formatBillingAddress($data['shipping_details']->shipping) . '</p>';
                }else{
                  $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $logo = ($invoice_data->logo) ? $invoice_data->logo : '';

                $tags = [
                    'customer_name' => $customer->full_name,
                    'company_name' => $customer->company_name,
                    'billing_address' => $billing,
                    'shipping_address' => $shipping,
                    'order_items' => view('admin.new_order.email_supplier_order_items', compact('product_list', 'order'))->render(),
                    'order_items_n_article' => view('admin.new_order.email_supplier_order_items_n_article', compact('product_list', 'order'))->render(),
                    'order_date' => \Carbon\Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                    'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data->store_name . '" >',
                    'order_number' => $order->id,
                    'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                    'contact_name' => $supplier_name,
                    'pay_url' => null,
                    'PAYWALL' => false,
                    'IS_DROPSHIPPING'   => true,
                    'IS_FULFILLMENT'    => false,
                ];

                // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                    $tags['credit_note'] = $order->credit_number;

                    $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                    if($email_signatures){
                        foreach($email_signatures as $key => $signature){
                            $tags['drm-sign-'.$key] = $signature;
                        }
                    }
                // }

                if(strpos($order->order_id_api, "mfull_")){
                    $tags['IS_DROPSHIPPING'] = false;
                    $tags['IS_FULFILLMENT'] = true;
                    $tags['shipping_address'] = 'XXXXXXXXXX';
                }

                $label_urls = [];
                $trackings = OrderTrackings::where('order_id', '=', $order->id)->where('provider', '=', OrderTrackings::SHIPCLOUD_TRACKING)->get();
                if (!empty($trackings)) {
                    foreach ($trackings as $tracking) {
                        $shipment_data = $tracking->shipment_data ?? [];
                        $label_url = $shipment_data['label_url'] ?? null;
                        if (!empty($label_url)) {
                            $label_urls[] = $label_url;
                        }
                    }
                }

                // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])){

                    $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                    $channel_supplier_mail = DB::table('drm_supplier_mail')
                    ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                    ->exists();

                    if($channel_supplier_mail){
                        $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id, $channel);
                    }else{
                        $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id);
                    }

                // }else{
                // $template = DRMParseSupplierEmailTemplate($tags, $supplier_user_id);
                // }

                $data['email_to'] = $email_to = $supplier->order_email ?? $supplier->email;
                $data['email_from'] = $template['senderEmail'];
                $data['subject'] = $template['subject'];
                $data['bcc'] = $template['bcc'];

                if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                    throw new Exception("Something Wrong! Email Not Sent!.");
                }

                // $note_name = 'delivery_note_' . $supplier_name . '_' . $order->invoice_number;
                // $note_name = preg_replace('/\s+/', '_', $note_name);
                // $note_name = preg_replace('/[_]+/', '_', $note_name);
                $note_name = $order->id . '.pdf';

                app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_supplier', $template, function ($messages) use ($data, $pdf_stream, $label_urls, $note_name) {
                    // $messages->from($data['email_from']);
                    $messages->to($data['email_to']);
                    $messages->subject($data['subject']);

                    if ($data['bcc']) {
                        $messages->bcc($data['bcc']);
                    }

                    $messages->attachData($pdf_stream, $note_name, [
                        'mime' => 'application/pdf',
                    ]);

                    if (!empty($label_urls)) {
                        foreach ($label_urls as $label_url) {
                            $messages->attach($label_url);
                        }
                    }
                });

                $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';

                // STORE_ORDER_ON_DATABASE
                $order->update(['supplier_id' => $supplier_id, 'supplier_time' => now(), 'supplier' => 'checked', 'status' => $new_val]);

                $log_data = [];
                $log_data['product_list'] = $product_list;
                $log_data['tags'] = $tags;
                $log_data['order_id'] = $order->id;

                $log_message = 'Order placed Successfully! Email: ' . $email_to . ' (' . $supplier_name . ')' . $is_bcc;


                $supplier->logs()->create([
                    'type' => 1,
                    'data' => $log_data,
                    'message' => $log_message,
                ]);

                updateOrderHistory($order, $new_val, $log_message);

                //send FTP file
                try {
                  if((int) $supplier_id === 13040)
                  {
                    app(\App\Services\FTP\OrderFTPService::class)->sendOrders([$order->id], $supplier_user_id);
                  }
                }catch(\Exception $ex){}

                return [
                    'success' => true,
                    'message' => 'Order placed Successfully! Email: ' . $email_to
                ];
            } else {
                throw new Exception("Supplier can not be empty!");
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Order placed failed! Error: ' . $e->getMessage()
            ];
        }
    }

    static function service_block($order, $status, $options = [])
    {

        return static::serviceBlockAction($order, $status, $options = []);
    }

    static function remainder_start($order, $status, $options = [])
    {
        $log_message = null;

        try {
            // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])){

                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                $channel_remainder_mail = DB::table('remainder_email_settings')
                ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                ->exists();

                if($channel_remainder_mail){
                    $res = app('App\Http\Controllers\AdminDrmAllOrdersController')->send_remainder_email($order->id, $channel);
                }else{
                    $res = app('App\Http\Controllers\AdminDrmAllOrdersController')->send_remainder_email($order->id);
                }

            // }else{
            // $res = app('App\Http\Controllers\AdminDrmAllOrdersController')->send_remainder_email($order->id);
            // }

            if (isset($res['success'])) {
                if ($res['success'] == true) {
                    $log_message = $res['message'];
                    return [
                        'success' => true,
                        'message' => $log_message
                    ];
                } else {
                    throw new Exception($res['message']);
                }
            }

            throw new Exception('Remainder email not send!');

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    static function create_test_order($order, $status, $options = [])
    {

        $old_val = $order->status;
        $status = 'test_order';
        $log_message = 'Test order created successfully!';

        try {

            if (!(CRUDBooster::isSuperadmin() || (CRUDBooster::myParentId() == 98))) {
                if (userTestOrderCount() >= userTestOrderLimit()) throw new Exception("Test Order limit finished!");
            }

            $order_data = NewOrder::where('id', $order->id)->where('cms_user_id', CRUDBooster::myParentId())->where('invoice_number', '!=', -1)->first();
            if ($order_data && ($order_data->test_order != 1)) {
                if ($order_data->update(['test_order' => 1, 'status' => $status])) {
                    Cache::forget('test_order_count_' . $order_data->cms_user_id);
                    updateOrderHistory($order_data, $status, $log_message);

                    $hasOrder = NewCustomer::whereHas('orders', function($q){
                        $q->where('new_orders.invoice_number', '!=', -1)
                        ->where('new_orders.test_order', '!=', 1)
                        ->where('new_orders.credit_number', '=', 0)
                        ->whereNull('new_orders.credit_ref')
                        ->where('new_orders.eur_total', '>', 0);
                    })
                    ->where('id', $order->drm_customer_id)
                    ->exists();

                    if(!$hasOrder) {
                        DropfunnelCustomerTag::insertLeads($order->cms_user_id, $order->drm_customer_id, 1);
                    }

                    static::sendOrderStatusMail($order->id);

                    static::makeMpOrderTestStatus($order);

                    return [
                        'success' => true,
                        'message' => $log_message
                    ];
                }
            }

            throw new Exception('Status update failed.');

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    private static function makeMpOrderTestStatus($order_data)
    {
        $has_marketplace_ref = !empty($order_data->marketplace_order_ref) && in_array($order_data->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID]);
        if($has_marketplace_ref)
        {
            $order = NewOrder::where('id', $order_data->marketplace_order_ref)
            ->where('cms_user_id', in_array($order_data->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID]))
            ->where('test_order', '<>', 1)
            ->first();

            $status = 'test_order';
            $log_message = 'Test order created successfully!';


            // STORE_ORDER_STATUS_ON_DATABASE
            if($order && $order->update(['test_order' => 1, 'status' => $status]))
            {
                Cache::forget('test_order_count_' . $order->cms_user_id);
                updateOrderHistory($order, $status, $log_message);

                $hasOrder = NewCustomer::whereHas('orders', function($q){
                    $q->where('new_orders.invoice_number', '!=', -1)
                    ->where('new_orders.test_order', '!=', 1)
                    ->where('new_orders.credit_number', '=', 0)
                    ->whereNull('new_orders.credit_ref')
                    ->where('new_orders.eur_total', '>', 0);
                })
                ->where('id', $order->drm_customer_id)
                ->exists();

                if(!$hasOrder) {
                    DropfunnelCustomerTag::insertLeads($order->cms_user_id, $order->drm_customer_id, 1);
                }

                static::sendOrderStatusMail($order->id);
            }
        }
    }

    static function mark_as_paid($order, $status, $options = [])
    {
        try {

            $old_val = $order->status;
            $status = 'paid';
            $message = $options['message'] ?? '';
            $intend_id = isset($options['intend_id']) ? $options['intend_id'] : null;
            $log_message = 'Status changed from ' . drmHistoryLabel($old_val) . ' to ' . drmHistoryLabel($status) . ' successfully!'.$message;

            $payment_type = $options['payment_type'] ?? ($order->payment_type ?? 'DRM');
            $payment_date = $options['payment_date'] ?? ($order->payment_date ?? date('Y-m-d H:i:s'));

            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['status' => $status, 'intend_id' => $intend_id, 'payment_type' => $payment_type, 'payment_date' => $payment_date])) {

                //Mp order place order
                app(MpOrderPlace::class)->send($order->id);

                //Transfer order to dropmatix
                $paid_extra_action = static::paidAction($order->id);

                $log_message .= $paid_extra_action;
                updateOrderHistory($order, $status, $log_message);
                static::sendOrderStatusMail($order->id);
                $extra_message = $options['message'] ?? '';
                return [
                    'success' => true,
                    'message' => 'Status updated successfully !' . $paid_extra_action.$extra_message
                ];
            }

            throw new Exception('Status update failed.');

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    static function mark_as_shipped($order, $status, $options = [])
    {
        try {

            $old_val = $order->status;
            $status = 'Shipped';
            $log_message = 'Status changed from ' . drmHistoryLabel($old_val) . ' to ' . drmHistoryLabel($status) . ' successfully!';

            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['status' => $status])) {
                updateOrderHistory($order, $status, $log_message);
                static::sendOrderStatusMail($order->id);
                return [
                    'success' => true,
                    'message' => $log_message
                ];
            }

            throw new Exception('Status update failed.');

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }


    //Default action
    public static function defaultStatusAction($order, $status, $options = [], $deal = null, $misc_params = [])
    {
        try {
            $auto_deal        = (!empty($deal) && isAutoDeal($deal->source)) ? true : false;
            $generate_invoice = $misc_params['generate_invoice'] ?? true;

            $old_val       = $order->status;
            $log_message   = 'Status changed from ' . drmHistoryLabel($old_val) . ' to ' . drmHistoryLabel($status) . ' successfully!';
            $extra_message = $options['message'] ?? '';

            if (!empty($deal) && (strtolower($status) == $old_val)) {
                throw new \Exception('Offer document already transferred successfully!');
            }

            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['status' => $status])) {

                if(strtolower($status) == 'paid'){

                    // STORE_ORDER_STATUS_ON_DATABASE
                    $order->update(['payment_type' => 'DRM', 'payment_date' => now()]);
                    $log_message .= static::paidAction($order->id);
                }

                else if((strtolower($status) == 'offer_accepted') && $generate_invoice){
                    $inserted = app('\App\Http\Controllers\AdminDrmAllOrdersController')->transferToInvoice($order->id, $deal, $misc_params);
                    if($inserted) {
                        $options['message'] = " Transferred to invoice successfully!";
                    }else {
                        throw new Exception('Failed to accept this offer!');
                    }
                }

                else if(strtolower($status) == 'offer_rejected'){
                    $extra_message = !empty($extra_message)? " Reason: $extra_message" : '';
                }
                //'offer_sent', 'offer_accepted', 'offer_rejected'

                // Remove offer deal tags
                if(in_array(strtolower($status), ['offer_accepted', 'offer_rejected'])) {
                    app('\App\Http\Controllers\AdminPipelineController')->removeOfferDealTags($order->id);
                }

                app('App\Http\Controllers\AdminDrmAllCustomersController')->lockUnlockCustomer($order->cms_client,$status);

                $log_message .= $extra_message;
                updateOrderHistory($order, $status, $log_message, $options);

                if (!$auto_deal) {
                    static::sendOrderStatusMail($order->id, $options);
                }

                if (in_array(strtolower($status), ['retoure eingegangen', 'retoure_eingegangen'])) {
                    UpdateProductRq::dispatch(json_decode($order->cart), $order->cms_user_id, $order->shop_id);
                }

                return [
                    'success' => true,
                    'message' => $log_message
                ];
            }

            throw new Exception('Status update failed.');
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    //Send handling time
    public static function send_handling_email($order, $status, $options = [])
    {
        try {
            if (empty($order)) throw new Exception('Invalid order item');

            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = ($invoice_data['setting'] && $invoice_data['setting']->logo) ? $invoice_data['setting']->logo : '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => \Carbon\Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = $order->credit_number;

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])){

                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                $channel_handling_time_mail = DB::table('marketing_email_settings')
                ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                ->exists();

                if($channel_handling_time_mail){
                    $template = DRMParseHandlingTimeEmailTemplate($tags, $order->cms_user_id, $channel);
                }else{
                    $template = DRMParseHandlingTimeEmailTemplate($tags, $order->cms_user_id);
                }

            // }else{
            // $template = DRMParseHandlingTimeEmailTemplate($tags, $order->cms_user_id);
            // }

            $data['email_to'] = $invoice_data['customer']->email;
            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $data['subject'] = $template['subject'];
            $data['bcc'] = $template['bcc'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }
            });

            // STORE_ORDER_STATUS_ON_DATABASE
            $order->update(['status' => $status]);

            $history = new HandlingTimeEmailHistory();
            $history->user_id = $order->cms_user_id;
            $history->customer_id = $order->drm_customer_id;
            $history->order_id = $order->id;
            $history->save();

            try {
                $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
                $message_text = 'Handling time email sent to ' . $data['email_to'] . '' . $is_bcc . '. Status change to ' . drmHistoryLabel($status);
                updateOrderHistory($order, 'handlingtime_email', $message_text);
                return [
                    'success' => true,
                    'message' => $message_text
                ];
            } catch (Exception $ex) {
                throw new Exception($ex->getMessage());
            }

        } catch (Exception $exception) {
            return [
                'success' => true,
                'message' => $exception->getMessage()
            ];
        }
    }

    //Service block action
    public static function serviceBlockAction($order, $status, $options = [])
    {
        //DB::beginTransaction();
        try {

            $old_val = $order->status;
            $log_message = 'Status changed from ' . drmHistoryLabel($old_val) . ' to ' . drmHistoryLabel($status) . ' successfully!';
            $aditional_log = '';

            //service block
            $customer = NewCustomer::find($order->drm_customer_id);
            if ($customer && $order->feature_type == 1) {

                $app = \DB::table('app_stores')->where('id', $order->feature_id)->select('id', 'menu_name')->first();
                $assign_app = \DB::table('app_assigns')->where('user_id', $customer->cc_user_id)->where('app_id', $order->feature_id)->select('id')->first(); //assign app id

                if ($assign_app) {
                    //Delete assign app
                    \DB::table('app_assigns')->where('id', $assign_app->id)->delete();
                    $feature_name = ($app->menu_name) ? $app->menu_name : 'App';
                    $aditional_log .= $feature_name . ' blocked successfully!';
                }

            } elseif ($customer && $order->feature_type == 2) {

                $import_plan = DB::table('import_plans')->where('id', $order->feature_id)->select('id', 'plan')->first();
                $feature_name = ($import_plan->plan) ? $import_plan->plan : 'Import Plan';

                $manual_tariff = \DB::table('manual_import_tarrif')->where('user_id', $customer->cc_user_id)->select('id', 'import_amount')->first(); //assign plan id
                if ($manual_tariff) {
                    \DB::table('manual_import_tarrif')->where('id', $manual_tariff->id)->delete();
                    $aditional_log .= ' Manual assigned import tariff blocked successfully';
                }
            }

            //paywall block
            if ($order->insert_type == 4) {
                $paywall = MonthlyPaywall::has('order')->whereNull('paid_at')->where('order_id', $order->id)->first();
                if ($paywall) {
                    $paywall->update(['status' => 'over_due']);
                    \DB::table('cms_users')->where('id', $paywall->user_id)->update(['status' => null]);
                    $aditional_log .= ' Paywall user blocked!';
                }
            }

            $aditional_log = trim($aditional_log);
            $log_message = trim($log_message . ' ' . $aditional_log);

            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['status' => $status])) {
                updateOrderHistory($order, $status, $log_message);
                app('App\Http\Controllers\AdminDrmAllCustomersController')->lockUnlockCustomer($order->cms_client,$status);

                static::sendOrderStatusMail($order->id);
                return [
                    'success' => true,
                    'message' => $log_message
                ];
            }

            throw new Exception('Status update failed.');
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }


    //paid action
    static public function paidAction($id)
    {

        //Update marketplace order paid status
        $db_order = DB::table('new_orders')->where('id', $id)
           ->select('id', 'cms_user_id', 'marketplace_order_ref', 'marketplace_paid_status as m_status', 'cms_client')->first();

           // User account lock status update
           $account_status = 'paid';
           app('App\Http\Controllers\AdminDrmAllCustomersController')->lockUnlockCustomer($db_order->cms_client, $account_status);

           if(!empty($db_order) && $db_order->m_status != 1 && in_array($db_order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID]) && !empty($db_order->marketplace_order_ref)){
            DB::table('new_orders')->whereNotNull('marketplace_order_ref')->where(function($mo) use ($id) {
                $mo->where('id', $id)->orWhere('marketplace_order_ref', $id);
            })
            ->update(['marketplace_paid_status' => 1]);

            //Update pending auto payment status
            DB::table('stripe_pending_auto_payments')->where('item_id', $id)->update(['status' => 1]);

            //Send order to internel
            $m = app('\App\Services\Marketplace\InternelSyncService')->transferOrderToInternel($id);
            if(is_array($m)) {
                return ' '.$m['message'];
            }
            return " ";
        }

        //check if it relates to paywall invoice
        $paywall = MonthlyPaywall::has('order')->whereNull('paid_at')->where('order_id', $id)->first();
        if ($paywall) {
            $order = $paywall->order;
            $total = $order->total;
            $update_data = [];
            $update_data['payment_id'] = 'mnl_' . $paywall->id . '_' . date('YmdHis');
            $update_data['total'] = $total;
            $update_data['sub_total'] = $total;
            $update_data['paid_at'] = now();
            $update_data['status'] = 'paid';
            $update_data = array_filter($update_data);

            $block_message = '';
            if(DB::table('cms_users')->where('id', $order->cms_client)->whereNull('status')->exists()){
               $block_message = (DB::table('cms_users')->where('id', $order->cms_client)->update(['status' => 'Active'])) ? ' User unblocked!' : ' User unbloking failed! Please unblock manually!';
            }

            //Update pending pauto ayment status
            DB::table('stripe_pending_auto_payments')->where('item_id', $id)->update(['status' => 1]);

            $status_message = ($paywall->update($update_data)) ? ' Paywall status also changed successfully.' : ' Paywall status change failed.';
            return $block_message . $status_message;
        }
        return '';
    }

    public function updateHistory($original_order, $updated_order)
    {
        $changes = collect($original_order->toArray())->diff($updated_order->toArray());
        foreach ($changes as $change_key => $value) {
            switch ($change_key) {
                case 'order_date':
                    updateOrderHistory($updated_order, 'edit_order', 'Order date changed from ' . substr($original_order->order_date, 0, 10) . ' to ' . substr($updated_order->order_date, 0, 10));
                    break;
                case 'client_note':
                    updateOrderHistory($updated_order, 'edit_order', "Client note updated successfully to \"" . $updated_order->client_note . "\"");
                    break;
                case 'drm_customer_id':
                    updateOrderHistory($updated_order, 'edit_order', "Customer changed.");
                    break;
                case 'cart':
                    updateOrderHistory($updated_order, 'edit_order', "Cart Product info updated.");
                    break;
                case 'total_tax':
                    updateOrderHistory($updated_order, 'edit_order', "Total tax updated to " . number_format((float)$updated_order->total_tax, 2, ',', '.'));
                    break;
                case 'total':
                    updateOrderHistory($updated_order, 'edit_order', "Total price updated to " . number_format((float)$updated_order->total, 2, ',', '.'));
                    break;
                case 'shipping_cost':
                    updateOrderHistory($updated_order, 'edit_order', "Shipping cost updated to " . number_format((float)$updated_order->shipping_cost, 2, ',', '.'));
                    break;
            }
        }
    }


    public function delete_if_invoice_watermark_exists($setting, $prefix = 'invoice_')
    {
        $watermark_photo = $setting->watermark_photo;
        if(empty($watermark_photo)) return;

        $search = '.com/';
        $file = substr($watermark_photo, strpos($watermark_photo, $search) + strlen($search));

        if (Storage::disk('spaces')->exists($file)) {
            Storage::disk('spaces')->delete($file);
        }
    }

    public function cloneSpacesUrl($url)
    {
        if(empty($url)) return;
        $search = '.com/';
        $file = substr($url, strpos($url, $search) + strlen($search));

        if (Storage::disk('spaces')->exists($file)) {

            $rand = strtolower(\Str::random(6));
            $rand1 = strtolower(\Str::random(6));
            $new_file = pathinfo($file, PATHINFO_DIRNAME).'/clone_'.$rand1.time().$rand.'.'.pathinfo($file, PATHINFO_EXTENSION);

            Storage::disk('spaces')->put($new_file, Storage::disk('spaces')->get($file), 'public');
            return Storage::disk('spaces')->url($new_file);
        }
    }


    //Send status email
    public static function sendOrderStatusMail($order_id, $options = [])
    {
        // //DB::beginTransaction();
        try {
            //Check validity
            if (empty($order_id)) return false;

            usleep(5);
            $order = NewOrder::with('customer')->where('id', $order_id)->first();
            if (empty($order)) return false;

            $order_status = $order->status;
            if (empty($order_status)) return false;

            //Check user has active status mail
            $user_id = $order->cms_user_id;

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])) ){
                // $combine_status = ['Erstattet', 'credit_note_created', 'refund_initiated', 'refund_completed'];
                $combine_status = config('order_status.combine_status');

                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                if(in_array($order_status, $combine_status)){

                    if(CRUDBooster::myParentId() > 2990){
                        $channel_combine_status_mail = DB::table('combine_status_email_settings')
                        ->where(['cms_user_id' => $user_id, 'channel' => $channel])
                        ->exists();

                        if($channel_combine_status_mail){
                            $combine_status_template = getCombineStatusMailTemplate($user_id, $channel);
                        }else{
                            $combine_status_template = getCombineStatusMailTemplate($user_id);
                        }

                        if (empty($combine_status_template) || ($combine_status_template->auto_mail != 1)) return false;
                    }else{
                        $channel_order_status_mail = DB::table('order_mail_templates')
                        ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                        ->exists();

                        if($channel_order_status_mail){
                            $status_template = getStatusMailTemplate($user_id, $order_status, $channel);
                        }else{
                            $status_template = getStatusMailTemplate($user_id, $order_status);
                        }

                        if (empty($status_template) || ($status_template->auto_mail != 1)) return false;
                    }

                }else if($order_status == 'payment_in_progress'){
                    $channel_payment_progress_mail = DB::table('payment_progress_email_settings')
                    ->where(['cms_user_id' => $user_id, 'channel' => $channel])
                    ->exists();

                    if($channel_payment_progress_mail){
                        $status_template = findSystemMail('payment_progress_email_settings', $user_id, $channel);
                    }else{
                        $status_template = findSystemMail('payment_progress_email_settings', $user_id);
                    }

                    if (empty($status_template) || ($status_template->auto_mail != 1)) return false;
                }else{

                    $channel_order_status_mail = DB::table('order_mail_templates')
                    ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                    ->exists();

                    if($channel_order_status_mail){
                        $status_template = getStatusMailTemplate($user_id, $order_status, $channel);
                    }else{
                        $status_template = getStatusMailTemplate($user_id, $order_status);
                    }

                    if (empty($status_template) || ($status_template->auto_mail != 1)) return false;

                }

            // }else{
            // $status_template = getStatusMailTemplate($user_id, $order_status);
            // if (empty($status_template) || ($status_template->auto_mail != 1)) return false;
            // }
            //validity check end

            $is_charge_email = false;
            $pay_url = null;
            $paywall = null;

            $data = [];
            $invoice_data = [];
            $invoice_data['page_title'] = 'Order mail';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = $product_list = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            if($order->marketplace_order_ref && in_array($order->cms_user_id, [User::DROPMATIX_ACCOUNT_ID, User::LAYAN_ACCOUNT_ID])) {
              $invoice_data['shipping_details'] = DB::table("new_orders")
              ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
              ->select("new_orders.id as mp_order_id", "new_customers.*")
              ->where('new_orders.id', $order->marketplace_order_ref)->first();
            }

            $pdf_view = '';
            $pdf_name = '';
            //Select blade file
            if ($order->credit_number > 0) {
                $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.credit_note.daily' : 'admin.credit_note.general';
                $pdf_view = ($order->insert_type == 4) ? 'admin.credit_note.charge_inv' : $pdf_view;

                $pdf_name = 'credit_' . $order->credit_number;
            } else {
                $pdf_view = (in_array($order->cms_user_id, User::FABIANS_ID)) ? 'admin.invoice.daily' : 'admin.invoice.general';
                $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;
                $pdf_name = 'invoice_' . $order->invoice_number;
            }

            $customer_name = $order->customer ? $order->customer->full_name : '';
            $note_name = $pdf_name . '_' . $customer_name;
            $note_name = preg_replace('/\s+/', '_', $note_name);
            $note_name = preg_replace('/[_]+/', '_', $note_name);
            $note_name = strtolower($note_name . '.pdf');

            //Send payment link
            // if ($order->insert_type == 4) {
            //     $paywall = MonthlyPaywall::where('order_id', $order->id)->whereNull('paid_at')->first();
            //     if ($paywall) {
            //         $is_charge_email = true;
            //         $pay_url = route('user-paywall-payment', $paywall->id);
            //     }
            // } elseif (($order->cms_user_id == 98) && ($order->total > 0)) {
            //     $due_status = ['nicht_bezahlt'];
            //     if (in_array($order->status, $due_status)) {
            //         $is_charge_email = true;
            //         $pay_url = route('user-invoice-payment', $order->id);
            //     }
            // }

            //Email content
            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';
            $logo = $invoice_data['setting']->logo ?? '';

            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => \Carbon\Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = $order->credit_number;

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            // (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) &&
            if( ($order_status == 'Shipped') ){

                $tags['package_number'] = '';
                $tags['parcel_service'] = '';

                $tracking = OrderTrackings::with('parcel')->where('order_id', '=', $order->id)->latest()->first();
                if($tracking && $tracking->id)
                {
                    $parcel_name = ($tracking->parcel) ? $tracking->parcel->parcel_name : '';
                    $package_number = $tracking->package_number;

                    if (strtolower($parcel_name) == 'dhl') {
                        $package_number = '<a href="https://www.dhl.de/de/privatkunden/pakete-empfangen/verfolgen.html?piececode=' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'dpd') {
                        $package_number = '<a href="https://www.dpd.com/de/de/empfangen/sendungsverfolgung-und-live-tracking" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'hermes') {
                        $package_number = '<a href="https://www.myhermes.de/empfangen/sendungsverfolgung" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'ups') {
                        $package_number = '<a href="https://www.ups.com/track?loc=de_DE&requester=ST&trackNums=' . $package_number . '/trackdetails" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'tnt') {
                        $package_number = '<a href="https://www.tnt.com/express/de_de/site/shipping-tools/tracking.html?searchType=con&cons=' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'gls') {
                        $package_number = '<a href="https://www.gls-pakete.de/sendungsverfolgung?trackingNumber=' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'dachser') {
                        $package_number = '<a href="https://elogistics.dachser.com/shp2s/?search=' . $package_number . '&javalocale=de_DE" target="_blank">' . $package_number . '</a>';
                    }else if (strtolower($parcel_name) == 'swisspost') {
                        $package_number = '<a href="https://service.post.ch/ekp-web/ui/entry/search/' . $package_number . '" target="_blank">' . $package_number . '</a>';
                    }

                    $tags['package_number'] = $package_number;
                    $tags['parcel_service'] = $parcel_name;
                }
            }

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592, 2991])) ){
                // $combine_status = ['Erstattet', 'credit_note_created', 'refund_initiated', 'refund_completed'];
                $combine_status = config('order_status.combine_status');

                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                if(in_array($order_status, $combine_status)){

                    if(CRUDBooster::myParentId() > 2990){
                        $channel_combine_status_mail = DB::table('combine_status_email_settings')
                        ->where(['cms_user_id' => $user_id, 'channel' => $channel])
                        ->exists();

                        if($channel_combine_status_mail){
                            $template = DRMParseOrderCombineStatusEmailTemplate($tags, $user_id, $channel);
                        }else{
                            $template = DRMParseOrderCombineStatusEmailTemplate($tags, $user_id);
                        }
                    }else{
                        $channel_order_status_mail = DB::table('order_mail_templates')
                        ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                        ->exists();

                        if($channel_order_status_mail){
                            //Get appropriate template
                            $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status, $channel);
                        }else{
                            //Get appropriate template
                            $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status);
                        }
                    }

                }else if($order_status == 'payment_in_progress'){
                    $channel_payment_progress_mail = DB::table('payment_progress_email_settings')
                    ->where(['cms_user_id' => $user_id, 'channel' => $channel])
                    ->exists();

                    if($channel_payment_progress_mail){
                        $template = DRMParsePaymentProgressEmailTemplate($tags, $order->cms_user_id, $channel);
                    }else{
                        $template = DRMParsePaymentProgressEmailTemplate($tags, $order->cms_user_id);
                    }
                }else{

                    $channel_order_status_mail = DB::table('order_mail_templates')
                    ->where(['user_id' => $user_id, 'channel' => $channel, 'order_status' => $order_status])
                    ->exists();

                    if($channel_order_status_mail){
                        //Get appropriate template
                        $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status, $channel);
                    }else{
                        //Get appropriate template
                        $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status);
                    }
                }

            // }else{
            // //Get appropriate template
            // $template = DRMParseOrderStatusEmailTemplate($tags, $user_id, $order_status);
            // }

            $data['email_to'] = $email_to = $invoice_data['customer']->email;
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];
            $data['subject'] = $template['subject'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $is_send_invoice = $template['send_invoice'] ?? 0;

            // TODO:: DROPMATIX
            $pdf_stream = $is_send_invoice ? (new \App\Services\Order\InvoiceDoc($order->id))->output() : '';


            //Send mail
            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $is_send_invoice, $note_name, $options) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                //Send invoice
                if ($is_send_invoice) {
                    $messages->attachData($pdf_stream, $note_name, [
                        'mime' => 'application/pdf',
                    ]);
                }

                if( isset($options['files']) && !empty($options['files']) )
                {
                    foreach($options['files'] as $file_url)
                    {
                        $messages->attach($file_url);
                    }
                }

                //BCC
                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

            });

            // if ($paywall) {
            //     MonthlyPaywall::whereNull('paid_at')->whereNull('start_at')->where('id', $paywall->id)->update(['start_at' => now()->addDays(7)]);
            // }
            // //DB::commit();

            try {
                $is_bcc = ($data['bcc']) ? ' BCC: ' . $data['bcc'] : '';
                $message_text = ucfirst(drmHistoryLabel($order_status)) . ' - Email sent to: ' . $email_to . '' . $is_bcc;
                updateOrderHistory($order, 'send_email_' . $order_status, $message_text);
            } catch (Exception $ee) {
            }

            return true;
        } catch (Exception $e) {

            try {
                User::find(71)->notify(new DRMNotification('Send customer invoice error ' . $e->getMessage() . ' Line:' . $e->getLine(), '', '#'));
            } catch(Exception $ex) {}

            return false;
        }
    }


    //Send Tracking number to API
    public static function sendTrackingNumberToAPI($tracking_id, $channel)
    {
        try {

            if(!in_array($channel, [2, 4, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39])) return;

            $tracking = OrderTrackings::with('parcel:id,parcel_name')
            ->select('order_id', 'package_number', 'parcel_id')
            ->where('id', '=', $tracking_id)
            ->first();

            if(empty($tracking) || empty($tracking->parcel)) return;

            $order_id = $tracking->order_id;
            $payload = [
                "parcel_name" => $tracking->parcel->parcel_name,
                "tracking_number" => $tracking->package_number,
                "token" => "token345rt",
            ];

            $api_url = env('BACKGROUND_BASE_URL').'/api/order-tracking-send-to-api/'.$order_id;

            $curl = curl_init();
            curl_setopt_array($curl, array(
              CURLOPT_URL => $api_url,
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_ENCODING => '',
              CURLOPT_MAXREDIRS => 10,
              CURLOPT_TIMEOUT => 0,
              CURLOPT_FOLLOWLOCATION => true,
              CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
              CURLOPT_CUSTOMREQUEST => 'POST',
              CURLOPT_POSTFIELDS => json_encode($payload),
              CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
              ),
            ));

            $response = curl_exec($curl);
            curl_close($curl);

        }catch(Exception $e){}
    }


    // Cancel order to API
    public static function sendCancelRequestToApi($order_id)
    {
        $payload = [
            "token" => "token345rt",
        ];

        $api_url = env('BACKGROUND_BASE_URL').'/api/order-cancel-send-to-api/'.$order_id;
        $curl = curl_init();
        curl_setopt_array($curl, array(
          CURLOPT_URL => $api_url,
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS => json_encode($payload),
          CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
          ),
        ));

        curl_exec($curl);
        curl_close($curl);
    }

    //MP Order shipping failed
    public static function order_shipping_failed($order, $status, $options = [])
    {
        $status = 'zustellproblem';
        $message = $options['message'] ?? 'Order shipping failed';

        // STORE_ORDER_STATUS_ON_DATABASE
        $order->update(['status' => $status]);
        updateOrderHistory($order, $status, $message);
        (new \App\Services\Notification\PushNotifyDeliveryProblem)->send($order, $message); // Send push notification
        static::sendOrderStatusMail($order->id);


        if(!empty($options['not_for_client'])) return;

        //Marketplace ref order
        if ($order->marketplace_order_ref) {
            $api_array = ['BIGBUY API', 'BIKE API', 'B2BUHREN API', 'VIDAXL API', 'BINO MERTENS API', 'BTS WHOLESALE API', 'FLORA LOGISTICS API'];

            foreach ($api_array as $api) {
                if (stripos($message, $api) !== false) {
                    $message = str_ireplace($api, '', $message);
                    break;
                }
            }
            $referencedOrder = NewOrder::find($order->marketplace_order_ref);

            // STORE_ORDER_STATUS_ON_DATABASE
            $referencedOrder->update(['status' => $status]);
            updateOrderHistory($referencedOrder, $status, $message);
            (new \App\Services\Notification\PushNotifyDeliveryProblem)->send($referencedOrder, $message); // Send push notification
            static::sendOrderStatusMail($referencedOrder->id);
        }
    }

    // Marketplace order return
    public static function mp_return($order, $status, $options = [])
    {
        try {
            $old_val = $order->status;
            $log_message = 'Status changed from ' . drmHistoryLabel($old_val) . ' to ' . drmHistoryLabel($status) . ' successfully!';

            if(isset($options['message']))
            {
                $log_message .= ' '.$options['message'];
            }

            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['status' => $status])) {
                updateOrderHistory($order, $status, $log_message);
                app(\App\Services\Order\ReturnLabel::class)->mpReturnMail($order->id);

                return [
                    'success' => true,
                    'message' => $log_message
                ];
            }

            throw new Exception('Status update failed.');

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    // Marketplace order shipped
    public static function return_shipped($order, $status, $options = [])
    {
        try {
            $old_val = $order->status;
            $log_message = 'Status changed from ' . drmHistoryLabel($old_val) . ' to ' . drmHistoryLabel($status) . ' successfully!';

            if(isset($options['message']))
            {
                $log_message .= ' '.$options['message'];
            }

            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['status' => $status])) {
                updateOrderHistory($order, $status, $log_message);
                app(\App\Services\Order\ReturnLabel::class)->mpReturnShippedMail($order->id);

                return [
                    'success' => true,
                    'message' => $log_message
                ];
            }

            throw new Exception('Status update failed.');

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    // Marketplace order received
    public static function return_received($order, $status, $options = [])
    {
        try {
            $old_val = $order->status;
            $log_message = 'Status changed from ' . drmHistoryLabel($old_val) . ' to ' . drmHistoryLabel($status) . ' successfully!';

            if(isset($options['message']))
            {
                $log_message .= ' '.$options['message'];
            }

            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['status' => $status])) {
                updateOrderHistory($order, $status, $log_message);
                app(\App\Services\Order\ReturnLabel::class)->mpReturnReceivedMail($order->id);

                return [
                    'success' => true,
                    'message' => $log_message
                ];
            }

            throw new Exception('Status update failed.');

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    // Marketplace Order reject
    public static function mp_rejected($order, $status, $options = [])
    {
        try {
            $old_val = $order->status;
            $log_message = 'Status changed from ' . drmHistoryLabel($old_val) . ' to ' . drmHistoryLabel($status) . ' successfully!';

            if(isset($options['message']))
            {
                $log_message .= ' '.$options['message'];
            }

            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['status' => $status])) {
                updateOrderHistory($order, $status, $log_message);
                app(\App\Services\Order\MPOrderMail::class)->rejectMail($order->id);
                return [
                    'success' => true,
                    'message' => $log_message
                ];
            }

            throw new Exception('Status update failed.');

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }


    // Marketplace stock unavailable
    public static function stock_unavailable($order, $status, $options = [])
    {
        try {
            $old_val = $order->status;
            $log_message = 'Status changed from ' . drmHistoryLabel($old_val) . ' to ' . drmHistoryLabel($status) . ' successfully!';

            if(isset($options['message']))
            {
                $log_message .= ' '.$options['message'];
            }

            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['status' => $status])) {
                updateOrderHistory($order, $status, $log_message);
                app(\App\Services\Order\MPOrderMail::class)->stockUnavailablel($order->id);
                return [
                    'success' => true,
                    'message' => $log_message
                ];
            }

            throw new Exception('Status update failed.');

        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    public static function checkOrderEmailTemplatesByChannel($user_id, $status, $channel)
    {
        return DB::table('order_mail_templates')
        ->where(['user_id' => $user_id, 'order_status' => $status, 'channel' => $channel])
        ->exists();
    }
}
