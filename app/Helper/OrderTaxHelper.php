<?php

namespace App\Helper;
use App\NewOrder;
use DB;

class OrderTaxHelper
{
	protected $tax_rate = 19; //Default tax
	protected $total = 0;

	private $order = null;

	private $country_id = null;
	private $is_german = false;
	private $small_business = false;

	private $insert_type = null;
	private $user_id = null;

	private $tax_number = null;

	//Initialize order id
	public function __construct($order_id){
    	// $order = NewOrder::with(['customer', 'invoice_setting'])->where('id', $order_id)->first();



$order = NewOrder::with(['customer' => function($query) {
    // return $query->select(['id', 'country', 'vat_number']);
},'invoice_setting' => function($invq) {

	dd($invq);
    return $invq->select(['id', 'small_business']);
}



])->where('id', $order_id)->first();

dd($order);









    	if($order){
    		$this->order = $order;

    		$customer_country = null;
    		if($order->customer){
    			$customer =  $order->customer;
    			$customer_country = $order->customer->country;
    			$this->tax_number = $customer->vat_number;
    		}




			$small_business = ($order->invoice_setting) ? $order->invoice_setting->small_business : null;
			if($small_business == 1){
				$this->small_business = true;
			}

			$order_date = strtotime($order->order_date);
			$seven_july = strtotime("07/01/2020");
			$january_first = strtotime("01/01/2021");

			$this->insert_type = $order->insert_type;
			$this->user_id = $order->cms_user_id;

			$country_tax = $this->countryRate($customer_country);
			$this->country_id = $country_tax['country_id'];
			$this->is_german = (is_null($customer_country) || ($customer_country == '') || ($this->country_id == 4)) ? true : false;
			$this->tax_rate = ($this->is_german && (($order_date >= $seven_july) && ($order_date < $january_first)) )? 16 : $country_tax['tax'];

			//Daily account tax
			if(($this->user_id == 98) && !in_array($this->insert_type, [4, 6])){
				$this->tax_rate = 21;
			}

			//Calculate manual tax
			if($this->insert_type == 6){
				$this->tax_rate = manualTaxCalculation($this->order);
			}

			//German small business tax
			if(!in_array($this->insert_type, [4, 6])){ //Without Manual & paywall invoice
				if($this->is_german && $this->small_business){
					$this->tax_rate = 0;
				}
			}

			$this->total = (float)$order->total;
    	}
    }

    //Tax.php country by country name
    private function countryRate($country){
      $tax_rate = ($this->user_id == 98)? 21 : 19;
      $country_id = null;
      if($country){
        $tax_rate_data = $this->countryTax($country);
        if($tax_rate_data){
          $tax_rate = (float)$tax_rate_data->charge;
          $country_id = $tax_rate_data->id;
        }
      }
      return ['tax' => $tax_rate, 'country_id' => $country_id];
    }

    //country tax
    public function countryTax($country){
    	return DB::table('tax_rates')->select('id', 'charge')->where('country_code', 'like', $country)->orWhere('country', 'like', $country)
          ->orWhere('country_de', 'like', $country)->first();
    }


    //is small business
    public function isGermanSmallBusiness(){
    	return $this->small_business;
    }

    //get tax
    public function tax(){
    	return $this->tax_rate;
    }

    //Get total
    public function total(){
    	return $this->total;
    }

    //country_id
    public function country_id(){
    	return $this->country_id;
    }

    //is_german
    public function is_german(){
    	return $this->is_german;
    }

    //Param amount, rate
	public function orderTaxForNet(){
		$amount = $this->tax_rate;
		$tax_rate = $this->total;
		return $amount/(100+ $tax_rate)* $tax_rate; // Tax.php
	}

  	//Param amount, rate
	public function orderNetForNet(){
		$amount = $this->tax_rate;
		$tax_rate = $this->total;
		return $amount/(100+ $tax_rate)* 100;
	}

	public function arrData(){
		return [
			'tax_rate' 		=> $this->tax_rate,
			'total' 		=> $this->total,
			'country_id' 	=> $this->country_id,
			'is_german' 	=> $this->is_german,
			'small_business'=> $this->small_business,
			'user_id' 		=> $this->user_id,
			'tax_number' 	=> $this->tax_number,
		];
	}
}
