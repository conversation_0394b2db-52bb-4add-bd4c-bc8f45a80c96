<?php

namespace App\Helper;

use DB;
use Illuminate\Support\Arr;
use App\Services\Notification\Notification;

class ShopifyApi
{
    public $products_id, $auth, $shop_details, $lang, $user_id;

    function __construct($products_id, $shop_details, $lang, $user_id)
    {
        $this->products_id = $products_id;
        $this->shop_details = $shop_details;
        $this->lang = $lang;
        $this->user_id = $user_id;
        $this->auth = base64_encode("$shop_details->username:$shop_details->password");
    }



    public function drmProduct()
    {
        if (count((array) $this->products_id) > 0) {

            $new_products = [];

            $table = "drm_translation_" .$this->lang;

            foreach ($this->products_id as $i => $id) {

                $product = DB::table($table)
                                ->join('drm_products', 'drm_products.id', '=', $table . '.product_id')
                                ->where('drm_products.user_id', $this->user_id)
                                ->whereNull('drm_products.deleted_at')
                                ->select('drm_products.*', 'drm_products.description as descriptionProduct', 'drm_products.name as product_category_nameroduct', 'drm_products.id as product_id', $table . '.description', $table . '.title as name')
                                ->where('drm_products.id', $id)->first();


                $category = DB::table('drm_products')->join('drm_product_categories', 'drm_product_categories.product_id', '=', 'drm_products.id')
                                                    ->join('drm_category', 'drm_category.id', '=', 'drm_product_categories.category_id')
                                                    ->select('drm_category.*', 'drm_products.ean')
                                                    ->where('drm_products.id', $id)->get();

                $product_price = DB::table('drm_product_shop_prices')->where(['drm_product_id' => $id, 'shop_id' => $this->shop_details->id])->first();

                $trans_category = 'category_name_' . $this->lang;

                $variables_tag = ["#number", "#ean", "#ek_price", "#vk_price", "#weight", "#size", "#color", "#note", "#production_year", "#brand", "#materials", "#tags","#status", "#gender"];

                foreach ($variables_tag as $key => $variables_tag_value) {
                   if($variables_tag_value == "#number"){$variables_tag_info[$key] = $product->item_number;}
                   if($variables_tag_value == "#ean"){$variables_tag_info[$key] = $product->ean;}
                   if($variables_tag_value == "#ek_price"){$variables_tag_info[$key] = $product->ek_price;}
                   if($variables_tag_value == "#vk_price"){$variables_tag_info[$key] = $product->vk_price;}
                   if($variables_tag_value == "#weight"){$variables_tag_info[$key] = deNumberFormatterAuto($product->item_weight);}
                   if($variables_tag_value == "#size"){$variables_tag_info[$key] = $product->item_size;}
                   if($variables_tag_value == "#color"){$variables_tag_info[$key] = $product->item_color;}
                   if($variables_tag_value == "#note"){$variables_tag_info[$key] = $product->note;}
                   if($variables_tag_value == "#production_year"){$variables_tag_info[$key] = $product->production_year;}
                   if($variables_tag_value == "#brand"){$variables_tag_info[$key] = $product->brand;}
                   if($variables_tag_value == "#materials"){$variables_tag_info[$key] = $product->materials;}
                   if($variables_tag_value == "#tags"){$variables_tag_info[$key] = $product->tags;}
                   if($variables_tag_value == "#status"){$variables_tag_info[$key] = $product->status;}
                   if($variables_tag_value == "#gender"){$variables_tag_info[$key] = $product->gender;}
                }

                $getProductCategoryName = $this->getProductCategories();
                $getProductCategory = [];
                foreach ((object)$getProductCategoryName as $valuea) {
                    foreach ($valuea as  $value) {
                        $getProductCategory[] = $value->title;
                    }
                }
                //dd($getProductCategory);
                foreach($category as $value) {
                    $category_name_translation = $value->$trans_category;
                    if (preg_match("/( - |>)/", $category_name_translation)) {
                        $arrayOfItems = preg_split("/( - |>)/", trim($category_name_translation));
                        $arrayOfItems = array_map('trim',$arrayOfItems);
                        foreach ($arrayOfItems as $item) {
                            if (!in_array($item, $getProductCategory)) {
                                if (next($arrayOfItems) != $item) {
                                    $this->postProductcategories(trim($item));
                                }
                            }
                        }
                    }else{
                        if (!in_array($category_name_translation, $getProductCategory)) {
                            $this->postProductcategories($category_name_translation);
                        }
                    }
                }

                $getProductCategoryTitle = $this->getProductCategories();
                foreach ((object) $getProductCategoryTitle as $value) {
                    foreach ($value as $key => $cat) {
                        $getProductCategoryTitleId[] = $cat;
                    }
                }
                //dd($getProductCategoryTitleId);
                foreach ($category as $value) {
                    $category_name_trans = $value->category_name ?: $value->$trans_category;
                    if (preg_match("/( - |>)/", $category_name_trans)) {
                        $arrayOfItems = preg_split("/( - |>)/", trim($category_name_trans));
                        foreach ($arrayOfItems as $itemInfo) {
                            foreach ((object) $getProductCategoryTitleId as  $valueInfoCategory) {
                                if (trim($itemInfo) == $valueInfoCategory->title) {
                                    $data['collection_id'] = $valueInfoCategory->id;
                                }
                            }
                        }

                    } else {
                        foreach ((object) $getProductCategoryTitleId as $valueInfo) {
                            if ($category_name_trans == $valueInfo->title) {
                                $data['collection_id'] = $valueInfo->id;
                            }
                        }
                    }
                }

                $images = json_decode($product->image);
                if (is_array($images)) {
                    $explode_images = (isset($images[0])) ? $explode_images = getImageArray($images) : "";

                    foreach ($explode_images as $value) {
                        if (preg_match('/\.(jpeg|jpg|png|gif)$/i', $value)) {
                            $final_images[] = array('src' => url($value));
                        }
                    }
                }


                $data['id'] = $product->ean;
                $data['title'] = trim(str_replace($variables_tag, $variables_tag_info, $product->name));
                $data['body_html'] = trim(str_replace($variables_tag, $variables_tag_info, $product->description));
                $data['published'] = true;

                // $weight = deNumberFormatterAuto($product->item_weight) ?? 0;

                $weight = (float)$product->item_weight;

                // if($weight == 0.0){
                //     $notify = new Notification('ShopifyExportError',$this->user_id);
                //     $notify->map("Invalid weight value for -".$product->ean,"https://drm.software/admin/drm_products/detail/".$product->id);
                // }

                $data['variants'] = array([
                    'barcode' => $product->ean,
                    'price' => $product_price->vk_price??$product->vk_price,
                    'sku' => $product->item_number,
                    'weight' => $weight,
                    'inventory_quantity' => (int)$product->stock,
                ]);
                // $data['options'] = array(
                //     'name' => 'color',
                //     'values' => $product->item_color
                // );
                // $data['options'] = [
                //     array(
                //         "name"=> "Color",
                //         "values"=> ["Blue","Black"]
                //     ),
                //     array(
                //         "name" => "Size",
                //         "values" => ["155","159"]
                //     )
                // ];

                $data['tags'] = str_replace('#', '', $product->tags);
                $data['product_type'] = $product->brand;
                $data['vendor'] = $product->brand;
                $data['images'] = $final_images;

                $new_products = array('product' => $data);
                $final_images = NULL;
                $exist = DB::table('shopify_products')->where(['ean' => $new_products['product']['id'], 'shop_id' => $this->shop_details->id])->first();

                if(count(array_filter($new_products)) > 0){
                    if($exist){
                        $this->updateExportProduct($exist->product_id,$new_products);
                    }else{
                        $this->newProductExport($new_products);
                    }
                }
            }


        }
    }

    public function newProductExport($new_products)
    {
        $url = $this->shop_details->url . "admin/api/2020-01/products.json";
        $curl_header = array("accept: application/json", "authorization: Basic " . $this->auth, "content-type: application/json");
        $alldata = json_encode($new_products);
        $response = $this->culrRequestExec($url, $curl_header, $alldata, 'POST');
        $insertProducts = json_decode($response, true);
        $this->postProductCategoryLink($insertProducts['product']['id'], $new_products['product']['collection_id']);

        $row['user_id'] = $this->user_id;
        $row['shop_id'] = $this->shop_details->id;
        $row['product_id'] = $insertProducts['product']['id'];
        $row['ean'] = $new_products['product']['id'];
        $shopify_products = DB::table('shopify_products')->where(['ean' => $new_products['product']['id'], 'shop_id' => $this->shop_details->id])->first();
        if($shopify_products){
            $row['updated_at'] = now();
            DB::table('shopify_products')->where(['ean' => $new_products['product']['id'], 'shop_id' => $this->shop_details->id])->update($row);
        }elseif($insertProducts['product']['id']){
            $row['created_at'] = now();
            DB::table('shopify_products')->where(['ean' => $new_products['product']['id'], 'shop_id' => $this->shop_details->id])->insert($row);
        }
    }

    public function updateExportProduct($product_id, $old_products)
    {
        $url = $this->shop_details->url . "admin/api/2020-01/products/$product_id.json";
        $curl_header = array("accept: application/json", "authorization: Basic " . $this->auth, "content-type: application/json");
        $alldata = json_encode($old_products);
        $response = $this->culrRequestExec($url, $curl_header, $alldata, 'PUT');

        $insertProducts = json_decode($response, true);

        if($insertProducts['product']['id'] == $product_id){
            DB::table('shopify_products')->where(['ean' => $old_products['product']['id'], 'shop_id' => $this->shop_details->id])->update(['updated_at' => now()]);
        }

        $CollectCategory = $this->getConnectedCategoryProduct($product_id);

        foreach (json_decode($CollectCategory)->collects as $value) {
            $this->deleteConnectedCategoryProduct($value->id);
        }

        $this->postProductCategoryLink($product_id, $old_products['product']['collection_id']);
    }

    public static function ShopifyProductDelete($shop, $product_ids)
    {
        foreach ($product_ids as $product_id) {
            $url = $shop->url . "admin/api/2020-01/products/$product_id.json";
            $curl_header = array("accept: application/json", "authorization: Basic " . base64_encode("$shop->username:$shop->password"), "content-type: application/json");
            $alldata = NULL;
            $response = ShopifyApi::culrRequestExec($url, $curl_header, $alldata, 'DELETE');

            // if ($response) {
                DB::table('shopify_products')->where(['product_id' => $product_id, 'shop_id' => $shop->id])->delete();
            // }
        }
    }

    public function getProductCategories()
    {
        $url = $this->shop_details->url . "admin/api/2020-07/custom_collections.json";
        $curl_header = array("authorization: Basic " . $this->auth, "content-type: application/json");
        $alldata = NULL;
        $response = $this->culrRequestExec($url, $curl_header, $alldata, 'GET');
        $response = json_decode($response);
        return $response;
    }

    public function postProductcategories($category)
    {
        $create_custom_collection['custom_collection'] = array(
            'title' => $category,
            'published' => true
        );

        $url = $this->shop_details->url . "admin/api/2020-07/custom_collections.json";
        $curl_header = array("accept: application/json", "authorization: Basic " . $this->auth, "content-type: application/json");
        $alldata = json_encode($create_custom_collection);
        $response = $this->culrRequestExec($url, $curl_header, $alldata, 'POST');
    }

    public function postProductCategoryLink($product_id, $collection_id)
    {
        $create_collect['collect'] = array(
            'product_id' => $product_id,
            'collection_id' => $collection_id
        );

        $url = $this->shop_details->url . "admin/api/2020-07/collects.json";
        $curl_header = array("accept: application/json", "authorization: Basic " . $this->auth, "content-type: application/json");
        $alldata = json_encode($create_collect);
        $response = $this->culrRequestExec($url, $curl_header, $alldata, 'POST');
    }

    public function getConnectedCategoryProduct($product_id)
    {
        $url = $this->shop_details->url . "admin/api/2020-07/collects.json?product_id=$product_id";
        $curl_header = array("accept: application/json", "authorization: Basic " . $this->auth, "content-type: application/json");
        $alldata = NULL;
        $response = $this->culrRequestExec($url, $curl_header, $alldata, 'GET');
        return $response;
    }

    public function deleteConnectedCategoryProduct($collect_id)
    {
        $url = $this->shop_details->url . "admin/api/2020-07/collects/$collect_id.json";
        $curl_header = array("accept: application/json", "authorization: Basic " . $this->auth, "content-type: application/json");
        $alldata = NULL;
        return $this->culrRequestExec($url, $curl_header, $alldata, 'DELETE');
    }


    public static function checkPost($shop_details): bool
    {
        $auth = base64_encode("$shop_details->username:$shop_details->password");

        $url = $shop_details->url . "admin/api/2023-07/products.json";

        $curl_header = array(
            "accept: application/json",
            "authorization: Basic " . $auth,
            "content-type: application/json",
            "X-Shopify-Access-Token: ".$shop_details->password
        );

        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_HEADER => 0,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_HTTPHEADER => $curl_header,
            CURLOPT_POSTFIELDS =>  [],
            CURLOPT_FOLLOWLOCATION => true,
        ));

        curl_exec($curl);
        $info = curl_getinfo($curl);

        return $info['http_code'] == 400;
    }

    public static function shopInfo($shop_details)
    {
        $auth = base64_encode("$shop_details->username:$shop_details->password");
        $url = $shop_details->url . "admin/api/2023-07/shop.json";

        $curl_header = array(
            "accept: application/json",
            "authorization: Basic " . $auth,
            "content-type: application/json",
            "X-Shopify-Access-Token: ".$shop_details->password
        );

        $alldata = NULL;
        $response = self::culrRequestExec($url, $curl_header, $alldata, 'GET');
        $response = json_decode($response,true);

        if($response && !$response['errors']){
          $url = $shop_details->url ."/admin/oauth/access_scopes.json";
          $scopes_res = self::culrRequestExec($url, $curl_header, $alldata, 'GET');
          $scopes = json_decode($scopes_res,true);
          $scopes = Arr::flatten($scopes['access_scopes']);

          $required_scopes = [
            'read_products',
            'read_product_listings',
            'write_products',
            'read_orders',
            'write_orders',
            'read_all_orders'
          ];

          $result = array_diff($required_scopes,$scopes);
          if(count($result)){
            $response['errors'] = "Missing access scopes : ".implode(' , ',$result);
          }
        }
        elseif (empty($response)){
            $response['errors'] = "No response from server !";
        }
        return $response;
    }


    public static function culrRequestExec($curl_url, $curl_header, $alldata = null, $request_type = 'POST')
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $curl_url,
            CURLOPT_HEADER => 0,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_CUSTOMREQUEST => $request_type,
            CURLOPT_HTTPHEADER => $curl_header,
            CURLOPT_POSTFIELDS =>  $alldata,
            CURLOPT_FOLLOWLOCATION => true,
        ));

        return curl_exec($curl);
    }
}
