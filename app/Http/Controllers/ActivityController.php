<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Shop;
use Exception;
use CRUDBooster;
use Carbon\Carbon;
use App\Services\DateTime\DateTime;
use App\Services\ChannelProductService;
use App\Traits\ProjectShare;

class ActivityController extends Controller
{
	use ProjectShare;

	public function index(Request $request)
	{
		if(!$this->canAccess()) abort(404, "Invalid access!");

		$data = [
			'page_title' => 'Activity',
		];

		$data['isDrotienda'] = CRUDBooster::isDroptienda();
		$data['isSuperAdmin'] = CRUDBooster::isSuperAdmin();
		$data['isDropmatix'] = CRUDBooster::isDropMatrix();
		$data['isDropmatixSupport'] = CRUDBooster::isDropmatixSupport();

		return view('admin.activity', $data);
	}



	public function unsendOrder(Request $request)
	{
		if(!$this->canAccess()) return response()->json(['data' => []]);

		$data = DB::table('new_orders')
		->leftjoin('skip_mp_orders', 'new_orders.id', '=', 'skip_mp_orders.order_id')
		->leftjoin('order_trackings', 'new_orders.id', '=', 'order_trackings.order_id')
		->join('shops', 'new_orders.shop_id', '=', 'shops.id')
		->join('cms_users', 'new_orders.cms_user_id', '=', 'cms_users.id')
		->where('new_orders.insert_type', 1)
		->whereNull('new_orders.marketplace_order_ref')
        ->where('new_orders.test_order', '<>', 1)
        ->where('new_orders.invoice_number', '>', 0)
        ->whereNull('new_orders.credit_ref')
        ->where('new_orders.credit_number', 0)
        ->whereNull('new_orders.offer_number')
        ->where('new_orders.cms_user_id', '<>', 2455)
        ->whereNull('skip_mp_orders.order_id')
        ->whereNull('order_trackings.order_id')
        ->whereRaw('new_orders.cart->\'$[*].ean\' IS NULL')
        ->where('new_orders.id', '>', 36107)
        ->whereNull('new_orders.deleted_at')
        ->whereNull('new_orders.supplier_id')
        ->orderBy('new_orders.id', 'desc')
        ->select('new_orders.id', 'cms_users.email', 'cms_users.name', 'new_orders.order_date', 'shops.shop_name')
        ->groupBy('new_orders.id')
        ->get()
        ->map(function($order) {
        	return [
        		'id' => $order->id,
        		'email' => $order->email,
        		'name' => $order->name,
        		'order_date' => $order->order_date,
        		'action' => '<a href="#" class="btn btn-danger btn-xs hide_mp_list_x" title="Hide" data-id="'.$order->id.'">Hide <i class="fa fa fa-trash"></i></a>',
        		'shop_name' => $order->shop_name,
        		'cart' => '<a href="#" class="btn btn-drm btn-xs show_product_unseen_x" title="Products" data-id="'.$order->id.'">Show <i class="fa fa fa-eye"></i></a>',
        		'log' => '<a href="#" class="btn btn-drm btn-xs status_info_btn_x" title="Log" data-id="'.$order->id.'">Show <i class="fa fa fa-eye"></i></a>',
        	];
        });

		return response()->json(['data' => $data]);

	}



	public function dtServer(Request $request)
	{
		if(!$this->canAccess()) return response()->json(['data' => []]);

		$data = DB::table('shop_delete_instances')
		->whereNull('deleted_at')
		->whereNotNull('email')
		->select('id', 'guid', 'name', 'email', 'created_at', 'status', 'shop_id')
		->orderBy('id', 'desc')
		->get()
		->map(function($item) {

			$statusBtn = '--';
			$userStatusBtn = '--';
			$status = (int)$item->status;

			if($status === 1)
			{
				$statusBtn = '<a href="#" class="btn btn-danger btn-xs dt_server_delete_x" title="Remove" data-id="'.$item->id.'" data-guid="'.$item->guid.'">Delete <i class="fa fa fa-trash"></i></a>';

				$statusBtn .= '&nbsp;<a href="#" class="btn btn-warning btn-xs dt_server_delete_undo" title="Undo" data-id="'.$item->id.'" data-guid="'.$item->guid.'">Undo <i class="fa fa-undo"></i></a>';


				//user status button
				$userStatusBtn =  '';

				$user_id = $this->getUserIdByShopId($item->shop_id);

				if(!empty($user_id)){
					$userStatusBtn = '<a href="#" class="btn btn-info btn-xs dt_user_status_info" title="User Info" data-id="'.$user_id.'"><i class="fa fa-info-circle"></i></a>';
				}else{
					$userStatusBtn = 'Shop Not Exists!';
				}

				//user status button END
			}

			return [
				'id' => $item->id,
				'guid' => $item->guid,
				'name' => $item->name,
				'email' => $item->email,
				'created_at' => $item->created_at,
				'status' => $statusBtn,
				'user_status' => $userStatusBtn,
			];
		})
		->toArray();

		return response()->json(['data' => $data]);
	}


	public function dtServerDelete(Request $request, $id)
	{
    	try {

    		if(!$this->canAccess()) throw new Exception('Invalid access!');

    		$item = DB::table('shop_delete_instances')->find($id);
    		if(empty($item) || empty($item->guid)) throw new Exception('Invalid action!');

    		$logs = $item->log && drmIsJSON($item->log) ? json_decode($item->log, true) : [];

			if(isset($logs[0]['is_trial']) && $logs[0]['is_trial']){
				try {

					$shop = Shop::find($item->shop_id);

					if(!empty($shop)){
						app(ChannelProductService::class)->deleteChannelProducts($shop->id, $shop->user_id);
						app(ChannelProductService::class)->deleteChannelCategories($shop->id, $shop->user_id);

						$this->tagInsertToCustomer($shop->user_id, drm_shop_type_name($shop->channel) . ' cancels', 8);

						//Clear account activity step
						\App\Services\CheckListProgress\Checklist::cache_key_clear(5, $shop->user_id);

						// Agb Logfile
						create_agb_log(
							$shop->user_id,
							[
								'shop_info' => ['shop_name' => $shop->shop_name, 'shop_type' => $shop->channel, 'shop_url' => $shop->url,],
							], [],
							'Shop ' . $shop->shop_name . ' is deleted by admin.',
						);

						Shop::where([
							'id' => $shop->id
						])->delete();
					}


				} catch (\Exception $th) {}

			}

    		$log = [
    			'time' => now(),
    			'name' => \CRUDBooster::myName(),
    			'id' => \CRUDBooster::myId(),
    		];
			$curl = curl_init();
				curl_setopt_array($curl, array(
				CURLOPT_URL => 'https://mgmt.droptienda.eu/api/v4/projects/10/trigger/pipeline',
				CURLOPT_RETURNTRANSFER => true,
				CURLOPT_ENCODING => '',
				CURLOPT_MAXREDIRS => 10,
				CURLOPT_TIMEOUT => 0,
				CURLOPT_FOLLOWLOCATION => true,
				CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
				CURLOPT_CUSTOMREQUEST => 'POST',
				CURLOPT_POSTFIELDS => array('token' => 'glptt-6c8658edd9cf3ce2ccaf06fabd65380d60af9118','ref' => 'main','variables[GUID]' =>  $item->guid),
				)
			);
			$response = curl_exec($curl);

			curl_close($curl);
	        $logs[] = json_decode($response);

	        DB::table('shop_delete_instances')
	        ->where('id', $item->id)
	        ->update([
	        	'status' => 2,
	        	'log' => json_encode($logs),
	        	'deleted_at' => now(),
	        	'updated_at' => now(),
	        ]);

	        DB::table('server_statuses')->where('shop_id', $item->shop_id)->delete();
	        // \App\Shop::where('id', $item->shop_id)->update(['url' => null]);

			//Delete user related data
			// $user_id = $this->getUserIdByShopId($item->shop_id);
			// UserRelatedDataDeleteJob::dispatch($user_id);

			return response()->json([
				'success' => true,
				'message' => 'Server delete request sent!'
			]);

        } catch(Exception $e) {
			return response()->json([
				'success' => false,
				'message' => $e->getMessage(),
			]);
		}
	}



	public function hideOrder(Request $request, $id)
	{
    	try {

    		if(!$this->canAccess()) throw new Exception('Invalid access!');


    		if(DB::table('skip_mp_orders')->where('order_id', $id)->exists()) throw new Exception('Already hide!');

    		DB::table('skip_mp_orders')
    		->insert([
    			'order_id' => $id,
    			'change_by' => CRUDBooster::myId(),
    		]);

			return response()->json([
				'success' => true,
				'message' => 'Hide success!'
			]);

        } catch(Exception $e) {
			return response()->json([
				'success' => false,
				'message' => $e->getMessage(),
			]);
		}
	}


	private function canAccess(): bool
	{
		return CRUDBooster::isSuperAdmin() || CRUDBooster::isDropmatixSupport() || CRUDBooster::isDropMatrix() || CRUDBooster::isDroptienda();
	}

	public function dtServerDeleteUndo($id)
	{
    	try {

    		if(!$this->canAccess()) throw new Exception('Invalid access!');

    		$shop_instance = DB::table('shop_delete_instances')->find($id);
    		if(empty($shop_instance) || empty($shop_instance->guid)) throw new Exception('Invalid action!');

			// User unblock
			$user_id = $this->getUserIdByShopId($shop_instance->shop_id);

			if($user_id){
				$this->userBlockUnblock($user_id, 'Active');
			}

			// Dt shop entry delete
			$this->shopInstanceDeleteById($id);

			return response()->json([
				'success' => true,
				'message' => 'Server delete request undo done!'
			]);

        } catch(Exception $e) {
			return response()->json([
				'success' => false,
				'message' => $e->getMessage(),
			]);
		}
	}

	public function getUserIdByShopId($shop_id)
	{
		return \App\Shop::where('id', $shop_id)->value('user_id');
	}

	public function userBlockUnblock($user_id, $status = '')
	{
		DB::table('cms_users')->where('id', $user_id)->whereNull('status')->update([
			'status'=> $status
		]);
	}

	public function checkDtUserHasShopDeleteInstance($user_id)
    {
        $id = DB::table('shop_delete_instances')->whereNotNull('log')->whereJsonContains('log', [['id' => $user_id]])->value('id');

        return $id;
    }

	public function shopInstanceDeleteById($id)
    {
        DB::table('shop_delete_instances')->where('id', $id)->delete();
    }

	public function dtUserStatus($user_id)
	{
		$user_status = [];

		$has_dt_purchase_plan = DB::table('dt_tariff_purchases')
		->join('import_plans', 'dt_tariff_purchases.plan_id' , '=', 'import_plans.id')
		->where('user_id', $user_id)
		// ->whereDate('end_date', '>=', $today)
		->select('dt_tariff_purchases.user_id', 'dt_tariff_purchases.plan_id', 'dt_tariff_purchases.start_date', 'dt_tariff_purchases.end_date', 'dt_tariff_purchases.subscription_id', 'import_plans.plan')
		->first();

		if($has_dt_purchase_plan){
			$user_status['plan_name'] = $has_dt_purchase_plan->plan;
			$user_status['start_date'] = Carbon::parse($has_dt_purchase_plan->start_date)->toDateString();
			$user_status['end_date'] = Carbon::parse($has_dt_purchase_plan->end_date)->toDateString();


			if(!empty($has_dt_purchase_plan->subscription_id)){
				$user_status['plan_type'] = 'Purchased';
			}else{
				$user_status['plan_type'] = 'Assigned';
			}

			$remain_days = DateTime::getRemainDays(Carbon::now()->toDateString(), $has_dt_purchase_plan->end_date);
			if((int)$remain_days === -0){
				$remain_days = 1;
			}

			$user_status['plan_days'] = $remain_days > 0 ? (int)$remain_days  : 'Expired';
		}else{
			$is_trial = DB::table('app_trials')
			->select('trial_days', 'start_date')
			->where(['user_id' => $user_id, 'app_id' => 0])->first();

			$trial_remain_days = DateTime::getTrialRemaining($is_trial->start_date, $is_trial->trial_days);

			if($is_trial){
				$user_status['plan_name'] = 'Trial';
				$user_status['start_date'] = Carbon::parse($is_trial->start_date)->toDateString();
				$user_status['end_date'] = Carbon::parse($is_trial->start_date)->addDays(13)->toDateString();

				$user_status['plan_days'] = $trial_remain_days > 0 ? (int)$trial_remain_days  : 'Expired';
			}else{
				$user_status['plan_name'] = 'No Plan';
				$user_status['start_date'] = 'No Plan';
				$user_status['end_date'] = '0 Days';

				$user_status['plan_days'] = 'No Plan';
			}
		}

		return response()->json([
			'data' => $user_status
		], 200);
	}
}
