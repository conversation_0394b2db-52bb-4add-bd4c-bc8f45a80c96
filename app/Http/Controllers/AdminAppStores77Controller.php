<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use Illuminate\Support\Str;
	use Illuminate\Support\Facades\Storage;

	class AdminAppStores77Controller extends \crocodicstudio\crudbooster\controllers\CBController {

		public $lang;
		public $lang_name;

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->getLanguage();

			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "app_stores";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Service Name","name"=>"menu_name"];

			$this->col[] = ["label"=>"Category","name"=>"id"];

			$this->col[] = ["label"=>"Fixed Price","name"=>"fixed_price"];

			$this->col[] = ["label"=>"Icon","name"=>"icon","image"=>true];
			$this->col[] = ["label"=>"Description","name"=>"description"];

			$this->col[] = ["label"=>"view","name"=>"is_view",'visible'=>false];

			$this->col[] = ["label"=>"Promotion","name"=>"promotion"];

// 			$this->col[] = ["label"=>"Payment Type","name"=>"payment_type"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];

			$this->form[] = ['label'=>'Service Name (Menu)','name'=>'menu_name','type'=>'text','width'=>'col-sm-10','validation'=>'required'];

			$this->form[] = ['label'=>'Service Title ('.$this->lang_name.')','name'=>'app_name_'.$this->lang,'type'=>'text','width'=>'col-sm-10','validation'=>'required'];

			// $this->form[] = ['label'=>'Select App Category','name'=>'app_category_id','type'=>'select2','datatable'=>'app_categories,category_name'];
			// $this->form[] = ['label'=>'Select App Category gg','name'=>'app_id','type'=>'select2','datatable'=>'app_categories,category_name','relationship_table'=>'app_store_categories'];

			$this->form[] = ['label'=>'Select App Category','name'=>'app_id','type'=>'app_categories','datatable'=>'app_categories,category_name','relationship_table'=>'app_store_categories', 'foreign_key'=>'app_id'];

			$this->form[] = ['label'=>'Fixed Price','name'=>'fixed_price','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];

			$this->form[] = ['label'=>'Icon','name'=>'icon','type'=>'upload','validation'=>'image','width'=>'col-sm-10'];

			$this->form[] = ['label'=>'File','name'=>'download_file','type'=>'upload','width'=>'col-sm-10'];

			$this->form[] = ['label'=>'Description','name'=>'description_'.$this->lang,'type'=>'wysiwyg','validation'=>'required','width'=>'col-sm-10'];
			
			$this->form[] = ['label'=>'Feature Product for Magazine','name'=>'feature_product','type'=>'radio','width'=>'col-sm-10','dataenum'=>'Active;Deactive'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();
	        
	        
	        $this->addaction[] = ['label'=>'Set Active','url'=>CRUDBooster::mainpath('set-status/1/[id]'),'icon'=>'fa fa-check','color'=>'success','showIf'=>"[is_view] == null",'confirmation' => true];
            $this->addaction[] = ['label'=>'Set UnActive','url'=>CRUDBooster::mainpath('set-status/0/[id]'),'icon'=>'fa fa-ban','color'=>'warning','showIf'=>"[is_view] == '1'", 'confirmation' => true];
				
			$this->addaction[] = ['label'=>'Promotion Off','url'=>CRUDBooster::mainpath('promotion-active/[id]'), 'icon'=>'fa fa-bullhorn', 'color'=>'danger', 'showIf' => '[promotion] == Deactive'];
			
			$this->addaction[] = ['label'=>'Promotion On','url'=>CRUDBooster::mainpath('promotion-inactive/[id]'), 'icon'=>'fa fa-times', 'color'=>'success', 'showIf' => '[promotion] == Active'];

	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


		}
		
		public function getLanguage()
		{
			$this->lang = \Config::get('app.locale');
			$this->lang_name = DB::table('countries')->where('language_shortcode',$this->lang)->first()->language;
		}
	    
	    public function getSetStatus($status,$id) {
	    	if($status=='0'){
		       DB::table('app_stores')->where('id',$id)->update(['is_view'=>null]);	
	    	}else{
		   DB::table('app_stores')->where('id',$id)->update(['is_view'=>$status]);

	    	}
		   
		   //This will redirect back and gives a message
		   CRUDBooster::redirect($_SERVER['HTTP_REFERER'],"The status product has been updated !","success");
		}


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here
	        $query->where('type','service');

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	// if($column_index ==  6){
	    	//     if($column_value == 1){
	    	//         $column_value ='<p">Active</p>'; 
	    	//     }else{
	    	//         $column_value ='<p>UnActive</p>'; 
	    	//     }
			// }

			if($column_index == 2) {
	    		$column_value = DB::table('app_categories')
			    ->join('app_store_categories', 'app_store_categories.category_id', '=', 'app_categories.id')
			    ->where('app_store_categories.app_id', $column_value)
			    ->select('app_categories.category_name as name')->implode('name', ', ');
	    	}

	    	if($column_index==5){
	    		$column_value = Str::words(strip_tags($column_value), 8, '...');
	    	}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here
	        $postdata['type']='service';

	        if(Request::hasFile('download_file')){
	        	$file = Request::file('download_file');
	        	$postdata['download_file'] = $this->fileUpload($file, $postdata['download_file'], 'document');
	        }
	       	if(Request::hasFile('icon')){
	        	$file = Request::file('icon');
	        	$postdata['icon'] = $this->fileUpload($file, $postdata['icon'], 'image');
	        }

	        

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here
	        $postdata['type']='service';
	       	if(Request::hasFile('download_file')){
	        	$file = Request::file('download_file');
	        	$postdata['download_file'] = $this->fileUpload($file, $postdata['download_file'], 'document');
	        }
	       	if(Request::hasFile('icon')){
	        	$file = Request::file('icon');
	        	$postdata['icon'] = $this->fileUpload($file, $postdata['icon'], 'image');
	        }

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here
	        $this->removeOldFile($id, 'download_file');
	        $this->removeOldFile($id, 'icon');

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)
	    public function getDeleteFile($id, $col){
	    	$this->removeOldFile($id, $col);
	    	return redirect()->back()->with(["msg" => 'Successfully deleted!']);
	    }

	    public function getEdit($id) {
		  //Create an Auth
		  if(!CRUDBooster::isUpdate() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {    
		    CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
		  }	  
		  $data = [];
		  $data['page_title'] = 'Edit Service in App Store';
		  $data['row'] = $row = \App\AppStore::with('categories')->where('id', $id)->first();
		  $data['app_categories'] = DB::table('app_categories')->get();
		  $data['selected_categories'] = $row->categories ? $row->categories->pluck('id')->toArray() : [];
		  //Please use cbView method instead view method from laravel
		  $this->cbView('app_store.edit_service',$data);
		}

		public function getDetail($id) {
		  //Create an Auth
		  if(!CRUDBooster::isRead() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {    
		    CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
		  }
		  $data = [];
		  $data['page_title'] = 'Details of Service in App Store';
		  $data['row'] = $row = \App\AppStore::with('categories')->where('id', $id)->first();
		  $data['category'] = $row->categories ? $row->categories->pluck('category_name')->implode(", ") : null;	  
		  //Please use cbView method instead view method from laravel
		  $data['avg_rating'] = round(DB::table('ratings')->where('cms_modul_id', $id)->where('rating', '>', 0)->groupBy('cms_modul_id')->avg('rating'), 1);
		  $single_user_rating = DB::table('ratings')->where(['cms_modul_id'=>$id, 'cms_user_id'=> \CRUDBooster::myId()])->select('rating')->first();
		  $data['user_rating'] = $single_user_rating->rating?? 0;
		  $this->cbView('app_store.view_service',$data);
		}

	    private function fileUpload($file, $tmp, $folder){
	    	if( Storage::exists($tmp) ) Storage::delete($tmp); //del uploaded file
        	$file_name = $folder.'/'.md5( $file->getClientOriginalName(). microtime() ).'.'.$file->getClientOriginalExtension();
        	Storage::disk('spaces')->put($file_name, file_get_contents($file), 'public');
        	return ( Storage::disk('spaces')->exists($file_name) )? Storage::disk('spaces')->url($file_name) : null;
	    }

	    private function removeOldFile($id, $col){
	    	$item = DB::table('app_stores')->find($id);
	    	if($item && $item->{$col}){

				$url = $item->{$col};
				$search = '.com/';
				$file = substr($url, strpos($url, $search)+ strlen($search) );
	    		if( Storage::disk('spaces')->exists($file) ){
	    			Storage::disk('spaces')->delete($file);
	    		}
	    		DB::table('app_stores')->where('id', $id)->update([$col => null]);
	    	}
	    }

		public function getPromotionActive($id){
			DB::table('app_stores')->where('id',$id)->update(['promotion' => 'Active']);
			CRUDBooster::redirect(CRUDBooster::adminPath('app_stores77'),"App Promotion Activated!","success");
		}

		public function getPromotionInactive($id){
			DB::table('app_stores')->where('id',$id)->update(['promotion' => 'Deactive']);
			CRUDBooster::redirect(CRUDBooster::adminPath('app_stores77'),"App Promotion Deactivated!!","success");
		}

	}