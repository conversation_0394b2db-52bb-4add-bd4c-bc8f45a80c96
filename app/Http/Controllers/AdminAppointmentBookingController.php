<?php

namespace App\Http\Controllers;

use App\Appointment;
use App\AppointmentBooking;
use App\AppointmentType;
use App\AppointmentProjectSlot;
use App\AppointmentHistory;
use App\NewCustomer;
use App\DropfunnelCustomerTag;
use App\DropfunnelTag;
use App\SenderEmailSetting;
use App\User;
use App\Services\Dashboard\Customer;
use App\Notifications\DRMNotification;
use App\OfferDeal;
use Exception;
use PDF;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Request;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request as LaravelRequest;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Spatie\GoogleCalendar\Event;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redirect;

class AdminAppointmentBookingController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = true;
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = true;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "appointment_slot";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "User Id", "name" => "user_id", "join" => "cms_users,id"];
        $this->col[] = ["label" => "Event Slot", "name" => "event_slot"];
        $this->col[] = ["label" => "Break Slot", "name" => "break_slot"];
        $this->col[] = ["label" => "Status", "name" => "status"];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'User Id', 'name' => 'user_id', 'type' => 'select2', 'validation' => 'required|integer|min:0', 'width' => 'col-sm-10', 'datatable' => 'cms_users,id'];
        $this->form[] = ['label' => 'Event Slot', 'name' => 'event_slot', 'type' => 'textarea', 'validation' => 'required|string|min:5|max:5000', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Break Slot', 'name' => 'break_slot', 'type' => 'textarea', 'validation' => 'required|string|min:5|max:5000', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Status', 'name' => 'status', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'cms_users,id'];
        //$this->form[] = ['label'=>'Event Slot','name'=>'event_slot','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        //$this->form[] = ['label'=>'Break Slot','name'=>'break_slot','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        //$this->form[] = ['label'=>'Status','name'=>'status','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }


    //By the way, you can still create your own method in here... :)
    public function getIndex()
    {

       $data = [];
    //    $events = $this->CalendarEvents();

        $user_id = CRUDBooster::myId();
        $data['user_id'] = $user_id;
        $events['appointment_days'] = AppointmentBooking::where('user_id', $user_id)->pluck('user_id')->toArray() ?? [];
        $events['appointment_types'] = AppointmentType::where('user_id', $user_id)->get();
        $events['settings'] = DB::table('appointment_slot')->where('user_id', $user_id)->exists();

       $data['page_title'] = __("Appointment Booking");
       $data['user_id'] = $events['user_id'];
       $data['appointment_days'] = $events['appointment_days'];
       $data['appointment_types'] = $events['appointment_types'];
       $data['settings']  = $events['settings'];
        return view('admin.appointment_booking.index', $data);
    }

    public function eventExport(){


//        delete event from google

//        $credentials = DB::table('google_calendar_credentials')->where('user_id',CRUDBooster::myId())->first();
//        $calenderConfiguration = $this->googleDataMapping($credentials);
//        \Config::set('google-calendar', $calenderConfiguration);
//        if ($calenderConfiguration['calendar_id']!='0' && !empty($credentials)) {
//                try {
//                    $event = Event::find('n1sfkh8a7mhnaat73ajng3q5g0');
//                    $event->delete();
//                }catch (Exception $e){
//
//                }
//        }




//        create event to google calendar

//        $credentials = DB::table('google_calendar_credentials')->where('user_id',CRUDBooster::myId())->first();
//        $calenderConfiguration = $this->googleDataMapping($credentials);
//        \Config::set('google-calendar', $calenderConfiguration);
//
//        if ($calenderConfiguration['calendar_id']!='0' && $credentials){
//            $event = new Event;
//            $event->name =  'Thomas Landwehr';
//            $event->description = 'null';
//            $event->startDateTime = Carbon::parse('2021-09-13T18:00:00+06:00');
//            $event->endDateTime = Carbon::parse('2021-09-13T18:30:00+06:00');
//            $event =$event->save();
//
//            dd($event->id);

//        }


    }

    // appointment_booking: all calendar events
    public function getCalendarEvents(){
      $data = $this->CalendarEvents();
      return response()->json([
          'success' => true,
          'data' => $data
      ]);
    }

    protected function CalendarEvents(){
      $data = [];
      $data['page_title'] = 'Calender Booking';

      $user_id = CRUDBooster::myId();
      $my_id = CRUDBooster::myId();

      $available_slots = AppointmentBooking::where('user_id', $user_id)->first();
      $project_slots = AppointmentProjectSlot::where('user_id', $user_id)->select('slots')->get();

      $is_sub_user = (CRUDBooster::myParentId() != $my_id);
      $parent_sub_users = [];

      if ($is_sub_user) 
      { 
        $appointments = Appointment::where('user_id', $user_id)->orWhere('customer_id',$user_id)->get();
      } else {
        $parent_sub_users = DB::table('cms_users')->where('parent_id', $user_id)->pluck('id')->toArray();

        $user_and_sub_users = $parent_sub_users;
        $user_and_sub_users[] = $user_id;
        $appointments = Appointment::whereIn('user_id', $user_and_sub_users)->orWhereIn('customer_id',$user_and_sub_users)->get();
      }

      $data['appointment_days'] = AppointmentBooking::where('user_id', $user_id)->pluck('user_id')->toArray() ?? [];
      $data['user_id'] = $user_id;
      $google_credentials = DB::table('google_calendar_credentials')->where('user_id',$user_id)->first();
      $data['appointment_types'] = AppointmentType::where('user_id', $user_id)->get();

      //get appointment booked event
      $events = [];
      if ($available_slots->event_slot) {
          foreach ($available_slots->event_slot as $key => $slot) {
              $events_array = $this->makeEventsArray($slot['startTime'], $slot['endTime'], $slot['daysOfWeek'], $slot['slot']);
              if (empty($events_array)) {
                  continue;
              }
              if (count($events_array) > 1) {
                  foreach ($events_array as $item) {
                      $events[] = $item;
                  }
              } else {
                  $events[] = self::array_flatten($events_array);
              }
          }
      }

      $appointment_array = [];
      foreach ($appointments as $app) {
          foreach ($app->appointment as $value) {
              $temp = [];
              $temp['title'] = $value['title'];

              if (!$is_sub_user && in_array($app->user_id, $parent_sub_users)) {
                $temp['title'] .= ' (Sub)';
              }

              $temp['start'] = $value['start'];
              $temp['end'] = $value['end'];
              $temp['color'] = $value['color'];
              $temp['slot'] = $value['slot'];
              $temp['type'] = $value['type'];
              $temp['phone'] = $value['phone'];
              $temp['email'] = $value['email'];
              $temp['note'] = $value['description'];
              $temp['slug'] = $app->slug;
              $temp['status'] = $app->status;
              $temp['appointment_id'] = $app->id;
              $temp['additional_info'] = $value['additional_info'] ?? '';

              $task_info = DB::table('drm_project_tasks')->where('id',$app->slug)->select('start_date','due_date','name','description','cover_image')->first();
              if(!empty($task_info)){
                $temp['task_title'] = $task_info->name;
                $temp['image'] = $task_info->cover_image;
                $temp['start_date'] = $task_info->start_date;
                $temp['end_date'] = $task_info->due_date;
                $temp['description'] = $task_info->description;
              }
          }

          if ($app->booking_info != null) {
              $temp['title'] = $app->booking_info['first_name'] . ' ' . $app->booking_info['last_name'];
              $temp['email'] = $app->booking_info['email'];
              $temp['phone'] = $app->booking_info['phone'];
          }
          array_push($appointment_array, $temp);
      }

     	// get google calender event
        $isConnect = $this->googleIsConnection($google_credentials);
        $isConnectArr = [];
        $isConnectArr[$user_id] = $isConnect;

        if (!$is_sub_user && !empty($parent_sub_users)) {
            foreach($parent_sub_users as $index => $sub_user_id) {
                $google_credentials_info = DB::table('google_calendar_credentials')->where('user_id', $sub_user_id)->first();
                if (!empty($google_credentials_info)) {
                    $isConnectArr[$sub_user_id] = $this->googleIsConnection($google_credentials_info);
                }
            }
        }

        foreach($isConnectArr as $temp_user_id => $isConnect) {
        if($isConnect){
            $credentials = DB::table('google_calendar_credentials')->where('user_id',$temp_user_id)->first();
            $calenderConfiguration = $this->googleDataMapping($credentials);

            \Config::set('google-calendar', $calenderConfiguration);
            if ($calenderConfiguration['calendar_id']!='0' && !empty($credentials)) {
                $startDateTime = Carbon::now()->subYear();
                $google_events = Event::get($startDateTime);

                foreach ($google_events as $event) {
                    $available_event = DB::table('appointments')->where('event_id', $event->googleEvent->id)->first();
                    if ($available_event == null) {
                        $temp = [];
                        $temp['title'] = $event->googleEvent->summary;
                        if (!$is_sub_user && in_array($temp_user_id, $parent_sub_users)) {
                            $temp['title'] .= ' (Sub)';
                        }
                        
                        $temp['start'] =  date('Y-m-d H:i:s', strtotime($event->googleEvent->start->dateTime));
                        $temp['end'] =  date('Y-m-d H:i:s', strtotime($event->googleEvent->end->dateTime));
                        $temp['email'] = $event->googleEvent->creator->email;
                        $temp['note'] = $event->googleEvent->description;
                        $temp['google_link'] = $event->googleEvent->htmlLink;
                        array_push($appointment_array, $temp);
                    }else{
                        $booking_array = json_decode($available_event->appointment);
                        $start_google_time = explode('T',$event->googleEvent->start->dateTime);
                        $new_start_google_time = Carbon::parse($start_google_time['0'].''.$start_google_time['1'])->format('H:i');
                        $start = $start_google_time['0'].'T'.$new_start_google_time;

                        $end_google_time = explode('T',$event->googleEvent->end->dateTime);
                        $new_end_google_time = Carbon::parse($end_google_time['0'].''.$end_google_time['1'])->format('H:i');
                        $end = $end_google_time['0'].'T'.$new_end_google_time;

                        if($booking_array['0']->start != $event->googleEvent->start->dateTime || $booking_array['0']->end != $event->googleEvent->end->dateTime){
                            $new_appiontment[] = [
                                'title' => $event->googleEvent->summary,
                                'start' => $event->googleEvent->start->dateTime,
                                'end' => $event->googleEvent->end->dateTime,
                                'description' => $event->googleEvent->description,
                                'constraint' => "availableForMeeting",
                                'slot' => "book",
                                'type' => $booking_array['0']->type,
                                'email' => $booking_array['0']->email,
                                'phone' => $booking_array['0']->phone,
                                'color' => ($booking_array['0']->color) ? $booking_array['0']->color : 'gray',
                                'date' => $start_google_time['0'],
                                'start_time' => $start_google_time['1'],
                                'end_time' => $end_google_time['1'],
                                'post' => ($booking_array['0']->post) ?? 0,
                            ];
//                            Appointment::where('id', $available_event->id)->where('user_id', $available_event->user_id)->update(
//                                [
//                                    'appointment' => $new_appiontment
//                                ]);
//                            $updatedAppointment = Appointment::find($available_event->id);
//                            $this->sendAppointmentEmail($updatedAppointment);
                        }

                    }

                }
            }
        }
        }


      //get project slots
      $all_project = [];
      foreach ($project_slots as $project) {
          foreach ($project->slots as $value) {
              $projects = $this->makeProjectArray($value['start_time'], $value['end_time'], $value['date']);
              if (count($projects) > 1) {
                  foreach ($projects as $item) {
                      $all_project[] = $item;
                  }
              }
          }
      }

      // manual slots
      $manual_slots = \Cache::rememberForever('manual_slots_' . CRUDBooster::myId(), function () {
          $data = [];
          for ($day = 0; $day < 7; $day++) {
              $final_slots = $this->halfHourTimes($day);
              foreach ($final_slots as $slot) {
                  $data[] = $slot;
              }
          }
          return $data;
      });

      $data['events'] = array_merge($manual_slots, $appointment_array, $events, $all_project);

      $data['settings'] = DB::table('appointment_slot')->where('user_id', $user_id)->exists();

      if($isConnectArr[$user_id]) {
          $data['api_key'] = $google_credentials->api_key;
          $data['calendar_id'] = $google_credentials->calendar_id;
      }

      $data['current_lang'] = \Config::get('app.locale');
      $data['iframe_url'] = url('/appointment/booking-form');
			
      return $data;
    }

    public function getAdd()
    {
        $data = [];
        $data['page_title'] = __('Setting');
        $data['time_slots'] = $this->timeSlot();

        $availables = $breaks = [];
        $stored_booking = AppointmentBooking::where('user_id', CRUDBooster::myId())->first();

        $available_slots = $stored_booking->event_slot;
        if (!empty($available_slots)) {

            foreach ($available_slots as $index => $info) {
                $slot = $info['slot'];
                $daysOfWeek = $info['daysOfWeek'];
                $startTime = $info['startTime'];
                $endTime = $info['endTime'];
    
                if ($slot == 'available') {
                    $availables[$daysOfWeek]['start'] = $startTime;
                    $availables[$daysOfWeek]['end'] = $endTime;
                }
                elseif ($slot == 'break') {
                    $breaks[$daysOfWeek]['start'][] = $startTime;
                    $breaks[$daysOfWeek]['end'][] = $endTime;
                }
            }
        }

        $data['availables'] = $availables;
        $data['breaks'] = $breaks;
        $data['block_day'] = !empty($stored_booking) ? $stored_booking->input_array['block_day'] : '#ee5149';
        $data['free_slot'] = !empty($stored_booking) ? $stored_booking->input_array['free_slot'] : '#4f9275';
        
        return view('admin.appointment_booking.opening_hour.create', $data);
    }

    // opening hour slots save
    public function postSave(LaravelRequest $request)
    {
        $request = $request->except('_token');
        $rules = [
            'api_key' => 'string|nullable',
            'calendar_id' => 'string|nullable',
            'block_day' => 'required',
            'free_slot' => 'required',
            // 'start.*' => "required|gt:end.*",
            // 'start.*' =>  [
            //                'required',
            //                function($attribute, $value, $fail) use ($request) {
            //                    $arr = explode('.', $attribute);
            //                    $index = $arr[1];
            //                    if ($value > $request['end'][$Index]) {
            //                       $fail('Start time can not greater then end time');
            //                   }
            //                }
            //            ],
            'end.*' => 'required|string',
            'break_start.*' => 'required|string',
            'break_end.*' => 'required|string',
        ];

        $message = [
            'api_key.required' => 'API Key field is required',
            'block_day.required' => 'Block day field must be string',
            'free_slot.required' => 'Free slot name field is required',
            // 'start.required' => 'Start time field is required',
            // 'end.required' => 'End time field is required',
        ];

        Validator::make($request, $rules, $message)->validate();

        try {
            $slots = $breaks = [];
            $days = $request['day'];
            foreach ($days as $key => $day) {   // Slots for avaialbe days = [0-6]
                $slot = [];
                if ($request['start'][$key] != '00:00' && $request['end'][$key] != '00:00') {
                    $slot['groupId'] = 'availableForMeeting';
                    $slot['startTime'] = $request['start'][$key];
                    $slot['endTime'] = $request['end'][$key];
                    $slot['daysOfWeek'] = $day;
                    $slot['slot'] = 'available';
                    $slot['display'] = 'background';
                    $slots[] = $slot;
                }

                foreach ($request['start_break'][$key] as $break_key => $break_start) {     // Break slots for every day
                    if ($break_start != "00:00" && $request['end_break'][$key][$break_key] != "00:00") {
                        $break = [];
                        $break['startTime'] = $break_start;
                        $break['endTime'] = $request['end_break'][$key][$break_key];
                        $break['daysOfWeek'] = $day;
                        $break['overlap'] = false;
                        $break['slot'] = 'break';
                        $break['color'] = '#ff9f89';
                        $break['display'] = 'background';
                        $breaks[] = $break;
                    }
                }
                if (!empty($request['show_status'])) {
                    if (in_array($key, $request['show_status'])) {     // set off day
                        unset($slots[$key]);
                        $break = [];
                        $break['startTime'] = '00:01';
                        $break['endTime'] = '24:00';
                        $break['daysOfWeek'] = "$key";
                        $break['overlap'] = false;
                        $break['slot'] = 'off_day';
                        $break['color'] = '#80808057';
                        $break['display'] = 'background';
                        $breaks[] = $break;
                    }
                }
            }

            $jsonData = array_merge($slots, $breaks);

            $user_id = CRUDBooster::myId(); // opening hour slots save
            $booking = AppointmentBooking::updateOrCreate(
                ['user_id' => $user_id],
                [
                    'event_slot' => $jsonData,
                    'api_key' => $request['api_key'],
                    'calendar_id' => $request['calendar_id'],
                    'input_array' => $request
                ]);

            return redirect('/admin/appointment_booking')->with(['message' => 'Opening hour store successfully!', 'message_type' => 'success']);
        } catch (Exception $e) {
            return redirect('/admin/appointment_booking')->with(['message' => 'Oops!! Something Went Wrong', 'message_type' => 'warning']);
        }
    }
    public function GoogleCalendarSettings()
      {
          $data = [];
          $data['page_title'] = __('Google Calendar Settings');
          $user_id = CRUDBooster::myId();
          $data['credentials'] = DB::table('google_calendar_credentials')->where('user_id',$user_id )->first();
          $data['isConnect'] = $this->googleIsConnection($data['credentials']);
          return view('admin.appointment_booking.google.setting', $data);
      }

      public function saveGoogleCredentials(LaravelRequest $request)
      {

          $request->validate([
              'calendar_id' => 'nullable|string',
              'api_key' => 'nullable|string',
              'service_account' => 'nullable|string',
              'oauth' => 'nullable|string',
          ]);

          try {
              $data = [
                  'calendar_id' => $request->calendar_id,
                  'api_key' => $request->api_key,
                  'service_account' => $request->service_account,
                  'oauth' => $request->oauth,
                  'created_at' => Carbon::now(),
                  'updated_at' =>  Carbon::now(),
              ];
              // DB::table('google_calendar_credentials')->where('user_id', CRUDBooster::myId())->update($data); 
              DB::table('google_calendar_credentials')->updateOrInsert(
                  ['user_id' => CRUDBooster::myId()], 
                  $data
              );
            return redirect('/admin/google-calendar-settings')->with(['message' => 'Credentials save successfully!', 'message_type' => 'success']);
          } catch (Exception $e) {
              CRUDBooster::redirect(CRUDBooster::adminPath('google_calendar_settings'), 'Something Went Wrong!', 'danger');
          }
      }

    // opening hour slots edit page  
    public function Edit()
    {
        $data = [];
        $data['page_title'] = __('Edit Settings');
        $data['time_slots'] = $this->timeSlot();
        $data['available_slots'] = AppointmentBooking::where('user_id', CRUDBooster::myId())->first();
        return view('admin.appointment_booking.opening_hour.edit', $data);
    }

    //Appointment types
    public function appointmentType()
    {

        $data = [];
        $data['page_title'] = __('Appointment Type');
        $data['appointment_types'] = AppointmentType::where('user_id', CRUDBooster::myId())->get(); // appointment type index page
        $data['user_id'] = CRUDBooster::myId();
        $data['app_id'] = config('global.online_appointment_app_id');
        $content = $url = null;
        if (!empty($embadedPage)) {
            $url = url("drm-email-iframe/" . $embadedPage->slug);
            $content = '<iframe src="' . $url . '" width="800" height="300" style="border:none !important;"></iframe>';
        }
        $data['content'] = $content;
        $data['url'] = $url;
        return view('admin.appointment_booking.appointment_type.index', $data);
    }

    // appointment type index: update link status
    public function updateLinkStatus(LaravelRequest $request){
      try{
          AppointmentType::where('user_id', CRUDBooster::myId())->where('id',$request->type_id)->update(
              [
                  'link_status' => $request->status
              ]);
          return response()->json(['success' => true, 'message' => 'Link status update successfully!']);
      } catch (Exception $e) {
          return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
      }
    }

    // appointment type index: update link url
    public function updateLinkUrl(LaravelRequest $request){
        try{
            AppointmentType::where('user_id', CRUDBooster::myId())
                ->where('id', $request->type_id)->update(
                [
                    'link' => $request->extra_url
                ]);
            return response()->json(['success' => true, 'message' => 'Link update successfully!']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
        }
    }

    //Add appointment type 
    public function addType()
    {
        $app_purchse = DB::table('appointment_type')->where('user_id', CRUDBooster::myId())->count();
        $app_id = config('global.online_appointment_app_id');
        if ($app_purchse >= 1 && !DrmUserHasPurchasedApp(CRUDBooster::myParentId(), $app_id)) {
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-type'), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = __('Add Appointment Type');
        return view('admin.appointment_booking.appointment_type.create', $data);
    }

    //Save appointment type 
    public function saveType(LaravelRequest $request)
    {
        $request->validate([
            'name' => 'required|string',
            'description' => 'nullable|string',
            'duration' => 'required|numeric|min:0|max:180',
            'post' => 'required|numeric|min:0|max:60',
            'color' => 'required|string',
            'position' => 'nullable|numeric',
            'content_color' => 'required|string',
            'iframe_bg' => 'nullable|string',
            'frame_color' => 'required|string',
            'select_tag' => 'nullable|exists:dropfunnel_tags,id',
            'separator_width' => 'nullable|numeric',
            'separator_color' => 'nullable|string',
            'date_format' => 'nullable|numeric',
            'frame_radius' => 'nullable|numeric',
            'frame_padding' => 'nullable|numeric',
            'separator_show' => 'nullable|numeric',
            'separator_style' => 'nullable|numeric',
            // 'price'             => 'nullable',
            // 'pipeline_transfer' => 'boolean',
            // 'pipeline_version' => 'numeric',
            // 'redirect_to_thank' => 'boolean',
            // 'show_user' => 'boolean',
        ]);
        try {
            $redirect_to_thank = (bool) $request->redirect_to_thank;
            $redirect_url      = $request->redirect_url;

            $data = [
                'user_id' => CRUDBooster::myId(), 
                'name' => $request->name,
                'slug' => uniqid(),
                'description' => $request->description,
                'duration' => $request->duration,
                // 'pre' => $request->preparation,
                'post' => $request->post,
                'color' => $request->color,
                'position' => $request->position,
                'content_color' => $request->content_color,
                'frame_color' => $request->frame_color,
                'iframe_bg_color_image' => $request->iframe_bg_color_image ?? 0,
                'iframe_background' => ($request->transparent != 1) ? $request->iframe_bg : null,
                'tag' => $request->select_tag,
                'optional_tag' => $request->optional_tag,
                'date_format' => $request->date_format,
                'frame_padding' => $request->frame_padding,
                'frame_radius' => $request->frame_radius,
                'border_switch' => $request->border_switch,
                'separator_show' => $request->separator_show,
                'separator_color' => $request->separator_color,
                'separator_width' => $request->separator_width,
                'separator_style' => $request->separator_style,
                'price'             => $request->price ?? null,
                'pipeline_transfer' => $request->pipeline_transfer ?? 0,
                'pipeline_version' => (int) $request->pipeline_version ?? 1,
                'redirect_to_thank' => $redirect_to_thank,
                'redirect_url'      => $redirect_url,
                'show_user' => $request->show_user ?? 0,
                'widget_name' => $request->widget_name,
                'widget_color' => $request->widget_color,
            ];

            if ($request->hasFile('bg_image')) {
                $request->validate([
                    'bg_image' => 'required|mimes:png,jpg,jpeg'
                ]);
    
                $file = $request->file('bg_image');
                $serverPath = uploadImage($file, 'appointment_type');

                if (!empty($serverPath)) {
                    $data['iframe_background_image'] = $serverPath;
                }
            }

            $type_id = AppointmentType::Create($data)->id;
            if ($type_id) {
                if (!$redirect_to_thank && !empty($redirect_url)) {
                    return redirect()->away($redirect_url);
                } else {
                    return redirect()->route('edit_thank_you_page', ['id' => $type_id]);
                }
            } else {
                throw new Exception('Appointment Type not created!');
            }
        } catch (Exception $e) {
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-type'), 'Something Went Wrong!', 'danger');
        }
    }

    //Edit appointment type
    public function editType($id)
    {
        $data = [];
        $data['page_title'] = __('Edit Appointment Type');
        $user_id = CRUDBooster::myId();
        $data['type'] = $type = AppointmentType::where('id', $id)->where('user_id', $user_id)->first();
        if (empty($type)) CRUDBooster::redirect(CRUDBooster::adminPath('appointment-type'), 'Appointment type not found!', 'info');
        $border = ($type->border_switch == 1) ? '2px solid' : 'none';
        $data['iframe'] = "<iframe src='".url('appointment/user-booking-form')."/".$id."/".$user_id."'  style ='border-radius:".$type->frame_radius."px;border:".$border."' height ='500' width ='100%' title='Iframe Example' > </iframe >";
        $data['existingTag'] = (!empty($type) && $type->tag) ? DropfunnelTag::where('id', $type->tag)->select('tag', 'id')->first() : null;
        $data['existingOptinalTag'] = (!empty($type) && $type->tag) ? DropfunnelTag::where('id', $type->optional_tag)->select('tag', 'id')->first() : null;

        $data['type_id'] = $id;
        $data['form_info'] = appointmentContactFormInfo($id, $user_id);
        $data['additional_fields'] = DB::table('appointment_contact_form_fields')
            ->where([
                'user_id' => $user_id,
                'type_id' => $id,
            ])
            ->orderBy('id', 'asc')
            ->get();

        return view('admin.appointment_booking.appointment_type.edit', $data);
    }


    //Update appointment type
    public function updateType(LaravelRequest $request, $id)
    {
        $request->validate([
            'name' => 'required|string',
            'description' => 'nullable|string',
            'duration' => 'required|numeric|min:0|max:180',
            'post' => 'required|numeric|min:0|max:60',
            'color' => 'nullable|string',
            'content_color' => 'required|string',
            'iframe_bg' => 'nullable|string',
            'frame_color' => 'required|string',
            'select_tag' => 'nullable|exists:dropfunnel_tags,id',
            'position' => 'nullable|numeric',
            'separator_width' => 'nullable|numeric',
            'separator_color' => 'nullable|string',
            'date_format' => 'nullable|numeric',
            'frame_radius' => 'nullable|numeric',
            'frame_padding' => 'nullable|numeric',
            'separator_show' => 'nullable|numeric',
            'separator_style' => 'nullable|numeric',
            // 'price'             => 'nullable',
            // 'pipeline_transfer' => 'boolean',
            // 'pipeline_version' => 'numeric',
            // 'redirect_to_thank' => 'boolean',
            // 'show_user' => 'boolean',
        ]);

        try {
            $type = AppointmentType::where('id', $id)->where('user_id', CRUDBooster::myId())->first(); // update type
            if (empty($type)) throw new Exception('Invalid action!');
            $type = AppointmentType::find($request->id);
            $type->name = $request->name;
            $type->description = $request->description;
            $type->duration = $request->duration;
            // $type->pre = $request->preparation;
            $type->post = $request->post;
            $type->color = $request->color;
            $type->frame_color = $request->frame_color;
            $type->position = $request->position;
            $type->content_color = $request->content_color;
            $type->iframe_bg_color_image = $request->iframe_bg_color_image ?? 0;
            $type->iframe_background = ($request->transparent != 1) ? $request->iframe_bg : null;
            $type->tag = $request->select_tag;
            $type->optional_tag = $request->optional_tag;
            $type->date_format = $request->date_format;
            $type->frame_padding = $request->frame_padding;
            $type->frame_radius = $request->frame_radius;
            $type->border_switch = $request->border_switch;
            $type->separator_show = $request->separator_show;
            $type->separator_color = $request->separator_color;
            $type->separator_width = $request->separator_width;
            $type->separator_style = $request->separator_style;
            $type->price             = $request->price ?? null;
            $type->pipeline_transfer = $request->pipeline_transfer ?? 0;
            $type->pipeline_version = (int) $request->pipeline_version ?? 1;

            $redirect_to_thank       = (bool) $request->redirect_to_thank;
            $redirect_url            = $request->redirect_url;
            $type->show_user = $request->show_user ?? 0;
            $type->widget_name = $request->widget_name;
            $type->widget_color = $request->widget_color;

            // $url_parts     = explode('.', $redirect_url);
            // $http_www_part = $url_parts[0];

            // if (!(strpos($http_www_part, 'www') !== false)) { // no www here
            //     if (strpos($redirect_url, '://') !== false) { // http here
            //         $parts        = explode('://', $redirect_url); // http, google.com
            //         $parts[0]     = 'http://www'; // no . here
            //         $redirect_url = implode('.', $parts); // add .
            //     } else { // no http, just domain
            //         array_unshift($url_parts, 'http://www');
            //         $redirect_url = implode('.', $url_parts);
            //     }
            // }

            $type->redirect_to_thank = $redirect_to_thank;
            $type->redirect_url      = $redirect_url;

            if ($request->hasFile('bg_image')) {
                $request->validate([
                    'bg_image' => 'required|mimes:png,jpg,jpeg'
                ]);
    
                $file = $request->file('bg_image');
                $serverPath = uploadImage($file, 'appointment_type');

                if (!empty($serverPath)) {
                    $type->iframe_background_image = $serverPath;
                }
            }

            if ($type->save()) {
                if (!$redirect_to_thank && !empty($redirect_url)) {
                    return redirect()->away($redirect_url);
                } else {
                    return redirect()->route('edit_thank_you_page', ['id' => $id]);
                }
            } else {
                throw new Exception('Appointment Type update failed!');
            }
        } catch (Exception $e) {
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-type'), 'Something Went Wrong!', 'danger');
        }
    }

    //delete appointment type
    public function deleteType($id)
    {
        try {
            $type = AppointmentType::where('id', $id)->where('user_id', CRUDBooster::myId())->first(); 
            $optinId = DB::table("opln_mails")->where("calendar_id", $id)->value('id');
            if($optinId){
                CRUDBooster::redirect(CRUDBooster::adminPath('appointment-type'), 'Attention!, this calendar is linked to a Dropfunnels® OptIn form. Are you sure you want to delete this calendar? If this calendar is to be deleted, we recommend you to select another calendar in the Optin form or to deactivate the form as well.<br><a href="'.url('admin/opln_mails/edit').'/'.$optinId.'">Click to remove</a>', 'info');
            }
            if ($type && $type->delete()) {
                CRUDBooster::redirect(CRUDBooster::adminPath('appointment-type'), 'Appointment type deleted', 'success');
            } else {
                throw new Exception('Invalid action!');
            }
        } catch (Exception $exception) {
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-type'), 'Failed to Delete', 'danger');
        }
    }

    public function editThankYouPage($id)
    {
        $data = [];
        $data['page_title'] = __('Edit Thank You Page');
        $type = AppointmentType::where('id', $id)->where('user_id', CRUDBooster::myId())->first();
        if ($type) {
            $data['type_id'] = $id;
            $data['content'] = $type->thank_page_content;
            return view('admin.appointment_booking.appointment_type.edit_thank_you', $data);
        } else {
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-type'), 'Invalid action', 'danger');
        }
    }

    public function update_thank_you(LaravelRequest $request)
    {
        $request->validate([
            'thank_you_content' => 'nullable|string',
        ]);
        try {
            DB::table('appointment_type')->where('id', $request->id)->update(['thank_page_content' => $request->thank_you_content]);
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-type'), 'Appointment Type Successfully Store', 'success');
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
        }
    }

    // save anonymous appointment
    public function anonymousAppointment(LaravelRequest $request){


        if($request->end <= $request->start){
            return response()->json(['success' => false, 'message' => 'End time must be greater then start time']);
        }

      try{
        $types = AppointmentType::find($request->type);
        $data[] = [
          'title' => ($request->title) ?? '',
          'start' => $request->meeting . 'T' . $request->start,
          'end' => $request->meeting . 'T' . $request->end,
          'description' => $request->note,
          'constraint' => "availableForMeeting",
          'slot' => "book",
          'type' => 4,    // 4 for anonymous appointment
          'email' => ($request->email) ?? ' ',
          'phone' => ($request->phone) ?? ' ',
          'color' => ($types->color) ? $types->color : 'gray',
          'date' => $request->meeting,
          'start_time' => $request->start,
          'end_time' => $request->end,
          'appointment_type' => $request->type
        ];
        $slug = Str::slug($request->title . Str::random(25), '-');
        $user_id = CRUDBooster::myId(); // save anonymous appointment
        $start_end_time = $this->appointStartEndTime($request->meeting, $request->start, $request->end);

        $appointment = Appointment::Create(
          [
            'user_id' => $user_id,
            'my_id' => CRUDBooster::myId(),
            'slug' => $slug,
            'appointment' => $data,
            'type'       => 4, // 4 for anonymous appointment
            'start_time' => $start_end_time['start_time'],
            'end_time'   => $start_end_time['end_time'],
            'start_at'   => $start_end_time['start_at'],
            'end_at'     => $start_end_time['end_at'],
          ]);

        $this->googleEventCreate($data, $appointment,$user_id);

        return response()->json(['success' => true,'date' => $request->meeting, 'message' => 'Appointment Create Successfully!']);
      }catch (Exception $e){
        return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
      }
    }

    // anonymous appointment delete
    public function anonymousAppointmentDelete($appointment_id)
    {
        try {
            $appointment = Appointment::where('id', $appointment_id)->first();
            $date = $appointment->appointment['0']['date'];
            if(!empty($appointment->event_id)){
            $this->googleEventDelete($appointment->event_id, $appointment, $appointment->user_id);
            }
            Appointment::where('id', $appointment_id)->where('user_id', $appointment->user_id)->delete(); // anonymous appointment delete
            return response()->json(['success' => true, 'date' => $date, 'message' => 'Appointment Delete Successfully']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something went wrong!']);
        }
    }

    // create appointment over calendar
    public function appointmentCreate(LaravelRequest $request)
    {

        // calendar view
        if($request->end <= $request->start){
            return response()->json(['success' => false, 'message' => 'End time must be greater then start time']);
        }

      try {
        if(CRUDBooster::isKeyAccount()){
          $all_key_users_data = \DRM::keyAccountAllUsersId(CRUDBooster::myParentId()) ?? [];
          $key_users_id = is_array($all_key_users_data)? array_keys($all_key_users_data) : [];
          $customer =  DB::table('cms_users')->where('id',$request->customer)->select('name','email','contact_number as phone')->first();
          $previous = DB::table('takeappointment')->where('user_id', $request->customer)->value('manager_date');
          $mentor_type = '1';    // 1 for manager mentoring type
          if($previous <= 0){
            return response()->json(['success' => false, 'message' => 'Sorry! User do not have remaining dates']);
          }
        }else{
          $customer = NewCustomer::where('id',$request->customer)->select('full_name as name','email','phone')->first();
        }

        $types = AppointmentType::find($request->type);
        $appointmentName = $types->name ?? '';
        $appointmentTitle = ($customer->name  ?? ''). ' - '.$appointmentName;

        $data[] = [
          'title' => $appointmentTitle,
          'start' => $request->meeting . 'T' . $request->start,
          'end' => $request->meeting . 'T' . $request->end,
          'description' => $request->note,
          'constraint' => "availableForMeeting",
          'slot' => "book",
          'type' => 1,    // 1 for menual appointment
          'tag_id' => $request->select_tag,    // 1 for menual appointment
          'email' => ($customer->email) ?? '',
          'phone' => ($customer->phone) ?? '',
          'name' => trim($appointmentName),
          'color' => ($types->color) ? $types->color : 'gray',
          'color' => ($types->color) ? $types->color : 'gray',
          'date' => $request->meeting,
          'start_time' => $request->start,
          'end_time' => $request->end,
          'mentor_type' => $mentor_type,
          'appointment_type' => $request->type
        ];
        $slug = Str::slug($request->input('title') . Str::random(25), '-');
        $user_id = CRUDBooster::myId(); // create appointment over calendar
        $start_end_time = $this->appointStartEndTime($request->meeting, $request->start, $request->end);

        $appointment = Appointment::Create(
          [
            'user_id' => $user_id,
            'customer_id' => ($request->customer) ? $request->customer : ' ',
            'my_id' => CRUDBooster::myId(),
            'slug' => $slug,
            'appointment' => $data,
            'type'        => 1, // 1 for manual appointment
            'mentor_type' => $mentor_type ?? null,
            'start_time'  => $start_end_time['start_time'],
            'end_time'    => $start_end_time['end_time'],
            'start_at'    => $start_end_time['start_at'],
            'end_at'      => $start_end_time['end_at'],
          ]);

            $appointmentName = $appointmentName ? " ({$appointmentName})" : '';
          $this->googleEventCreate($data, $appointment, $user_id);

            // create appointment deal over calendar
            if ($types->pipeline_transfer && !empty($appointment) && !empty($customer)) {
                $type_price = $types->price;
                
                if (!empty($type_price) && (float) $type_price > 0) {
                    $dealPayload = [
                        'source'       => 'calendar',
                        'product_name' => $appointmentTitle . ' - ' . $type_price . ' EUR', 
                        'deal_title'   => $appointmentTitle . ' - ' . $type_price . ' EUR',
                        'deal_price'   => $type_price,
                        'stage_id'     => 3,
                        // 'customer_id'  => $request->customer ?? null,
                        'email_addr'   => ($customer->email) ?? '',
                        'contact_name' => ($customer->name) ?? '',
                        'user_id'      => $user_id,
                        'appointment_id' => $appointment->id ?? null,
                    ];
                    // app('App\Http\Controllers\AdminPipelineController')->autoTypeDealCreation($dealPayload);
                }
            }

            $deal_id = $request->deal_id ?? 0;
            if (!empty($deal_id)) {
                DB::table('offer_deal')->where('id', $deal_id)->where('user_id', CRUDBooster::myParentId())->update([
                    'appointment_id' => $appointment->id ?? null,
                ]);

                \App\DealHistory::create([
                    'deal_id' => $deal_id,
                    'message' => __('Appointment added from Deal details view'),
                ]);
            }

            $user_info = User::find(CRUDBooster::myId());
            if(CRUDBooster::isKeyAccount()){
              $message = 'User get approintment ' . $request->meeting . ' from ' . $request->start . ' - ' . $request->end.' By key manager '.$user_info->name;
              $this->decrimentAppointmentHistory($request->customer, $message, 1 );
            }

          //insert tag
          $tag_name = DropfunnelTag::select('tag')->find($request->select_tag);
          DropfunnelCustomerTag::insertTag($tag_name->tag, CRUDBooster::myId(), $request->customer, 17);

          // send notification
          if(User::find($request->customer)){
            User::find($request->customer)->notify(new DRMNotification('You have new appointment with ' . $user_info->name . ' on ' . $request->meeting . ' ' . $request->start . $appointmentName, 'APPOINTMENT_BOOKING', url('/') . '/admin/appointment_booking'));
          }
          //send booking mail
          $this->sendAppointmentEmail($appointment);
          return response()->json(['success' => true,'date' => $request->meeting, 'message' => 'Appointment Create Successfully!']);
        } catch (Exception $e) {
          return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
        }
      }

    public function appointmentUpdate(LaravelRequest $request)
    {


      try {
        $slots = explode('-', $request->slot);
        $appointment = Appointment::find($request->id);
        if(empty($appointment)) throw new \Exception('Invalid');
//        if(trim($slots['1']) <= trim($slots['0'])){
//          return response()->json(['success' => false, 'message' => 'End time must be greater then start time']);
//        }
        $new_appiontment[] = [
          'title' => ($request->name) ?? '',
          'start' => $request->date . 'T' . trim($slots['0']),
          'end' => $request->date . 'T' . trim($slots['1']),
          'description' => $appointment->appointment[0]['note'],
          'constraint' => "availableForMeeting",
          'slot' => "book",
          'type' => $request->booking_type,
          'email' => ($request->email) ?? '',
          'phone' => ($request->phone) ?? '',
          'color' => ($appointment->appointment[0]['color']) ? $appointment->appointment[0]['color'] : 'gray',
          'date' => $request->date,
          'start_time' => trim($slots['0']),
          'end_time' => trim($slots['1']),
          'post' => ($appointment->appointment[0]['post']) ?? 0,
        ];

        $appoint_post   = ($appointment->appointment[0]['post']) ?? 0;
        $start_end_time = $this->appointStartEndTime($request->date, trim($slots['0']), trim($slots['1']), 0, $appoint_post);

        Appointment::where('id', $request->id)->where('user_id', $appointment->user_id)->update(
          [
            'appointment' => $new_appiontment,
            'start_time'  => $start_end_time['start_time'],
            'end_time'    => $start_end_time['end_time'],
            'start_at'    => $start_end_time['start_at'],
            'end_at'      => $start_end_time['end_at'],
          ]);
          $updatedAppointment = Appointment::find($request->id);

          //update google event
          $this->googleEventUpdate($updatedAppointment->event_id, $appointment, $new_appiontment);

          $this->sendAppointmentEmail($updatedAppointment);
          return response()->json(['success' => true, 'message' => 'Appointment Update Successfully!','date' => $request->date]);
        } catch (Exception $e) {
          return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
        }
      }

    public function menualAppointmentUpdate(LaravelRequest $request)
    {
        if($request->end_time <= $request->start_time){
            return response()->json(['success' => false, 'message' => 'End time must be greater then start time']);
        }

        try {
            $slots = explode('-', $request->slot);
            $appointment = Appointment::find($request->id);
            $appointment_type = AppointmentType::find($request->type);
            $new_appiontment[] = [
                'title' => ($request->name) ?? '',
                'start' => $request->booking_date . 'T' . trim($request->start_time),
                'end' => $request->booking_date . 'T' . trim($request->end_time),
                'description' => $request->note,
                'constraint' => "availableForMeeting",
                'slot' => "book",
                'type' => ($request->email)? 1 : 4 ,  // for menual booking
                'email' => ($request->email) ?? '',
                'phone' => ($request->phone) ?? '',
                'color' => ($appointment_type->color) ? $appointment_type->color : 'gray',
                'date' => $request->booking_date,
                'start_time' => trim($request->start_time),
                'end_time' => trim($request->end_time),
            ];

            $start_end_time = $this->appointStartEndTime($request->booking_date, trim($request->start_time), trim($request->end_time));

            Appointment::where('id', $request->id)->where('user_id', $appointment->user_id)->update(
                [
                    'appointment' => $new_appiontment,
                    'start_time'  => $start_end_time['start_time'],
                    'end_time'    => $start_end_time['end_time'],
                    'start_at'    => $start_end_time['start_at'],
                    'end_at'      => $start_end_time['end_at'],
                ]);
            $updated_apointment = Appointment::find($request->id);

            //googel event update
            $this->googleEventUpdate($updated_apointment->event_id, $appointment, $new_appiontment);

            if($request->email){
              $this->sendAppointmentEmail($updated_apointment);
            }
            return response()->json(['success' => true, 'message' => 'Appointment Update Successfully!','date' => $request->booking_date]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
        }
    }

    public function dragAppointmentUpdate($start, $end, $start_date, $end_date, $id){
      // dd($start,$end,$date,$id);
      try {
            $user_info = User::find(CRUDBooster::myId());
            $appointment = Appointment::find($id);
            $appointmentName = ($appointment->appointment['0']['name']) ?? '';
            $appointmentTitle = ($appointment->appointment['0']['title'] ?? '').' - '.$appointmentName;

            $new_appiontment[] = [
              'title' => $appointmentTitle,
              'start' => $start_date . 'T' . trim($start),
              'end' => $end_date . 'T' . trim($end),
              'description' => $appointment->appointment['0']['note'],
              'constraint' => "availableForMeeting",
              'slot' => $appointment->appointment['0']['slot'],
              'type' => ($appointment->appointment['0']['type'])? 1 : 4 ,  // for menual booking
              'email' => ($appointment->appointment['0']['email']) ?? '',
              'name' => trim($appointmentName),
              'phone' => ($appointment->appointment['0']['phone']) ?? '',
              'color' => ($appointment->appointment['0']['color']) ? $appointment->appointment['0']['color'] : 'gray',
              'date' => $start_date,
              'start_time' => trim($start),
              'end_time' => trim($end),
            ];

            $start_end_time = $this->appointStartEndTime($start_date, trim($start), trim($end), 0, 0, $end_date);

            Appointment::where('id', $id)->where('user_id', $appointment->user_id)->update(
              [
                'appointment' => $new_appiontment,
                'status' => $appointment->status,
                'start_time' => $start_end_time['start_time'],
                'end_time'   => $start_end_time['end_time'],
                'start_at'   => $start_end_time['start_at'],
                'end_at'     => $start_end_time['end_at'],
              ]);
               $google_event_id  = Appointment::where('id',$id)->value('event_id');
              //googel event update
              $this->googleEventUpdate($google_event_id, $appointment, $new_appiontment);

              $appointmentName = $appointmentName ? " ({$appointmentName})" : '';

              // Old appointment delete
              if(User::find($appointment->customer_id)){
                User::find($appointment->customer_id)->notify(new DRMNotification('Your appointment has been cancelled by ' . $user_info->name . ' of ' . $appointment->appointment[0]['date'] . ' ' . $appointment->appointment[0]['start_time'] . $appointmentName, 'APPOINTMENT_BOOKING', url('/') . '/admin/appointment_booking'));
              }

                $parsedData = DRMParseAppointmentCancelEmailTemplate($appointment);
                app('drm.mailer')->getMailer($appointment->user_id,$parsedData['senderEmail'])->send('admin.new_order.email_invoice_template', ['body' => $parsedData['body']], function ($messages) use ($parsedData) {
                //   $messages->from($parsedData['senderEmail']);
                  $messages->to($parsedData['toEmail']);
                  $messages->subject($parsedData['subject']);
                  if (!empty($parsedData['bcc'])) {
                    $messages->bcc($parsedData['bcc']);
                  }
                });

              // New Appointment comfirmation
              $manager_name = User::where('id', $appointment->user_id)->value('name');
              if(User::find($appointment->customer_id)){
                User::find($appointment->customer_id)->notify(new DRMNotification('You have new appointment with ' .$manager_name . ' on ' . $start_date . ' ' . $start . $appointmentName, 'APPOINTMENT_BOOKING', url('/') . '/admin/appointment_booking'));
              }
              //send booking mail
              $this->sendAppointmentEmail(Appointment::find($id));

          return response()->json(['success' => true, 'message' => 'Appointment Update Successfully!','date' => $start_date]);
        } catch (Exception $e) {
          return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
        }
      }

    public function appointmentDelete($appointment_id, $flag = '')
    {
        try {
            if ($flag == 'user') {
                $condition_column = 'customer_id';
            } else {
                $condition_column = 'user_id';
            }
            $appointment = Appointment::where('id', $appointment_id)->first();
            $compareTime = Carbon::parse(Carbon::tomorrow()->format('Y-m-d') . ' ' . Carbon::now()->format('G:i'));
            $appointmentTime = Carbon::parse($appointment->appointment[0]['date'] . ' ' . $appointment->appointment[0]['start_time']);
            $date = $appointment->appointment[0]['date'];
            $mentor_type = $appointment->appointment[0]['mentor_type'];
            // return remaining date to user account after cancel booking before 1 day left
            if ($appointmentTime->gt($compareTime)) {
                if (DB::table('takeappointment')->where('user_id', $appointment->customer_id)->exists()) {
                    $message = 'Appointment canceled on ' . now() . '. The appointment was ' .$date . ' from ' . $appointment->appointment[0]['start_time'] . ' - ' . $appointment->appointment[0]['end_time'];
                    $this->incrementAppointmentHistory($appointment->customer_id, $message, $mentor_type );
                }
            }
//            if(isLocal ()){
//                if ($flag != 'user') {
//                    $manager_name = User::where('id', $appointment->user_id)->value('name');
//                    $user_exits = user::where('id',$appointment->customer_id)->exists();
//                    if($user_exits){
//                        User::find($appointment->customer_id)->notify(new DRMNotification('Your appointment has been cancelled by ' . $manager_name . ' of ' . $appointment->appointment[0]['date'] . ' ' . $appointment->appointment[0]['start_time'], 'APPOINTMENT_BOOKING', url('/') . '/admin/appointment_booking'));
//                    }
//                    if(!empty($appointment->appointment[0]['email'] != " ")){
//                        $parsedData = DRMParseAppointmentCancelEmailTemplate($appointment);
//                        Mail::send('admin.new_order.email_invoice_template', ['body' => $parsedData['body']], function ($messages) use ($parsedData) {
//                            $messages->from($parsedData['senderEmail']);
//                            $messages->to($parsedData['toEmail']);
//                            $messages->subject($parsedData['subject']);
//                            if (!empty($parsedData['bcc'])) {
//                                $messages->bcc($parsedData['bcc']);
//                            }
//                        });
//                    }
//                }
//                Appointment::where('id', $appointment_id)->where($condition_column, CRUDBooster::myId())->delete();
//            }
            if(!empty($appointment->event_id)){

                $this->googleEventDelete($appointment->event_id, $appointment, $appointment->user_id);
            }
            Appointment::where('id', $appointment_id)->delete();
            if ($flag != 'user') {
                $manager_name = User::where('id', $appointment->user_id)->value('name');
                $user_exits = user::where('id',$appointment->customer_id)->exists();
                if($user_exits){
                    User::find($appointment->customer_id)->notify(new DRMNotification('Your appointment has been cancelled by ' . $manager_name . ' of ' . $appointment->appointment[0]['date'] . ' ' . $appointment->appointment[0]['start_time'], 'APPOINTMENT_BOOKING', url('/') . '/admin/appointment_booking'));
                }
            }
            // Appointment::where('id', $appointment_id)->where($condition_column, CRUDBooster::myId())->delete();

            if ($flag != 'user') {
                if(!empty($appointment->appointment) && $appointment->appointment[0]['email'] != " "){
                    $parsedData = DRMParseAppointmentCancelEmailTemplate($appointment);
                    app('drm.mailer')->getMailer($appointment->user_id,$parsedData['senderEmail'])->send('admin.new_order.email_invoice_template', ['body' => $parsedData['body']], function ($messages) use ($parsedData) {
                        // $messages->from($parsedData['senderEmail']);
                        $messages->to($parsedData['toEmail']);
                        $messages->subject($parsedData['subject']);
                        if (!empty($parsedData['bcc'])) {
                            $messages->bcc($parsedData['bcc']);
                        }
                    });
                }
            }

            return response()->json(['success' => true, 'date' => $date, 'message' => 'Appointment Delete Successfully']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something went wrong!']);
        }
    }

    // backend: calendar view
    public function mentoring()
    {
        $data = [];
        $data['page_title'] = 'Calender Booking';
        $user_id = CRUDBooster::myId(); // mentoring
        $my_id = CRUDBooster::myId();

        $appointments = Appointment::where('customer_id', $user_id)->select('id', 'appointment', 'user_id', 'my_id')->get();
        $data['appointment_days'] = AppointmentBooking::where('user_id', $user_id)->pluck('user_id')->toArray() ?? [];
        $data['user_id'] = $user_id;
        $data['appointment_types'] = AppointmentType::where('user_id', $user_id)->get();

        $appointment_array = [];
        foreach ($appointments as $app) {
            $user_info = User::where('id', $app->user_id)->select('name', 'email', 'contact_number')->first();
            foreach ($app->appointment as $value) {
                $temp = [];
                $temp['title'] = $value['title'];
                $temp['start'] = $value['start'];
                $temp['end'] = $value['end'];
                $temp['color'] = $value['color'];
                $temp['slot'] = $value['slot'];
                $temp['phone'] = $value['phone'];
                $temp['email'] = $value['email'];
                $temp['slug'] = $app->slug;
                $temp['type'] = 2;        // 2 for mentoring appointment
                $temp['status'] = $app->status;
                $temp['appointment_id'] = $app->id;
            }
            if ($app->booking_info != null) {
                $temp['title'] = $value['title'];
                $temp['email'] = $value['email'];
                $temp['phone'] = $value['phone'];
            }
            array_push($appointment_array, $temp);
        }
        $data['events'] = $appointment_array;

        $data['time_slots'] = $this->timeSlot();
        $data['iframe_url'] = url('/appointment/booking-form');
        return view('admin.appointment_booking.mentoring', $data);
    }

    public function bookingForm($slug)
    {
        $data = [];
        $data['appointment'] = DB::table('appointments')->where('slug', $slug)->first();
        return view('admin.appointment_booking.booking.booking_form', $data);
    }

    public function ThankYou()
    {

        return view('admin.appointment_booking.booking.thank_you');
    }

    public function NewUserThankYou()
    {

        return view('admin.appointment_booking.booking.new_user_thank_you');
    }

    public function festival(){
       $data['page_title'] = __('Festival Event');
       $data['festival_events'] = Appointment::where('status', '1')->where('user_id', CRUDBooster::myId())->orderby('id','desc')->get();
      return view('admin.appointment_booking.festival.index', $data);
    }

    public function saveFestival(LaravelRequest $request){

      $request->validate([
          'title' => 'required|string',
          'start_date' => 'required|date',
          'end_date' => 'required|date|after_or_equal:start_date',
          'start_time' => 'required|date_format:H:i',
          'end_time' => 'required|date_format:H:i',
          'color' => 'nullable|string',
      ]);

      try {

        $data[] = [
            'title' => $request->title,
            'start' => $request->start_date . 'T' . $request->start_time,
            'end' => $request->end_date . 'T' . $request->end_time,
            'description' => '',
            'constraint' => "availableForMeeting",
            'slot' => "festival",
            'type' => 6,    //6 for festival events
            'color' => $request->color,
            'date' => $request->date,
            'start_time' => $request->start_time,
            'end_time' => $request->end_time,
        ];
        $slug = Str::slug($request->input('title') . Str::random(25), '-');
        $user_id = crudbooster::myId(); // save festival
        $start_end_time = $this->appointStartEndTime($request->start_date, $request->start_time, $request->end_time, 0, 0, $request->end_date);

        $appointment = Appointment::Create(
            [
                'user_id' => $user_id ,
                'my_id' => CRUDBooster::myId(),
                'slug' => $slug,
                'appointment' => $data,
                'status' => 1,
                'type'       => 6, // 6 for festival events
                'start_time' => $start_end_time['start_time'],
                'end_time'   => $start_end_time['end_time'],
                'start_at'   => $start_end_time['start_at'],
                'end_at'     => $start_end_time['end_at'],
            ]);
          $this->googleEventCreate($data, $appointment, $user_id);

      CRUDBooster::redirect(CRUDBooster::adminPath('festival'), 'Festival event save ', 'success');
      } catch (Exception $exception) {
          CRUDBooster::redirect(CRUDBooster::adminPath('festival'), 'Failed to Delete', 'danger');
      }
    }

    public function editFestival($id){
       $data['page_title'] = 'Festival Event';
       $data['festival_info'] = Appointment::where('id', $id)->where('user_id', CRUDBooster::myId())->first();
       $data['festival_events'] = Appointment::where('status', '1')->where('user_id', CRUDBooster::myId())->orderby('id','desc')->get();
      return view('admin.appointment_booking.festival.edit', $data);
    }


        public function updateFestival(LaravelRequest $request){

          $request->validate([
              'title' => 'required|string',
              'start_date' => 'required|date',
              'start_time' => 'required|date_format:H:i',
              'end_time' => 'required|date_format:H:i',
              'end_date' => 'required|date|after_or_equal:start_date',
              'color' => 'nullable|string',
          ]);

          try {
            $new_appointment[] = [
                'title' => $request->title,
                'start' => $request->start_date . 'T' . $request->start_time,
                'end' => $request->end_date . 'T' . $request->end_time,
                'description' => '',
                'constraint' => "availableForMeeting",
                'slot' => "festival",
                'type' => 6,    //6 for festival events
                'color' => $request->color,
                'date' => $request->date,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
            ];
            $slug = Str::slug($request->input('title') . Str::random(25), '-');

            $start_end_time = $this->appointStartEndTime($request->start_date, $request->start_time, $request->end_time, 0, 0, $request->end_date);

            // update festival
            Appointment::where('id',$request->id)
                ->where('user_id', CRUDBooster::myId())
                ->update(
                [
                    'appointment' => $new_appointment,
                    'start_time'  => $start_end_time['start_time'],
                    'end_time'    => $start_end_time['end_time'],
                    'start_at'    => $start_end_time['start_at'],
                    'end_at'      => $start_end_time['end_at'],
                ]);
              $appointment = Appointment::find($request->id);
              $this->googleEventUpdate($appointment->event_id, $appointment, $new_appointment);
            return back()->with('success', 'Update festival event!');
          } catch (Exception $exception) {
      return back()->with('danger', 'Something went wrong!');
          }
        }


    public function deleteFestival($id){
      try {
          $type = Appointment::where('id', $id)->where('user_id', CRUDBooster::myId())->first();
          if(!empty($type->event_id)){
              $this->googleEventDelete($type->event_id, $type, $type->user_id);
          }
          if ($type && $type->delete()) {
              CRUDBooster::redirect(CRUDBooster::adminPath('festival'), 'Festival event deleted', 'success');
          } else {
              throw new Exception('Invalid action!');
          }
      } catch (Exception $exception) {
          CRUDBooster::redirect(CRUDBooster::adminPath('festival'), 'Failed to Delete', 'danger');
      }
    }

    // frontend: admin/user-appointment-booking
    public function userBooking()
    {
        $mentor_enable = app('\App\Http\Controllers\AdminSubAccountController')->isMentorActive(CRUDBooster::myParentId(),'mentor');
        if(!$mentor_enable){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'You do not have access permission', 'danger');
        }
        $data = [];
        $data['page_title'] = __('Mentoring Appointment Booking');
        $data['available_slots'] = AppointmentBooking::where('user_id', 98)->first();
        $data['remaining_date'] = DB::table('takeappointment')->where('user_id', CRUDBooster::myParentId())->first();
        $data['off_days'] = $this->getOffDaysInWeek(98);
        return view('admin.appointment_booking.booking.user_booking', $data);
    }

    // frontend: admin/manager-appointment-booking
    public function managerBooking()
    {
        
        $data = [];
        $data['type'] = request()->type;
        $user_id = CRUDBooster::myParentId();
        $manager_id = app('\App\Http\Controllers\AdminSubAccountController')->userAppointmentManagerId($user_id);
        $data['available_slots'] = AppointmentBooking::where('user_id', $manager_id)->first();
        $remaining_date = DB::table('takeappointment')->where('user_id', $user_id)->first();
        if(request()->type == "manager"){
            $manager_assign = app('\App\Http\Controllers\AdminSubAccountController')->userHasManagerId(CRUDBooster::myParentId());
            $manager_enable = app('\App\Http\Controllers\AdminSubAccountController')->isMentorActive(CRUDBooster::myParentId(),'manager');
            if(!$manager_assign || !$manager_enable){
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'You do not have access permission', 'danger');
            }
            $data['page_title'] = __('Manager Appointment Booking');
            $data['remaining_date'] = $remaining_date->manager_date;
        }elseif (request()->type == "Onboarding"){
            $data['page_title'] = __('Free Appointment Booking');
            $data['remaining_date'] = $remaining_date->free_date;
        }else{
            $data['page_title'] = __('Mentoring Appointment Booking');
            $data['remaining_date'] = $remaining_date->payment_date_remaining;
        }

        $data['off_days'] = $this->getOffDaysInWeek($manager_id);

        if (request()->ajax()) {
            $content = view('admin.appointment_booking.booking.manager_booking_onboarding', compact('data'))->render();

            return response()->json([
                'success' => true,
                'data'    => $content, 
            ], 200);
        }

        return view('admin.appointment_booking.booking.manager_booking', $data);
    }

    // frontend
    public function getDefultAvailableSlots(LaravelRequest $request)
    {

        $validated = $request->validate([
            'day' => 'required|integer',
            'date' => 'required|date',
        ]);

        try {
            $free_slots = ' ';
            $free_date = ' ';
            $remaining_dates = 0;
            $plan_url = ' ';
            for ($i = 0; $i <= 31; $i++) {
                $date = Carbon::now()->addDays($i)->format('Y-m-d');
                $dayOfWeek = Carbon::now()->addDays($i)->dayOfWeek;


                $remaining_date = DB::table('takeappointment')->where('user_id', CRUDBooster::myParentId())->select('payment_date_remaining','manager_duration','duration','manager_date','free_date')->first();

//                $duration = 30;
//                $duration = (!empty($remaining_date->duration && $remaining_date->duration > 0)) ? $remaining_date->duration : 90 ;
//                if(!$manager && !CRUDBooster::isKeyAccount()){
//                    $duration = 45;
//                }

                if($request->type == 'manager'){
                    $user_id = app('\App\Http\Controllers\AdminSubAccountController')->userAppointmentManagerId(CRUDBooster::myParentId());
                    $duration = (!empty($remaining_date->manager_duration && $remaining_date->manager_duration > 0)) ? $remaining_date->manager_duration : 45 ;
                    $remaining_dates = $remaining_date->manager_date;
                    $plan_url = 'admin/appointment-plan/?type=manager';
                }elseif($request->type == 'Onboarding'){
                    $user_id = app('\App\Http\Controllers\AdminSubAccountController')->defaultBookingManager();
                    $duration = (!empty($remaining_date->manager_duration && $remaining_date->manager_duration > 0)) ? $remaining_date->manager_duration : 45 ;
                    $remaining_dates = $remaining_date->free_date;
                    $plan_url = 'admin/appointment-plan/?type=manager';
                }else{
                    $user_id = 98;
                    $duration = (!empty($remaining_date->duration && $remaining_date->duration > 0)) ? $remaining_date->duration : 90 ;
                    $remaining_dates = $remaining_date->payment_date_remaining;
                    $plan_url = 'admin/appointment-plan/?type=mentoring';
                }

                $free_appointment = $this->get_available_slots_on_duration($dayOfWeek, $date, $user_id, $duration);
                // $free_appointment = $this->get_available_slots($dayOfWeek, $date);
                if (!empty($free_appointment)) {
                    $free_slots = $free_appointment;
                    $free_date = $date;
                    break;
                }
            }

            if (!empty($free_slots)) {
               $html = '';
                foreach ($free_slots as $slot) {
                    if ($remaining_dates <= 0) {
                        $html .= '<a class="slot_booking" id="booking_limit"  href="' . url($plan_url) . '">' . $slot . '</a>';
                    } else {
                        $html .= '<a id="booking_limit" data-type="'.$request->type.'" data-date="'.$free_date.'" href="' . url('admin/appointment/preferred-slot/') . '/' . $slot . '/' . $free_date . '?type='.$request->type.'">' . $slot . '</a>';
                    }
                }
                return response()->json(['success' => true, 'message' => 'Free Time ', 'date' => $date, 'data' => $html, 
                    'off_days' => $this->getOffDaysInWeek($user_id)
                ]);

            } else {
                return response()->json(['success' => false, 'message' => '<h4>' . __('No Free Appointments on This Day!') . '</h4>', 'date' => $request->date, 'data' => '']);
            }
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => '<p>Something Went Wrong!</p>', 'data' => '', 'error' => $e]);
        }
    }

    public function getAllAvailableSlots(LaravelRequest $request)
    {

        $validated = $request->validate([
            'date' => 'required|date',
            'appointment_id' => 'required',
        ]);
        try {
            $appointment = Appointment::where('id', $request->appointment_id)->select('id', 'customer_id', 'user_id')->first();
            $dayOfWeek = Carbon::createFromFormat('Y-m-d', $request->date)->dayOfWeek;
            $date = $request->date;
            $free_slots = $this->get_available_slots_on_duration($dayOfWeek, $date,$appointment->user_id);
            // $free_slots = $this->get_available_slots($dayOfWeek, $date);
            if (!empty($free_slots)) {
                $remaining_date = DB::table('takeappointment')->where('user_id', $appointment->customer_id)->select('payment_date_remaining')->first();
                $html = '';
                foreach ($free_slots as $key => $slot) {
                    if (!empty($remaining_date) && $remaining_date->payment_date_remaining <= 0) {
                        $html .= '<a class="slot_booking"  href="' . url('admin/appointment-plan') . '">' . $slot . '</a>';
                    } else {
                        $html .= '<a class="free_slot" id="slot' . $key . '" href="#" data-date="' . $date . '" data-slot="' . $slot . '"=>' . $slot . '</a>';
                    }
                }
                return response()->json(['success' => true, 'message' => 'Free Time ', 'date' => $request->date, 'data' => $html]);

            } else {
                return response()->json(['success' => false, 'message' => '<h4>' . __('No Free Appointments on This Day!') . '</h4>', 'date' => $request->date, 'data' => '']);
            }
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => '<p>Something Went Wrong!</p>', 'data' => '']);
        }
    }

    public function getDefaultAllAvailableSlots($booking_type,$appointment_id)
    {
        try {
            $free_slots = '';
            $free_date = '';
            $appointment = Appointment::where('id', $appointment_id)->select('id', 'customer_id', 'user_id')->first();
            // if($booking_type == 3){
            //     $user_id = CRUDBooster::myParentId();
            // }else{
            //     $user_id = 98;
            // }

            for ($i = 0; $i <= 31; $i++) {
                $date = Carbon::now()->addDays($i)->format('Y-m-d');
                $dayOfWeek = Carbon::now()->addDays($i)->dayOfWeek;
                $free_appointment = $this->get_available_slots_on_duration($dayOfWeek, $date, $appointment->user_id);
                // $free_appointment = $this->get_available_slots($dayOfWeek, $date, $user_id);
                if (!empty($free_appointment)) {
                    $free_slots = $free_appointment;
                    $free_date = $date;
                    break;
                }
            }

            if (!empty($free_slots)) {
                $remaining_date = DB::table('takeappointment')->where('user_id', $appointment->customer_id)->select('payment_date_remaining')->first();
                $html = '';
                foreach ($free_slots as $key => $slot) {
                    if ($remaining_date->payment_date_remaining <= 0) {
                        $html .= '<a class="slot_booking" href="' . url('admin/appointment-plan') . '">' . $slot . '</a>';
                    } else {
                        $html .= '<a class="free_slot" id="slot' . $key . '" href="#" data-date="' . $free_date . '" data-slot="' . $slot . '"=>' . $slot . '</a>';
                    }
                }
                return response()->json(['success' => true, 'message' => 'Free Time ', 'date' => $free_date, 'data' => $html]);

            } else {
                return response()->json(['success' => false, 'message' => '<h4>' . __('No Free Appointments on This Day!') . '</h4>', 'date' => $free_date, 'data' => '']);
            }
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => '<p>Something Went Wrong!</p>', 'data' => '']);
        }
    }

    public function getSlotTime($start_time)
    {
        $time = explode(':', $start_time);
        $startFrom = $time['0'];
        if ((int)$time['1'] == 30) {
            $startFrom = ($time['0'] . ".5");
        }
        $slots = $this->getTimeSlot($startFrom);
        $html = '';
        foreach ($slots as $key => $value) {
            $html .= '<option value="' . $value . '">' . $value . '</option>';
        }

        if ($slots) {
            return response()->json(['success' => true, 'message' => 'Free Time ', 'data' => $html]);
        } else {
            return response()->json(['success' => false, 'message' => 'Something went wrong!', 'data' => '']);
        }
    }

    public function getAvailableSlots(LaravelRequest $request)
    {

        $validated = $request->validate([
            'day' => 'required|integer',
            'date' => 'required|date',
        ]);

        try {

//            $manager_id = app('\App\Http\Controllers\AdminSubAccountController')->userAppointmentManagerId(CRUDBooster::myParentId());
//            $manager = app('\App\Http\Controllers\AdminSubAccountController')->userHasManagerId(CRUDBooster::myParentId());
            $remaining_date = DB::table('takeappointment')->where('user_id', CRUDBooster::myParentId())->select('payment_date_remaining', 'manager_duration','duration','manager_date','free_date')->first();

////            $duration = 30;
//            $duration = (!empty($remaining_date->duration && $remaining_date->duration > 0)) ? $remaining_date->duration : 90 ;
//            if(!$manager && !CRUDBooster::isKeyAccount()){
//                  $duration = 45;
//            }


            if($request->type == 'manager'){
                $user_id = app('\App\Http\Controllers\AdminSubAccountController')->userAppointmentManagerId(CRUDBooster::myParentId());
                $duration = (!empty($remaining_date->manager_duration && $remaining_date->manager_duration > 0)) ? $remaining_date->manager_duration : 45 ;
                $remaining_dates = $remaining_date->manager_date;
                $plan_url = 'admin/appointment-plan/?type=manager';
            }elseif($request->type == 'Onboarding'){
                $user_id = app('\App\Http\Controllers\AdminSubAccountController')->defaultBookingManager();
                $duration = (!empty($remaining_date->manager_duration && $remaining_date->manager_duration > 0)) ? $remaining_date->manager_duration : 45 ;
                $remaining_dates = $remaining_date->free_date;
                $plan_url = 'admin/appointment-plan/?type=manager';
            }else{
                $user_id = 98;
                $duration = (!empty($remaining_date->duration && $remaining_date->duration > 0)) ? $remaining_date->duration : 90 ;
                $remaining_dates = $remaining_date->payment_date_remaining;
                $plan_url = 'admin/appointment-plan/?type=mentoring';
            }

            $free_slots = $this->get_available_slots_on_duration($request->day, $request->date, $user_id, $duration);
            if (!empty($free_slots)) {
                $html = '';
                foreach ($free_slots as $slot) {
                    if ($remaining_dates <= 0) {
                        $html .= '<a class="slot_booking" id="booking_limit" href="' . url($plan_url) . '">' . $slot . '</a>';
                    } else {
                        $html .= '<a  id="booking_limit" data-date="'.$request->date.'"  data-type="'.$request->type.'" href="' . url('admin/appointment/preferred-slot/') . '/' . $slot . '/' . $request->date .'?type='.$request->type.'">' . $slot . '</a>';
                    }
                }
                return response()->json(['success' => true, 'message' => 'Free Time ', 'date' => $request->date, 'data' => $html]);

            } else {
                return response()->json(['success' => false, 'message' => '<h4>' . __('No Free Appointments on This Day!') . '</h4>', 'date' => $request->date, 'data' => '']);
            }
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => '<p>Something Went Wrong!</p>', 'data' => '']);
        }
    }

    //link booking

    public function booking($slug){
        $type = AppointmentType::where('slug', $slug)->first();
        $data = [];
        $data['type_id'] = $type->id;
        $data['user_id'] = $type->user_id;
        $background_color = $type->iframe_background;
        if(empty($background_color)){
            $background_color = '#fff0';
        }
        ($type->position == 0)?  $width = 'horizontal' :   $width = 'vertical';
        $data['width'] = $width;
        $data['background_color'] = $background_color;
        $data['background_image'] = ($type->iframe_bg_color_image == 1) ? $type->iframe_background_image : '';
        $data['content_color'] = $type->content_color;
        $data['frame_color'] = $type->frame_color;
        $data['frame_data'] = $type;
        $data['available_slots'] = AppointmentBooking::where('user_id',$type->user_id)->first();
        $data['off_days'] = $this->getOffDaysInWeek($type->user_id);

        $user_info = null;
        $show_user = DB::table('appointment_type')->where('id', $type->id)->value('show_user');
        if(!empty($show_user)) {
          $user_info = \App\User::find(CRUDBooster::myId());
        }

        $data['user_info'] = $user_info;
        $data['form_info'] = appointmentContactFormInfo($type->id, $type->user_id);
        return view('admin.appointment_booking.booking.link_booking', $data);
    }

    // new user start........
    public function NewUserBookingForm($type_id, $user_id)
    {

        $data = [];
        $data['type_id'] = $type_id;
        $data['user_id'] = $user_id;
        $color = DB::table('appointment_type')->where('id', $type_id)->first();
        $background_color = $color->iframe_background;
        if(empty($background_color)){
            $background_color = '#fff0';
        }
        ($color->position == 0)?  $width = 'horizontal' :   $width = 'vertical';
        $data['width'] = $width;
        $data['background_color'] = $background_color;
        $data['background_image'] = ($color->iframe_bg_color_image == 1) ? $color->iframe_background_image : '';
        $data['content_color'] = $color->content_color;
        $data['frame_color'] = $color->frame_color;
        $data['frame_data'] = $color;
        $data['available_slots'] = AppointmentBooking::where('user_id',$user_id)->first();
        $data['off_days'] = $this->getOffDaysInWeek($user_id);

        $user_info = null;
        $show_user = DB::table('appointment_type')->where('id', $type_id)->value('show_user');
        if(!empty($show_user)) {
          $user_info = \App\User::find(CRUDBooster::myId());
        }

        $data['user_info'] = $user_info;
        $data['form_info'] = appointmentContactFormInfo($type_id, $user_id);
        return view('admin.appointment_booking.booking.new_user_booking', $data);
    }

    // backend: appointment type edit type new user booking
    public function userDefaultAvailableSlots($day, $today, $type_id, $user_id)
    {

       try {
            $free_slots = '';
            $free_date = ' ';
            $type =  AppointmentType::where('id',$type_id)->first();
            $duration = $type->duration;
            for ($i = 0; $i <= 31; $i++) {
                $date = Carbon::now()->addDays($i)->format('Y-m-d');

                $dayOfWeek = Carbon::now()->addDays($i)->dayOfWeek;
                $free_appointment = $this->get_available_slots_on_duration($dayOfWeek, $date, $user_id,$duration);
                // $free_appointment = $this->get_available_slots($dayOfWeek, $date, $user_id);

                if (!empty($free_appointment)) {
                    $free_slots = $free_appointment;
                    $free_date = $date;
                    break;
                }
            }

            if (!empty($free_slots)) {
                $html = '';
                foreach ($free_slots as $slot) {
                    $html .= '<a id="booking_slot" href="' . url('/appointment/user-preferred-slot/') . '/' . $slot . '/' . $free_date . '/' . $type_id . '/' . $user_id . '">' . $slot . '</a>';
                }

                return response()->json(['success' => true, 'message' => 'Free Time ', "date_format" => $type->date_format ,'date' => $date, 'data' => $html,
                    'off_days' => $this->getOffDaysInWeek($user_id)
                ]);
            } else {
                return response()->json(['success' => false, 'message' => '<h4>' . __('No Free Appointments on This Day!') . '</h4>', 'date' => $today, "date_format" => $type->date_format, 'data' => '']);
            }
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => '<p>Something Went Wrong!</p>', 'data' => '']);
        }


    }

    public function userGetAvailableSlots($day, $today, $type_id, $user_id)
    {

      try {
        $type =  AppointmentType::where('id',$type_id)->first();
        $duration = $type->duration;
        $free_slots = $this->get_available_slots_on_duration($day, $today, $user_id,$duration);
        // $free_slots = $this->get_available_slots($day, $today, $user_id,$slot_time);
        if (!empty($free_slots)) {
          $html = '';
          foreach ($free_slots as $slot) {
            $html .= '<a id="booking_slot" href="' . url('/appointment/user-preferred-slot') . '/' . $slot . '/' . $today . '/' . $type_id . '/' . $user_id . '">' . $slot . '</a>';
          }
          return response()->json(['success' => true, 'message' => 'Free Time ', 'date' => $today,"date_format" => $type->date_format , 'data' => $html]);
        } else {
          return response()->json(['success' => false, 'message' => '<h4>' . __('No Free Appointments on This Day!') . '</h4>', 'date' => $today,"date_format" => $type->date_format , 'data' => '']);
        }
      } catch (Exception $e) {
        return response()->json(['success' => false, 'message' => '<p>Something Went Wrong!</p>', 'data' => '']);
      }
    }

    public function userPreferredDate($slot, $date, $type_id, $user_id)
    {


        $data = [];
        $data['slot'] = $slot;
        $data['date'] = $date;
        $data['type_id'] = $type_id;
        $data['user_id'] = $user_id;
        $data['page_title'] = ' Appointment Booking';
        $data['frame_color'] = AppointmentType::where('id',$type_id)->value('frame_color');
        $data['customer'] = json_decode(Request::input('customer'),true);

        $data['form_info'] = appointmentContactFormInfo($type_id, $user_id);
        $data['additional_fields'] = DB::table('appointment_contact_form_fields')
            ->where([
                'user_id' => $user_id,
                'type_id' => $type_id,
            ])
            ->orderBy('id', 'asc')
            ->get();

        return view('admin.appointment_booking.booking.new_user_booking_form', $data);
    }

    public function appointmentNewUserBooking(LaravelRequest $request)
    {
        $type_id = $request->type_id;

        $additional_inputs = $additional_messages = [];
        $req_keys = array_keys(request()->all());

        DB::table('appointment_contact_form_fields')
            ->where([
                'user_id' => CRUDBooster::myId(),
                'type_id' => $type_id,
            ])
            ->orderBy('id', 'asc')
            ->get()->each(function($row) use($req_keys, &$additional_inputs) {
                $additional_input_key = 'additional_' . $row->input_name;

                if (in_array($additional_input_key, $req_keys)) {
                    $info = [
                        'show' => $row->show,
                        'required' => $row->required,
                        'label' => $row->label,
                        'value' => request()->input($additional_input_key),
                    ];

                    $additional_inputs[$additional_input_key] = $info;
                }
            });

        $last_name_rules = $phone_rules = 'nullable';

        $form_info = appointmentContactFormInfo($type_id, CRUDBooster::myId());
        if (!empty($form_info)) {
            if ($form_info->last_name_show && $form_info->last_name_req) {
                $last_name_rules = 'required|string';
            }

            if ($form_info->phone_show && $form_info->phone_req) {
                $phone_rules = 'required';
            }
        } else {
            $phone_rules = 'required';
        }

        $validation_rules = [
            'slot' => 'required|string',
            'date' => 'required|date',
            'first_name' => 'required|string',
            'last_name' => $last_name_rules,
            'email' => 'required|email',
            'phone' => $phone_rules,
        ];

        if (!empty($additional_inputs)) {
            foreach ($additional_inputs as $key => $info) {
                if (($info['show'] == 1) && ($info['required'] == 1)) {
                    $validation_rules[$key] = 'required';
                    $additional_messages[$key . '.required'] = $info['label'] . ' field is required';
                }
            }
        }

        $validated = $request->validate($validation_rules, $additional_messages);

        try {
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($request->email);
            if(isset($request->phone) && !empty($request->phone))
            {
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($request->phone);
            }
        } catch(Exception $e) {
            return back()->with(['message' => $e->getMessage(), 'message_type' => 'warning'])->withInput();
        }

        try {
            $data = [];
            $slots = explode('-', $request->slot);
            $data['start_time'] = $slots['0'];
            $data['end_time'] = $slots['1'];
            $data['date'] = $request->date;
            $data['name'] = $request->first_name . ' ' . $request->last_name;
            $data['email'] = $request->email;
            $data['phone'] = $request->phone;
            $data['type_id'] = $request->type_id;
            $data['user_id'] = $request->user_id;
            $data['redirect_url'] = $request->redirect_url;
            $data['page_title'] = ' Appointment Booking';
            $data['frame_color'] = AppointmentType::where('id',$request->type_id)->value('frame_color');
            $data['has_redirect_url'] = 0;

            $appiontment_type = AppointmentType::where('id',$request->type_id)->first();
            if (empty($appiontment_type->redirect_to_thank) && !empty($appiontment_type->redirect_url)) { 
                $data['has_redirect_url'] = 1;
            }

            // Do accounting
            app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

            $data['form_info'] = $form_info;
            $data['additional_inputs'] = $additional_inputs;
            return view('admin.appointment_booking.booking.new_user_confirm_data', $data)->with(['message' => 'Your Appointment Send Successfully!', 'message_type' => 'success']);
        } catch (Exception $e) {
            return redirect('/admin/user-appointment-booking')->with(['message' => 'Oops!! Something Went Wrong', 'message_type' => 'warning']);
        }

    }

    public function appointmentNewUserBookingSubmit(LaravelRequest $request)
    {
        $type_id = $request->type_id;
        $phone_valid_rules = 'nullable';

        $form_info = appointmentContactFormInfo($type_id, CRUDBooster::myId());
        if (!empty($form_info)) {
            if ($form_info->phone_show && $form_info->phone_req) {
                $phone_valid_rules = 'required|numeric';
            }
        } else {
            $phone_valid_rules = 'required|numeric';
        }

        $additional_inputs = $additional_messages = [];
        $req_keys = array_keys(request()->all());

        DB::table('appointment_contact_form_fields')
            ->where([
                'user_id' => CRUDBooster::myId(),
                'type_id' => $type_id,
            ])
            ->orderBy('id', 'asc')
            ->get()->each(function($row) use($req_keys, &$additional_inputs) {
                $additional_input_key = 'additional_' . $row->input_name;

                if (in_array($additional_input_key, $req_keys)) {
                    $info = [
                        'show' => $row->show,
                        'required' => $row->required,
                        'label' => $row->label,
                        'value' => !empty(request()->input($additional_input_key)) ? request()->input($additional_input_key) : '',
                    ];

                    $additional_inputs[$additional_input_key] = $info;
                }
            });

        $validation_rules = [
            'start_time' => 'required|string',
            'end_time' => 'required|string',
            'date' => 'required|date',
            'name' => 'required|string',
            'email' => 'required|email',
            'phone' => $phone_valid_rules,
        ];

        if (!empty($additional_inputs)) {
            foreach ($additional_inputs as $key => $info) {
                if (($info['show'] == 1) && ($info['required'] == 1)) {
                    $validation_rules[$key] = 'required';
                    $additional_messages[$key . '.required'] = $info['label'] . ' field is required';
                }
            }
        }

        // edit view
        $validated = $request->validate($validation_rules, $additional_messages);

        try {
            $appiontment_type = AppointmentType::find($request->type_id);
//            $customer_id = DB::table('new_customers')->insertGetId([
//                'full_name' => $request->name,
//                'email' => $request->email,
//                'phone' => $request->phone,
//                'insert_type' => '12',
//                'user_id' => $request->user_id,
//            ]);
            $customer_info = [
                "customer_full_name" => $request->name,
                "company_name" => '',
                'email' => $request->email,
                'phone' => $request->phone,
                'insert_type' => '12', // tag type 12 = calendar
                'source' => 53,  // customer registration source 53 = calendar
                'status' => 1,
                'user_id' => $request->user_id,
            ];

            //Add customer account
            $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);
            $appointmentName = $appiontment_type->name ?? '';
            $appointmentTitle = $request->name. ' - '.$appointmentName;

            $appointment_data[] = [
                'title' => $appointmentTitle,
                'start' => $request->date . 'T' . $request->start_time,
                'end' => $request->date . 'T' . $request->end_time,
                'description' => '',
                'constraint' => "availableForMeeting",
                'slot' => "book",
                'type' => 3,    //3 for external iframe appointment
                'email' => $request->email,
                'phone' => $request->phone,
                'name' => trim($appointmentName),
                'color' => $appiontment_type->color,
                'date' => $request->date,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'post' => $appiontment_type->post,
                'pre' => $appiontment_type->pre * 60,
                'appointment_type' => $request->type_id
            ];

            if (!empty($additional_inputs)) {
                $info = [];
                foreach($additional_inputs as $key => $additional_input) {
                    $info[$additional_input['label']] = $additional_input['value'];
                }
                $appointment_data[0]['additional_info'] = json_encode($info);
            }

            $slug = Str::slug($request->input('title') . Str::random(25), '-');

            $appoint_pre    = $appiontment_type->pre * 60;
            $appoint_post   = $appiontment_type->post;
            $start_end_time = $this->appointStartEndTime($request->date, $request->start_time, $request->end_time, $appoint_pre, $appoint_post);

            $appointment = Appointment::Create(
                [
                    'user_id' => $request->user_id,
                    'customer_id' => $customer_id,
                    'my_id' => CRUDBooster::myId(),
                    'slug' => $slug,
                    'appointment' => $appointment_data,
                    'type'       => 3, // 3 for external iframe appointment
                    'pre'        => $appoint_pre,
                    'post'       => $appoint_post,
                    'start_time' => $start_end_time['start_time'],
                    'end_time'   => $start_end_time['end_time'],
                    'start_at'   => $start_end_time['start_at'],
                    'end_at'     => $start_end_time['end_at'],
                ]);
            $this->googleEventCreate($appointment_data, $appointment, $request->user_id);

            // appointment type edit time: create calendar deal 
            if ($appiontment_type->pipeline_transfer && !empty($appointment) && !empty($customer_id)) {
                $type_price = (float) $appiontment_type->price;
                
                // if (!empty($type_price) && (float) $type_price > 0) {
                    $default_stage_id = 3;
                    if ($appiontment_type->pipeline_version == 2) {
                        $user_stage_id = DB::table('offer_deal_stages')
                            ->where('user_id', $request->user_id)
                            ->orderBy('sort', 'asc')
                            ->value('id');
                        
                        $default_stage_id = !empty($user_stage_id) ? $user_stage_id : $default_stage_id;
                    }

                    $dealPayload = [
                        'source'       => 'calendar',
                        'product_name' => $appointmentTitle . ' - ' . $type_price . ' EUR', 
                        'deal_title'   => $appointmentTitle . ' - ' . $type_price . ' EUR',
                        'deal_price'   => $type_price,
                        'stage_id'     => $default_stage_id,
                        // 'customer_id'  => $customer_id,
                        'email_addr'   => $request->email,
                        'contact_name' => $request->name,
                        'user_id'      => $request->user_id,
                        'appointment_id' => $appointment->id ?? null,
                        'pipeline_version' => $appiontment_type->pipeline_version,
                    ];
                    app('App\Http\Controllers\AdminPipelineController')->autoTypeDealCreation($dealPayload);
                // }
            }

            $tag_name = DropfunnelTag::select('tag')->find($appiontment_type->tag);
            DropfunnelCustomerTag::insertTag($tag_name->tag, $request->user_id, $customer_id, 17);
            $data = [];
            $data['content'] = $appiontment_type->thank_page_content;
            $data['type_id'] = $request->type_id;
            $data['user_id'] = $request->user_id;
            $data['available_slots'] = AppointmentBooking::first();

            $this->sendAppointmentEmail($appointment);

            try {
                $appointmentName = $appointmentName ? " ({$appointmentName})" : '';
                $user = User::find($request->user_id);
                if(!empty($user))
                {
                    $user->notify(new DRMNotification('You have new appointment of ' . $request->name . ' on ' . $request->date . ' ' . $request->start_time . $appointmentName, 'APPOINTMENT_BOOKING', url('/') . '/admin/appointment_booking'));
                }
            }catch(Exception $ex){}


            if(!empty($request->redirect_url)){
                return redirect()->to($request->redirect_url);
            }

            if (empty($appiontment_type->redirect_to_thank) && !empty($appiontment_type->redirect_url)) { 
                return response()->json([
                    'success' => true, 
                    'data' => [
                        'redirect_url' => $appiontment_type->redirect_url,
                        'html' => view('admin.appointment_booking.booking.new_user_thank_you', $data)->render(),
                    ]
                ]);
            }

            return view('admin.appointment_booking.booking.new_user_thank_you', $data);
        } catch (Exception $e) {
            \Log::channel('command')->info($e);
            return view('admin.appointment_booking.booking.new_user_booking', ['type_id' => $request->type_id, 'user_id' => $request->user_id, 'massage' => $e->getMessage()]);

            // return redirect()->url('appointment/user-booking-form/'.$request->type_id.'/'.$request->user_id)->with(['message' => 'Oops!! Something Went Wrong', 'message_type' => 'warning']);
        }

    }

    //new user end
    // frontend: customer selected slot
    public function preferredDate($slot, $date)
    {
        $user_info = DB::table('cms_users')->where('id', CRUDBooster::myParentId())->select('name', 'email', 'contact_number')->first();
        $data = [];
        $slots = explode('-', $slot);
        $data['start_time'] = $slots['0'];
        $data['end_time'] = $slots['1'];
        $data['date'] = $date;
        $data['name'] = $user_info->name;
        $data['email'] = $user_info->email;
        $data['phone'] = $user_info->contact_number;
        $data['type'] = request()->type;
        $data['page_title'] = ' Appointment Booking';
        return view('admin.appointment_booking.booking.confirm_data', $data);
    }

    public function appointmentUserBooking(LaravelRequest $request)
    {

        $validated = $request->validate([
            'slot' => 'required|string',
            'date' => 'required|date',
            'first_name' => 'required|string',
            'last_name' => 'string|nullable',
            'email' => 'required|email',
            'phone' => 'required|numeric', // 
        ]);

        try {
            $data = [];
            $slots = explode('-', $request->slot);
            $data['start_time'] = $slots['0'];
            $data['end_time'] = $slots['1'];
            $data['date'] = $request->date;
            $data['name'] = $request->first_name . ' ' . $request->last_name;
            $data['email'] = $request->email;
            $data['phone'] = $request->phone;
            $data['page_title'] = ' Appointment Booking';
            return view('admin.appointment_booking.booking.confirm_data', $data);
        } catch (Exception $e) {

            return redirect('/admin/user-appointment-booking')->with(['message' => 'Oops!! Something Went Wrong', 'message_type' => 'warning']);
        }

    }

    // frontend: appointment submit
    public function appointmentUserBookingSubmit(LaravelRequest $request)
    {
        $validated = $request->validate([
            'start_time' => 'required|string',
            'end_time' => 'required|string',
            'date' => 'required|date',
            'name' => 'required|string',
            'email' => 'required|email',
            'phone' => 'numeric|required', // 
        ]);

        try {
            $data = [];
            $auth_id = CRUDBooster::myParentId();
            $check_number = DB::table('cms_users')->where('id', $auth_id)->select('contact_number')->first();

            if ($check_number->contact_number == null) {
                DB::table('cms_users')
                    ->where('id', $auth_id)
                    ->update(['contact_number' => $request->phone]);
            }

            if($request->type == 'manager'){
                $has_manager_date = \App\takeappointment::where('user_id', $auth_id)->value('manager_date');
                if ($has_manager_date <= 0) { 
                    $redirect_msg = __('You do not have remaining appointment date! Please purchase appointment date.');
                    return redirect('/admin/appointment-plan/?type=manager')->with(['message' => $redirect_msg, 'message_type' => 'warning']);
                }

                $data['redirect_url']  = url('/admin/manager-appointment-booking').'?type='.$request->type;
                $user_id = app('\App\Http\Controllers\AdminSubAccountController')->userAppointmentManagerId($auth_id);
                $mentor_type = 1;
            }
            elseif($request->type == 'Onboarding')
            {
                $has_free_date = \App\takeappointment::where('user_id', $auth_id)->value('free_date');
                if ($has_free_date <= 0) {
                    $redirect_url = '/admin/appointment-plan/?type=manager';
                    $redirect_msg = __('You do not have remaining appointment date! Please purchase appointment date?');

                    // check already onboarding free appointment taken or not
                    $free_appointment_exists = \App\AppointmentHistory::where([
                            'user_id'     => $auth_id,
                            'type'        => 1,
                            'mentor_type' => 2,
                        ])
                        ->exists();

                    if ($free_appointment_exists) {
                        $redirect_url  = '/admin';
                        $purchase_link = '<a href="'. url('/admin/appointment-plan/?type=manager') .'">PURCHASE</a>';
                        $redirect_msg  = __('You already taken free appointment. If you need additional appointment, please purchase appointment date from here. ') . $purchase_link;
                    }

                    return redirect($redirect_url)->with(['message' => $redirect_msg, 'message_type' => 'warning']);
                }

                $data['redirect_url']  = url('/admin/manager-appointment-booking').'?type='.$request->type;
                $user_id = app('\App\Http\Controllers\AdminSubAccountController')->defaultBookingManager();
                $mentor_type = 2;

                //Dashboard onboarding progressbar
                $step_data['appointment'] = [
                    "status" => 1,
                    "data" => [
                        'title' => $request->name,
                        'start' => $request->date . 'T' . $request->start_time,
                        'end' => $request->date . 'T' . $request->end_time,
                    ]
                ];
                resolve(Customer::class)->updateProgressBar($step_data, 'appointment');

            }else{
                $has_mentor_date = \App\takeappointment::where('user_id', $auth_id)->value('payment_date_remaining');
                if ($has_mentor_date <= 0) { 
                    $redirect_msg = __('You do not have remaining appointment date! Please purchase appointment date.');
                    return redirect('/admin/appointment-plan/?type=mentoring')->with(['message' => $redirect_msg, 'message_type' => 'warning']);
                }

                $data['redirect_url']  = url('/admin/user-appointment-booking');
                $user_id = 98;
            }

            $postTime  = AppointmentBooking::where('user_id', $user_id)->value('input_array');
            $post = ($postTime['post']) ? $postTime['post'] : 0;
            $post = in_array($user_id, [2955, 2439]) ? 30 : $post;

            $appointmentName = $request->type ?? '';
            $appointmentTitle = $request->name. ' - '.$appointmentName;

            $appiontment_data[] = [
                'title' => $appointmentTitle,
                'start' => $request->date . 'T' . $request->start_time,
                'end' => $request->date . 'T' . $request->end_time,
                'description' => '',
                'constraint' => "availableForMeeting",
                'slot' => "book",
                'type' => 2,   // 2 for mentoring appointment
                'email' => $request->email,
                'phone' => $request->phone,
                'name' => $request->type,
                'color' => '#ff7575',
                'date' => $request->date,
                'start_time' => $request->start_time,
                'end_time' => $request->end_time,
                'post' => $post,    // 15*60    15 minute post preparation
                'pre' => 0,
                'mentor_type' => $mentor_type,
            ];
            $slug = Str::slug($request->input('title') . Str::random(25), '-');
            $start_end_time = $this->appointStartEndTime($request->date, $request->start_time, $request->end_time, 0, $post);

            $new_appointment = Appointment::Create(
                [
                    'user_id' => $user_id,
                    'customer_id' => $auth_id,
                    'my_id' => CRUDBooster::myId(),
                    'slug' => $slug,
                    'appointment' => $appiontment_data,
                    'type'        => 2, // 2 for mentoring appointment
                    'pre'         => 0,
                    'post'        => $post,
                    'mentor_type' => $mentor_type ?? null,
                    'start_time'  => $start_end_time['start_time'],
                    'end_time'    => $start_end_time['end_time'],
                    'start_at'    => $start_end_time['start_at'],
                    'end_at'      => $start_end_time['end_at'],
                ]);
            $this->googleEventCreate($appiontment_data, $new_appointment, $user_id);

            $appointmentName = $appointmentName ? " ({$appointmentName})" : '';

            if($request->type == 'mentoring'){
                 $message = 'User takes mentoring appointment by termine on ' . $request->date . ' from ' . $request->start_time . ' - ' . $request->end_time;
                 $this->decrimentAppointmentHistory($auth_id, $message);
            }elseif($request->type == 'manager'){
                $message = 'User takes manager appointment by termine on ' . $request->date . ' from ' . $request->start_time . ' - ' . $request->end_time;
                $this->decrimentAppointmentHistory($auth_id, $message,'1');
            }else{
                // free appointment time: add appointment id to the deal, and stage may change
                if (!empty($new_appointment->id)) {
                    app('App\Http\Controllers\AdminPipelineController')->addAppointmentToDeal($new_appointment->id);
                }

                $message = 'User takes free appointment by termine on ' . $request->date . ' from ' . $request->start_time . ' - ' . $request->end_time;
                $this->decrimentAppointmentHistory($auth_id, $message,'2');
            }

            $this->sendAppointmentEmail($new_appointment);

            User::find($auth_id)->notify(new DRMNotification('You have an appointment on ' . $request->date . ' ' . $request->start_time . $appointmentName, 'APPOINTMENT_BOOKING', url('/') . '/admin/appointment_booking'));
            User::find($user_id)->notify(new DRMNotification('You have new appointment of ' . $request->name . ' on ' . $request->date . ' ' . $request->start_time . $appointmentName, 'APPOINTMENT_BOOKING', url('/') . '/admin/appointment_booking', '', null, true));

            return view('admin.appointment_booking.booking.thank_you', $data);
        } catch (Exception $e) {
            return redirect('/admin/user-appointment-booking')->with(['message' => 'Oops!! Something Went Wrong', 'message_type' => 'warning']);
        }
    }


    public function get_available_slots($day, $date, $user_id = 98,$duration = 30)
    {
        $slots = AppointmentBooking::where('user_id', $user_id)->first();
        $project_slots = AppointmentProjectSlot::where('user_id', $user_id)->get();
        $appointments = Appointment::where('user_id', $user_id)->orWhere('customer_id',$user_id)->get();

        //get project available slots
        $all_project = [];
        foreach ($project_slots as $project) {
            foreach ($project->slots as $value) {
                array_push($all_project, $value);
            }
        }
        $free_project_slots = [];
        foreach ($all_project as $pro) {
            if ($pro['date'] == $date) {
                $free_project_slots = $this->makeSlot($pro['start_time'], $pro['end_time'],$duration);
            }
        }

        // get available slots
        $available_slots = [];
        $break_time = [];
        if ($slots->event_slot) {
            foreach ($slots->event_slot as $value) {
                if ($value['startTime'] != '00:00' && $value['slot'] == 'available' && $day == $value['daysOfWeek']) {
                    $available_slots = $this->makeSlot($value['startTime'], $value['endTime'],$duration);

                    $select_date = Carbon::parse($date);
                    if (Carbon::now()->isSameDay($select_date)) {
                        $available_slots = [];
                    }

//                    $current_time = Carbon::now();
//                    $start_time = Carbon::now()->addHour();
//                    $end_time = Carbon::parse($date . ' ' . $value['endTime']);
//                    $start_slot_time = Carbon::parse($date . ' ' . $value['startTime']);
//                    $select_date = Carbon::parse($date);
//                    if (Carbon::now()->isSameDay($select_date)) {
//                        if ($start_slot_time->gte($current_time) && $start_time->lt($end_time)) {
//                            $available_slots = $this->makeSlot($value['startTime'], $value['endTime']);
//                        } elseif ($start_slot_time->lte($current_time) && $current_time->lt($end_time)) {
//                            $temp = explode(':', $start_time->format("H:i"));
//                            $hour = (int)$temp['0'];
//                            $minute = (int)$temp['1'];
//                            if ($minute < 30) {
//                                $minute = '00';
//                            } else {
//                                $minute = '30';
//                            }
//                            $time = "$hour:$minute";
//                            $temporaryTime = Carbon::parse($time);
//                            $temporaryEndTime = Carbon::parse($value['endTime']);
//                            $diffInHour = $temporaryTime->diffInHours($temporaryEndTime);
//                            if ($diffInHour >= 2) {
//                                ++$hour;
//                                $time = "$hour:$minute";
//                                $available_slots = $this->makeSlot($time, $value['endTime']);
//                            } else {
//                                $available_slots = [];
//                            }
//                        } else {
//                            $available_slots = [];
//                        }
//                    }


//                    $current_time = Carbon::now()->format('G')+12;
//                    $start_time = $current_time + 1;
//                    $end_time =  explode(':',$value['endTime']);
//                    $start_slot_time =  explode(':',$value['startTime']);
//
//                    if (Carbon::now()->format('Y-m-d') == $date) {
//                        if($start_slot_time['0'] >= $current_time && $start_time < (int)$end_time['0']){
//                           $available_slots = $this->makeSlot($start_slot_time['0'], $value['endTime']);
//                        }elseif($start_slot_time['0'] <= $current_time && $current_time < (int)$end_time['0']){
//                            $available_slots = $this->makeSlot($start_time, $value['endTime']);
//                        }else{
//                          $available_slots = [];
//                        }
//                    }

                } elseif ($value['startTime'] != '00:00' && $value['slot'] == 'break' && $day == $value['daysOfWeek']) {
                    $temp = $this->makeSlot($value['startTime'], $value['endTime'],$duration);
                    array_push($break_time, $temp);
                }
            }
        }

        $all_available_slots = array_merge(($available_slots) ? $available_slots : [], $free_project_slots);
        // get book appointment slots
        $appointment_slots = [];
        foreach ($appointments as $appointment) {
            foreach ($appointment['appointment'] as $app) {
                if ($app['date'] == $date) {
                    $temp = $this->makeSlot($app['start_time'], $app['end_time'],$duration);
                    array_push($appointment_slots, $temp);
                }
            }
        }

        // marge break time slots
        $break_slots = $this->slotFormating($break_time);
        $book_slots = $this->slotFormating($appointment_slots);
        $free_slots = array_diff($all_available_slots, $book_slots, $break_slots);    //Remove booked events

        if(isLocal() || CRUDBooster::myParentId() == 212 ){
          $google_booked_slots = $this->googleEvents($date, $user_id,$duration);     // google events
          if(!empty($google_booked_slots)){
            $free_slots = array_diff($free_slots,$google_booked_slots);
          }
        }

        return $free_slots;
    }




    public function get_available_slots_on_duration($day, $date, $user_id = 98,$duration = 30)
    {

      $slots = AppointmentBooking::where('user_id', $user_id)->first();
      $project_slots = AppointmentProjectSlot::where('user_id', $user_id)->get();
      $appointments = Appointment::where('status', '0')
        ->where(function($q) use ($user_id) {
            $q->where('user_id', $user_id)
            ->orWhere('customer_id',$user_id);
        })
        ->get();

      $festival_events = Appointment::where('status', '1')
        ->where(function($q) use ($user_id) {
            $q->where('user_id', $user_id)
            ->orWhere('customer_id',$user_id);
        })
        ->get();

      // dd($appointments,$festival_events);
      //get project available slots
      if(Carbon::now()->format('Y-m-d') >= Carbon::parse($date)->format('Y-m-d')){
        return [];
      }

      $all_project = [];
      foreach ($project_slots as $project) {
        foreach ($project->slots as $value) {
          array_push($all_project, $value);
        }
      }
      $free_project_slots = [];
      foreach ($all_project as $pro) {
        if ($pro['date'] == $date) {
          $start_project_range = $this->replaceString($pro['start_time']);
          $end_project_range = $this->replaceString($pro['end_time']);
        }
      }
      // get available slots
      $break_slots = [];
      if ($slots->event_slot) {
        foreach ($slots->event_slot as $value) {
          if ($value['startTime'] != '00:00' && $value['slot'] == 'available' && $day == $value['daysOfWeek']) {
            // get all available slots
            $start_range = $this->replaceString($value['startTime']);
            $end_range = $this->replaceString($value['endTime']);

            //get all break slots
          } elseif ($value['startTime'] != '00:00' && $value['slot'] == 'break' && $day == $value['daysOfWeek']) {
            $temp = [];
            $temp['start'] = $value['startTime'];
            $temp['end'] = $value['endTime'];
            array_push($break_slots,$temp);
          }
        }
      }
      // get all appointment booking
      $appointment_slots = [];
      foreach ($appointments as $appointment) {
        foreach ($appointment['appointment'] as $app) {
          if ($app['date'] == $date) {
            $temp['start'] = $app['start_time'];
            $temp['end'] = $app['end_time'];
            $temp['start_time_sec'] = $this->replaceString($app['start_time']);
            $temp['end_time_ses'] = $this->replaceString($app['end_time']);
            $temp['post'] = $app['post'];
            $temp['pre'] = $app['pre'];
            array_push($appointment_slots,$temp);
          }
        }
      }

      // Project avaialbe slots
      for($i = $start_project_range; $i <= $end_project_range; $i = $i + ($duration*60)){
        $value = $this->secondToTimeFormat($i);
        $post_time = (int)$this->reSloting($value,$appointment_slots);
        if($post_time > 0){
            $i = $post_time;
        }
        $free_project_slot_for_bookig[]= $i;
      }

      for($i = $start_range; $i <= $end_range; $i = $i + ($duration*60)){
        $value = $this->secondToTimeFormat($i);
        $post_time = (int)$this->reSloting($value,$appointment_slots);

        if($post_time > 0){
            $i = $post_time;
        }
        $free_for_bookigs[]= $i;
      }


      if($free_project_slot_for_bookig['0'] != 0){
          $free_for_bookigs = array_merge($free_for_bookigs,$free_project_slot_for_bookig);
      }

      $formatter = function ($time = 24) {
        return $this->secondToTimeFormat($time);
      };
      $mapping_data_frees = array_map($formatter, $free_for_bookigs);

      $google_booked_slots = $this->googleEventsTime($date, $user_id,$duration);
      foreach ($mapping_data_frees as $key => $value) {
        $condition = $this->ganerate_free_slot($value, $mapping_data_frees[$key+1], $google_booked_slots, $break_slots, $appointment_slots, $festival_events, $date);
        if($condition == true && $mapping_data_frees[$key+1] != null && $value != $mapping_data_frees[$key+1]){
          $date1 = $this->replaceString($value);
          $date2 =  $this->replaceString($mapping_data_frees[$key+1]);
          $diff  = $date2 - $date1;
          if( $duration * 60 < $diff){
            // $end_value = $this->secondToTimeFormat($date+($duration * 60));
            //   $green_slots[] = $value." - ".$end_value;
          }else{
            $green_slots[] = $value." - ".$mapping_data_frees[$key+1];
          }
        }
      }
      return $green_slots;
    }

    protected function reSloting($time,$appointment_slots){
      $post = 0;
      $starTime = Carbon::parse($time)->format('H:i');

      foreach ($appointment_slots as $book) {
        $date2 = Carbon::parse($book['start'])->format('H:i');
        $date3 = Carbon::parse($book['end'])->format('H:i');
        $end_time = $this->replaceString($book['end']);
          // if ($starTime >= ($date2 - $book['pre']) && $starTime < $date3)
//          dump($starTime,$date2,$date3);
        if ($starTime > $date2 && $starTime <= $date3){
          $post = $end_time+ ($book['post'] * 60);
        }
      }
      return $post;
    }

    protected function ganerate_free_slot($time, $end_time, $google_booked_slots, $break_slots, $appointment_slots, $festival_events, $date){
      $isTure = true;
      $starTime = Carbon::parse($time)->format('H:i');
      $endTime = Carbon::parse($end_time)->format('H:i');
      //check festival events...
      $appointment_dateTime = Carbon::parse(Carbon::parse($date)->format('Y-m-d').' '.Carbon::parse($time)->format('H:i'));


      foreach ($festival_events as  $fest) {
        $fest_start = explode('T', $fest['appointment']['0']['start']);
        $fest_end = explode('T', $fest['appointment']['0']['end']);
        $festival_start_dateTime = Carbon::parse($fest_start['0'].' '.$fest_start['1']);
        $festival_end_dateTime = Carbon::parse($fest_end['0'].' '.$fest_end['1']);
        if($appointment_dateTime->gte($festival_start_dateTime) && $festival_end_dateTime->gt($appointment_dateTime)){
           return false;
         }
       }

//        foreach ($google_booked_slots as $google) {
//          $date2 = Carbon::parse($google['start'])->format('H:i');
//          $date3 = Carbon::parse($google['end'])->format('H:i');
//          if ($starTime > $date2 && $starTime < $date3)
//          {
//            // $isTure = $date2.', '.$date1.', '.$date3.' = false';
//            $isTure = false;
//          }
//          if ($endTime > $date2 && $endTime < $date3)
//          {
//            $isTure = false;
//          }
//          elseif ($date2 >= $starTime &&  $endTime > $date2)
//          {
//              $isTure = false;
//          }
//        }


//      foreach ($break_slots as $break) {
//        $date2 = Carbon::parse($break['start'])->format('H:i');
//        $date3 = Carbon::parse($break['end'])->format('H:i');
//        if ($starTime > $date2 && $starTime < $date3)
//        {
//          $isTure = false;
//        }
//        if ($endTime > $date2 && $endTime < $date3)
//        {
//          $isTure = false;
//        }
//        elseif ($date2 >= $starTime &&  $endTime > $date2)
//        {
//            $isTure = false;
//        }
//      }
//        dd($google_booked_slots,$break_slots,$appointment_slots);
        $isTure = $this->finalCompire($google_booked_slots,$starTime,$endTime,$isTure);
        $isTure = $this->finalCompire($break_slots,$starTime,$endTime,$isTure);
        $isTure = $this->finalCompire($appointment_slots,$starTime,$endTime,$isTure);

      return $isTure;
    }

    public function finalCompire($appointment_slots,$starTime,$endTime,$isTure){

        foreach ($appointment_slots as $book) {
            $booking_start = Carbon::parse($book['start'])->format('H:i');
            $booking_end = Carbon::parse($book['end'])->format('H:i');

            if ($starTime == $booking_start || $endTime == $booking_end)         //start and end in same time
            {
                $isTure = false;
            }elseif ($starTime < $booking_start && $booking_start < $endTime)    // booking start between slot time
            {
                $isTure = false;
            }elseif ($starTime < $booking_end &&  $booking_end < $endTime)       // booking end  between slot time
            {
                $isTure = false;
            }
            elseif ($starTime < $booking_start &&  $booking_end < $endTime)       // booking start and end between  slot time
            {
                $isTure = false;
            }
            elseif ($booking_start < $starTime &&  $endTime < $booking_end)       // slot time in a large booking
            {
                $isTure = false;
            }

        }
        return $isTure;
        }

    public function googleEvents($date, $user_id,$duration = 30){
      try{
        $credentials = DB::table('google_calendar_credentials')->where('user_id',$user_id)->first();
        $calenderConfiguration = $this->googleDataMapping($credentials);
        \Config::set('google-calendar', $calenderConfiguration);
        $startDateTime = Carbon::now()->subYear();
         $events = Event::get($startDateTime);
        // $events = Event::get(null,null,[],null,$calenderConfiguration);
        $google_event = [];
        $google_time_slot = [];
        foreach($events as $event){
            $startDateTime = explode('T',$event->googleEvent->start->dateTime);
            $endDateTime = explode('T',$event->googleEvent->end->dateTime);
            $starTime = $startDateTime['1'];
            $start = trim(str_replace(":00:00+06:00"," ",$starTime));
            $endTime = $endDateTime['1'];
            $end = trim(str_replace(":00:00+06:00"," ",$endTime));
            $appointment_date = $startDateTime['0'];
            if($date == $appointment_date){
                $temp = [];
                $temp['date'] = $appointment_date;
                $temp['start'] = $start;
                $temp['end'] = $end;
                $temp['title'] = $event->googleEvent->summary;
                $temp['email'] = $event->googleEvent->creator->email;
                $google_event[] = $temp;
                $google_slot = $this->makeSlot($start, $end ,$duration);
                $google_time_slot[] = $google_slot;
            }
        }
        $google_booked_slots = call_user_func_array('array_merge', $google_time_slot);
        return $google_booked_slots;
      }catch(Exception $e){
        return [];
      }
    }

    public function googleEventsTime($date, $user_id,$duration = 30){

      try{
        $credentials = DB::table('google_calendar_credentials')->where('user_id',$user_id)->first();
        $calenderConfiguration = $this->googleDataMapping($credentials);
        \Config::set('google-calendar', $calenderConfiguration);
        $startDateTime = Carbon::now()->subYear();
         $events = Event::get($startDateTime);
        $google_time_slot = [];
        foreach($events as $event){
            $startDateTime = explode('T',$event->googleEvent->start->dateTime);
            $endDateTime = explode('T',$event->googleEvent->end->dateTime);
            $starTime = $startDateTime['1'];
            $start = trim(str_replace(":00+06:00"," ",$starTime));
            $endTime = $endDateTime['1'];
            $end = trim(str_replace(":00+06:00"," ",$endTime));
            $appointment_date = $startDateTime['0'];
            if($date == $appointment_date && $date == $endDateTime['0']){
              $temp = [];
              $temp['start']= $start;
              $temp['end']= $end;
              $google_time_slot[] = $temp;
            }elseif($date == $appointment_date && $date < $endDateTime['0']){
              $temp = [];
              $temp['start']= $start;
              $temp['end']= "23:59";
              $google_time_slot[] = $temp;
            }elseif($date > $appointment_date && $date < $endDateTime['0']){
              $temp = [];
              $temp['start']= "00:01";
              $temp['end']= "23:59";
              $google_time_slot[] = $temp;
            }elseif($date > $appointment_date && $date == $endDateTime['0']){
              $temp = [];
              $temp['start']= "00:01";
              $temp['end']= $end;
              $google_time_slot[] = $temp;
            }
        }
        return $google_time_slot;
      }catch(Exception $e){
        return [];
      }
    }

    public function googleIsConnection($credentials){
      try {
        if($credentials){
          $calenderConfiguration = $this->googleDataMapping($credentials);
          \Config::set('google-calendar', $calenderConfiguration);
          $startDateTime = Carbon::now()->subYear();
          $events = Event::get($startDateTime);
        }
      } catch (\Exception $e) {
        $events = [];
      }
      return !empty($events)?true : false;
    }

    public function googleDataMapping($credentials){
      $calenderConfiguration = [
          'default_auth_profile' => env('GOOGLE_CALENDAR_AUTH_PROFILE', 'service_account'),
          'auth_profiles' => [
               // * Authenticate using a service account.
              'service_account' => [
                   // * Path to the json file containing the credentials.
                  'credentials_json' => json_decode($credentials->service_account, true),
              ],
               // * Authenticate with actual google user account.
              'oauth' => [
                   // * Path to the json file containing the oauth2 credentials.
                  'credentials_json' => json_decode($credentials->oauth, true),
                   // * Path to the json file containing the oauth2 token.
                  'token_json' => json_decode($credentials->auth_token, true),
              ],
          ],
           // *  The id of the Google Calendar that will be used by default.
          'calendar_id' => $credentials->calendar_id ?? '0',
      ];
      return $calenderConfiguration;
    }

    protected function timeSlot()
    {
        // time slot rang in 30 min
        $formatter = function ($time = 24) {
            if ($time % 3600 == 0) {
                $current_time = date('H:00', $time);
                return ($current_time != '00:00') ? $current_time : '24:00';
            } else {
                return date('H:i', $time);
            }
        };
        if (CRUDBooster::myParentId() == 98) {
            $halfHourSteps = range(25200, 38 * 1800, 1800);
        } else {
            $halfHourSteps = range(0, 46 * 1800, 1800);
        }
        return array_map($formatter, $halfHourSteps);
    }

    protected function getTimeSlot($start_time)
    {
        // time slot rang in 30 min
        $formatter = function ($time = 24) {
            if ($time % 3600 == 0) {
                $current_time = date('H:00', $time);
                return ($current_time != '00:00') ? $current_time : '24:00';
            } else {
                return date('H:i', $time);
            }
        };
        $halfHourSteps = range(($start_time * 3600) - 1800, 46 * 1800, 1800);
        return array_map($formatter, $halfHourSteps);
    }

    protected function makeSlot($start_time, $end_time, $duration)
    {
        // time slot rang in 30 min
        $formatter = function ($time = 24) {
            if ($time % 3600 == 0) {
                return date('H:00', $time);
            } else {
                return date('H:i', $time);
            }
        };
        $start_range = $this->replaceString($start_time);
        $end_range = $this->replaceString($end_time);
        if ($start_range == $end_range) {
            $end_range = $end_range + $duration*60;
        }
        $number = range($start_range, $end_range, $duration*60);
        $maping_data = array_map($formatter, $number);
        $array = [];
        foreach ($maping_data as $key => $data) {
            if (!empty($maping_data[$key + 1])) {
                if ($maping_data[$key + 1] != '00:00') {
                    array_push($array, $data . ' - ' . $maping_data[$key + 1]);
                } else {

                    array_push($array, $data . ' - 24:00');
                }
            }
        }

        return $array;
    }

    protected function makeEventsArray($start_time, $end_time, $day, $slot)
    {
        // time slot rang in 30 min
        $formatter = function ($time = 24) {
            if ($time % 3600 == 0) {
                return date('H:00', $time);
            } else {
                return date('H:i', $time);
            }
        };
        $start_range = $this->replaceString($start_time);
        $end_range = $this->replaceString($end_time);
        if ($start_range == $end_range) {
            $end_range = $end_range + 1800;
        }
        $number = range($start_range, $end_range, 1800);
        $maping_data = array_map($formatter, $number);
        $array = [];
        foreach ($maping_data as $key => $data) {
            if (!empty($maping_data[$key + 1])) {
                $endTime = $maping_data[$key + 1];
                if ($maping_data[$key + 1] == '00:00') {
//                    array_push($array, $data . ' - ' . $maping_data[$key + 1]);
                    $endTime = '24:00';
                }

                $color = "#97e28a";
                if ($slot == "break") {
                    $color = "#ff9f89";
                } elseif ($slot == "off_day") {
                    $color = "#80808057";
                }

                $arrayData = [
                    "groupId" => "availableForMeeting",
                    "startTime" => $data,
                    "endTime" => "$endTime",
                    "daysOfWeek" => "[$day]",
                    "slot" => $slot,
                    "display" => "background",
                    "color" => $color,
                ];
                array_push($array, $arrayData);
            }
        }
        return $array;
    }

    protected function makeProjectArray($start_time, $end_time, $date)
    {
        // time slot rang in 30 min
        $formatter = function ($time = 24) {
            if ($time % 3600 == 0) {
                return date('H:00', $time);
            } else {
                return date('H:i', $time);
            }
        };
        $start_range = $this->replaceString($start_time);
        $end_range = $this->replaceString($end_time);
        if ($start_range == $end_range) {
            $end_range = $end_range + 1800;
        }
        $number = range($start_range, $end_range, 1800);
        $maping_data = array_map($formatter, $number);
        $array = [];
        foreach ($maping_data as $key => $data) {
            if (!empty($maping_data[$key + 1])) {
                $endTime = $maping_data[$key + 1];
                if ($maping_data[$key + 1] == '00:00') {
//                    array_push($array, $data . ' - ' . $maping_data[$key + 1]);
                    $endTime = '24:00';
                }

                $arrayData = [
                    "groupId" => "availableForMeeting",
                    "start" => $date . "T" . $data . ":00",
                    "end" => $date . "T" . $endTime . "00",
                    "date" => "$date",
                    "stat_time" => "$data",
                    "end_time" => "$endTime",
                    "slot" => "avaialbe",
                    "display" => "background",
                    "color" => "green",
                ];
                array_push($array, $arrayData);
            }
        }
        return $array;
    }

    public function slotFormating($slots)
    {
        $marge_array = [];
        foreach ($slots as $value) {
            foreach ($value as $array_value) {
                array_push($marge_array, $array_value);
            }
        }
        return $marge_array;
    }

    public function replaceString($time)
    {
        list($hour, $min) = explode(':', $time);
        return ((int)$hour - 1) * 3600 + $min * 60;
    }

    // backend: from calendar view search customer to assign appointment
    public function searchCustomers()
    {
      $term = $_REQUEST['term'];
      $result = [];
      if (isset($term)) {
        //Key users
        if(!CRUDBooster::isKeyAccount()){
          $result = NewCustomer::where('full_name', 'LIKE', '%' . $term . '%')->where('user_id', '=', CRUDBooster::myId())->take(15)->select('id', 'full_name as value')->get()->toArray();
          if (!empty($result)) return $result;
        }else{
          $all_key_users_data = \DRM::keyAccountAllUsersId(CRUDBooster::myParentId()) ?? [];
          $key_users_id = is_array($all_key_users_data)? array_keys($all_key_users_data) : [];

          $key_users = DB::table('cms_users')
          ->whereNotNull('status')
          ->whereNotNull('email_verified_at')
          ->whereIntegerInRaw('id', $key_users_id)
          ->where('name', 'LIKE', '%' . $term . '%')
          ->select('name', 'id')
          ->get()
          ->map(function($item) use ($all_key_users_data) {
            $status = (int)$all_key_users_data[$item->id];
            $status_str = $status === 1 ? 'active' : 'inactive';
            return [
              'id' => $item->id,
              'value' => $item->name,
              'status' => $status_str
            ];
          })
          ->values()->toArray();
          return $key_users;
        }
      }
    }

    public function getAppointmentEmailSettings()
    {
        $data['page_title'] = 'Appointment Email Marketing';
        $authId = CRUDBooster::myId(); // 1 get appointment email settings
        $data['mail'] = DB::table('appointment_email_settings')->where('status', 1)->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myId());
        return view('admin.appointment_booking.appointment_email_setting', $data);
    }

    public function postAddSaveAppointmentEmailSettings()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'email_template' => $_REQUEST['email_template'],
            'created_at' => now(),
            'updated_at' => now(),
        ];
        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        DB::table('appointment_email_settings')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myId(), 'status' => 1
            ],
            $data
        );

        $user_id = CRUDBooster::myId();

        Cache::forget('appointment_email_settings_' . $user_id);
        clear_remote_cache('appointment_email_settings_' . $user_id);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Appointment email Setting changed', 'success');
    }

    public function postSendTestAppointmentMail()
    {
        try {
            $authId = CRUDBooster::myId(); // 2 send test appointment mail
            $template = config('system_email_settings.appointment_mail_setting');
            $subject = config('system_email_settings.appointment_mail_subject');
            $emailSetting = DB::table('appointment_email_settings')->where('status', 1)->where('cms_user_id', $authId)->first();
            $userEmail = DB::table('cms_users')->where('id', $authId)->value('email');
            $data = [];
            $data['email_to'] = $_REQUEST['test_email'];
            if (!empty($emailSetting)) {
                $data['subject'] = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
                $data['email_from'] = empty($emailSetting->sender_email) ? $userEmail : $emailSetting->sender_email;
                $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
            }

            $invoiceSetting = DB::table('drm_invoice_setting')->select('logo', 'store_name')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();

            $img = $invoiceSetting->logo ?? '';
            $logo = '<img id="display_logo" width="150" src="' . $img . '" alt="' . $invoiceSetting->store_name . '">';

            // $logo = '<img id="display_logo" width="150" src="" alt="">';
            $template = preg_replace('/\[logo]/', $logo, $template);

            $faker = \Faker\Factory::create();
            $template = preg_replace('/\[customer_name]/', $faker->name, $template);
            $template = preg_replace('/\[company_name]/', $faker->company, $template);
            $template = preg_replace('/\[appointment_date]/', date('Y-m-d'), $template);
            $template = preg_replace('/\[appointment_time]/', date('H:i:s'), $template);

            // if( (isLocal() || in_array(CRUDBooster::myId(), [212, 2592])) ){

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myId())->pluck('signature','id')->toArray();
                $signature_tags = [];

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        // $signature_tags['drm-sign-'.$key] = $signature;
                        $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
                    }
                }

            // }

            $data['subject'] = preg_replace('/\[appointment_date]/', date('Y-m-d'), $data['subject']);
            $data['subject'] = preg_replace('/\[appointment_time]/', date('H:i:s'), $data['subject']);

            app('drm.mailer')->getMailer($authId,$data['email_from'])->send('admin.new_order.email_invoice_template', ['body' => $template], function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function getAppointmentCancelEmailSettings()
    {
        $data['page_title'] = 'Appointment Cancel Email Marketing';
        $authId = CRUDBooster::myId(); // 3 get appointment cancel email settings
        $conditions = ['cms_user_id' => $authId, 'status' => 0];
        $data['mail'] = DB::table('appointment_email_settings')->where($conditions)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myId());
        return view('admin.appointment_booking.appointment_cancel_email_setting', $data);
    }

    public function postAppointmentCancelEmailSettings()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'email_template' => $_REQUEST['email_template'],
            'created_at' => now(),
            'updated_at' => now(),
        ];
        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        DB::table('appointment_email_settings')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myId(), 'status' => 0
            ],
            $data
        );

        $user_id = CRUDBooster::myId();

        Cache::forget('appointment_cancel_email_settings_' . $user_id);
        clear_remote_cache('appointment_cancel_email_settings_' . $user_id);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Appointment cancel email Setting changed', 'success');
    }

    public function postSendTestAppointmentCancelMail()
    {
        try {
            $authId = CRUDBooster::myId(); // 4 send test appointment cancel mail
            $template = config('system_email_settings.appointment_cancel_mail');
            $subject = 'Appointment Cancellation Email';
            $conditions = ['cms_user_id' => $authId, 'status' => 0];
            $emailSetting = DB::table('appointment_email_settings')->where($conditions)->first();
            $userEmail = DB::table('cms_users')->where('id', $authId)->value('email');
            $data = [];
            $data['email_to'] = $_REQUEST['test_email'];
            if (!empty($emailSetting)) {
                $data['subject'] = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
                $data['email_from'] = empty($emailSetting->sender_email) ? $userEmail : $emailSetting->sender_email;
                $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
            }

            $invoiceSetting = DB::table('drm_invoice_setting')->select('logo', 'store_name')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();

            $img = $invoiceSetting->logo ?? '';
            $logo = '<img id="display_logo" width="150" src="' . $img . '" alt="' . $invoiceSetting->store_name . '">';

            $template = preg_replace('/\[logo]/', $logo, $template);

            $faker = \Faker\Factory::create();
            $template = preg_replace('/\[customer_name]/', $faker->name, $template);
            $template = preg_replace('/\[company_name]/', $faker->company, $template);
            $template = preg_replace('/\[appointment_date]/', date('Y-m-d'), $template);
            $template = preg_replace('/\[appointment_time]/', date('H:i:s'), $template);

            // if( (isLocal() || in_array(CRUDBooster::myId(), [212, 2592])) ){

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myId())->pluck('signature','id')->toArray();
                $signature_tags = [];

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        // $signature_tags['drm-sign-'.$key] = $signature;
                        $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
                    }
                }

            // }

            app('drm.mailer')->getMailer($authId,$data['email_from'])->send('admin.new_order.email_invoice_template', ['body' => $template], function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function getAppointmentReminderEmailSettings()
    {
        $data['page_title'] = 'Appointment Email Marketing';
        $authId = CRUDBooster::myId(); // 5 get appointment reminder email settings
        $data['mail'] = DB::table('appointment_email_settings')->where('status', 2)->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        return view('admin.appointment_booking.appointment_reminder_email_setting', $data);
    }

    public function postAddSaveAppointmentReminderEmailSettings()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'email_template' => $_REQUEST['email_template'],
            'created_at' => now(),
            'updated_at' => now(),
        ];
        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        DB::table('appointment_email_settings')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myId(), 'status' => 2
            ],
            $data
        );

        $user_id = CRUDBooster::myId();

        Cache::forget('appointment_email_settings_' . $user_id);
        clear_remote_cache('appointment_email_settings_' . $user_id);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Appointment email Setting changed', 'success');
    }

    public function postSendTestAppointmentReminderMail()
    {
        try {
            $authId = CRUDBooster::myId(); // 6 send test appointment reminder mail
            $template = config('system_email_settings.appointment_reminder_mail_body');
            $subject = config('system_email_settings.appointment_reminder_mail_subject');
            $emailSetting = DB::table('appointment_email_settings')->where('status', 2)->where('cms_user_id', $authId)->first();
            $userEmail = DB::table('cms_users')->where('id', $authId)->value('email');
            $data = [];
            $data['email_to'] = $_REQUEST['test_email'];
            if (!empty($emailSetting)) {
                $data['subject'] = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
                $data['email_from'] = empty($emailSetting->sender_email) ? $userEmail : $emailSetting->sender_email;
                $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
            }

            $invoiceSetting = DB::table('drm_invoice_setting')->select('logo', 'store_name')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();

            $img = $invoiceSetting->logo ?? '';
            $logo = '<img id="display_logo" width="150" src="' . $img . '" alt="' . $invoiceSetting->store_name . '">';

            // $logo = '<img id="display_logo" width="150" src="" alt="">';
            $template = preg_replace('/\[logo]/', $logo, $template);

            $faker = \Faker\Factory::create();
            $template = preg_replace('/\[customer_name]/', $faker->name, $template);
            $template = preg_replace('/\[company_name]/', $faker->company, $template);
            $template = preg_replace('/\[appointment_date]/', date('Y-m-d'), $template);
            $template = preg_replace('/\[appointment_time]/', date('H:i:s'), $template);
            $template = preg_replace('/\[time]/', 30, $template);
            $template = preg_replace('/\[type]/', 'Minute', $template);

            // if( (isLocal() || in_array(CRUDBooster::myId(), [212, 2592])) ){

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myId())->pluck('signature','id')->toArray();
                $signature_tags = [];

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        // $signature_tags['drm-sign-'.$key] = $signature;
                        $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
                    }
                }

            // }

            $data['subject'] = preg_replace('/\[appointment_date]/', date('Y-m-d'), $data['subject']);
            $data['subject'] = preg_replace('/\[appointment_time]/', date('H:i:s'), $data['subject']);

            app('drm.mailer')->getMailer($authId,$data['email_from'])->send('admin.new_order.email_invoice_template', ['body' => $template], function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function getManualTimes($start, $end, $date)
    {

        $end_range = $this->replaceString($start);
        $end = $this->secondToTimeFormat($end_range + 1800);
        $slots = $this->timeSlot();
        $start_html = '<option selected value="' . $start . '">' . $start . '</option>';
        $end_html = '<option selected value="' . $end . '">' . $end . '</option>';
        foreach ($slots as $key => $value) {
            $start_html .= '<option value="' . $value . '">' . $value . '</option>';
            $end_html .= '<option value="' . $value . '">' . $value . '</option>';
        }
        if ($slots) {
            return response()->json(['success' => true, 'message' => 'Free Time ', 'start' => $start_html, 'end' => $end_html, 'date' => $date]);
        } else {
            return response()->json(['success' => false, 'message' => 'Something went wrong!', 'start' => '']);
        }
    }

    public function sendAppointmentCancelEmail($appointment, $userId, $customerId, $flag = '')
    {
        try {
            $parsedData = DRMParseAppointmentCancelEmailTemplate($appointment, $userId, $customerId, $flag);
            app('drm.mailer')->getMailer($appointment->user_id,$parsedData['senderEmail'])->send('admin.new_order.email_invoice_template', ['body' => $parsedData['body']], function ($messages) use ($parsedData) {
                // $messages->from($parsedData['senderEmail']);
                $messages->to($parsedData['toEmail']);
                $messages->subject($parsedData['subject']);
                if (!empty($parsedData['bcc'])) {
                    $messages->bcc($parsedData['bcc']);
                }
            });
            return true;
        } catch (Exception $exception) {
            return false;
        }
    }

    public function sendAppointmentEmail($appointment)
    {
         try {
            $parsedData = DRMParseAppointmentEmailTemplate($appointment);
            app('drm.mailer')->getMailer($appointment->user_id,$parsedData['senderEmail'])->send('admin.new_order.email_invoice_template', ['body' => $parsedData['body']], function ($messages) use ($parsedData) {
                $messages->to($parsedData['toEmail']);
                $messages->subject($parsedData['subject']);
                if (!empty($parsedData['bcc'])) {
                    $messages->bcc($parsedData['bcc']);
                }

            });

           
            return true;
        } catch (Exception $exception) {
            return false;
        }
    }

    protected function array_flatten($array)
    {
        if (!is_array($array)) {
            return FALSE;
        }
        $result = array();
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $result = array_merge($result, $this->array_flatten($value));
            } else {
                $result[$key] = $value;
            }
        }
        return $result;
    }

    function halfHourTimes($day)
    {
        $formatter = function ($time) use ($day) {

            $end_time = $time + 1800;
            if ($time % 3600 == 0) {
                $start_time = date('H:i', $time);
                $end_time = date('H:i', $end_time);
            } else {
                $start_time = date('H:i', $time);
                $end_time = date('H:i', $end_time);
            }
            return [
                "groupId" => "availableForMeeting",
                "startTime" => $start_time,
                "endTime" => "$end_time:00",
                "daysOfWeek" => "[$day]",
                "slot" => "available",
                "display" => "background",
                "color" => "white"
            ];
        };
        $halfHourSteps = range(0, 47 * 1800, 1800);
        return array_map($formatter, $halfHourSteps);
    }

    // frontend
    public function checkBookingLimit($date, $type)
    {
        $appointment_limit = Appointment::where('customer_id', CRUDBooster::myParentId())->get();
        $exits = false;

        if ($type == 'manager') {
            $mentor_type = 1;
        } elseif ($type == 'Onboarding') {
            $mentor_type = 2;
        } else {
            $mentor_type = null;
        }

        foreach ($appointment_limit as $app) {
            foreach ($app['appointment'] as $dates) {
           // if($dates['date'] == $date && $dates['mentor_type'] == $mentor_type){
                if ($dates['date'] == $date) {
                    $exits = true;
                }
            }
        }
        return response()->json(['success' => ($exits) ? true : false, 'exists' => $exits, 'message' => ' ']);
    }
    protected function secondToTimeFormat($second){
      if ($second % 3600 == 0) {
          return date('H:00', $second);
      } else {
          return date('H:i', $second);
      }
    }


    protected function googleEventCreate($data, $appointment, $user_id){
        try {
            $credentials = DB::table('google_calendar_credentials')->where('user_id',$user_id)->first();
            $calenderConfiguration = $this->googleDataMapping($credentials);
            \Config::set('google-calendar', $calenderConfiguration);

            if ($calenderConfiguration['calendar_id']!='0' && $credentials){
                $email = (trim($data[0]['email'])) ? "Email : ".$data[0]['email']."                                       ": " ";
                $phone = (trim($data[0]['phone'])) ? "Phone : ".$data[0]['phone']." ":" ";
                $event = new Event;
                $event->name =  $data[0]['title'];
                $event->description = $data[0]['description'].''.$email.' '.$phone;
                $event->startDateTime = Carbon::parse($data[0]['start']);
                $event->endDateTime = Carbon::parse($data[0]['end']);
                $event =$event->save();

                $appointment->event_id=$event->id;
                $appointment->save();
            }
        }catch (Exception $e){
            dd($e->getMessage());
        }
    }

    protected function googleEventUpdate($event_id, $appointment,$new_appiontment){
        try{
            $appointment_user_id = isset($appointment->user_id) ? $appointment->user_id : CRUDBooster::myId();
            $credentials = DB::table('google_calendar_credentials')->where('user_id', $appointment_user_id)->first();
            $calenderConfiguration = $this->googleDataMapping($credentials);
            \Config::set('google-calendar', $calenderConfiguration);

            if ($calenderConfiguration['calendar_id']!='0' && !empty($credentials)) {

                $event = Event::find($event_id);;

                $event->name = $new_appiontment[0]['title'];
                $event->description = $new_appiontment[0]['description'];
                $event->startDateTime = Carbon::parse($new_appiontment[0]['start']);
                $event->endDateTime = Carbon::parse($new_appiontment[0]['end']);
                $event =$event->save();

                if ($appointment->event_id==null){
                    $appointment->event_id=$event->id;
                    $appointment->save();
                }
            }
        }catch(Exception $e){

        }
    }

    protected function googleEventDelete($event_id, $appointment, $user_id){
      try{
          $credentials = DB::table('google_calendar_credentials')->where('user_id',$user_id)->first();
          $calenderConfiguration = $this->googleDataMapping($credentials);
          \Config::set('google-calendar', $calenderConfiguration);
          if ($calenderConfiguration['calendar_id']!='0' && !empty($credentials)) {
              if ($event_id!=null) {
                  try {
                      $event = Event::find($appointment->event_id);
                      $event->delete();
                  }catch (Exception $e){

                  }
              }
          }
      }catch (Exception $e){

      }
    }

    protected function incrementAppointmentHistory($user_id, $message, $mentor_type = null ){
        $appointment_history = AppointmentHistory::where('user_id', $user_id)->orderby('id', 'desc')->where('mentor_type',$mentor_type)->first();
        if ($appointment_history) {
            $previous = $appointment_history->current;
        } else {
            $remaining_date = DB::table('takeappointment')
                ->where('user_id', $user_id)
                ->first();
            if($mentor_type == 1){
                $previous = $remaining_date->manager_date;
            }elseif ($mentor_type == 2){
                $previous = $remaining_date->free_date;
            }else{
                $previous = $remaining_date->payment_date_remaining;
            }
        }

        if($mentor_type == 1){                          // 1 for manager
            $column_name = 'manager_date';
        }elseif ($mentor_type == 2){                    //2 for free appointment
            $column_name = 'free_date';
        }else{
            $column_name = 'payment_date_remaining';    // null for mentoring
        }


        AppointmentHistory::insert([
            'user_id' => $user_id,
            'previous' => $previous,
            'current' => $previous + 1,
            'type' => 1,      //1 for user appointment
            'mentor_type' => $mentor_type,
            'message' => $message,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        DB::table('takeappointment')->where('user_id', $user_id)->increment($column_name);
    }

    protected function decrimentAppointmentHistory($user_id, $message, $mentor_type = null ){
        $appointment_history = AppointmentHistory::where('user_id', $user_id)->orderby('id', 'desc')->where('mentor_type',$mentor_type)->first();
        if ($appointment_history) {
            $previous = $appointment_history->current;
        } else {
            $remaining_date = DB::table('takeappointment')
                ->where('user_id', $user_id)
                ->first();
            if($mentor_type == 1){
                $previous = $remaining_date->manager_date;
            }elseif ($mentor_type == 2){
                $previous = $remaining_date->free_date;
            }else{
                $previous = $remaining_date->payment_date_remaining;
            }
        }

        if($mentor_type == 1){                          // 1 for manager
            $column_name = 'manager_date';
        }elseif ($mentor_type == 2){                    //2 for free appointment
            $column_name = 'free_date';
        }else{
            $column_name = 'payment_date_remaining';    // null for mentoring
        }


        AppointmentHistory::insert([
            'user_id' => $user_id,
            'previous' => $previous,
            'current' => $previous - 1,
            'type' => 1,      //1 for user appointment
            'mentor_type' => $mentor_type,
            'message' => $message,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        DB::table('takeappointment')->where('user_id', $user_id)->decrement($column_name);
    }

    public function checkAppointment(){

        $user_id = request()->id;
        $date = Carbon::today();
        if(request()->date){
            $date = Carbon::parse(request()->date)->format('Y-m-d');
        }

        $appointments = Appointment::query();
        $appointments->where('status', '0')->orderBy('id','desc');
        if(!empty($user_id)){
            $appointments->where('user_id',$user_id);
        }
        $appointments  = $appointments->whereDate('created_at', $date)->get();

        $appointment_array = [];
        foreach ($appointments as $app) {
            $google_credentials = DB::table('google_calendar_credentials')->where('user_id',$app->user_id)->first();
            $user = User::find($app->user_id);
            $isConnected = $this->googleIsConnection($google_credentials);

            foreach ($app->appointment as $value) {
                $temp = [];
                $temp['author'] = $user->name;
                $temp['title'] = $value['title'];
                $temp['date'] = $value['date'];
                $temp['start'] = $value['start_time'];
                $temp['end'] = $value['end_time'];
                $temp['type'] = $value['type'];
                $temp['event_id'] = $app->event_id;
                $temp['created_at'] = $app->created_at;
                $temp['updated_at'] = $app->updated_at;
                $temp['connection'] = $isConnected;
            }
            array_push($appointment_array, $temp);
        }




        $google_credentials = DB::table('google_calendar_credentials');
        if(!empty($user_id)) {
            $google_credentials->where('user_id', $user_id);
        }
        $google_credentials  = $google_credentials->get();


        $google_appointment = [];
        foreach ($google_credentials as $credential){
            $isConnect = $this->googleIsConnection($credential);
            if($isConnect){
                $calenderConfiguration = $this->googleDataMapping($credential);
                \Config::set('google-calendar', $calenderConfiguration);
                if ($calenderConfiguration['calendar_id']!='0' && !empty($credential)) {
                    $startDateTime = Carbon::now()->subYear();
                    $google_events = Event::get($startDateTime);
                    if(!empty($google_events)){
                        foreach ($google_events as $event) {
                            $temp = [];
                            $temp['title'] = $event->googleEvent->summary;
                            $temp['start'] = $event->googleEvent->start->dateTime;
                            $temp['end'] = $event->googleEvent->end->dateTime;
                            $temp['email'] = $event->googleEvent->creator->email;
                            $temp['note'] = $event->googleEvent->description;
                            $temp['google_link'] = $event->googleEvent->htmlLink;
                            $temp['created_at'] = $event->googleEvent->created;
                            $temp['updated_at'] = $event->googleEvent->updated;
                            array_push($google_appointment, $temp);
                        }
                    }
                }
            }
        }

        $data['appointments'] = $appointment_array;
        $data['google_appointments'] = $google_appointment;
        return view('admin.appointment_booking.check_appointment',$data);
    }

    public function transerDate()
    {
        if(isLocal()){
            DB::table('takeappointment')->where(function ($a) {
                $a->whereNull('last_renewal_date')->orWhereDate('last_renewal_date', '<', Carbon::now());
            })
                ->select('id', 'renewal_qty', 'manager_renewal_qty', 'payment_date_remaining', 'manager_date')
                ->orderBy('id')
                ->get()
                ->each(function ($item) {
                    // start add history
                    $remaining_date = DB::table('takeappointment')->where('id', $item->id)->first();

                    // if($remaining_date->user_id == 96){

                        $this->decreaseAppointmentDates($remaining_date->user_id);
                        $this->decreaseAppointmentDates($remaining_date->user_id, 1);

                        if ($remaining_date->renewal_qty > 0) {
                            $this->increaseAppointmentDates($remaining_date->user_id, $remaining_date->renewal_qty);    //add mentor dates
                        }

                        if ($remaining_date->manager_renewal_qty> 0) {
                            $this->increaseAppointmentDates($remaining_date->user_id, $remaining_date->manager_renewal_qty, 1);    //add Manager dates
                        }

                        // if ($remaining_date->free_date > 0) {
                        //     $this->increaseAppointmentDates($remaining_date->user_id, 0, 2);    //reset Free dates
                        // }

                    //    dd($remaining_date->payment_date_remaining,$remaining_date->renewal_qty,'mentor',$remaining_date->manager_date,$remaining_date->manager_renewal_qty,'manager');
                    // }


                });
        }
        dd('Transfer Dates');
    }

    protected function decreaseAppointmentDates($user_id, $mentor_type = null){
        $last_month_mentor_date = DB::table('appointment_histories')
        ->where('user_id', $user_id)
        ->where('mentor_type', $mentor_type)
        ->where('type', 4)
        ->whereBetween('created_at',
                    [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()]
                )
        ->orderby('id', 'desc')
        ->first();

        //temp..
        if($last_month_mentor_date->current == $last_month_mentor_date->previous){
            $mentor_renewal_dates = $last_month_mentor_date->current;
        }else{
           $mentor_renewal_dates = $last_month_mentor_date->current - $last_month_mentor_date->previous;
        }

        $last_month_mentor_used = DB::table('appointment_histories')
        ->where('user_id', $user_id)
        ->where('mentor_type', $mentor_type)
        ->whereBetween('created_at',
                    [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()]
        );

        $message = ' ';
        if($mentor_type == 1){
            $last_month_mentor_used->where('message', 'like', '%User takes manager%');
            $message = 'Manager';
        }else{
            $last_month_mentor_used->where('message', 'like', '%User takes mentoring%');
            $message = 'Mentoring';
        }
        $used_dates = $last_month_mentor_used->count();


        // if($used_dates > 0){
        //     $last_month_canceled = DB::table('appointment_histories')
        //     ->where('user_id', $user_id)
        //     ->where('mentor_type', $mentor_type)
        //     ->whereBetween('created_at',
        //                 [Carbon::now()->subMonth()->startOfMonth(), Carbon::now()->subMonth()->endOfMonth()]
        //     )
        //     ->where('message', 'like', '%Appointment canceled%')
        //     ->count();
        //     $used_dates = $used_dates - $last_month_canceled;

        // }


        $expire_date = $mentor_renewal_dates - $used_dates;
        // dump($mentor_renewal_dates,$used, $expire_date,'-----last month dates '.$message);

        if(($expire_date) > 0){
            //expire unused dates b
            $appointment_history = DB::table('appointment_histories')
            ->where('user_id', $user_id)
            ->where('mentor_type', $mentor_type)
            ->orderby('id', 'desc')
            ->first();

        $previous = ($appointment_history->current) ? $appointment_history->current : 0;
        $final_dates = $previous - $expire_date;
        $final_dates = ($final_dates > 0) ? $final_dates : 0;

        DB::table('appointment_histories')->insert([
            'user_id' => $user_id,
            'previous' => $previous,
            'current' => $final_dates,
            'mentor_type' => $mentor_type,
            'type' => 6,  // 6 for expired remaining dates
            'message' => 'Renewal dates ' . $expire_date . ' expired on ' . Carbon::today()->toDateString(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        // end add history
        $update_column = 'payment_date_remaining';
        if ($mentor_type == 1) {
            $update_column = 'manager_date';
        } elseif ($mentor_type == 2) {
            $update_column = 'free_date';
        }

        DB::table('takeappointment')->where('user_id', $user_id)->update([$update_column => $final_dates, 'last_renewal_date' => Carbon::now()]);
        }
    }

    protected function increaseAppointmentDates($user_id, $renew_qty, $mentor_type = null)
    {
        $appointment_history = DB::table('appointment_histories')
            ->where('user_id', $user_id)
            ->where('mentor_type', $mentor_type)
            ->orderby('id', 'desc')
            ->first();

        $previous = ($appointment_history->current) ? $appointment_history->current : 0;

        DB::table('appointment_histories')->insert([
            'user_id' => $user_id,
            'previous' => $previous,
            'current' => $previous + $renew_qty,
            'mentor_type' => $mentor_type,
            'type' => 4,
            'message' => 'Renewal dates ' . $renew_qty . ' added on ' . Carbon::today()->toDateString(),
            'created_at' => now(),
            'updated_at' => now()
        ]);
        // end add history
        $update_column = 'payment_date_remaining';
        if ($mentor_type == 1) {
            $update_column = 'manager_date';
        } elseif ($mentor_type == 2) {
            $update_column = 'free_date';
        }
        DB::table('takeappointment')->where('user_id', $user_id)->update([$update_column => $previous + $renew_qty, 'last_renewal_date' => Carbon::now()]);
    }

    public function getRefferenceEmailSettings()
    {
        $data['page_title'] = 'Refference Email Template';
        $authId = CRUDBooster::myId(); // 7 get reference email settings
        $data['mail'] = DB::table('appointment_email_settings')->where('status', 4)->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        return view('admin.appointment_booking.refference_email_setting', $data);
    }

    public function postAddSaveRefferenceEmailSettings()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'email_template' => $_REQUEST['email_template'],
            'created_at' => now(),
            'updated_at' => now(),
        ];
        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        DB::table('appointment_email_settings')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myId(), 'status' => 4
            ],
            $data
        );

        $user_id = CRUDBooster::myId();

        Cache::forget('appointment_email_settings_' . $user_id);
        clear_remote_cache('appointment_email_settings_' . $user_id);

        $redirect_url = $_REQUEST['redirect_url'];

        if($redirect_url){
            return Redirect::to($redirect_url);
        }

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Appointment email Setting changed', 'success');
    }

    public function postSendRefferenceMail()
    {
        try {
            $authId = CRUDBooster::myId(); // 8 send reference mail
            $email_address = $_REQUEST['email_address'];
            $supplier_name = $_REQUEST['supplier_name'];
            $template = config('system_email_settings.refference_mail_body');
            $data['subject'] = config('system_email_settings.refference_mail_subject');
            $emailSetting = DB::table('appointment_email_settings')->where('status', 4)->where('cms_user_id', $authId)->first();
            $userDets = DB::table('cms_users')->where('id', $authId)->first();
            $data = [];
            $data['email_to'] = $email_address;
            $data['email_from'] = $userDets->email;
            if (!empty($emailSetting)) {
                $data['subject'] = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
                $data['email_from'] = empty($emailSetting->sender_email) ? $userEmail : $emailSetting->sender_email;
                $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
            }

            if(DB::table('cms_users')->where('id',$authId)->first()->ref_id==null){
                DB::table('cms_users')->where('id',$authId)->update(['ref_id'=> Str::random(25)]);
            }

            $user = DB::table('cms_users')->where('id',$authId)->first();

            $supplier_ref_url=url("supplier/registration?ref_id=".$user->ref_id."&user_referred_by=".$user->id);
            $user_ref_url=url("registration/sign-up?ref_id=".$user->ref_id."&user_id=".$user->id);

            $affiliate_link_button = '<a style="background-color: #fd6500; padding: 10px 20px;color: white; text-decoration:none;font-size:14px;font-family:Roboto,sans-serif;border-radius:5px"  class="button" href=' . $user_ref_url . 'target="_blank">Join As A User</a>';
            $supplier_link_button = '<a style="background-color: #fd6500; padding: 10px 20px;color: white; text-decoration:none;font-size:14px;font-family:Roboto,sans-serif;border-radius:5px"  class="button" href=' . $supplier_ref_url . 'target="_blank">Join As A Supplier</a>';

            $template = preg_replace('/\[supplier_name]/', $supplier_name, $template);
            $template = preg_replace('/\[user_affiliate_link]/', $affiliate_link_button, $template);
            $template = preg_replace('/\[supplier_affiliate_link]/', $supplier_link_button, $template);

            $img = $invoiceSetting->logo ?? '';
            $logo = '<img id="display_logo" width="150" src="' . $img . '" alt="' . $invoiceSetting->store_name . '">';

            // $logo = '<img id="display_logo" width="150" src="" alt="">';
            $template = preg_replace('/\[logo]/', $logo, $template);

            $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myId())->pluck('signature','id')->toArray();
            $signature_tags = [];

            if($email_signatures){
                foreach($email_signatures as $key => $signature){
                    // $signature_tags['drm-sign-'.$key] = $signature;
                    $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
                }
            }


            app('drm.mailer')->getMailer($authId,$data['email_from'])->send('admin.new_order.email_invoice_template', ['body' => $template], function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
            });

            return response()->json([
                'success' => true,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
            ]);
        }
    }

    private function getOffDaysInWeek($user_id) {
        $off_days = [];
                
        $slots = AppointmentBooking::where('user_id', $user_id)->first();
        if ($slots->event_slot) {
            foreach ($slots->event_slot as $value) {
                if ($value['slot'] == 'off_day') {
                    $off_days[] = $value['daysOfWeek'];
                }
            }
        }

        return $off_days;
    }

    // appointment start time, end time, start at, end at
    public function appointStartEndTime($start_date, $start_time_param, $end_time_param, $pre = 0, $post = 0, $end_date = null)
    {
        $startDate = Carbon::parse($start_date);
        $endDate   = !empty($end_date) ? Carbon::parse($end_date) : $startDate;

        // start time, start at
        if (!empty($start_time_param)) {
            $startDate->setTimeFromTimeString($start_time_param);
        }

        $start_time = $startDate->format('Y-m-d H:i:00');
        $start_at   = empty($pre) ? $start_time : $startDate->addMinutes($pre)->format('Y-m-d H:i:00');

        // end time, end at
        if (!empty($end_time_param)) {
            $endDate->setTimeFromTimeString($end_time_param);
        }

        $end_time = $endDate->format('Y-m-d H:i:00');
        $end_at   = empty($post) ? $end_time : $endDate->addMinutes($post)->format('Y-m-d H:i:00');

        return [
            'start_time' => $start_time,
            'end_time'   => $end_time,
            'start_at'   => $start_at,
            'end_at'     => $end_at,
        ];
    }

    public function saveLabel(): JsonResponse
    {
        $message = '';
        $data = [];

        $request = $_REQUEST;

        Validator::make($request, [
            'label' => 'required|min:2',
            'type' => 'required',
        ], [
            'label.required' => __("Label name is required."),
        ])->validate();

        $input_id = $request['id'] ?? 0;
        $type_id = $request['type_id'];
        $user_id = CRUDBooster::myId();
    
        try {
            if ($input_id > 0) {
                DB::table('appointment_contact_form_fields')
                    ->where([
                        'id' => $input_id,
                        'user_id' => $user_id,
                        'type_id' => $type_id,
                    ])
                    ->update([
                        'label' => $request['label'],
                        'type' => $request['type'],
                        'updated_at' => now(),
                    ]);

                $data['saved'] = true;
                $message = __("Label updated successfully!");
            } else {
                $input_id = DB::table('appointment_contact_form_fields')
                    ->insertGetId([
                        'user_id' => $user_id,
                        'type_id' => $type_id,
                        'label' => $request['label'],
                        'value' => $request['label'],
                        'type' => $request['type'],
                        'input_name' => \Str::random(8),
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                $data['saved'] = true;
                $message = __("Label created successfully!");
            }

        } catch (\Exception $ex) {
            $message = __("Save failed! Please try again.");
        }

        return response()->json([
            'success' => true,
            'data'    => $data,
            'message' => $message,
        ], 200);
    }

    public function deleteLabel($id): JsonResponse
    {
        $type_id = $_REQUEST['type_id'] ?? 0;
        if ($type_id <= 0) {
            $message = __("Delete process failed! Please try again.");
        }

        $message = '';
        $data = [];
    
        try {
            if ($id > 0) {
                DB::table('appointment_contact_form_fields')
                    ->where([
                        'id' => $id,
                        'user_id' => CRUDBooster::myId(),
                        'type_id' => $type_id,
                    ])
                    ->delete();

                $data['deleted'] = true;
                $message = __("Field deleted successfully!");
            }
        } catch (\Exception $ex) {
            $message = __("Delete process failed! Please try again.");
        }

        return response()->json([
            'success' => true,
            'data'    => $data,
            'message' => $message,
        ], 200);
    }

    
    public function saveCustomiseContactForm(): JsonResponse
    {
        $message = '';
        $data = [];

        $request = $_REQUEST;

        // Validator::make($request, [
        //     'headline' => 'required|min:2',
        // ], [
        //     'headline.required' => __("Form headline is required."),
        // ])->validate();

        $user_id = CRUDBooster::myId();
        $type_id = $request['type_id'];

        try {
            $content = [
                'cal_headline' => $request['cal_headline'],
                'subtext' => $request['subtext'],
                'subtext_show' => isset($request['subtext_show']) ? 1 : 0,
                'headline' => $request['headline'],
                'last_name' => $request['last_name'],
                'email' => $request['email'],
                'phone' => $request['phone'],
                'tos' => $request['tos'],
                'last_name_req' => isset($request['last_name_req']) ? 1 : 0,
                'phone_req' => isset($request['phone_req']) ? 1 : 0,
                'last_name_show' => isset($request['last_name_show']) ? 1 : 0,
                'phone_show' => isset($request['phone_show']) ? 1 : 0,
                'tos_show' => isset($request['tos_show']) ? 1 : 0,
                'updated_at' => now(),
            ];

            $customise_exists = DB::table('appointment_contact_forms')
                ->where([
                    'user_id' => $user_id,
                    'type_id' => $type_id,
                ])
                ->exists();

            if ($customise_exists) { // update
                DB::table('appointment_contact_forms')
                    ->where([
                        'user_id' => $user_id,
                        'type_id' => $type_id,
                    ])
                    ->update($content);

                $data['updated'] = true;
                $message = __("Contact form settings updated successfully!");
            } else { // insert
                $content['user_id'] = $user_id;
                $content['type_id'] = $type_id;
                $content['created_at'] = now();

                DB::table('appointment_contact_forms')->insertGetId($content);

                $data['updated'] = true;
                $message = __("Contact form settings updated successfully!");
            }

            // additional fields save
            $req_keys = array_keys(request()->all());

            DB::table('appointment_contact_form_fields')
                ->where([
                    'user_id' => $user_id,
                    'type_id' => $type_id,
                ])
                ->get()->each(function($row) use($req_keys) {

                    if (in_array('additional_' . $row->input_name, $req_keys)) {
                        DB::table('appointment_contact_form_fields')
                            ->where('id', $row->id)
                            ->update([
                                'value' => request()->input('additional_' . $row->input_name),
                                'required' => request()->has('required_' . $row->input_name) ? 1 : 0,
                                'show' => request()->has('show_' . $row->input_name) ? 1 : 0,
                            ]);
                    }
                });
        } catch (\Exception $ex) {
            $message = __("Update failed! Please try again.");
        }

        return response()->json([
            'success' => true,
            'data'    => $data,
            'message' => $message,
        ], 200);
    }

    public function saveClone(): JsonResponse
    {
        $message = '';
        $data = [];

        $request = $_REQUEST;

        Validator::make($request, [
            'type_name' => 'required|min:2',
            'type_id' => 'required|numeric',
        ], [
            'type_name.required' => __("Appointment type name is required."),
        ])->validate();

        try {
            $type_id = $request['type_id'] ?? 0;

            // 1 appointment type clone
            $type_info = AppointmentType::where([
                    'id' => $type_id,
                    'user_id' => CRUDBooster::myId(),
                ])
                ->first()->toArray();
            
            if (!empty($type_info)) {
                if ($type_info['name'] == $request['type_name']) {
                    throw new Exception(__('Clone type name must be different! Please rename.'));
                }

                unset($type_info['id']);

                $type_info['slug'] = uniqid();
                $type_info['name'] = $request['type_name'];
                $type_info['created_at'] = $type_info['updated_at'] = now();
    
                $new_type_id = AppointmentType::Create($type_info)->id;
    
                if (!empty($new_type_id)) {
                    // 2 appointment type contact form settings clone
                    $this->appointmentTypeContactFormClone($type_id, $new_type_id);
    
                    // 3 appointment type contact form additional fields clone
                    $this->appointmentTypeContactFormFieldsClone($type_id, $new_type_id);
                }
            }
            
            $data['saved'] = true;
            $message = __("Clone created successfully!");

        } catch (\Exception $ex) {
            $message = $ex->getMessage();
        }

        return response()->json([
            'success' => true,
            'data'    => $data,
            'message' => $message,
        ], 200);
    }

    private function appointmentTypeContactFormClone($type_id, $new_type_id) {
        $form_info = DB::table('appointment_contact_forms')->where([
            'user_id' => CRUDBooster::myId(),
            'type_id' => $type_id,
        ])
        ->first();

        if (!empty($form_info)) {
            $form_info = (array) $form_info;

            unset($form_info['id']);
            $form_info['type_id'] = $new_type_id;
            $form_info['created_at'] = $form_info['updated_at'] = now();

            DB::table('appointment_contact_forms')->insert($form_info);
        }
    }

    private function appointmentTypeContactFormFieldsClone($type_id, $new_type_id) {
        $form_fields = DB::table('appointment_contact_form_fields')->where([
            'user_id' => CRUDBooster::myId(),
            'type_id' => $type_id,
        ])
        ->get()->toArray();

        if (!empty($form_fields)) {
            foreach ($form_fields as $index => $row) {
                $row = (array) $row;

                unset($row['id']);
                $row['type_id'] = $new_type_id;
                $row['created_at'] = $row['updated_at'] = now();
    
                DB::table('appointment_contact_form_fields')->insert($row);
            }
        }
    }

    public function embedInfo(): JsonResponse {
        $data = [
            'widget_name' => '',
            'widget_color' => '',
        ];

        $request = $_REQUEST;

        $type_id = $request['type_id'] ?? 0;
        $next_id = $request['next_id'] ?? 0;

        try {
            $info = AppointmentType::where([
                    'id' => $type_id,
                    'user_id' => $next_id,
                ])
                ->select('widget_name', 'widget_color')
                ->first();

            if (!empty($info)) {
                $data = [
                    'widget_name' => $info->widget_name,
                    'widget_color' => $info->widget_color,
                ];
            }
        } catch (\Exception $ex) {

        }

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }
}