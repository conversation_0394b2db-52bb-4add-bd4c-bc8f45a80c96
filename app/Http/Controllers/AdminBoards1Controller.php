<?php

namespace App\Http\Controllers;

use App\Appointment;
use App\Notifications\DRMNotification;
use App\ProjectTasks;
use App\User;
use App\UserNotificationSetting;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request as LaravelRequest;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Request;
use Illuminate\Support\Facades\Session;

class AdminBoards1Controller extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "name";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = true;
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = true;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "drm_projects";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "Title", "name" => "title"];
        $this->col[] = ["label" => "Customer Id", "name" => "drm_customer_id", "join" => "drm_customers,company_name"];
        $this->col[] = ["label" => "Total Cost", "name" => "total_cost"];
        $this->col[] = ["label" => "Status", "name" => "status"];

        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'Title', 'name' => 'title', 'type' => 'text', 'validation' => 'required|string|min:3|max:100', 'width' => 'col-sm-10', 'placeholder' => 'Enter Project Title'];
        $this->form[] = ['label' => 'Company Name', 'name' => 'drm_customer_id', 'type' => 'select2', 'validation' => 'required', 'width' => 'col-sm-10', 'datatable' => 'drm_customers,company_name'];
        $this->form[] = ['label' => 'Total Cost', 'name' => 'total_cost', 'type' => 'number', 'validation' => 'required|numeric', 'width' => 'col-sm-10'];
        $this->form[] = [
            'label' => 'Status', 'name' => 'status', 'type' => 'select',
            'dataenum' => 'not_started|Not Stated;on_hold|On Hold;in_progress|In Progress;finished|Finished;cancelled|Cancencled',
            'validation' => 'required', 'width' => 'col-sm-10'
        ];
        // $this->form[] = ['label'=>'Project Members','name'=>'members[]','type'=>'select2_multiple','validation'=>'required',];
        $this->form[] = ['label' => 'Description', 'name' => 'description', 'type' => 'textarea', 'width' => 'col-sm-10'];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ['label'=>'Title','name'=>'title','type'=>'text','validation'=>'required|string|min:3|max:100','width'=>'col-sm-10','placeholder'=>'Enter Project Title'];
        //$this->form[] = ['label'=>'Company Name','name'=>'drm_customer_id','type'=>'select2','validation'=>'required','width'=>'col-sm-10','datatable'=>'drm_customers,company_name'];
        //$this->form[] = ['label'=>'Total Cost','name'=>'total_cost','type'=>'number','validation'=>'required|numeric','width'=>'col-sm-10'];
        //$this->form[] = ['label'=>'Status','name'=>'status','type'=>'select','validation'=>'required','width'=>'col-sm-10','datatable'=>'drm_projects,status'];
        //$this->form[] = ['label'=>'Description','name'=>'description','type'=>'textarea','width'=>'col-sm-10'];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();
        //  $this->sub_module[] = ['label'=>'Sub Module','path'=>'lists','parent_columns'=>'name,created_at','foreign_key'=>'board_id','button_color'=>'success','button_icon'=>'fa fa-bars'];


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();
        $this->addaction[] = ['label' => 'Project Acitivity', 'url' => CRUDBooster::mainpath('project/[id]'), 'icon' => 'fa fa-check', 'color' => 'warning'];

        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = ".small-box{cursor: pointer;border-radius: 5px;padding:15px 10px;}
		.small-box .icon i{vertical-align: top;font-size:35px;transition: .5s;vertical-align: top;margin-top: 18px;}.small-box:hover i{font-size: 40px;transition: .5s;}.margin-style{width: 159px;}.small-box>.inner{padding: 0px;}";


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();

        $this->index_statistic = array();
        $this->index_statistic[] = ['label' => 'Not started', 'count' => DB::table('drm_projects')->where('status', 'not_started')->count(), 'icon' => 'fa fa-area-chart', 'color' => 'success btn-warning', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'In progress', 'count' => DB::table('drm_projects')->where('status', 'in_progress')->count(), 'icon' => 'fa fa-google-wallet', 'color' => 'success btn-primary', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'On hold', 'count' => DB::table('drm_projects')->where('status', 'on_hold')->count(), 'icon' => 'fa fa fa-line-chart', 'color' => 'success btn-success', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'Finished', 'count' => DB::table('drm_projects')->where('status', 'finished')->count(), 'icon' => 'fa fa-briefcase', 'color' => 'success btn-danger', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'Canceled', 'count' => DB::table('drm_projects')->where('status', 'canceled')->count(), 'icon' => 'fa fa-file-image-o', 'color' => 'success btn-primary', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'Total Data', 'count' => DB::table('drm_projects')->count(), 'icon' => 'fa fa-pie-chart', 'color' => 'success btn-default', 'width' => 'col-md-2'];


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {

        //Your code here
        return $query->where('cms_user_id', CRUDBooster::myParentId());

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here
        $postdata['user_id'] = CRUDBooster::myParentId();

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here
        $postdata['user_id'] = CRUDBooster::myParentId();

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }


    //index
    public function getIndex1()
    {
        //First, Add an auth


        //Create your own query
        $data = [];
        $data['page_title'] = 'Project Summary';
        $data['projects'] = DB::table('drm_projects')->where('cms_user_id', CRUDBooster::myParentId())->orderby('id', 'desc')->get();

        //Create a view. Please use `cbView` method instead of view method from laravel.
        $this->cbView('admin.drm_projects.index', $data);
    }

    //add
    public function getAdd()
    {
        $data = [];
        $data['page_title'] = 'Add Project Details';

        if (CRUDBooster::isSuperadmin()) {
            $data['customers'] = DB::table('drm_customers')->get();
        } else {
            $data['customers'] = DB::table('drm_customers')->where('user_id', CRUDBooster::myParentId())->get();
        }

        $data['members'] = DB::table('cms_users')->get();

        //Please use cbView method instead view method from laravel
        $this->cbView('admin.drm_projects.add', $data);
    }

    // post add project
    public function postAddSave()
    {
        $validator = Validator::make($_REQUEST, [
            "title" => "required|string",
            "drm_customer_id" => "required",
            "start_date" => "required|date",
            "total_cost" => "required|numeric",
        ]);

        if ($validator->fails()) {
            return $validator->messages();
        }

        $project_id = DB::table('drm_projects')->insertGetId([
            'title' => $_REQUEST['title'],
            'drm_customer_id' => $_REQUEST['drm_customer_id'],
            'tags' => $_REQUEST['tags'],
            'start_date' => $_REQUEST['start_date'],
            'due_date' => $_REQUEST['due_date'],
            'rate_type' => $_REQUEST['rate_type'],
            'total_cost' => $_REQUEST['total_cost'],
            'status' => $_REQUEST['status'],
            'description' => $_REQUEST['description'],
            'cms_user_id' => CRUDBooster::myParentId(),
        ]);


        if (isset($_REQUEST['drm_project_member_ids'])) {
            foreach ($_REQUEST['drm_project_member_ids'] as $member_id) {
                DB::table('drm_project_members')->insert([
                    'drm_project_id' => $project_id,
                    'cms_user_id' => $member_id
                ]);
                $message_title = 'You have been assign to an new project !';
                User::find($member_id)->notify(new DRMNotification($message_title, 'PROJECT_MANAGEMENT_MODULE'));
            }
        }

        return redirect('admin/boards')->with('success', 'Project added');

    }


    // get project edit
    public function getEdit($id)
    {
        //Create an Auth
        $data = [];
        $data['page_title'] = 'Edit Data';
        $data['project'] = DB::table('drm_projects')->where('id', $id)->first();

        if (CRUDBooster::isSuperadmin()) {
            $data['customers'] = DB::table('drm_customers')->get();
        } else {
            $data['customers'] = DB::table('drm_customers')->where('user_id', CRUDBooster::myParentId())->get();
        }

        $data['members'] = DB::table('cms_users')->get();
        $data['project_members'] = DB::table('drm_project_members')->where('drm_project_id', $id)->get();

        session(['project_id' => $id]);

        //Please use cbView method instead view method from laravel
        $this->cbView('admin.drm_projects.edit', $data);
    }

    public function searchUser()
    {
        if (isset($_REQUEST['param'])) {
            $users = DB::table('cms_users')->whereNotNull('email_verified_at')->where('name', 'like', '%' . $_REQUEST['param'] . '%')
                ->orWhere('email', 'like', '%' . $_REQUEST['param'] . '%')->get();
            if ($users->isNotEmpty())
                return response()->json(['success' => true, 'message' => 'search user list', 'data' => $users], 200);
        }
        return response()->json(['success' => false, 'message' => 'No Data Found']);
    }

    // post -- project edit
    public function postEditSave()
    {
        $project_id = session('project_id');

        if ($project_id == null) {
            return redirect('admin/boards')->with('error', 'Project id not found');
        }

        DB::table('drm_projects')->where('id', $project_id)->update([
            'title' => $_REQUEST['title'],
            'drm_customer_id' => $_REQUEST['drm_customer_id'],
            'tags' => $_REQUEST['tags'],
            'start_date' => $_REQUEST['start_date'],
            'due_date' => $_REQUEST['due_date'],
            'rate_type' => $_REQUEST['rate_type'],
            'total_cost' => $_REQUEST['total_cost'],
            'status' => $_REQUEST['status'],
            'description' => $_REQUEST['description'],
        ]);

        if (isset($_REQUEST['drm_project_member_ids'])) {
            DB::table('drm_project_members')->where('drm_project_id', $project_id)->delete();
            foreach ($_REQUEST['drm_project_member_ids'] as $member_id) {
                DB::table('drm_project_members')->insert([
                    'drm_project_id' => $project_id,
                    'cms_user_id' => $member_id
                ]);
                $message_title = 'A update in project is available!';
                User::find($member_id)->notify(new DRMNotification($message_title, 'PROJECT_MANAGEMENT_MODULE'));
            }
        }

        return redirect('admin/boards');

    }

    public function notificationTriggerSession(LaravelRequest $request)
    {
        if ($request->session()->has('notification_hooks')) {
            $request->session()->forget('notification_hooks');
            return response()->json(['success' => true], 200);
        }
        return response()->json(['success' => false], 200);
    }

    public function notificationTriggerOnOff(LaravelRequest $request)
    {

        $keys = config('global.notification_positions')?? [];
        $keys = array_keys($keys);
        $position_str = implode(',', $keys);


        $request->validate([
            'position' => 'required|in:'.$position_str,
            'checkbox' => 'required|in:is_email,is_telegram',
            'value' => 'nullable'
        ],
        [
            'position' => 'Invalid Data!',
            'checkbox' => 'Invalid option!'
        ]);

        try{
            $value = $request->value? 1 : 0;
            $checkbox = $request->checkbox;
            $position = $request->position;
            $user_id = ($position == 5) ? CRUDBooster::myId() : CRUDBooster::myParentId();

            $inserted = UserNotificationSetting::updateOrCreate([
                'user_id' => $user_id,
                'sidebar_id' => $position,
            ],
            [
              $checkbox  => $value
            ]);

            if($inserted){
                return response()->json([
                    'success' => true,
                    'message' => 'Data update successfully!',
                    'position' => $position,
                ], 200);
            }

            throw new \Exception('Nothing changed!');

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function autoPopUpCommentSection()
    {
        $comments = DB::table('drm_task_comments as comment')
            ->join('cms_users as users', 'users.id', '=', 'comment.cms_user_id')
            ->where('task_id', $_REQUEST['task_id'])
            ->select('comment.*', 'users.id as user_id', 'users.name')
            ->orderBy('created_at', 'desc')
            ->get();
        if ($comments->isNotEmpty()) {
            $task = DB::table('drm_project_tasks')->find($_REQUEST['task_id']);
            $html = view('admin.drm_projects.partials.auto_pop_up_comment', compact('comments'))->render();
            return response()->json(['success' => true, 'data' => $html, 'task_name' => $task->name], 200);
        }
    }

    public function storeNotifyEmail(LaravelRequest $request)
    {
        $keys = config('global.notification_positions')?? [];
        $keys = array_keys($keys);
        $position_str = implode(',', $keys);

        $request->validate([
            'notify_email' => 'required|email',
            'position' => 'required|in:'.$position_str
        ]);

        try{
            $position = $request->position;
            $notify_email = $request->notify_email;
            $user_id = ($position == 5) ? CRUDBooster::myId() : CRUDBooster::myParentId();

            $inserted = UserNotificationSetting::updateOrCreate([
                'user_id' => $user_id,
                'sidebar_id' => $position,
            ],
            [
                'notify_email' => $notify_email
            ]);

            if($inserted){
                return response()->json([
                    'success' => true,
                    'message' => 'Data update successfully!',
                    'position' => $position,
                ], 200);
            }

            throw new \Exception('Nothing changed!');
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function storeUserNotifyEmail(LaravelRequest $request)
    {
        $user = User::find(CRUDBooster::myParentId());
        if (!empty($user)) {
            $user->notify_email = $request->email;
            if ($user->save())
                return response()->json(['success' => true, 'message' => 'Email Saved successfully'], 200);
        }
        return response()->json(['success' => false]);
    }

    public function loadTelegramPage()
    {
        $data['page_title'] = __('telegram.page_title');
        return view('admin.drm.telegram_token', $data);
    }

    public function storeTelegramToken(LaravelRequest $request)
    {
        $user = User::find(CRUDBooster::myParentId());
        if (!empty($user)) {
            $user->telegram_user_token = $request->telegram_token;
            if ($user->save()) {
                Session::flash('success_message', 'Telegram Token Stored Successfully');
            } else {
                Session::flash('error_message', 'Failed to Store');
            }
        } else {
            Session::flash('warn_message', 'No User Found');
        }
        return redirect()->back();
    }

    //get project details
    public function getProject($id)
    {


        $project = DB::table('drm_projects')->find($id);

        $data['page_title'] = "Project: " . $project->title;
        $data['project_id'] = $id;


        // $this->cbView('admin.menudashboard.board_detail_view',$data);
        $this->cbView('admin.drm_projects.project_detail', $data);
        // return view('admin.drm_projects.project_detail',$data);
    }

    // ----------------- card -----------------------

    public function getGetAllCards()
    {
        $cards = DB::table('drm_project_cards')->where('drm_project_id', $_REQUEST['project_id'])->orderBy('position')->get();

        $card_with_lists = [];

        foreach ($cards as $card) {
            $lists = DB::table('drm_project_card_lists')->where('drm_project_card_id', $card->id)->orderBy('position')->get();
            $card_with_lists[] = [
                "card" => $card,
                "lists" => $lists
            ];
        }

        return response()->json($card_with_lists);
    }

    // add new card ajax
    public function postAddNewCard()
    {

        $card_id = DB::table('drm_project_cards')->insertGetId([
            'title' => $_REQUEST['title'],
            'position' => $_REQUEST['position'],
            'drm_project_id' => $_REQUEST['project_id'],
        ]);
        return response()->json($card_id);
    }

    public function saveTaskTitle()
    {
        $cardList = ProjectTasks::find($_POST['task_id']);
        if (!empty($cardList)) {
          $task_info = DB::table('drm_project_tasks')->where('id',$_POST['task_id'])->select('start_date','due_date','name','description','cover_image')->first();
          $task_checklist = DB::table('drm_task_checklist')->where('id',$_POST['task_id'])->select('title','image')->first();
          $taskeEvent[] = [
              'title' => $_POST['name'],
              'start' => $task_info->due_date,
              'description' => $task_info->description,
              'constraint' => "availableForMeeting",
              'slot' => "task",
              'tast_id' => $_POST['task_id'],
              'type' => 6,    //6 for task slot
              'display' => 'background',
              'start_date' => $task_info->start_date,
              'end_date' => $task_info->due_date,
              'task_title' => $task_checklist->title,
              'image' => $task_info->cover_image,
              'color' => '#fd6500'
          ];


            $cardList->name = $_POST['name'];
            if ($cardList->save()){
              Appointment::updateOrCreate(
                ['slug' =>  $_POST['task_id']],
                [
                    'user_id' => CRUDBooster::myParentId(),
                    'my_id' => CRUDBooster::myId(),
                    'appointment' => $taskeEvent,
                ]);
              return response()->json(['success' => true, 'data' => $cardList], 200);
            }
        }
        return response()->json(['success' => false, 'data' => 'Not Found'], 200);
    }

    public function editCheckList()
    {
        $checkList = DB::table('drm_task_checklist')->where('id', '=', $_REQUEST['id'])->first();
        if (!empty($checkList)) {
            $html = view('admin.drm_projects.partials.edit_check_list', compact('checkList'))->render();
            return response()->json(['success' => true, 'title' => 'Edit Checklist', 'data' => $html], 200);
        }
        return response()->json(['success' => false, 'data' => 'Not Found'], 200);
    }

    public function editSaveCheckList(LaravelRequest $request)
    {
        $validator = Validator::make($request->all(), [
            'checklist_id' => 'required|numeric',
            'checklist_title' => 'required|string',
            'checklist_image' => 'nullable|image|max:3000'
        ]);
        if ($validator->fails()) {
            $errors = [];
            $result = $validator->errors();
            if (!empty($result->first('checklist_image')))
                $errors['checklist_image'] = $result->first('checklist_image');
            if (!empty($result->first('checklist_title')))
                $errors['checklist_title'] = $result->first('checklist_title');
            return response()->json([
                'success' => false,
                'errors' => $errors
            ], 200);
        }

        DB::table('drm_task_checklist')
            ->where('id', '=', $request->checklist_id)
            ->update([
                'title' => $request->checklist_title,
                'start_date' => Carbon::parse($request->start_date)->format('Y-m-d'),
                'end_date' => Carbon::parse($request->end_date)->format('Y-m-d')
            ]);

        if ($request->hasFile('checklist_image')) {
            $file = $request->file('checklist_image');
            $name = 'cl' . time() . '.' . $file->getClientOriginalExtension();
            $path = 'public/checklist_images';
            Storage::putFileAs($path, $file, $name);

            DB::table('drm_task_checklist')
                ->where('id', '=', $request->checklist_id)
                ->update(['image' => $name]);
        }

        return response()->json(['success' => true, 'data' => DB::table('drm_task_checklist')
            ->where('id', '=', $request->checklist_id)->first()], 200);
    }

    public function sideBarNotificationFire($hook)
    {
        $notification_trigger = DB::table('notification_trigger')->where('hook', '=', $hook)->first();
        if (!empty($notification_trigger)) {
            $message_title = $notification_trigger->title;
            User::find(CRUDBooster::myParentId())->notify(new DRMNotification($message_title, $hook));
            echo "Notification Send Successful";
            return true;
        }
        echo "Invalid Hook";
        return false;
    }

    public function getChecklistData(LaravelRequest $request)
    {
        $checklists = DB::table('drm_task_checklist')
            ->join('cms_users', 'cms_users.id', '=', 'drm_task_checklist.cms_user_id')
            ->where('task_id', '=', $request->id)
            ->select('drm_task_checklist.*', 'cms_users.name as user_name')
            ->orderBy('drm_task_checklist.position', 'asc')
            ->get();
        if ($checklists->isNotEmpty()) {
            $html = view('admin.drm_projects.partials.checklist', compact('checklists'))->render();
            return response()->json(['success' => true, 'html' => $html], 200);
        }
        return response()->json(['success' => false, 'html' => 'No Data Found'], 200);
    }

    public function updateChecklistPosition(LaravelRequest $request)
    {
        foreach ($request->data as $key => $value) {
            DB::table('drm_task_checklist')
                ->where('id', '=', $value['checklist_id'])
                ->update(['position' => $value['position']]);
        }
        return response()->json(['success' => true, 'message' => 'updated'], 200);
    }

    public function deleteAudioFile(LaravelRequest $request)
    {
        $taskComment = DB::table('drm_task_comments')->where('id', '=', $request->id)->first();
        if (!empty($taskComment)) {
            if (!empty($taskComment->audio_file)) {
                $splitValue = explode('/', $taskComment->audio_file);
                if (Storage::disk('spaces')->exists($splitValue[3])) {
                    Storage::disk('spaces')->delete($splitValue[3]);
                }
                DB::table('drm_task_comments')->where('id', '=', $request->id)->update(['audio_file' => null]);
                return response()->json(['success' => true, 'message' => 'Delete Successful'], 200);
            }
        }
        return response()->json(['success' => false, 'message' => 'No Data Found'], 200);
    }

    // update card position
    public function postUpdateCardPosition()
    {
        // $_REQUEST['card_position'][0]["id"]
        foreach ($_REQUEST['card_position'] as $key => $card) {
            DB::table('drm_project_cards')->where("id", $card["id"])
                ->update(['position' => $card["position"]]);
        }
        return response()->json("updated");
    }

    // -------------- list -------------

    // add-new-list
    public function postAddNewList()
    {
        $list_id = DB::table('drm_project_card_lists')->insertGetId([
            'title' => $_REQUEST['title'],
            'position' => $_REQUEST['position'],
            'drm_project_card_id' => $_REQUEST['card_id'],
        ]);
        return response()->json($list_id);
    }

    public function updateUserChatId(LaravelRequest $request)
    {
        return DB::table('cms_users')
            ->where('id', '=', CRUDBooster::myParentId())
            ->update(['telegram_channel_id' => $request->chatId]);
    }

    // check over due
    public function checkOverDue()
    {
        $task = DB::table('drm_project_tasks')->find($_REQUEST['task_id']);
        if (!empty($task)) {
            $today = Carbon::now();
            $dueDate = Carbon::parse($task->due_date);
            if ($today->gt($dueDate)) { // today is newer than due_date
                return response()->json(['success' => true, 'task' => $task, 'over_due' => true], 200);
            }
            return response()->json(['success' => true, 'task' => $task, 'over_due' => false], 200);
        }
        return response()->json(['success' => false], 200);
    }

}
