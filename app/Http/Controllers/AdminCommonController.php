<?php namespace App\Http\Controllers;

use App\Notifications\DRMTelegramNotification;
  use App\CsvHeader;
	use Session;
	use Request;
	use DB;
  use URL;
	use CRUDBooster;
	use Illuminate\Support\Facades\Schema;
	use DigiStore24\DigiStoreApi;
	use App\Helper\LengowApi;
	use App\User;
	use App\DeliveryCompany;
	use App\DrmImport;
	use App\DrmProduct;
	use App\TmpDrmProduct;
	use App\DrmProductField;
	use App\DrmProductProfitMargin;
	use App\UserCsvHeaderValue;
	use Illuminate\Support\Facades\Storage;
	use Illuminate\Support\Str;
	use PhpOffice\PhpSpreadsheet\IOFactory;
	use DataTables as DataTables;
	use App\DrmProductCountryVat;
	use App\ProductCategory;
	use Carbon\Carbon;
	use XMLReader;
	use SimpleXMLElement;
	use Illuminate\Pagination\LengthAwarePaginator;
  use App\Notifications\DRMNotification;
  use App\Events\progressEvent;

	class AdminCommonController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "name";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "commons";
			$this->sidebar_mode = "collapse-mini";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Name","name"=>"name"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Name','name'=>'name','type'=>'text','validation'=>'required|string|min:3|max:70','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Name","name"=>"name","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }

			public function getMail(){

					return view('admin.dashboard.posteingang');
			}

			public function getSubscription(){

				//	$digi = new DigiStoreApi(config('app.digi_api_key'));
			//	$a=new LengowApi();
					//$a->getToken();
					//dd($digi);
					return view('admin.menudashboard.subscription');
			}


			public function getCancellation(){

					return view('admin.menudashboard.cancellation');
			}

			public function getBuchTwo(){

					return view('admin.menudashboard.buchtwo');
			}
			public function getLieferanten(){

					return view('admin.menudashboard.lieferanten');
			}
			public function getKunden(){

					return view('admin.menudashboard.kunden');
			}
			public function getDrmMember(){

					return view('admin.menudashboard.drmmember');
			}
			public function getTmpDrmProducts(){

			    ini_set('memory_limit',-1);
				//if($id!=null){
				    $id = $_REQUEST['id'];
					//$products = User::find(CRUDBooster::myId())->tmp_drm_products()->where('drm_import_id',$id)->get();

					$products = TmpDrmProduct::where('drm_import_id',$id)->get();
				// 	return DataTables::of($products)
				// 	->make(true);
				//}

				//$products = User::find(CRUDBooster::myId())->tmp_drm_products;
                $all_products = [];
				foreach ($products as $key => $value) {
                  $json = json_decode($value->image);
                  $image ='';
                  foreach ($json as $i=> $img) {
                    if($i == count($json)-1){
                      $image.=$img->src;
                    }else {
                      $image.=$img->src.',';
                    }
                  }
                  $value->image = $image;
                  $all_products[] = $value;
                }
				return DataTables::of($all_products)->make(true);
			}

			public function getGorillaProducts(){
				return view('admin.menudashboard.drm_products');
			}
			public function getGorillaProductsAjax(){
				$products=User::find(CRUDBooster::myId())->drm_products;
				// $p= (GorillaProduct)$products;
				// dd($products);
				return DataTables::of($products)
				->make(true);
			}

			public function getDrmProduct(){
				$product = DrmProduct::find(Request::input('id'));
				// dd($product);
				return $product;
			}

			public function postGorillaProductUpdate(){
				$product = DrmProduct::find(Request::input('id'));
				$product->name=Request::input('name');
				$product->item_number=Request::input('item_number');
				$product->ean=Request::input('ean');
				$product->description=Request::input('description');
				$product->image=Request::input('image');
				$product->ek_price=Request::input('ek_price');
				$product->vk_price=Request::input('vk_price');
				$product->vat=Request::input('vat');
				$product->stock=Request::input('stock');
				$product->category=Request::input('category');
				$product->save();
				$import=$product->drm_imports;

				return "success";
			}
			public function getAllOrder(){
					//$digi = new DigiStoreApi(config('app.digi_api_key'));
					//$key='283689-pNkLnEjrYYAJt1lRbmbZn0gmFegtnUecrdJxS5h2';
					$key='304282-I9hUcwVlDQmJGwRjKpza6ddxZBlKhqoqdhTEocMZ';
					$digi = DigiStoreApi::connect($key);
					$search=array();
					if(isset($_GET['first_name'])){
						$search['first_name']=$_GET['first_name'];
					}
					if(isset($_GET['purchase_id'])){
						$search['purchase_id']=$_GET['purchase_id'];
					}

					$allorder=$digi->listTransactions('-365d','now' ,$search,'date', 'asc');
					//dd($allorder);
 					$order=$allorder->transaction_list;
 					$perPage = 50;
					$page = 1;
					$collection = collect($order);

					$order = new LengthAwarePaginator($collection->forPage($page, $perPage), $collection->count(), $perPage, $page, ['path'=>CRUDBooster::adminPath('common/all-order')]);
					//dd($order);

					return view('admin.menudashboard.allorders',compact('order'));
			}
			public function getVideoTraining(){

					return view('admin.menudashboard.videotraining');
			}
			public function getLengowMentoring(){

					return view('admin.menudashboard.lengowmentoring');
			}
			public function getProjectManagement(){

					return view('admin.menudashboard.projectmanagement');
			}
			public function getSortiment(){

					return view('admin.menudashboard.sortiment');
			}
			public function getGruppen(){

					return view('admin.menudashboard.gruppen');
			}
			public function getImportverwaltung(){

					return view('admin.menudashboard.importverwaltung');
			}



		private function csvToArray($csv,$type,$delimiter){
		ini_set('memory_limit',-1);
		$paths=explode(';',$csv);
		$key=null;
		$key_count=0;
		$array = array();
		foreach($paths as $path){
        if($type =='csv'|| $type == 'txt'){
            // if(filesize($path)>20000000){
            //     $array = csvToArray($path);
            //     return $array;
            // }
            // else{
              $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
              $reader->setInputEncoding('UTF-8');
              $reader->setDelimiter($delimiter);
              $spreadsheet = $reader->load($path);
           // }
        }

		else{
          $spreadsheet =IOFactory::load($path);
        }
			$spreadsheet = $spreadsheet->getActiveSheet()->toArray();

			if($key==null){
				//$key = $spreadsheet[0];

				$key = array_map('trim', $spreadsheet[0]);
				$key_count=count($key);
			}
			unset($spreadsheet[0]);
			foreach($spreadsheet as $row){
			    if(!containsOnlyNull($row)){
			        if(count($row)==$key_count){
    					$array[] = array_combine($key, $row);
    				}
			    }
			}
		}
		return $array;
	}


			private function setMinMaxPrice($id,$header){
				$drm= DrmImport::find($id);
				$paths=$drm->csv_file_path;
				$index=0;
				foreach(json_decode($drm->csv_headers) as $head){
					if($head==$header){
						break;
					}
					$index++;
				}
				$paths=explode(';',$paths);
				$min=0;
				$max=0;
				$touched=0;
				foreach($paths as $path){

          $type = pathinfo($path, PATHINFO_EXTENSION);

          if($type == 'xml'){
            $spreadsheet = $this->xmlToArray($path);
          }
          else{
            $spreadsheet =IOFactory::load($path);
  					$spreadsheet= $spreadsheet->getActiveSheet()->toArray();
  					unset($spreadsheet[0]);
  					if($touched==0){
  						$min=$spreadsheet[1][$index];
  						$max=$spreadsheet[1][$index];
  						$touched=1;
  					}
          }

					foreach($spreadsheet as $row){
						$val=$row[$index];
						if($val==''){
							continue;
						}
						if($min>$val){
							$min=$val;
						}
						if($max<$val){
							$max=$val;
						}
					}
				}
				$drm->max_price=$max;
				$drm->min_price=$min;
				$drm->save();
			}



			private function csvToCsvHeaderJson($file,$delimiter,$cloud = true){
			 ini_set('memory_limit',-1);
				$paths=explode(';',$file);
				$headers=null;
				$count=0;
			    foreach($paths as $path){
			        $file_type = pathinfo($path, PATHINFO_EXTENSION);
					try{
					    if($file_type == 'csv' || $file_type == 'txt'){
                            // if(filesize($path)>20000000){
                            //   Request::session()->put('demoData',getDemoData($path));
                            //   $headers = getCsvHeader($path);
                            //   return $headers;
                            // }
                            // else{
                              $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Csv');
                              $reader->setInputEncoding('UTF-8');
                              $reader->setDelimiter($delimiter);
                              $spreadsheet = $reader->load($path);
                            //}
					    }
					    else{
					       $spreadsheet = IOFactory::load($path);
					    }
					    $spreadsheet= $spreadsheet->getActiveSheet()->toArray();

    					}catch(Exception $e){
                            echo $e;
    				    }


					if($headers==null){
						$headers=$spreadsheet[0];

						if(count($headers)<2){
							Request::session()->put('curr_tab',null);
							return redirect()->back()->withErrors(['It seems your CSV file\'s data is not valid']);
						}
						Request::session()->put('demoData',json_encode($spreadsheet[1]));
					}

					$cnt=count($spreadsheet[0]);
					if($count!=0){
						if($count!=$cnt){
							return abort(500);
						}
					}
					$count=$cnt;
			    }
			    //$headers_utf8 = makeArrayUtf8($headers);

				return json_encode($headers);
			}

  function file_get_contents_utf8($fn) {
    $content = file_get_contents($fn);
    return mb_convert_encoding($content, 'UTF-8',
    mb_detect_encoding($content, 'UTF-8, ISO-8859-1', true));
  }

	public function postFields(Request $request){
	 ini_set('memory_limit',-1);
    $message = ['text' => 'Initializing...','percent'=>'10'];
    event(new progressEvent($message));

		Request::session()->put('curr_tab','fields');
		$company_id;
		$paths=array();
		$url_file=null;
		$type=null;

		if(Request::input('file_type')==1){
			$type='file';
			if(!Request::hasFile('csv_file')){
				return abort(500);
			}
			foreach(Request::file('csv_file') as $file){

        $message = ['text' => 'Uploading File...','percent'=>'50'];
        event(new progressEvent($message));

				$path=$file->store('public/csv_files');
				$paths[]=str_replace('public','storage',$path);
			}
		}
      else if(Request::input('file_type')!=4) {
        $check_file = DB::table('drm_imports')->where('user_id',CRUDBooster::myId())->where('file_url',trim(Request::input('csv_link')))->count();
        if($check_file){
          $message = ['error' => 'File already exists !','percent'=>'100'];
          event(new progressEvent($message));
          return false;
        }
        else{
          $type='url';
      		$url_file=trim(Request::input('csv_link'));

          $message = ['text' => 'Getting Data From : <i>'.substr(Request::input('csv_link'),40).'...</i>','percent'=>'50'];
          event(new progressEvent($message));

      		$csv_data= $this->file_get_contents_utf8($url_file);

      		$rand=Str::random(40);
      		Storage::disk('local')->put('public/csv_files/'.$rand.'.csv',$csv_data);
      		$paths[]= 'storage/csv_files/'.$rand.'.csv';
        }
    }

        if(Request::input('file_type')==4){
          $type = 'xml';
          $rand=Str::random(40);
          $url_file=trim(Request::input('csv_link'));

          $message = ['text' => 'Getting Data From '.substr(Request::input('csv_link'),40).'...','percent'=>'50'];
          event(new progressEvent($message));

          $xml_data = file_get_contents($url_file);
          Storage::disk('local')->put('public/csv_files/'.$rand.'.xml',$xml_data);
          $xml_file = Storage::get('public/csv_files/'.$rand.'.xml');
          $paths[]= 'storage/csv_files/'.$rand.'.xml';

          $xml = simplexml_load_string($xml_data);
          $array = $this->simpleXmlToArray($xml->product);

          $csv_headers = $this->xmlToJsonHeader($xml->product[0]);
        }

        else{
          $csv_headers = $this->csvToCsvHeaderJson(implode($paths,';'),Request::input('delimiter'));
        }

        $message = ['text' => 'Storing Information...','percent'=>'90'];
        event(new progressEvent($message));

			if(Request::input('drm_import_id')!==null){
				if(Request::input('company_user_type')==1){
					$company_id= DeliveryCompany::create([
						'user_id'=>CRUDBooster::myId(),
						'name'=>Request::input('company_name'),
						'address'=>Request::input('company_address'),
						'phone'=>Request::input('company_contactnumber'),
						'state'=>Request::input('company_state'),
						'country_id'=>Request::input('company_country'),
						'zip'=>Request::input('company_zip'),
						'email'=>Request::input('company_email'),
						'contact_name'=>Request::input('company_contactname')
					])->id;
				}else{
					$company_id=Request::input('company_user');
				}
      $item_countries = Request::input('item_country');

			$object=DrmImport::find(Request::input('drm_import_id'));
			$object->delivery_company_id=$company_id;
			$object->csv_file_name=Request::input('csv_filename');
            $object->country_id = $item_countries;
			$object->delivery_time=Request::input('delivery_time');
			$object->csv_file_path=implode($paths,';');
			$object->type=$type;
			$object->file_url=$url_file;
			$object->delimiter=Request::input('delimiter');
			$object->csv_headers=$csv_headers;
			$object->demo_data = Request::session()->pull('demoData');
			$object->save();
      return (int)Request::input('drm_import_id');
			//return redirect('admin/common/produkte-importieren?import_id='.Request::input('drm_import_id'));
		}

		if(Request::input('company_user_type')==1){
			$company_id= DeliveryCompany::create([
				'user_id'=>CRUDBooster::myId(),
				'name'=>Request::input('company_name'),
				'address'=>Request::input('company_address'),
				'phone'=>Request::input('company_contactnumber'),
				'state'=>Request::input('company_state'),
				'type'=>$type,
				'file_url'=>$url_file,
				'country_id'=>Request::input('company_country'),
				'zip'=>Request::input('company_zip'),

				'email'=>Request::input('company_email'),
				'contact_name'=>Request::input('company_contactname')
			])->id;
		}else{
			$company_id=Request::input('company_user');
		}


  $item_countries = Request::input('item_country');
		$drm_id = DrmImport::create([
			'user_id'=>CRUDBooster::myId(),
			'delivery_company_id'=>$company_id,
			'csv_file_name'=>Request::input('csv_filename'),
			'csv_headers'=>$csv_headers,
			'type'=>$type,
			'file_url'=>$url_file,
			'country_id'=>$item_countries,
			'delimiter' => Request::input('delimiter'),
			"delivery_time"=>Request::input('delivery_time'),
			'csv_file_path'=>implode($paths,';'),
			'demo_data'=>Request::session()->pull('demoData')
		])->id;

    DB::table('drm_imports')->where('id',$drm_id)->update(['current_step' => 'fields']);
    return (int)$drm_id;
	}

    public function ImportTempProducts($import_id,$return_url){
    $message = ['text' => 'Initializing Import'];
    event(new progressEvent($message));
    Request::session()->put('curr_tab','products');
    $user_id = CRUDBooster::myId();
    $drm = DB::table('drm_imports')->where('id',$import_id)->first();
    DB::table('drm_imports')->where('id',$import_id)->update(['current_step' => 'products']);
    $file_url=$drm->csv_file_path;
    $type = pathinfo($drm->file_url, PATHINFO_EXTENSION);

    $message = ['text' => 'Getting Data From Sheet'];
    event(new progressEvent($message));

    if($type == 'xml'){
      $array=$this->xmlToArray($file_url);
    }
    else {
      $array=$this->csvToArray($file_url,$type,$drm->delimiter);
    }
    $fields = DB::table('drm_product_fields')->where('drm_import_id',$import_id)->first();
    DB::table('tmp_drm_products')->where('drm_import_id',$import_id)->delete();
    // try{
    $chunks = array_chunk($array, 200);
    $invalid = 0;
    $count = 0;
    $total = count($array);
    $message = ['text' => 'Starting Import'];
    event(new progressEvent($message));

    foreach($chunks as $chunk){
      foreach($chunk as $key => $data){
        $data = makeArrayUtf8($data);
        $duplicate = DB::table('drm_products')->where('user_id',$user_id)->where('ean',$data[$fields->ean])->where('drm_import_id','!=',$import_id)->count();
        $duplicate_ean=0;
        if($duplicate){
          $duplicate_ean=1;
        }
        //@vat_processing
        if(is_numeric($fields->vat)){
          $vat = $fields->vat;
        }
        else{
          $vat = strip_tags($data[$fields->vat]);
        }
        //@end_vat_processing

        //@image_processing
        $image_headers =  explode('|',$fields->image);
        $image_separator = $drm->image_separator;
        $final_array = array();
        foreach ($image_headers as $header){
          $csv_images = explode($image_separator,$data[$header]);
          $final_array = array_merge($final_array,$csv_images);
        }
        $json_image = $this->getImageJson($final_array,$drm->prefix,$drm->suffix);
        //@end_image_processing

        $valid = $this->drmErrorReport($data,$fields,$drm->id);

        ($drm->custom_category==1)?$category=$fields->category:$category=strip_tags($data[$fields->category]);
        ($drm->item_number_prefix!=null)?$item_number=$drm->item_number_prefix."".strip_tags($data[$fields->item_number]):$item_number=strip_tags($data[$fields->item_number]);


        DB::table('drm_category')
          ->insert([
            'drm_import_id' => $import_id,
            'category_name' => $category,
            'country_id' => $drm->country_id,
            'user_id' => $drm->user_id
        ]);

        $temp_duplicate = DB::table('tmp_drm_products')->where('user_id',$user_id)->where('ean',$data[$fields->ean])->where('drm_import_id',$import_id)->count();

        if($valid && $temp_duplicate == 0){
         TmpDrmProduct::create([
          'drm_import_id'=>$import_id,
       	  'name'=> $data[$fields->name],
       	  'item_number'=> $item_number,
    	  'item_weight'=> strip_tags($data[$fields->item_weight]),
    	  'item_size'=> strip_tags($data[$fields->item_size]),
       	  'item_color'=> strip_tags($data[$fields->item_color]),
       	  'production_year'=> strip_tags($data[$fields->production_year]),
       	  'brand'=> strip_tags($data[$fields->brand]),
       	  'materials'=> strip_tags($data[$fields->materials]),
       	  'ean'=> strip_tags($data[$fields->ean]),
       	  'description'=> $data[$fields->description],
       	  'image'=> $json_image,
       	  'ek_price'=> $data[$fields->ek_price],
       	  'vk_price'=> $data[$fields->vk_price],
       	  'vat'=> $vat,
       	  'stock'=> strip_tags($data[$fields->stock]),
       	  'category'=> $category,
       	  'duplicate_ean'=> $duplicate_ean,
       	  'update_enabled' => '1',
       	  'status' => strip_tags($data[$fields->status]),
		  'gender'	=> strip_tags($data[$fields->gender]),
       	  'user_id'=> $drm->user_id,
       	  'delivery_company_id'=> $drm->delivery_company_id,
       	  'country_id' => $drm->country_id,
          ]);
        }
        else{
          $invalid++;
        }

        $count++;
        $message = ['total' => $total, 'count' => $count,'percent' => round(($count/$total)*100,2),'invalid' => $invalid,'name' => strip_tags($data[$fields->name])];
        event(new progressEvent($message));
      }
    }
    CRUDBooster::redirect(CRUDBooster::adminPath($return_url),"","");
}

public function getImportTmpProducts(){
    return redirect()->back();
}


public function postImportTmpProducts(){
  $message = ['text' => 'Initializing Import'];
  event(new progressEvent($message));
  Request::session()->put('curr_tab','products');
  $import_id = Request::input('drm_import_id');
  $return_url = Request::input('return_url');
  $user_id = CRUDBooster::myId();
  $drm = DB::table('drm_imports')->where('id',$import_id)->first();
  $fallback = $_REQUEST['fallback'];
  DB::table('drm_imports')->where('id',$import_id)->update(['current_step' => 'products','fallback' => $fallback]);

  Session::put($import_id, []);
  $file_url=$drm->csv_file_path;
  $type = pathinfo($drm->file_url, PATHINFO_EXTENSION);

  $message = ['text' => 'Getting Data From Sheet'];
  event(new progressEvent($message));

  if($type == 'xml'){
    $array=$this->xmlToArray($file_url);
  }
  else {
    $array=$this->csvToArray($file_url,$type,$drm->delimiter);
  }

  $fields = DB::table('drm_product_fields')->where('drm_import_id',$import_id)->first();
  DB::table('tmp_drm_products')->where('drm_import_id',$import_id)->delete();
  //try{

  $message = ['text' => 'Starting Import'];
  event(new progressEvent($message));

  $total = count($array);
  $count = 0;
  $invalid = 0;
  $chunks = array_chunk($array, 200);
    foreach($chunks as $chunk){
      foreach($chunk as $key=> $data){

      $data = makeArrayUtf8($data);

      $duplicate = DB::table('drm_products')->where('user_id',CRUDBooster::myId())->where('ean',$data[$fields->ean])->where('drm_import_id','!=',$import_id)->count();
      $duplicate_ean=0;
      if($duplicate){
        $duplicate_ean=1;
      }

      $price = $this->drmPriceCalculation($fields,$data,$drm);

        if(is_numeric($fields->vat)){
          $vat = $fields->vat;
        }
        else{
          $vat = strip_tags($data[$fields->vat]);
        }

      //@end_vat_processing

      //@image_processing
      $image_headers =  explode('|',$fields->image);
      $image_separator = $drm->image_separator;
      $final_array = array();
      foreach ($image_headers as $header){
        $csv_images = explode($image_separator,$data[$header]);
        $final_array = array_merge($final_array,$csv_images);
      }

      $json_image = $this->getImageJson($final_array,$drm->prefix,$drm->suffix);
      //@end_image_processing

      $valid = $this->drmErrorReport($data,$fields,$import_id);
      //$valid = true;
      ($drm->custom_category==1)?$category=$fields->category:$category=strip_tags($data[$fields->category]);
      ($drm->item_number_prefix!=null)?$item_number=$drm->item_number_prefix."".strip_tags($data[$fields->item_number]):$item_number=strip_tags($data[$fields->item_number]);
        $temp_duplicate = DB::table('tmp_drm_products')->where('user_id',$user_id)->where('ean',$data[$fields->ean])->where('drm_import_id',$import_id)->count();
      if($valid && $temp_duplicate == 0){
       TmpDrmProduct::create([
          'drm_import_id'=>$import_id,
       	  'name'=> $data[$fields->name],
       	  'item_number'=> $item_number,
       	  'item_weight'=> strip_tags($data[$fields->item_weight]),
       	  'item_size'=> strip_tags($data[$fields->item_size]),
       	  'item_color'=> strip_tags($data[$fields->item_color]),
       	  'production_year'=> strip_tags($data[$fields->production_year]),
       	  'brand'=> strip_tags($data[$fields->brand]),
       	  'materials'=> strip_tags($data[$fields->materials]),
       	  'ean'=> strip_tags($data[$fields->ean]),
       	  'description'=> $data[$fields->description],
       	  'image'=> $json_image,
       	  'ek_price'=> drm_convert_european_to_decimal($data[$fields->ek_price]),
       	  'vk_price'=> $price,
       	  'vat'=> $vat,
       	  'stock'=> strip_tags($data[$fields->stock]),
       	  'category'=> $category,
       	  'duplicate_ean'=> $duplicate_ean,
       	  'update_enabled' => '1',
       	  'status' => strip_tags($data[$fields->status]),
		  'gender'	=> strip_tags($data[$fields->gender]),
       	  'user_id'=> $drm->user_id,
       	  'delivery_company_id'=> $drm->delivery_company_id,
       	  'country_id' => $drm->country_id,
       ]);
      }
      else{
        $invalid++;
      }
      $count ++;
      $message = ['total' => $total, 'count' => $count,'percent' => number_format(($count/$total)*100,2),'invalid' => $invalid,'name' => strip_tags($data[$fields->name])];
      event(new progressEvent($message));
    }
  }

//   }catch(Exception $e){
//     return redirect($return_url);
//   }
  return redirect($return_url);
}

public function drmPriceCalculation($fields,$data,$drm){
  $user = CRUDBooster::myId();
  if($fields->vk_price != '' || $fields->vk_price != null){
    $temp_price=floatval($data[$fields->vk_price]);
  }
  else{
    $temp_price=drm_convert_european_to_decimal($data[$fields->ek_price]);
  }
  $price=$temp_price;
  $has_profit_margin=false;

  ($drm->custom_category==1)?$category=$fields->category:$category=strip_tags($data[$fields->category]);

  if($fields->vk_price==null){
    $category_id = DB::table('drm_category')
                  ->where('category_name',$category)->where('drm_import_id',$drm->id)->first();

    $profit_margin = DB::table('drm_product_profit_margins')
    ->where('category_id',$category_id->id)
    ->where('price_from','<=',$temp_price)
    ->where('price_to','>=',$temp_price)
    ->first();

    if($profit_margin!=null){
      $price = $temp_price + $temp_price * (floatval($profit_margin->profit_percent)/100) + drm_convert_european_to_decimal($profit_margin->shipping_cost)+ drm_convert_european_to_decimal($profit_margin->additional_charge_fixed);
    }
    else{

      $gloabal_setting = DB::table('drm_fallback_calculations')
      ->where('id',$drm->fallback)
      ->first();

      if($gloabal_setting!=null){
        $price = $temp_price + $temp_price * (floatval($gloabal_setting->profit_percent)/100) + drm_convert_european_to_decimal($gloabal_setting->shipping_cost)+ drm_convert_european_to_decimal($gloabal_setting->additional_charge);
      }
    }
  }
  elseif ($temp_price == null) {
    $temp_price=drm_convert_european_to_decimal($data[$fields->ek_price]);
    $gloabal_setting = DB::table('drm_fallback_calculations')
    ->where('user_id',$user)
    ->first();

    if($gloabal_setting!=null){
      $price = $temp_price + $temp_price * (floatval($gloabal_setting->profit_percent)/100) + drm_convert_european_to_decimal($gloabal_setting->shipping_cost)+ drm_convert_european_to_decimal($gloabal_setting->additional_charge);
    }
  }

  if($profit_margin->round_scale!=null){
    $has_price = explode('.',$price);
    $first_item_array = $has_price[0];
    $last_item_array = $has_price[1];
    if($last_item_array == 0){
      $price;
    }
    else{
      $price = $first_item_array+$profit_margin->round_scale;
    }
  }
  return floatval($price);
}

public function getImageJson($array,$prefix,$suffix){
  $final_img=array();
  foreach ($array as $key => $value){
    $img = str_replace(' ', '', $value);
    if($img!=null){
      if (filter_var($img, FILTER_VALIDATE_URL) === FALSE){

        $value = $this->importImagePrefix($prefix,$img);
      }

      if($suffix!=null){
        $value = $this->importImageSuffix($suffix,$value);
      }

      $img_data['id']=$key+1;
      if($key>10){
        $img_data['status']=0;
      }
      else{
        $img_data['status']=1;
      }
      $img_data['src']=$value;
      $final_img[]=$img_data;
    }
  }
  $json_image=json_encode($final_img);
  return $json_image;
}


public function importImagePrefix($prefix,$image){
  if(substr($prefix , -1)== '/'){
    $image = $prefix.$image;
  }else {
    $image = $prefix.'/'.$image;
  }
  return $image;
}

public function importImageSuffix($suffix,$image){
  $image = $image.$suffix;
  return $image;
}

public function postSave_fields(){
  $message = ['text' => 'Initializing Fields', 'percent' => '10'];
  event(new progressEvent($message));

  $vat = Request::input('vat');
  $custom_vat = Request::input('custom_vat');
  $custom_category = Request::input('custom_category');
  $import_id = Request::input('drm_import_id');
  $drm_category = Request::input('category_from_drm');

  $import_update['image_prefix'] = Request::input('prefix');
  $import_update['image_suffix'] = Request::input('suffix');
  $import_update['image_separator'] = Request::input('image_separator');
  $import_update['item_number_prefix'] = Request::input('item_number_prefix');



  if($drm_category!=null){
    $custom_category = $drm_category;
  }

  if($custom_category!=null){
    $import_update['custom_category'] = 1;
  }
  DB::table('drm_imports')->where('id',$import_id)->update($import_update);

  $message = ['text' => 'Processing Fields', 'percent' => '20'];
  event(new progressEvent($message));

    if($custom_vat!=null){
      $vat = $custom_vat;
    }
  $images = Request::input('image');

  foreach ($images as $key => $value){
    if($key == 0){
      $image = $value;
    }
    else{
      $image = $image."|".$value;
    }
  }

  ($custom_category!=null)?$category=$custom_category:$category=Request::input('category');

  (Request::input('item_number')!=null)?$item_number=Request::input('item_number'):$item_number=Request::input('ean');


  $message = ['text' => 'Saving Fields', 'percent' => '50'];
  event(new progressEvent($message));

	DrmProductField::create([
		'drm_import_id' => $import_id,
		'name' => Request::input('name'),
		'item_number' => $item_number,
		'item_weight' => Request::input('item_weight'),
		'item_size' => Request::input('item_size'),
		'item_color' => Request::input('item_color'),
		'production_year' => Request::input('production_year'),
		'brand' => Request::input('brand'),
		'materials' => Request::input('materials'),
		'ean' => Request::input('ean'),
		'description' => Request::input('description'),
		'image' => $image,
		'ek_price' => Request::input('ek_price'),
		'vk_price' => Request::input('vk_price'),
		'vat' => $vat,
		'stock' => Request::input('stock'),
		'category' => $category,
		'gender'	=> Request::input('gender'),
		'status'	=> Request::input('status'),
	]);

  $message = ['text' => 'Saving Minimum and Maximum Price', 'percent' => '60'];
  event(new progressEvent($message));

//   if(Request::input('vk_price')==null){
//     $this->setMinMaxPrice($import_id,Request::input('ek_price'));
//   }
//   else{
//     $this->setMinMaxPrice($import_id,Request::input('vk_price'));
//   }

  $fields = DB::table('drm_product_fields')->where('drm_import_id',$import_id)->first();


  $message = ['text' => 'Creating Categories', 'percent' => '70'];
  event(new progressEvent($message));

  $res = $this->drmSaveCsvCategories($fields);

  if($fields->vk_price!=null){

    $message = ['text' => 'Mapping Completed', 'percent' => '100'];
    event(new progressEvent($message));

    Request::session()->put('curr_tab','products');
    DB::table('drm_imports')->where('id',$import_id)->update(['current_step' => 'products']);

    $message = ['text' => 'Imporing Products', 'percent' => '0'];
    event(new progressEvent($message));

    $this->ImportTempProducts($import_id,'common/produkte-importieren?import_id='.$import_id.'');
  }
  else{
    $message = ['text' => 'Mapping Completed', 'percent' => '100'];
    event(new progressEvent($message));
    Request::session()->put('curr_tab','calc_money');
    DB::table('drm_imports')->where('id',Request::input('drm_import_id'))->update(['current_step' => 'calc_money']);
    return redirect()->back();
  }
}


      public function drmSaveCsvCategories($fields){
        ini_set('memory_limit','-1');
        $id = $fields->drm_import_id;

        $import = DB::table('drm_imports')->where('id',$id)->first();
          if($import->custom_category==1){
              DB::table('drm_category')
                ->insert([
                  'drm_import_id' => $id,
                  'category_name' => $fields->category,
                  'country_id' => $import->country_id,
                  'user_id' => $import->user_id,
                ]);
          }
          else{
            $path = $import->csv_file_path;
            $type = pathinfo($path,PATHINFO_EXTENSION);
            if($type == 'xml'){
                $csv = $this->xmlToArray($path);
            }
            else{
               $csv = $this->csvToArray($path,$type,$import->delimiter);
            }

            $categories = [];
            foreach ($csv as $key => $value){
              if(!in_array($value[$fields->category], $categories)){
                if($value[$fields->category]!=null){
                    $categories[] = $value[$fields->category];
                }
              }
            }

            foreach ($categories as $key => $value){
              DB::table('drm_category')
              ->insert([
                'drm_import_id' => $id,
                'category_name' => $value,
                'country_id' => $import->country_id,
                'user_id' => $import->user_id
              ]);
            }
          }
        //}
      }

			public function postSave_margin(){
				Request::session()->put('curr_tab','calc_money');
                DB::table('drm_imports')->where('id',Request::input('drm_import_id'))->update(['current_step' => 'calc_money']);
				$categories = Request::input('product_category');
				// $request= Request::input();
				// dd($request);
				if(Request::input('id')!=null){
					$margin=DrmProductProfitMargin::find(Request::input('id'));
					foreach($categories as $key => $category){
					    $margin->format=Request::input('format');
                        $margin->category_id = $category;
    					$margin->price_from=str_replace(',','.',Request::input('price_from'));
    					$margin->price_to=str_replace(',','.',Request::input('price_to'));
    					$margin->shipping_cost=str_replace(',','.',Request::input('shipping_cost'));
    					$margin->profit_percent=str_replace(',','.',Request::input('profit_percent'));
    					$margin->round_scale=Request::input('round_scale');
    					$margin->additional_charge_fixed=str_replace(',','.',Request::input('additional_charge_fixed'));
    					$margin->save();
					}
					return redirect()->back();
				}
				 //dd(Request::input());
				foreach($categories as $key => $category){
    				DrmProductProfitMargin::create([
    					'drm_import_id' => Request::input('drm_import_id'),
    					'format' => Request::input('format'),
              'category_id' => $category,
    					'price_from' => str_replace(',','.',Request::input('price_from')),
    					'price_to' => str_replace(',','.',Request::input('price_to')),
    					'shipping_cost' => str_replace(',','.',Request::input('shipping_cost')),
    					'profit_percent' => str_replace(',','.',Request::input('profit_percent')),
    					'round_scale' => Request::input('round_scale'),
    					'additional_charge_fixed' => str_replace(',','.',Request::input('additional_charge_fixed')),
    				]);
				}
				return redirect()->back();
			}

  	public function getDrmImport(){
  	    ini_set('memory_limit',-1);

  	    if(CRUDBooster::myId()==null){
  	        return redirect(CRUDBooster::adminPath());
  	    }
      if(isset($_REQUEST['import_id'])){
        $id = $_REQUEST['import_id'];
        if($id!=null){
            $drm = User::find(CRUDBooster::myId())->drm_imports()->where('id',$id)->first();
            Request::session()->put('curr_tab',$drm->current_step);
           $item_countries = explode(';',$drm->country_id);
           $countries;
           foreach ($item_countries as $key => $country) {
             $country_db = DB::table('countries')
             ->where('id',$country)
             ->first();
             $countries[$key] = array(
               'id' => $country_db->id,
               'name' => $country_db->name,
               'country_shortcut' => $country_db->country_shortcut
             );
           }

           $drm_categories = DB::table('drm_category')->where('user_id',$drm->user_id)->get();

           $price_categories = DB::table('price_category')->where('user_id',CRUDBooster::myId())->get();

           $product_categories = DB::table('drm_category')->where('drm_import_id',$drm->id)->get();


           if($product_categories == null){
             $product_categories = [];
           }

       		if($drm->import_finished==1){
            Request::session()->remove('curr_tab');
       			return view('admin.menudashboard.produkteimportieren',compact('countries'));
       		}
           else {
             $errors = DB::table('drm_import_errors')->where('drm_import_id',$id)->limit(10)->get();
             $select_option=json_decode($drm->csv_headers);
             return view('admin.menudashboard.produkteimportieren',compact('select_option','drm','price_categories','countries','product_categories','errors','drm_categories'));
           }
        }
        else{
            return redirect()->back();
        }
     }
     else {
       Request::session()->remove('curr_tab');
       return view('admin.menudashboard.produkteimportieren',compact('countries'));
     }
  	}

      public function getPriceCategoryPrices(){
        $prices = DB::table('category_prices')
        ->where('price_category_id',$_REQUEST["category_id"])
        ->get();

        return json_encode($prices);
      }


      public function getPriceCategory(){
        $id = $_REQUEST["id"];

        $price_category = DB::table('price_category')
        ->where('id',$id)
        ->first();
        return json_encode($price_category);
      }

			public function postSearchAndReplace(){
				Request::session()->put('curr_tab','search_and_replace');
				$drm_id=Request::input('drm_import_id');
        DB::table('drm_imports')->where('id',$drm_id)->update(['current_step' => 'search_and_replace']);

				$keyword=Request::input('keyword');
				$replace_with=Request::input('replace_with');
				$queries=DB::select(DB::raw("select concat(
					'UPDATE tmp_drm_products SET ',
					column_name,
					' = REPLACE(', COLUMN_NAME, ', ''$keyword'', ''$replace_with'') where drm_import_id = $drm_id;') AS query
					from information_schema.columns
					where table_name = 'tmp_drm_products'"));
					unset($queries[0]);
					unset($queries[1]);
    				array_pop($queries);
    				array_pop($queries);

            DB::table('drm_imports')->where('id',$id)->update(['current_step' => 'search_and_replace']);

				foreach($queries as $query){
					$res= DB::statement($query->query);
				}

				return "search_and_replace";
				//return redirect()->back()->with('success',"Successfully replaced");
			}

			public function getCsvImportToDatabase(){
			    return redirect()->back();
			}

			public function postCsvImportToDatabase(){
			    ini_set('memory_limit',-1);
        $import_id = Request::input('drm_import_id');
				$drm=DrmImport::find($import_id);
				$data=$drm->tmp_drm_products;
        $language = DB::table('countries')->where('id',$drm->country_id)->first();
      	$table = 'drm_translation_'.$language->language_shortcode;

				if(is_object($data) && \Schema::hasTable($table)){
          $count = 0;
          $total = count($data);
				foreach($data as $record){
          if($this->drmCheckExcluded($record)){
          if($record->duplicate_ean==1){
  				if($drm->overwrite==1){
  					$ean=$record->ean;
  					$record=$record->toArray();
  					unset($record['id']);
  					unset($record['ean']);
  					unset($record['drm_import_id']);
  					$duplicate_products=User::find(CRUDBooster::myId())->drm_products()->where('ean',$ean)->where('drm_import_id','!=',$drm->id)->get();
  					foreach($duplicate_products as $duplicate_product){
  						$duplicate_product->update($record);
  					}
  				}
  				continue;
  				}
  				$record=$record->toArray();

  				$value['title'] = $record['name'];
  			    $value['description'] = $record['description'];
  			    $value['ean'] = $record['ean'];
                $category = $record['category'];

                unset($record['category']);
  				unset($record['id']);
  				unset($record['name']);
  				unset($record['description']);
  				$p_id = DrmProduct::create($record)->id;

                DB::table('product_update_status')->insert(['product_id' => $p_id]);

  			    $value['product_id'] = $p_id;
  			    DB::table($table)->insert($value);

                $category_name = DB::table('drm_category')->where('drm_import_id',$import_id)->where('category_name',$category)->first();

                if($category_name!=null){
                  $product_category = [
                    'category_id' => $category_name->id,
    								'country_id' => $record['country_id'],
    								'product_id' => $p_id,
                  ];
                  DB::table('drm_product_categories')->insert($product_category);
                }
      					$import=$drm;
              }
              $count++;
            $message = ['total' => $total, 'count' => $count,'finalize'=> 1 ,'percent' => number_format(($count/$total)*100,2),'name' => strip_tags($value['title'])];
            event(new progressEvent($message));
    		}

    	    $drm->import_finished=1;

            DB::table('drm_import_errors')->where('drm_import_id',$import_id)->delete();
    				$drm->save();
    				$drm->tmp_drm_products()->delete();
    				Request::session()->put('curr_tab',null);
            DB::table('drm_imports')->where('id',$drm->id)->update(['current_step' => null]);
    			  Request::session()->remove('drm_import_error'.Request::input('drm_import_id'));
    			  Request::session()->remove('drm_import_'.Request::input('drm_import_id'));

    			    if(isset($_REQUEST['type'])){
    			        if($_REQUEST['type'] == '2'){

    			            CRUDBooster::redirect('admin/drm_imports','CSV Updated Successfully', 'success');
    			        }
    			    }
    			    else{
    			        // $message=DB::table('notification_trigger')->where('hook','DRMImport')->where('status',0)->first();

            	  //       if($message){
            	  //       	// if (isHookRemainOnSidebar('DRMImport') && isLocal()) {
	            	 //        //     User::find(Crudbooster::myId())->notify(new DRMTelegramNotification($message->title, 'DRMImport'));
            	  //       	// }else{
	            	 //            User::find(Crudbooster::myId())->notify(new DRMNotification($message->title, 'DRMImport'));
            	  //       	// }
            	  //       }

    			        return redirect()->back();
    			    }
				}
				else{
				   return redirect()->back();
				}

			}

			public function postGorillaDemoData(){
				if(Request::input('id')!=null){
					$drm=DrmImport::find(Request::input('id'));
					$headers=json_decode($drm->csv_headers);
					$demo=json_decode($drm->demo_data,true);
					$demo_data=array_combine($headers,$demo);
					$request=Request::input();
					return view('admin.menudashboard.demo_data_table',compact('demo_data','request'));
				}
			}


			private function valOrZero($col=null){
				if(isset($col)){
					return $col;
				}else{
					return 0;
				}
			}
			private function valOrBlank($col=null){
				if(isset($col)){
					return $col;
				}else{
					return "";
				}
			}

			public function getAddCsvHeaderCriteria(){
				$user=User::findOrFail(CRUDBooster::myId());
				$headers= $user->user_csv_headers;
				return view('admin.menudashboard.add_csv_header_criteria',compact('headers'));
			}

			public function postAddCsvHeaderCriteria(){
				$user=User::findOrFail(CRUDBooster::myId());
				if($user->user_csv_headers!=null){
					$fields=$user->user_csv_headers;
						$fields->name=Request::input('name');
						$fields->item_number=Request::input('item_number');
						$fields->ean=Request::input('ean');
						$fields->description=Request::input('description');
						$fields->image=Request::input('image');
						$fields->ek_price=Request::input('ek_price');
						$fields->vk_price=Request::input('vk_price');
						$fields->vat=Request::input('vat');
						$fields->stock=Request::input('stock');
						$fields->category=Request::input('category');
						$fields->save();
				}else{
					$user->user_csv_headers()->create([
						'name'=> Request::input('name'),
						'item_number'=> Request::input('item_number'),
						'ean'=> Request::input('ean'),
						'description'=> Request::input('description'),
						'image'=> Request::input('image'),
						'ek_price'=> Request::input('ek_price'),
						'vk_price'=> Request::input('vk_price'),
						'vat'=> Request::input('vat'),
						'stock'=> Request::input('stock'),
						'category'=> Request::input('category'),
					]);
				}
				return redirect()->back()->with('success','Criteria(s) successfully saved');
			}

			private function format_price($val,$format){
				if($format=='german'){
					return str_replace('.',',',$val);
				}else{
					return $val;
				}
			}


			function postUpdateImport(){
				if(Request::input('id')==null){
					return abort(500);
				}
				$import= DrmImport::find(Request::input('id'));
					if(Request::input('file_type')==1){
						$type='file';
						// dd(Request::file('csv_file'));
						if(!Request::hasFile('csv_file')){
							return abort(500);
						}
						foreach(Request::file('csv_file') as $file){
							$path=$file->store('public/csv_files');
							$paths[]=str_replace('public','storage',$path);
						}

					}else{
						$type='url';
						$url_file=trim(Request::input('url'));
						$csv_data= file_get_contents($url_file);
						$rand=Str::random(40);
						Storage::disk('local')->put('public/csv_files/'.$rand.'.csv',$csv_data);
						$paths[]= 'storage/csv_files/'.$rand.'.csv';
					}
					$csv_headers=$this->csvToCsvHeaderJson(implode($paths,';'),$import->delimiter);
					// dd($csv_headers);
					if($csv_headers!=$import->csv_headers){
						return "Headers did not mactch";
					}
					$file_type = pathinfo($url_file, PATHINFO_EXTENSION);
					$array=$this->csvToArray(implode($paths,';'),$file_type,$import->delimiter);
					$fields=$import->drm_product_fields;
					$profit_margins= $import->drm_product_profit_margins;
					foreach($array as $row){
				// 		if($this->isFielterable($row,$import)){
				// 			continue;
				// 		}

						//dd($fields);
						$temp_price=drm_convert_european_to_decimal($row[$fields->ek_price]);
						$price=$temp_price;

            if($fields->vk_price==null){
              foreach($profit_margins as $profit_margin){
                $category_name = DB::table('drm_product_categories')->where('id',$profit_margin->category_id)->first()->category;
  						  if($temp_price>=$profit_margin->price_from && $temp_price < $profit_margin->price_to && $row[$fields->category] == $category_name){
  							  $has_profit_margin=true;
  							  $price=$temp_price + $temp_price * (floatval($profit_margin->profit_percent)/100) + floatval($profit_margin->shipping_cost)+ floatval($profit_margin->additional_charge_fixed);
  							  break;
  						  }
  						}
            }

						$duplicate_products=User::find(CRUDBooster::myId())->drm_products()->where('ean',$row[$fields->ean])->where('drm_import_id','!=',$import->id)->get();
						if(count($duplicate_products)>0){
							if($import->overwrite==1){
								foreach($duplicate_products as $duplicate_product){
									$duplicate_product->update([
										'name'=>$row[$fields->name],
										'item_number'=>$row[$fields->item_number],
										'description'=>$row[$fields->description],
										'image'=>$row[$fields->image],
										'ek_price'=>$row[$fields->ek_price],
										'vk_price'=>$price,
										'vat'=>$row[$fields->vat],
										'stock'=>$row[$fields->stock],
										'category'=>$row[$fields->category],
										]);
								}
							}
							continue;
						}
						$drm_import_id=$import->id;
						$p_id= DrmProduct::updateOrCreate(
							['ean'=>$row[$fields->ean],'drm_import_id'=>$drm_import_id],
							[
							'drm_import_id'=>$drm_import_id,
							'name'=>$row[$fields->name],
							'item_number'=>$row[$fields->item_number],
							'ean'=>$row[$fields->ean],
							'description'=>$row[$fields->description],
							'image'=>$row[$fields->image],
							'ek_price'=>drm_convert_european_to_decimal($row[$fields->ek_price]),
							'vk_price'=>floatval($price),
							'vat'=>$row[$fields->vat],
							'stock'=>$row[$fields->stock],
							'category'=>$row[$fields->category],
							]
						)->id;
					}
					if(Request::input('file_type')!=1){
						$import->file_url=trim(Request::input('url'));
					}
					$import->type=$type;
					$import->save();
					foreach($paths as $path){
						Storage::delete($path);
					}
				return redirect()->back();

			}





    // public function getManualImportSave(){
    //     return redirect('drm_imports');
    // }


    //   public function getNewProduct($fields,$data,$drm){
    //     $valid = $this->drmErrorReport($data,$fields,$drm->id);
    //     if($valid){

    //       $image_headers =  explode('|',$fields->image);
    //       $image_separator = $object->image_separator;
    //       $final_array = array();
    //       foreach ($image_headers as $header){
    //         $csv_images = explode($image_separator,$data[$header]);
    //         $final_array = array_merge($final_array,$csv_images);
    //       }
    //       $json_image= $this->getImageJson($final_array,$object->prefix,$object->suffix);

    //       if($fields->vk_price!=null){
    //         $price = $data[$fields_array['vk_price']];
    //       }
    //       else{
    //         $price = drmPriceCalculation($fields,$data,$drm);
    //       }

    //       $vat_all = 19;

    //     $existing_category = DB::table('drm_category')
    //                         ->where('category_name',strip_tags($data[$fields->category]))
    //                         ->where('drm_import_id',$drm->id)
    //                         ->first();
    //     if($existing_category){
    //         $category = $existing_category->id;
    //     }
    //     else{
    //         $category = DB::table('drm_category')
    //                 ->insertGetId([
    //                     'category_name' => strip_tags($data[$fields->category]),
    //                     'drm_import_id' => $drm->id,
    //                     'country_id' => $drm->country_id,
    //                     'user_id' => $drm->use_id,
    //                 ]);
    //     }


    //      $id = DrmProduct::create([
    //         'drm_import_id'=>$drm->id,
    //      	  'name'=> $data[$fields->name],
    //      	  'item_number'=> strip_tags($data[$fields->item_number]),
			 // 'item_weight'=> strip_tags($data[$fields->item_weight]),
			 // 'item_size'=> strip_tags($data[$fields->item_size]),
			 // 'item_color'=> strip_tags($data[$fields->item_color]),
			 // 'production_year'=> strip_tags($data[$fields->production_year]),
			 // 'brand'=> strip_tags($data[$fields->brand]),
			 // 'materials'=> strip_tags($data[$fields->materials]),
    //      	  'ean'=> strip_tags($data[$fields->ean]),
    //      	  'description'=> $data[$fields->description],
    //      	  'image'=> $json_image,
    //      	  'ek_price'=> $data[$fields->ek_price],
    //      	  'vk_price'=> number_format($price,2),
    //      	  'vat'=> $vat_all,
    //      	  'stock'=> strip_tags($data[$fields->stock]),
    //      	  'update_enabled' => '1',
    //      	  'status' => '1',
    //      	  'user_id'=> $drm->user_id,
    //      	  'delivery_company_id'=> $drm->delivery_company_id,
    //      	  'country_id' => $drm->country_id,
    //      ])->id;

    //      DB::table('drm_product_categories')->insert(['category_id'=>$category,'product_id'=>$id,'country_id'=>$drm->country_id]);
    //     }
    //   }

			// function getSetTab(){
			// 	$tab=Request::input('tab');
			// 	if($tab=='home'){
			// 		Request::session()->put('curr_tab',null);
			// 		return;
			// 	}
			// 	Request::session()->put('curr_tab',$tab);
			// }


  public function getEditGorilla(){
				$drm_id=Request::input('id');
				$drm= User::find(CRUDBooster::myId())->drm_imports()->where('drm_imports.id',$drm_id)->first();
        //$drm = DB::table('drm_imports')->where('user_id',CRUDBooster::myId())->where('id',$drm_id)->first();
				if($drm!=null){
				    $select_option=json_decode($drm->csv_headers);

    				$countries = DB::table('countries')->get();

    				$item = explode(';',$drm->country_id);
                    foreach ($item as $key => $country) {
                      $country_db = DB::table('countries')
                      ->where('id',$country)
                      ->first();
                      $item_countries[$key] = array(
                        'id' => $country_db->id,
                        'name' => $country_db->name,
                        'country_shortcut' => $country_db->country_shortcut
                      );
                    }

            $price_categories = DB::table('price_category')
            ->get();

            $product_categories = DB::table('drm_product_categories')->where('drm_import_id',$drm->id)->get();
            if($product_categories == null){
              $product_categories = [];
            }

    				return view('admin.menudashboard.update_gorilla',compact('select_option','drm','countries','item_countries','price_categories','product_categories'));
				}

				else{
				    CRUDBooster::redirect('admin/drm_imports','Access Denied', 'warning');
				}
			}


    	public function postGorillaImportSetOverwrite(){
    		$import=DrmImport::findOrFail(Request::input('id'));
    		$import->overwrite=Request::input('overwrite');
    		$import->save();
    		return "success";
    	}

      public function drmErrorReport($data,$field,$id){
        $valid = true;
        if(trim($data[$field->ean]) == null || trim($data[$field->ean]) == ''){
          $valid = false;
          DB::table('drm_import_errors')->insert(['drm_import_id'=>$id, 'error_type'=> 'EAN', 'error' => "ean missing"]);
        }

        // if($data[$field->image] == null || $data[$field->image] == ''){
        //   $valid = false;
        //   Session::push($import_id, "DRM import Error at item number: ".$data[$field->item_number]." : No Image");
        // }

        if(trim($data[$field->name]) == null || trim($data[$field->name]) == ''){
           $valid = false;
          DB::table('drm_import_errors')->insert(['drm_import_id'=>$id, 'error_type'=> 'Title', 'error' => "title missing"]);
        }

        if(trim($data[$field->description]) == null || trim($data[$field->description]) == ''){
          $valid = false;
          DB::table('drm_import_errors')->insert(['drm_import_id'=>$id, 'error_type'=> 'Description', 'error' => "description missing"]);
        }

        if(trim($data[$field->category]) == null && trim($field->category) != null){
          $valid = false;
          DB::table('drm_import_errors')->insert(['drm_import_id'=>$id, 'error_type'=> 'Description', 'error' => "Category Missing"]);
        }

        if(trim($data[$field->ek_price]) == null && trim($field->ek_price) != null){
          $valid = false;
          DB::table('drm_import_errors')->insert(['drm_import_id'=>$id, 'error_type'=> 'Purchase Prie', 'error' => "Purchase Prie Missing"]);
        }

        // $explode = explode(';',$data[$field->vat]);
        // foreach ($explode as $key => $value) {
        //   $vats = explode('|',$value);
        //   if($vats[1] == '' || $vats[1] == null){
        //     Session::push($import_id, "DRM import Error at item number: ".$data[$field->item_number]." : No vat");
        //   }
        // }
        return $valid;
      }

      public function drmCheckExcluded($product){
        $import_id = $product->drm_import_id;
        $filter = DB::table('drm_import_filter')->where('drm_import_id',$import_id)->first();
        $filter_session = json_decode($filter->filter, true);

        if ($filter_session['price_below']!=null) {
          if($product->vk_price < $filter_session['price_below']){
            return false;
          }
        }

        if ($filter_session['price_more_than']!=null) {
          if($product->vk_price > $filter_session['price_more_than']){
            return false;
          }
        }

        if($filter_session['ean']!=null){
          foreach ($filter_session['ean'] as $value){
            if($value == $product->ean){
              return false;
            }
          }
        }

        if($filter_session['category']!=null){
          foreach ($filter_session['category'] as $value){
            if($value == $product->category){
              return false;
            }
          }
        }

        if($filter_session['stock']!=null){
          foreach ($filter_session['stock'] as $key => $value) {

            if($filter_session['stock_operator'][$key] == '='){
              if($product->stock == (int)$value){
                return false;
              }
            }
            if($filter_session['stock_operator'][$key] == '>'){
              if($product->stock > (int)$value){
                return false;
              }
            }
            if($filter_session['stock_operator'][$key] == '<'){
              if($product->stock < (int)$value){
                return false;
              }
            }

          }
        }
        return true;
      }


      public function xmlToArray($path){
        ini_set('memory_limit',-1);
        $xml_file = file_get_contents_utf8($path);
        $xml = simplexml_load_string($xml_file);
        $array = $this->simpleXmlToArray($xml->product);

        foreach($xml->product as $key => $value){
          $array = $this->simpleXmlToArray($value);
          $final_array[] = $array;
        }
        return $final_array;
      }

      public function xmlToJsonHeader($xml){
        ini_set('memory_limit',-1);
        $array = $this->simpleXmlToArray($xml);
        foreach($array as $key => $value){
           $headers_array[] = $key;
        }
        $Headers = json_encode($headers_array);
        Request::session()->put('demoData',json_encode($array));
        return $Headers;
      }

    function simpleXmlToArray($xmlObject){
        ini_set('memory_limit',-1);
        $array = [];
        foreach ($xmlObject->children() as $node) {
            $array[$node->getName()] = is_array($node) ? simplexml_to_array($node) : (string) $node;
        }
        return $array;
    }


	    public function getXmlImport(){
	        if(isset($_REQUEST['import_id'])){
            $id = $_REQUEST['import_id'];
            $drm = User::find(CRUDBooster::myId())->drm_imports()->where('id',$id)->first();
            Request::session()->put('curr_tab',$drm->current_step);
               $item_countries = explode(';',$drm->country_id);
               $countries;
               foreach ($item_countries as $key => $country) {
                 $country_db = DB::table('countries')
                 ->where('id',$country)
                 ->first();
                 $countries[$key] = array(
                   'id' => $country_db->id,
                   'name' => $country_db->name,
                   'country_shortcut' => $country_db->country_shortcut
                 );
               }

               $price_categories = DB::table('price_category')
               ->get();

               $product_categories = DB::table('drm_product_categories')->where('drm_import_id',$drm->id)->get();
               if($product_categories == null){
                 $product_categories = [];
               }

           		if($drm->import_finished==1){
                Request::session()->remove('curr_tab');
           			return view('admin.menudashboard.xmlimport',compact('countries'));
           		}
               else {
                 $select_option=json_decode($drm->csv_headers);
                 return view('admin.menudashboard.xmlimport',compact('select_option','drm','price_categories','countries','product_categories'));
               }
             }
             else {
               Request::session()->remove('curr_tab');
               return view('admin.menudashboard.xmlimport',compact('countries'));
             }
	    }

      public function postCreateFallbackCalculation(){
        $value['name'] = $_REQUEST['name'];
        $value['shipping_cost'] = $_REQUEST['shipping_cost'];
        $value['profit_percent'] = $_REQUEST['profit_percent'];
        $value['additional_charge'] = $_REQUEST['additional_charge'];
        $value['user_id'] = CRUDBooster::myId();

        DB::table('drm_fallback_calculations')->insert($value);
        return redirect()->back();
      }


	    public function drmParseXml(){
	       // ini_set('memory_limit','1536M'); // 1.5 GB
        //     ini_set('max_execution_time', 18000);
            //$xmlfile = Storage::get('public/csv_files/DIFOX-22390344-TECDAT.xml');
            $reader = new XMLReader();
            dd($reader);
            $reader->open($xmlfile);

            while ($reader->read()) {
                dd($reader->name);
                if($reader->nodeType == XMLReader::ELEMENT && $reader->name == 'address'){
                    $address = new SimpleXMLElement($reader->readOuterXml());
                    $attributes = $address->attributes();
                }
            }
	    }

	    public function postDrmSetTab(){
	        $tab = $_REQUEST['tab'];
	        $id = $_REQUEST['id'];
	        DB::table('drm_imports')->where('id',$id)->update(['current_step' => $tab]);
	        return redirect()->back();
	    }
	}
