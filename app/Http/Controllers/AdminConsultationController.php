<?php

namespace App\Http\Controllers;

use App\ConsultationHour;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Exception;
use CRUDBooster;
use Illuminate\Http\JsonResponse;

class AdminConsultationController extends Controller
{
	public function hours()
	{
        $user_id = in_array(CRUDBooster::myParentId(), [2455]) ? CRUDBooster::myParentId() : 0;
        if (empty($user_id)) {
            return redirect('/admin');
        }

        $data = [];
        $days = [
            '0' => 'Sunday',
            '1' => 'Monday',
            '2' => 'Tuesday',
            '3' => 'Wednesday',
            '4' => 'Thursday',
            '5' => 'Friday',
            '6' => 'Saturday'
        ];
        
		$data['days'] = $days;
		$data['time_slots'] = $this->timeSlot();
		$data['consultation_hours'] = ConsultationHour::where('user_id', $user_id)->value('hours');

        return view('admin.consultation.hours', $data);
	}

	public function saveHours(Request $req)
	{
        $user_id = in_array(CRUDBooster::myParentId(), [2455]) ? CRUDBooster::myParentId() : 0;
        if (empty($user_id)) {
            return redirect('/admin');
        }

        try {
            $hours = $breaks = [];

            $days = $req['days'];
            $off_days = $req['off_days'] ?? [];

            foreach ($days as $key => $day) {
                $slot = [];
                $slot['day_no'] = $day;
                $slot['day_off'] = in_array($key, $off_days) ? 1 : 0;
                $slot['start_time'] = $req['start'][$key];
                $slot['end_time'] = $req['end'][$key];
                $slot['title'] = $req['titles'][$key];
                $slot['link'] = $req['links'][$key];

                $hours[$day] = $slot;
            }

            $hours = array_merge($hours, $breaks);

            ConsultationHour::updateOrCreate(
                [
                    'user_id' => $user_id
                ],
                [
                    'hours' => $hours,
                ]
            );

            return redirect()->back();

        } catch (Exception $e) {

        }
	}

    // helper function
	protected function timeSlot()
    {
        // time slot rang in 30 min
        $formatter = function ($time = 24) {
            if ($time % 3600 == 0) {
                $current_time = date('H:00', $time);
                return ($current_time != '00:00') ? $current_time : '23:59';
            } else {
                return date('H:i', $time);
            }
        };

        if (CRUDBooster::myParentId() == 98) {
            $halfHourSteps = range(25200, 38 * 1800, 1800);
        } else {
            $halfHourSteps = range(0, 46 * 1800, 1800);
        }

        return array_map($formatter, $halfHourSteps);
    }

    // frontend 2
    public function consultationDate(Request $req): JsonResponse {
        $data = [];

        $has_consultation = 0;
        $next_consultation_day = $next_consultation_time = $next_consultation_topic = '';

        $consultation_hours = ConsultationHour::where('user_id', 2455)->value('hours');
        if (!empty($consultation_hours) && isset($consultation_hours[$req['day']])) {
            
            $hour_info = $consultation_hours[$req['day']];
            if (!empty($hour_info['title'][0]) && ($hour_info['start_time'][0] != '00:00' || $hour_info['end_time'][0] != '00:00')) {
                $has_consultation = 1;
                $next_consultation_topic = $hour_info['title'][0];
                
                $next_consultation_day = Carbon::parse($req['date'])->format('l');
                $next_consultation_time = $req['date'] . ' (' . $hour_info['start_time'][0] . ' - ' . $hour_info['end_time'][0] . ')';
            }
        }

        $data['has_consultation'] = $has_consultation;
        $data['next_consultation_day'] = $next_consultation_day;
        $data['next_consultation_time'] = $next_consultation_time;
        $data['next_consultation_topic'] = $next_consultation_topic;

        return response()->json([
            'success' => true,
            'data' => $data,
        ], 200);
    }

    /**
     * frontend 1: next consultation information
     */
    public function nextConsultation(Request $req): JsonResponse {
        $data = [];
        $data['next_consultation_day'] = $req['day'];
        $data['next_consultation_date'] = $req['date'];
        $data['next_consultation_time'] = $req['time'];
        $data['next_consultation_topic'] = $req['topic'];

        $off_days = [];
        $consultation_hours = ConsultationHour::where('user_id', 2455)->value('hours');
        if (!empty($consultation_hours)) {
            foreach ($consultation_hours as $index => $info) {
                if ($info['day_off'] == 1) {
                    $off_days[] = $info['day_no'];
                }
            }
        }
        $data['off_days'] = $off_days;

        $content = view('admin.consultation.next_consultation', compact('data'))->render();

        return response()->json([
            'success' => true,
            'data'    => $content, 
        ], 200);
    }
}
