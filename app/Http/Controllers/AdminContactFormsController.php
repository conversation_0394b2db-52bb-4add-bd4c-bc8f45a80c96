<?php

namespace App\Http\Controllers;

use App\ContactForm;
use App\ContactFormMessagesHistory;
use App\DropfunnelCustomerTag;
use App\DropfunnelTag;
use App\NewCustomer;
use App\Notifications\DRMNotification;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Session;
use Request;
use Illuminate\Http\Request as LaravelRequest;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Validator;

use \Illuminate\Support\Facades\Request as req;
use crocodicstudio\crudbooster\helpers\CB;

class AdminContactFormsController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {
        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "title";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = false;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = true;
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "contact_forms";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "#", "name" => "id"];
        $this->col[] = ["label" => __("Title"), "name" => "title", "width" => '70%'];
        $this->col[] = ["label" => __("Action")];
//        $this->col[] = ["label" => "Form Json", "name" => "form_json"];
//        $this->col[] = ["label" => "Form Html", "name" => "form_html"];
//        $this->col[] = ["label" => "Thank You Json", "name" => "thank_you_json"];
//        $this->col[] = ["label" => "Thank You Html", "name" => "thank_you_html"];
        // $this->col[] = ["label" => "Shareable Link", "name" => "shareable_link"];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|min:1|max:255","datatable"=>"user,id"];
        //$this->form[] = ["label"=>"Title","name"=>"title","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
        //$this->form[] = ["label"=>"Form Json","name"=>"form_json","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Form Html","name"=>"form_html","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Thank You Json","name"=>"thank_you_json","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Thank You Html","name"=>"thank_you_html","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Shareable Link","name"=>"shareable_link","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Slug","name"=>"slug","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();

        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = "

            function copyToClipBoard(id) {
                /* Get the text field */
                var copyText = document.getElementById(id);
                copyText.select();
                copyText.setSelectionRange(0, 99999); /* For mobile devices */
                document.execCommand('copy');
                return copyText.value;
            }

            let checkedValue = [];
            $(document).on('click','.shareFormCheck',function(){
                if ($(this).children('input').is(':checked')) {
                    let val = $(this).children('input').val();
                    let index = checkedValue.indexOf(val);
                    if (index < 0) {
                        checkedValue.push(val);
                    } else {
                        checkedValue.splice(index, 1);
                    }
                }
            });

            $(document).on('click','.share-form-with-user',function() {
                let id = $(this).data('id');
                console.log(checkedValue);
                $.ajax({
                    method: 'post',
                    url: '" . route('share.contactform') . "',
                    data: {id:id,user_ids:checkedValue},
                    success:function(response) {
                        $('#shareable_modal').modal('hide');
                        if (response.success) {
                            swal('Success!!',response.message,'success');
                        }
                    },
                    error:function(jqXHR, textStatus, errorThrown) {
                        swal('Oops!!',jqXHR.responseJSON.message,'error');
                    }
                });
            });

            $(document).on('click','#generate_btn_parent',function(){
                $(document).find('#response_url').attr('type','text');
                let copiedText = copyToClipBoard('response_url');
                $(document).find('#response_url').attr('type','hidden');
                swal('Copied Successfully', copiedText, 'success');
            });

            $(document).on('click','#embaded_code_copy',function(){
                $(document).find('#shareable_embaded_iframe_inp').removeAttr('disabled');
                let copiedText = copyToClipBoard('shareable_embaded_iframe_inp');
                $(document).find('#shareable_embaded_iframe_inp').attr('disabled','disabled');
                swal('Copied Successfully', copiedText, 'success');
            });

            $(document).on('keyup','.search-term',function(){
                let searchValue = $(this).val().trim();
                if (searchValue.length >= 3) {
                    if (searchValue) {
                        $.ajax({
                            method: 'post',
                            url: '" . route('search-user-name-email') . "',
                            data:{term:searchValue},
                            success:function(response) {
                                if (response.success) {
                                    $(document).find('.check-list-items').html(response.data);
                                }
                            }
                        });
                    }
                }
            });

            $(document).on('click','#generate_shareable_url', function(){
                let id = $(this).data('id');
                $.ajax({
                    url: '" . url('/admin/make-shareable-url') . "',
                    method: 'GET',
                    data:{id:id},
                    success: function(response) {
                        if (response.success) {
                            $('#response_url').val(response.url);
                            $(document).find('#response_url').attr('type','text');
                            let copiedText = copyToClipBoard('response_url');
                            $(document).find('#response_url').attr('type','hidden');
                            swal('Copied Successfully', copiedText, 'success');
                            $('#generate_btn_parent').html(response.url);
                        }
                    }
                });
            });

            $(document).on('click','.delete_contact_form',function(){
                let id = $(this).data('id');

                swal({
                    title: 'Do you want to Delete this?',
                    text: 'The proccess can not be undone!!!',
                    type:'warning',
                    showCancelButton:true,
                    allowOutsideClick:true,
                    confirmButtonColor: '#DD6B55',
                    confirmButtonText: 'Delete',
                    cancelButtonText: 'Cancel',
                    closeOnConfirm: true,
                },function() {
                    $.ajax({
                        method: 'GET',
                        url: '" . url('/admin/contact_forms/delete') . "'+'/'+id,
                        success: function(response) {
                            if (response.success) {
                                swal({
                                    type: 'success',
                                    title: 'Success!!',
                                    text: response.message,
                                    html: true
                                });
                            } else {
                                swal({
                                    type: 'error',
                                    title: 'Failed!!',
                                    text: response.message,
                                    html: true
                                });
                            }
                            location.reload();
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            swal('Oops!!',jqXHR.responseJSON.message,'error');
                        }
                    });
                });
            });
        ";


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = '
            <div id="shareable_embaded_iframe" class="modal fade" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Embaded Code</h4>
                            <button type="button" class="close" data-dismiss="modal">&times;</button>
                        </div>
                        <div class="modal-body">
                            <textarea id="shareable_embaded_iframe_inp" class="form-control" cols="30" rows="10" disabled></textarea>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="btn btn-success" id="embaded_code_copy">Copy</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="shareable_modal" class="modal fade" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h4 class="modal-title">Share Contact Form</h4>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">

                        </div>
                    </div>
                </div>
            </div>
                ';


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();
        $this->load_js[] = asset('js/dropfunnel/contact_form.js?v=1.1');


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = null;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();
    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        $query->where('user_id', CRUDBooster::myParentId());
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    private $contactForm;
    private $index = 1;

    public function hook_row_index($column_index, &$column_value)
    {
        if ($column_index == 1) {
            $this->contactForm = ContactForm::select('id', 'thank_you_html')->where('user_id', CRUDBooster::myParentId())->find($column_value);
            $column_value = $this->index++;
        }
        // if ($column_index == 3) {
        //     $url = $column_value;
        //     $column_value = '<span id="generate_btn_parent" style="cursor:pointer;"><input type="hidden" id="response_url" value="' . $url . '">';
        //     if (empty($url)) {
        //         $column_value .= '<button type="button" class="btn btn-drm btn-xs" data-id="' . $this->contactForm->id . '" id="generate_shareable_url">Generate</button>';
        //     } else {
        //         $column_value .= $url;
        //     }
        //     $column_value .= '</span>';
        // } <li style="margin-bottom: 2px;"><a style="color: #fff; text-align: left" href="javascript:void(0)" onclick="drm_contact_form_share(' . $this->contactForm->id . ')" class="btn btn-sm btn-info text-capitalize"><i class="fa fa-share"></i> share</a></li>

        if ($column_index == 3) {
            $column_value = '<div class="dropdown">
                      <button class="btn btn-drm dropdown-toggle" type="button" id="dropdownMenu' . $this->contactForm->id . '" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                            '.__("Actions").'
                        <span class="caret"></span>
                      </button>
                      <ul class="dropdown-menu action_dropdown" aria-labelledby="dropdownMenu' . $this->contactForm->id . '">
                        <li style="margin-bottom: 2px;"><a style="color: #222; text-align: left" href="javascript:void(0)" onclick="generate_iframe(' . $this->contactForm->id . ')" class="btn btn-sm btn-default text-capitalize"><i class="fa fa-cog"></i> '.__("Generate Iframe").' </a></li>';
            if (empty($this->contactForm->thank_you_html)) {
                $column_value .= '<li style="margin-bottom: 2px;"><a style="color: #fff; text-align: left" href="' . url('/admin/contact_forms/create-thank-you') . '/' . $this->contactForm->id . '" class="btn btn-sm btn-success text-capitalize"><i class="fa fa-plus"></i> '.__("Add Thank You").'</a></li>';
            } else {
                $column_value .= '<li style="margin-bottom: 2px;"><a style="color: #fff; text-align: left" href="' . url('/admin/contact_forms/edit-thank-you') . '/' . $this->contactForm->id . '" class="btn btn-sm btn-warning text-capitalize"><i class="fa fa-pencil-square-o"></i> '.__("Edit Thank You").' </a></li>';
            }

            $column_value .= '<li style="margin-bottom: 2px;"><a style="color: #fff; text-align: left" href="' . url('/admin/contact_forms/detail') . '/' . $this->contactForm->id . '" class="btn btn-sm btn-primary text-capitalize"><i class="fa fa-eye"></i> '.__("Preview").'</a></li>
                        <li style="margin-bottom: 2px;"><a style="color: #222; text-align: left" href="' . url('/admin/contact_forms/edit') . '/' . $this->contactForm->id . '" class="btn btn-sm text-capitalize"><i class="fa fa-pencil"></i> '.__("Edit").'</a></li>
                        <li style="margin-bottom: 2px;"><a style="color: #fff!important;text-align: left" href="javascript:void(0)" data-id="' . $this->contactForm->id . '" class="btn btn-sm btn-danger text-capitalize delete_contact_form"><i class="fa fa-trash"></i> '.__("Delete").'</a></li>
                      </ul>
                </div>';
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }

    public function getIndex()
    {
        $this->cbLoader();

        $module = CRUDBooster::getCurrentModule();

        if ((!CRUDBooster::isView() && $this->global_privilege == false) && !hasAllRightsPermission()) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        if (req::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::table(req::get('parent_table'))->where($parentTablePK, req::get('parent_id'))->first();
            if (req::get('foreign_key')) {
                $data['parent_field'] = req::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($parent_field) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $parent_field) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        $data['page_title'] = __('Contact Form');
        $data['action_add_data_title'] = __('Add Data');
        $data['date_candidate'] = $this->date_candidate;
        $data['limit'] = $limit = (req::get('limit')) ? req::get('limit') : $this->limit;

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::table($this->table)->select(DB::raw($this->table . "." . $this->primary_key));

        if (req::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . req::get('foreign_key'), req::get('parent_id'));
        }

        $this->hook_query_index($result);

        if (in_array('deleted_at', $table_columns)) {
            $result->where($this->table . '.deleted_at', null);
        }

        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;
        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {

                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if (req::get('q')) {
            $result->where(function ($w) use ($columns_table, $req) {
                foreach ($columns_table as $col) {
                    if (!$col['field_with']) {
                        continue;
                    }
                    if ($col['is_subquery']) {
                        continue;
                    }
                    $w->orwhere($col['field_with'], "like", "%" . req::get("q") . "%");
                }
            });
        }

        if (req::get('where')) {
            foreach (req::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }

        $filter_is_orderby = false;
        if (req::get('filter_column')) {

            $filter_column = req::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {
                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];

                if ($sorting != '') {
                    if ($key) {
                        $result->orderby($key, $sorting);
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];

        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(req::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (req::get('page')) ? req::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            if ($this->button_bulk_action) {

                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];

                if (isset($col['image'])) {
                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('vendor/crudbooster/avatar.jpg') . "'><img width='40px' height='40px' src='" . asset('vendor/crudbooster/avatar.jpg') . "'/></a>";
                    } else {
                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='" . $pic . "'><img width='40px' height='40px' src='" . $pic . "'/></a>";
                    }
                }

                if (@$col['download']) {
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = \Illuminate\Support\Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action):

                $button_action_style = $this->button_action_style;
                $html_content[] = "<div class='button_action' style='text-align:right'>" . view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render() . "</div>";

            endif;//button_table_action

            foreach ($html_content as $i => $v) {
                $this->hook_row_index($i, $v);
                $html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

        $data['html_contents'] = $html_contents;

        return view("crudbooster::default.index", $data);
    }

    //By the way, you can still create your own method in here... :)

    public function getAdd()
    {
        $this->cbLoader();
        if ((!CRUDBooster::isCreate() && $this->global_privilege == false || $this->button_add == false) && !hasAllRightsPermission()) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
        $data = [];
        $data['page_title'] = __('Contact Form builder');
        $authId = CRUDBooster::myParentId();
        $data['tags'] = DropfunnelTag::select('id', 'tag')->where('user_id', $authId)->get();
        $data['termsConditions'] = ContactForm::select('id', 'title')->whereNotNull('terms_conditions')->where('user_id', $authId)->get();
        DB::table('temp_contact_forms')->where('user_id', $authId)->whereDate('created_at', '<', Carbon::today())->delete();
        return view('admin.drm_email_marketings.contact_form.create', $data);
    }

    public function postAddSave()
    {
        $request = $_REQUEST;
        $rules = [
            'title' => 'required|string',
            'json' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if (empty(json_decode($value, true))) {
                        $fail('Please insert any element');
                    }
                },
                function ($attribute, $value, $fail) {
                    if (!preg_match_all('/"type":"button"/', $value)) {
                        $fail('You must enter a submit button');
                    }
                }
            ],
            'terms' => 'nullable|string',
            'bg_color' => 'nullable|string',
              // 'price'             => 'nullable',
            // 'pipeline_transfer' => 'boolean',
            // 'redirect_to_thank' => 'boolean',
        ];
        $response = $this->getJsonAndHtml();
        $request['json'] = $response['json'];
        $request['html'] = $response['html'];
        $response['partialHtml'] = array_values($response['partialHtml']);
        Validator::make($request, $rules)->validate();

        try {
            $redirect_to_thank = (bool) $request['redirect_to_thank'];
            $redirect_url      = $request['redirect_url'];

            $authId = CRUDBooster::myParentId();
            $form = new ContactForm();
            $form->user_id = $authId;
            $form->title = $request['title'];
//            if (!isLocal()) {
//                $form->form_json = $request['json'];
//            }
            $form->form_html = $request['html'];
            $form->terms_conditions = $request['terms'];
            $form->text_color = $request['text_color'];
            $form->bg_color = $request['bg_color'];
            $form->prev_btn_background = $request['prev_bg_color'];
            $form->prev_btn_text = $request['prev_btn_txt'];
            $form->next_btn_background = $request['next_bg_color'];
            $form->next_btn_text = $request['nxt_btn_txt'];
            $form->slug = $authId . uniqid();
            $form->price             = (isset($request['price']) && ($request['price'] > 0)) ? (float) $request['price'] : null;
            $form->pipeline_transfer = $request['pipeline_transfer'] ?? 0;
            $form->redirect_to_thank = $redirect_to_thank;
            $form->redirect_url      = !empty($redirect_url) ? $redirect_url : null;
            $form->save();

            $jsonDecoded = json_decode($request['json']);
            foreach ($jsonDecoded as $index => $json) {
                DB::table('contact_form_pages')->insert([
                    'contact_form_id' => $form->id,
                    'page' => $index + 1,
                    'user_id' => $authId,
                    'form_json' => json_encode($json),
                    'form_html' => $response['partialHtml'][$index],
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            DB::table('temp_contact_forms')->where('user_id', $authId)->whereNull('edit_id')->delete();

            if (!empty($request['tags'])) {
                $data = ['contact_form_id' => $form->id, 'user_id' => $authId, 'tag_id' => $request['tags'], 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];
                DB::table('contact_form_tags')->insert($data);
            }

            if (!$redirect_to_thank && !empty($redirect_url)) {
                $url = $redirect_url;
            } else {
                $url = url('/admin/contact_forms/create-thank-you') . '/' . $form->id;
            }

            return response()->json(['success' => true, 'message' => 'Data Successfully Stored', 'redirect_url' => $url]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => 'Failed to Store']);
        }
    }

    private function getJsonAndHtml($flag = null, $id = null)
    {
        $contactForms = DB::table('temp_contact_forms')->where('user_id', CRUDBooster::myParentId())->orderBy('page_number');
        if ($flag === 'edit') {
            $contactForms = $contactForms->whereNotNull('edit_id');
        } else {
            $contactForms = $contactForms->whereNull('edit_id');
        }
        $contactForms = $contactForms->select('form_json', 'form_html', 'page_number')->get();

        $contactFormHtmls = $contactForms->pluck('form_html', 'page_number')->toArray();
        $contactFormJson = $contactForms->pluck('form_json', 'page_number')->toArray();
        $margedJson = [];
        foreach ($contactFormJson as $json) {
            $margedJson[] = json_decode($json);
        }

        if ($id) {
            $data = DB::table('contact_form_pages')->where(['user_id' => CRUDBooster::myParentId(), 'contact_form_id' => $id])->select('form_json', 'form_html', 'page')->get()->keyBy('page');
            $pages = $data->pluck('form_html', 'page')->toArray();
            $pagesJson = $data->pluck('form_json', 'page')->toArray();
            foreach ($contactFormHtmls as $page => $html) {
                $pages[$page] = $html;
                $pagesJson[$page] = $contactFormJson[$page];
            }
            $contactFormHtmls = $pages;
            $margedJson = array_map(function ($item) {
                return json_decode($item);
            }, array_values($pagesJson));
        }

        $html = view('admin.drm_email_marketings.contact_form._multiple_step', compact('contactFormHtmls'))->render();
        return ['json' => json_encode($margedJson), 'html' => $html, 'partialHtml' => $contactFormHtmls];
    }

    public function getDetail($id)
    {
        $this->cbLoader();
        if ((!CRUDBooster::isCreate() && $this->global_privilege == false || $this->button_add == false) && !hasAllRightsPermission()) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
        $data = [];
        $data['page_title'] = 'Contact Form';
        $data['contactForm'] = ContactForm::select('title', 'form_html', 'thank_you_html', 'shareable_link', 'slug', 'bg_color', 'terms_conditions', 'prev_btn_background', 'prev_btn_text', 'next_btn_background', 'next_btn_text')->where('user_id', CRUDBooster::myParentId())->find($id)->toArray();
        return view('admin.drm_email_marketings.contact_form.show', $data);
    }

    public function getEdit($id)
    {
        $this->cbLoader();
        if ((!CRUDBooster::isCreate() && $this->global_privilege == false || $this->button_add == false) && !hasAllRightsPermission()) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = 'Contact Form builder';
        $authId = CRUDBooster::myParentId();
        $form = ContactForm::where('user_id', $authId)->find($id);

        if (empty($form)) {
            abort(404);
        }

        $data['contactForm'] = $form;
        $data['contactFormPages'] = DB::table('contact_form_pages')->where('user_id', $authId)->where('contact_form_id', $id)->get();
        DB::table('temp_contact_forms')->where('user_id', $authId)->whereDate('created_at', '<', Carbon::today())->delete();
        $data['existingTag'] = $form->dropfunnelTags()->pluck('id')->toArray();
        $data['tags'] = DropfunnelTag::select('id', 'tag')->where('user_id', $authId)->get();
        $data['termsConditions'] = ContactForm::select('id', 'title')->whereNotNull('terms_conditions')->where('user_id', $authId)->get();
        return view('admin.drm_email_marketings.contact_form.edit', $data);
    }

    public function postEditSave($id)
    {
        $request = $_REQUEST;

        $rules = [
            'title' => 'required|string',
            'json' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    if (empty(json_decode($value, true))) {
                        $fail('Please insert any element');
                    }
                },
                function ($attribute, $value, $fail) {
                    if (!preg_match_all('/"type":"button"/', $value)) {
                        $fail('You must enter a submit button');
                    }
                }
            ],
            'terms' => 'nullable|string',
            'bg_color' => 'nullable|string',
            // 'price'             => 'nullable',
            // 'pipeline_transfer' => 'boolean',
            // 'redirect_to_thank' => 'boolean',
        ];
        $response = $this->getJsonAndHtml('edit', $id);
        $request['json'] = $response['json'];
        $request['html'] = $response['html'];
        $response['partialHtml'] = array_values($response['partialHtml']);
        Validator::make($request, $rules)->validate();

        //DB::beginTransaction();
        try {
            $redirect_to_thank = (bool) $request['redirect_to_thank'];
            $redirect_url      = $request['redirect_url'];
            
            $authId = CRUDBooster::myParentId();
            $form = ContactForm::where('user_id', $authId)->find($id);
            $form->title = $request['title'];
//            if (!isLocal()) {
//                $form->form_json = $request['json'];
//            }
            $form->form_html = $request['html'];
            $form->terms_conditions = $request['terms'];
            $form->bg_color = $request['bg_color'];
            $form->text_color = $request['text_color'];
            $form->prev_btn_background = $request['prev_bg_color'];
            $form->prev_btn_text = $request['prev_btn_txt'];
            $form->next_btn_background = $request['next_bg_color'];
            $form->next_btn_text = $request['nxt_btn_txt'];
            $form->price             = (isset($request['price']) && ($request['price'] > 0)) ? (float) $request['price'] : null;
            $form->pipeline_transfer = $request['pipeline_transfer'] ?? 0;
            $form->redirect_to_thank = $redirect_to_thank;
            $form->redirect_url      = !empty($redirect_url) ? $redirect_url : null;
            $form->save();

            $jsonDecoded = json_decode($request['json']);
            foreach ($jsonDecoded as $index => $json) {
                DB::table('contact_form_pages')->updateOrInsert([
                    'contact_form_id' => $form->id,
                    'page' => $index + 1,
                    'user_id' => $authId
                ], [
                    'form_json' => json_encode($json),
                    'form_html' => $response['partialHtml'][$index],
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            DB::table('temp_contact_forms')->where('user_id', $authId)->whereNotNull('edit_id')->delete();

            if (!empty($request['tags'])) {
                DB::table('contact_form_tags')->where(['contact_form_id' => $form->id, 'user_id' => $authId])->delete();
                $data = ['contact_form_id' => $form->id, 'user_id' => $authId, 'tag_id' => $request['tags'], 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];
                DB::table('contact_form_tags')->insert($data);
            }

            //DB::commit();
            if (!$redirect_to_thank && !empty($redirect_url)) {
                $url = $redirect_url;
            } else {
                $url = url('/admin/contact_forms/edit-thank-you') . '/' . $form->id;
            }
            return response()->json(['success' => true, 'message' => 'Data Successfully Updated', 'redirect_url' => $url]);
        } catch (Exception $exception) {
            //DB::rollBack();
            return response()->json(['success' => false, 'message' => 'Failed to Update']);
        }
    }

    public function generateShareableUrl()
    {
        try {
            $id = intval($_GET['id'] ?? '');
            $form = ContactForm::where('user_id', CRUDBooster::myParentId())->find($id);
            $form->shareable_link = url('admin/contact_forms_share') . '/' . $form->slug;
            $form->save();
            return response()->json(['success' => true, 'url' => $form->shareable_link]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'url' => null]);
        }
    }

    public function shareContactForm($slug)
    {
        try {
            $form = ContactForm::where('slug', $slug)->whereNotNull('shareable_link')->first();
            $authId = CRUDBooster::myParentId();

            if ($authId == $form->user_id) {
                return redirect('/admin/contact_forms')->with(['message' => 'Hi There. You Already owner of this form', 'message_type' => 'info']);
            }

            $newForm = $form->replicate();
            $newForm->save();
            $newForm->user_id = $authId;
            $newForm->shareable_link = null;
            $newForm->terms_conditions = null;
            $newForm->slug = $authId . uniqid();
            $newForm->save();

            return redirect('/admin/contact_forms')->with(['message' => 'Congatulations!! You own a new Contact Form', 'message_type' => 'success']);
        } catch (Exception $exception) {
            return redirect('/admin')->with(['message' => 'Oops!! Something Went Wrong', 'message_type' => 'warning']);
        }
    }

    public function getGenerateEmbadedCode($id)
    {
        try {
            $form = ContactForm::where('user_id', CRUDBooster::myParentId())->find($id);
            if (!empty($form)) {
                $route = route('contact-form-iframe', $form->slug);
                $url = '<iframe width="800" height="450" style="border:none !important;" src="' . $route . '"></iframe>';
                return response()->json(['success' => true, 'url' => $url]);
            }
            throw new Exception('Data Not Found');
        } catch (Exception $exception) {
            return response()->json(['success' => true, 'url' => null]);
        }
    }

    public function previewContactFormIframe($slug)
    {
        // $d = DB::table('tax_rates')
        // ->select('country', 'country_de', 'country_code')
        // ->get()
        // ->map(function($item) {
        //     return [
        //         $item->country => $item->country_code,
        //         $item->country_de => $item->country_code,
        //         $item->country_code => $item->country_code,
        //     ];
        // })
        // ->toArray();

        // \Storage::disk('local')->put('country/country.json', json_encode(array_merge(...$d)));

        // $countryList = \Storage::disk('local')->get('country/country.json');
        // dd( $d,  json_decode($countryList, true) );


        try {
            $form = ContactForm::where('slug', $slug)->first();
            if (!empty($form)) {
                return view('admin.drm_email_marketings.contact_form._iframe', compact('form'));
            }
        } catch (Exception $exception) {

        }
    }

    //Multiple values to string
    private function multipleValuesToString($field_values, $values, $type)
    {
        $data = [];

        if(!is_array($values))
        {
            $values = [$values];            
        }

        if(!empty($values))
        {
            foreach($values as $value)
            {
                $data[] = @collect($field_values)->firstWhere('value', $value)->label;
            }            
        }

        return implode(', ', $data);
    }

    public function storeCustomer(LaravelRequest $request)
    {
        Validator::make($request->all(), [
            'full_name' => 'nullable|string',
            'email_address' => 'required|email', //unique:new_customers,email
            'phone_number' => 'nullable',
            'address' => 'nullable|string',
            'message' => 'nullable|string',
            'file_message' => 'nullable|mimes:jpg,bmp,png,pdf,txt',
        ])->validate();

        // Validate Email and phone on API
        try {
            $email = $request->email_address;
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);
        } catch(Exception $e) {
            return response()->json([
                'message' => 'The given data was invalid.',
                'errors' => [
                    'email_address' => $e->getMessage(),
                ],
            ], 422);
        }

        // Phone number
        try {
            if(isset($request->phone_number) && !empty($request->phone_number))
            {
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($request->phone_number);
            }
        } catch(Exception $e) {
            return response()->json([
                'message' => 'The given data was invalid.',
                'errors' => [
                    'phone_number' => $e->getMessage(),
                ],
            ], 422);
        }


        try {
            $conditions = ['slug' => $request->slug, 'user_id' => $request->user_id];
            $form = ContactForm::with('dropfunnelTags')->where($conditions)->first();
            if (empty($form)) {
                throw new Exception("Oops!! Something Went Wrong");
            }

            $user = User::find($form->user_id);
            if (empty($user)) {
                throw new Exception("Oops!! Something Went Wrong. Invalid user");
            }
            
            $contactFormPages = DB::table('contact_form_pages')->where('user_id', $form->user_id)->where('contact_form_id', $form->id)->get();
            $fields = $contactFormPages->pluck('form_json')->map(function($row) {
                return collect(json_decode($row))->whereNotNull('name');
            })
            ->flatten();

            $extra_fields = [];
            if(!empty($request->input('fields')))
            {
                foreach($request->input('fields') as $key => $value)
                {
                    $field = @$fields->firstWhere('name', 'fields['.$key.']');
                    $label = $field->label;

                    if($field->type && in_array($field->type, ['checkbox-group', 'select']))
                    {
                        $field_values = $field->values;
                        $value = $this->multipleValuesToString($field_values, $value, $field->type);
                    }

                    $extra_fields[] = [
                        'name' => 'fields['.$key.']',
                        'label' => $label,
                        'value' => $value,
                    ];
                }
            }

            //Process Extra fields
            if($request->hasFile('fields'))
            {
                foreach ($request->file('fields') as $key => $file) {
                    
                    $serverPath = uploadImage($file, 'contact_form_message');
                    $label = @$fields->firstWhere('name', 'fields['.$key.']')->label;

                    $extra_fields[] = [
                        'name' => 'fields['.$key.']',
                        'file' => 'yes',
                        'file_name' => $file->getClientOriginalName(),
                        'label' => $label,
                        'value' => $serverPath,
                    ];
                }
            }

            $fields_row = [
                'tag_select[]' => $request->tag_select,
                'tag_select1[]' => $request->tag_select1,
                'tag_select2[]' => $request->tag_select2,
                'tag_select3[]' => $request->tag_select3,
                'message' => $request->message,
                'full_name' => $request->full_name,
                'email_address' => $request->email_address,
                'phone_number' => $request->phone_number,
            ];

            ///Fields value
            foreach($fields_row as $key => $value)
            {
                $field = @$fields->firstWhere('name', $key);
                if(empty($field)) continue;

                $label = $field->label;

                if($field->type && in_array($field->type, ['checkbox-group', 'select']))
                {
                    $field_values = $field->values;
                    $value = $this->multipleValuesToString($field_values, $value, $field->type);
                }

                $extra_fields[] = [
                    'name' => $key,
                    'label' => $label,
                    'value' => $value,
                ];
            }



            $customer = NewCustomer::updateOrCreate(
                ['email' => $request->email_address, 'user_id' => $form->user_id],
                [
                    'full_name' => $request->full_name,
                    'phone' => $request->phone_number,
                    'address' => $request->address,
                    'insert_type' => 11,
                    'source' => 54,
                    'status' => 1
                ]
            );

            if (!empty($customer)) {
                $messageTitle = 'New Customer (' . $customer->email . ') registered from Contact Form' . PHP_EOL;

                $serverPath = null;
                if ($request->hasFile('file_message')) {
                    $file = $request->file('file_message');
                    $serverPath = uploadImage($file, 'contact_form_message');

                    $label = @$fields->firstWhere('name', 'file_message')->label;
                    $extra_fields[] = [
                        'name' => 'file_message',
                        'file' => 'yes',
                        'file_name' => $file->getClientOriginalName(),
                        'label' => $label,
                        'value' => $serverPath,
                    ];
                }

                $insert = false;
                $history = ['user_id' => $form->user_id, 'customer_id' => $customer->id, 'contact_form_id' => $form->id, 'checkbox_label' => $request->checkboxes_label, 'checkbox_value' => $request->checkboxes_value, 'message' => $request->message];
                
                if (!empty($request->checkboxes_label)) {
                    $insert = true;
                }

                if (!empty($request->message) || !empty($serverPath)) {
                    $messageTitle .= $request->message;
                    // message history insert
                    $history['message'] = $request->message;
                    $history['file_message'] = $serverPath;
                    $insert = true;
                }

                if(!empty($extra_fields))
                {
                    $sorted_extra = [];
                    foreach($fields as $f_l)
                    {
                        $used = false;
                        foreach($extra_fields as $k => $sk)
                        {
                            if($sk['label'] === $f_l->label) {
                                $sorted_extra[] = $sk;
                                unset($extra_fields[$k]);
                                $used = true;
                                break;
                            }
                        }

                        if(!$used && $f_l->type != 'button') {
                            $res = $this->remainingSortedExtra($request, $f_l);

                            $sorted_extra[] = [
                                "label" => $f_l->label,
                                "name" => $f_l->name,
                                "value" => $res['temp_value'],
                                'file_name' => $res['temp_file_name'],
                            ];
                        }
                    }

                    $sorted_extra = array_merge($sorted_extra, $extra_fields);
                    $history['extra_fields'] = json_encode($sorted_extra);
                    $insert = true;
                }

                if ($insert) {
                    $history_info = ContactFormMessagesHistory::create($history);

                    // transfer to pipeline
                    if ($form->pipeline_transfer && !empty($history_info->id) && !empty($history['customer_id'])) {
                        $contact_price = $form->price;
                        
                        if (!empty($contact_price) && (float) $contact_price > 0) {
                            $dealPayload = [
                                'source'       => 'contact',
                                'product_name' => $request->email_address . ' - ' . $form->title . ' - ' . $contact_price . ' EUR', 
                                'deal_title'   => $request->email_address . ' - ' . $form->title . ' - ' . $contact_price . ' EUR',
                                'deal_price'   => $contact_price,
                                'stage_id'     => 1,
                                'email_addr'   => $request->email_address,
                                'contact_name' => $request->full_name,
                                'user_id'      => $history['user_id'],
                                'contact_history_id' => $history_info->id,
                            ];
                            app('App\Http\Controllers\AdminPipelineController')->autoTypeDealCreation($dealPayload);
                        }
                    }
                }

                // Do accounting
                app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

                $url = url('admin/drm_all_customers/detail') . '/' . $customer->id;
                $user->notify(new DRMNotification($messageTitle, 'CONTACT_FORM_CUSTOMER_ADD', $url));

                if ($form->dropfunnelTags->isNotEmpty()) {
                    foreach ($form->dropfunnelTags as $tag) {
                        DropfunnelCustomerTag::insertTag($tag->tag, $form->user_id, $customer->id, 15);
                    }
                }

                if (!empty($request->tag_select)) {
                    foreach ($request->tag_select as $tag) {
                        DropfunnelCustomerTag::insertTag($tag, $form->user_id, $customer->id, 15);
                    }
                }

                return response()->json(['success' => true, 'message' => 'Data Successfully Stored', 'redirect_url' => route('contact-thank-you', $form->slug)]);
            }

            throw new Exception("Error Processing Request");
        } catch (Exception $exception) {
            notificationToTelegram('store customer error ' . $exception->getMessage() . ' In Line Number ' . $exception->getLine() . ' In File ' . $exception->getFile());
            return response()->json(['success' => false, 'message' => $exception->getMessage(), 'redirect_url' => null]);
        }
    }

    private function remainingSortedExtra(LaravelRequest $request, $f_l) {
        $reqArr = $request->toArray();
        $temp_value = $temp_file_name = '';

        $field_type = $f_l->type;
        $attrName = $f_l->name;

        if ($field_type == 'textarea' && ($attrName == 'address' || substr($attrName, 0, 9) == 'textarea-')) {
            $temp_value = $reqArr[$attrName];
        }
        elseif ($field_type == 'select' && substr($attrName, 0, 7) == 'select-' && isset($reqArr[$attrName])) {
            foreach ($f_l->values as $index => $info) {
                if ($info->value == $reqArr[$attrName]) {
                    $temp_value = $info->label;
                }
            }
        }
        elseif ($field_type == 'checkbox-group' && substr($attrName, 0, 15) == 'checkbox-group-' && isset($reqArr[$attrName])) {
            $temp_valueArr = [];

            foreach ($f_l->values as $index => $info) {
                if (in_array($info->value, $reqArr[$attrName])) {
                    $temp_valueArr[] = $info->label;
                }
            }

            $temp_value = implode(', ', $temp_valueArr);
        }
        elseif ($field_type == 'file' && substr($attrName, 0, 5) == 'file-') {
            if ($request->hasFile($attrName)) {
                $file = $request->file($attrName);
                $temp_file_name = $file->getClientOriginalName();
                $temp_value = uploadImage($file, 'contact_form_message');
            }
        }

        return [
            'temp_value' => $temp_value,
            'temp_file_name' => $temp_file_name,
        ];
    }

    public function previewThankYouPage($slug)
    {
        $form = ContactForm::select('thank_you_html')->where('slug', $slug)->first();
        $html = $form->thank_you_html;
        return view('admin.drm_email_marketings.contact_form.thank_you_page', compact('html'));
    }

    public function searchUserViaNameOrEmail(LaravelRequest $request)
    {
        if (!empty($request->term)) {
            $users = User::select('id', 'name', 'email')
                ->whereNotNull('email_verified_at')
                ->where(function ($query) use ($request) {
                    $query->where('email', 'like', '%' . $request->term . '%')->orWhere('name', 'like', '%' . $request->term . '%');
                })
                ->take(15)
                ->get();

            if ($users->isNotEmpty()) {
                $html = '';
                foreach ($users as $user) {
                    $html .= '<div class="checkbox">
                                <label class="shareFormCheck">
                                  <input type="checkbox" value="' . $user->id . '"> ' . $user->name . '(' . $user->email . ')
                                </label>
                            </div>';
                }
                return response()->json(['success' => true, 'data' => $html]);
            }
        }
        return response()->json(['success' => false, 'data' => null]);
    }

    public function shareContactFormModal($id)
    {
        $conditions = ['id' => $id, 'user_id' => CRUDBooster::myParentId()];
        $form = ContactForm::select('id', 'shareable_link')->where($conditions)->first();

        if (!empty($form)) {
            $html = view('admin.drm_email_marketings.contact_form._share_modal', compact('form'))->render();
            return response()->json(['success' => true, 'html' => $html]);
        }

        return response()->json(['success' => false, 'html' => '<p><strong>No Data Found</strong></p>']);
    }

    public function removeShareableUrl($id)
    {
        $conditions = ['id' => $id, 'user_id' => CRUDBooster::myParentId()];
        $form = ContactForm::select('id', 'shareable_link')->where($conditions)->whereNotNull('shareable_link')->first();

        if (!empty($form)) {
            $form->shareable_link = null;
            $form->save();
            return response()->json(['success' => true, 'message' => 'Url Revoke Successfully']);
        }

        return response()->json(['success' => false, 'html' => '<p><strong>No Data Found OR Please Generate Url</strong></p>']);
    }

    public function shareContactFormWithUser(LaravelRequest $request)
    {
        try {
            $id = $request->id ?? null;
            $form = ContactForm::where('user_id', CRUDBooster::myParentId())->where('id', $id)->first();
            if (!empty($form)) {
                $userIds = $request->user_ids ?? [];

                if (empty($userIds)) {
                    throw new Exception("Please Check some user");
                }

                $authId = CRUDBooster::myParentId();
                foreach ($userIds as $id) {
                    if ($id == $authId) { // prevent self sharing
                        continue;
                    }
                    $newForm = $form->replicate();
                    $newForm->save();
                    $newForm->user_id = $id;
                    $newForm->shareable_link = null;
                    $newForm->terms_conditions = null;
                    $newForm->slug = $id . uniqid();
                    $newForm->save();
                }
                return response()->json(['success' => true, 'message' => 'Contact Form Successfully Shared']);
            }
            return response()->json(['success' => false, 'message' => 'Something Went Wrong'], 400);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage()], 400);
        }
    }

    public function getDelete($id)
    {
        try {
            $authId = CRUDBooster::myParentId();
            $form = ContactForm::where('user_id', $authId)->where('id', $id)->first();
            if (!empty($form)) {
                $form->dropfunnelTags()->detach();
                DB::table('contact_form_pages')->where(['user_id' => $authId, 'contact_form_id' => $form->id])->delete();
                if ($form->delete()) {
                    return response()->json(['success' => true, 'message' => 'Form Delete Successfully']);
                }
                throw new Exception('Failed to Delete');
            }
            return response()->json(['success' => true, 'message' => 'No Data Found']);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage()], 400);
        }
    }

    public function getCreateThankYou($id)
    {
        $this->cbLoader();
        if ((!CRUDBooster::isCreate() && $this->global_privilege == false || $this->button_add == false) && !hasAllRightsPermission()) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
        $data = [];
        $data['page_title'] = 'Thank You builder';
        $data['id'] = $id;
        return view('admin.drm_email_marketings.contact_form.thank_you.create', $data);
    }

    public function postSaveThankYou()
    {
        $request = $_REQUEST;

        if (empty($request['html'])) {
            return response()->json(['success' => false, 'message' => 'Please Enter Something']);
        }

        try {
            $authId = CRUDBooster::myParentId();
            $form = ContactForm::where('user_id', $authId)->find($request['id']);
            $form->thank_you_json = null;
            $form->thank_you_html = $request['html'];
            $form->save();
            $url = url('/admin/contact_forms/iframe-page') . '/' . $form->id;
            return response()->json(['success' => true, 'message' => 'Data Successfully Stored', 'redirect_url' => $url]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => 'Failed to Store']);
        }
    }

    public function getEditThankYou($id)
    {
        $this->cbLoader();
        if ((!CRUDBooster::isCreate() && $this->global_privilege == false || $this->button_add == false) && !hasAllRightsPermission()) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
        $data = [];
        $data['page_title'] = 'Thank You Edit';
        $data['contactForm'] = ContactForm::where('user_id', CRUDBooster::myParentId())->find($id);
        if (empty($data['contactForm'])) {
            abort(404);
        }
        return view('admin.drm_email_marketings.contact_form.thank_you.edit', $data);
    }

    public function postUpdateThankYou($id)
    {
        $request = $_REQUEST;

        if (empty($request['html'])) {
            return response()->json(['success' => false, 'message' => 'Please Enter Something']);
        }

        try {
            $authId = CRUDBooster::myParentId();
            $form = ContactForm::where('user_id', $authId)->find($id);
            $form->thank_you_json = null;
            $form->thank_you_html = $request['html'];
            $form->save();

            $url = url('/admin/contact_forms/iframe-page') . '/' . $form->id;
            return response()->json(['success' => true, 'message' => 'Data Successfully Updated', 'redirect_url' => $url]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => 'Failed to Update']);
        }
    }

    public function getIframePage($id)
    {
        $this->cbLoader();
        if ((!CRUDBooster::isCreate() && $this->global_privilege == false || $this->button_add == false) && !hasAllRightsPermission()) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
        $data = [];
        $data['page_title'] = 'Embaded Code for Contact Form';
        $form = ContactForm::select('slug')->where('user_id', CRUDBooster::myParentId())->find($id);
        $url = '<iframe width="800" height="450" style="border:none !important;" src="' . route('contact-form-iframe', $form->slug) . '"></iframe>';
        $data['embadedCode'] = $url;
        return view('admin.drm_email_marketings.contact_form.thank_you._iframe_page', $data);
    }

    public function getLoadTerms($id)
    {
        $form = ContactForm::where('user_id', CRUDBooster::myParentId())->whereNotNull('terms_conditions')->find($id);
        if (!empty($form)) {
            return response()->json(['success' => true, 'data' => $form->terms_conditions]);
        }
        return response()->json(['success' => true, 'data' => null]);
    }

    public function storeContactFormDropfunnelTag(LaravelRequest $request)
    {
        Validator::make($request->all(), [
            'tag' => 'required|string'
        ])->validate();

        try {
            $authId = CRUDBooster::myParentId();
            $isTagExists = DropfunnelTag::where('user_id', $authId)
                ->where(DB::raw('LOWER(`tag`)'), strtolower($request->tag))
                ->exists();

            if ($isTagExists) {
                return response()->json(['success' => false, 'message' => 'Already Exists']);
            }

            $newTag = new DropfunnelTag();
            $newTag->tag = $request->tag;
            $newTag->user_id = $authId;
            $newTag->save();

            $allTags = DropfunnelTag::select('id', 'tag')->where('user_id', $authId)->get();

            return response()->json(['success' => true, 'message' => 'Tag Successfully Inserted', 'data' => $allTags]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => 'Failed to Store']);
        }

    }

    public function storeTempData(LaravelRequest $request)
    {
        try {
            $conditions = ['user_id' => CRUDBooster::myParentId(), 'page_number' => $request->page];
            if (!empty($request->edit_id)) {
                $conditions['edit_id'] = $request->edit_id;
            }
            $data = ['form_json' => $request->jsonData, 'form_html' => $request->html, 'created_at' => Carbon::now(), 'updated_at' => Carbon::now()];
            DB::table('temp_contact_forms')->updateOrInsert($conditions, $data);
            return response()->json(['success' => true]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine(), 'trace' => $exception->getTrace()]);
        }
    }

    public function getContactFormPreview(LaravelRequest $request)
    {
        try {
            $authId = CRUDBooster::myParentId();
            $contactFormHtmls = DB::table('temp_contact_forms')->where('user_id', $authId);
            if (!empty($request->flag) && $request->flag === 'onsave') {
                $contactFormHtmls = $contactFormHtmls->get();
                return response()->json(['success' => true, 'data' => $contactFormHtmls]);
            }
            if (!empty($request->page_number)) {
                $contactFormHtmls = $contactFormHtmls->where('page_number', $request->page_number)->value('form_json');
                return response()->json(['success' => true, 'jsonData' => $contactFormHtmls]);
            } else {
                if (!empty($request->edit_id)) {
                    $contactFormHtmls = $contactFormHtmls->where('edit_id', $request->edit_id);
                }
                $contactFormHtmls = $contactFormHtmls->orderBy('page_number')->pluck('form_html', 'page_number')->toArray();
                if (!empty($request->edit_id)) {
                    $pages = DB::table('contact_form_pages')->where(['user_id' => $authId, 'contact_form_id' => $request->edit_id])->orderBy('page')->pluck('form_html', 'page')->toArray();
                    foreach ($contactFormHtmls as $page => $data) {
                        $pages[$page] = $data;
                    }
                    $contactFormHtmls = empty($contactFormHtmls) ? $pages : array_values($pages);
                }
                $contactFormHtmls = view('admin.drm_email_marketings.contact_form._multiple_step', compact('contactFormHtmls'))->render();
            }
            return response()->json(['success' => true, 'html' => $contactFormHtmls]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine(), 'trace' => $exception->getTrace()]);
        }
    }

    public function deleteFormStep(LaravelRequest $request)
    {
        try {
            $authId = CRUDBooster::myParentId();
            if (DB::table('contact_form_pages')->where(['user_id' => $authId, 'id' => $request->id])->delete()) {
                $conditions = ['user_id' => $authId, 'page_number' => $request->page];
                if (!empty($request->edit)) {
                    $conditions['edit_id'] = $request->edit;
                }
                DB::table('temp_contact_forms')->where($conditions)->delete();
                $ids = DB::table('contact_form_pages')->where('user_id', $authId)->where('contact_form_id', $request->editId)->pluck('id')->toArray();
                foreach ($ids as  $key => $id) {
                    DB::table('contact_form_pages')->where('user_id', $authId)->where('id', $id)->update(['page' => $key + 1, 'updated_at' => now()]);
                }
                return response()->json(['success' => true]);
            }
            return response()->json(['success' => false]);

        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine(), 'trace' => $exception->getTrace()]);
        }
    }

    public function deleteContactFormStep(LaravelRequest $request)
    {
        try {
            $authId = CRUDBooster::myParentId();
            $conditions = ['user_id' => $authId, 'page_number' => $request->page];
            if (!empty($request->edit)) {
                $conditions['edit_id'] = $request->edit;
            }

            if (!DB::table('temp_contact_forms')->where($conditions)->exists() || DB::table('temp_contact_forms')->where($conditions)->delete()) {
                $ids = DB::table('temp_contact_forms')->where('user_id', $authId)->pluck('id')->toArray();
                foreach ($ids as  $key => $id) {
                    DB::table('temp_contact_forms')->where('user_id', $authId)->where('id', $id)->update(['page_number' => $key + 1, 'updated_at' => now()]);
                }
                return response()->json(['success' => true]);
            }
            return response()->json(['success' => false]);

        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage(), 'file' => $exception->getFile(), 'line' => $exception->getLine(), 'trace' => $exception->getTrace()]);
        }
    }

    //Show details
    public function getShowHistory($id)
    {
        $data = [];
        $data['history'] = ContactFormMessagesHistory::find($id, ['message', 'file_message', 'checkbox_label' , 'checkbox_value' , 'extra_fields', 'created_at']);
        $html = view('admin.drm_email_marketings.contact_form.history', $data)->render();
        return response()->json(['html' => $html]);
    }
}
