<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;

	use ServiceKey;
	use App\Coupon;
	use App\Enums\Apps;
	use App\Voucher;
	use Carbon\Carbon;

	class AdminCouponsController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {
	    	if(!CRUDBooster::isSuperAdmin() && !in_array(CRUDBooster::myID(), [71, 98, 2944, 3742, 3762])){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
	    	}

			if (request()->is('admin/coupons/*') && in_array(CRUDBooster::myID(), [2944, 3742, 3762])) {
	    		CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			}


			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "name";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = true;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_edit = false;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "coupons";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Coupon Code","name"=>"name"];
			$this->col[] = ["label"=>"Coupon Type","name"=>"coupon_type"];
			$this->col[] = ["label"=>"Percent Off (%)","name"=>"percent_off"];
			$this->col[] = ["label"=>"Amount Off","name"=>"amount_off"];
			$this->col[] = ["label"=>"Currency","name"=>"currency"];
			$this->col[] = ["label"=>"Duration","name"=>"duration"];
			$this->col[] = ["label"=>"Duration In Months","name"=>"duration_in_months"];
			$this->col[] = ["label"=>"Max Redemptions","name"=>"max_redemptions"];
			$this->col[] = ["label"=>"Expired Date","name"=>"redeem_by"];
			$this->col[] = ["label"=>"Contract For (Months)","name"=>"contract_for"];
			if (!in_array(CRUDBooster::myID(), [2944, 3742, 3762])) {
				$this->col[] = ["label"=>"Coupon ID","name"=>"coupon_id"];
			}
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Coupon Code','name'=>'name','type'=>'text','validation'=>'required|string|min:3|max:70|unique:coupons,name','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];

			$this->form[] = ['label'=>'Coupon Type','name'=>'coupon_type','type'=>'select','validation'=>'required','width'=>'col-sm-10', 'dataenum'=>'fixed|Fixed;percentage|Percentage', 'value' => 'fixed'];
			$this->form[] = ['label'=>'Amount Off','name'=>'amount_off','type'=>'text','validation'=>'nullable|required_if:coupon_type,fixed|integer','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Percent Off (%)','name'=>'percent_off','type'=>'text','validation'=>'nullable|required_if:coupon_type,percentage|numeric','width'=>'col-sm-10'];

			$this->form[] = ['label'=>'Currency','name'=>'currency','type'=>'select','validation'=>'required','width'=>'col-sm-10', 'dataenum'=>'eur|EUR;usd|USD', 'value' => 'eur'];

			$this->form[] = ['label'=>'Duration','name'=>'duration','type'=>'select','validation'=>'required','width'=>'col-sm-10', 'dataenum'=>'once|Once;repeating|Repeating;forever|Life Time', 'value' => 'once'];
			$this->form[] = ['label'=>'Duration In Months','name'=>'duration_in_months','type'=>'text','validation'=>'nullable|required_if:duration,repeating|integer','width'=>'col-sm-10'];

			$this->form[] = ['label'=>'Max Redemptions','name'=>'max_redemptions','type'=>'number','validation'=>'nullable','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Expired Date','name'=>'redeem_by','type'=>'datetime','validation'=>'nullable','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Contract For (Months)','name'=>'contract_for','type'=>'number','validation'=>'nullable','width'=>'col-sm-10'];

			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Name","name"=>"name","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
			//$this->form[] = ["label"=>"Percent Off","name"=>"percent_off","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Amount Off","name"=>"amount_off","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Currency","name"=>"currency","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Duration","name"=>"duration","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Duration In Months","name"=>"duration_in_months","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Max Redemptions","name"=>"max_redemptions","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
			//$this->form[] = ["label"=>"Redeem By","name"=>"redeem_by","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();
			if (in_array(CRUDBooster::myID(), [2944, 3742, 3762])) {
				$this->index_button[] = [ 
					'label' => __('Create Coupon'), 
					'url'   => 'javascript: void(0)', 
					'icon'  => 'fa fa-plus',
				];
			}



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = "

	        $(function(){
				couponFormFields();
	        });

            $(document).on('change', '#coupon_type, #duration', function(){
                couponFormFields();
            });

	        function couponFormFields(){
			    var coupon_type = $('#coupon_type').val();
			    var duration = $('#duration').val();

			    if(coupon_type == 'fixed'){
			    	$('#form-group-amount_off').show();
			    	$('#amount_off').prop('required',true);

			    	$('#form-group-percent_off').hide();
			    	$('#percent_off').prop('required',false);
			    }else{
			    	$('#form-group-amount_off').hide();
			    	$('#amount_off').prop('required',false);

			    	$('#form-group-percent_off').show();
			    	$('#percent_off').prop('required',true);
			    }

			    if(duration == 'repeating'){
			    	$('#form-group-duration_in_months').show();
			    	$('#duration_in_months').prop('required',true);

			    }else{
			    	$('#form-group-duration_in_months').hide();
			    	$('#duration_in_months').prop('required',false);
			    }
			}
	        ";


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

			if (in_array(CRUDBooster::myID(), [2944, 3742, 3762])) {
				$query = $query->where('creator_id', CRUDBooster::myID());
				// $query = $query->whereIn('creator_id', [2944, 3742, 3762]);
			}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
			$this->table 	   = !is_null($this->table) ? $this->table : 'coupons';
			$this->primary_key = !is_null($this->primary_key) ? $this->primary_key : 'id';

	        //Your code here
	        // \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
	        try{
		        $db_data = Coupon::find($id, ['name', 'coupon_type', 'percent_off', 'amount_off', 'currency', 'duration', 'duration_in_months', 'max_redemptions', 'redeem_by' ])->toArray();
		        if($db_data){

		        	$data = array_filter($db_data);
		        	$data['redeem_by'] = isset($data['redeem_by'])? Carbon::parse($data['redeem_by'])->unix() : null;

		        	if($data['coupon_type'] == 'fixed'){
		        		unset($data['percent_off']);

		        		$data['amount_off'] = $data['amount_off'] * 100;
		        	}else{
				    	unset($data['amount_off']);
		        	}

		        	if($data['duration'] != 'repeating'){
				    	unset($data['duration_in_months']);
		        	}

		        	unset($data['coupon_type']);

		     		$data = array_filter($data);

		        	//Create stripe Coupon
		        	\Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
		        	$coupon = \Stripe\Coupon::create($data);

		        	if($coupon && isset($coupon->id)){
		        		DB::table($this->table)->where($this->primary_key, $id)->update(['coupon_id' => $coupon->id]);
		        	}else{
		        		throw new \Exception('Stripe coupon creation failed!');
		        	}

					if(request()->ajax()) {
						return $coupon->id;
					}
		        }
	        }catch(\Exception $e){
	        	DB::table($this->table)->where($this->primary_key, $id)->delete();
	        	CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error')->send();
	        }

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	        try{
		        $db_data = Coupon::find($id, ['name', 'coupon_type', 'percent_off', 'amount_off', 'currency', 'duration', 'duration_in_months', 'max_redemptions', 'redeem_by' , 'coupon_id' ])->toArray();
		        if($db_data){

		        	$data = array_filter($db_data);
		        	$data['redeem_by'] = isset($data['redeem_by'])? Carbon::parse($data['redeem_by'])->unix() : null;

		        	if($data['coupon_type'] == 'fixed'){
		        		unset($data['percent_off']);
		        		$data['amount_off'] = $data['amount_off'] * 100;
		        	}else{
				    	unset($data['amount_off']);
		        	}

		        	if($data['duration'] != 'repeating'){
				    	unset($data['duration_in_months']);
		        	}

		        	unset($data['coupon_type']);
		        	unset($data['coupon_id']);

		     		$data = array_filter($data);

		        	//Create stripe Coupon
		        	\Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
		        	$old_coupon = (isset($db_data['coupon_id']) && $db_data['coupon_id'] )? \Stripe\Coupon::retrieve($db_data['coupon_id'], []) : null;

		        	// if($old_coupon){
		        	// 	\Stripe\Coupon::update($old_coupon->id, $data);
		        	// }else{
			        // 	$coupon = \Stripe\Coupon::create($data);

			        // 	if($coupon && isset($coupon->id)){
			        // 		DB::table($this->table)->where($this->primary_key, $id)->update(['coupon_id' => $coupon->id]);
			        // 	}else{
			        // 		throw new \Exception('Stripe coupon update failed!');
			        // 	}
		        	// }
		        }
	        }catch(\Exception $e){
	        	if( isset($e->getError()->code) && ($e->getError()->code == 'resource_missing') ){
	        		DB::table($this->table)->where($this->primary_key, $id)->delete();
	        		CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error')->send();
	        	}
	        	CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error')->send();
	        }
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here
	        try{
		        $db_data = Coupon::find($id, ['coupon_id']);
		        if($db_data){
		        	if($db_data->coupon_id){
						$stripe = new \Stripe\StripeClient(\DRM::stripeSecretKey('stripe_key_2455'));
						$response = $stripe->coupons->delete($db_data->coupon_id, []);
	        			if(is_null($response) || !$response->deleted){
	        				throw new \Exception('Stripe coupon delete failed!');
	        			}
		        	}
		        }
	        }catch(\Exception $e){
	        	//$e->getError()->code
	        	if( ($e->getCode() == 0) || ($e->getCode() == 'resource_missing') ){
	        		return;
	        	}
	        	CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error')->send();
	        }
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)
	    public function getValidateCoupon(){
	    	try{
		    	$coupon_code  = request()->coupon_code;
		    	if(is_null($coupon_code)) throw new \Exception(__('appForm.COUPON_EMPTY'));

		    	$db_coupon = DB::table('coupons')->where('name', $coupon_code)->first();
		    	if($db_coupon){
					// isLocal() || 
					if (checkTariffEligibility(CRUDBooster::myId())) {
						$voucher_id = app('\App\Services\CouponService')->getUserVoucherPId(CRUDBooster::myId(), $coupon_code);

						if (!empty($voucher_id)) {
							if (!isset(request()->onboarding_coupon_cat)) {
								return response()->json([
									'success' => false,
									'message' => __("onboarding_coupon_only_for_import_plan_msg")
								]);
							}
	
							// onboarding coupon 48 hours validity check
							if($db_coupon->redeem_by < Carbon::now()->toDateTimeString()) { // 48 hours elapsed
								app('\App\Services\CouponService')->hideOnboardingProgressBar($coupon_code, $voucher_id, CRUDBooster::myId());
								
								return response()->json([
									'success' => false,
									'message' => __("Coupon Expired!")
								]);
							}
						}
					}
					
		    		try{
			    		\Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
					    $coupon = \Stripe\Coupon::retrieve($db_coupon->coupon_id, []);
		    		}catch(\Exception $ee){}


				    if ($coupon->valid) {
				    	$discount_text = ($coupon->amount_off)? round(($coupon->amount_off / 100), 2).formatCurrency($coupon->currency) : $coupon->percent_off .'%';
						return response()->json([
							'success' => true,
							'coupon' => $coupon,
							'discount' => 'Discount: <b>'.$discount_text.'</b>',
							'message' => __('appForm.COUPON_ADDED')
						]);
					}

					return response()->json([
						'success' => false,
						'coupon' => $coupon,
						'message' => __('appForm.INVALID_COUPON')
					]);
		    	}else{
		    		throw new \Exception(__('appForm.INVALID_COUPON'));
		    	}

	    	}catch(\Exception $e){
				return response()->json([
					'success' => false,
					'message' => $e->getMessage()
				]);
	    	}

			//TODO:  Rework to use Stripe's status codes https://stripe.com/docs/api/php#errors
			// 200 means the coupon is valid
			// 400 means the coupon code was missing
			// 402 means it was a good coupon code, but it's expired or otherwise can't be used
			// 404 means the coupon code does not exist
	    }

	    public function getTestIntend(){
	    	$stripe = new \Stripe\StripeClient(\DRM::stripeSecretKey('stripe_key_2455'));
			$stripe_intend = $stripe->paymentIntents->create([
			  'amount' => 2000,
			  'currency' => 'eur',
			  // 'payment_method_types' => ['card'],
			]);

			return response()->json([
				'paymentIntent' => $stripe_intend->client_secret,
				'publicKey' => \DRM::stripePublicKey('stripe_key_2455')
			]);
	    }

	   	public function getTestPayment(){
	    	return view('stripe.index');
	    }


	    /*---------------------------------------------
	    -------------- user Voucher manage-------------
	    ----------------------------------------------*/
	    public function postAddVouchar(){
	    	$request = request();

    	    $request->validate([
                'coupon_code' => 'required'
            ]);

    	    try{
    	    	$coupon_code = $request->coupon_code;
		    	$db_coupon = DB::table('coupons')->where('name', $coupon_code)->first();

		    	if($db_coupon){
		    		\Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
				    $coupon = \Stripe\Coupon::retrieve($db_coupon->coupon_id, []);

				    if ($coupon->valid) {
						$coupon_cat = null;
						if (checkTariffEligibility(CRUDBooster::myId()) && ($coupon_code == config('global.onboarding.coupon_prefix') . CRUDBooster::myId()) ) {
							$coupon_cat = config('global.onboarding.coupon_cat');
						}

			    	    $vouchar = Voucher::updateOrCreate([
			    	    	'user_id' => CRUDBooster::myId()
			    	    ], 
						[
			    	    	'coupon_code' => $coupon_code,
			    	    	'coupon_cat'  => $coupon_cat
			    	    ]);

			    	    CRUDBooster::redirect($_SERVER['HTTP_REFERER'],"Coupon added successfully!","success");
					}else{
						throw new \Exception('Invalid Coupon!');
					}
		    	}else{
		    		throw new \Exception('Coupon not found!');
		    	}
    	    }catch(\Exception $e){
    	    	CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $e->getMessage(), "error");
    	    }
	    }

	    //Remove vouchar
	    public function getRemoveVouchar($id){
	    	try{
    	    	DB::table('user_coupons')->where('id', $id)->delete();
    	    	// DB::table('user_coupons')->whereIn('user_id', [CRUDBooster::myId(), 98])->where('id', $id)->delete();
		    	CRUDBooster::redirect($_SERVER['HTTP_REFERER'],"Vouchar removed successfully!", "success");
    	    }catch(\Exception $e){
    	    	CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $e->getMessage(), "error");
    	    }
	    }
	}
