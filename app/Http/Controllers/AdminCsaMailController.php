<?php

namespace App\Http\Controllers;

use App\Notifications\DRMNotification;
use Illuminate\Http\Request;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Services\Mailgun\Dropfunel;
use App\User;

class AdminCsaMailController extends Controller
{
    use Dropfunel;

    function index()
    {
        $query = DB::table('dkim_users_log');
        if(!request()->get('all')){
            $query->where('user_id', CRUDBooster::myId());
        }
        $user_emails = $query->get();
        return view('admin.csa_mail.index',compact('user_emails'));
    }

    function store(Request $req)
    {

        $validator = Validator::make($req->all(), [
            'email' => 'required|unique:dkim_users_log,email',
        ]);


        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }
        $email = $req->email;

        $key = $this->dkimKeyGenerate($email);

        //if key generation connection failed then outputs false
        if(!$key){
            return redirect()->back()->withErrors(['error' => 'Can not connect with Server'])->withInput();
        }

        //if key generation failed then outputs first word is Domain then return false
        $words = explode(' ', $key);

        if ($words[0] === 'Domain') {
            return redirect()->back()->withErrors(['error' => 'Key generation failed -->'.$key])->withInput();
        }

        //if key generation success then insert into database
        $user_id = CRUDBooster::myId();
        DB::table('dkim_users_log')->insert([
            'email' => $email,
            'user_id' => $user_id,
            'dkim_key' => $key,
            'created_at' => now(),
            'updated_at' => now()
        ]);

        return redirect()->back();

    }

    function delete(Request $req)
    {
        $id = $req->id;
        DB::table('dkim_users_log')->where('id', $id)->delete();
        return true;
    }

    function getCsaKeys(Request $req)
    {
        $domain = $req->domain;

        $key = $this->getDkimKey($domain);
        return response()->json(['success'=>true,'key' => $key]);
    }

    function confirmKeySetup(Request $req)
    {
        if($req->confirm_status==1){
            DB::table('dkim_users_log')->where('email', $req->domain)->update(['user_confirm_status' => $req->confirm_status]);
            User::find(71)->notify(new DRMNotification($req->domain.' has been set up by user '.CRUDBooster::myId(), '', '#'));

        }
        return response()->json(['success'=>true]);
    }
}
