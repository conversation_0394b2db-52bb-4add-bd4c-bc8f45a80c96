<?php namespace App\Http\Controllers;

use Apiz\Http\Request as HttpRequest;
use App\Jobs\SendPaywallEmailJob;
use App\Notifications\DRMTelegramNotification;

	use Session;
	//use Request;
	use DB;
	use CRUDBooster;
    use PDF;
    use App\NewOrder;
    use App\User;
    use App\Notifications\DRMNotification;
use App\Shop;
use Illuminate\Http\Request;
use App\Services\Payment\PaymentTax;

	class AdminCustomPaywallChargesController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "custom_paywall_charges";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"User Name","name"=>"user_id","join"=>"cms_users,name"];
			$this->col[] = ["label"=>"Charge","name"=>"charge"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'User Name','name'=>'user_id','type'=>'select','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'cms_users,name'];
			// $this->form[] = ['label'=>'Charge','name'=>'charge','type'=>'text','validation'=>'required|text|min:0','width'=>'col-sm-10'];
			$this->form[]=['label'=>'Charge(%)','name'=>'charge','type'=>'number','validation'=>'required|min:0','step'=>'any','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'cms_users,name'];
			//$this->form[] = ['label'=>'Charge','name'=>'charge','type'=>'text','validation'=>'required|text|min:0','width'=>'col-sm-10'];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }


	    //Order transfer
	    public function postTransferInvoice(){
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

	    	// //DB::beginTransaction();
	    	try{
	    		$auto_mail = DB::table('drm_order_mail')->where('cms_user_id', 2455)->value('auto_mail');
	    		$auto_mail_active = empty($auto_mail)? false : true;

	    		$user_count = $order_count = 0;

		    	$user_ids = request()->users;
		    	if($user_ids){
			    	$date_data = isset(request()->date)? request()->date : date('Y-m');
			    	$date_data = explode('-',$date_data);
			    	$year = isset($date_data[0])? $date_data[0] : date('Y');
			    	$month = isset($date_data[1])? $date_data[1] : date('m');

			    	$date_string = "$year-$month-".date('d').' '.date('H:i:s');
			    	$transfer_date = date('Y-m-d H:i:s', strtotime($date_string));

			    	if( isset(request()->date) && (is_null($year) || is_null($month) ) ) throw new \Exception('Invalid month, Year!');

			    	//Protected shop
			    	$protectedShop = \App\Shop::whereIn('user_id', $user_ids)
			    	->whereNotNull('protected_shop')
			    	->select('protected_shop', 'id', 'user_id', 'shop_name')
			    	->get()
			    	->map(function($item) {
			    		// $protection = $item->protected_shop ? 'yes' : 'no';
			    		return ['user_id' => $item->user_id, 'shop_id' => $item->id, 'shop_name' => $item->shop_name, 'protected' => (bool)$item->protected_shop ];
			    	})
			    	->groupBy('user_id')
			    	->toArray();

			    	//Transfered orders id
			    	$transfered_order_ids = [];

		    		foreach ($user_ids as $user_id) {

		    			$user = ( isset(request()->date) && $year && $month)? $this->yearMonthUserData($user_id, $month, $year) : $this->allDatesUserData($user_id);
 						                        
		    			if( $user && $user->due_orders->count() ){

	 						/*
	 						*
	 						* Protected shop
	 						*/
	                        $charges = [];
	                        $total_protected_shop_charge = 0;

	                        if( isset($protectedShop[$user_id]) && $protectedShop[$user_id] )
	                        {
	                        	$protectedShop_data = collect($protectedShop[$user_id])->pluck('shop_name', 'shop_id')->toArray();
	                        	$protectedShop_ids = collect($protectedShop[$user_id])->pluck('shop_id')->toArray();

	                        	$protectedShop_due_orders = clone $user->due_orders;
	                        	$protectedShop_due_orders = $protectedShop_due_orders->whereIn('shop_id', $protectedShop_ids)->groupBy('shop_id');

	                        	foreach($protectedShop_due_orders as $protected_shop_id => $protectedShop_due_order ) {

	                        		$shop_charge = $protectedShop_due_order->sum('eur_total');
	                        		$protection_charge = ($shop_charge < 250) ? 7.99 : 0;

	                        		$charges[] = [
	                                    'charge' => $shop_charge,
	                                    'shop_name' => $protectedShop_data[$protected_shop_id],
	                                    'shop_id' => $protected_shop_id,
	                                    'paywall_extra' => $protection_charge,
	                                ];

	                                $total_protected_shop_charge += $protection_charge;
	                        	}
	                        }
	                        //Protected shop price calculation end

	                        $paymentTax = new PaymentTax((int)$user_id, 2455);
					        $vat_number = $paymentTax->vatNumber();
					        $tax_rate = $paymentTax->taxRate() ?? 0;

		    				//due orders Id
		    				$due_orders_id = $user->due_orders->pluck('id');

			    			$order_count += $user->due_orders->count();
			    			$paywall_charge = $user->paywall_charge;

							//Total price
							$price = $user->total_payable + $total_protected_shop_charge;

							#tax calculation
		    				$total_tax = 0;
		    				$tax_rate = 0;
		    				$tax_version = 1;

		    				if($tax_rate) {
		    					$total_tax = (($price * $tax_rate) / 100);
		    					$price = $price + $total_tax;
		    				}

		    				$user_setting = DB::table('drm_invoice_setting')
		    				->where('cms_user_id', $user->id)
		    				->orderBy('id', 'desc')
		    				->first(['store_name', 'company_address']);

                            $order_paywall_charge = [
                            	'protected_shops' => $charges, 
                            	'paywall_charge' => $paywall_charge,
                            	'additional_charge' => $user->paywall_additional_charge,
                            	'protected_shop_charge' => $total_protected_shop_charge,
                            	'store_name' => strip_tags($user_setting->store_name),
								'company_address' => strip_tags($user_setting->company_address),
                            ];

							//Order insert
							$order_info = [
								'user_id' 		=> 2455,
				            	'cms_client'  	=> $user->id,
								'order_date'    => date('Y-m-d H:i:s'),
								'total' 		=> round($price, 2),
								'sub_total' 	=> round($user->total_due_charge_amount ,2),
								'total_tax' 	=> $total_tax,
								'status'    	=> $price == 0 ? 'paid' : "nicht_bezahlt",
								'payment_type'  => $price == 0 ? 'DRM' : null,
								'payment_date'  => $price == 0 ? now() : null,
								'currency'  	=> "EUR",
								'adjustment'    => 0,
								'insert_type'   => 4,
								'shop_id'       => null,
								'order_id_api'  => 'dpwl_'.$user->id.'_'.date('YmdHis'),
								'char_status'  => 1,
								'transfer_date' => $transfer_date,

								'tax_rate' => $tax_rate,
								'vat_number' => $vat_number,
                				'tax_version' => $tax_version,

                                // 'order_info' => $order_paywall_charge,
								'client_note' => '* This amount has been converted into Euro according to our terms and conditions. The exchange rate on the day of sale was decisive. In your documents is still the invoice in the original currency.',
							];

							$carts = [];

				          	foreach($user->due_orders as $order){

				          		$transfered_order_ids[] = ['id' => $order->id, 'shop_id' => $order->shop_id];

								$cart_item = [];
								$cart_title = 'Paywall charge - Order: '.$order->id;
								$addttional_title = '';
								$amount = round((($paywall_charge * $order->eur_total) / 100), 2);

								if($order->test_order == 1){
									$amount = round(0, 2);
									$addttional_title .= ' (Test Order)';
								}

								if($order->credit_number > 0){
									$addttional_title .= ' (Credit: '.$order->credit_number.')';
								}

								$cart_title .= $addttional_title;

								$cart_item['id'] = $order->id;
								$cart_item['product_name'] = $cart_title;
								$cart_item['description'] =  null;
								$cart_item['qty'] = 1;
								$cart_item['rate'] = $amount;
								$cart_item['tax'] = 0;
								$cart_item['product_discount'] = 0;
								$cart_item['currency'] = $order->currency;

								$cart_item['created_at'] = date('Y-m-d H:i:s', strtotime($order->order_date));
								$cart_item['invoice_number'] = inv_number_string($order->invoice_number, $order->inv_pattern) .$addttional_title;
								$cart_item['selling_price'] = $order->eur_total;

								$cart_item['amount'] = $amount;
								$carts[] = $cart_item;
				          	}

				          	$order_paywall_charge['item_charge'] = count($carts) * $user->paywall_additional_charge;
				          	$order_info['order_info'] = $order_paywall_charge;

							$order_info['cart'] = json_encode($carts);
							$new_order_data = app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $user->id, true);
				            if($new_order_data && $new_order_data->id)
				            {
				            	DB::table('new_orders')->whereIn('id', $due_orders_id)->update(['char_status' => 1]);
				            	$user_count++;

				            	$paywall_user_data = [];
				            	$paywall_user_data['data'] = $due_orders_id;
				            	$paywall_user_data['order_id'] = $new_order_data->id;
				            	$paywall_user_data['status'] = $order_info['total'] > 0 ? 'due' : 'paid';
				            	$paywall_user_data['paid_at'] = $order_info['total'] > 0 ? null : now();

				            	if( isset(request()->date) && $year && $month){
				            		$paywall_user_data['paywall_date'] = date('M, Y', strtotime('01-'.$month.'-'.$year));
				            	}else{
				            		$inv_latest_date = NewOrder::where('cms_user_id', $user_id)->latest()->first(['order_date']);
		        					$inv_first_date = NewOrder::where('cms_user_id', $user_id)->oldest()->first(['order_date']);
		        					$paywall_user_data['paywall_date'] = ($inv_latest_date->order_date && $inv_first_date->order_date)? date('M, Y', strtotime($inv_first_date->order_date)).' to '.date('M, Y', strtotime($inv_latest_date->order_date)) : date('M, Y');
				            	}

				            	//Create paywall and send email if automail on
				            	if($user->monthly_paywalls()->create($paywall_user_data)){

					            	//Stripe automatic payment
					            	try{

					            		if($order_info['total'] > 0)
					            		{
					            			$res = \DRM::stripeAutoOrderPayment($new_order_data->id, 1);
					            		}

						            	
					            	}catch(\Exception $epx) {

										try{
											if($auto_mail_active){
												// app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($new_order_data->id);
												SendPaywallEmailJob::dispatch($new_order_data->id);
											}
										}catch(\Exception $epx){}

									}
				            	}

				            }else{
				            	// $message = DB::table('notification_trigger')->where('hook', 'DRMPTransferPaywallInvoice')->where('status', 0)->first();
								// if ($message) {
									$message_title = $user->name.'\'s Paywall order transfer failed.';
									// if (isHookRemainOnSidebar('DRMPTransferPaywallInvoice') && isLocal()) {
									// 	User::find(98)->notify(new DRMTelegramNotification($message_title,'DRMPTransferPaywallInvoice'));
									// }else{
										User::find(98)->notify(new DRMNotification($message_title,'DRMPTransferPaywallInvoice'));
									// }

									$message_title_cus = 'Dear '.$user->name.', We request you to create a customer profile.';
									// if (isHookRemainOnSidebar('DRMPTransferPaywallInvoice') && isLocal()) {
									// 	User::find($user->id)->notify(new DRMTelegramNotification($message_title_cus,'DRMPTransferPaywallInvoice', CRUDBooster::adminPath('drm_all_customers/user-to-customer')));
									// }else{
										// User::find($user->id)->notify(new DRMNotification($message_title_cus,'DRMPTransferPaywallInvoice', CRUDBooster::adminPath('drm_all_customers/user-to-customer')));
									// }
								// }


				            }
		    			}
		    		}

		    		if($order_count == 0) throw new \Exception('No order avaliable to transfer!');
		    		if($user_count == 0) throw new \Exception('No order transfered!');

		    		//Create invoice on droptienda daily
		    		$this->createInvoiceOnDroptiendaDaily($transfered_order_ids);
		    	}

		    	// //DB::commit();
		    	return response()->json([
			    	'success'	=> true,
			    	'message'	=> 'Total '.$user_count.' user\'s Order transfered!'
			    ]);
	    	} catch (\Exception $e) {
			    // //DB::rollBack();
			    return response()->json([
			    	'success'	=> false,
			    	'message'	=> $e->getMessage()
			    ]);
			}
	    }




	    //By the way, you can still create your own method in here... :)

	    //Get current month data & filter option
	    public function getChargedAmountList(){
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

// DB::table('new_orders')->where('char_status', 1)->update(['char_status' => 0]);
// DB::table('new_orders')->where('insert_type', 4)->delete();

	    	$request = $_REQUEST;

	    	$date_data = isset($request['date'])? $request['date'] : date('Y-m');

	    	$date_data = explode('-',$date_data);

	    	$year = isset($date_data[0])? $date_data[0] : date('Y');
	    	$month = isset($date_data[1])? $date_data[1] : date('m');

		   	$data = [];
            $data['page_title'] = 'User List';
            $data['user_list'] = User::has('new_orders')->where('id_cms_privileges',3)->select('id', 'name')->get();
            $data['users'] = User::with(['new_orders' => function($q) use($month, $year){
            	return $q->whereYear('new_orders.order_date', '=', $year)->whereMonth('new_orders.order_date', '=', $month)->where('new_orders.invoice_number', '>', 0);
            } , 'paywall'])->whereHas('new_orders', function($qq) use($month, $year){
            	return $qq->whereYear('new_orders.order_date', '=', $year)->whereMonth('new_orders.order_date', '=', $month)->where('new_orders.invoice_number', '>', 0);
            })->where('id_cms_privileges',3)->where('paywall_blacklist', 0)->get();
            return view('admin.new_order.charged_amount_list',$data);
        }

        //All paywall charge list
       	public function getAllChargedAmountList(){
       		//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

		   	$data = [];
            $data['page_title'] = 'User List - All dates';
            $data['user_list'] = User::has('new_orders')->where('id_cms_privileges',3)->select('id', 'name')->get();
            $data['users'] = User::with(['new_orders' => function($q){
            	return $q->where('new_orders.invoice_number', '>', 0);
            } , 'paywall'])->whereHas('new_orders', function($qq){
            	return $qq->where('new_orders.invoice_number', '>', 0);
            })->where('id_cms_privileges',3)->where('paywall_blacklist', 0)->get()->sortByDesc('total_payable');
            return view('admin.new_order.charged_amount_list_all',$data);
        }

        //Generate invoice
	    public function getChargeInvoice($user_id)
	    {
			$data['page_title'] = 'Paywall Invoice';
	    	if(isset($_REQUEST['date']) && $_REQUEST['date'] ){
	    		$date_data = explode('-',$_REQUEST['date']);
	    		$year = isset($date_data[0])? $date_data[0] : date('Y');
	    		$month = isset($date_data[1])? $date_data[1] : date('m');
	    		if($year && $month){
		            $data['user'] = $this->yearMonthUserData($user_id, $month, $year);
	    		}else{
	    			if (CRUDBooster::myId()) CRUDBooster::redirectBack('Invalid month,Year!', 'error');
	    		}
	    	}else{
	    		$data['user'] = $this->allDatesUserData($user_id); // All data
	    	}

	    	if(is_null($data['user']) || is_null($data['user']->id)) CRUDBooster::redirectBack("Invalid user!", 'error');

	        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', config('global.fabian_daily_account_id'))->orderBy('id', 'desc')->first();
	        $data['user_setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();
	        $pdf = PDF::loadView('admin.invoice.charge', $data);
	        return $pdf->stream();
	    }

	    // View charged amount
	    public function getChargeAmount($user_id)
	    {
	    	$data['page_title'] = 'Charged Amount';
	    	$user = User::with('new_orders')->find($user_id);
	        $data = array(
	            'charged_due_amount'    => $user->total_payable_amount,
	            'charged_paid_amount'   => $user->total_paid_amount,
	            'total_order'       	=> $user->total_order,
	            'total_order_price' 	=> $user->total_order_amount,
	            'user_name'         	=> $user->name,
	            'user_id'				=> $user_id,

	        );
	        return view('admin.new_order.charged_amount', $data);
	    }

	    //Send monthly paywall
	    public function getSendPaywallInvoice($user_id){
	    	
	    	//Without fabian or superadmin deny access
	    	// if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	// }

	    	$data = [];
	    	if(isset($_REQUEST['date']) && $_REQUEST['date'] ){
	    		$date_data = explode('-',$_REQUEST['date']);
	    		$year = isset($date_data[0])? $date_data[0] : date('Y');
	    		$month = isset($date_data[1])? $date_data[1] : date('m');
	    		if($year && $month){
		            $data['user'] = $this->yearMonthUserData($user_id, $month, $year);
		            $data['page_title'] = 'Charged Amount Month: '.$month.' Year: '. $year.' from DRM';
	    		}else{
	    			if (CRUDBooster::myId()) CRUDBooster::redirectBack('Invalid month,Year!', 'error');
	    		}
	    	}else{
	    		$data['user'] = $this->allDatesUserData($user_id); // All data
	    		$latest_date = NewOrder::where('cms_user_id', $user_id)->latest()->first(['order_date']);
		        $first_date = NewOrder::where('cms_user_id', $user_id)->oldest()->first(['order_date']);
		        $data['page_title'] = 'DRM Charged Amount From: '.$first_date->order_date.' To: '. $latest_date->order_date;
	    	}

	        try {
	        	if(is_null($data['user']) || is_null($data['user']->id)) throw new \Exception("Invalid User!");

	        	$data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', config('global.fabian_daily_account_id'))->orderBy('id', 'desc')->first();
		        $data['user_setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();

	        	$pdf = PDF::loadView('admin.invoice.charge', $data);
	        	if (filter_var($data['user']->email, FILTER_VALIDATE_EMAIL)) {
		            app('drm.mailer')->getMailer()->send('admin.drm_order.charged_mailD', $data, function ($message) use ($data, $pdf) {
		                $message->to($data['user']->email, $data['user']->name)
		                    ->subject($data['page_title'])
		                    ->attachData($pdf->output(), "charged_invoice.pdf");
		            });
		            if (CRUDBooster::myId()) CRUDBooster::redirectBack(trans('Mail sent!'), 'success');
		        }else{
		        	if (CRUDBooster::myId()) CRUDBooster::redirectBack(trans('Invalid Email!'.$data['user']->email), 'error');
		        }
	        } catch (\Exception $e) {
				if (CRUDBooster::myId()) CRUDBooster::redirectBack($e->getMessage(), 'error');
	        }
	    }

	    // Creating for sending mail with charged amount for a All User
	    public function getEmailCharge($user_id)
	    {
	    	//Without fabian or superadmin deny access
	    	// if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	// }

	        $data = [];
	        $data['page_title'] = 'Invoice Details';
	        $data['user'] = User::with('new_orders')->find($user_id);
	        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', config('global.fabian_daily_account_id'))->orderBy('id', 'desc')->first();

	        $pdf = PDF::loadView('admin.invoice.charge', $data);

	        try {
	        	if (filter_var($data['user']->email, FILTER_VALIDATE_EMAIL)) {
		            app('drm.mailer')->getMailer()->send('admin.drm_order.charged_mailD', $data, function ($message) use ($data, $pdf) {
		                $message->to($data['user']->email, $data['user']->name)
		                    ->subject('Charged Amount this month from DRM')
		                    ->attachData($pdf->output(), "charged_invoice.pdf");
		            });
		            if (CRUDBooster::myId()) CRUDBooster::redirectBack(trans('Mail sent!'), 'success');
		        }else{
		        	if (CRUDBooster::myId()) CRUDBooster::redirectBack(trans('Invalid Email!'), 'error');
		        }
	        } catch (JWTException $exception) {
	            $this->serverstatuscode = "0";
	            $this->serverstatusdes = $exception->getMessage();
				if (CRUDBooster::myId()) CRUDBooster::redirectBack($exception->getMessage(), 'error');
	        }
	    }

 		//Year month wise paywall charge by user
	    private function yearMonthUserData($user_id, $month, $year){
	    	$data = User::with(['new_orders' => function($q) use($month, $year){
	            	return $q->whereYear('new_orders.order_date', '=', $year)->whereMonth('new_orders.order_date', '=', $month)->where('new_orders.invoice_number', '>', 0)->orderBy('new_orders.order_date');
	            } , 'paywall', 'billing_detail:user_id,vat_id',
	            'groups' => function($g) {
        			$g->where('user_groups.id', 2)->select('user_groups.id');
    			},
    			'old_tariff:id,user_id'
	        ])->whereHas('new_orders', function($qq) use($month, $year){
	            	return $qq->whereYear('new_orders.order_date', '=', $year)->whereMonth('new_orders.order_date', '=', $month)->where('new_orders.invoice_number', '>', 0);
	            })->where('id_cms_privileges',3)->find($user_id);
		    return $data;
	    }


	   	//All paywall charge by user
	    private function allDatesUserData($user_id){
	    	$data = User::with(['new_orders' => function($q){
	            	return $q->where('new_orders.invoice_number', '>', 0)->orderBy('new_orders.order_date');
	            } , 'paywall', 'billing_detail:user_id,vat_id',
	            'groups' => function($g) {
        			$g->where('user_groups.id', 2)->select('user_groups.id');
    			},
    			'old_tariff:id,user_id'
	        ])->whereHas('new_orders', function($qq){
	            	return $qq->where('new_orders.invoice_number', '>', 0);
	            })->where('id_cms_privileges',3)->find($user_id);
		    return $data;
        }

        // invoice blacklist
        public function postPaywallBlacklist(Request $request)
        {
            try {
            	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    			throw new \Exception("Permission denied!", 3);
	    		}

                $user_id = is_array($request->users) ? array_unique($request->users) : [];
                if($user_id){
					if(DB::table('cms_users')->whereIn('id', $user_id)->update(["paywall_blacklist" => true])){
						return response()->json(['success' => true, 'message' => 'Added to Blacklist successfully!'], 200);
					}else{
						throw new \Exception("Nothing added to Blacklist!", 1);
					}
                }else{
                	throw new \Exception("Nothing added to Blacklist!", 2);
                }
            } catch (\Exception $ex) {
                return response()->json(['success' => false, 'message' => $ex->getMessage()], 400);
            }
        }


        //Create order on droptienda daily [id, shop_id]
        public function createInvoiceOnDroptiendaDaily(array $transferedOrders)
        {
        	//All oredrs id
        	$orders_id = collect($transferedOrders)->pluck('id')->toArray();

        	//All shops
        	$shop_ids = collect($transferedOrders)->pluck('shop_id')->toArray();
        	if(empty($shop_ids)) return;

        	//Only Dt shops
        	$dt_shops = \App\Shop::whereIntegerInRaw('id', $shop_ids)->where('channel', 10)->pluck('id')->toArray();
        	if(empty($dt_shops)) return;

        	//Orders
        	$orders = NewOrder::with('user:id,name')->whereIntegerInRaw('id', $orders_id)->whereIn('shop_id', $dt_shops)->get();
        	if(empty($orders) || blank($orders)) return;


        	$auto_mail = DB::table('drm_order_mail')->where('cms_user_id', 2439)->value('auto_mail');
			$auto_mail_active = empty($auto_mail)? false : true;

			$user_id = 2455; //Dropmatix
			$transfer_date = now();
			$tax_rate = config('global.tax_for_invoice');
			$paywall_charge = 1.4;

			//Tatal value
			$total_due_charge = 0;
			$total_order_count = 0;

			$carts = [];
        	foreach($orders as $order)
        	{
        		$total_order_count++;

				$cart_item = [];
				$cart_title = 'Paywall charge - Order: '.$order->id;

				$addttional_title = '';
				$price = ($paywall_charge * $order->eur_total) / 100;
				$amount = round($price, 2);

				if($order->test_order == 1){
					$amount = round(0, 2);
					$addttional_title .= ' (Test Order)';
				}else{
					$total_due_charge += $price;
				}

				if($order->credit_number > 0){
					$addttional_title .= ' (Credit: '.$order->credit_number.')';
				}

				$addttional_title .= ' (User: '.$order->user->name.')';

				$cart_title .= $addttional_title;
				$cart_item['id'] = $k;
				$cart_item['product_name'] = $cart_title;
				$cart_item['description'] =  null;
				$cart_item['qty'] = 1;
				$cart_item['rate'] = $amount;
				$cart_item['tax'] = 0;
				$cart_item['product_discount'] = 0;
				$cart_item['currency'] = 'EUR';

				$cart_item['created_at'] = date('Y-m-d H:i:s', strtotime($order->order_date));
				$cart_item['invoice_number'] = inv_number_string($order->invoice_number, $order->inv_pattern) .$addttional_title;
				$cart_item['selling_price'] = $order->eur_total;

				$cart_item['amount'] = $amount;
				$carts[] = $cart_item;
        	}

			$price = $total_due_charge;
			$total_tax = (($price * $tax_rate)/ 100);


			$order_info = [
			  'user_id'     => 2439,
			  'cms_client'    => $user_id,
			  'order_date'    => date('Y-m-d H:i:s'),
			  'total'     => round($price + $total_tax, 2),
			  'sub_total'   => round($total_due_charge ,2),
			  'total_tax'   => $total_tax,
			  'tax_rate' => $tax_rate,
			  'payment_type'  => null,
			  'status'      => "nicht_bezahlt",
			  'currency'    => "EUR",
			  'adjustment'    => 0,
			  'insert_type'   => 4,
			  'shop_id'       => null,
			  'order_id_api'  => 'dpwl_'.$user_id.'_'.date('YmdHis').'DT',
			  'char_status'  => 1,
			  'transfer_date' => $transfer_date,

			  'client_note' => '* This amount has been converted into Euro according to our terms and conditions. The exchange rate on the day of sale was decisive. In your documents is still the invoice in the original currency.',
			];
			
			$order_info['status'] = $order_info['total'] == 0 ? 'paid' : 'nicht_bezahlt';


			if($order_info['status'] == 'paid')
			{
				$order_info['payment_type'] = 'DRM';
				$order_info['payment_date'] = now();
			}

			$user_setting = DB::table('drm_invoice_setting')
			->where('cms_user_id', $user_id)
			->orderBy('id', 'desc')
			->first(['store_name', 'company_address']);

			$order_info['order_info'] = [
            	'protected_shops' => [], 
            	'paywall_charge' => $paywall_charge,
            	'additional_charge' => 0,
            	'protected_shop_charge' => 0,
            	'store_name' => strip_tags($user_setting->store_name),
				'company_address' => strip_tags($user_setting->company_address),
				'item_charge' => 0,
            ];

			$order_info['cart'] = json_encode($carts);
			$new_order_data = app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $user_id, true);

			if(empty($new_order_data)){
				throw new \Exception('Droptienda daily account paywall failed!');
			}

			// Send droptienda paywall auto mail
			if($auto_mail_active && $new_order_data->id){
				app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($new_order_data->id);
			}
        }
	}
