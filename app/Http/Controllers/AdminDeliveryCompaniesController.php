<?php

namespace App\Http\Controllers;

use App\Country;
use App\Models\DeliveryNoteAssigned;
use App\Models\Marketplace\Product;
use App\Models\DeliveryNote;
use App\SenderEmailSetting;
use App\Services\ChannelProductService;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use Request;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use crocodicstudio\crudbooster\helpers\CB;
use App\AppStore;
use App\DrmSupplierCategory;
use App\DeliveryCompany;
use App\SupplierLog;
use App\NewOrder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;
use PDF;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;
use App\Http\Controllers\AdminDrmImportsController;
use Illuminate\Validation\Rule;
use Exception;

class AdminDeliveryCompaniesController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {
        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "name";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = false;
        $this->button_bulk_action = false;
        $this->button_action_style = "button_icon";
        $this->button_add = false;
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "delivery_companies";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        $this->supplierCountries = DB::table('countries')->pluck('name', 'id')->toArray();

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        if(CRUDBooster::isSuperadmin()){
            $this->col[] = ["label" => "User", "name" => "user_id", "join" => "cms_users,name"];
        }

        $this->col[] = ["label" => "Unternehmen", "name" => "name", 'inline_edit' => 'text'];
        $this->col[] = ["label" => "Website", "name" => "url", 'inline_edit' => 'url'];

        if ( CRUDBooster::isSuperadmin() || CRUDBooster::isDropMatrix()){
            $this->col[] = ["label" => "Marketplace Stock", "name" => "supplier_id", 'callback_php' => '$this->getMarketplaceStockOfSupplier($row->id)'];

            $this->col[] = ["label" => "Total Sales", "name" => "user_id", 'callback_php' => '$this->callbackForTotalSales($row->id)'];
        }

        // $this->col[] = ["label" => "Category", "name" => "category_id", "join" => "drm_supplier_categories,category_name"];
        $this->col[] = ["label" => "Category", "name" => "id", 'callback_php' => '$this->callbackForCategory($row->id)', 'inline_edit' => 'category'];

        $this->col[] = ["label" => "Straße & Nr.", "name" => "address"];
        $this->col[] = ["label" => 'PLZ', "name" => "zip"];
        $this->col[] = ["label" => 'Stadt/Ort', "name" => "state"];
        $this->col[] = ["label" => 'Phone', "name" => "phone", 'inline_edit' => 'text'];
        $this->col[] = ["label" => "Land", "name" => "country_id", "join" => "countries,name"];
        $this->col[] = ["label" => "E-Mail-Adresse", "name" => "email"];
        $this->col[] = ["label" => "Action", "name" => "id", "inline_edit" => 'action'];
        # END COLUMNS DO NOT REMOVE THIS LINE


        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => __('Supplier Name'), 'name' => 'name', 'type' => 'text', 'validation' => 'required|string|min:3|max:70', 'width' => 'col-sm-10', 'placeholder' =>__('Supplier Name')];
        $this->form[] = ['label' => __('Website'), 'name' => 'url', 'type' => 'text', 'validation' => 'max:70', 'width' => 'col-sm-10', 'placeholder' => __('Website')];
        $this->form[] = ['label' => __('Address'), 'name' => 'address', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'placeholder' => __('Address')];
        $this->form[] = ['label' => __('Zip'), 'name' => 'zip', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'placeholder' => __('Zip')];
        $this->form[] = ['label' => __('City'), 'name' => 'state', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'placeholder' => __('City')];
        $this->form[] = ['label' => __('Country'), 'name' => 'country_id', 'type' => 'select', 'validation' => 'required|integer|min:0', 'width' => 'col-sm-10', 'datatable' => 'countries,name', 'placeholder' => __('Country')];
        $this->form[] = ['label' => __('Email'), 'name' => 'email', 'type' => 'email', 'validation' => 'required|min:1|max:255|email', 'width' => 'col-sm-10', 'placeholder' => __('Please enter a valid email address')];
        $this->form[] = ['label' => __('Phone'), 'name' => 'phone', 'type' => 'text', 'validation' => 'required|numeric', 'width' => 'col-sm-10', 'placeholder' => __('Phone')];
        $this->form[] = ['label' => __('Contact Name'), 'name' => 'contact_name', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'placeholder' => __('Contact Name')];
        $this->form[] = ['label' => __('Customer Number'), 'name' => 'customer_number', 'type' => 'text', 'validation' => 'nullable|min:1|max:255', 'width' => 'col-sm-10', 'placeholder' => __('Customer Number')];
        $this->form[] = ['label' => __('Category'), 'name' => 'category_id', 'type' => 'select2', 'validation' => 'nullable|integer|min:0', 'width' => 'col-sm-10', 'datatable' => 'drm_supplier_categories,category_name', 'datatable_where' => 'drm_supplier_categories.user_id = ' . CRUDBooster::myParentId(), 'placeholder' => __('Category')];

        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
        //$this->form[] = ["label"=>"Name","name"=>"name","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
        //$this->form[] = ["label"=>"Address","name"=>"address","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Zip","name"=>"zip","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"State","name"=>"state","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Country Id","name"=>"country_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"country,id"];
        //$this->form[] = ["label"=>"Email","name"=>"email","type"=>"email","required"=>TRUE,"validation"=>"required|min:1|max:255|email|unique:delivery_companies","placeholder"=>"Please enter a valid email address"];
        //$this->form[] = ["label"=>"Phone","name"=>"phone","type"=>"number","required"=>TRUE,"validation"=>"required|numeric","placeholder"=>"You can only enter the number only"];
        //$this->form[] = ["label"=>"Contact Name","name"=>"contact_name","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = "
	            #table_dashboard tr td:nth-child(4) {
                    width: 214px;
                }

	        ";


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    //Country edit select box
    private function countryEditableHtml($field_name, $pk_id, $selected_id, $list)
    {
        $option = '';
        if(is_array($list)) {
            foreach ($list as $id => $name) {
                $selected = $id == $selected_id ? 'selected' : '';
                $option .= '<option value="'.$id.'" '.$selected.'>'.$name.'</option>';
            }
        }
        $html  = '<select class="supplier_edit_country" data-old="'.$selected_id.'" data-id="'.$pk_id.'" name="'.$field_name.'"><option disabled readonly value="">Select country</option>'.$option.'</select>';
        return $html;
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        $user_id = CRUDBooster::myParentId();
        if (!CRUDBooster::isSuperadmin() && !CRUDBooster::isDropMatrix() ) {
            $query->where('delivery_companies.user_id', $user_id);
        } else if ( CRUDBooster::isDropMatrix() ) {
            $query->where(function($q) use ($user_id) {
                $q->where('delivery_companies.user_id', $user_id);
            });
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        // $action_column = 9;

        // if ( CRUDBooster::isSuperadmin())
        // {
        //     $action_column += 1;

        // } elseif ( CRUDBooster::isDropMatrix() ) {

        //     $action_column += 2;
        // }

        // if ($column_index == $action_column) {


        //     $id = $column_value;
        //     // $detail_path = CRUDBooster::mainpath('detail/'.$id);         previous details page link
        //     $detail_path = CRUDBooster::adminPath('suppliers/detail/'.$id);
        //     $edit_path = CRUDBooster::mainpath('edit/'.$id);
        //     $delete_path = CRUDBooster::mainpath('delete/'.$id);

        //     $onclick = 'swal({
        //         title: "Are you sure ?",
        //         text: "You will not be able to recover this record data!",
        //         type: "warning",
        //         showCancelButton: true,
        //         confirmButtonColor: "#ff0000",
        //         confirmButtonText: "Yes!",
        //         cancelButtonText: "No",
        //         closeOnConfirm: false,
        //         showLoaderOnConfirm: true
        //         },
        //         function(){
        //             location.href = "'.$delete_path.'";
        //         })';

        //     $column_value = "<div class='button_action' style='text-align:left'>
        //                         <a href=$detail_path class='btn btn-xs btn-primary btn-detail' title='Detail Data'> <i class='fa fa-eye'></i> </a>
        //                         <a href=$edit_path class='btn btn-xs btn-success btn-edit' title='Edit Data'> <i class='fa fa-pencil'></i> </a>
        //                         <a href='javascript:void(0)' class='btn btn-xs btn-warning btn-delete' title='Delete' onclick='$onclick'><i class='fa fa-trash'></i>
        //                         </a>
        //                     </div>";

        // }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {

        $postdata['user_id'] = CRUDBooster::myParentId();
        //custom email validation
        Validator::make($postdata, [
            'email' => [
                'required',
                Rule::unique('delivery_companies')->where(function ($query) {
                    return $query->where('user_id', CRUDBooster::myParentId());
                })],
            'name' => ['required'],
            'contact_name' => ['required'],
            'phone' => ['required'],
            'address' => ['required'],
            'zip' => ['required'],
            'state' => ['required'],
            'country_id' => ['required'],
        ], [
                'address.required' => 'Street field is required',
            ]

        )->validate();

        // Validate Email and phone on API
        try {
            $email = $postdata['email'];
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);
            if(isset($postdata['phone']) && !empty($postdata['phone']))
            {
                $phone = $postdata['phone'];
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
            }
        } catch (\Exception $e) {
            return CRUDBooster::redirectBack($e->getMessage(), 'error');
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here
        \App\Services\CheckListProgress\Checklist::cache_key_clear(3, CRUDBooster::myParentId());
        $redirect_url = CRUDBooster::adminPath('suppliers/detail/').'/'.$id;

        // Do accounting
        app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

        CRUDBooster::redirect($redirect_url, trans('Supplier save successfully'), 'success');
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        $postdata['user_id'] = CRUDBooster::myParentId();

        //custom email validation
        Validator::make($postdata, [
            'email' => [
                'required',
                Rule::unique('delivery_companies')->where(function ($query) {
                    return $query->where('user_id', CRUDBooster::myParentId());
                })->ignore($id)], //ignoring supplier that is being eidted
        ])->validate();

        // Validate Email and phone on API
        try {
            $email = $postdata['email'];
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);
            if(isset($postdata['phone']) && !empty($postdata['phone']))
            {
                $phone = $postdata['phone'];
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
            }
        } catch (\Exception $e) {
            return CRUDBooster::redirectBack($e->getMessage(), 'error');
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

        // Do accounting
        app(\App\Services\UiValidation\UiValidation::class)->doAccounting();
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
            // Marketplace
            try {
                \App\Models\Marketplace\Product::where('delivery_company_id', $id)->delete();
                \App\Models\Marketplace\Collection::where('delivery_company_id', $id)->delete();
            } catch (\Exception $e) {}

            $import_controller = new AdminDrmImportsController();
            $imports = DB::table('drm_imports')->where('delivery_company_id', $id)->get();

            foreach ($imports as $import) {
                $import_controller->deleteImport($import->id);
            }
            //Clear account activity step
            \App\Services\CheckListProgress\Checklist::cache_key_clear(3, CRUDBooster::myParentId());
    }

    public function getAddressManagement()
    {

    }

    public function getChangeBaseCalculation()
    {

    }


    public function getAssortment()
    {

    }

    public function getAdd()
    {
        $this->cbLoader();
        if (!CRUDBooster::isCreate()) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $page_title = trans("crudbooster.add_data_page_title", ['module' => CRUDBooster::getCurrentModule()->name]);
        $page_menu = Route::getCurrentRoute()->getActionName();
        $command = 'add';

        $db_countries = DB::table('countries')->select('name', 'id', 'country_shortcut as code')->get();
        $countries = $db_countries->keyBy('name')->toArray();
        $countriesJson = json_encode($countries);
        $countries = $db_countries->keyBy('id')->toArray();
        $countriesRevJson = json_encode($countries);


        return view('admin.drm_delivery_companies.form', compact('page_title', 'page_menu', 'command', 'countriesJson', 'countriesRevJson'));
    }


    public function getEdit($id)
    {
        $this->cbLoader();
        $row = DB::table($this->table)->where($this->primary_key, $id)->first();

        if (! CRUDBooster::isRead() && $this->global_privilege == false || $this->button_edit == false) {
            CRUDBooster::insertLog(trans("crudbooster.log_try_edit", [
                'name' => $row->{$this->title_field},
                'module' => CRUDBooster::getCurrentModule()->name,
            ]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $page_menu = Route::getCurrentRoute()->getActionName();
        $page_title = trans("crudbooster.edit_data_page_title", ['module' => CRUDBooster::getCurrentModule()->name, 'name' => $row->{$this->title_field}]);
        $command = 'edit';
        Session::put('current_row_id', $id);


        $db_countries = DB::table('countries')->select('name', 'id', 'country_shortcut as code')->get();
        $countries = $db_countries->keyBy('name')->toArray();
        $countriesJson = json_encode($countries);
        $countries = $db_countries->keyBy('id')->toArray();
        $countriesRevJson = json_encode($countries);

        return view('admin.drm_delivery_companies.form', compact('id', 'row', 'page_menu', 'page_title', 'command', 'countriesJson', 'countriesRevJson'));
    }


    public function getImportData()
    {
        $this->cbLoader();
        $data['page_menu'] = \Route::getCurrentRoute()->getActionName();
        $data['page_title'] = 'Import Data ' . $module->name;

        if (Request::get('file') && !Request::get('import')) {
            $file = base64_decode(Request::get('file'));
            $file = storage_path('app/' . $file);
            // $rows = Excel::load($file, function ($reader) {
            // })->get();
            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;
            //dd($countRows );
            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = ['name', 'address', 'zip', 'state', 'country_id', 'email', 'phone', 'contact_name', 'category', 'customer_number', 'profile_picture','note', 'url'];

            $labels = [
                0 => 'Name Lieferant',
                1 => 'Straße',
                2 => 'PLZ',
                3 => 'Ort/Stadt',
                4 => 'Land',
                5 => 'E-Mail-Adresse',
                6 => 'Telefon',
                7 => 'Name Ansprechpartner',
                8 => 'Kategorie',
                9 => 'Kundennummer',
                10 => 'Hersteller-Logo',
                11 => 'Notitz',
                12 => 'Webseite',
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;

        }
        return view('admin.drm_delivery_companies.import', $data);
    }


    public function postDoImportChunk()
    {
        $this->cbLoader();
        $file_md5 = md5(Request::get('file'));

        if (Request::get('file') && Request::get('resume') == 1) {
            $total = Session::get('total_data_import');
            $prog = intval(Cache::get('success_' . $file_md5)) / $total * 100;
            $prog = round($prog, 2);
            if ($prog >= 100) {
                Cache::forget('success_' . $file_md5);
            }

            return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
        }

        $select_column = Session::get('select_column');
        $select_column = array_filter($select_column);
        // $table_columns = DB::getSchemaBuilder()->getColumnListing($this->table);

        $table_columns = ['name', 'address', 'zip', 'state', 'country_id', 'email', 'phone', 'contact_name', 'category', 'customer_number', 'profile_picture', 'note', 'url'];

        $file = base64_decode(Request::get('file'));
        $file = storage_path('app/' . $file);

        // $rows = Excel::load($file, function ($reader) {
        // })->get();
        $type = pathinfo($file, PATHINFO_EXTENSION);
        $import = new AdminDrmImportsController;
        $rows = $import->csvToArray($file, $type, 'auto', false);

        $has_created_at = false;
        if (CRUDBooster::isColumnExists($this->table, 'created_at')) {
            $has_created_at = true;
        }

        $countries = Country::all();
        $country_labels = config('global.country_labels');
        $count = 0;
        $data_import_column = [];
        foreach ($rows as $value) {

            $count++;
            Cache::put('success_' . $file_md5, $count);
            $value = (object)$value;
            $a = [];

            foreach ($select_column as $sk => $s) {
                $a['user_id'] = CRUDBooster::myParentId();
                $colname = $table_columns[$sk];

                if($colname == 'category') continue;
                if($colname == 'country_id'){
                    $country_name = $value->$s;
                    $found = array_filter($country_labels,function($v,$k) use ($country_name){
                        if(in_array($country_name,$v)){
                            return $k;
                        }
                        return null;
                    },ARRAY_FILTER_USE_BOTH);
                    if($found){
                        $country_name = array_keys($found)[0];
                    }
                    $country_name = $countries->where('name',$country_name)->first();
                    if(!$country_name) continue;
                    $a[$colname] = $country_name->id;
                    continue;
                }
                if ($value->$s == '') {
                    continue;
                }
                $a[$colname] = $value->$s;
            }

            try {

                if ($has_created_at) {
                    $a['created_at'] = date('Y-m-d H:i:s');
                }

                DB::table($this->table)->insert($a);
            } catch (Exception $e) {
                $e = (string)$e;
                Cache::put('error_' . $file_md5, $e, 500);
            }
        }

        return response()->json(['status' => true]);
    }


    public function getIndex()
    {
        $this->cbLoader();

        $module = CRUDBooster::getCurrentModule();

        if (!CRUDBooster::isView() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        if (Request::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
            if (Request::get('foreign_key')) {
                $data['parent_field'] = Request::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($parent_field) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $parent_field) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        $data['page_title'] = $module->name;
        $data['iframe_url'] = 'https://player.vimeo.com/video/388360305';
        $data['page_description'] = trans('crudbooster.default_module_description');
        $data['date_candidate'] = $this->date_candidate;
        $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::table($this->table)->select(DB::raw($this->table . "." . $this->primary_key));

        if (Request::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . Request::get('foreign_key'), Request::get('parent_id'));
        }

        $this->hook_query_index($result);

        if (in_array('deleted_at', $table_columns)) {
            $result->where($this->table . '.deleted_at', null);
        }

        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;
        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {

                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if (Request::get('q')) {
            $result->where(function ($w) use ($columns_table, $request) {
                foreach ($columns_table as $col) {
                    if (!$col['field_with']) {
                        continue;
                    }
                    if ($col['is_subquery']) {
                        continue;
                    }
                    $w->orwhere($col['field_with'], "like", "%" . Request::get("q") . "%");
                }
            });
        }

        if (Request::get('where')) {
            foreach (Request::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }

        $filter_is_orderby = false;
        if (Request::get('filter_column')) {

            $filter_column = Request::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {
                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];

                if ($sorting != '') {
                    if ($key) {
                        $result->orderby($key, $sorting);
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];

        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(Request::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (Request::get('page')) ? Request::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            $pk_id = $row->id;
            $country_id = $row->country_id;

            if ($this->button_bulk_action) {

                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];

                $field_name = $col['name'];

                $edit_field_type = ( isset($col['inline_edit']) && $col['inline_edit'] ) ? $col['inline_edit'] : null;
                if ($edit_field_type) {

                    if($edit_field_type == 'text') {
                        $value = '<input class="supplier_quick_edit bn-input-style q-input" name="'.$field_name.'" value="'.$value.'" data-id="'.$pk_id.'">';
                    }

                    if($edit_field_type == 'url') {
                        if (!pathIsUrl($value)) {
                            $value = "http://" . $value;
                        }
                        $value = '<input class="supplier_quick_edit bn-input-style q-input" name="'.$field_name.'" value="'.$value.'" data-id="'.$pk_id.'">';
                    }

                    if($edit_field_type == 'country') {
                        $value = $this->countryEditableHtml($field_name, $pk_id, $country_id, $this->supplierCountries);
                    }




                    if($edit_field_type == 'action') {
                        //$value = $this->countryEditableHtml($field_name, $pk_id, $country_id, $this->supplierCountries);

            $id = $value;
            // $detail_path = CRUDBooster::mainpath('detail/'.$id);         previous details page link
            $detail_path = CRUDBooster::adminPath('suppliers/detail/'.$id);
            $edit_path = CRUDBooster::mainpath('edit/'.$id);
            $delete_path = CRUDBooster::mainpath('delete/'.$id);

            $onclick = 'swal({
                title: "Are you sure ?",
                text: "You will not be able to recover this record data!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#ff0000",
                confirmButtonText: "Yes!",
                cancelButtonText: "No",
                closeOnConfirm: false,
                showLoaderOnConfirm: true
                },
                function(){
                    location.href = "'.$delete_path.'";
                })';

            $value = "<div class='button_action' style='text-align:left'>
                                <a href=$detail_path class='btn btn-xs btn-primary btn-detail' title='Detail Data'> <i class='fa fa-eye'></i> </a>
                                <a href=$edit_path class='btn btn-xs btn-success btn-edit' title='Edit Data'> <i class='fa fa-pencil'></i> </a>
                                <a href='javascript:void(0)' class='btn btn-xs btn-warning btn-delete' title='Delete' onclick='$onclick'><i class='fa fa-trash'></i>
                                </a>
                            </div>";










                    }














                }



                if (isset($col['image'])) {
                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('vendor/crudbooster/avatar.jpg') . "'><img width='40px' height='40px' src='" . asset('vendor/crudbooster/avatar.jpg') . "'/></a>";
                    } else {
                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='" . $pic . "'><img width='40px' height='40px' src='" . $pic . "'/></a>";
                    }
                }

                if (@$col['download']) {
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action):

                $button_action_style = $this->button_action_style;
                $html_content[] = "<div class='button_action' style='text-align:right'>" . view('admin.crudbooster.supplier_action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render() . "</div>";

            endif;//button_table_action

            foreach ($html_content as $i => $v) {
                $this->hook_row_index($i, $v);
                $html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

        $data['html_contents'] = $html_contents;
        return view('admin.drm_delivery_companies.index', $data);
    }

    // get details
    public function getDetail($id)
    {

        $data['page_title'] = 'Details';

        $data['d_companies'] = DeliveryCompany::with('logs')->where(['user_id' => CRUDBooster::myParentId(), 'id' => $id])->first();

        if ($data['d_companies'] == null) {
            CRUDBooster::redirect(CRUDBooster::mainpath(), trans('crudbooster.denied_access'));
        }

        $data['docs'] = json_decode($data['d_companies']->doc);

        return view('admin.drm_delivery_companies.details', $data);
    }


    //preview delivery note
    public function getDeliveryNote()
    {
        $log_id = request()->log_id;
        if ($log_id) {
            $log = SupplierLog::find($log_id);
            $data = $log->data;
            if ($data) {
                $html = $data['delivery_note'];
                return \PDF::loadHTML($html)->setWarnings(false)->stream();
            }

            dd($log, $html, $pdf_stream);
        }

    }

    // upload-profile-doc
    public function postUploadProfileDoc()
    {
        if ($_FILES['profile_picture']) {
            $ext = end(explode(".", $_FILES['profile_picture']['name']));
            $name = 'pp' . time() . '.' . $ext;

            $file = Request::file('profile_picture');
            $path = $file->storeAs('public/delivery_companies/profile_picture', $name, ['visibility' => 'public', 'disk' => 'spaces']);

            $path = Storage::disk('spaces')->url($path);

            DB::table('delivery_companies')->where('id', $_POST['id'])->update(['profile_picture' => $path]);

        } else if ($_FILES['upload_doc']) {

            $docs = json_decode(DB::table('delivery_companies')->where('id', $_POST['id'])->first()->doc, true);
            if (!$docs) {
                $count = 1;
            } else {
                $count = count($docs) + 1;
            }

            if ($count > 5) {
                return response()->json('File is maxed out.', 500);
            }


            if ($docs) {
                foreach ($docs as $doc_file_name) {
                    $decoded_url = urldecode($doc_file_name);

                    $database_file_name = pathinfo($decoded_url, PATHINFO_FILENAME);
                    $uploaded_file_name = preg_replace('/\s+/', '_', \App\Helper\Encoding::toUTF8(array_shift(explode(".", $_FILES['upload_doc']['name']))));

                    if (mb_convert_case($database_file_name, MB_CASE_UPPER, "UTF-8") == mb_convert_case($uploaded_file_name, MB_CASE_UPPER, "UTF-8")) {
                        return response()->json('File name already exists !!!', 422);
                    }
                }
            }


            $ext = end(explode(".", $_FILES['upload_doc']['name']));

            $name = preg_replace('/\s+/', '_', \App\Helper\Encoding::toUTF8($_FILES['upload_doc']['name']));

            $file = Request::file('upload_doc');

            $docs['doc' . $count] = $file->storeAs('public/delivery_companies/doc', $name, ['visibility' => 'public', 'disk' => 'spaces']);
            $docs['doc' . $count] = Storage::disk('spaces')->url($docs['doc' . $count]);

            $path = $docs['doc' . $count];
            DB::table('delivery_companies')->where('id', $_POST['id'])->update([
                // 'file'.$_POST["file_no"] => $name,
                'doc' => json_encode($docs),
            ]);
        } else {
            return response()->json('Picture not found', 500);
        }

        $data['file'] = $path;
        $data['label'] = $name;
        $data['type'] = array_key_first($_FILES);
        $data['count'] = $count;
        $data['docs'] = $docs;
        return response()->json($data);
        // return response()->json($_REQUEST);
    }

    public function postUpdateNote()
    {
        // return $_REQUEST;

        DB::table('delivery_companies')->where('id', $_REQUEST['id'])->update([
            'note' => $_REQUEST['note'],
        ]);
        $data = $_REQUEST;
        return response()->json($data, 200);
    }

    public function getSupplierListJson()
    {
        $email = isset($_REQUEST['email']) ? $_REQUEST['email'] : null;
        $suppliers = \App\DeliveryCompany::select('id', 'name', 'email')->where('user_id', CRUDBooster::myParentId())->get()->map(function ($item) use ($email) {
            $check = ($item->email == $email) ? 'selected' : '';
            return ['id' => $item->id, 'name' => $item->name, 'selected' => $check];
        })->toArray();
        if (count($suppliers)) {
            return response()->json([
                'success' => true,
                'data' => $suppliers
            ]);
        }
        return response()->json([
            'success' => true,
            'message' => 'Something went wrong!'
        ]);
    }

    public function postAddCategory()
    {
        $request = $_REQUEST;
        //custom email validation
        Validator::make($request, [
            'supplier_category_name' => 'required',
        ], [
                'supplier_category_name.required' => 'Category required!',
            ]

        )->validate();
        $data = DrmSupplierCategory::updateOrCreate(['category_name' => $request['supplier_category_name'], 'user_id' => CRUDBooster::myParentId()]);
        if ($data) {
            return response()->json([
                'success' => true,
                'message' => "Category created successfully!",
                'name' => $data->category_name,
                'id' => $data->id,
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => "Something went wrong!",
            ]);
        }
    }

    //Supplier email setting
    public function getSupplierEmailSetting()
    {
        $data['page_title'] = __('order.SUPPLIER_EMAIL_SETTING') . ' Template Setting';
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('drm_supplier_mail')->where('cms_user_id', $authId)->whereNull('channel')->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['order_ftp'] = \App\FtpCredential::where(['user_id' => $authId, 'is_i7o' => 1])->exists();
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myId());
        return view("admin.new_order.supplier_email_setting", $data);
    }

    public function postSaveSupplierEmailSetting()
    {
//        if (isLocal()) {
            Validator::make($_REQUEST, [
                'sender_email' => 'required|email'
            ])->validate();
//        }

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'email_template' => $_REQUEST['email_template'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'auto_send_ftp' => $_REQUEST['auto_send_ftp'],
        ];

        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        if (!empty($_REQUEST['channel'])) {
            $data['channel'] = $_REQUEST['channel'];
        }

        $authId = CRUDBooster::myParentId();
        Cache::forget('drm_supplier_mail_' . $authId);
        clear_remote_cache('drm_supplier_mail_' . $authId);
        DB::table('drm_supplier_mail')->updateOrInsert([
            'cms_user_id' => $authId,
            'channel' => !empty($_REQUEST['channel']) ? $_REQUEST['channel'] : NULL
        ],
            $data
        );

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Supplier Email Setting Changed'), 'success');
    }

    public function postTestSupplierEmail()
    {
        try {

            //Check user has supplier template
            // $drm_supplier_mail = DB::table('drm_supplier_mail')->where('cms_user_id', CRUDBooster::myParentId())->first();
            // if (is_null($drm_supplier_mail) || is_null($drm_supplier_mail->email_template) || !strlen(strip_tags($drm_supplier_mail->email_template))) {
            //     CRUDBooster::redirect(CRUDBooster::adminPath('delivery_companies/supplier-email-setting'), __('order.SUPPLIER_EMAIL_TEMPLATE_EMPTY'), 'info');
            // }

            $supplier_name = 'Fake Supplier Contact Name';

            $data = [];
            $data['page_title'] = 'Delivery Note';
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $data['order'] = $order;
            $data['product_list'] = json_decode($order->cart);
            $data['customer'] = $customer = (object)$order->customer;


            try {
                $shop_type = \App\Shop::where('id', $order->shop_id)->value('channel');
                $data['setting'] = DB::table('delivery_note_assign')
                    ->join('delivery_note_settings','.delivery_note_settings.id','=','delivery_note_assign.note_id')
                    ->where('delivery_note_assign.user_id', $order->cms_user_id)
                    ->where('delivery_note_assign.channel_type', $shop_type)
                    ->orderBy('delivery_note_assign.id', 'desc')
                    ->first();
            }catch (Exception $exception){

            }

            if(empty($data['setting'])){
                $data['setting'] = DeliveryNote::DoesntHave('channels')
                    ->DoesntHave('order')
                    ->where('cms_user_id', $order->cms_user_id)
                    ->orderBy('id', 'desc')
                    ->first();
            }


//            $data['setting'] = $invoice_data = DB::table('delivery_note_settings')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
            $invoice_data = $data['setting'];
            $pdf_stream = PDF::loadView('admin.invoice.delivery_note', $data)->setWarnings(false)->stream();

            $product_list = json_decode($order->cart);
            $billing = '<p>' . formatBillingAddress($order->billing) . '</p>';
            $shipping = '<p>' . formatBillingAddress($order->shipping) . '</p>';

            $logo = ($invoice_data->logo) ? $invoice_data->logo : asset("images/logo-dummy.png");
            $tags = [
                'customer_name' => $customer->full_name,
                'company_name' => $customer->company_name,
                'billing_address' => $billing,
                'shipping_address' => $shipping,
                'order_items' => view('admin.new_order.email_supplier_order_items', compact('product_list', 'order'))->render(),
                'order_items_n_article' => view('admin.new_order.email_supplier_order_items_n_article', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => $order->invoice_number,
                'contact_name' => $supplier_name,
                'pay_url' => null,
                'PAYWALL' => false,
                'IS_DROPSHIPPING'   => true,
                'IS_FULFILLMENT'    => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $template = DRMParseSupplierEmailTemplate($tags, $order->cms_user_id);
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];
            $data['subject'] = $template['subject'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            // $note_name = 'delivery_note_' . $supplier_name . '_' . $order->invoice_number;
            // $note_name = preg_replace('/\s+/', '_', $note_name);
            // $note_name = preg_replace('/[_]+/', '_', $note_name);
            $note_name = $order->id . '.pdf';

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_supplier', $template, function ($messages) use ($data, $pdf_stream, $note_name) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

                $messages->attachData($pdf_stream, $note_name, [
                    'mime' => 'application/pdf',
                ]);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    //Supplier email setting by channel
    public function getSupplierEmailSettingByChannel()
    {
        $data['page_title'] = __('order.SUPPLIER_EMAIL_SETTING') . ' Template Setting';

        $channel = $_REQUEST['channel'];
        $authId = CRUDBooster::myParentId();

        $data['mail'] = DB::table('drm_supplier_mail')->where('cms_user_id', $authId)->where('channel', $channel)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['order_ftp'] = \App\FtpCredential::where(['user_id' => $authId, 'is_i7o' => 1])->exists();

        $data['channel'] = (int) $channel;

        // return view("admin.new_order.system_email_by_channel.supplier_email_setting", $data);
        return view("admin.new_order.supplier_email_setting", $data);
    }

    public function postSaveSupplierEmailSettingByChannel()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'email_template' => $_REQUEST['email_template'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'auto_send_ftp' => $_REQUEST['auto_send_ftp'],
        ];

        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        $authId = CRUDBooster::myParentId();

        Cache::forget('drm_supplier_mail_by_channels_' . $authId . '_' . $_REQUEST['channel']);
        clear_remote_cache('drm_supplier_mail_by_channels_' . $authId . '_' . $_REQUEST['channel']);

        DB::table('drm_supplier_mail_by_channels')->updateOrInsert([
            'cms_user_id' => $authId,
            'channel' => $_REQUEST['channel']
        ],
            $data
        );

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Supplier Email Setting Changed'), 'success');
    }

    public function postTestSupplierEmailByChannel()
    {
        try {

            //Check user has supplier template
            // $drm_supplier_mail = DB::table('drm_supplier_mail')->where('cms_user_id', CRUDBooster::myParentId())->first();
            // if (is_null($drm_supplier_mail) || is_null($drm_supplier_mail->email_template) || !strlen(strip_tags($drm_supplier_mail->email_template))) {
            //     CRUDBooster::redirect(CRUDBooster::adminPath('delivery_companies/supplier-email-setting'), __('order.SUPPLIER_EMAIL_TEMPLATE_EMPTY'), 'info');
            // }

            $supplier_name = 'Fake Supplier Contact Name';

            $data = [];
            $data['page_title'] = 'Delivery Note';
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $data['order'] = $order;
            $data['product_list'] = json_decode($order->cart);
            $data['customer'] = $customer = (object)$order->customer;


            try {
                $shop_type = \App\Shop::where('id', $order->shop_id)->value('channel');
                $data['setting'] = DB::table('delivery_note_assign')
                    ->join('delivery_note_settings','.delivery_note_settings.id','=','delivery_note_assign.note_id')
                    ->where('delivery_note_assign.user_id', $order->cms_user_id)
                    ->where('delivery_note_assign.channel_type', $shop_type)
                    ->orderBy('delivery_note_assign.id', 'desc')
                    ->first();
            }catch (Exception $exception){

            }

            if(empty($data['setting'])){
                $data['setting'] = DeliveryNote::DoesntHave('channels')
                    ->DoesntHave('order')
                    ->where('cms_user_id', $order->cms_user_id)
                    ->orderBy('id', 'desc')
                    ->first();
            }


//            $data['setting'] = $invoice_data = DB::table('delivery_note_settings')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();
            $invoice_data = $data['setting'];
            $pdf_stream = PDF::loadView('admin.invoice.delivery_note', $data)->setWarnings(false)->stream();

            $product_list = json_decode($order->cart);
            $billing = '<p>' . formatBillingAddress($order->billing) . '</p>';
            $shipping = '<p>' . formatBillingAddress($order->shipping) . '</p>';

            $logo = ($invoice_data->logo) ? $invoice_data->logo : asset("images/logo-dummy.png");
            $tags = [
                'customer_name' => $customer->full_name,
                'company_name' => $customer->company_name,
                'billing_address' => $billing,
                'shipping_address' => $shipping,
                'order_items' => view('admin.new_order.email_supplier_order_items', compact('product_list', 'order'))->render(),
                'order_items_n_article' => view('admin.new_order.email_supplier_order_items_n_article', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => $order->invoice_number,
                'contact_name' => $supplier_name,
                'pay_url' => null,
                'PAYWALL' => false,
                'IS_DROPSHIPPING'   => true,
                'IS_FULFILLMENT'    => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $data['channel'] = (int) $_REQUEST['channel'];

            $template = DRMParseSupplierEmailTemplate($tags, $order->cms_user_id, $data['channel']);
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];
            $data['subject'] = $template['subject'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            // $note_name = 'delivery_note_' . $supplier_name . '_' . $order->invoice_number;
            // $note_name = preg_replace('/\s+/', '_', $note_name);
            // $note_name = preg_replace('/[_]+/', '_', $note_name);
            $note_name = $order->id . '.pdf';

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_supplier', $template, function ($messages) use ($data, $pdf_stream, $note_name) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

                $messages->attachData($pdf_stream, $note_name, [
                    'mime' => 'application/pdf',
                ]);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }


    public function postEditableSave()
    {
        $id = $_REQUEST['pk'];
        $name = $_REQUEST['name'];
        $value = $_REQUEST['value'];

        if($name == "email"){
            if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                $validationErr = true;
                $responseInfo = [
                    'status' => "ERROR",
                    'message' => 'Please give valid email address !!!',
                ];
            }

        }

        // Validate Email and phone on API
        try {
            if($name === 'phone' && !empty($value))
            {
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($value);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => "ERROR",
                'message' => $e->getMessage(),
            ], 400);
        }

        if ($validationErr) {
            return response()->json($responseInfo, 400);
        }

        DB::table('delivery_companies')->where('id', $id)->update([$name => $value]);

        // Do accounting
        app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

        return response()->json([
            'status' => "SUCCESS",
            'code' => 200
        ]);
    }

    //Supplier log
    public function postSupplierLog()
    {
        try {

            $supplier_id = $_REQUEST['supplier_id'];
            $supplier_logs = SupplierLog::where('supplier_id', $supplier_id)->orderBy('created_at', 'desc')->get()->groupBy(function ($date) {
                return Carbon::parse($date->created_at)->format('d-M-y');
            });

            if ($supplier_logs) {
                $data['logs'] = $supplier_logs;
                return response()->json([
                    'success' => true,
                    'log' => view('admin.drm_delivery_companies.log.index', $data)->render(),
                ]);
            } else {
                throw new Exception('Supplier has no log data!');
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //Supplier log comment
    public function postSupplierLogComment()
    {
        try {
            $supplier_id = $_REQUEST['supplier_id'];
            $comment_text = $_REQUEST['comment_text'];

            $supplier = DeliveryCompany::where('id', $supplier_id)->first();
            if ($supplier) {
                $supplier->logs()->create([
                    'type' => 2,
                    'data' => [],
                    'message' => $comment_text,
                ]);

                $data['logs'] = SupplierLog::where('supplier_id', $supplier_id)->orderBy('created_at', 'desc')->get()->groupBy(function ($date) {
                    return Carbon::parse($date->created_at)->format('d-M-y');
                });
                return response()->json([
                    'success' => true,
                    'log' => view('admin.drm_delivery_companies.log.index', $data)->render(),
                ]);

            } else {
                throw new Exception('Invalid Supplier!');
            }

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //Supplier delivery note
    public function getSupplierDeliveryNote($log_id)
    {
        $supplier_log = SupplierLog::findOrFail($log_id);
        $log_data = $supplier_log->data;
        if (empty($log_data)) abort(404);

        $order_id = $log_data['order_id'];
        $product_list = $log_data['product_list'];

        $data['page_title'] = 'Delivery Note';
        $order = NewOrder::find($order_id);
        $data['order'] = $order;

        $products = json_encode($product_list);
        $product_obj = json_decode($products);

        $data['product_list'] = $product_obj;
        $data['customer'] = $order->customer;

        //Dropmatix carnot supplied replace with Wohlauf carnot supplier
        $supplier_id = (int) $supplier_id === 14383 ? 13040 : $order->supplier_id;
        $supplier = DeliveryCompany::find($supplier_id);
        $supplier_user_id = (int)$supplier_id === 13040 ? $supplier->user_id : $order->cms_user_id;

        if($order->credit_number && $order->cms_user_id == 2455) {
            $order->marketplace_order_ref = DB::table('new_orders')->where('credit_ref', '=', $order->id)->value('marketplace_order_ref');
        }

        if($order->marketplace_order_ref && $order->cms_user_id == 2455) {
          $data['shipping_details'] = DB::table("new_orders")
          ->join("new_customers", "new_customers.id", "=", "new_orders.drm_customer_id")
          ->select("new_orders.id as mp_order_id", "new_customers.*")
          ->where('new_orders.id', $order->marketplace_order_ref)->first();
        }

//      $data['setting'] = DB::table('delivery_note_settings')->where('cms_user_id', $supplier_user_id)->orderBy('id', 'desc')->first();
        $data['setting'] = DeliveryNote::DoesntHave('channels')
            ->DoesntHave('order')
            ->where('cms_user_id', $supplier_user_id)
            ->orderBy('id', 'desc')
            ->first();

        $pdf_path = 'storage/order_delivery_note/delivery_note' . $order->id . '.pdf';

        $pdf_view = 'admin.invoice.delivery_note';
        return \PDF::loadView($pdf_view, $data)->setWarnings(false)->stream();
    }

    //Supplier log action
    public function postSupplierLogAction()
    {
        try {
            $request = $_REQUEST;
            $validator = Validator::make($request, [
                'log_id' => 'required',
                'action' => ['required', Rule::in(['invoice', 'delivery_note', 'resend'])],
            ]);

            if ($validator->fails()) {
                throw new Exception($validator->errors()->first());
            }


            $log_id = $request['log_id'];
            $action = strtolower($request['action']);

            $supplier_log = SupplierLog::where('id', $log_id)->first();
            if (empty($supplier_log)) throw new Exception('Invalid action!');

            $log_data = $supplier_log->data;
            if (empty($log_data)) throw new Exception('Invalid log action!');

            $order_id = $log_data['order_id'];
            $product_list = $log_data['product_list'];
            $supplier_id = $supplier_log->supplier_id;

            if ($action == 'invoice') {
                $order = NewOrder::where('id', $order_id)->select('id', 'credit_number')->first();
                if ($order) {
                    $url = ($order->credit_number > 0) ? 'drm_all_orders/credit-note/' : 'drm_all_orders/detail/';
                    if ($url) {
                        $url = CRUDBooster::adminPath($url . $order->id);
                        return response()->json([
                            'success' => true,
                            'message' => '',
                            'url' => $url,
                        ]);
                    }
                }
            } elseif ($action == 'delivery_note') {
                return response()->json([
                    'success' => true,
                    'message' => '',
                    'url' => CRUDBooster::adminPath('delivery_companies/supplier-delivery-note/' . $log_id),
                ]);
            } elseif ($action == 'resend') {

                $logValidator = Validator::make($log_data, [
                    'order_id' => 'required',
                    'product_list' => 'required',
                ]);

                if ($logValidator->fails()) {
                    throw new Exception($logValidator->errors()->first());
                }

                $order = NewOrder::find($order_id);
                if (is_null($order)) throw new Exception('Order might be deleted or not found!');

                $products = json_encode($product_list);
                $product_obj = json_decode($products);


                $response = \App\Helper\OrderHelper::order_placed($order, 'Order übertragen', [
                    'supplier_id' => $supplier_id,
                    'product_list' => $product_obj,
                ]);

                if ($response) {
                    $user_id = $order->cms_user_id;
                    Cache::forget('order_statt_' . $user_id);
                    Cache::forget('best_8_products_' . $user_id);

                    if ($response['success']) {
                        return response()->json([
                            'success' => true,
                            'message' => $response['message'],
                            'url' => '',
                            'reload' => true
                        ]);
                    }

                    throw new Exception($response['message']);
                }

                throw new Exception('Order placed failed!');
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }


    // Callback Functions
    public function getMarketplaceStock($column_value)
    {
            if ( $column_value != null ) {
                return app(\App\Services\Marketplace\ProductService::class)->countProducts([
                    'supplier'      => $column_value,
                ]);
            } else {
                return '-';
            }
    }

    public function callbackForTotalSalesOld($column_value = null)
    {
        if ( $column_value != null ) {
            $sales_amount = 0;
            $orders = NewOrder::where('marketplace_paid_status',1)
                                ->where('test_order', '!=', 1)
                                ->where('invoice_number', '>', 0)
                                ->where('credit_number', 0)
                                ->where('cms_user_id', 2455)
                                ->whereNull('credit_ref')
                                ->get()
                                ->map(function($query)use($column_value){
                                    $sub_total =  0;
                                    foreach (json_decode($query->cart) as $cart){
                                        if(Product::where('delivery_company_id',$column_value)->where('id',$cart->marketplace_product_id)->exists()){
                                            $sub_total +=  $cart->amount + (($cart->amount * $cart->tax) / 100);
                                        }
                                    }

                                    if($sub_total > 0){
                                        return ($sub_total + $query->shipping_cost);
                                    }
                                });

            foreach ($orders as $amount){
                $sales_amount +=$amount;
            }

            return number_format($sales_amount, 2, ',', '.').formatCurrency();

//            $eur_total_sum = 0;
//
//            $orders_group = \App\NewOrder::select('currency', 'invoice_number', 'total', 'test_order', 'credit_number', 'eur_total');
//
//            $order_top_shop = \App\NewOrder::has('shop');
//            $order_top_shop->where(['supplier_id'=> $column_value, 'marketplace_paid_status'=>1]);
//
//            $order_top_shop = $order_top_shop->selectRaw(DB::raw('count(*) as order_count, shop_id'))->groupBy('shop_id')->orderBy('order_count', 'desc')->first();
//
//            if ($order_top_shop) {
//                $best_selling_shop_type = \App\Shop::where('id', $order_top_shop->shop_id)->first(['channel']);
//                $best_selling_shop = drm_shop_type_name($best_selling_shop_type->channel);
//                $best_selling_shop_item = $order_top_shop->order_count;
//            }
//
//            $orders_group->where(['supplier_id' => $column_value,'marketplace_paid_status'=>1]);
//
//            $orders_group = $orders_group->get()->groupBy('currency');
//
//            if ($orders_group->isNotEmpty()) {
//
//                foreach ($orders_group as $key => $orders) {
//                    $order_sum = $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->where('credit_number', 0)->sum("total");
//                    $total += $orders->count(); //whereNotIn('status',['Storniert','Canceled'])->
//                    $total_sum += $order_sum;
//
//                    //EUR converted value
//                    $eur_total_sum += $orders->where('test_order', '!=', 1)->where('invoice_number', '>', 0)->where('credit_number', 0)->sum("eur_total");
//
//                }
//            }
//
//            return $eur_total_sum;

            // return app(\App\Services\Marketplace\ProductService::class)->getDashboardProductTotal($column_value);
        } else {
            return '-';
        }
    }

    public function callbackForTotalSales($column_value = null)
    {
        if ($column_value === null) {
            return '-';
        }
    
        $orders = NewOrder::where('marketplace_paid_status', 1)
            ->where('test_order', '!=', 1)
            ->where('invoice_number', '>', 0)
            ->where('credit_number', 0)
            ->where('cms_user_id', 2455)
            ->whereNull('credit_ref')
            ->select('id', 'cart', 'shipping_cost')
            ->get();
    
        if ($orders->isEmpty()) {
            return '0,00' . formatCurrency();
        }
    
        $productIds = $orders->pluck('cart')
            ->flatMap(function ($cart) {
                return array_column(json_decode($cart, true) ?? [], 'marketplace_product_id');
            })
            ->filter()
            ->unique()
            ->values()
            ->toArray();
    
        $validProductIds = Product::where('delivery_company_id', $column_value)
            ->whereIn('id', $productIds)
            ->pluck('id')
            ->toArray();
    
        $salesAmount = $orders->sum(function ($order) use ($validProductIds) {
            $cartItems = json_decode($order->cart, true) ?? [];
    
            $subTotal = collect($cartItems)->sum(function ($cart) use ($validProductIds) {
                if (in_array($cart['marketplace_product_id'] ?? null, $validProductIds)) {
                    $amount = $cart['amount'] ?? 0;
                    $tax = $cart['tax'] ?? 0;
                    return $amount + (($amount * $tax) / 100);
                }
                return 0;
            });
    
            return $subTotal > 0 ? $subTotal + $order->shipping_cost : 0;
        });
    
        return number_format($salesAmount, 2, ',', '.') . formatCurrency();
    }

    public function callbackForCategory($column_value)
    {
        $deliveryCompany = \App\DeliveryCompany::find($column_value);

        // Determine if real supplier account or not
        if ( $deliveryCompany->is_marketplace_supplier && $deliveryCompany->supplier_id && !$deliveryCompany->category_id ) {
            $fulfillment  = \App\Models\Marketplace\Product::where('delivery_company_id', $deliveryCompany->id)->where('shipping_method', \App\Enums\Marketplace\ShippingMethod::FULFILLment)->orWhere('marketplace_product_id', '>', 0)
                                    ->count() > 0;
            $dropShipping = \App\Models\Marketplace\Product::where('delivery_company_id', $deliveryCompany->id)->where('shipping_method', \App\Enums\Marketplace\ShippingMethod::DROPSHIPPING)
                                    ->count() > 0;
            if ( $fulfillment && $dropShipping ) {
                return '<span id="inline_supplier_cat_i_'.$deliveryCompany->id.'">'.'Dropshipping & Fulfillment'.'  <a href="#" class="btn btn-xs btn-drm edit_inline_supplier_category__btn" title="Edit" data-id="'.$deliveryCompany->id.'" data-cat_id="'.$category->id.'"><i class="fa fa-pencil-square-o"></i></a></span>';
            } elseif ( $fulfillment ) {
                return '<span id="inline_supplier_cat_i_'.$deliveryCompany->id.'">'.'Fulfillment'.'  <a href="#" class="btn btn-xs btn-drm edit_inline_supplier_category__btn" title="Edit" data-id="'.$deliveryCompany->id.'" data-cat_id="'.$category->id.'"><i class="fa fa-pencil-square-o"></i></a></span>';
            } elseif ( $dropShipping ) {
                return '<span id="inline_supplier_cat_i_'.$deliveryCompany->id.'">'.'Dropshipping'.'  <a href="#" class="btn btn-xs btn-drm edit_inline_supplier_category__btn" title="Edit" data-id="'.$deliveryCompany->id.'" data-cat_id="'.$category->id.'"><i class="fa fa-pencil-square-o"></i></a></span>';
            } else {
                return '<span id="inline_supplier_cat_i_'.$deliveryCompany->id.'">'.$category->category_name.'  <a href="#" class="btn btn-xs btn-drm edit_inline_supplier_category__btn" title="Edit" data-id="'.$deliveryCompany->id.'" data-cat_id="'.$category->id.'"><i class="fa fa-pencil-square-o"></i></a></span>';
            }

        } else {
            $category = \App\DrmSupplierCategory::find($deliveryCompany->category_id, ['category_name', 'id']);
            return '<span id="inline_supplier_cat_i_'.$deliveryCompany->id.'">'.$category->category_name.'  <a href="#" class="btn btn-xs btn-drm edit_inline_supplier_category__btn" title="Edit" data-id="'.$deliveryCompany->id.'" data-cat_id="'.$category->id.'"><i class="fa fa-pencil-square-o"></i></a></span>';
        }
    }

    public function totalSales ($id)
    {
        return \App\DrmProduct::where('marketplace_supplier_id', $id)->count();
    }

    public function getMarketplaceStockOfSupplier($id)
    {
        $deliveryCompany = \App\DeliveryCompany::select('is_marketplace_supplier', 'supplier_id')->find($id);
    
        if (!$deliveryCompany) {
            return 0;
        }
    
        if ($deliveryCompany->is_marketplace_supplier && $deliveryCompany->supplier_id) {
            return \App\Models\Marketplace\Product::where('supplier_id', $deliveryCompany->supplier_id)->count();
        }
    
        return \App\Models\Marketplace\Product::where('delivery_company_id', $id)->count();

    }

    //load category
    public function postLoadCategory()
    {
        try {
            $pk = $_REQUEST['pk'];
            $category_id = $_REQUEST['category_id'];
            $categories = \App\DrmSupplierCategory::where('user_id', CRUDBooster::myParentId())->pluck('category_name', 'id')->toArray();

            return response()->json([
                'success' => true,
                'data' => $categories,
                'selected_id' => $category_id,
            ]);

        }catch(\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //Update supplier category
    public function postUpdateInlineCategory() {

        try {

            $category_id = $_REQUEST['category_id'];
            $supplier_id = $_REQUEST['supplier_id'];
            $user_id = CRUDBooster::myParentId();

            $category = \App\DrmSupplierCategory::where('user_id', $user_id)->where('id', $category_id)->select('category_name', 'id')->first();
            if(empty($category)) throw new \Exception('Invalid category!');


            $updated = DB::table('delivery_companies')
            ->where('user_id', CRUDBooster::myParentId())
            ->where('id', $supplier_id)
            ->update(['category_id' => $category_id]);


            $val = $category->category_name.'  <a href="#" class="btn btn-xs btn-drm edit_inline_supplier_category__btn" title="Edit" data-id="'.$supplier_id.'" data-cat_id="'.$category->id.'"><i class="fa fa-pencil-square-o"></i></a>';

            return response()->json(['update' => (bool)$updated, 'val' => $val], 200);

        }catch(\Exception $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        }
    }

    //Update supplier category add
    public function postUpdateInlineCategoryAdd() {

        try {

            $category_name = strip_tags($_REQUEST['category_name']);
            $category_name = trim($category_name);
            if(strlen($category_name) < 2) throw new \Exception('Category name too short!');

            $user_id = CRUDBooster::myParentId();

            $exists = DB::table('drm_supplier_categories')->where('user_id', $user_id)->where('category_name', $category_name)->exists();
            if($exists) throw new \Exception('Category already exists!');

            $category = \App\DrmSupplierCategory::create([
                'category_name' => $category_name,
                'user_id' => $user_id
            ]);

            if(!empty($category)) {
                return response()->json(['success' => true, 'data' => $category, 'message' => 'Category created successfully!'], 201);
            }

            throw new \Exception('Category not created!');

        }catch(\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }




    /*=============================================
    ========== Get Offer setting page =============
    ==============================================*/


    public function getDeliveryNoteTemplates()
    {
        if (CRUDBooster::isSubUser() && (!sub_account_can('invoice_setting') && !sub_account_can('all_modules', 122))) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $data['app_id'] = config('global.delivery_note_app_id');
        $authId = CRUDBooster::myParentId();
        $data['settings'] = DeliveryNote::where('cms_user_id', $authId)
            ->where(function($query){
                $query ->where('position', '>', 0);
                $query->orWhereNull('position');
            })
            ->orderBy('position', 'desc')
            ->get();
        $data['colors'] = $this->colors();
        $data['page_title'] = __('invoice.DELIVERY_NOTE_SETTING');
        return view("admin.drm_order.delivery_note_templates", $data);
    }


    public function getDeliveryNoteTemplateAdd(){
        if (CRUDBooster::isSubUser() && (!sub_account_can('invoice_setting') && !sub_account_can('all_modules', 122))) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $authId = CRUDBooster::myParentId();
        $note_id = $_REQUEST['id'];

        if(empty($note_id)){
            $delivery_note = DeliveryNote::where('cms_user_id', $authId)->exists();
            $app_id = config('global.delivery_note_app_id');
            if ($delivery_note && !checkUserPurchasedApp($app_id, $authId)) {
                CRUDBooster::redirect(CRUDBooster::adminPath('delivery_companies/delivery-note-templates'), trans("crudbooster.denied_access"));
            }
        }


        if(!empty($note_id)){
            $data['setting'] = DB::table('delivery_note_settings')->where('cms_user_id', $authId)->where('id', $note_id)->first();
        }
        $data['page_title'] = __('invoice.DELIVERY_NOTE_SETTING');
        $data['note_list'] = DB::table('delivery_note_settings')->where('cms_user_id', $authId)->orderBy('id', 'desc')->get();
        return view("admin.drm_order.add_delivery_note_template", $data);
    }

    public function postStoreAssignNote()
    {
        $request = $_REQUEST;
        $user_id = CRUDBooster::myParentId();
        $layout_id = $request['note_id'];
        try{
            if ($layout_id) {
                $this->channelAssign($user_id, $request['note_id'], $request['channel_types']);

                $all_assigned = DB::table('delivery_note_assign')->where('user_id', $user_id)
                    ->pluck('channel_type')->toArray();

                return response()->json([
                    'success' => true,
                    'channel_types' => $all_assigned,
                    'note_id' => $request['note_id'],
                    'message' => 'Delivery note assign successfully!'
                ]);

            }
        }catch (Exception $exception){
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong!'
            ]);
        }
    }


    public function getChannelList($id)
    {



        $user_id = CRUDBooster::myParentId();

        $note_ids = DeliveryNote::DoesntHave('channels')
            ->DoesntHave('order')
            ->where('cms_user_id', $user_id)
            ->pluck('id')
            ->toArray();

        if(count($note_ids) < 2 && in_array($id, $note_ids)){
            return response()->json([
                'success' => false,
                'note_id' => $id,
                'title' => __("Can't assign"),
                'message' => __('This template is used for default. Add more templates for use to the channels.')
            ]);
        }

        $channels = config('channel.list');
        $data['languageId'] = app('App\Services\UserService')->getProductCountry($user_id);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($data['languageId']);

        if (!CRUDBooster::isSuperadmin()) {
            $data['shop'] = app(ChannelProductService::class)->getUserShops($user_id, $data['lang']);
            $channels = array_values(collect($channels)->whereIn('type', $data['shop']->pluck('channel'))->toArray());
        }

        $assigned_channels = DB::table('delivery_note_assign')->where('note_id', '=', $id)
            ->pluck('channel_type')->toArray();

        $all_assigned = DB::table('delivery_note_assign')->where('user_id', $user_id)
            ->pluck('channel_type')->toArray();

        $html = view('admin.drm_order.assign_channel', compact('channels', 'assigned_channels', 'all_assigned','id'))->render();

        return response()->json([
            'success' => true,
            'note_id' => $id,
            'html' => $html
        ]);
    }

    public function postTemplateCopy()
    {
        $request = $_REQUEST;
        $template = DeliveryNote::find($request['id']);

        $newTemplate = $template->replicate();
        $newTemplate->note_name = $request['template_name'];
        $newTemplate->position = null;
        $newTemplate->save();

        // Insert log
        app(\App\Services\Order\Log\DeliveryNoteSettingLog::class)->insertLog($newTemplate->id, "Template copied from '{$template->note_name}'.");

        return response()->json([
            'success' => true,
            'message' => 'Template copied successfully!'
        ]);
    }

    public function getLayoutTemplatePreview($id)
    {
        $url = url('admin/delivery_companies/show-delivery-note-preview?id='.$id);
        $html ='<iframe style="height: 500px;" src="'.$url.'" title="Preview layout">
                                           </iframe>';
        return response()->json([
            'success' => true,
            'html' => $html
        ]);
    }

    public function getDeleteLayout($layout_id)
    {
        $user_id = CRUDBooster::myParentId();
        $isExists = DB::table('new_orders')->where([
            'cms_user_id' => $user_id,
            'delivery_layout_id' => $layout_id
        ])->exists();

        if ($isExists) {
            return response()->json([
                'success' => false,
                'title' => 'Sorry!',
                'message' => 'You can not delete the delivery note. This note already used for channel.'
            ]);
        }

        DeliveryNote::where('id', $layout_id)->delete();
        \App\DeliveryNoteSettingLog::where('delivery_note_id', $layout_id)->delete();

        return response()->json([
            'success' => true,
            'message' => 'Delivery note delete succesfully!'
        ]);
    }


    public function postSaveDeliveryNoteTemplate(\Illuminate\Http\Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'logo_width' => 'required|numeric',
                'logo_height' => 'required|numeric',
                // 'email' => 'required|email',
                'invoice_water_mark_pdf' => 'sometimes|mimes:jpg,bmp,png,jpeg,pdf',
                'shop_logo' => 'sometimes|image'
            ]);

            if ($validator->fails()) {
                throw new Exception($validator->errors()->first());
            }



            $row = [];
            $user_id = CRUDBooster::myParentId();
            $setting_id = $_REQUEST['setting_id'];
            $setting = DB::table('delivery_note_settings')->where('cms_user_id', '=', $user_id)->where('id', $setting_id)->first();
            $already_used = false;
            $channels = DeliveryNoteAssigned::where('note_id', $setting_id)->pluck('channel_type')->toArray();
            $position = DeliveryNote::orderBy('position', 'desc')->value('position') + 1 ?? 1;
            if($setting_id){
                $position = $setting->position;
                $already_used = $this->isDeliveryNoteTemplateAlreadyUsed($setting_id, $user_id, $channels);
            }

            $additional_error_message = '';
            $invoice_watermark_active_status = isset($_REQUEST['watermark_photo_active_status']) ? 1 : 0;


            //Background image
            if (request()->hasFile('invoice_water_mark_pdf')) {
                $watermark_file = request()->file('invoice_water_mark_pdf');
                $watermark_file_extention = $watermark_file->getClientOriginalExtension();

                $photo_ext = ['jpg', 'jpeg', 'png', 'bmp'];

                if ($watermark_file_extention == 'pdf') {

                    try {
                        $pdf_path = CRUDBooster::myParentId() . '_watermark_delivery_note.' . $watermark_file_extention;
                        $tmp_image_path = CRUDBooster::myParentId() . '_watermark_delivery_note.png';
                        $watermark_file->move(public_path('tmp'), $pdf_path);
                        $temp_pdf = public_path('tmp/' . $pdf_path);
                        $local_img_path = 'tmp/' . $tmp_image_path;

                        //Convert pdf to image
                        $pdf = new \Spatie\PdfToImage\Pdf($temp_pdf);
                        $pdf->setOutputFormat('png');
                        $pdf->saveImage(public_path($local_img_path));

                        //Local photo
                        $local_photo = File::get(public_path($local_img_path));
                        $new_upload_filename = 'delivery_note_watermark/' . 'user_id_' . CRUDBooster::myParentId(). '_'.time() . '.png';

                        // Delete existing watermark
                        if (isset($setting->watermark_photo)) {
                            if(!$already_used)
                            {
                                @app('App\Helper\OrderHelper')->delete_if_invoice_watermark_exists($setting, 'delivery_note_');
                            }

                            $row['watermark_photo'] = null;
                            $invoice_watermark_active_status = 0;
                        }

                        //upload image
                        Storage::disk('spaces')->put($new_upload_filename, $local_photo, 'public');

                        //delete tmp files
                        @File::delete(public_path($local_img_path));
                        @File::delete($temp_pdf);

                        if (Storage::disk('spaces')->exists($new_upload_filename)) {
                            $row['watermark_photo'] = Storage::disk('spaces')->url($new_upload_filename);
                            $invoice_watermark_active_status = 1;
                        }

                    } catch (Exception $ex) {
                        $additional_error_message = 'But watermark pdf not uploaded! ' . $ex->getMessage();
                    }

                } elseif (in_array($watermark_file_extention, $photo_ext)) {

                    $new_upload_filename = 'delivery_note_watermark/' . 'user_id_' . CRUDBooster::myParentId(). '_'.time() . '.' . $watermark_file_extention;

                    //Delete existing files
                    if (isset($setting->watermark_photo)) {
                        if(!$already_used)
                        {
                            app('App\Helper\OrderHelper')->delete_if_invoice_watermark_exists($setting, 'delivery_note_');
                        }
                        $row['watermark_photo'] = null;
                        $invoice_watermark_active_status = 0;
                    }

                    //Upload file
                    Storage::disk('spaces')->put($new_upload_filename, file_get_contents($watermark_file), 'public');

                    if (Storage::disk('spaces')->exists($new_upload_filename)) {
                        $row['watermark_photo'] = Storage::disk('spaces')->url($new_upload_filename);
                        $invoice_watermark_active_status = 1;
                    }
                }
            }


            $setting_logo = null;

            //Shop logo
            if (request()->hasFile('shop_logo')) {
                $logoFile = request()->file('shop_logo');
                $logoFileName = 'shop_logo/sl_delivery_note' . time() . '.' . $logoFile->getClientOriginalExtension();

                //If already has logo
                if (!$already_used && !empty($setting->logo)) {
                    $old_logo = $setting->logo;
                    $search = '.com/';
                    $file = substr($old_logo, strpos($old_logo, $search) + strlen($search));

                    if (Storage::disk('spaces')->exists($file)) {
                        Storage::disk('spaces')->delete($file);
                    }
                }

                //Upload logo
                Storage::disk('spaces')->put($logoFileName, file_get_contents($logoFile), 'public');
                if (Storage::disk('spaces')->exists($logoFileName)) {
                    $row['logo'] = $setting_logo = Storage::disk('spaces')->url($logoFileName);
                }
            }


            $row += [
                'logo_position' => $_REQUEST['logo_position'],
                // 'head_text' => $_REQUEST['head_text'],
                'store_name' => $_REQUEST['store_name'],
                'note_name' => $_REQUEST['note_name'],
//                'email' => $_REQUEST['email'],
                // 'start_invoice_number' => $_REQUEST['start_invoice_number'],
                // 'suffix' => $_REQUEST['suffix'],
                // 'start_from_1' => $_REQUEST['start_from_1'],
                // 'currency' => $_REQUEST['currency'],
                'company_address' => $_REQUEST['company_address'],
                'color' => $_REQUEST['color'],
                // 'current_month' => $_REQUEST['current_month'],
                // 'url' => $_REQUEST['url'],
                // 'note' => $_REQUEST['note'],
                // 'small_business' => $_REQUEST['small_business'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'col1_text' => $_REQUEST['col1_text'],
                'col2_text' => $_REQUEST['col2_text'],
                'col3_text' => $_REQUEST['col3_text'],
                'col4_text' => $_REQUEST['col4_text'],
                'position' => $position,

                'logo_width' => $_REQUEST['logo_width'],
                'logo_height' => $_REQUEST['logo_height'],
                'watermark_photo_active_status' => $invoice_watermark_active_status,
            ];

            if($already_used)
            {
                $this->insertDeliveryNoteTemplate($row, $user_id, $channels);
            }else {

                if($setting && $setting->id)
                {
                    //IF setting is not used -> Update setting
                    DB::table('delivery_note_settings')->where('id', '=', $setting->id)->update($row);

                    // Change log
                    usleep(10);
                    $newSetting = DB::table('delivery_note_settings')->where('id', '=', $setting->id)->first();
                    resolve(\App\Services\Order\Log\DeliveryNoteSettingLog::class)->createLog($setting, $newSetting);

                }else {
                    $this->insertDeliveryNoteTemplate($row, $user_id, $channels);
                }
            }

            //Clear account activity step
            \App\Services\CheckListProgress\Checklist::cache_key_clear(9, CRUDBooster::myParentId());

            $image_src = (isset($row['watermark_photo']) && $row['watermark_photo']) ? $row['watermark_photo'] : null;
            $user_id = CRUDBooster::myParentId();
            return response()->json([
                'success' => true,
                'message' => 'Saved success!',
                'image_src' => $image_src,
                'logo_src' => $setting_logo,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function colors(){
        return [
            '1' => '#9e9e9e',
            '2' => '#2196f3',
            '3' => '#ffc107',
            '4' => '#486c69',
            '5' => '#009688',
            '6' => '#00bcd4',
            '7' => '#f44336',
            '8' => '#cddc39',
            '9' => '#673ab7',
            '10' => '#4caf50',
            '11' => '#ff5722',
            '12' => '#607d8b',
            '13' => '#4caf50',
        ];
    }
    //Delivery note setting already used
    private function isDeliveryNoteTemplateAlreadyUsed($setting_id, $user_id, $channels)
    {

        if ($setting_id) {
            $orders = DB::table('new_orders')
                ->join('shops', 'shops.id', '=', 'new_orders.shop_id')
                ->where('new_orders.cms_user_id', $user_id)
                ->whereNull('new_orders.deleted_at')
                ->whereNull('new_orders.delivery_layout_id');
            if ($channels) {
                $orders = $orders->whereIn('shops.channel', $channels);
            }
            $orders->update([
                'new_orders.delivery_layout_id' => $setting_id,
            ]);

            $isExists = DB::table('new_orders')
                ->whereNull('deleted_at')
                ->where('cms_user_id', '=', $user_id)
                ->where('delivery_layout_id', '=', $setting_id)
                ->exists();

            if ($isExists) {
                DB::table('delivery_note_settings')->where('id', $setting_id)->update(['position' => 0]);
            }

            return $isExists;
        }

        return false;
    }

    private function insertDeliveryNoteTemplate($row, $user_id, $channels = [])
    {
        if(empty($row['watermark_photo']) && isset($_REQUEST['tmp_watermark']) && $_REQUEST['tmp_watermark'])
        {
            $row['watermark_photo'] = app('App\Helper\OrderHelper')->cloneSpacesUrl($_REQUEST['tmp_watermark']);
        }

        if(empty($row['logo']) && isset($_REQUEST['temp_logo']) && $_REQUEST['temp_logo'])
        {
            $row['logo'] = app('App\Helper\OrderHelper')->cloneSpacesUrl($_REQUEST['temp_logo']);
        }
        $row['cms_user_id'] = $user_id;
        $row['created_at'] = now();
        $row['updated_at'] = now();
        $setting_id = DB::table('delivery_note_settings')->insertGetId($row);
        if($channels){
            $this->channelAssign($user_id, $setting_id, $channels, true);
        }

        // Insert log
        app(\App\Services\Order\Log\DeliveryNoteSettingLog::class)->insertLog($setting_id, "Template created.");

        return $setting_id;
    }
    protected function channelAssign($user_id, $setting_id, $channels, $create = false){
        DB::table('delivery_note_assign')
            ->whereNotIn('channel_type', $channels)
            ->where([
                'note_id' => $setting_id,
                'user_id' => $user_id
            ])->delete();


        foreach ($channels as $channel){
            $used_layout_id = DB::table('delivery_note_assign')->where('channel_type', $channel)->value('note_id') ?? 0;
            DB::table('delivery_note_assign')->updateOrInsert(
                ['user_id' => $user_id,'channel_type' => $channel],
                [
                    'note_id' => $setting_id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
            if($create){
                $used_layout_id = DB::table('delivery_note_assign')->where('channel_type', $channel)->value('note_id') ?? 0;
            }
            $this->updateChannelLayoutID($user_id, $channel, $used_layout_id);
        }
    }

    protected function updateChannelLayoutID($user_id, $channel, $layout_id)
    {
        DB::table('new_orders')
            ->join('shops', 'shops.id', '=', 'new_orders.shop_id')
            ->where('new_orders.cms_user_id', $user_id)
            ->whereNull('new_orders.deleted_at')
            ->whereNull('new_orders.delivery_layout_id')
            ->where('shops.channel', $channel)
            ->update([
                'new_orders.delivery_layout_id' => $layout_id
            ]);
    }






    public function getDeliveryNoteSetting()
    {
        if (CRUDBooster::isSubUser() && (!sub_account_can('invoice_setting') && !sub_account_can('all_modules', 122))) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $data['setting'] =  DB::table('delivery_note_settings')->where('cms_user_id', '=', CRUDBooster::myParentId())->orderBy('id', 'desc')->first();
        $data['page_title'] = __('invoice.DELIVERY_NOTE_SETTING');
        return view("admin.drm_order.delivery_company", $data);
    }

    //Show offer preview
    public function getShowDeliveryNotePreview()
    {
        $note_id = $_REQUEST['id'];
        $data['page_title'] = __('order.DELIVERY_NOTE_SETTING');
        $order = app('\App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
        $data['order'] = $order;
        $data['product_list'] = json_decode($order->cart);
        $data['customer'] = $order->customer;
        $data['setting'] = DB::table('delivery_note_settings')->where('cms_user_id', $order->cms_user_id)->orderBy('id','desc')->first();
        if(!empty($note_id)){
            $data['setting'] = DB::table('delivery_note_settings')->where('cms_user_id', $order->cms_user_id)->where('id', $note_id)->first();
        }
        $pdf_view = 'admin.invoice.delivery_note';
        $pdf = \PDF::loadView($pdf_view, $data)->setWarnings(false);
        return $pdf->stream('delivery_note.pdf');
    }



    //Save offer setting
    public function postSaveDeliveryNoteSetting(\Illuminate\Http\Request $request)
    {
        try {

            $validator = Validator::make($request->all(), [
                'logo_width' => 'required|numeric',
                'logo_height' => 'required|numeric',
                // 'email' => 'required|email',
                'invoice_water_mark_pdf' => 'sometimes|mimes:jpg,bmp,png,jpeg,pdf',
                'shop_logo' => 'sometimes|image'
            ]);

            if ($validator->fails()) {
                throw new Exception($validator->errors()->first());
            }

            $row = [];
            $setting = DB::table('delivery_note_settings')->where('cms_user_id', '=', CRUDBooster::myParentId())->orderBy('id', 'desc')->first();
            $already_used = $this->isDeliveryNoteSettingAlreadyUsed($setting);
            $additional_error_message = '';
            $user_id = CRUDBooster::myParentId();
            $invoice_watermark_active_status = isset($_REQUEST['watermark_photo_active_status']) ? 1 : 0;


            //Background image
            if (request()->hasFile('invoice_water_mark_pdf')) {
                $watermark_file = request()->file('invoice_water_mark_pdf');
                $watermark_file_extention = $watermark_file->getClientOriginalExtension();

                $photo_ext = ['jpg', 'jpeg', 'png', 'bmp'];

                if ($watermark_file_extention == 'pdf') {

                    try {
                        $pdf_path = CRUDBooster::myParentId() . '_watermark_delivery_note.' . $watermark_file_extention;
                        $tmp_image_path = CRUDBooster::myParentId() . '_watermark_delivery_note.png';
                        $watermark_file->move(public_path('tmp'), $pdf_path);
                        $temp_pdf = public_path('tmp/' . $pdf_path);
                        $local_img_path = 'tmp/' . $tmp_image_path;

                        //Convert pdf to image
                        $pdf = new \Spatie\PdfToImage\Pdf($temp_pdf);
                        $pdf->setOutputFormat('png');
                        $pdf->saveImage(public_path($local_img_path));

                        //Local photo
                        $local_photo = File::get(public_path($local_img_path));
                        $new_upload_filename = 'delivery_note_watermark/' . 'user_id_' . CRUDBooster::myParentId(). '_'.time() . '.png';

                        // Delete existing watermark
                        if (isset($setting->watermark_photo)) {
                            if(!$already_used)
                            {
                                @app('App\Helper\OrderHelper')->delete_if_invoice_watermark_exists($setting, 'delivery_note_');
                            }

                            $row['watermark_photo'] = null;
                            $invoice_watermark_active_status = 0;
                        }

                        //upload image
                        Storage::disk('spaces')->put($new_upload_filename, $local_photo, 'public');

                        //delete tmp files
                        @File::delete(public_path($local_img_path));
                        @File::delete($temp_pdf);

                        if (Storage::disk('spaces')->exists($new_upload_filename)) {
                            $row['watermark_photo'] = Storage::disk('spaces')->url($new_upload_filename);
                            $invoice_watermark_active_status = 1;
                        }

                    } catch (Exception $ex) {
                        $additional_error_message = 'But watermark pdf not uploaded! ' . $ex->getMessage();
                    }

                } elseif (in_array($watermark_file_extention, $photo_ext)) {

                    $new_upload_filename = 'delivery_note_watermark/' . 'user_id_' . CRUDBooster::myParentId(). '_'.time() . '.' . $watermark_file_extention;

                    //Delete existing files
                    if (isset($setting->watermark_photo)) {
                        if(!$already_used)
                        {
                            app('App\Helper\OrderHelper')->delete_if_invoice_watermark_exists($setting, 'delivery_note_');
                        }
                        $row['watermark_photo'] = null;
                        $invoice_watermark_active_status = 0;
                    }

                    //Upload file
                    Storage::disk('spaces')->put($new_upload_filename, file_get_contents($watermark_file), 'public');

                    if (Storage::disk('spaces')->exists($new_upload_filename)) {
                        $row['watermark_photo'] = Storage::disk('spaces')->url($new_upload_filename);
                        $invoice_watermark_active_status = 1;
                    }
                }
            }


            $setting_logo = null;

            //Shop logo
            if (request()->hasFile('shop_logo')) {
                $logoFile = request()->file('shop_logo');
                $logoFileName = 'shop_logo/sl_delivery_note' . time() . '.' . $logoFile->getClientOriginalExtension();

                //If already has logo
                if (!$already_used && !empty($setting->logo)) {
                    $old_logo = $setting->logo;
                    $search = '.com/';
                    $file = substr($old_logo, strpos($old_logo, $search) + strlen($search));

                    if (Storage::disk('spaces')->exists($file)) {
                        Storage::disk('spaces')->delete($file);
                    }
                }

                //Upload logo
                Storage::disk('spaces')->put($logoFileName, file_get_contents($logoFile), 'public');
                if (Storage::disk('spaces')->exists($logoFileName)) {
                    $row['logo'] = $setting_logo = Storage::disk('spaces')->url($logoFileName);
                }
            }


            $row += [
                'logo_position' => $_REQUEST['logo_position'],
                // 'head_text' => $_REQUEST['head_text'],
                'store_name' => $_REQUEST['store_name'],
//                'email' => $_REQUEST['email'],
                // 'start_invoice_number' => $_REQUEST['start_invoice_number'],
                // 'suffix' => $_REQUEST['suffix'],
                // 'start_from_1' => $_REQUEST['start_from_1'],
                // 'currency' => $_REQUEST['currency'],
                'company_address' => $_REQUEST['company_address'],
                'color' => $_REQUEST['color'],
                // 'current_month' => $_REQUEST['current_month'],
                // 'url' => $_REQUEST['url'],
                // 'note' => $_REQUEST['note'],
                // 'small_business' => $_REQUEST['small_business'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'col1_text' => $_REQUEST['col1_text'],
                'col2_text' => $_REQUEST['col2_text'],
                'col3_text' => $_REQUEST['col3_text'],
                'col4_text' => $_REQUEST['col4_text'],

                'logo_width' => $_REQUEST['logo_width'],
                'logo_height' => $_REQUEST['logo_height'],
                'watermark_photo_active_status' => $invoice_watermark_active_status,
            ];

            //Update item or create new
            if($already_used)
            {
                $this->insertDeliveryNoteSetting($row, $user_id);
            }else {

                if($setting && $setting->id)
                {
                    //IF setting is not used -> Update setting
                    DB::table('delivery_note_settings')->where('id', '=', $setting->id)->update($row);

                    // Change log
                    usleep(10);
                    $newSetting = DB::table('delivery_note_settings')->where('id', '=', $setting->id)->first();
                    resolve(\App\Services\Order\Log\DeliveryNoteSettingLog::class)->createLog($setting, $newSetting);

                }else {
                    $this->insertDeliveryNoteSetting($row, $user_id);
                }
            }

            //Clear account activity step
            \App\Services\CheckListProgress\Checklist::cache_key_clear(9, CRUDBooster::myParentId());

            $image_src = (isset($row['watermark_photo']) && $row['watermark_photo']) ? $row['watermark_photo'] : null;
            $user_id = CRUDBooster::myParentId();
            return response()->json([
                'success' => true,
                'message' => 'Saved success!',
                'image_src' => $image_src,
                'logo_src' => $setting_logo,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }


    //insert delivery note setting
    private function insertDeliveryNoteSetting($row, $user_id)
    {
        if(empty($row['watermark_photo']) && isset($_REQUEST['tmp_watermark']) && $_REQUEST['tmp_watermark'])
        {
            $row['watermark_photo'] = app('App\Helper\OrderHelper')->cloneSpacesUrl($_REQUEST['tmp_watermark']);
        }

        if(empty($row['logo']) && isset($_REQUEST['temp_logo']) && $_REQUEST['temp_logo'])
        {
            $row['logo'] = app('App\Helper\OrderHelper')->cloneSpacesUrl($_REQUEST['temp_logo']);
        }
        $row['cms_user_id'] = $user_id;
        $row['created_at'] = now();
        $row['updated_at'] = now();
        $inserted = DB::table('delivery_note_settings')->insert($row);

        if($inserted)
        {
            // Change log
            usleep(10);
            $setting_id = DB::table('delivery_note_settings')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->value('id');
            app(\App\Services\Order\Log\DeliveryNoteSettingLog::class)->insertLog($setting_id, "Template created.");
        }

        return $inserted;
    }



    //Delivery note setting already used
    private function isDeliveryNoteSettingAlreadyUsed($setting)
    {
        if($setting && $setting->id)
        {
            //Update existing orders layout
            DB::table('new_orders')
            ->where('cms_user_id', '=', $setting->cms_user_id)
            ->whereNull('delivery_layout_id')
            ->whereNull('deleted_at')
            ->update(['delivery_layout_id' => $setting->id]);
            usleep(1000);

            return DB::table('new_orders')
                ->whereNull('deleted_at')
                ->where('cms_user_id', '=', $setting->cms_user_id)
                ->where('delivery_layout_id', '=', $setting->id)
                ->exists();
        }

        return false;
    }


    //Delete shop logo
    public function postDeleteDeliveryNoteShopLogo()
    {
        $request = $_REQUEST;
        $setting_id = $request['id'];
        $user_id = CRUDBooster::myParentId();
        try {
            $setting = DB::table('delivery_note_settings')->where('cms_user_id', CRUDBooster::myParentId())
                ->where('id', $request['id'])
                ->orderBy('id', 'desc')
                ->first();
            if (empty($setting) || is_null($setting)) throw new Exception('Invalid action!');

            $old_logo = $setting->logo;
            if (empty($old_logo)) throw new Exception('Invalid action!');
//            $already_used = $this->isDeliveryNoteSettingAlreadyUsed($setting);

            $channels = DeliveryNoteAssigned::where('note_id', $setting_id)->pluck('channel_type')->toArray();
            if($setting_id){
                $already_used = $this->isDeliveryNoteTemplateAlreadyUsed($setting_id, $user_id, $channels);
            }

            if($already_used)
            {
                $new_setting = collect($setting)->except(['id', 'cms_user_id', 'logo'])->toArray();
                $this->insertDeliveryNoteTemplate($new_setting, $user_id, $channels);
                    return response()->json([
                        'success' => true,
                        'message' => 'Logo deleted successfully!'
                    ]);

            }else {

                $search = '.com/';
                $file = substr($old_logo, strpos($old_logo, $search) + strlen($search));
                if (Storage::disk('spaces')->exists($file)) {
                    Storage::disk('spaces')->delete($file);
                }

                if (DB::table('delivery_note_settings')->where('id', $setting_id)->update(['logo' => null]))
                {
                    app(\App\Services\Order\Log\DeliveryNoteSettingLog::class)->insertLog($setting_id, "Logo deleted successfully!");
                    return response()->json([
                        'success' => true,
                        'message' => 'Logo deleted successfully!'
                    ]);
                }
            }

            throw new Exception('Logo delete failed');
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //Delete offer watermark
    public function getDeleteDeliveryNoteWaterMark()
    {
        try {
            $request = $_REQUEST;
            $setting_id = $request['id'];
            $user_id = CRUDBooster::myParentId();
            $data = null;
            $setting = DB::table('delivery_note_settings')->where('cms_user_id', $user_id)
                ->where('id', $request['id'])
                ->orderBy('id', 'desc')
                ->first();

            if($setting && $setting->id)
            {
//                $already_used = $this->isDeliveryNoteSettingAlreadyUsed($setting);
                $channels = DeliveryNoteAssigned::where('note_id', $setting_id)->pluck('channel_type')->toArray();
                if($setting_id){
                    $already_used = $this->isDeliveryNoteTemplateAlreadyUsed($setting_id, $user_id, $channels);
                }
                if($already_used)
                {
                    $new_setting = collect($setting)->except(['id', 'cms_user_id'])->toArray();
                    $new_setting['watermark_photo'] = '';
                    $new_setting['watermark_photo_active_status'] = 0;
                    $data = $this->insertDeliveryNoteTemplate($new_setting, $user_id, $channels);
                }else {
                    $data = DB::table('delivery_note_settings')
                    ->where('id', '=', $setting_id)
                    ->update([
                        'watermark_photo' => '',
                        'watermark_photo_active_status' => 0
                    ]);
                    app(\App\Services\Order\Log\DeliveryNoteSettingLog::class)->insertLog($setting_id, "Watermark removed!");
                }
            }

            if (is_null($data)) throw new Exception("Something went wrong!");
            return response()->json([
                'success' => true,
                'message' => 'Delete success!',
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function postDoneImport()
    {
        $this->cbLoader();
        $data['page_menu'] = Route::getCurrentRoute()->getActionName();
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => $module->name]);
        Session::put('select_column', Request::get('select_column'));

        return view('admin.drm_delivery_companies.import', $data);
    }




    public function getDownloadDeliveryNoteLog($id)
    {
        if(!(DB::table('delivery_note_settings')->where('cms_user_id', CRUDBooster::myParentId())->where('id', $id)->exists())) abort(404);
        $logs = '';
        \App\DeliveryNoteSettingLog::where('delivery_note_id', $id)
        ->orderBy('id', 'desc')
        ->get()
        ->each(function($item) use (&$logs) {
            $log = strip_tags($item->log);
            $logs .= "{$item->log}\r\nTime: {$item->created_at}\r\nUser: {$item->change_by}\r\n\n\n";
        });

        $headers = [
            'Content-type'        => 'text/plain',
            'Content-Disposition' => 'attachment; filename="Delivery note setting logs.txt"',
        ];

        return \Response::make($logs, 200, $headers);
    }

}
