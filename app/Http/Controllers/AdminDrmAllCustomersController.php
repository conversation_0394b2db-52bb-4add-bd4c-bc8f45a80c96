<?php

namespace App\Http\Controllers;

use App\CampainTag;
use App\ContactFormMessagesHistory;
use App\Traker;
use Illuminate\Support\Facades\Session;
use Request;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Validator;
use crocodicstudio\crudbooster\helpers\CB;
use App\NewCustomer;
use App\NewOrder;
use App\CustomerTag;
use App\DrmProduct;
use App\DropfunnelCustomerTag;
use App\DropfunnelTag;
use App\EmailMarketing;
use App\User;
use App\DrmUserLockUnlock;
use App\Shop;
use Illuminate\Support\Str;
use Illuminate\Support\LazyCollection;
use Illuminate\Support\Facades\Cache;
use AppStore;
use App\Notifications\DRMNotification;
use Carbon\Carbon;
use App\Traits\ProjectShare;
use GuzzleHttp\Client;
use PhpOffice\PhpSpreadsheet\IOFactory;

use App\Http\Resources\CustomerTagResource;
use Illuminate\Support\Facades\Route;
use App\Jobs\CustomerDataExportJob;
use App\Jobs\CustomerBulkAddTagJob;
use App\Jobs\CustomerBulkRemoveTagJob;
use App\Jobs\CustomerMergeJob;
use Exception;
use App\Jobs\DtUserLockUnlockJob;
use App\Enums\Channel;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request as LaravelRequest;

class AdminDrmAllCustomersController extends \crocodicstudio\crudbooster\controllers\CBController
{
    use ProjectShare;

    //Columns data
    private function colData()
    {
        $col = [];
        $col[] = ["label" => __('customer.CUSTOMER_ID'), "name" => "id", 'field_with' => 'id'];
        $col[] = ["label" => __('customer.NAME'), "name" => "full_name", 'field_with' => 'full_name'];
        $col[] = ["label" => 'OptIn', "name" => "status", 'field_with' => 'status'];
        $col[] = ["label" => __('Source'), "name" => "source", 'field_with' => 'source'];
        $col[] = ["label" => __('Registration'), "name" => "created_at", 'field_with' => 'created_at'];
        $col[] = ["label" => __('Confirmation'), "name" => "verified_date", 'field_with' => 'verified_date'];
        $col[] = ["label" => __('customer.COMPANYNAME_customer'), "name" => "company_name", 'field_with' => 'company_name'];
        $col[] = ["label" => wordwrap(__('customer.VATNUMBER'), 10, '<br />'), "name" => "vat_number", 'field_with' => 'vat_number', 'width' => 50];
        $col[] = ["label" => __('customer.PHONE'), "name" => "phone", 'field_with' => 'phone'];
        $col[] = ["label" => __('customer.EMAIL'), "name" => "email", 'field_with' => 'email'];
        $col[] = ["label" => __('customer.CURRENCY'), "name" => "currency", 'field_with' => 'currency'];
        $col[] = ["label" => __('customer.STREET'), "name" => "address", 'field_with' => 'address'];
        $col[] = ["label" => __('customer.CITY'), "name" => "city", 'field_with' => 'city', 'width' => 50];
        $col[] = ["label" => __('customer.COUNTRY'), "name" => "country", 'field_with' => 'country'];

        $col[] = ["label" => __('Revenue'), "name" => "revenue", 'field_with' => 'revenue'];
        $col[] = ["label" => 'TAG', "name" => "cus_type", 'field_with' => 'cus_type'];

        if (CRUDBooster::isSuperadmin()) {
            $col[] = ["label" => __('Status'), "name" => "status", 'field_with' => 'status'];
        }

        return $col;
    }

    public function cbInit()
    {

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = false;
        $this->button_action_style = "button_icon";
        $this->button_add = false;
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = false;
        $this->button_import = false;

        $this->button_export = false;
        $this->table = "new_customers";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        // $this->col[] = ["label" => __('customer.CUSTOMER_ID'), "name" => "id"];
        // $this->col[] = ["label" => __('customer.NAME'), "name" => "full_name"];
        // $this->col[] = ["label" => __('customer.COMPANYNAME_customer'), "name" => "company_name"];
        // $this->col[] = ["label" => __('customer.VATNUMBER'), "name" => "vat_number"];
        // $this->col[] = ["label" => __('customer.PHONE'), "name" => "phone"];
        // $this->col[] = ["label" => __('customer.EMAIL'), "name" => "email"];
        // $this->col[] = ["label" => __('customer.CURRENCY'), "name" => "currency"];
        // $this->col[] = ["label" => __('customer.STREET'), "name" => "address"];
        // $this->col[] = ["label" => __('customer.CITY'), "name" => "city"];
        // $this->col[] = ["label" => __('customer.COUNTRY'), "name" => "country"];
        // if (isLocal())
        //     $this->col[] = ["label" => 'TAG', "name" => "insert_type"];
        // if (CRUDBooster::isSuperadmin()) {
        //     $this->col[] = ["label" => "Payment Status", "name" => "status"];
        // }
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];

        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ["label"=>"Full Name","name"=>"full_name","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
        //$this->form[] = ["label"=>"Company Name","name"=>"company_name","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Vat Number","name"=>"vat_number","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Phone","name"=>"phone","type"=>"number","required"=>TRUE,"validation"=>"required|numeric","placeholder"=>"You can only enter the number only"];
        //$this->form[] = ["label"=>"Website","name"=>"website","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Email","name"=>"email","type"=>"email","required"=>TRUE,"validation"=>"required|min:1|max:255|email|unique:new_customers","placeholder"=>"Please enter a valid email address"];
        //$this->form[] = ["label"=>"Currency","name"=>"currency","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Default Language","name"=>"default_language","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Address","name"=>"address","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"City","name"=>"city","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"State","name"=>"state","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Zip Code","name"=>"zip_code","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Country","name"=>"country","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Note","name"=>"note","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Shipping","name"=>"shipping","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Billing","name"=>"billing","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Insert Type","name"=>"insert_type","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
        //$this->form[] = ["label"=>"Status","name"=>"status","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();

        $lead_data = $this->statisticsLeadData();

        $total_leads = $lead_data['leads'] ?? 0;
        $customer = $lead_data['customers'] ?? 0;
        $total_customer = $lead_data['total_customer'] ?? 0;

        $this->index_statistic[] = ['label' => 'Total', 'count' => $total_customer, 'img_icon' => asset('images/icons/customer.svg'), 'icon' => 'fa fa-google-wallet', 'color' => 'order-stat new_order_header_stat customer_head_filter ' . $this->activeBox('all_customer'), 'width' => 'col-md-2', 'url' => CRUDBooster::mainpath('?filter=all_customer')];
        $this->index_statistic[] = ['label' => 'Leads', 'img_icon' => asset('images/customer_module/leads.png'), 'count' => $total_leads, 'icon' => 'fa fa-eur', 'color' => 'order-stat new_order_header_stat customer_head_filter ' . $this->activeBox('leads'), 'width' => 'col-md-2', 'url' => CRUDBooster::mainpath('?filter=leads')];
        $this->index_statistic[] = ['label' => __('Customers_'), 'count' => $customer, 'img_icon' => asset('images/customer_module/customers.png'), 'icon' => 'fa fa-eur', 'tax_summery' => true, 'color' => 'order-stat new_order_header_stat customer_head_filter ' . $this->activeBox('customers'), 'width' => 'col-md-2', 'url' => CRUDBooster::mainpath('?filter=customers')];



        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }

    //Active class
    private function activeBox($filter)
    {
        return (isset($_GET['filter']) && $_GET['filter'] === $filter) ? 'active ' : '';
    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        if (!CRUDBooster::isSuperadmin()) {
            $query->where('new_customers.user_id', CRUDBooster::myParentId());
        }else{
            $is_key_account = CRUDBooster::isKeyAccount();
            $key_users = [];
            if($is_key_account){
                $key_users = \DRM::keyAccountUsersId(CRUDBooster::myId());
                $key_users[] = CRUDBooster::myId();
                $query->whereIntegerInRaw('new_customers.user_id', $key_users);
            }
        }

        //Custom Searching If user search by tag using #
        if (Request::get('q')) {
            $searchText = Request::get('q');
            $tagSearchChecker = Str::contains($searchText, '#');
            $filteredSearchText = str_replace('#', '', $searchText);

            if ($tagSearchChecker) { //if string contains '#' search with tag will be executed

                $tags = DropfunnelCustomerTag::whereHas('tag', function ($tag) use ($filteredSearchText) {
                    $tag->where('tag', 'LIKE', '%' . $filteredSearchText . '%');

                    if (!CRUDBooster::isSuperadmin()) {
                        $tag->where('user_id', CRUDBooster::myParentId());
                    }
                })->pluck('customer_id')->toArray();

                $query->whereIntegerInRaw('new_customers.id', $tags);
            } else {
                $query->where(function ($q) use ($searchText) {
                    $q->where('new_customers.id', $searchText)
                        ->orWhere('new_customers.full_name', 'LIKE', '%' . $searchText . '%')
                        ->orWhere('new_customers.company_name', 'LIKE', '%' . $searchText . '%')
                        ->orWhere('new_customers.vat_number', 'LIKE', '%' . $searchText . '%')
                        ->orWhere('new_customers.phone', 'LIKE', '%' . $searchText . '%')
                        ->orWhere('new_customers.email', 'LIKE', '%' . $searchText . '%')
                        ->orWhere('new_customers.currency', 'LIKE', '%' . $searchText . '%')
                        ->orWhere('new_customers.address', 'LIKE', '%' . $searchText . '%')
                        ->orWhere('new_customers.city', 'LIKE', '%' . $searchText . '%')
                        ->orWhere('new_customers.country', 'LIKE', '%' . $searchText . '%');
                });
            }
        }

        //filter by leads
        if (Request::get('filter')) {
            $filter_type = Request::get('filter');
            if (!empty($filter_type)) {
                if ($filter_type === 'customers') {
                    $query->whereHas('orders', function($q){
                        $q->where('new_orders.invoice_number', '!=', -1)
                        ->where('new_orders.test_order', '!=', 1)
                        ->where('new_orders.credit_number', '=', 0)
                        ->whereNull('new_orders.credit_ref')
                        ->where('new_orders.eur_total', '>', 0);
                    });

                } elseif ($filter_type === 'leads') {
                    $query->whereDoesntHave('orders', function($q){
                        $q->where('new_orders.invoice_number', '!=', -1)
                        ->where('new_orders.test_order', '!=', 1)
                        ->where('new_orders.credit_number', '=', 0)
                        ->whereNull('new_orders.credit_ref')
                        ->where('new_orders.eur_total', '>', 0);
                    });
                }
            }
        }

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {

    }

    public function getDelete($id)
    {
        $this->cbLoader();
        $url = g('return_url') ?: CRUDBooster::referer();


        if (!DB::table('new_customers')->where('user_id', CRUDBooster::myParentId())->where('id', '=', $id)->exists()) {
            CRUDBooster::redirect($url, 'Premission denied!', 'warning');
        }

        $customer_has_orders = NewOrder::where('drm_customer_id', $id)->count();
        if ($customer_has_orders) {
            CRUDBooster::redirect($url, 'This customer can not be deleted. Customer has total ' . $customer_has_orders . ' order.', 'warning');
        } else {

            $this->hook_before_delete($id);
            DB::table('new_customers')->where('id', '=', $id)->delete();
            $this->hook_after_delete($id);
            CRUDBooster::redirect($url, trans("crudbooster.alert_delete_data_success"), 'success');
        }
    }


    public function postActionSelected()
    {

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //droptienda customer sync history
        /*DroptiendaSyncHistory::create([
            'sync_type' => DroptiendaSyncType::CUSTOMER,
            'sync_event' => DroptiendaSyncEvent::DELETE,
            'model_id' => $id,
        ]);*/

    }


    private array $extensions = [
        'xls' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'csv' => 'text/csv',
    ];

    // Export data
    public function getExportFile($ext = 'csv')
    {
        if(!in_array($ext, ['csv', 'xls'])) abort(404);
        $filename = 'customers-'.date('Y-m-d-His').'.'.$ext;

        $rows = DB::table('new_customers')->where('user_id', CRUDBooster::myParentId())
            ->select('full_name', 'email', 'address', 'zip_code', 'city', 'country', 'phone', 'created_at')
            ->orderBy('full_name', 'asc')
            ->get();

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $this->setRow(1, [
                'email' => 'Email',
                'name' => 'Name', 
                'address' => 'Street',
                'zip_code' => 'Zip code',
                'city' => 'City',
                'country' => 'Country',
                'phone' => 'Phone',
                'created_at' => 'Created At'
            ], $sheet);

        $i = 2;

        if ($ext === 'xls') {
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        } else {
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Csv($spreadsheet);
        }

        return new \Symfony\Component\HttpFoundation\StreamedResponse(
            function () use ($writer, $rows, &$i, &$sheet) {
                foreach ($rows as $index => $info) {
                    $this->setRow($i, [
                        'email' => $info->email,
                        'name' => $info->full_name, 
                        'address' => $info->address,
                        'zip_code' => $info->zip_code,
                        'city' => $info->city,
                        'country' => $info->country,
                        'phone' => $info->phone,
                        'created_at' => $info->created_at
                    ], $sheet);
                    $i++;
                }
                $writer->save('php://output');
            },
            200,
            [
                'Content-Type' => $this->extensions[$ext],
                'Content-Disposition' => 'attachment;filename="'.$filename.'"',
                'Cache-Control' => 'no-cache',
            ]
        );
    }

    // Set rows
    private function setRow($i, array $row, &$sheet)
    {
        $sheet->setCellValue('A'.$i, $row['email']);
        $sheet->setCellValue('B'.$i, $row['name']);
        $sheet->setCellValue('C'.$i, $row['address']);
        $sheet->setCellValue('D'.$i, $row['zip_code']);
        $sheet->setCellValue('E'.$i, $row['city']);
        $sheet->setCellValue('F'.$i, $row['country']);
        $sheet->setCellValue('G'.$i, $row['phone']);
        $sheet->setCellValue('H'.$i, $row['created_at']);
    }


    //Customer lead data
    private function statisticsLeadData()
    {

        $is_key_account = CRUDBooster::isKeyAccount();
        $key_users = [];
        if($is_key_account){
            $key_users = \DRM::keyAccountUsersId(CRUDBooster::myId());
            $key_users[] = CRUDBooster::myId();
        }

        // $customers = NewCustomer::join('new_orders', 'new_orders.drm_customer_id', '=', 'new_customers.id');
        $customers = NewCustomer::whereHas('orders', function($q){
            $q->where('new_orders.invoice_number', '!=', -1)
            ->where('new_orders.test_order', '!=', 1)
            ->where('new_orders.credit_number', '=', 0)
            ->whereNull('new_orders.credit_ref')
            ->where('new_orders.eur_total', '>', 0);
        });

            if (!CRUDBooster::isSuperadmin()) {
                $customers->where('user_id', CRUDBooster::myParentId());
            }elseif(!empty($key_users)){
               $customers->whereIntegerInRaw('user_id', $key_users);
            }
        $customer_count = $customers->count();


        $total_customer =  NewCustomer::query();
            if (!CRUDBooster::isSuperadmin()) {
                $total_customer->where('user_id', CRUDBooster::myParentId());
            }elseif(!empty($key_users)){
               $total_customer->whereIntegerInRaw('user_id', $key_users);
            }

        $total_customer = $total_customer->count();

        $leads = $total_customer - $customer_count;

        return [
            'total_customer' => $total_customer,
            'leads' => $leads,
            'customers' => $customer_count
        ];
    }

    /*======================================
    =============== Import Data ============
    =======================================*/
    public function getImportData()
    {
        if(!CRUDBooster::isSupplier()){
            redirectToV2('/customers/import');
        }

        $this->cbLoader();
        $data['page_menu'] = \Route::getCurrentRoute()->getActionName();
        $data['page_title'] = __('customer.Import_tittle') . $module->name;

        if (Request::get('file') && !Request::get('import')) {
            $file = base64_decode(Request::get('file'));
            $file = storage_path('app/' . $file);
            // $rows = Excel::load($file, function ($reader) {
            // })->get();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $rows = $this->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;
            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }
            $table_columns = ['full_name', 'company_name', 'vat_number', 'phone', 'website', 'email', 'currency', 'default_language', 'address', 'city', 'state', 'zip_code', 'country', 'note', 'status', 'customer_tags'];
            $labels = array_map(function ($str) {
                return ucwords(str_replace("_", " ", $str));
            }, $table_columns);
            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }
        return view('admin.new_customer.import', $data);
    }

    public function postDoneImport()
    {
        if(!CRUDBooster::isSupplier()){
            redirectToV2('/customers/import');
        }

        $this->cbLoader();
        $data['page_menu'] = Route::getCurrentRoute()->getActionName();
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => $module->name]);
        Session::put('select_column', Request::get('select_column'));

        return view('admin.new_customer.import', $data);
    }

    public function postDoImportChunk()
    {
        $this->cbLoader();
        $file_md5 = md5(Request::get('file'));

        if (Request::get('file') && Request::get('resume') == 1) {
            $total = Session::get('total_data_import');
            $prog = intval(Cache::get('success_' . $file_md5)) / $total * 100;
            $prog = round($prog, 2);
            if ($prog >= 100) {
                Cache::forget('success_' . $file_md5);
            }

            return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5), 'inserted' => Cache::get('insert_success_' . $file_md5)]);
        }

        $count = 0;
        $data_inserted = 0;

        try {
            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);
            $table_columns = ['full_name', 'company_name', 'vat_number', 'phone', 'website', 'email', 'currency', 'default_language', 'address', 'city', 'state', 'zip_code', 'country', 'note', 'status', 'customer_tags'];

            $file = base64_decode(Request::get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);
            $rows = $this->csvToArray($file, $type, 'auto', false);

            $data_import_column = [];
            foreach ($rows as $value) {
                $count++;
                Cache::put('success_' . $file_md5, $count);

                $value = (object)$value;
                $a = [];
                foreach ($select_column as $sk => $s) {
                    $colname = $table_columns[$sk];

                    if ($value->$s == '') {
                        continue;
                    }
                    $a[$colname] = $value->$s;
                }

                $user_id = CRUDBooster::myParentId();

                $skipVatCheck = isset($a['country']) && strtolower($a['country']) === 'switzerland';

                $vat_id = null;
                $vat_number = $a['vat_number'];
                if($vat_number){
                    $vat_checker = \DRM::checkTaxNumber($vat_number);
                    $vat_id = $vat_checker['success'] ? $vat_number : null;
                }

                if($skipVatCheck && $vat_number) {
                    $vat_id = $vat_number;
                }

                $customer_info = [
                    "user_id" => $user_id,
                    "customer_full_name" => $a['full_name'],
                    "currency" => $a['currency'],
                    'email' => $a['email'],
                    'address' => $a['address'],
                    'city' => $a['city'],
                    'country' => $a['country'],
                    'phone' => $a['phone'],
                    'zip_code' => $a['zip_code'],
                    'note' => $a['note'],
                    'website' => $a['website'],
                    'insert_type' => 5,
                    'vat_number' => $vat_id,
                    'source' => 56,
                    'status' => 1,
                ];
                $customer_id = $this->add_customer(array_filter($customer_info));

                //Customer manual tags
                if ($customer_id && (isset($a['customer_tags']) && $a['customer_tags'])) {
                    $manual_tags_str = trim($a['customer_tags']);
                    $manual_tags_arr = ($manual_tags_str) ? explode(',', $manual_tags_str) : [];
                    $manual_tags = ($manual_tags_arr && is_array($manual_tags_arr)) ? array_map('trim', $manual_tags_arr) : [];

                    if ($manual_tags) {
                        foreach ($manual_tags as $manual_tag) {
                            $tag = trim($manual_tag);
                            if ($tag) {

                                //insert tag
                                try {
                                    DropfunnelCustomerTag::insertTag($tag, $user_id, $customer_id, 4);
                                } catch (\Exception $ev) {
                                }
                            }
                        }
                    }
                }
                //Tag insert end
            }
        } catch (\Exception $e) {
            Cache::put('error_' . $file_md5, $e->getMessage());
        }

        return response()->json(['status' => true, 'inserted' => Cache::get('insert_success_' . $file_md5)]);
    }

    private function csvToArray($csv, $type, $delimiter, $deleteFile = true)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $paths = explode(';', $csv);
        $key = null;
        $key_count = 0;
        $array = array();
        $rand = Str::random(40);

        foreach ($paths as $path) {
            // if($deleteFile){
            // 	$path = Storage::disk('spaces')->url($path);
            // 	$file_type = pathinfo($path, PATHINFO_EXTENSION);
            // 	$file = file_get_contents($path);
            // 	file_put_contents($rand.'.'.$file_type,$file);
            // 	$localpath = $rand.'.'.$file_type;
            // }
            // else{
            $localpath = $path;
            // }
            if ($type == 'csv' || $type == 'txt') {
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');

                if ($delimiter != 'auto') {
                    $reader->setDelimiter($delimiter);
                }
                $spreadsheet = $reader->load($localpath);
            } else {
                $spreadsheet = IOFactory::load($localpath);
            }
            $spreadsheet = $spreadsheet->getActiveSheet()->toArray();
            $collection = LazyCollection::make($spreadsheet);

            if ($key == null) {
                $key = array_map('trim', $collection->first());
                $key_count = count($key);
            }
            $key = array_map('removeDots', $key);
            $collection = $collection->except(0);
            foreach ($collection as $row) {

                if (count($row) == $key_count && !containsOnlyNull($row)) {
                    $array[] = array_combine($key, $row);
                }
            }

            if (!pathIsUrl($path) && $deleteFile) {
                unlink($localpath);
            }
        }

        return $array;
    }

    //End Import customer data


    //suggesting tag when searching for customer //FIX soon
    public function autoCompleteSearch(Request $request)
    {

        if ($request::ajax()) {

            $all_data = $request::all();
            $tagSearchChecker = Str::contains($all_data['tag'], '#');
            $filteredSearchText = str_replace('#', '', $all_data['tag']);

            $data = DropfunnelTag::where('tag', 'like', '%' . $filteredSearchText . '%');
            if (!CRUDBooster::isSuperadmin()) {
                $data = $data->where('user_id', CRUDBooster::myParentId());
            }
            $data = $data->pluck('tag')->map(function ($item) {
                return '#' . $item;
            });

            return response()->json($data);
        }
    }


    public function getIndexOld()
    {
        if(!CRUDBooster::isSupplier()){
            redirectToV2('/customers');
        }

        $this->cbLoader();

        $module = CRUDBooster::getCurrentModule();
        $table = $this->table;

        if (!CRUDBooster::isView() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $data = [];
        $data['page_title'] = $module->name;
        $data['page_description'] = trans('crudbooster.default_module_description');
        $data['columns'] = $this->colData();
        $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

        $result = NewCustomer::with([
            // 'user:id,name',
            'orders' => function ($o) {
                $o->where('invoice_number', '!=', -1)->select('id', 'drm_customer_id', 'eur_total');
            },
            // 'dropfunnel_customer_tags' => function($t){
            //     $t->whereIntegerInRaw('insert_type', [10, 11, 12])->select('tag_id', 'insert_type');
            // }
        ])
            ->select('new_customers.id', 'new_customers.user_id', 'new_customers.full_name', 'new_customers.company_name', 'new_customers.vat_number', 'new_customers.phone', 'new_customers.email', 'new_customers.currency', 'new_customers.address', 'new_customers.city', 'new_customers.country', 'new_customers.status', 'new_customers.insert_type','new_customers.created_at','new_customers.updated_at','new_customers.source','new_customers.verified_date')
            ->addSelect(['customer_type' => NewOrder::where('offer_number', null)->selectRaw("(CASE WHEN COUNT(new_orders.drm_customer_id) > 0 THEN 'Customer' ELSE 'Leads' END) as customer_type")
                ->whereColumn('new_orders.drm_customer_id', 'new_customers.id')
                ->where('new_orders.invoice_number', '!=', -1)
                ->where('new_orders.test_order', '!=', 1)
                ->where('new_orders.credit_number', '=', 0)
                ->whereNull('new_orders.credit_ref')
                ->where('new_orders.eur_total', '>', 0)
                ->groupBy('new_orders.drm_customer_id')
            ])
            ->addSelect(['revenue' => NewOrder::selectRaw('sum(eur_total) as total')
                ->whereColumn('drm_customer_id', 'new_customers.id')
                ->where('new_orders.invoice_number', '!=', -1)
                ->where('new_orders.test_order', '!=', 1)
                ->where('new_orders.credit_number', '=', 0)
                ->whereNull('new_orders.credit_ref')
                ->where('new_orders.eur_total', '>', 0)
                ->groupBy('drm_customer_id')
            ]);


        $this->hook_query_index($result);

        //Where query filter
        if (Request::get('where')) {
            foreach (Request::get('where') as $k => $v) {
                if (in_array($k, ['cus_type', 'revenue'])) {
                    continue;
                }
                $result->where($table . '.' . $k, $v);
            }
        }
        //brand filter
        if(!empty(Request::get('search_by_status'))){
            $result->where('status',Request::get('search_by_status'));
        }elseif(Request::get('search_by_status') == '0'){
            $result->where('status',Request::get('search_by_status'));
        }


        $filter_is_orderby = false;
        if (Request::get('filter_column')) {

            $filter_column = Request::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    if (in_array($key, ['cus_type', 'revenue'])) {
                        continue;
                    }

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {

                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];

                if (!empty($sorting) && in_array($key, ['cus_type', 'revenue'])) {
                    if ($key === 'revenue') {
                        $result->orderBy('revenue', $sorting);

                    } elseif ($key = 'cus_type') {
                        $result->orderBy('customer_type', $sorting);
                    }

                    continue;
                }

                if ($sorting != '') {
                    if ($key) {
                        $result->orderby($key, $sorting);
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $data['sources'] = config('customer_sources.sources');
        $data['remove_interval'] = DB::table('customer_delete_interval')->where('user_id', CRUDBooster::myParentId())->value('interval');

        return view("admin.new_customer.index", $data);
    }


    public function getIndex()
    {
        if(!CRUDBooster::isSupplier()){
            redirectToV2('/customers');
        }

        $data = [];
        $data['page_title'] = __('Customers');
        $data['page_description'] = trans('crudbooster.default_module_description');
        $user_id = CRUDBooster::myParentId();

        $data['results'] = NewCustomer::orderBy('id', 'desc')
            ->where('user_id', $user_id)
            ->select('full_name', 'company_name', 'phone', 'email', 'id', 'status', 'favorite', 'is_valid_email')
            ->orderBy('id', 'desc')
            ->paginate(20);

        $countries = NewCustomer::distinct()
            ->whereNotNull('country')->where('country', '<>', '')
            ->pluck('country', 'country')->toArray();
            
        if (isset($countries['Test'])) {
            unset($countries['Test']);
        }
        
        $abbre = [
            'MC' => 'Monaco',
            'NZ' => 'New Zealand',
            'MX' => 'Mexico',
            'UA' => 'Ukraine',
            'NO' => 'Norway',
            'GR' => 'Greece',
            'US' => 'United States',
            'de_DE' => 'Germany',
            'IM' => 'Isle of Man',
            'MQ' => 'Martinique',
            'RE' => 'Réunion',
        ];
        $final_countries = [];
        foreach ($countries as $code => $country_name) {
            $final_countries[$country_name] = $abbre[$country_name] ?? $country_name;
        }
        $data['countries'] = $final_countries;

        $data['grouped_customers'] = DB::table('new_customers')
            ->select('new_customers.full_name', 'new_customers.id', DB::raw('SUM(new_orders.total) as total_sum'))
            ->leftjoin('new_orders', 'new_orders.drm_customer_id', '=', 'new_customers.id')
            ->where('new_customers.user_id', $user_id)
            ->whereNull('new_customers.deleted_at')
            ->groupBy('new_customers.id')
            ->orderBy('total_sum', 'DESC')
            ->get()
            ->groupBy(function ($item) {
                return strtoupper($item->full_name[0]);;
            })
            ->sortBy(function ($item, $key) {
                return $key;
            });

        $data['defaultFilter'] = \App\Option::where([
                            'user_id' => \CRUDBooster::myId(),
                            'option_key' => 'default_customer_filter',
                            'option_group' => 'default_filter',
                        ])
                        ->value('option_value');

        // $data['sources'] = config('customer_sources.sources');
        $data['remove_interval'] = DB::table('customer_delete_interval')->where('user_id', CRUDBooster::myParentId())->value('interval');

//        $data['grouped_customers'] = NewCustomer::where('user_id', $user_id)
//            ->select([
//                'new_customers.full_name',
//                'new_customers.id'
//            ])
//            ->withCount([
//                'orders as total_sum' => function($query) {
//                    $query->select(DB::raw('SUM(total)'));
//                }
//            ])
//            ->whereHas('orders')
//            ->orderBy('total_sum', 'DESC')
//            ->get()
//            ->groupBy(function ($item) {
//                return strtoupper($item->full_name[0]);;
//            })
//            ->sortBy(function ($item, $key) {
//                return $key;
//            });

//        dd($data['grouped_customers']);

        return view("admin.new_customer.contact.index", $data);
    }

    public function postCustomerList()
    {
        $request = $_REQUEST;
        $user_id = CRUDBooster::myParentId();

        $limit = ($request['limit']) ? (int)$request['limit'] : 20;

        $results = NewCustomer::where('user_id', $user_id)
            ->select('full_name', 'company_name', 'phone', 'email', 'id', 'status', 'favorite', 'is_valid_email');

        $q = $request['q'];

        // All filters
        if ($request['filter'] == 'country') {
            if (empty($q) && isset($request['country'])) {
                $q = $request['country'];
            }
            $results = $results->where('country', 'like', '%' . $q . '%');
        }

        if ($request['filter'] == 'full_name') {
            $results = $results->where(function($query) use ($q) {
                $query->where('full_name', 'like', '%' . $q . '%')
                ->orWhere('email', 'like', '%' . $q . '%');
            });
        }

        if ($request['filter'] == 'company_name') {
            $results = $results->where('company_name', 'like', '%' . $q . '%');
        }

        if ($request['filter'] == 'sale') {
            $results = $results->whereHas('orders', function($q) use ($request){
                $q->where('new_orders.total', '>=', (int)$request['min']);
                $q->where('new_orders.total', '<=', (int)$request['max']);
            });
        }

        if($request['filter'] == 'tag'){
            if (empty($q) && isset($request['tag'])) {
                $q = $request['tag'];
            }

            if (!is_array($q)) {
                $q = [$q];
            }

            if (!empty($q)) {
            $results = $results->whereHas('dropfunnel_customer_tags', function($query) use ($q){
                $query->whereHas('tag', function($qbuilder) use ($q){
                    $qbuilder->whereIn('dropfunnel_tags.tag', $q);
                });
            });
            }
        }

        if($request['filter'] == 'email_verification'){
            $results = $results->where('is_valid_email', $request['verification']);
        }


        if ($request['sort_by_type'] == 'customer') {
            $results = $results->where('customer_type', DropfunnelCustomerTag::CUSTOMER);
        } elseif ($request['sort_by_type'] == 'lead') {
            $results = $results->where('customer_type', DropfunnelCustomerTag::LEAD);
        }

        if ($request['sort_by_favorite']) {
            $results = $results->where('favorite',$request['sort_by_favorite']);
        }

        if ($request['sort_by_name']) {
            $results = $results->orderBy('full_name', $request['sort_by_name']);
        }

        if ($request['sort_by_date']) {
            $results = $results->orderBy('created_at', $request['sort_by_date']);
        }

        if ($request['start_date'] && $request['end_date']) {
            $results = $results->whereBetween('created_at', [$request['start_date'], $request['end_date']]);
        }

        if(!empty($request['filter']))
        {
            \App\Option::updateOrCreate([
                'user_id' => \CRUDBooster::myId(),
                'option_key' => 'default_customer_filter',
                'option_group' => 'default_filter',
            ],
            [
                'option_value' => $request['filter'],
            ]);
        }

        $results = $results->paginate($limit);

        $customers = view('admin.new_customer.contact.table', compact('results'))->render();

        return response()->json([
            'success' => true,
            'customer_table' => $customers,
        ]);
    }

    public function postAddNewInput(){

        if(!CRUDBooster::isSupplier()){
            redirectToV2('/customers');
        }
        
        $inputname = strtolower(str_replace(' ', '_', request()->fromlabel));
        $reponse = DB::table('user_custom_input_fields')->insertGetId([
                'creator_id' => CRUDBooster::myId(),
                'user_id'   => request()->customer,
                'label'     => request()->fromlabel,
                'inputname' => $inputname,
                'type' => request()->type
        ]);
        $data = DB::table('user_custom_input_fields')->find($reponse);

        return response()->json([
            'success' => $reponse,
            'data'    => $this->processCustomInput($data),
            'message' => 'added successfully!',
        ]);
    }

    public function postEditInput(){
        $reponse = DB::table('user_custom_input_fields')->where('id',request()->id)->update([
            'label'     => request()->fromlabel,
            'type' => request()->type
        ]);

        $data = DB::table('user_custom_input_fields')->where('id',request()->id)->first();
        $divinp = '';
        if ($data->type == 'textaria') {
            $divinp = '<textarea onchange="trackChange(this)" data-value="'.$data->value.'" name="'.$data->inputname.'" class="form-control" id="labelname" aria-describedby="lebelHelp" placeholder="Enter '.$data->label.'">'.$data->value.'</textarea>';
        } else {
            $divinp = '<input onchange="trackChange(this)" data-value="'.$data->value.'" type="'.$data->type.'" name="'.$data->inputname.'" class="form-control" id="labelname" aria-describedby="lebelHelp" placeholder="Enter '.$data->label.'" value="'.$data->value.'">';
        }


       return response()->json([
            'success' => true,
            'data'    => $data,
            'input' => $divinp,
            'message' => 'Edit filed successfully!',
        ]);
    }

    private function processCustomInput($input) {
        $id = $input->id;
        $label =  $input->label;
        $name = $input->inputname;
        $type = $input->type;
        $value = $input->value;
        // Common HTML structure
        $htmlDiv = '
            <div class="col-md-12" style="padding-left: 0; padding-right: 0;" id="inp'.$id.'">
                <div class="form-group col-md-10">
                    <label for="labelname">'.$label.'</label>';

        // Append input or textarea based on type
        if ($type === 'textaria') {
            $htmlDiv .= '
                <textarea onchange="trackChange(this)" data-value="'.$value.'" name="'.$name.'" class="form-control" id="labelname" aria-describedby="lebelHelp" placeholder="Enter '.$label.'">'.$value.'</textarea>';
        } else {
            $htmlDiv .= '
                <input onchange="trackChange(this)" data-value="'.$value.'" type="'.$type.'" name="'.$name.'" class="form-control" id="labelname" aria-describedby="lebelHelp" placeholder="Enter '.$label.'" value="'.$value.'">';
        }

        // Common HTML structure continued
        $htmlDiv .= '
                </div>
                <div class="col-md-2" style="padding-top: 3%">
                    <button class="btn btn-primery btn-edit" data-id="'.$id.'" data-label="'.$label.'" data-type="'.$type.'"><i class="fa fa-pencil"></i></button>
                    <button class="btn btn-warning btn-delete" data-id="'.$id.'"><i class="fa fa-trash"></i></button>
                </div>
            </div>';

        return $htmlDiv;
    }


    public function postDeleteInput(){
        DB::table('user_custom_input_fields')->where(['id'=>request()->id, 'creator_id' => CRUDBooster::myId()])->delete();
        return response()->json([
            'success' => true,
            'message' => 'Successfully Deleted !',
        ]);
    }


    public function postUpdateDiscount()
    {
        $request = $_REQUEST;
        try {

            $label = $request['field_name'];

            // Blocking time
            if($request['field_name'] == 'df_block_time')
            {
                if($request['value'] == 1)
                {
                    $request['value'] = now();
                } else {
                    $request['value'] = null;
                    DB::table('campaign_unsubscribers')
                    ->where('customer_id', $request['customer_id'])
                    ->delete();
                }

                $label = 'dropfunnel subscription';
            }

            $user_id = CRUDBooster::myParentId();
            NewCustomer::where('id', $request['customer_id'])
                ->where('user_id', $user_id)
                ->update([
                    $request['field_name'] => $request['value']
                ]);
            return response()->json([
                'success' => false,
                'message' => 'Update ' . $label . ' successfully!',
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong!',
            ]);
        }
    }

    public function CustomerDeleteInterval(Request $request)
    {
        try {
            $interval = (int)$request::get('interval');
            $user_id = CRUDBooster::myParentId();
            $message = ($interval != 0) ? 'Customer will delete automatically after ' . $interval . ' day' . $multi = ($interval > 1) ? 's' : '' : 'Do not remove customer automatically!';

            if($interval < 1)
            {
                DB::table('customer_delete_interval')->where('user_id', $user_id)->delete();
            } else {

                DB::table('customer_delete_interval')
                    ->updateOrInsert(
                        ['user_id' => $user_id],
                        [
                            'interval' => $interval,
                            'created_at' => now(),
                            'updated_at' => now()
                        ]
                    );
            }

            return response()->json([
                'success' => true,
                'message' => $message,
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong!',
            ]);
        }
    }

    public function getDetail($id)
    {
        //Create an Auth
        if (!CRUDBooster::isRead() && $this->global_privilege == FALSE || $this->button_edit == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = __('customer.Customer_overview_title');

        // Menu point :: Report
        if (app()->environment('development')) {

            // In the list view we show the 5 customers with the most total sales
            $data['topFiveSellerCustomers'] = NewOrder::select('drm_customer_id', DB::raw('sum(total) as total'))
                ->with('customer')
                ->orderBy('total', 'desc')
                ->groupBy('drm_customer_id')
                ->limit(5)
                ->get();


            // Cancelled Orders
            $data['canceled_order_accounts'] = NewOrder::with('user')
                ->where('status', 'Canceled')
                ->groupBy('cms_user_id')
                ->select('cms_user_id', DB::raw("COUNT(credit_number) as total_cancel"))
                ->orderBy('total_cancel', 'desc')
                ->take(5)
                ->get();


            $order_top_shop = \App\NewOrder::has('shop');
            if (!CRUDBooster::isSuperadmin()) {
                $order_top_shop->where('cms_user_id', \CRUDBooster::myParentId());
            }
            $order_top_shops = $order_top_shop->selectRaw(DB::raw('count(*) as order_count, sum(total) as total_amount, shop_id'))
                ->groupBy('shop_id')
                ->orderBy('order_count', 'desc')
                ->take(5)
                ->pluck('total_amount', 'shop_id')
                ->toArray();

            $top_shop_types = \App\Shop::whereIn('id', array_keys($order_top_shops))
                ->pluck('channel')->toArray();

            $data['topChannels'] = array_map("drm_shop_type_name", $top_shop_types);
            $data['order_top_shops'] = $order_top_shops;

            // Returned order
            $returnedOrders = NewOrder::where([
                ['status', '=', 'Canceled'],
                ['cms_user_id', '=', \CRUDBooster::myParentId()],
            ]);

            // dd( $top5SellerCustomers, $canceled_order_accounts, $topChannels );

        }


        if (CRUDBooster::isSuperadmin()) {
            $customer = NewCustomer::find($id);
        } else {
            $customer = NewCustomer::where([
                'id' => $id,
                'user_id' => CRUDBooster::myParentId(),
            ])->first();
        }

        // error if customer not found
        if ($customer->id == NULL) {
            CRUDBooster::redirectBack(trans('Invalid Customer'), 'error');
        }

        $orders = NewOrder::where('drm_customer_id', $customer->id);
        $order_data = $orders->get();

        $prouducts = [];
        if (count($order_data)) {
            foreach ((object)$order_data as $item) {
                if($item->cart)
                {
                    foreach (json_decode($item->cart) as $cart) {
                        $prouducts[] = $cart;
                    }
                }
            }
        }

        $data['purchase_check'] = $purchase_check = false;// AppStore::CheckAppPurchaseBoolean('40');

        $tag_type = [];
        $tag_type[] = 1;
        if ($purchase_check[0] || CRUDBooster::isSuperadmin()) {
            $tag_type[] = 2; //Clicktip
        }
        if (in_array(CRUDBooster::myParentId(), [212,98, config('global.vod_account_id')]) || CRUDBooster::isSuperadmin()) {
            // $tag_type[] = 3; //VOD
            // $tag_type[] = 11; // VOD Favourites
            $tag_type[] = 12; // Zapier
            // $tag_type[] = 13; // VOD progress
            $tag_type[] = 18; // DT

            $tag_type[] = 24; // Dropcampus
        }

        $tag_type[] = 17; // Calendar

        $tag_type[] = 15; // Leads
        $tag_type[] = 16; // Price Tag
        $tag_type[] = 19; // Offer Tag

        if (CRUDBooster::myParentId() == 2439) {
            $tag_type[] = 20; // DT shop version update tag
        }

        if (CRUDBooster::myParentId() == 2455) {
            $tag_type[] = 14; // Inactive User
        }

        if ( in_array(CRUDBooster::myParentId() , [98,2693,2494]) ) {
            $tag_type[] = 21; // Not available product
        }

        // DropCampus tags check
        if (in_array(CRUDBooster::myParentId(), [71, 2454])) {
            $tag_type[] = 22;
            $tag_type[] = 23;
        }

        $tag_type[] = 4; //Manuell
        $tag_type[] = 5; // Campaign
        $tag_type[] = 6; // Campaign

        $tag_type[] = 7; // Extension
        $tag_type[] = 8; // Channel
        $tag_type[] = 9; // Help Video
        $tag_type[] = 10; // Droptienda
        $tag_type[] = 25; // Dropfunnel Link

        if (in_array(CRUDBooster::myParentId(), [2439, 71, 2494, 2879])) {
            $tag_type[] = 26; // Droptienda User Group
        }

        $tag_type[] = 27; // Offer Deal

        if (in_array(CRUDBooster::myParentId(), [2455, 2439])) {
            $tag_type[] = 28; // Marketplace
        }

        $tag_type[] = 29; // Product

        // asort($tag_type);

        $tag_names = [];
        foreach ($tag_type as $index => $tag_number) {
            $tag_names[$tag_number] = __('customer.' . customerTagNameByType($tag_number));
        }
        asort($tag_names);

        $data['tag_types'] = $tag_type;
        $data['tag_names'] = $tag_names;

        $data['customer'] = $customer;
        $data['orders'] = $order_data;

        $data['total_order'] = $orders->count();
        $data['shipped'] = $orders->where("status", "Shipped")->count() + $orders->where("status", "Versendet")->count();
        $data['others'] = $data['total_order'] - $data['shipped'];
        $data['grandTotal'] = customerGrandTotalOrder($customer->id);
        $data['total_product'] = count($prouducts);
        $data['languages'] = DB::table('countries')->where('is_active', 1)->orderBy('language_shortcode')->get();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();
        $authId = CRUDBooster::myParentId();
        $data['dropfunnel_email_sent_histories'] = DB::table('dropfunnel_email_sent_histories')
            ->where('user_id', $authId)
            ->where('customer_id', $id)
            ->get();
        $data['custom_inputs'] = DB::table('user_custom_input_fields')->where('user_id',$customer->id)->get();
        // $data['custom_inputs'] = collect();
        // dd($data);
        $conditions = ['user_id' => $authId, 'customer_id' => $id];
        $data['contact_form_messages_history'] = ContactFormMessagesHistory::with('contactForm:id,title')->where($conditions)->orderBy('id', 'desc')->get();
        $data['sources'] = config('customer_sources.sources');
        return view('admin.new_customer.details', $data);
    }

    public function postSaveCustomInputData(){

        if(request()->isNotFilled('customer_id')) CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_customers'), "Customer not valid");
        $req_keys = array_keys(request()->all());

        DB::table('user_custom_input_fields')
        ->where(['user_id' => request()->customer_id, 'creator_id' => CRUDBooster::myId()])
        ->get()->each(function($filed) use($req_keys){
            if (in_array($filed->inputname, $req_keys)) {
                $filed->value = request()->input($filed->inputname);
                DB::table('user_custom_input_fields')
                    ->where('id', $filed->id)
                    ->update(['value' => $filed->value]);
            }
        });
        return response()->json([
            'success' => true,
            'message' => 'Successfully added infos !',
        ]);
    }


    public function getCampaingStepDetail($customer_id, $campaignId)
    {
        $campaign = \App\EmailMarketing::where('user_id', CRUDBooster::myParentId())->where('id', $campaignId)->first();
        if(empty($campaign )) return '';

        $stepMails = $campaign->steps()->whereNotNull('subject')->whereNotNull('email_body')->orderBy('position')->get();
        if(empty($stepMails)) return '';

        $history = DB::table('step_mail_sent_history')
        ->where('campaign_id', $campaignId)
        ->where('customer_id', $customer_id)
        ->pluck('step_mail_id', 'step_mail_id')
        ->toArray();

        $blockingLog = DB::table('campaign_unsubscribers')
            ->where('campaign_id', $campaignId)
            // ->whereNotNull('step_id')
            ->pluck('updated_at', 'step_id')
            ->toArray();

        $rows = [];


        foreach ($stepMails as $index => $stepMail) {
            $data = [
                'sent' => isset($history[$stepMail->id]),
                'name' => $stepMail->subject,
                'block' => isset($blockingLog[$stepMail->id]),
            ];

            $date = empty($stepMail->created_at) ? $campaign->created_at : $stepMail->created_at;
            if ($index > 0) { // sumation of all previous step mail
                $delayedDate = $this->_previousStepDelayedTime($stepMails, $stepMail->position, $campaign->created_at);
                if (!empty($delayedDate)) {
                    $date = $delayedDate;
                }
            }

            $stepDelayTime = $this->_hasDelayedTime($date, $stepMail);

            if (!empty($campaign->day_value)) { // if parent has delay time so add
                $delayDate = $this->_hasDelayedTime($stepDelayTime, $campaign);
                if (!empty($delayDate)) {
                    $stepDelayTime = $delayDate;
                }
            }

            if($stepDelayTime->lte(now()))
            {
                $stepDelayTime = '-----';
            }

            $data['schedule'] = !isset($blockingLog[null])? $stepDelayTime : '';
            $rows[] = $data;
        }


        return view('admin.new_customer.df-steps-log', compact('rows'));
    }

    // Delayed Time
    private function _hasDelayedTime($date, $model)
    {
        $type = !empty($model->type) ? $model->type : $model->day_value_type;
        $value = !empty($model->value) ? $model->value : $model->day_value;

        if (empty($value)) {
            $value = 0;
        }

        if (!empty($type) && isset($value)) {
            switch ($type) {
                case 'minutes':
                    return $date->addMinutes($value);
                case 'hours':
                    return $date->addHours($value);
                case 'days':
                    return $date->addDays($value);
            }
        }
        return null;
    }

    // Previous delayed time
    private function _previousStepDelayedTime($steps, $position, $campaignDate)
    {
        $steps = $steps->filter(function ($item) use ($position) {
            if ($item->position < $position) {
                return $item;
            }
        });

        $date = null;
        foreach ($steps as $step) {
            $newDate = empty($step->created_at) ? $campaignDate : $step->created_at;

            if ($date) {
                $newDate = $date;
            }

            if (!empty($step->type) && isset($step->value)) {
                switch ($step->type) {
                    case 'minutes':
                        $date = $newDate->addMinutes($step->value);
                        break;
                    case 'hours':
                        $date = $newDate->addHours($step->value);
                        break;
                    case 'days':
                        $date = $newDate->addDays($step->value);
                        break;
                }
            }
        }
        return $date;
    }


    // --------------- update customer ajax ----------
    public function postUpdateUser()
    {
        //DB::beginTransaction();
        try {

            $validator = Validator::make($_REQUEST, [
                "value" => "nullable",
                "field" => 'required',
                "table" => 'required|in:customer_info,billing,shipping,customer_note',
                "customer_id" => 'required',
                "billing" => 'nullable|array',
                "shipping" => 'nullable|array',
                "customer_info" => 'nullable|array',
            ]);
            if ($validator->fails()) {
                throw new \Exception($validator->errors()->first());
            }

            $customer_id = $_REQUEST['customer_id'];
            $table = $_REQUEST['table'];
            $field = $_REQUEST['field'];
            $value = $_REQUEST['value'];

            $customer = NewCustomer::where('id', '=', $customer_id);
            if (!CRUDBooster::isSuperadmin()) {
                $customer->where('user_id', CRUDBooster::myParentId());
            }
            $customer = $customer->first();

            if (is_null($customer)) {
                throw new \Exception('Invalid customer.');
            }

            //Update customer note
            if ($table == 'customer_note' && $field == 'note') {
                $customer->update(['note' => $value]);
                //DB::commit(); //All ok
                return response()->json([
                    'succecss' => true,
                    'message' => 'Update success!',
                    'data' => $return_data,
                ]);
            }


            if (in_array($table, ['customer_info', 'billing', 'shipping'])) {
                $new_billing = $_REQUEST['billing'] ?? [];
                $new_shipping = $_REQUEST['shipping'] ?? [];
                $new_customer_info = $_REQUEST['customer_info'] ?? [];

                $customer_info = [];

                $billing = [];
                foreach ($new_billing as $billing_data) {
                    $b_k = $billing_data['name'];
                    $customer_info[$b_k] = $billing[$b_k] = $billing_data['value'];
                }

                $shipping = [];
                foreach ($new_shipping as $shipping_data) {
                    $s_k = $shipping_data['name'];
                    $customer_info[$s_k] = $shipping[$s_k] = $shipping_data['value'];
                }

                foreach ($new_customer_info as $customer_info_data) {
                    $c_k = $customer_info_data['name'];
                    $customer_info[$c_k] = $customer_info[$c_k] = $customer_info_data['value'];
                }


                if ($table == 'customer_info') {
                    $skipVatCheck = isset($customer_info['country']) && strtolower($customer_info['country']) === 'switzerland';

                    $old_vat_number = $customer->vat_number;
                    $new_vat_number = $customer_info['vat_number'] ?? null;
                    if($old_vat_number == $new_vat_number)
                    {
                        $skipVatCheck = true;
                    }

                    $customer_info_validator = Validator::make($customer_info, [
                        // "full_name" => "required",
                        // "address" => "required",
                        // "city" => "required",
                        // "country" => "required",
                        "tax_number" => "nullable",
                        'vat_number' => ['nullable', function ($attribute, $value, $fail) use ($skipVatCheck) {
                            $vat_checker = \DRM::checkTaxNumber($value);
                            if(!$vat_checker['success'] && !$skipVatCheck){
                                $fail('The '.$attribute.' is invalid. '.$vat_checker['message']);
                            }
                        }],
                    ]);

                    if ($customer_info_validator->fails()) {
                        throw new \Exception($customer_info_validator->errors()->first());
                    }


                    // Validate Email and phone on API
                    // try {
                        if(isset($customer_info['phone']) && !empty($customer_info['phone']))
                        {
                            app(\App\Services\UiValidation\UiValidation::class)->validatePhone($customer_info['phone']);
                        }
                    // } catch (\Exception $e) {
                    //     CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
                    // }
                }

                $customer_info_data = $customer_info;
                if (!empty($customer_info_data['full_name'])) {
                    $customer_info_data['customer_full_name'] = $customer_info_data['full_name'];
                }

                if($table == 'shipping')
                {
                    $customer_info['shipping'] = shippingInfoJson($customer_info_data, true); //updateBillingShippingAddress(shippingInfoJson($customer_info_data), $customer->shipping);
                }

                if($table == 'billing')
                {
                    $customer_info['billing'] = billingInfoJson($customer_info_data, true); //updateBillingShippingAddress(billingInfoJson($customer_info_data), $customer->shipping);
                }

                unset($customer_info['email']);

                if ($customer->update($customer_info)) {
                    // $this->updateOrderCustomerInfo($customer->id);
                    //DB::commit(); //All ok


                    $updated_billing = json_decode($customer->billing, true);
                    $updated_shipping = json_decode($customer->shipping, true);

                    $return_data = [
                        'full_name' => $customer->full_name,
                        'company_name' => $customer->company_name,
                        'website' => $customer->website,
                        'phone' => $customer->phone,
                        'vat_number' => $customer->vat_number,
                        'tax_number' => $customer->tax_number,
                        'address' => $customer->address,
                        'city' => $customer->city,
                        'state' => $customer->state,
                        'zip_code' => $customer->zip_code,
                        'country' => $customer->country,
                    ];


                    $return_data['street_shipping'] = $updated_shipping['street'];
                    $return_data['city_shipping'] = $updated_shipping['city'];
                    $return_data['state_shipping'] = $updated_shipping['state'];
                    $return_data['zipcode_shipping'] = $updated_shipping['zip_code'];
                    $return_data['country_shipping'] = $updated_shipping['country'];
                    $return_data['shipping_name'] = $updated_shipping['name'];
                    $return_data['shipping_company'] = $updated_shipping['company'];


                    $return_data['street_billing'] = $updated_billing['street'];
                    $return_data['city_billing'] = $updated_billing['city'];
                    $return_data['state_billing'] = $updated_billing['state'];
                    $return_data['zipcode_billing'] = $updated_billing['zip_code'];
                    $return_data['country_billing'] = $updated_billing['country'];
                    $return_data['billing_name'] = $updated_billing['name'];
                    $return_data['billing_company'] = $updated_billing['company'];

                    // update dt customer info
                    $customer = DB::table('new_customers')->find($customer_id);
                    if (!empty($customer)) {
                        app(\App\Services\Order\Store\CustomerOrder::class)->updateDroptiendaAddress($customer);

                        $customer = DB::table('new_customers')->find($customer_id);
                        if (is_dt_user() && !empty($customer->user_id)) {
                            $shop = \App\Shop::where('status', 1)->where('channel', 10)->where('user_id', $customer->user_id)->first();
                            if (!empty($shop)) {
                                $response = $this->sendCustomerInfoToDt($customer_id, $shop);
                            }
                        }
                    }

                    // Do accounting
                    app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

                    return response()->json([
                        'succecss' => true,
                        'message' => 'Update success!',
                        'data' => $return_data,
                    ]);
                }
            }

            throw new \Exception('Nothing changed!');
        } catch (\Exception $e) {
            //DB::rollBack();
            return response()->json([
                'succecss' => false,
                'message' => $e->getMessage()
            ]);
        }
    }


    public function getEdit($id)
    {
        //Create an Auth
        if (!CRUDBooster::isUpdate() && $this->global_privilege == FALSE || $this->button_edit == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }


        session(['customer_id' => $id]);

        $data = [];
        $data['page_title'] = __('customer.Edit_Data');
        // $data['languages'] = DB::table('countries')->get(); // jahdiulhasazahid
        $data['languages'] = DB::table('countries')->where('is_active', 1)->get();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();

        $customer = DB::table('new_customers')->where('id', $id);

        if (!CRUDBooster::isSuperadmin()) {
            $customer->where('user_id', CRUDBooster::myParentId());
        }

        $data['customer'] = $customer->first();
        // $data['selectUserName'] = DB::table('cms_users')->where('id',$data['customer']->user_id)->select('name')->first();

        if ($data['customer'] == null) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }


        $data['shipping'] = json_decode($data['customer']->shipping);
        $data['billing'] = json_decode($data['customer']->billing);

        // dd($data['shipping'], $data['billing']);

        // jahidulhasanzahid
        $data['users'] = DB::table('cms_users')->orderBy('name')->select('id', 'name')->get();
        // dd($data['customer']);

        return view('admin.new_customer.edit', $data);
    }


    public function postEditSave($id)
    {
        // dd(billingInfoJson($_REQUEST));
        $skipVatCheck = isset($_REQUEST['country']) && strtolower($_REQUEST['country']) === 'switzerland';

        $customer = NewCustomer::query();
        if(!CRUDBooster::isSuperAdmin())
        {
            $customer->where('user_id', CRUDBooster::myParentId());
        }
        $customer = $customer->find($id);
        if(empty($customer)) CRUDBooster::redirectBack('Sorry, Something went wrong!');

        $old_vat_number = $customer->vat_number;
        $new_vat_number = $_REQUEST['vat_number'] ?? null;
        if($old_vat_number == $new_vat_number)
        {
            $skipVatCheck = true;
        }

        $validator = Validator::make($_REQUEST, [

            "customer_full_name" => "required",
            "email" => "required|email",
            "address" => "required",
            // "company_name" => "required",
            // "phone" => "required",
            "currency" => "required",
            "default_language" => "required",
            "city" => "required",
            "zip_code" => "required",
            "country" => "required",
            "tax_number" => "nullable",
            'vat_number' => ['nullable', function ($attribute, $value, $fail) use ($skipVatCheck) {
                $vat_checker = \DRM::checkTaxNumber($value);
                if(!$vat_checker['success'] && !$skipVatCheck){
                    $fail('The '.$attribute.' is invalid. '.$vat_checker['message']);
                }
            }],
            // "street_shipping" => "required",
            // "city_shipping" => "required",
            // "state_shipping" => "required",
            "shipping_name" => "required",
            "country_shipping" => "required",
            // "street_billing" => "required",
            // "zipcode_billing" => "required",
            // "city_billing" => "required",

        ]);

        if ($validator->fails()) {
            return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
        }

        if(!isset($_REQUEST['is_same_address'])){

            $validator = Validator::make($_REQUEST, [
                "billing_name" => "required",
                "street_billing" => "required",
                "city_billing" => "required",
                "zipcode_billing" => "required",
                "country_billing" => "required",
            ]);

            if ($validator->fails()) {
                return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
            }

        }

        // Validate Email and phone on API
        try {
            $email = $_REQUEST['email'];
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);
            if(isset($_REQUEST['phone']) && !empty($_REQUEST['phone']))
            {
                $phone = $_REQUEST['phone'];
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
            }
        } catch (\Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }


        $user_id = $_REQUEST['user_id'] ?? $customer->user_id;
        // $insert_type = $_REQUEST['insert_type'] ?? 6;

        if ($customer) {
            $shipping = shippingInfoJson($_REQUEST, true); //updateBillingShippingAddress(shippingInfoJson($_REQUEST), $customer->shipping);
            $billing = (isset($_REQUEST['is_same_address'])) ? $shipping : billingInfoJson($_REQUEST, true); //updateBillingShippingAddress(billingInfoJson($_REQUEST), $customer->shipping);

            $email = $_REQUEST['email'];
            if (!empty($customer->cc_user_id)) {
                if ($email != $customer->email) {
                    CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'You can not edit email address! It will be change if user change his email address!', 'error');
                }
                $email = $customer->email;
            }

            if (NewCustomer::where('id', '<>', $id)->where('user_id', '=', $user_id)->where('email', '=', $email)->exists()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Email already used!', 'error');
            }

            $originalVatNumber = $customer->vat_number;
            $originalBillingCountry = json_decode($customer->billing, true)['country'];
            $ccUserId = $customer->cc_user_id;
            $newVatNumber = $_REQUEST['vat_number'];
            $newBillingCountry = json_decode($billing, true)['country'];

            $customer->update([
                'full_name' => $_REQUEST['customer_full_name'],
                'company_name' => $_REQUEST['company_name'],
                'email' => $_REQUEST['email'],
                'city' => $_REQUEST['city'],
                'zip_code' => $_REQUEST['zip_code'],
                'state' => $_REQUEST['state'],
                'country' => drmCountryNameFull($_REQUEST['country']),
                'phone' => $_REQUEST['phone'],
                'website' => $_REQUEST['website'],
                'currency' => $_REQUEST['currency'],
                'default_language' => $_REQUEST['default_language'],
                'address' => $_REQUEST['address'],
                // 'insert_type'=>$_REQUEST['insert_type'],
                'user_id' => $user_id,
                'vat_number' => $_REQUEST['vat_number'],
                'tax_number' => $_REQUEST['tax_number'],
                // 'cc_user_id' => $_REQUEST['cc_user_id'],
                'shipping' => $shipping,
                'billing' => $billing,
            ]);

            // $this->updateOrderCustomerInfo($customer->id);

            //  && (($originalVatNumber != $newVatNumber) || ($originalBillingCountry != $newBillingCountry))

            if (is_dt_user() && !empty($customer->user_id)) {
                $shop = \App\Shop::where('status', 1)->where('channel', 10)->where('user_id', $customer->user_id)->first();
                if (!empty($shop)) {
                    $response = $this->sendCustomerInfoToDt($id, $shop);
                }
            }

            if(!empty($ccUserId))
            {
                usleep(3);
                app(\App\Services\Payment\RemoveSubscriptionTax::class)->removeTaxByUserId($ccUserId);
            }

            //droptienda customer sync history
            /*DroptiendaSyncHistory::create([
                'sync_type' => DroptiendaSyncType::CUSTOMER,
                'sync_event' => DroptiendaSyncEvent::UPDATE,
                'model_id' => $customer->id,
                'user_id' => $user_id,
            ]);*/
        }

        // Do accounting
        app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

        CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_customers'), trans('Save seccessfull.'), 'success');
    }

    private function sendCustomerInfoToDt($customer_id, $shop) {
        $customer = NewCustomer::find($customer_id);
        $billing = !is_array($customer->billing) ? json_decode($customer->billing, true) : $customer->billing;
        $shipping = !is_array($customer->shipping) ? json_decode($customer->shipping, true) : $customer->shipping;

        $apiUrl = rtrim($shop->url, '/').'/api/v1/user-info-update';

        $response = Http::withHeaders([
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'userToken' => $shop->username,
            'userPassToken' => $shop->password,
        ])->post($apiUrl, [
            'email' => $customer->email,
            'name' => $customer->full_name,
            'phone' => $customer->phone,
            'website_url' => $customer->website,
            'country' => $customer->country,
            'shipping_country' => $shipping['country'],
            'shipping_name' => $shipping['name'],
            'shipping_city' => $shipping['city'],
            'shipping_state' => $shipping['street'] ?? $shipping['state'],
            'shipping_zip' => $shipping['zip_code'],
            'billing_country' => $billing['country'],
            'billing_name' => $billing['name'],
            'billing_city' => $billing['city'],
            'billing_state' => $billing['street'] ?? $billing['state'],
            'billing_zip' => $billing['zip_code'],
        ]);

        if ($response->ok()) {
            return $response->json();
        }

        return true;
    }

    //Update proforma tax
    private function updateProformaTax($user_id, $customer_id)
    {
        NewOrder::with('customer:id,vat_number,tax_number')->where('cms_user_id', $user_id)
        ->where('drm_customer_id', $customer_id)
        ->whereHas('customer', function($cus) {
            $cus->whereNotNull('vat_number')
            ->whereRaw("COALESCE(char_length(vat_number), 0) > 0");
        })
        ->where('invoice_number', -1)
        ->where('dropmatix_total_tax', '>', 0)
        ->get()
        ->each(function($item) {

            $total_tax = (float)$item->dropmatix_total_tax;
            $total = (float)$item->total;

            $total_without_tax = $total - $total_tax;
            $carts = json_decode($item->cart, true);

            $new_carts = collect($carts)->map(function($cart) {
                $cart['tax'] = 0;
                return $cart;
            })
            ->toJson();

            $item->update([
                'total_tax' => 0,
                'total' => $total_without_tax,
                'tax_rate' => 0,
                'eur_total' => $total_without_tax,
                'cart' => $new_carts,
                'vat_number' => $item->customer && $item->customer->vat_number ? $item->customer->vat_number : $item->vat_number,
                'tax_number' => $item->customer && $item->customer->tax_number ? $item->customer->tax_number : $item->tax_number,
            ]);
        });
    }

    public function getAdd()
    {
        if(!CRUDBooster::isSupplier()){
            redirectToV2('/customers');
        }

        //Create an Auth
        if (!CRUDBooster::isCreate() && $this->global_privilege == FALSE || $this->button_add == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = __('customer.ADD_DATA');

        // $data['languages'] = DB::table('languages')->get();
        $data['languages'] = DB::table('countries')->where('is_active', 1)->orderBy('name')->get();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();
        $data['users'] = DB::table('cms_users')->orderBy('name')->get();

        return view('admin.new_customer.add', $data);
    }


    public function postAddSave()
    {
        if(!CRUDBooster::isSupplier()){
            redirectToV2('/customers');
        }

        $skipVatCheck = isset($_REQUEST['country']) && strtolower($_REQUEST['country']) === 'switzerland';

        $validator = Validator::make($_REQUEST, [
            "customer_full_name" => "required",
            "email" => "required|email",
            "address" => "required",
            // "company_name" => "required",
            // "phone" => "required",
            "currency" => "required",
            "default_language" => "required",
            "city" => "required",
            "zip_code" => "required",
            "country" => "required",
            'vat_number' => ['nullable', function ($attribute, $value, $fail) use ($skipVatCheck) {
                $vat_checker = \DRM::checkTaxNumber($value);
                if(!$vat_checker['success'] && !$skipVatCheck){
                    $fail('The '.$attribute.' is invalid. '.$vat_checker['message']);
                }
            }],
            "tax_number" => "nullable",
            // "street_shipping" => "required",
            // "city_shipping" => "required",
            // "state_shipping" => "required",
            "shipping_name" => "required",
            "country_shipping" => "required",
            // "street_billing" => "required",
            // "zipcode_billing" => "required",
            // "city_billing" => "required",
        ]);


        if (CRUDBooster::isSuperadmin()) {
            $validator = Validator::make($_REQUEST, [
                "user_id" => "required",
            ]);

            $user_id = $_REQUEST['user_id'];
        } else {
            $user_id = CRUDBooster::myParentId();
        }

        if ($validator->fails()) {
            return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
        }

        // Validate Email and phone on API
        try {
            $email = $_REQUEST['email'];
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);
            if(isset($_REQUEST['phone']) && !empty($_REQUEST['phone']))
            {
                $phone = $_REQUEST['phone'];
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
            }
        } catch (\Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }

        if(!isset($_REQUEST['is_same_address'])){

            $validator = Validator::make($_REQUEST, [
                "billing_name" => "required",
                "street_billing" => "required",
                "city_billing" => "required",
                "zipcode_billing" => "required",
                "country_billing" => "required",
            ]);

            if ($validator->fails()) {
                return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
            }

        }

//        if (NewCustomer::where(['user_id' => $user_id, 'email' => $_REQUEST['email']])->exists()) {
//            CRUDBooster::redirect(Request::server('HTTP_REFERER'), __('customer.email_already_used'), 'error');
//        }

        $slug = uniqid();
        $_REQUEST['insert_type'] = 6;
        $_REQUEST['slug'] = $slug;
        $_REQUEST['source'] = 50;  // 50 = manual customer register
        $_REQUEST['status'] = 1;
        $_REQUEST['email_verification'] = 1;
        $_REQUEST['verified_date'] = now();
        if ($this->add_customer($_REQUEST, true)) {
            $data = [
                'user_id' => $user_id,
                'slug' => $slug,
                'customer_name' => $_REQUEST["customer_full_name"],
                'to_email' => $_REQUEST["email"]
            ];
//            app(\App\Http\Controllers\AdminOplnMailsController::class)->sendVerificationEmail($data);

            // Do accounting
            app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

            return redirect('admin/drm_all_customers')->with('success', "Successfully Added Customer");
        }

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Please enter input correctly'), 'error');
    }

    // return id if successfull
    // return null if unsuccessful
    public function add_customer($customer_info, $strict = false)
    {
        //DB::beginTransaction();
        try {

            $validator = Validator::make($customer_info, [
                'email' => 'required',
            ]);
            if ($validator->fails()) {
                return null;
            }
            $user_id = $customer_info['user_id'] ?? CRUDBooster::myParentId();

            $customer_data = [];
            $customer_data['full_name'] = $customer_info['customer_full_name'];
            $customer_data['company_name'] = $customer_info['company_name'];
            $customer_data['country'] = drmCountryNameFull($customer_info['country']);
            $customer_data['phone'] = $customer_info['phone'];
            $customer_data['website'] = $customer_info['website'];
            $customer_data['city'] = $customer_info['city'];
            $customer_data['zip_code'] = $customer_info['zip_code'];
            $customer_data['state'] = $customer_info['state'];
            $customer_data['currency'] = $customer_info['currency'];
            $customer_data['default_language'] = $customer_info['default_language'];
            $customer_data['address'] = $customer_info['address'];
            $customer_data['insert_type'] = $customer_info['insert_type'];
            $customer_data['vat_number'] = $customer_info['vat_number'];
            $customer_data['tax_number'] = $customer_info['tax_number'];
            $customer_data['source'] = $customer_info['source'];
            $customer_data['status'] = $customer_info['status'];
            $customer_data['slug'] = $customer_info['slug'];
            $customer_data = array_filter($customer_data);
            $customer_data['source'] = $customer_info['source'];
            $customer_data['status'] = $customer_info['status'];
            $customer_data['verified_date'] = $customer_info['verified_date'];
            $customer = null;

            $softDeletedId = DB::table('new_customers')->where('user_id', $user_id)->where('email', $customer_info['email'])
                ->whereNotNull('deleted_at')
                ->value('id');

            if (!empty($softDeletedId)) {
                $customer_data['deleted_at'] = null;
                $customer_data['updated_at'] = now();
                $customer = DB::table('new_customers')->where('id', $softDeletedId)->update($customer_data);
            } else {
            $customer = NewCustomer::updateOrCreate([
                'email' => $customer_info['email'],
                'user_id' => $user_id,
            ], $customer_data);
            }

            //Update order data
            if ($customer && $customer->id) {

                //Remove this users duplicate cc_user_id
                if (isset($customer_info['cc_user_id']) && $customer_info['cc_user_id']) {
                    $cc_user_id = $customer_info['cc_user_id'];
                    $customer_id = $customer->id;
                    DB::table('new_customers')->where('user_id', $user_id)->where('cc_user_id', $cc_user_id)->where('id', '<>', $customer_id)->update(['cc_user_id' => null]);
                    DB::table('new_customers')->where('id', $customer_id)->update(['cc_user_id' => $cc_user_id]);
                }

                //Update billing and shipping address
                $shipping = $strict ? shippingInfoJson($customer_info, $strict) : updateBillingShippingAddress(shippingInfoJson($customer_info), $customer->shipping);
                $billing = (isset($_REQUEST['is_same_address'])) ? $shipping : ( $strict ? billingInfoJson($customer_info, $strict) : updateBillingShippingAddress(billingInfoJson($customer_info), $customer->billing) );
                $customer->update([
                    'billing' => $billing,
                    'shipping' => $shipping
                ]);

                // $this->updateOrderCustomerInfo($customer->id);

                //Customer manual tags
                if (isset($customer_info['customer_tag_suggest']) && $customer_info['customer_tag_suggest']) {
                    $manual_tags = $customer_info['customer_tag_suggest'];
                    foreach ($manual_tags as $manual_tag) {
                        $tag = trim($manual_tag);
                        if ($tag) {
                            //insert tag
                            try {
                                DropfunnelCustomerTag::insertTag($tag, $user_id, $customer->id, 4);
                            } catch (\Exception $ev) {
                            }
                        }
                    }
                }
                // customer tag

            } else {
                return null;
            }

            //DB::commit();
            return $customer->id;

        } catch (\Exception $e) {

            if(isset($_GET['test']))
            {
                dd($e);
            }
            
            //DB::rollBack();
            return null;
        }
    }

    public function update_customer($customer_info, $id)
    {
        $mpCustomer = isset($customer_info['dropmatix_mp']) && $customer_info['dropmatix_mp'];

        $customer = NewCustomer::find($id);
        if ($customer) {
            $customer_data = [];
            $customer_data['full_name'] = $customer_info['customer_full_name'];
            $customer_data['company_name'] = $customer_info['company_name'];
            $customer_data['country'] = drmCountryNameFull($customer_info['country']);
            $customer_data['city'] = $customer_info['city'];
            $customer_data['zip_code'] = $customer_info['zip_code'];
            $customer_data['address'] = $customer_info['address'];

            $optional = [];
            $optional['state'] = $customer_info['state'];
            $optional['phone'] = $customer_info['phone'];
            $optional['website'] = $customer_info['website'];
            $optional['currency'] = $customer_info['currency'];
            $optional['default_language'] = $customer_info['default_language'];
            $optional = array_filter($optional);

            $customer_data = array_merge($customer_data, $optional);

            if (isset($customer_info['vat_number'])) {
                $customer_data['vat_number'] = $customer_info['vat_number'];
            }

            if (isset($customer_info['tax_number'])) {
                $customer_data['tax_number'] = $customer_info['tax_number'];
            }

            $shipping = shippingInfoJson($customer_info, true); //updateBillingShippingAddress(shippingInfoJson($customer_info), $customer->shipping);
            $billing = (isset($customer_info['is_same_address']) && !$mpCustomer) ? $shipping : billingInfoJson($customer_info, true);

            $customer_data['billing'] = $billing;

            if(!$mpCustomer)
            {
                $customer_data['shipping'] = $shipping;
            }

            // customer filter empty value
            // $customer_data = array_filter($customer_data);
            $customer->update($customer_data);

            //Update order data

            // if(!$light)
            // {
            //    $this->updateOrderCustomerInfo($customer->id);
            // }


            return $id;
        }
        return null;
    }


    // ----------- add customer ajax ---------
    public function postAddCustomerAjax()
    {
        // return response()->json($_REQUEST);
        // return response()->json($_REQUEST["street_billing"]);

        $skipVatCheck = isset($_REQUEST['country']) && strtolower($_REQUEST['country']) === 'switzerland';

        $validator = Validator::make($_REQUEST, [

            "customer_full_name" => "required",
            // "email" => "required | unique:new_customers",
            // "company_name" => "required",
            // "phone" => "required",
            // "currency" => "required",
            // "default_language" => "required",
            "city" => "required",
            // "state" => "required",
            "country" => "required",
            // "street_shipping" => "required",
            // "city_shipping" => "required",
            // "state_shipping" => "required",
            "country_shipping" => "required",
            'vat_number' => ['nullable', function ($attribute, $value, $fail) use ($skipVatCheck){
                $vat_checker = \DRM::checkTaxNumber($value);
                if(!$vat_checker['success'] && !$skipVatCheck){
                    $fail('The '.$attribute.' is invalid. '.$vat_checker['message']);
                }
            }],

        ]);

        // if(CRUDBooster::isSuperadmin())
        // {
        // 	$validator = Validator::make($_REQUEST, [
        // 		"user_id" => "required",
        // 	]);

        // 	$user_id =  $_REQUEST['user_id'];
        // }else{
        // 	$user_id = CRUDBooster::myParentId();
        // }


        if ($validator->fails()) {
            $_REQUEST["error"] = "1";
            return response()->json($validator->errors()->first(), 402);
            // return 0;
        }


        // Validate Email and phone on API
        try {
            $email = $_REQUEST['email'];
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);
            if(isset($_REQUEST['phone']) && !empty($_REQUEST['phone']))
            {
                $phone = $_REQUEST['phone'];
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
            }
        } catch (\Exception $e) {
            return response()->json([
                $e->getMessage(),
            ], 400);
        }

        $_REQUEST['user_id'] = CRUDBooster::myParentId();
        $_REQUEST['insert_type'] = 6;

        $_REQUEST['customer_id'] = "";
        $_REQUEST['customer_id'] = $this->add_customer($_REQUEST, true);

        $data = $_REQUEST;

        if ($data['customer_id'] == null || $data['customer_id'] == "") {
            return response()->json($data, 500);
        }


        // Do accounting
        app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

        return response()->json($data, 200);

    }


    //Do not remove this function. It's use for vod customer insert
    // public function getCsvInsert()
    //   	{

    //   		// $str= '';
    //   		// foreach (NewCustomer::all() as $value) {
    //   		// 	$str .= '("'.$value->full_name.'","'.$value->email.'", '.$value->user_id.','.$value->insert_type.'),';
    //   		// }

    //   		// dd($str);


    //    	$count = 0;
    //        try{
    // 	$csv_path = 'csv/vod_customers.csv';
    // 	// $csv = \Storage::disk('public')->get($csv_path);

    //  //          \Storage::disk('spaces')->put($csv_path, $csv, 'public');

    //  //          if(\Storage::disk('spaces')->exists($csv_path)){
    //  //          	\Storage::disk('public')->put('vod_customers.csv', \Storage::disk('spaces')->get($csv_path) );
    // 	// }

    // 	// dd(\Storage::disk('spaces')->url($csv_path));


    // 	$order_content = @file_get_contents(\Storage::disk('spaces')->url($csv_path));
    //        if (!$order_content) {
    //            throw new \Exception("Can not access url or no order found!");
    //        }
    //        // dd($content);

    //        $putted_orders = @file_put_contents(storage_path() . "/vod_customers.csv", $order_content);
    //        if (!$putted_orders) {
    //            throw new \Exception("Content can not be putted to file " . storage_path() . "/vod_customers.csv");
    //        }


    //         $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
    //         $reader->setInputEncoding('UTF-8');
    //         $reader->setDelimiter(',');
    //         $spreadsheet = $reader->load(realpath(storage_path() . "/vod_customers.csv"));

    //         $customer_arr = $spreadsheet->getActiveSheet()->toArray();

    //         // dd($customer_arr);


    //         $customer_columns = $customer_arr[0];
    //         unset($customer_arr[0]);

    //         // $new_customers = [];
    //         if (count($customer_arr)) {
    // 	        foreach ($customer_arr as $item) {
    // 	        	$name = $item[8];
    // 	        	$email = $item[2];
    // 	        	$street = $item[3];
    //				if($email == '<EMAIL>') continue; //Skip update

    // 	        	$address = $item[4];
    // 	        	$zip_code = null;
    // 	        	$city = $address;

    // 	        	if($address){
    // 	        		preg_match("/\b\d{4,6}(-\d{4,6})?\b/", $address, $matches);
    // 	        		$zip = $matches[0];
    // 	        		if ($zip) {
    // 	        			$zip_code = $zip;
    // 						$city = preg_replace('/\b'.$zip.'\b/i', '', $address);
    // 	        		}
    // 	        	}
    //                 $customer_info = [
    //                     "full_name" => $name,
    //                     'address' => $street,
    //                     'zip_code' => $zip_code,
    //                     'city' => trim($city),
    //                     'insert_type' => 2,
    //                 ];


    //                 $check = [
    //                 	'email' => $email,
    //                 	'user_id' => 98,
    //                 ];

    //                 if(is_null($email)) continue;

    // 		        // customer add
    // 		        $customer_info = array_filter($customer_info);
    // 				$customer = NewCustomer::updateOrCreate($check, $customer_info);
    // 		        if($customer){
    // 			        $customer->update([
    // 			        	'billing' => updateBillingShippingAddress(customerToBillingJson($customer), $customer->billing),
    // 			        	'shipping' => updateBillingShippingAddress(customerToShippingJson($customer), $customer->shipping)
    // 			        ]);
    // 		        	$this->updateOrderCustomerInfo($customer->id);

    // 		        	$count++;
    // 		        }

    // 	        	// $new_customers[] = @array_combine($customer_columns, $item);

    // 	    	}
    // 	    }

    //     }  catch (\Exception $e) {
    //         dd($e->getMessage());
    //     }finally{
    //     	dd($count);
    //     }
    // }

    // public function getInsertOldData(){
    // 	$customers = DB::table('drm_customers')->get();
    // 	$count = 0;
    // 	foreach ($customers as $customer) {
    // 		$billing = DB::table('drm_customer_address')->where('drm_customer_id', $customer->id)->where('type', 'billing')->first();
    // 		$shipping = DB::table('drm_customer_address')->where('drm_customer_id', $customer->id)->where('type', 'shipping')->first();

    // 		$insert_type = ($customer->insert_type == 'Import')? 5 : ( ($customer->insert_type == 'API')? 1 : 6);

    // 		$exist_api = NewCustomer::where('user_id', $customer->user_id)->where('email', $customer->email)->whereIn('insert_type', [1, 2])->first();
    // 		if ($exist_api) {
    // 			continue;
    // 		}

    //               $customer_info = [
    //                   "customer_full_name" => $customer->full_name,
    //                   "company_name" =>  $customer->company_name,
    //                   "currency" => $customer->currency,
    //                   'email' => $customer->email,
    //                   'phone' => $customer->phone,
    //                   'website' => $customer->website,

    //                   'city' =>  $customer->city,
    //                   'zip_code' => $customer->zip_code,
    //                   'state' => $customer->state,
    //                   'address' =>  $customer->address,
    //                   'country' => $customer->country,
    //                   'default_language' => $customer->default_language,

    //                   'insert_type' => $insert_type,

    //                   //shipping
    //                   'street_shipping' => $shipping->street,
    //                   'city_shipping' => $shipping->city,
    //                   'state_shipping' => $shipping->state,
    //                   'zipcode_shipping' => $shipping->zipcode,
    //                   'country_shipping' => $shipping->countryIsoCode,

    //                   //billing
    //                   'street_billing' => $billing->street,
    //                   'city_billing' => $billing->city,
    //                   'state_billing' => $billing->state,
    //                   'zipcode_billing' => $billing->zipcode,
    //                   'country_billing' => $billing->countryIsoCode,

    //                   'user_id' => $customer->user_id,
    //               ];
    //               $count ++;
    //               $this->add_customer($customer_info);
    // 	}
    // 	dd($count);
    // }

    /*----------------------------------------------------------
    -----update all order customer data, on update customer-----
    -----------------------------------------------------------*/
    public function updateOrderCustomerInfo($customer_id)
    {

        // TODO:: DROPMATIX
        return app(\App\Services\Order\Store\CustomerOrder::class)->updateOrderCustomerInfo($customer_id);


        $customer = DB::table('new_customers')->find($customer_id);
        if ($customer) {
            DB::table('new_orders')->where('drm_customer_id', $customer->id)
            ->where('created_at', '>', now()->subHours(72))
            ->update([
                'customer_info' => customerToCustomerInfoJson($customer),
                'billing' => $customer->billing,
                'shipping' => $customer->shipping,
                'vat_number' => $customer->vat_number,
            ]);

            //Update Proforma TAX
            if(in_array($customer->user_id, [98, 2454, 2455, 2439]))
            {
                $this->updateProformaTax($customer->user_id, $customer_id);
            }

            return true;
        }
        return false;
    }

    //user to customer - add
    public function getUserToCustomer()
    {
        $data = [];
        $data['page_title'] = __('customer.create_customer_title');
        // $data['languages'] = DB::table('languages')->get();
        $data['languages'] = DB::table('countries')->where('is_active', 1)->orderBy('name')->get();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();

        $customer = DB::table('new_customers')->where(['user_id' => 98, 'cc_user_id' => CRUDBooster::myParentId()])->first();
        $data['me'] = CRUDBooster::me();
        $data['name'] = ($customer) ? $customer->full_name : CRUDBooster::myName();
        $data['email'] = CRUDBooster::me()->email;

        $data['customer'] = $customer;
        $data['shipping'] = ($customer) ? json_decode($data['customer']->shipping) : null;
        $data['billing'] = ($customer) ? json_decode($data['customer']->billing) : null;
        $data['users'] = DB::table('cms_users')->orderBy('name')->get();
        return view('admin.new_customer.user_to_customer', $data);
    }


    //User to customer
    public function postAddUserToCustomer()
    {
        //DB::beginTransaction();
        try {

            $_REQUEST['insert_type'] = 6;
            $_REQUEST['cc_user_id'] = CRUDBooster::myParentId(); //$_REQUEST['cc_user_id']??
            $_REQUEST['email'] = CRUDBooster::me()->email;

            $skipVatCheck = isset($_REQUEST['country']) && strtolower($_REQUEST['country']) === 'switzerland';

            $validator = Validator::make($_REQUEST, [
                "customer_full_name" => "required",
                "email" => "required",
                "address" => "required",
                // "company_name" => "required",
                // "phone" => "required",
                "currency" => "required",
                "default_language" => "required",
                "city" => "required",
                "zip_code" => "required",
                "country" => "required",
                'vat_number' => ['nullable', function ($attribute, $value, $fail) use ($skipVatCheck){
                    $vat_checker = \DRM::checkTaxNumber($value);
                    if(!$vat_checker['success'] && !$skipVatCheck){
                        $fail('The '.$attribute.' is invalid. '.$vat_checker['message']);
                    }
                }],
                "shipping_name" => "required",
                // "street_shipping" => "required",
                // "city_shipping" => "required",
                // "state_shipping" => "required",
                "country_shipping" => "required",
            ]);

            if ($validator->fails()) {
                //DB::rollBack();
                return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
            }

            if(!isset($_REQUEST['is_same_address'])){

                $validator = Validator::make($_REQUEST, [
                    "billing_name" => "required",
                    "street_billing" => "required",
                    "city_billing" => "required",
                    "zipcode_billing" => "required",
                    "country_billing" => "required",
                ]);

                if ($validator->fails()) {
                    //DB::rollBack();
                    return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
                }

            }

            $user = User::find($_REQUEST['cc_user_id']);
            $cc_user_id = $user->id;

            $customers = [];
            $users = [98, 2454, 2455, 2439];

            //Insert customers
            foreach ($users as $user_id) {

                $customer = NewCustomer::where('user_id', $user_id)->where('cc_user_id', $user->id)->select('id')->first();
                $newly = (empty($customer)) ? true : false;


                $_REQUEST['user_id'] = $user_id;
                $customer_id = $this->add_customer($_REQUEST, true);

                //If customer cereated
                if ($customer_id) {
                    $customers[] = $customer_id;
                    if ($newly) {
                        $this->updateCmsClientOrder($customer_id);
                    }
                }
            }

            if (!empty($customers)) {
                //DB::commit();

                if(!CRUDBooster::isSubUser() && !(DB::table('new_orders')->where('cms_user_id', '=', $user_id)->exists()) ){
                    $this->insertInitialTestOrder();
                }

                CRUDBooster::redirect(CRUDBooster::adminPath(), trans('customer.customer_profile_created_sucessfully'), 'success');
            }

            throw new \Exception('Customer profile created failed');

        } catch (\Exception $e) {
            //DB::rollBack();
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }
    }



    //Insert initial test order
    public function insertInitialTestOrder()
    {
        if(CRUDBooster::isSubUser()) return;


        $user_id = CRUDBooster::myParentId();
        $customer_info = [
            "customer_full_name" => 'Test Customer',
            "currency" => 'EUR',
            'email' => 'test-1234'.$user_id.'@mail.test',
            'address' => '102 Schwarzwaldstrasse',
            'country' => 'Germany',
            'default_language' => 'DE',
            'zip_code' => 60528,
            'city' => 'Frankfurt am Main',
            'insert_type' => 6, //int

            "company_name" =>  'DRM',
            'phone' => '01770000004',
            'website' => 'https://drm.software',

            //shipping
            'shipping_name' => 'Test Customer',
            'shipping_company' => 'DRM',
            'street_shipping' => '102 Schwarzwaldstrasse',
            'city_shipping' => 'Frankfurt am Main',
            'state_shipping' => 'Hesse',
            'zipcode_shipping' => 60528,
            'country_shipping' => 'Germany',

            //billing
            'billing_name' => 'Test Customer',
            'billing_company' => 'DRM',
            'street_billing' => '102 Schwarzwaldstrasse',
            'city_billing' => 'Frankfurt am Main',
            'state_billing' => 'Hesse',
            'zipcode_billing' => 60528,
            'country_billing' => 'Germany',
            'user_id' => $user_id,
            'status' => 1,
        ];

        $customer_id = $this->add_customer($customer_info, true);
        $customer = NewCustomer::find($customer_id);

        $cart_item = [];
        $cart_item['id'] = 1;
        $cart_item['product_name'] = 'Test product';
        $cart_item['description'] = 'Test product description';
        $cart_item['qty'] = 1;
        $cart_item['rate'] = round(0, 2);
        $cart_item['tax'] = 19;
        $cart_item['product_discount'] = 0;
        $cart_item['amount'] = round(0, 2);

        $order_info = [
            'user_id' => $user_id,
            'order_date' => date('Y-m-d H:i:s'),
            'total' => round(0, 2),
            'sub_total' => round(0, 2),
            'discount' => round(0, 2),
            'discount_type' => 'fixed',
            'total_tax' => 0,
            'status' => "test_order",
            'currency' => "EUR",
            'adjustment' => 0,
            'insert_type' => 6,
            'shop_id' => null,
            'order_id_api' => 'test_order',
            'test_order' => 1,
            'invoice_number' => 0,

            'dropmatix_sub_total' => round(0, 2),
            'dropmatix_total_tax' => round(0, 2),
            'dropmatix_discount' => round(0, 2),
            'dropmatix_shipping_cost' => round(0, 2),
            'dropmatix_tax_rate' => 0,
        ];
        $order_info['cart'] = json_encode([$cart_item]);
        $order_info['drm_customer_id'] = $customer->id;
        $order_info['customer_info'] = vodCustomerInfo($customer);
        $order_info['billing'] = vodCustomerAddress($customer);
        $order_info['shipping'] = vodCustomerAddress($customer);

        //create invoice
        app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
    }

    //Update cms client orders
    public function updateCmsClientOrder($customer_id)
    {

        // TODO:: DROPMATIX
        return app(\App\Services\Order\Store\CustomerOrder::class)->updateCmsClientOrder($customer_id);

        $customer = NewCustomer::find($customer_id);
        if ($customer) {

            //Check if customer has cc_user_id
            $has_cc_user_id = (isset($customer->cc_user_id) && !is_null($customer->cc_user_id) && ($customer->cc_user_id > 0)) ? true : false;

            if ($has_cc_user_id) {
                NewOrder::where(['cms_client' => $customer->cc_user_id, 'cms_user_id' => $customer->user_id])->whereNull('drm_customer_id')->update([
                    'drm_customer_id' => $customer_id,
                    'customer_info' => customerToCustomerInfoJson($customer),
                    'billing' => $customer->billing,
                    'shipping' => $customer->shipping
                ]);
                return true;
            }
        }
        return false;
    }

    // Customer ID
    public function getCustomerCId($user_id, $client_id)
    {
        return $this->userBillingToCustomerProfile($user_id, $client_id);
    }


    //Create user billing details to Customer
    public function userBillingToCustomerProfile($user_id, $client_id)
    {
        $user_data = User::find($user_id);
        $user = User::with('billing_detail')->has('billing_detail')->find($user_id);

        $customer = NewCustomer::where('user_id', $client_id)->where('cc_user_id', $user_id)->select('id', 'tax_number')->first();

        $newly = (empty($customer)) ? true : false;

        $customer_id = null;
        $customer_info = [];

        if ($user) {
            $billing = $user->billing_detail;
            $country = ($billing->country) ? (($billing->country->id == 2) ? 'United Kingdom' : $billing->country->name) : null;
            $country = drmCountryNameFull($country);
            $language = ($billing->country) ? $billing->country->language : null;

            $customer_info["customer_full_name"] = $user->name;
            $customer_info["company_name"] = $billing->company_name;
            $customer_info["currency"] = 'EUR';
            $customer_info['email'] = $user->email;
            $customer_info['phone'] = $billing->phone;
            $customer_info['address'] = $billing->address;
            $customer_info['city'] = $billing->city;
            $customer_info['country'] = $country;
            $customer_info['default_language'] = $language;
            $customer_info['zip_code'] = $billing->zip;
            $customer_info['state'] = null;
            $customer_info['vat_number'] = $billing->vat_id;
            $customer_info['tax_number'] =  $customer ? $customer->tax_number : null;

            //shipping
            $customer_info['street_shipping'] = $billing->address;
            $customer_info['city_shipping'] = $billing->city;
            $customer_info['state_shipping'] = null;
            $customer_info['zipcode_shipping'] = $billing->zip;
            $customer_info['country_shipping'] = $country;

            //billing
            $customer_info['street_billing'] = $billing->address;
            $customer_info['city_billing'] = $billing->city;
            $customer_info['state_billing'] = null;
            $customer_info['zipcode_billing'] = $billing->zip;
            $customer_info['country_billing'] = $country;

        } else if (!empty($customer) && $customer->id) {
            return $customer->id;  //Customer_id
        } else {
            $customer_info["customer_full_name"] = $user_data->name;
            $customer_info["currency"] = 'EUR';
            $customer_info['email'] = $user_data->email;
        }

        $customer_info['insert_type'] = 6;
        $customer_info['user_id'] = $client_id;
        $customer_info['cc_user_id'] = $user_data->id;

        $customer_id = $this->add_customer($customer_info);  //Customer_id
        // if ($customer_id && !is_null($customer_id) && $newly) {
        //     $this->updateCmsClientOrder($customer_id);    // Update cms clients customer_id
        // }

        return $customer_id;
    }

    //fetch customer tags
    public function postFetchCustomerTags()
    {
        try {
            $customer_id    = request()->customer_id;
            $tag_types      = (array)request()->customer_tag_types;
            $filter_by_tags = request()->filter_by_tags; // array value

            $customer_tags = DropfunnelCustomerTag::with('tag:id,tag')->has('tag')
                                ->where('customer_id', $customer_id);

            if (!empty($filter_by_tags)) {
                $customer_tags = $customer_tags->whereIn('insert_type', $filter_by_tags);
            } else {
                // $customer_tags = $customer_tags->whereIn('insert_type', $tag_types);
            }

            if (!CRUDBooster::isSuperadmin()) {
                $user_id = CRUDBooster::myParentId();
                $customer_tags->whereHas('tag', function ($q) use ($user_id) {
                    $q->where('user_id', $user_id);
                });
            }

            $search_string = request()->search_customer_tags;
            if ($search_string) {
                $customer_tags->whereHas('tag', function ($q) use ($search_string) {
                    $q->where('tag', 'like', '%' . $search_string . '%');
                });
            }

            $customer_tags = $customer_tags->select('id', 'insert_type', 'created_at', 'score', 'tag_id', 'updated_at', 'created_at')->get();
            if (!empty($customer_tags)) {
                return response()->json([
                    'success' => true,
                    'data' => CustomerTagResource::collection($customer_tags),
                    'message' => 'Tags loaded successfully!'
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => [],
                'message' => 'Tags not found!'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //Add customer Tag
    function postAddCustomerTag()
    {
        try {
            $title = request()->tag_value;
            $customer_id = request()->customer_id;

            if ($title && $customer_id) {
                $customer = NewCustomer::find($customer_id, ['id', 'user_id']);
                if ($customer) {
                    if (!CRUDBooster::isSuperadmin() && ($customer->user_id != CRUDBooster::myParentId())) throw new \Exception("Invalid action!");

                    //New customer tag system
                    $title = trim($title);
                    if (empty($title)) throw new \Exception('Tag can not be empty!');

                    $res = DropfunnelCustomerTag::insertTag($title, $customer->user_id, $customer->id, 4);
                    if ($res) {
                        //Message
                        $message = $res['message'];

                        if ($res['success'] === true) {

                            $tag_id = $res['tag_id'];

                            $customer_tag = DropfunnelCustomerTag::has('tag')->with('tag:id,tag')->where('tag_id', $tag_id)->where('customer_id', $customer->id)->select('id', 'insert_type', 'created_at', 'score', 'tag_id')->first();
                            if ($customer_tag) {

                                $tag_score = ($customer_tag->score > 1) ? ' ( ' . $customer_tag->score . ')' : '';
                                $tag = $customer_tag->tag;
                                $title = $tag->tag . $tag_score;

                                return response()->json([
                                    'success' => true,
                                    'title' => $title,
                                    'id' => $customer_tag->id,
                                    'input_type' => $customer_tag->insert_type,
                                    'date' => $customer_tag->created_at->format('Y-m-d H:i:s'),
                                    'message' => $message
                                ]);

                            } else {
                                throw new \Exception('Tag may not inserted properly!');
                            }
                        } else {
                            throw new \Exception($message);
                        }
                    }

                    throw new \Exception('Tag may not inserted properly!');
                    //Tag insert system


                } else {
                    throw new \Exception("Invalid customer!");
                }
            } else {
                throw new \Exception("tag name can not be empty!");
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //Delete customer Tag
    function postRemoveCustomerTag()
    {
        try {
            $id = request()->tag_id;
            $is_soft_delete = request()->soft_delete;

            $customer_tag = DropfunnelCustomerTag::where('id', $id);
            if (!CRUDBooster::isSuperadmin()) {
                $user_id = CRUDBooster::myParentId();

                $customer_tag->whereHas('tag', function ($q) use ($user_id) {
                    $q->where('user_id', $user_id);
                });
            }

            $customer_tag = $customer_tag->select('id')->first();

            if ($customer_tag) {
                if ($is_soft_delete == 'yes') {
                    $customer_tag->delete();
                } else {
                    $customer_tag->forceDelete();
                }
                return response()->json([
                    'success' => true,
                    'tag_id' => $customer_tag->id,
                    'message' => "Tag deleted successfully!"
                ]);
            }

            throw new \Exception('Invalid operation!');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //Insert customer tag
    public function insertCustomerTag($order_id)
    {
        $order = NewOrder::find($order_id);
        if (is_null($order)) {
            return false;
        }

        //DB::beginTransaction();
        try {

            $shop_types = [
                1 => 'Gambio',
                2 => 'Lengow',
                3 => 'Yatego',
                5 => 'AMAZON',
                6 => 'Shopify',
                7 => 'Woocommerce',
                8 => 'ClouSale',
                9 => 'Chrono24',
            ];

            $decodedCartTag = json_decode($order->cart, true);
            $drm_customer_id = $order->drm_customer_id;
            $cms_user_id = $order->cms_user_id;
            $shop_id = $order->shop_id;
            $tagsArray = [];

            foreach ($decodedCartTag as $productNameasTag) {
                $tag_label = strip_tags(preg_replace_array('/"/', [' '], $productNameasTag['product_name']));
                $tagsArray[] = ltrim($tag_label, '"'); // removing double quote from product name
            }

            //get shop
            $shop = \App\Shop::where('id', $shop_id)->select('channel', 'shop_name')->first();
            if ($shop) {
                $tagsArray[] = $shop->shop_name;
                $channel_name = (isset($shop_types[$shop->channel])) ? $shop_types[$shop->channel] : null;
                if ($channel_name) {
                    $tagsArray[] = $channel_name;
                }
            }

            if ($order->customer) {
                $country_tag = drmCountryNameFull($order->customer->country) ?? null;
                if ($country_tag) {
                    $tagsArray[] = $country_tag;
                }
            }

            // add product tags
            $product_tags = [];
            if (isset($decodedCartTag[0]['product_id'])) {
                $product_tags = DrmProduct::where('id', $decodedCartTag[0]['product_id'])->value('tags');
            } else if (isset($decodedCartTag[0]['ean'])) {
                $product_tags = DrmProduct::where('ean', $decodedCartTag[0]['ean'])->value('tags');
            }

            if (!empty($product_tags)) {
                $product_tags = explode(',', $product_tags);
                foreach ($product_tags as $tag) {
                    try {
                        DropfunnelCustomerTag::insertTag(trim($tag), $cms_user_id, $drm_customer_id, 29);
                    } catch (\Exception $ev) {

                    }
                }
            }

            // do not continue if tags array is empty
            $tagsArray = array_filter($tagsArray);
            if ($tagsArray) {
                foreach ($tagsArray as $tag) {

                    $tag = trim($tag);
                    //insert tag
                    try {
                        DropfunnelCustomerTag::insertTag($tag, $cms_user_id, $drm_customer_id, 1);
                    } catch (\Exception $ev) {
                    }
                }
            }

            $hasOrder = NewOrder::where('new_orders.invoice_number', '!=', -1)
            ->where('new_orders.test_order', '!=', 1)
            ->where('new_orders.credit_number', '=', 0)
            ->whereNull('new_orders.credit_ref')
            ->where('new_orders.eur_total', '>', 0)
            ->where('new_orders.cms_user_id', $cms_user_id)
            ->where('new_orders.drm_customer_id', $drm_customer_id)
            ->exists();

            $type = !empty($order->offer_number) ? 3 : ($hasOrder ? 2 : 1);
            DropfunnelCustomerTag::insertLeads($cms_user_id, $drm_customer_id, $type);

//          DropfunnelCustomerTag::insertLeads($cms_user_id, $drm_customer_id, 2);


            //DB::commit();
        } catch (\Exception $e) {
            //DB::rollBack();
            User::find(71)->notify(new DRMNotification('Customer tag inserte error DRM Customer file ' . $e->getMessage(), 'TAG_NOTIFICATION_ADMIN_ERROR'));
        }
    }


    public function getFixKarinaOrder()
    {
        // $count = 0;
        // $orders = NewOrder::where('cms_user_id', 123)->where('shop_id', 263)->select('drm_customer_id')->get();
        // foreach ($orders as $order) {
        // 	$this->updateOrderCustomerInfo($order->drm_customer_id);
        // 	$count++;
        // }
        // dd($count);
    }


    public function getDroptindaUsers()
    {
        try {
            if (!app()->environment('development')) throw new \Exception('This is allow only development server!');

            $shop = Shop::find(152, ['user_id']);
            if (is_null($shop) || empty($shop)) throw new \Exception('Invalid Shop!');

            $client = new Client();
            $droptinda_user_url = 'https://droptienda.rocks/api/all_users';
            $count = 0;

            $response = $client->get($droptinda_user_url);
            $user_list = json_decode($response->getBody()->getContents());
            if ($user_list) {
                foreach ($user_list as $user) {
                    $data = [];
                    $data['email'] = $user->email;
                    $data['user_id'] = $shop->user_id;

                    $first_name = $user->first_name;
                    $middle_name = $user->middle_name;
                    $last_name = $user->last_name;

                    $username = $user->username;
                    $name = (strlen(trim($first_name . $middle_name . $last_name)) > 1) ? trim($first_name . ' ' . $middle_name . ' ' . $last_name) : $username;

                    $validator = Validator::make($data, [
                        'user_id' => 'required',
                        'email' => 'required',
                        // 'password' => 'required',
                    ]);

                    if ($validator->fails()) {
                        continue; //throw new \Exception("Error Processing Request", 1);
                    }

                    NewCustomer::updateOrCreate($data, [
                        'full_name' => $name
                    ]);

                    $count++;
                }
            }
            return $count;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    //Droptinda New Custtomer Webhook
    public function DroptindataNewCustomerWebhook()
    {
        return $this->getDroptindaUsers();
    }

    public function getTagExtensionSave()
    {
        try {
            $purchased_app = DB::table('purchase_apps')
                ->select('app_stores.id', 'app_stores.menu_name', 'purchase_apps.cms_user_id')
                ->join('app_stores', 'purchase_apps.app_id', '=', 'app_stores.id')
                ->whereDate('purchase_apps.subscription_date_end', '>=', Carbon::now())
                ->get()
                ->toArray();

            $assigned_app = DB::table('app_assigns')
                ->select('app_stores.menu_name', 'app_assigns.user_id')
                ->join('app_stores', 'app_assigns.app_id', '=', 'app_stores.id')
                ->where(function($amnu){
                    $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
                })
                ->get()
                ->toArray();

            foreach ($purchased_app as $item) {
                $this->tagInsertToCustomer($item->cms_user_id, $item->menu_name, 7);
            }
            foreach ($assigned_app as $value) {
                $this->tagInsertToCustomer($value->cms_user_id, $value->menu_name, 7);
            }

            $purchased_app_expire = DB::table('purchase_apps')
                ->select('app_stores.id', 'app_stores.menu_name', 'purchase_apps.cms_user_id')
                ->join('app_stores', 'purchase_apps.app_id', '=', 'app_stores.id')
                ->whereDate('purchase_apps.subscription_date_end', '<', Carbon::now())
                ->get()
                ->toArray();

            foreach ($purchased_app_expire as $item) {
                $this->tagInsertToCustomer($item->cms_user_id, $item->menu_name . '-Cancelt', 7);
            }

        } catch (\Exception $ex) {
            return $ex->getMessage();
        }
    }

    public function tagInsertForInactiveUsers()
    {
        try {
            $trakers = Traker::has('user')->with('user:id,email_verified_at')->where('created_at', '>=', Carbon::today()->subDays(90))->orderBy('created_at', 'desc')->groupBy('user_id')->get();
            if ($trakers->isNotEmpty()) {
                foreach ($trakers as $traker) {
                    $diff = Carbon::now()->diffInDays($traker->created_at);
                    $day = 0;
                    if ($diff >= 30 && $diff < 60) {
                        $day = 30;
                    }

                    if ($diff >= 60 && $diff < 90) {
                        $day = 60;
                    }

                    if ($diff >= 90) {
                        $day = 90;

                    }

                    if ($day > 0) {
                        $this->tagInsertToCustomer($traker->user_id, "Inactive for $day days");
                        if (($day == 30 && empty($traker->user->email_verified_at)) || $day == 90) {
                            // block user
                            DB::table('cms_users')->where('id', $traker->user_id)->update(['status' => null, 'updated_at' => Carbon::now()]);
                        }
                    }
                }
            }
        } catch (\Exception $exception) {
        }
    }

    public function getDuplicateCustomer($id = null)
    {
        if (CRUDBooster::isSuperadmin()) {
            return DB::table('new_customers')
                ->selectRaw('user_id,cc_user_id,full_name,GROUP_CONCAT(email) as email,COUNT(email) as duplicates')
                ->where('user_id', $id ?? CRUDBooster::myParentId())
                ->groupBy('email')
                ->get();
        }
        return 'Access Denied';
    }

    public function getFixLeads()
    {
        $rm_tag = 'Customer';
        $user_tag_id = DB::table('dropfunnel_tags')
        ->where('tag', $rm_tag)
        ->pluck('user_id', 'user_id')
        ->each(function($item) {
            $this->getFixLead($id);
        });
    }



    public function getFixLead($id)
    {
        $user_id = $id;
        if(empty($user_id)) return;


        $rm_tag = 'Customer';
        $user_tag_id = DB::table('dropfunnel_tags')->where('user_id', $user_id)->where('tag', $rm_tag)->value('id');
        if(empty($user_tag_id)) return;

        $customers = NewCustomer::where('user_id', $user_id)->whereDoesntHave('orders', function($q) {
            $q->where('new_orders.invoice_number', '!=', -1)
            ->where('new_orders.test_order', '!=', 1)
            ->where('new_orders.credit_number', '=', 0)
            ->whereNull('new_orders.credit_ref')
            ->where('new_orders.eur_total', '>', 0);
        })
        ->whereHas('dropfunnel_customer_tags', function($t) use ($user_tag_id) {
            $t->where('dropfunnel_customer_tags.tag_id', $user_tag_id);
        })
        ->select('user_id', 'id')
        ->get()
        ->each(function($customer) {
            DropfunnelCustomerTag::insertLeads($customer->user_id, $customer->id);
        });

        dd($customers);
    }

    public function getDropfunnelCustomers()
    {
        $data = [];
        $data['page_title'] = __('Customers');
        $data['page_description'] = trans('crudbooster.default_module_description');
        $user_id = CRUDBooster::myParentId();

        // $data['results'] = NewCustomer::orderBy('id', 'desc')
        //     ->where('user_id', $user_id)
        //     ->select('full_name', 'company_name', 'phone', 'email', 'id', 'status', 'favorite')
        //     ->orderBy('id', 'desc')
        //     ->paginate(20);

        $results = NewCustomer::where('user_id', $user_id)
        ->select('full_name', 'company_name', 'phone', 'email', 'id', 'status', 'favorite', 'is_valid_email');
        $data['results'] = $results->doesntHave('orders')->paginate(20);

        $countries = NewCustomer::distinct()
            ->whereNotNull('country')->where('country', '<>', '')
            ->pluck('country', 'country')->toArray();

        if (isset($countries['Test'])) {
            unset($countries['Test']);
        }

        $abbre = [
            'MC' => 'Monaco',
            'NZ' => 'New Zealand',
            'MX' => 'Mexico',
            'UA' => 'Ukraine',
            'NO' => 'Norway',
            'GR' => 'Greece',
            'US' => 'United States',
            'de_DE' => 'Germany',
            'IM' => 'Isle of Man',
            'MQ' => 'Martinique',
            'RE' => 'Réunion',
        ];
        $final_countries = [];
        foreach ($countries as $code => $country_name) {
            $final_countries[$country_name] = $abbre[$country_name] ?? $country_name;
        }
        $data['countries'] = $final_countries;

        $data['grouped_customers'] = $results->doesntHave('orders');

        // $data['grouped_customers'] = DB::table('new_customers')
        //     ->select('new_customers.full_name', 'new_customers.id', DB::raw('SUM(new_orders.total) as total_sum'))
        //     ->leftjoin('new_orders', 'new_orders.drm_customer_id', '=', 'new_customers.id')
        //     ->where('new_customers.user_id', $user_id)
        //     ->groupBy('new_customers.id')
        //     ->orderBy('total_sum', 'DESC')
        $data['grouped_customers'] = $data['grouped_customers']->get()
            ->groupBy(function ($item) {
                return strtoupper($item->full_name[0]);;
            })
            ->sortBy(function ($item, $key) {
                return $key;
            });

        $user_campaigns = DropfunnelTag::join('campaign_tags', 'dropfunnel_tags.id', '=', 'campaign_tags.tag_id')
            ->join('email_marketings', 'campaign_tags.campaign_id', '=', 'email_marketings.id')
            ->where('dropfunnel_tags.user_id', CRUDBooster::myParentId())
            ->distinct()
            ->select('campaign_id', 'campaign_name')
            ->get();

        $data['user_campaigns'] = $user_campaigns;

        return view("admin.new_customer.contact.dropfunnel_customer_index", $data);
    }

    public function postDropfunnelCustomerList()
    {
        $request = $_REQUEST;
        $user_id = CRUDBooster::myParentId();

        $limit = ($request['limit']) ? (int)$request['limit'] : 20;

        $results = NewCustomer::where('user_id', $user_id)
            ->select('full_name', 'company_name', 'phone', 'email', 'new_customers.id', 'status', 'favorite', 'is_valid_email');

        $grouped_customers = NewCustomer::where('user_id', $user_id)
            ->select('full_name', 'company_name', 'phone', 'email', 'new_customers.id', 'status', 'favorite','country');

        if ($request['filter'] == 'campaign') {
            $results->join('dropfunnel_customer_tags', 'new_customers.id', '=', 'dropfunnel_customer_tags.customer_id')
                ->join('campaign_tags', 'dropfunnel_customer_tags.tag_id', '=', 'campaign_tags.tag_id')
                ->where('campaign_id', (int)$request['campaign_id']);

            $grouped_customers->join('dropfunnel_customer_tags', 'new_customers.id', '=', 'dropfunnel_customer_tags.customer_id')
                ->join('campaign_tags', 'dropfunnel_customer_tags.tag_id', '=', 'campaign_tags.tag_id')
                ->where('campaign_id', (int)$request['campaign_id']);
        }

        if ($request['filter'] == 'country') {
            $results = $results->where('country', $request['country']);
            $grouped_customers = $grouped_customers->where('country', $request['country']);

        }

        $q = $request['q'];

        if ($request['filter'] == 'full_name') {
            $results = $results->where(function($query) use ($q) {
                $query->where('full_name', 'like', '%' . $q . '%')
                ->orWhere('email', 'like', '%' . $q . '%');
            });

            $grouped_customers = $grouped_customers->where(function($query) use ($q) {
                $query->where('full_name', 'like', '%' . $q . '%')
                ->orWhere('email', 'like', '%' . $q . '%');
            });
        }

        if ($request['filter'] == 'company_name') {
            $results = $results->where('company_name', 'like', '%' . $q . '%');
            $grouped_customers = $grouped_customers->where('company_name', 'like', '%' . $q . '%');
        }


        if ($request['filter'] == 'sale') {
            $results = $results->whereHas('orders', function($q) use ($request){
                $q->where('new_orders.total', '>=', (int)$request['min']);
                $q->where('new_orders.total', '<=', (int)$request['max']);
            });
            $grouped_customers = $grouped_customers->whereHas('orders', function($q) use ($request){
                $q->where('new_orders.total', '>=', (int)$request['min']);
                $q->where('new_orders.total', '<=', (int)$request['max']);
            });
        }

        if($request['filter'] == 'tag' && !empty($request['tags'])){
            $results = $results->whereHas('dropfunnel_customer_tags', function($query) use ($request){
                $query->whereHas('tag', function($q) use ($request){
                    $q->whereIn('dropfunnel_tags.tag', $request['tags']);
                });
            });
            $grouped_customers = $grouped_customers->whereHas('dropfunnel_customer_tags', function($query) use ($request){
                $query->whereHas('tag', function($q) use ($request){
                    $q->whereIn('dropfunnel_tags.tag', $request['tags']);
                });
            });
        }

        if($request['filter'] == 'email_verification'){
            $results = $results->where('is_valid_email', $request['verification']);
        }

        if ($request['sort_by_type'] == 'customer') {
            $results = $results->whereHas('orders');
            $grouped_customers = $results->whereHas('orders');
        } elseif ($request['sort_by_type'] == 'lead') {
            $results = $results->doesntHave('orders');
            $grouped_customers = $results->doesntHave('orders');
        }

        if ($request['sort_by_favorite']) {
            $results = $results->where('favorite',$request['sort_by_favorite']);
            $grouped_customers = $grouped_customers->where('favorite',$request['sort_by_favorite']);
        }

        if ($request['sort_by_name']) {
            $results = $results->orderBy('full_name', $request['sort_by_name']);
            $grouped_customers = $grouped_customers->orderBy('full_name', $request['sort_by_name']);
        }

        if ($request['sort_by_date']) {
            $results = $results->orderBy('new_customers.created_at', $request['sort_by_date']);
            $grouped_customers = $grouped_customers->orderBy('new_customers.created_at', $request['sort_by_date']);
        }

        if ($request['start_date'] && $request['end_date']) {
            $results = $results->whereBetween('new_customers.created_at', [$request['start_date'], $request['end_date']]);
            $grouped_customers = $grouped_customers->whereBetween('new_customers.created_at', [$request['start_date'], $request['end_date']]);
        }

        $grouped_customers = $grouped_customers->get();


        $grouped_customers =  $grouped_customers->groupBy(function ($item) {
            return strtoupper($item->full_name[0]);;
        })
        ->sortBy(function ($item, $key) {
            return $key;
        });
        $results = $results->paginate($limit);

        $customers = view('admin.new_customer.contact.table', compact('results'))->render();
        $customer_group = view('admin.new_customer.contact.customer_group', compact('grouped_customers'))->render();

        return response()->json([
            'success' => true,
            'customer_table' => $customers,
            'grouped_customers' => $customer_group,
        ]);
    }

    // Set existing user tag
    public function getSetExistingUserTags()
    {
        $usersId = DB::table('user_group_relations')->where('user_id', '>=', 3222)->where('group_id', 1)->select('user_id')->pluck('user_id', 'user_id')->toArray();
        foreach($usersId as $id)
        {
            try {
                if( DB::table('user_tags')->where('user_id', $id)->where('tag', 'drm_user')->exists()) continue;
                DB::table('user_tags')->insert([
                    'user_id' => $id,
                    'tag' => 'drm_user',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            } catch(\Exception $e) {}
        }
    }

    public function postCustomerDataExport(Request $request){
        try{
            $user_id = CRUDBooster::myParentId();
            $all_data = $request::all();
            $checkedAll = $all_data['checkedAll'];
            $customer_ids = $all_data['customer_ids'];
            $file_ext = $all_data['file_ext'];
            $params = $all_data['params'];
            if($checkedAll == 'true'){
                $customer_ids = $this->selectedCustomerIds($params, $user_id);
            }

            $job_payload = [
                'user_id' => $user_id,
                'ids' => $customer_ids,
                'ext' => $file_ext,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            $payload_id = DB::table('customer_data_export_requests')->insertGetId([
                'user_id' => $user_id,
                'payload' => json_encode($job_payload),
            ]);

            CustomerDataExportJob::dispatch($payload_id);

            return response()->json([
                'success' => true,
                'message' => __('Process running on background'),
            ]);

        } catch(Exception $e){
            dd($e);
        }
    }

    public function postBulkAddTag(Request $request){
        $user_id = CRUDBooster::myParentId();
        $all_data = $request::all();
        $checkedAll = $all_data['checkedAll'];
        $customer_ids = $all_data['customer_ids'];
        $params = $all_data['params'];
        $tag = $all_data['tag'];

        if($checkedAll == 'true'){
            $customer_ids = $this->selectedCustomerIds($params, $user_id);
        }

        CustomerBulkAddTagJob::dispatch($customer_ids, $user_id, $tag);

        return response()->json([
            'success' => true,
            'message' => __('Tag adding process is running on background.'),
        ]);
    }

    public function postDeleteExistingCustomersBulk(Request $request){
        try{
            $user_id = CRUDBooster::myParentId();
            $all_data = $request::all();
            $checkedAll = $all_data['checkedAll'];
            $customer_ids = $all_data['customer_ids'];
            $params = $all_data['params'];
            $tag = $all_data['tag'];

            if($checkedAll == 'true'){
                $customer_ids = $this->selectedCustomerIds($params, $user_id);
            }

            $customer_ids = explode(",", $customer_ids);

            $id_chunks = array_chunk($customer_ids, 500);

            foreach($id_chunks as $chunk){
                NewCustomer::whereIn('id', $chunk)->delete();
            }

            return response()->json([
                'success' => true,
                'message' => __('Customers Deleted Successfully!'),
            ]);
        } catch(Exception $e){
            dd($e);
        }
    }

    public function selectedCustomerIds($params, $user_id) {
        $results = NewCustomer::where('user_id', $user_id);

        if ($params['filter'] == 'country') {
            $results = $results->where('country', $params['country']);
        }

        $q = $params['q'];

        if ($params['filter'] == 'full_name') {
            $results = $results->where(function($query) use ($q) {
                $query->where('full_name', 'like', '%' . $q . '%')
                ->orWhere('email', 'like', '%' . $q . '%');
            });
        }

        if ($params['filter'] == 'company_name') {
            $results = $results->where('company_name', 'like', '%' . $q . '%');
        }


        if ($params['filter'] == 'sale') {
            $results = $results->whereHas('orders', function($q) use ($params){
                $q->where('new_orders.total', '>=', (int)$params['min']);
                $q->where('new_orders.total', '<=', (int)$params['max']);
            });
        }

        if($params['filter'] == 'tag' && !empty($params['tags'])){
            $results = $results->whereHas('dropfunnel_customer_tags', function($query) use ($params){
                $query->whereHas('tag', function($q) use ($params){
                    $q->whereIn('dropfunnel_tags.tag', $params['tags']);
                });
            });
        }

        if($params['filter'] == 'email_verification' && !empty($params['verification'])){
            $results = $results->where('is_valid_email', $params['verification']);
        }

        if ($params['sort_by_type'] == 'customer') {
            $results = $results->whereHas('orders');
        } elseif ($params['sort_by_type'] == 'lead') {
            $results = $results->doesntHave('orders');
        }

        if ($params['sort_by_favorite']) {
            $results = $results->where('favorite',$params['sort_by_favorite']);
        }

        if ($params['sort_by_name']) {
            $results = $results->orderBy('full_name', $params['sort_by_name']);
        }

        if ($params['sort_by_date']) {
            $results = $results->orderBy('created_at', $params['sort_by_date']);
        }

        $customer_ids = implode(",", $results->pluck('id')->toArray());

        return $customer_ids;
    }

    //fetch customer tags
    public function postBulkFetchCustomerTags(Request $request)
    {
        try {

            $user_id = CRUDBooster::myParentId();
            $all_data = $request::all();
            $checkedAll = $all_data['checkedAll'];
            $customer_ids = $all_data['customer_ids'];
            $params = $all_data['params'];

            if($checkedAll == 'true'){
                $customer_ids = $this->selectedCustomerIds($params, $user_id);
            }

            $customer_ids = explode(",", $customer_ids);

            $all_tags = [];

            $html = '';

            foreach($customer_ids as $customer){
                $customer_tags = DropfunnelCustomerTag::with('tag:id,tag')->has('tag')->where('customer_id', $customer);

                if (!CRUDBooster::isSuperadmin()) {
                    $customer_tags->whereHas('tag', function ($q) use ($user_id) {
                        $q->where('user_id', $user_id);
                    });
                }

                $customer_tags = $customer_tags->select('id', 'insert_type', 'score', 'tag_id')->get();
                if (!empty($customer_tags)) {
                    foreach($customer_tags as $tag){
                        if(!in_array($tag->tag_id, $all_tags)){
                            $all_tags[] = $tag->tag_id;
                            $html .= '<option value="' . $tag->tag_id . '">' . $tag->tag->tag . '</option>';
                        }
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => $html,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //fetch customer tags
    public function postBulkRemoveCustomerTags(Request $request)
    {
        try {

            $user_id = CRUDBooster::myParentId();
            $all_data = $request::all();
            $checkedAll = $all_data['checkedAll'];
            $customer_ids = $all_data['customer_ids'];
            $params = $all_data['params'];
            $tags = $all_data['tags'];

            if($checkedAll == 'true'){
                $customer_ids = $this->selectedCustomerIds($params, $user_id);
            }

            $customer_ids = explode(",", $customer_ids);

            CustomerBulkRemoveTagJob::dispatch($customer_ids, $tags);

            return response()->json([
                'success' => true,
                'message' => __('Tag remove process is running on background.'),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //fetch customer tags
    public function postBulkFetchCustomerDetails(Request $request)
    {
        try {

            $user_id = CRUDBooster::myParentId();
            $all_data = $request::all();
            $checkedAll = $all_data['checkedAll'];
            $customer_ids = $all_data['customer_ids'];
            $params = $all_data['params'];

            if($checkedAll == 'true'){
                $customer_ids = $this->selectedCustomerIds($params, $user_id);
            }

            $customer_ids = explode(",", $customer_ids);

            $html = '';

            $id_chunks = array_chunk($customer_ids, 500);

            $unmergeable_customers = [];
            $unmergeable = false;

            foreach($id_chunks as $chunk){
                $customerDets = NewCustomer::whereIn('id', $chunk)->select('id', 'full_name', 'cc_user_id')->get();
                foreach($customerDets as $customer){
                    if($customer->cc_user_id && !in_array($customer->cc_user_id, $unmergeable_customers)){
                        $unmergeable_customers[] = $customer->cc_user_id;
                    }
                    $html .= '<option value="' . $customer->id . '">' . $customer->full_name . '</option>';
                }
            }

            if(count($unmergeable_customers) > 1){
                $unmergeable = true;
            }

            return response()->json([
                'success' => true,
                'unmergeable' => $unmergeable,
                'data' => $html,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function postMergeAccounts(Request $request){
        try{
            $user_id = CRUDBooster::myParentId();
            $all_data = $request::all();
            $checkedAll = $all_data['checkedAll'];
            $customer_ids = $all_data['customer_ids'];
            $params = $all_data['params'];
            $primary_account = $all_data['primary_account'];

            if($checkedAll == 'true'){
                $customer_ids = $this->selectedCustomerIds($params, $user_id);
            }

            $customer_ids = explode(",", $customer_ids);

            $id_chunks = array_chunk($customer_ids, 500);

            foreach($id_chunks as $chunk){
                CustomerMergeJob::dispatch($chunk, $primary_account);
            }

            return response()->json([
                'success' => true,
                'message' => __('Contact merging process is running on background'),
            ]);
        } catch(Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getValidateEmail(Request $request){
        $all_data = $request::all();
        $emails = $all_data['emails'];

        $sessionId = \Str::random(40);
        session()->put('ui_validatior_trnx_id', $sessionId);
        request()->merge(['first_email_emailservices_session_id' => $sessionId]);

        $email_validations = [];

        foreach($emails as $email => $id){
            if(str_ends_with($email, 'fake.com')) {
                $email_validations[$id] = false;
            }
            else{
                $already_varified = DB::table('new_customers')->where('email', $email)->whereNotNull('is_valid_email')->value('is_valid_email');

                if(isset($already_varified)){
                    $email_validations[$id] = $already_varified;
                    \DB::table('new_customers')->where('email', $email)->whereNull('is_valid_email')->update(['is_valid_email' => $already_varified]);
                }
                else{
                    try{
                        app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);

                        $email_validations[$id] = true;
                        \DB::table('new_customers')->where('email', $email)->whereNull('is_valid_email')->update(['is_valid_email' => 1]);

                        app(\App\Services\UiValidation\UiValidation::class)->doAccounting();
                    } catch (\Exception $e) {
                        $email_validations[$id] = false;
                        \DB::table('new_customers')->where('email', $email)->whereNull('is_valid_email')->update(['is_valid_email' => 0]);
                    }
                }
            }
        }

        return $email_validations;
    }

    public function getTagList()
    {
        $data                     = [];
        $data['page_title']       = __('Tag Management');
        $data['page_description'] = trans('crudbooster.default_module_description');
        $user_id = CRUDBooster::myParentId();

        $tags = DropfunnelTag::where('dropfunnel_tags.user_id', $user_id)
                    ->leftJoin('dropfunnel_customer_tags as drop_cus_tags', 'dropfunnel_tags.id', '=', 'drop_cus_tags.tag_id')
                    // ->leftJoin('campaign_tags', 'dropfunnel_tags.id', '=', 'campaign_tags.tag_id')
                    ->leftJoin('new_customers', function ($join) use ($user_id) {
                        $join->on('drop_cus_tags.customer_id', '=', 'new_customers.id')
                            ->where('new_customers.user_id', $user_id);
                    })
                    ->select('dropfunnel_tags.id', 'tag', 'drop_cus_tags.insert_type', 'drop_cus_tags.id as drop_cus_tags_id',
                        DB::raw('count(DISTINCT drop_cus_tags.customer_id) as customer_count'),
                        DB::raw('sum(drop_cus_tags.score) as score_count')
                        // , DB::raw('count(DISTINCT campaign_tags.campaign_id) as campaign_count')
                    )
                    ->groupBy('dropfunnel_tags.id')
                    ->orderBy('tag', 'asc')
                    ->paginate(20);

        $data['results']   = $tags;
        $data['row_count'] = $tags->total();
        return view("admin.new_customer.tags.index", $data);
    }

    public function postTagList()
    {
        $request = $_REQUEST;
        $user_id = CRUDBooster::myParentId();

        $results = DropfunnelTag::where('dropfunnel_tags.user_id', $user_id)
                    ->leftJoin('dropfunnel_customer_tags as drop_cus_tags', 'dropfunnel_tags.id', '=', 'drop_cus_tags.tag_id')
                    // ->leftJoin('campaign_tags', 'dropfunnel_tags.id', '=', 'campaign_tags.tag_id')
                    ->leftJoin('new_customers', function ($join) use ($user_id) {
                        $join->on('drop_cus_tags.customer_id', '=', 'new_customers.id')
                            ->where('new_customers.user_id', $user_id);
                    });

        $q = $request['q'] ?? '';
        $results = $results->where(function($query) use ($q) {
            $query->where('tag', 'like', '%' . $q . '%');
        });

        if ($request['filter_by_tag']) {
            $results = $results->where('drop_cus_tags.insert_type', $request['filter_by_tag']);
        }

        $results = $results->select('dropfunnel_tags.id', 'tag', 'drop_cus_tags.insert_type', 'drop_cus_tags.id as drop_cus_tags_id',
                                DB::raw('count(DISTINCT drop_cus_tags.customer_id) as customer_count'),
                                DB::raw('sum(drop_cus_tags.score) as score_count')
                                // , DB::raw('count(DISTINCT campaign_tags.campaign_id) as campaign_count')
                            )
                            ->groupBy('dropfunnel_tags.id');

        if (isset($request['find_by_column']) && $request['find_by_column'] == 'tag_score' && !empty($request['find_by_value'])) {
            $results = $results->having('score_count', '=', $request['find_by_value']);
        }

        $order_by = $request['sort_by'] ?? 'asc';
        $results  = $results->orderBy('tag', $order_by);

        $limit   = isset($request['limit']) ? (int)$request['limit'] : 20;
        $results = $results->paginate($limit);

        $row_count          = $results->total();
        $table_content      = view('admin.new_customer.tags.parts.table_content', compact('results', 'row_count'))->render();
        $pagination_content = view('admin.new_customer.tags.parts.pagination_content', compact('results'))->render();

        return response()->json([
            'success'            => true,
            'table_content'      => $table_content,
            'pagination_content' => $pagination_content
        ]);
    }

    public function postAddTag(Request $request){
        $user_id  = CRUDBooster::myParentId();
        $all_data = $request::all();
        $tag      = $all_data['tag'];

        $tag_content = '';

        DB::beginTransaction();
        try {
            $success   = $has_issue = false;
            $message   = '';
            $data      = [];

            $tag_exists = DropfunnelTag::where('user_id', $user_id)->where('tag', $tag)->select('id')->first();
            if (empty($tag_exists)) {
                $tag_insertion = DropfunnelTag::create(['tag' => $tag, 'user_id' => $user_id]);

                if (!empty($tag_insertion->id)) {
                    $success    = true;

                    $results[0] = (object) [
                        'id'             => $tag_insertion->id,
                        'tag'            => $tag,
                        'insert_type'    => 4,
                        'customer_count' => 0,
                        'campaign_count' => 0,
                    ];

                    $row_count   = 1;
                    $tag_content = view('admin.new_customer.tags.parts.table_content', compact('results', 'row_count'))->render();
                    $data['tag_content'] = $tag_content;
                } else {
                    $has_issue = true;
                    $message   = __("Tag insertion failed.");
                }

            } else {
                $has_issue = true;
                $message   = __("Tag already exists! Please add different tag title.");
            }

            if ($success) {
                DB::commit();
                $message = __("Tag created successfully!");
            } else {
                if (empty($has_issue)) {
                    throw new Exception(__("Tag insertion failed."));
                }
            }

            return response()->json([
                'success' => true,
                'data'    => $data,
                'message' => $message,
                'has_issue' => $has_issue
            ], 200);

        } catch (Exception $ex) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => $ex->getMessage()
            ], 422);
        }
    }

    public function postTagDetails(Request $request){
        $all_data  = $request::all();
        $tag_id    = $all_data['tag_id'];
        $tag_title = $all_data['tag_title'];
        $user_id   = CRUDBooster::myParentId();

        try {
            $message   = '';
            $data      = [];

            $campaign_data = DropfunnelTag::where('dropfunnel_tags.id', $tag_id)
                                ->join('campaign_tags', 'dropfunnel_tags.id', '=', 'campaign_tags.tag_id')
                                ->join('email_marketings', 'campaign_tags.campaign_id', '=', 'email_marketings.id')
                                ->where('dropfunnel_tags.user_id', $user_id)
                                ->select('dropfunnel_tags.id', 'tag', 'campaign_id', 'campaign_name')
                                ->get();

            $customers_data = DropfunnelTag::where('dropfunnel_tags.id', $tag_id)
                                ->join('dropfunnel_customer_tags as drop_cus_tags', 'dropfunnel_tags.id', '=', 'drop_cus_tags.tag_id')
                                ->join('new_customers', 'drop_cus_tags.customer_id', '=', 'new_customers.id')
                                ->where('new_customers.user_id', $user_id)
                                ->select('dropfunnel_tags.id', 'tag', 'drop_cus_tags.insert_type', 'customer_id', 'full_name', 'drop_cus_tags.id as drop_cus_tags_id')
                                ->get();

            $data['tag_details'] = view('admin.new_customer.tags.parts.details_popup', compact('tag_title', 'campaign_data', 'customers_data'))->render();;

            return response()->json([
                'success' => true,
                'data'    => $data,
                'message' => $message,
            ], 200);

        } catch (Exception $ex) {
            return response()->json([
                'success' => false,
                'message' => $ex->getMessage()
            ], 422);
        }
    }

    public function getTagEdit($id)
    {
        $data = [];
        $data['page_title'] = __('Tag Modification');

        $data['tagDetails'] = DropfunnelTag::where('id', '=', $id)->first();
        $data['id'] = $id;
        return view('admin.new_customer.tags.edit', $data);
    }

    public function postTagEditSave($id)
    {
        $this->cbLoader();
        $url = g('return_url') ?: CRUDBooster::referer();

        $newTag = $_REQUEST['tag'];
        DropfunnelTag::where('id', $id)->update([
            'tag' => $newTag
        ]);

        CRUDBooster::redirect($url, trans("crudbooster.alert_update_data_success"), 'success');
    }

    public function getTagDelete($id)
    {
        $this->cbLoader();
        $url = g('return_url') ?: CRUDBooster::referer();

        if (!DropfunnelTag::where('user_id', CRUDBooster::myParentId())->where('id', '=', $id)->exists()) {
            CRUDBooster::redirect($url, 'Premission denied!', 'warning');
        }

        $this->updateCampaignsStatusByTag($id);

        DropfunnelTag::where('id', '=', $id)->delete();

        CRUDBooster::redirect($url, trans("crudbooster.alert_delete_data_success"), 'success');
    }

    public function postDeleteExistingTagsBulk(Request $request){
        try {
            $success   = $has_issue = false;
            $message   = '';
            $data      = [];

            $user_id    = CRUDBooster::myParentId();
            $all_data   = $request::all();
            $checkedAll = $all_data['checkedAll'];
            $tag_ids    = $all_data['tag_ids'];
            $params     = $all_data['params'];

            if($checkedAll == 'true'){
                $tag_ids = $this->selectedTagIds($params, $user_id);
            }

            $tag_ids = explode(",", $tag_ids);

            $id_chunks = array_chunk($tag_ids, 500);
            foreach($id_chunks as $chunk){
                foreach ($chunk as $tag_id) {
                    $this->updateCampaignsStatusByTag($tag_id);
                }

                $deletion = DropfunnelTag::whereIn('id', $chunk)->delete();
                $message  = __("Selected Tags Deleted Successfully!");
            }

            return response()->json([
                'success' => true,
                'data'    => $data,
                'message' => $message,
            ]);
        } catch(Exception $ex){
            return response()->json([
                'success' => false,
                'message' => $ex->getMessage()
            ], 422);
        }
    }

    public function selectedTagIds($params, $user_id) {
        $results = DropfunnelTag::where('user_id', $user_id)
                    ->leftJoin('dropfunnel_customer_tags as drop_cus_tags', 'dropfunnel_tags.id', '=', 'drop_cus_tags.tag_id')
                    ->leftJoin('campaign_tags', 'dropfunnel_tags.id', '=', 'campaign_tags.tag_id');

        if (!empty($params['q'])) {
            $results = $results->where('tag', 'like', '%' . $params['q'] . '%');
        }

        if (!empty($params['filter_by_tag'])) {
            $results = $results->where('insert_type', $params['filter_by_tag']);
        }

        $order_by = $params['sort_by'] ?? 'asc';
        $results  = $results->orderBy('tag', $order_by);

        $tag_ids = $results->pluck('dropfunnel_tags.id')->toArray();
        $tag_ids = implode(",", $tag_ids);

        return $tag_ids;
    }

    public function updateCampaignsStatusByTag($tag_id = 0) {
        $campaign_ids = CampainTag::where('tag_id', $tag_id)->pluck('campaign_id');

        if (!empty($campaign_ids)) {
            foreach ($campaign_ids as $campaign_id) {
                $campaign_info = EmailMarketing::find($campaign_id);
                $campaign_info->status = 0;
                $campaign_info->active_at_the_moment = 0;
                $campaign_info->save();
            }
        }
    }

    public function getAssignDtTagFix()
    {
        // $tags = DB::table('dropfunnel_tags')->where('user_id', 2455)
        // ->whereIn('tag', ['Account deleted', 'Inactive for 30 days', 'Inactive for 60 days', 'Inactive for 90 days'])
        // ->pluck('id', 'id')
        // ->toArray();

        // $customers = DB::table('dropfunnel_customer_tags')
        // ->whereIntegerInRaw('tag_id', $tags)
        // ->pluck('customer_id', 'customer_id')
        // ->toArray();

        // foreach($customers as $customer_id)
        // {

        //     $user_id = DB::table('new_customers')->where('user_id', 2455)->where('id', $customer_id)->value('cc_user_id');
        //     if(empty($user_id)) continue;
        //     if($user_id && DB::table('cms_users')->whereNotNull('email_verified_at')->where('id', $user_id)->value('id')) continue;

        //     if(DB::table('new_orders')->where('drm_customer_id', $customer_id)->exists()) continue;
        //     if(DB::table('new_orders')->where('drm_customer_id', $customer_id)->exists()) continue;

        //     DB::table('new_customers')->where('user_id', 2455)->where('id', $customer_id)->delete();
        //     DB::table('dropfunnel_customer_tags')->whereIntegerInRaw('tag_id', $tags)->where('customer_id', $customer_id)->delete();
        // }
    }


    public function getTestRedis(Request $request)
    {
        try
        {
            $redis = Redis::connection();
            dump($redis);

            Redis::set('name', 'Dropmatix');

            $values = Redis::lrange('names', 5, 10);
            dump($values);

            return response('redis working');
        } catch (\Exception $e)
        {
            dump($e);
            return response('error connection redis');
        }
    }



    public function lockUnlockCustomer($user_id, $status='')
    {
        try{

            $plan = app('App\Services\UserService')->userPlanStatus($user_id);
            // $plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);

            $user_account_status = 1;

            //lock
            if($status == 'inkasso' && !empty($user_id)){
                DrmUserLockUnlock::updateOrCreate([
                    'user_id' => $user_id] ,[
                    'status' => 1, //for lock status 1
                    // 'created_at' => now(),
                    // 'updated_at' => now()
                ]);


                // TODO:: Disconnect products
                \App\Jobs\SuspendUser\DisconnectProducts::dispatch($user_id);

                $user_account_status = 1;

                $user = User::where('id', $user_id)->first();
                resolve(\App\Services\DropCampus\DropmatixCampus::class)->deactivateDropmatixCampusUser($user);
            }else{
                //unlock
                $user_orders = DB::table('new_orders')->where('cms_client', $user_id)->where('status', 'inkasso')->exists();

                if(!$user_orders && in_array($plan['plan'], ['Purchased','Assigned','Trial','Unlimited'])){

                    DrmUserLockUnlock::updateOrCreate([
                        'user_id' => $user_id] ,[
                        'status' => 0, //for unlock status 0
                        // 'created_at' => now(),
                        // 'updated_at' => now()
                    ]);

                    $user = User::where('id', $user_id)->first();
                    resolve(\App\Services\DropCampus\DropmatixCampus::class)->reactivateDropmatixCampusUser($user);
                }

                $user_account_status = 0;
            }

            $user_has_dt_shop = DB::table('shops')->where([
                'channel' => Channel::DROPTIENDA,
                'user_id' => $user_id
            ])->exists();

            if($user_has_dt_shop){
                // DtUserLockUnlockJob::dispatch($user_id, $user_account_status);  //commented due to background server is slow to hit the dt server
                app('App\Http\Controllers\AdminDtTariffController')->dtAccountLockUnlock($user_id, $user_account_status);
            }

        }catch(\Exception $e){}

    }

    public function getTagSuggestion()
    {
        $search_string = $_REQUEST['search'] ?? '';
        $user_id = CRUDBooster::myParentId();

        $user_all_tags = $this->findUserTagsByInputString($user_id, $search_string);

        return response()->json([
            'success' => true,
            'tags' => $user_all_tags
        ], 200);
    }

    public function findUserTagsByInputString($user_id, $search_string): array
    {
        return DropfunnelTag::where('dropfunnel_tags.user_id', $user_id)
        ->where('tag', 'like', '%' . $search_string . '%')
        ->orderBy('tag', 'asc')
        ->pluck('tag')
        ->toArray();
    }

    public function getTagFilterValue()
    {
        $user_id = CRUDBooster::myParentId();
        $search_by = $_GET['search_column'];
        $html = '';

        $filter_values = $this->findFilterValue($user_id, $search_by);

        if($search_by == 'tag_score' && !empty($filter_values)){
            foreach($filter_values as $value){
                $html .= '<option value="'.$value.'">'.$value.'</option>';
            }
        }

        return response()->json([
            'success' => true,
            'data' => $html
        ], 200);
    }

    public function findFilterValue($user_id, $filter_by)
    {
        $filter_value = DropfunnelTag::where('dropfunnel_tags.user_id', $user_id)
        ->leftJoin('dropfunnel_customer_tags as drop_cus_tags', 'dropfunnel_tags.id', '=', 'drop_cus_tags.tag_id');

        $select_query = $group_query = $pluck_query = 'drop_cus_tags.customer_id';

        if($filter_by == 'tag_score'){
            $select_query =  DB::raw('sum(drop_cus_tags.score) as score_count');
            $group_query =  'dropfunnel_tags.id';
            $pluck_query = 'score_count';
        }

        $filter_value = $filter_value->select($select_query)->groupBy($group_query)->orderBy($pluck_query)->pluck($pluck_query)->unique()->toArray();

        return $filter_value;
    }

    public function updateHeaderVideoPlayButton()
    {
        try{
            \App\Option::updateOrCreate([
                'user_id' => CRUDBooster::myParentId(),
                'option_key' => 'video_play_switch',
                'option_group' => 'video_play_switch'
            ],
            [
                'option_value' => $_REQUEST['is_checked'],
            ]);
            return response()->json(['success' => true], 200);

        } catch(\Exception $e){
            return response()->json([
                'success' => true,
                'message' => 'Something went wrong!'
            ], 400);
        }
    }

    public function addCustomerLead(LaravelRequest $request)
    {
        $rules = [
            'email' => 'required|email',
        ];
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->errors()->first()]);
        }

        try {
            $req = $request->all();

            $password = $request->header('userPassToken');
            $token = $request->header('userToken');

            $shop = Shop::whereNotNull('username')->whereNotNull('password')
                ->where('username', $token)->where('password', $password)
                ->first();
            $user_id = $req['user_id'] = $shop->user_id;

            if ($shop) {
                $customer_tag_suggest = isset($req['customer_tag_suggest']) ? json_decode($req['customer_tag_suggest']) : [];
                $customer_tag_suggest = !is_array($customer_tag_suggest) ? json_decode($req['customer_tag_suggest']) : $customer_tag_suggest;
                
                $customer_info = [
                    "customer_full_name" => $req['name'] ?? '',
                    "company_name" => $req['company_name'] ?? '',
                    "currency" => 'EUR',
                    'email' => $req['email'] ?? '',
                    'address' => $req['address'] ?? '',
                    'country' => $req['country'] ?? '',
                    'default_language' => 'DE',
                    'zip_code' => $req['zip_code'] ?? '',
                    'state' => $req['state'] ?? '',
                    'city' => $req['city'] ?? '',
                    'phone' => $req['phone'] ?? '',
                    'insert_type' => '12',
                    'status' => 1,
                    'source' => '10',
                    'user_id' => $user_id,
                    'customer_tag_suggest' => $customer_tag_suggest,
                ];

                $customer_id = $this->add_customer($customer_info);

                $customer_info = [
                    'customer_full_name' => $req['name'] ?? '',
                    'company_name' => $req['customer_company_name'] ?? '',
                    'city' => $req['customer_city'] ?? '',
                    'zip_code' => $req['zip_code'] ?? '',
                    'state' => $req['state'] ?? '',
                    'country' => $req['country'] ?? '',
                    'address' => $req['address'] ?? '',
                    'vat_number' => $req['vat_number'] ?? '',
                    'tax_number' => $req['tax_number'] ?? '',
    
                    // shipping
                    'street_shipping' => $req['shipping_street'] ?? '',
                    'city_shipping' => $req['shipping_city'] ?? '',
                    'state_shipping' => $req['shipping_state'] ?? '',
                    'zipcode_shipping' => $req['shipping_zip_code'] ?? '',
                    'country_shipping' => $req['shipping_country'] ?? '',
                ];
    
                //billing
                if (isset($req['is_same_address']) && !empty($req['is_same_address'])) {
                    $customer_info['street_billing'] = $req['shipping_street'] ?? '';
                    $customer_info['city_billing'] = $req['shipping_city'] ?? '';
                    $customer_info['state_billing'] = $req['shipping_state'] ?? '';
                    $customer_info['zipcode_billing'] = $req['shipping_zip_code'] ?? '';
                    $customer_info['country_billing'] = $req['shipping_country'] ?? '';
                } else {
                    $customer_info['street_billing'] = $req['billing_street'] ?? '';
                    $customer_info['city_billing'] = $req['billing_city'] ?? '';
                    $customer_info['state_billing'] = $req['billing_state'] ?? '';
                    $customer_info['zipcode_billing'] = $req['billing_zip_code'] ?? '';
                    $customer_info['country_billing'] = $req['billing_country'] ?? '';
                }
    
                if (isset($req['is_same_address'])) {
                    $customer_info['is_same_address'] = true;
                }
    
                $this->update_customer($customer_info, $customer_id);

                return response()->json(['success' => false, 'message' => 'Customer added successfully!']);
            } else {
                return response()->json(['success' => false, 'message' => 'Shop does not exists!']);
            }
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => 'Something went wrong!',]);
        }
    }

}
