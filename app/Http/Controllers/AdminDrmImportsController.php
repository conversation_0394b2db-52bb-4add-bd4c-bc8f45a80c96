<?php namespace App\Http\Controllers;

use App\Jobs\ChannelManager\AutoTransfer;
use CB;
use DB;
use Request;
use Session;
use App\User;
use App\CsvHeader;
use App\DrmImport;
use App\Enums\Tax;
use Carbon\Carbon;
use App\DrmProduct;
use App\Enums\Apps;
use App\Enums\Product;
use App\TmpDrmProduct;
use League\Csv\Writer;
use App\DeliveryCompany;
use App\DrmProductField;
use App\Helper\AppStore;
use App\DrmImportTemplate;
use App\Enums\ImportFields;
use App\Models\DrmCategory;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Jobs\ImportToGambio;
use DataTables as DataTables;
use App\ImportPlanGetDiscounts;
use App\Jobs\ProcessTranslation;
use App\Models\Import\FileSource;
use App\Services\AppStoreService;
use App\Models\DRMProductCategory;
use App\Services\DateTime\DateTime;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\LazyCollection;
use App\Jobs\Supplier\DeleteImportFeed;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Services\Keepa\Automagic;
use App\Services\Modules\Import\ImportService;
use App\Services\Modules\Export\ExportServices;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\IndustryTemplate;
use App\Jobs\DrmFeedAutomagic;
use App\MarketplaceProducts;
use App\Services\ProductApi\TransferProduct;
use App\TrackAutomagic;
use App\DropmatixProductBrand;
use App\CustomIndustryTemplate;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use App\Enums\DroptiendaPlan;
use App\Services\DRM\DRMService;

class AdminDrmImportsController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public $all_translation = [];
    public $all_products = [];
    public $update_status_all = [];
    public $import_categories = [];
    public $table;
    public $insertArray = [];
    public $existing_products = [];
    public $profit_margins;
    public $calc_config;
    public $all_categories;
    public $fallback;
    public $errors;
    public $ean_field;
    public $ean;
    public $deleted_products = [];
    public $filter = [];
    public $import_check;
    public $app_interval;
    public $import;
    public $image_changed = [];
    public $changed_products = [];
    public $settings;

    public function cbInit()
    {
        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "csv_file_name";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = false;
        $this->button_edit = false;
        $this->button_delete = false;
        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "drm_imports";
        $this->app_interval = DRM_Inverval_App_Minute(CRUDBooster::myId(), config('global.csv_interval_app_id'));
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "Supplier", "name" => "delivery_company_id", "join" => "delivery_companies,name"];
        $this->col[] = ["label" => " Name", "name" => "csv_file_name"];
        $this->col[] = ["label" => "Country", "name" => "country_id", "join" => "countries,name"];

        $this->col[] = ["label" => "File Type", "name" => "type"];

        // if( (isLocal() && !in_array(CRUDBooster::myParentId(), [179, 212])) || (in_array(CRUDBooster::myParentId(), [62, 98])) ){
        //     $this->col[] = ["label" => "Download Feed", "name" => "id"];
        // }else{
            $this->col[] = ["label" => "Download Feed", "name" => "csv_file_path"];
        // }

        $this->col[] = ["label" => "Import Time", "name" => "created_at"];
        $this->col[] = ["label" => "Total Products", "name" => "(SELECT COUNT(*) FROM drm_products WHERE drm_import_id = drm_imports.id) as total"];
        # END COLUMNS DO NOT REMOVE THIS LINE
        $this->col[] = ["label" => "Allow New Products", "name" => "allow_new"];

        $this->col[] = ["label" => "Last Update", "name" => "(SELECT url FROM manual_update_mapping WHERE manual_update_mapping.drm_import_id = drm_imports.id) as manual_url"];

        $this->col[] = ["label" => "Update Type", "name" => "auto_update"];

        $this->col[] = ["label" => "Next Update", "name" => "last_update"];

        // $this->col[] = ["label"=>"Quantity","name"=>"unlimited_quantity"];

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        // $this->form[] = ['label'=>'Delivery Company Id','name'=>'delivery_company_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'delivery_companies,id'];
        // $this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'user_csv_header_values,id'];
        // $this->form[] = ['label'=>'Csv File Name','name'=>'csv_file_name','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Country Id','name'=>'country_id','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'countries,id'];
        // $this->form[] = ['label'=>'Delimiter','name'=>'delimiter','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Language Id','name'=>'language_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'languages,id'];
        // $this->form[] = ['label'=>'Delivery Time','name'=>'delivery_time','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Csv File Path','name'=>'csv_file_path','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Max Price','name'=>'max_price','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Min Price','name'=>'min_price','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'File Url','name'=>'file_url','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Type','name'=>'type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Csv Headers','name'=>'csv_headers','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Demo Data','name'=>'demo_data','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Fielter Min','name'=>'fielter_min','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Fielter Max','name'=>'fielter_max','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Fielter Names','name'=>'fielter_names','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Fielter Categories','name'=>'fielter_categories','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Overwrite','name'=>'overwrite','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Import Finished','name'=>'import_finished','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
        # END FORM DO NOT REMOVE THIS LINE

        $this->sub_module = array();
        $this->addaction = array();
        $this->addaction[] = ['url' => '[id]'];
        $this->button_selected = array();
        $this->alert = array();

        $this->index_button = array();

        $sync_import_app_url = checkUserPurchasedApp(config('global.csv_interval_app_id'), CRUDBooster::myId()) ? 'admin/update_app_form' : 'admin/app_form';
//        $this->index_button[] = ['label' => 'Import Sync Interval: ' . app_user_interval(app_user_plan_id(CRUDBooster::myId(), config('global.csv_interval_app_id'))), 'color' => 'btn interval_status_btn btn-modal btn-drm', 'interval_app' => true, "icon" => "fa fa-refresh fa-spin", 'data-id' => config('global.csv_interval_app_id'), 'url' => '#', 'data-url' => url($sync_import_app_url)];


        $this->table_row_color = array();
        $this->index_statistic = array();

        $this->script_js = '
            $(".btn_file_download").click(function(e) {
                e.preventDefault();

                let import_id = $(this).data("import-id");

                $.ajax({
                    url: "'.CRUDBooster::mainpath('fetch-imported-file').'",
                    method: "POST",
                    data: {import_id:import_id},
                    success:function(response){
                        if(response.success){
                            let csv_files_detail_info = response.data;

                            $("#imported_csv_show_modal .modal-body").html(csv_files_detail_info);
                            $("#imported_csv_show_modal").modal("show");
                        }
                    }
                });

            });
        ';

        $this->pre_index_html = null;

        $this->post_index_html = '
            <div class="modal fade" tabindex="-1" role="dialog" id="imported_csv_show_modal">
                <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                    <h4 class="modal-title">CSV File List</h4>
                    </div>

                    <div class="modal-body">

                    </div>

                    <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">'.__("order.CLOSE").'</button>
                    </div>

                </div>
                </div>
            </div>
        ';

        $this->load_js = array();

        $this->style_css = "
                .button_action>a {
                    margin-bottom: 5px;
                }
	        ";
        $this->load_css = array();
        $this->load_css[] = asset('css/drm-import.css');


    }

    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here
    }

    public function hook_query_index(&$query)
    {

        if (!CRUDBooster::isSuperadmin()) {
            $user_id = CRUDBooster::myParentId();
            $query->where("drm_imports.user_id", $user_id);
            //$query->where("drm_imports.import_finished",'1');
        }

        $query->where("drm_imports.import_finished", 1);

        // if(isLocal() || in_array(CRUDBooster::myParentId(), [62, 98, 179, 212])){
            $query->where("drm_imports.csv_file_path", "<>", "");
        // }

        if (isset($_GET['filter'])) {
            $query->where("drm_imports.delivery_company_id", $_GET['filter']);
        }
        $query->addSelect('drm_imports.type');
        $query->addSelect('drm_imports.auto_update');
        $query->addSelect('drm_imports.file_url');
        $query->addSelect('drm_imports.latest_file');
    }


    public function downloadCsv($drm_import_id){
        // echo $drm_import_id;
        # generate csv fo0r manual propducts
        $header = ['id','drm_import_id','user_id','delivery_company_id','country_id','language_id','name','item_number','ean','description','image','ek_price','vk_price','vat','stock','category','item_weight','item_size','item_color','note','production_year','brand','materials','tags','update_enabled','created_at','updated_at','status','gender','deleted_at','update_status','ean_field','old_stock','stock_updated_at','delivery_days','cloned_from','original_images','amazon_status','has_bundle','variants','uvp','original_ek','industry_template_data','title','short_description','marketplace_supplier_id','marketplace_product_id','marketplace_shipping_method','shipping_cost','tax','tax_type'];
        $products = DrmProduct::where([
            'drm_import_id' => $drm_import_id
        ])->get()->toArray();
        $lang = 'de';
        foreach ($products as $key => $product) {
            $products[$key]['description'] = $product['description'][$lang];
            $products[$key]['title'] = $product['title'][$lang];
            $products[$key]['image'] = implode(",", $product['image']);
            unset($products[$key]['update_status']);
        }

        //load the CSV document from a string
        $csv = Writer::createFromString();
        //insert the header
        $csv->insertOne($header);
        //insert all the records
        $csv->insertAll($products);
        $csv->getContent();


        $filename = date('Y-m-d') . "_" . $drm_import_id . '.csv';
        return response((string) $csv, 200, [
            'Content-Type' => 'text/csv',
            'Content-Transfer-Encoding' => 'utf-8',
            'Content-Disposition' => 'attachment; filename="'.$filename.'"',
        ]);

        $csv->output($drm_import_id.'.csv');
    }


    public function hook_row_index($column_index, &$column_value)
    {
        $user_id = CRUDBooster::myParentId();
        if ($column_index == 12) {
            $explode = explode("title='' onclick='' href=", $column_value);
            $explode_again = explode("target='_self'><i class=''></i> </a>&nbsp;", $explode[1]);
            $id = $explode_again[0];

            $str = str_replace("'", "", $id);
            $str = str_replace(" ", "", $str);
            $id = (int)$str;

            $import = DB::table('drm_imports')->where('id', $id)->first()->status;
            $site_url = url('');

            $function = "modal($id)";
            $template_btn = "";
            $template_purchased = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, $user_id);
            // if (app()->environment('development')) {
            // 	$template_purchased = true;
            // }
            // 	<a class='btn btn-xs btn-primary btn-filter' title='Customize Filter'
            // href=".CRUDBooster::mainpath('manage-filter/'.$id)."><i class='fa fa-filter'></i> Customize Filter</a>&nbsp;
            // $filter_btn = "";
            // if(isLocal()){
            $filter_btn = "<a class='btn btn-xs btn-primary btn-filter' title='Customize Filter'
					href=" . CRUDBooster::mainpath('manage-filter/' . $id) . "><i class='fa fa-filter'></i> Customize Filter</a>&nbsp;";
            // }
            if ($template_purchased) {
                $template_btn = "&nbsp;
							<a class='btn btn-xs btn-warning' title='Product Template' href=" . CRUDBooster::mainpath('product-template/') . $id . " target='_self'><i class='fa fa-sync'></i> Product Template</a>&nbsp;";
            }
            $type = DB::table('drm_imports')->where('id', $id)->first()->type;

            if (in_array($type,['url','ftp'])) {
                $column_value = "<div class='button_action' style='text-align:right'>
												$template_btn
												$filter_btn
                        <a class='btn btn-xs btn-primary btn-detail' title='Update CSV URL'
        	            href=" . CRUDBooster::mainpath('update-csv?id=' . $id) . "><i class='fa fa-edit'></i> Update CSV URL</a>&nbsp;<a class='btn btn-xs btn-warning' title='Delete'
					    onclick=$function;
        	            href='#' onclick= $function ><i class='fa fa-trash'></i></a>
        	            </div>";
            } else {
                $column_value = "<div class='button_action' style='text-align:right'>$template_btn
												<a class='btn btn-xs btn-primary btn-detail' title='Manual Update CSV'
        	            href=" . CRUDBooster::mainpath('manual-update-csv?id=') . $id . "><i class='fa fa-edit'></i> Manual Update CSV</a>&nbsp;<a class='btn btn-xs btn-warning' title='Delete'
					    onclick=$function;
        	            href='#' onclick= $function ><i class='fa fa-trash'></i></a>
        	            </div>";
            }

        }


        // <a class='btn btn-xs btn-success' title='Translation' onclick='' href='$site_url/admin/custom_import/do-translation/$id' target='_self'><i class='fa fa-check'></i> Translation</a>&nbsp;

// 			if($column_index == 5){
// 			    $import = DB::table('')
// 			}

        if ($column_index == 5) {
            // if( (isLocal() && !in_array(CRUDBooster::myParentId(), [179, 212])) || (in_array(CRUDBooster::myParentId(), [62, 98])) ){

            //     $column_value = '<a class="btn btn-xs btn-primary btn_file_download" href="javascript:void(0)" title="Download File" data-import-id="'.$column_value.'"><i class="fa fa-download"></i> Download</a>';

            // }else{
            $column_value = Storage::disk('spaces')->url($column_value);
            $html = '<a class="btn btn-xs btn-primary" href="' . $column_value . '?download=1" target="_blank" title="Download File"><i class="fa fa-download"></i> Download</a>';
            $column_value = $html;
            // }
        }

    }

    public function hook_before_add(&$postdata)
    {
        //Your code here
    }

    public function hook_after_add($id)
    {
        //Your code here

    }

    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    public function hook_after_edit($id)
    {
        //Your code here

    }

    public function hook_before_delete($id)
    {
        //Your code here

    }

    public function hook_after_delete($id)
    {
        //Your code here

    }

    public function postFeedAllowNew()
    {
        $id = $_REQUEST['id'];
        $action = (int)$_REQUEST['action'];
        DB::table('drm_imports')->where('id', $id)->update(['allow_new' => $action]);
    }

    public function getIndex()
    {
        redirectToV2('/suppliers/feeds');

        
        $this->cbLoader();

        $module = CRUDBooster::getCurrentModule();

        if (!CRUDBooster::isView() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        if (Request::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
            if (Request::get('foreign_key')) {
                $data['parent_field'] = Request::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($parent_field) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $parent_field) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        $data['page_title'] = $module->name;
        $data['page_description'] = trans('crudbooster.default_module_description');
        $data['date_candidate'] = $this->date_candidate;
        $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::table($this->table)->select(DB::raw($this->table . "." . $this->primary_key));

        if (Request::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . Request::get('foreign_key'), Request::get('parent_id'));
        }

        $this->hook_query_index($result);

        if (in_array('deleted_at', $table_columns)) {
            $result->where($this->table . '.deleted_at', null);
        }

        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;
        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {

                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if (Request::get('q')) {
            $result->where(function ($w) use ($columns_table, $request) {
                foreach ($columns_table as $col) {
                    if (!$col['field_with']) {
                        continue;
                    }
                    if ($col['is_subquery']) {
                        continue;
                    }
                    $w->orwhere($col['field_with'], "like", "%" . Request::get("q") . "%");
                }
            });
        }

        if (Request::get('where')) {
            foreach (Request::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }

        $filter_is_orderby = false;
        if (Request::get('filter_column')) {

            $filter_column = Request::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {
                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];

                if ($sorting != '') {
                    if ($key) {
                        $result->orderby($key, $sorting);
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];

        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(Request::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (Request::get('page')) ? Request::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            if ($this->button_bulk_action) {
                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];

                if (isset($col['image'])) {
                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('vendor/crudbooster/avatar.jpg') . "'><img width='40px' height='40px' src='" . asset('vendor/crudbooster/avatar.jpg') . "'/></a>";
                    } else {
                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='" . $pic . "'><img width='40px' height='40px' src='" . $pic . "'/></a>";
                    }
                }

                if (@$col['download']) {
                    header("Content-Type: application/force-download");
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action):
                $button_action_style = $this->button_action_style;
                $html_content[] = "<div class='button_action' style='text-align:right'>" . view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render() . "</div>";

            endif;//button_table_action

            foreach ($html_content as $i => $v) {
                $this->hook_row_index($i, $v);

                if ($i == 5) {
                    if (in_array($row->type,['url','ftp'])) {
                        $column_value = Storage::disk('spaces')->url($row->latest_file ?? $row->csv_file_path);
                        $v = '<a class="btn btn-xs btn-primary" href="' . $column_value . '?download=1" target="_blank" title="Download File"><i class="fa fa-download"></i> Download</a>';
                    }
                }


                if ($i == 8) {
                    if (in_array($row->type,['url','ftp'])) {
                        if ($v == 1) {
                            $v = '<input type="checkbox" id="allow_new_' . $row->{$tablePK} . '" onchange="allowNew(' . $row->{$tablePK} . ')" data-toggle="toggle" data-size="mini" style="opacity:0;" checked>';
                        } else {
                            $v = '<input type="checkbox" id="allow_new_' . $row->{$tablePK} . '" onchange="allowNew(' . $row->{$tablePK} . ')" data-toggle="toggle" data-size="mini" style="opacity:0;">';
                        }
                    } else {
                        $v = "N/A";
                    }
                }

                if ($i == 9) {
                    $sync_time = $row->last_update;
                    if ($row->type == "file" && $row->auto_update) {
                        $v = '<button type="button" class="btn btn-xs btn-info clipboard" data-clipboard-text="' . $v . '">Copy URL</button><br /><center><span style="color:#f09300">' . $sync_time . '</span></center>';
                    } elseif (in_array($row->type,['url','ftp'])) {
                        $v = '<button type="button" class="btn btn-xs btn-info clipboard" data-clipboard-text="' . $row->file_url . '">Copy URL</button><br /><center><span style="color:#f09300">' . $sync_time . '</span></center>';
                    } else {
                        if ($row->latest_file != null) {
                            $file_url = Storage::disk('spaces')->url($row->latest_file);
                            $v = '<a href="' . $file_url . '" target="_blank"><button type="button" class="btn btn-xs btn-info"><i class="fa fa-download"></i> Last Update File</button></a><br />
												<center><span style="color:#f09300">' . $sync_time . '</span></center>';
                        } else {
                            $v = "N/A";
                        }
                    }
                }

                if ($i == 10) {
                    if ($row->type == "file" && $row->auto_update) {
                        $v = 'Multiple';
                    } elseif (in_array($row->type,['url','ftp'])) {
                        $v = 'Multiple';
                    } else {
                        $v = "Single";
                    }
                }


                if ($i == 11) {
                    if (($row->type == "file" && $row->auto_update) || in_array($row->type,['url','ftp'])) {
                        if (!$v) {
                            $v = Carbon::now()->toDateTimeString();
                        }

                        $time_str = Carbon::createFromTimestamp(strtotime($v))->addMinutes($this->app_interval)->toDateTimeString();
                        $v = Carbon::createFromTimestamp(strtotime($v))->addMinutes($this->app_interval)->diffForHumans();

                        $now = Carbon::now()->toDateTimeString();

                        if (strtotime($now) > strtotime($time_str)) {
                            $v = Carbon::now()->toDateTimeString();
                            $v = Carbon::createFromTimestamp(strtotime($time_str))->addMinutes($this->app_interval)->diffForHumans();
                        }
                    } else {
                        $v = "N/A";
                    }
                }


                $html_content[$i] = $v;
            }

            // $first_import = DB::table('drm_import_sync')
            // ->select('drm_import_sync.created_at')
            // ->join('drm_imports','drm_imports.id','=','drm_import_sync.drm_import_id')
            // ->where('user_id',CRUDBooster::myId())
            // ->where('drm_imports.import_finished',1)
            // ->where(function($q){
            // 	$q->where('type','file');
            // 	$q->where('auto_update',1);
            // 	$q->orWhere('type','url');
            // })
            // ->oldest()->first()->created_at;
            // if($first_import){
            // 	$data['next_update'] = Carbon::createFromTimestamp(strtotime($first_import))->addMinutes($this->app_interval)->diffForHumans();
            // }
            // else {
            // 	$data['next_update'] = false;
            // }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

        $data['html_contents'] = $html_contents;

        return view("admin.drm_import.index", $data);
    }


    public function getDoTranslation($id)
    {

        //Create an Auth
        if (!CRUDBooster::isCreate() && $this->global_privilege == FALSE || $this->button_add == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $userLang = DB::table('drm_imports')->select('language_id')->where('id', $id)->first();

        $products = DB::table('drm_products')->where('drm_import_id', $id)->get();
        $data['products'] = $products;
        $charecter = 0;

        foreach ($products as $product) {
            $name = $product->name;
            $description = $product->description;
            $total = strlen($name) + strlen(($description));
            $charecter += $total;
        }

        $data = [];
        $data['page_title'] = 'Page Translation';
        $data['id'] = $id;
        $data['default_lang'] = DB::table('languages')->where('id', $userLang->language_id)->first();
        $data['languages'] = DB::table('languages')->where('id', '<>', $userLang->language_id)->get();

        $data['charecter'] = $charecter;

        //Please use cbView method instead view method from laravel
        $this->cbView('admin.drm.translation', $data);
    }

    public function getManageFilter($id)
    {
        $filter = DB::table('drm_import_filter')->where('drm_import_id', $id)->first();
        $data['import'] = DrmImport::find($id);
        $data['filter'] = $filter;

        $category_ids = json_decode($data['import']->category_ids);
        $country = DB::table('countries')->where('id', $data['import']->country_id)->first();

        $data['categories'] = [];
        if (is_array($category_ids)) {
            $data['categories'] = DB::table('drm_category')
                ->select('drm_category.category_name_' . $country->language_shortcode . ' as category_name', 'drm_category.id')
                ->whereIn('id', $category_ids)
                ->get();
        }

        return view('admin.drm_import.after_import.manage_filter', $data);
    }

    public function postRemoveFilter()
    {
        $import_id = $_REQUEST['drm_import_id'];
        $field = $_REQUEST['field'];
        $value = $_REQUEST['value'];
        $stock_operator = $_REQUEST['operator'];

        $filter = DB::table('drm_import_filter')
            ->select('drm_import_filter.*')
            ->join('drm_imports', 'drm_imports.id', '=', 'drm_import_filter.drm_import_id')
            ->where(['drm_import_id' => $import_id, 'drm_imports.user_id' => CRUDBooster::myParentId()])->first();

        $filters = json_decode($filter->filter ?? '[]', true);

        if (($field == "ean" || $field == "category" || $field == "brand" || $field == "gender" || $field == "materials" || $field == "status") && is_array($filters[$field])) {
            if (($key = array_search($value, $filters[$field])) !== false) {
                unset($filters[$field][$key]);
                if (!count($filters[$field])) {
                    unset($filters[$field]);
                }
            }
        }

        if ($field == "price_below") {
            unset($filters['price_below']);
        }

        if ($field == "price_more_than") {
            unset($filters['price_more_than']);
        }

        if ($field == "stock") {
            if (($key = array_search($stock_operator, $filters['stock_operator'])) !== false) {
                unset($filters['stock'][$key]);
                unset($filters['stock_operator'][$key]);

                // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){
                if($filters['stock']){
                    $filters['stock'] = array_values($filters['stock']);
                }
                if($filters['stock_operator']){
                    $filters['stock_operator'] = array_values($filters['stock_operator']);
                }
                // }

                if (!count($filters['stock'])) {
                    unset($filters['stock']);
                    unset($filters['stock_operator']);
                }
            }
        }
        $json = json_encode($filters);

        DB::table('drm_import_filter')->where('id', $filter->id)->update(['filter' => $json]);
        return response()->json(['status' => "UPDATED"], 200);
    }


    public function postFilterStatus()
    {
        $status = $_REQUEST['status'];
        $import_id = $_REQUEST['drm_import_id'];

        $filter = DB::table('drm_import_filter')->where(['drm_import_id' => $import_id])->first();

        DB::table('drm_import_filter')->updateOrInsert(
            ['drm_import_id' => $import_id],
            ['filter' => $filter->filter, 'blacklist' => (int)$status]
        );

    }

    public function getManageShop($id)
    {

        //Create an Auth
        if (!CRUDBooster::isCreate() && $this->global_privilege == FALSE || $this->button_add == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = 'Manage Shop';
        $shops = \App\Shop::where('user_id', $user_id = CRUDBooster::myId())->get();
        $languages = DB::table('languages')->get();
        $data['languages'] = $languages;
        $data['shops'] = $shops;
        $data['import_id'] = $id;

        //Please use cbView method instead view method from laravel
        $this->cbView('admin.drm.manageshop', $data);
    }

    public function test()
    {
        $h = 'hello';
        return $h;
    }

    public function postProcessTranslation()
    {

        $id = $_REQUEST['id'];
        $token = $_REQUEST['_token'];
        $source_lang = $_REQUEST['source_lang'];
        $language_code = $_REQUEST['language'];


        $ProcessTranslation = new ProcessTranslation($id, $source_lang, $language_code);
        $ProcessTranslation->handle();

        //Create an Auth
        if (!CRUDBooster::isCreate() && $this->global_privilege == FALSE || $this->button_add == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'), "Translation Complete!", "success");

//          $data = [];
//          $data['page_title'] = 'Page Translation';
//          $data['id'] = $id;
// 			$data['token'] = $token;
// 			$data['code'] = $language_code;

//             //Please use cbView method instead view method from laravel
//             $this->cbView('admin.drm.process_translation',$data);
    }


    public function getContinueImport()
    {

        redirectToV2('/suppliers/feeds');

        $incomplete_imports = DB::table('drm_imports')->where('user_id', CRUDBooster::myId())->where('import_finished', '<>', '1')->get();
        return view('admin.drm_import.incomplete_imports', compact('incomplete_imports'));
    }


    public function deleteImport($id)
    {
        DeleteImportFeed::dispatch($id,CRUDBooster::myParentId());
        CRUDBooster::redirect($_SERVER['HTTP_REFERER'], __('Import feed deletion process started. Your data will be deleted very soon.'), "success");
    }

    public function importDeleteProcess($id)
    {
        $import = DrmImport::find($id);

        //User id
        $user_id = $import->user_id;

        if ($import->image_backup) {
            Storage::disk('spaces')->deleteDirectory('import/backup/images/' . $id);
        }

        DB::table('drm_imports')->where('id', $id)->delete();
        DB::table('drm_import_errors')->where('drm_import_id', $id)->delete();
        DB::table('drm_import_filter')->where('drm_import_id', $id)->delete();
        DB::table('drm_import_sync')->where('drm_import_id', $id)->delete();
        DB::table('drm_product_fields')->where('drm_import_id', $id)->delete();
        DB::table('tmp_drm_products')->where('drm_import_id', $id)->delete();

        $product_ids = DB::table('drm_products')->where('drm_import_id', $id)->pluck('id')->toArray();

        foreach (array_chunk($product_ids, 500) as $chunk) {
            DB::table('drm_product_categories')->whereIn('product_id', $chunk)->delete();
            \App\Jobs\DestroyProduct::dispatchNow($import, $chunk);
        }

        $categories = DB::table('drm_category')->where('drm_import_id', $id)->pluck('id')->toArray();

        $connected_categories = DB::table('drm_product_categories')->whereIn('category_id', $categories)->groupBy('category_id')->pluck('category_id')->toArray();

        DB::table('drm_category')->where('drm_import_id', $id)->whereNotIn('id', $connected_categories)->delete();

        //Clear account activity step
        \App\Services\CheckListProgress\Checklist::cache_key_clear(4, $user_id);
    }


    public function getManualUpdateCsv()
    {
        $import_id = $_REQUEST['id'];
        $user_id = CRUDBooster::myId();
        $check_importing = $this->importProductCheck();
        if (CRUDBooster::myPrivilegeId() == '3') {
            if (($check_importing['blocked'] != '') or ($check_importing['product_amount'] <= 0)) {
                return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
            }
        }
        $drm = User::find($user_id)->drm_imports()->where('id', $import_id)->where('type', 'file')->where('import_finished', 1)->first();
        if ($drm == null) {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'), "Import not found", "danger");
        } else {
            $data['page_title'] = __("Feed List");
            $data['drm'] = $drm;
            $data['step'] = 1;
            return view('admin.drm_import.after_import.manual_update_csv', $data);
        }
    }

    public function getProductTemplate($id = null)
    {
        $template_purchased = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, CRUDBooster::myParentId());
        // if (app()->environment('local')) {
        // 	$template_purchased = true;
        // }
        if ($template_purchased) {
            $import = DrmImport::find($id);
            if ($import) {
                $data['template'] = $import->drm_import_template;
                $fields = $import->drm_product_fields->toArray();

                $data['title_fields'] = Product::TEMPLATE_FIELDS;
                unset($data['title_fields']['description']);

                $data['desc_fields'] = Product::TEMPLATE_FIELDS;

                $image_fields = explode('|', $fields['image']);
                foreach ($image_fields as $key => $value) {
                    $i = $key + 1;
                    $data['desc_fields']['image_' . $i] = $value;
                }

                $data['template'] = $import->drm_import_template;

                $data['import'] = $import;
                return view('admin.drm_import.after_import.product_template_edit', $data);
            } else {
                CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'), "Import not found", "danger");
            }
        } else {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'), "Please buy the app", "danger");
        }
    }


    public function postUpdateImportUrl()
    {
        $id = $_REQUEST['drm_import_id'];
        $import = DrmImport::find($id);
        if ($import) {
            $import->file_url = $_REQUEST['url'];
            $import->csv_file_name = $_REQUEST['csv_filename'];
            $import->save();
        }
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'), "CSV URL has been updated !", "success");
    }

    public function getUpdateCsv()
    {
        $id = $_REQUEST['id'];
        $drm = DB::table('drm_imports')->where('id', $id)->first();
        return view('admin.drm_import.after_import.update_csv_url', compact('drm'));
    }


    //////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Initial Step //////////////////////////////
    //////////////////////////////////////////////////////////////////////////

    public function postFixEan($id){
        $fixable = TmpDrmProduct::where('drm_import_id', $id)->whereRaw('LENGTH(ean) IN (11,12)');
        $fix_count = $fixable->count();

        $fixable->each(function($product){
            $fixed_ean = sprintf("%013d", $product->ean);
            $product->ean = $fixed_ean;
            $product->save();
        });

        return [
            'status' => 'success',
            'message' => $fix_count . ' EAN updated successfully!'
        ];
    }

    public function postFinishImport(){
        $import_id = Request::input('import_id');

        DrmImport::where(['id' => $import_id])->update([
            'import_finished' => '1'
        ]);
        return [
            'status' => 'success',
            'message' => 'Import finished successfully!'
        ];
    }

    public function getImport()
    {
        redirectToV2('/products/import');

        $data['ftp_app_banner'] = false;
        $check_importing = $this->importProductCheck();
        // $import_details=AppStore::ShowImportLimitBanner(CRUDBooster::myId());
        // dd($check_importing, drmTotalProduct(CRUDBooster::myParentId()), (500 > 500) ? true : false);

        if (CRUDBooster::myPrivilegeId() == '3') {
            if (($check_importing['blocked'] != '') or ($check_importing['product_amount'] <= 0)) {
                return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
            }
        }

        $data = [];
        $data['import_new_product'] = "https://player.vimeo.com/video/393321002";
        $data['import_video'] = drm_video_url(42);
        $data['product_sync'] = "https://player.vimeo.com/video/391571545";
        $data['import_product'] = $check_importing;

        $data['template_purchased'] = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, CRUDBooster::myParentId());
        if (CRUDBooster::myId() == null) {
            return redirect(CRUDBooster::adminPath());
        }
        if (isset($_REQUEST['id'])) {
            $id = $_REQUEST['id'];
            if ($id != null) {
                $drm = User::find(CRUDBooster::myParentId())->drm_imports()->where('id', $id)->first();

                $country = DB::table('countries')->where('id', $drm->country_id)->first();
                $data['drm_categories'] = [];
                if ($country != null) {
                    $data['drm_categories'] = DB::table('drm_category')->select('category_name_' . $country->language_shortcode . ' as category_name', 'id')->where('user_id', $drm->user_id)->where('country_id', $drm->country_id)->get();
                }

                //  $data['price_categories'] = DB::table('price_category')->where('user_id',CRUDBooster::myParentId())->get();
                $category_ids = json_decode($drm->category_ids);

                $data['product_categories'] = [];
                if (is_array($category_ids)) {
                    $data['product_categories'] = DB::table('drm_category')
                        ->select('drm_category.category_name_' . $country->language_shortcode . ' as category_name', 'drm_category.id')
                        ->whereIn('id', $category_ids)
                        ->get();
                }

                if ($data['product_categories'] == null) {
                    $data['product_categories'] = [];
                }
                if (isset($_GET['tab'])) {
                    $drm->current_step = $_GET['tab'];
                }

                if($drm->current_step == 'upload_csv'){
                    $view = "admin.drm_import.new.upload_file";
                    $data['ftp_app_banner'] = true;

                    $user_industry_temp_field = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->first();

                    $data['user_industry_temp_field'] = $user_industry_temp_field;

                    $data['has_industry_temp'] = false;

                    if($user_industry_temp_field){
                        $data['has_industry_temp'] = true;
                    }
                }

                if ($drm->current_step == 'fields') {
                    $sources = FileSource::where([
                        'drm_import_id' => $drm->id,
                    ])->get();

                    $data['import_video'] = drm_video_url(44);
                    $data['image_backup_premium'] = AppStore::ActiveFeature('Image-Backup');

                    $data['settings'] = DB::table('drm_import_settings')->where('drm_import_id', $drm->id)->first();
                    $data['country'] = $country;

                    // ( (isLocal() && !in_array(CRUDBooster::myParentId(), [179, 212])) || (in_array(CRUDBooster::myParentId(), [62, 98])) ) ? "admin.drm_import.new.field_mapping" :
                    $view = "admin.drm_import.field_mapping";
                }

                elseif ($drm->current_step == 'template') {
                    $data['import_video'] = drm_video_url(45);

                    $fields = $drm->drm_product_fields->toArray();

                    $data['title_fields'] = Product::TEMPLATE_FIELDS;
                    unset($data['title_fields']['description']);
                    $data['desc_fields'] = Product::TEMPLATE_FIELDS;
                    $image_fields = explode('|', $fields['image']);
                    foreach ($image_fields as $key => $value) {
                        $i = $key + 1;
                        $data['desc_fields'][] = 'image_' . $i;
                    }

                    $data['template'] = $drm->drm_import_template;

                    // if(isLocal() || in_array(CRUDBooster::myParentId(), [212])){
                        $industry_template_on_product_template = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->first();
                        $industry_template_field_name = [];

                        if($industry_template_on_product_template){
                            $industry_template_field_name = array_keys($industry_template_on_product_template->fields);
                        }

                        if($industry_template_field_name){
                            $data['title_fields'] = array_merge($data['title_fields'], $industry_template_field_name);
                            $data['desc_fields'] = array_merge($data['desc_fields'], $industry_template_field_name);
                        }
                    // }

                    // ( (isLocal() && !in_array(CRUDBooster::myParentId(), [179, 212])) || (in_array(CRUDBooster::myParentId(), [62, 98])) ) ? "admin.drm_import.new.import_product_template" :
                    $view = "admin.drm_import.import_product_template";
                }

                elseif ($drm->current_step == 'products') {
                    $data['import_video'] = drm_video_url(46);
                    $data['total'] = TmpDrmProduct::where('drm_import_id', $id)->count();

                    $data['ean_missing'] = TmpDrmProduct::where('drm_import_id', $id)->where(function ($q) {
                        return $q->where('ean', null)->orWhere('ean', "");
                    })->count();
                    $data['fixable_ean'] = TmpDrmProduct::where('drm_import_id', $id)->whereRaw('LENGTH(ean) IN (11,12)')->count();

                    $data['ean_module_purchased'] = AppStore::ActiveFeature('GTIN-Archiv');

                    if (CRUDBooster::myParentId() == 98) {
                        $data['ean_module_purchased'] = true;
                    }
                    $view = "admin.drm_import.temp_product_list";
                }

                elseif ($drm->current_step == 'search_and_replace') {
                    $data['import_video'] = drm_video_url(48);
                    $data['filtered_ids'] = $this->getFilteredIds($id);
                    $view = "admin.drm_import.search_and_replace";
                }
                //-- endif
                elseif ($drm->current_step == 'market_place') {

                    $template = DB::table('drm_product_fields')->select('industry_template')->where('drm_import_id', $drm->id)->first();
                    if($template){
                        $template = (array) json_decode($template->industry_template);
                        $template_name = array_key_first($template);
                        $marketplace_cat_id = templateMarketplaceCategory($template_name);

                        if(isset($template_name)){
                            $imported_products = DrmProduct::select('ean')->where('user_id', $id)->pluck('ean');
                            $data['suggested_products'] = MarketplaceProducts::whereNotIn('ean', $imported_products)
                                                        ->where('category_id', $marketplace_cat_id)->limit(15)->get();
                        }
                    }
                    $view = "admin.drm_import.market_place";
                }
                elseif ($drm->current_step == 'filter') {
                    $data['filter'] = DB::table('drm_import_filter')->where('drm_import_id', $id)->first();
                    $data['import_video'] = drm_video_url(47);
                    $data['filtered_ids'] = $this->getFilteredIds($id);

                    $data['magicable'] = TmpDrmProduct::select('brand', 'item_color', 'item_weight', 'item_size', 'ek_price')
                    ->where('drm_import_id', $id)
                    ->where(function($q) {
                        $q->whereNotNull('brand')->orWhere('brand','<>','');
                        $q->whereNotNull('item_color')->orWhere('item_color','<>','');
                        $q->whereNotNull('item_weight')->orWhere('item_weight','<>','');
                        $q->whereNotNull('item_size')->orWhere('item_size','<>','');
                        $q->whereNotNull('ek_price')->orWhere('ek_price','<>','');
                    })->count();


                    $automagic = new Automagic(CRUDBooster::myId());
                    $data['magic_left'] = $automagic->magicAvailable();

                    $view = "admin.drm_import.filter_v2";
                }
                $data['drm'] = $drm;

                if ($drm->import_finished == 1) {
                    return redirect(CRUDBooster::mainpath());
                } else {
                    $data['errors'] = DB::table('drm_import_errors')->where('drm_import_id', $id)->limit(10)->get();
                    $select_option = json_decode($drm->csv_headers);
                    $data['select_option'] = removeNulls($select_option);
                    if (view()->exists($view)) {
                        return view($view, $data);
                    } else {
                        return redirect()->back();
                    }
                }
            } else {
                return redirect(CRUDBooster::mainpath());
            }
        } else {
            $data['ftp_app_banner'] = true;

            $data['import_video'] = drm_video_url(43);

            // if((isLocal() || in_array(CRUDBooster::myParentId(), [212]))){
                $data['user_industry_template'] = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->first();
            // }

            // if( (isLocal() && !in_array(CRUDBooster::myParentId(), [179, 2455, 212])) || (in_array(CRUDBooster::myParentId(), [62, 98])) ){
            //     return view('admin.drm_import.new.initialize', $data);
            // }

            // if (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])){
                $additional_industry_temp = '';

                $user_ind_temp = CustomIndustryTemplate::where('user_id', CRUDBooster::myParentId())->pluck('name', 'id')->toArray();
                if($user_ind_temp){
                    foreach($user_ind_temp as $key => $value){
                        $pre_select = (!empty($data['user_industry_template']->name) && $data['user_industry_template']->name == $value) ? 'selected' : '';

                        $additional_industry_temp .= '<option value="'.$key.'" '.$pre_select.'>'.$value.'</option>';
                    }
                }

                $admin_ind_temp = CustomIndustryTemplate::where('publish_to_customer', 1)->pluck('name', 'id')->toArray();
                if($admin_ind_temp){
                    foreach($admin_ind_temp as $key => $value){
                        $pre_select = (!empty($data['user_industry_template']->name) && $data['user_industry_template']->name == $value) ? 'selected' : '';

                        $additional_industry_temp .= '<option value="'.$key.'" '.$pre_select.'>'.$value.'</option>';
                    }
                }

                $data['additional_temp'] = $additional_industry_temp;
            // }

            return view('admin.drm_import.upload_csv', $data);
        }
    }

    function postSetTab()
    {
        $tab = Request::input('tab');
        $id = Request::input('id');
        DB::table('drm_imports')->where('id', $id)->update(['current_step' => $tab]);
        Request::session()->put('curr_tab', $tab);
        return redirect('admin/drm_imports/import?id=' . $id . '&tab=' . $tab);
    }

    function getSetTab()
    {
        $tab = $_REQUEST['tab'];
        $id = $_REQUEST['id'];
        DB::table('drm_imports')->where('id', $id)->update(['current_step' => $tab]);
        return redirect('admin/drm_imports/import?id=' . $id);
    }

    //////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Save Import (1st Step) ////////////////////
    //////////////////////////////////////////////////////////////////////////

    // public function postSaveImportNew(\Illuminate\Http\Request $request)
    // {
    //     $message = ['text' => 'Initializing...', 'percent' => '10'];
    //     sentProgress($message, 'import');
    //     $user_id = CRUDBooster::myParentId();

    //     if ($request->company_user_type == 1) {
    //         $company_id = DeliveryCompany::create([
    //             'user_id' => $user_id,
    //             'name' => $request->company_name,
    //             'address' => $request->company_address,
    //             'phone' => $request->company_contactnumber,
    //             'state' => $request->company_state,
    //             'country_id' => $request->company_country,
    //             'zip' => $request->company_zip,
    //             'email' => $request->company_email,
    //             'contact_name' => $request->company_contactname,
    //         ])->id;
    //     } else {
    //         $company_id = $request->company_user;
    //     }

    //     $message = ['text' => 'Saving information...', 'percent' => '100'];
    //     sentProgress($message, 'import');

    //     $drm_id = DrmImport::create([
    //         'user_id' => $user_id,
    //         'delivery_company_id' => $company_id,
    //         'csv_file_name' => $request->csv_filename,
    //         'country_id' => $request->item_country,
    //         'current_step' => 'upload_csv'
    //     ])->id;

    //     $selected_industry_template_name = $_REQUEST['industry_templates'];
    //     $industry_template_checkbox = $_REQUEST['industry_template_checkbox'];

    //     if (!empty($selected_industry_template_name) && !isset($industry_template_checkbox)) {

    //         $selected_template_fields = 'industry_template.'.strtolower($selected_industry_template_name);

    //         $new_template_insert = IndustryTemplate::updateOrCreate(
    //             ['user_id' => $user_id],
    //             ['name' => $selected_industry_template_name, 'fields' => config($selected_template_fields), 'status' => true]
    //         );

    //     }else if( (isset($industry_template_checkbox) && $industry_template_checkbox == 'on') && (empty($selected_industry_template_name) || !empty($selected_industry_template_name)) ){

    //         $template_exits = IndustryTemplate::where('user_id',$user_id)->exists();

    //         if($template_exits){
    //             IndustryTemplate::where('user_id',$user_id)->update(['status' => false]);
    //         }

    //     }

    //     Session::put('unfinished_' . $user_id, $drm_id);
    //     Session::save();

    //     // return (int)$drm_id;
    //     // return redirect('/admin/drm_imports/import?id='.(int)$drm_id);

    //     return redirect(CRUDBooster::mainPath('import?id='.(int)$drm_id));
    // }

    public function postStoreSource(\Illuminate\Http\Request $request)
    {
        dd($request->all());
    }

    public function postSaveImport()
    {

        redirectToV2('/products/import');

        $message = ['text' => 'Initializing...', 'percent' => '10'];
        sentProgress($message, 'import');
        $paths = array();
        $url_file = null;
        $type = null;
        $user_id = CRUDBooster::myParentId();
        $rand = Str::random(40);

        $credentials = array();

        if (Request::input('file_type') == 1) {
            $type = 'file';
            if (!Request::hasFile('csv_file')) {
                $message = ['error' => 'CSV / Excel File is Invalid', 'percent' => '100'];
                sentProgress($message, 'import');
                return 0;
            }
            foreach (Request::file('csv_file') as $file) {
                $message = ['text' => 'Uploading File...', 'percent' => '50'];
                // event(new progressEvent($message));
                sentProgress($message, 'import');

                $extention = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);

                if ($extention == 'csv' || $extention == 'txt') {
                    $data = file_get_contents_utf8($file->getRealPath());
                    Storage::disk('spaces')->put('public/csv_files/' . $rand . '.' . $extention, $data, 'public');
                    $paths[] = 'public/csv_files/' . $rand . '.' . $extention;
                } else {
                    $paths[] = $file->storeAs('public/csv_files', $rand . '.' . $extention, ['visibility' => 'public', 'disk' => 'spaces']);
                }
            }
        } else if (Request::input('file_type') == 5) {
            $message = ['text' => 'Getting Data From FTP...', 'percent' => '50'];
            sentProgress($message, 'import');
            $type = 'ftp';
            $credentials = Session::get('drm_ftp_user_credentials' . CRUDBooster::myId());
            $csv_data = getFtpContents($credentials['host'], $credentials['username'], $credentials['password'], Request::input('file_path'));
            $url_file = 'ftp://' . $credentials['username'] . ':' . $credentials['password'] . '@' . $credentials['host'] . '/' . Request::input('file_path');

            Storage::disk('spaces')->put('public/csv_files/' . $rand . '.csv', $csv_data, 'public');
            $paths[] = 'public/csv_files/' . $rand . '.csv';
        } else if (Request::input('file_type') != 4) {
            $check_file = DB::table('drm_imports')->where('user_id', CRUDBooster::myParentId())->where('file_url', trim(Request::input('csv_link')))->count();
            if ($check_file) {
                $message = ['error' => 'already_exists'];
                return json_encode($message);
            } else {
                $type = 'url';
                $url_file = trim(Request::input('csv_link'));

                $message = ['text' => 'Getting Data From : <i>' . substr(Request::input('csv_link'), 0, 40) . '...</i>', 'percent' => '50'];
                // event(new progressEvent($message));
                sentProgress($message, 'import');
                $csv_data = getRemoteFile($url_file);
                Storage::disk('spaces')->put('public/csv_files/' . $rand . '.' . $csv_data['ext'], $csv_data['data'], 'public');
                $paths[] = 'public/csv_files/' . $rand . '.' . $csv_data['ext'];
            }
        }

        // else{
        // event(new progressEvent($message));
        sentProgress(['text' => 'Listing Headers...', 'percent' => '70'], 'import');
        $csv = $this->csvToCsvHeaderJson(implode(';', $paths), Request::input('delimiter'), true, true);

        if ($csv != false) {
            $csv_headers = makeArrayUtf8(makeArrayUtf8($csv[0]));
            $csv_headers = array_map('removeDots', $csv_headers);
            $csv_headers = json_encode($csv_headers);
        }
        // }
        if ($csv == false || count($csv) < 2) {
            $message = ['error' => 'CSV / Excel File is Invalid', 'percent' => '100'];
            // event(new progressEvent($message));
            sentProgress($message, 'import');
            return 0;
        }

        $message = ['text' => 'Storing Information...', 'percent' => '90'];
        // event(new progressEvent($message));
        sentProgress($message, 'import');

        if (Request::input('company_user_type') == 1) {
            $company_id = DeliveryCompany::create([
                'user_id' => CRUDBooster::myParentId(),
                'name' => Request::input('company_name'),
                'address' => Request::input('company_address'),
                'phone' => Request::input('company_contactnumber'),
                'state' => Request::input('company_state'),
                'country_id' => Request::input('company_country'),
                'zip' => Request::input('company_zip'),
                'email' => Request::input('company_email'),
                'contact_name' => Request::input('company_contactname'),
            ])->id;
        } else {
            $company_id = Request::input('company_user');
        }

        $drm_id = DrmImport::create([
            'user_id' => CRUDBooster::myParentId(),
            'delivery_company_id' => $company_id,
            'csv_file_name' => Request::input('csv_filename'),
            'csv_headers' => $csv_headers,
            'type' => $type,
            'file_url' => $url_file,
            'country_id' => Request::input('item_country'),
            'delimiter' => Request::input('delimiter'),
            'csv_file_path' => implode(';', $paths),
            'demo_data' => Request::session()->pull('demoData'),
            'ftp_credentials' => json_encode($credentials)
            // 'unlimited_quantity'=>Request::input('unlimited_quantity')
        ])->id;

        // if (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])){

            $selected_industry_template_name = $_REQUEST['industry_templates'];

            if (!empty($selected_industry_template_name)) {
                if( in_array(strtolower($selected_industry_template_name), array_keys(config('industry_template'))) ){

                    $selected_template_fields = 'industry_template.'.strtolower($selected_industry_template_name);

                    $new_template_insert = IndustryTemplate::updateOrCreate(
                        ['user_id' => $user_id],
                        ['name' => $selected_industry_template_name, 'fields' => config($selected_template_fields)]
                    );

                }else{
                    // Here ***$selected_industry_template_name*** refers to Custom Industry Template ID
                    $custom_temp = CustomIndustryTemplate::find($selected_industry_template_name);

                    $insert_data = [
                        'name' => $custom_temp->name,
                        'fields' => $custom_temp->fields,
                    ];

                    IndustryTemplate::updateOrCreate(
                        ['user_id' => $user_id],
                        $insert_data
                    );
                }
            }

        try {
            $redis = Redis::connection();
            $redis->set('import_csv_' . $drm_id, json_encode($csv));
        } catch (\Predis\Connection\ConnectionException $e) {
        }


        Session::put('unfinished_' . $user_id, $drm_id);
        Session::save();

        DB::table('drm_imports')->where('id', $drm_id)->update(['current_step' => 'fields']);
        return (int)$drm_id;
    }


    public function csvToCsvHeaderJson($file, $delimiter, $cloud = true, $initial = false)
    {
        $paths = explode(';', $file);
        $count = 0;
        $rand = Str::random(40);
        foreach ($paths as $path) {
            $file_type = pathinfo($path, PATHINFO_EXTENSION);
            try {
                if ($cloud == true) {
                    $path = Storage::disk('spaces')->url($path);
                    file_put_contents($rand . '.' . $file_type, fopen($path, 'r'));
                    $path = $rand . '.' . $file_type;
                }

                if ($file_type == 'csv' || $file_type == 'txt') {
                    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Csv');
                    if ($delimiter != 'auto') {
                        $reader->setDelimiter($delimiter);
                    }
                    $spreadsheet = $reader->load($path);
                } else {
                    $spreadsheet = IOFactory::load($path);
                }
                $spreadsheet = $spreadsheet->getActiveSheet()->toArray();
                $total = count($spreadsheet);
                for ($i = 0; $i <= 5; $i++) {
                    if ($total >= $i + 1) {
                        $collection[$i] = $spreadsheet[$i];
                    }
                }
                $valid = $this->validateFile($collection);
                if ($valid == false) {
                    if ($cloud == true) {
                        unlink($path);
                    }
                    return false;
                }

            } catch (\Exception $e) {
                return false;
            }
            $demoFinal = makeArrayUtf8(makeArrayUtf8($spreadsheet[1]));
            Request::session()->put('demoData', json_encode($demoFinal));
        }
        if ($cloud == true) {
            unlink($path);
        }
        if ($initial) {
            return $spreadsheet;
        } else {
            $headers = makeArrayUtf8(makeArrayUtf8($spreadsheet[0]));
            $headers = array_map('removeDots', $headers);
            return json_encode($headers);
        }
    }

    //END Step 1


    //////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Field Mapping (2nd Step) //////////////////
    //////////////////////////////////////////////////////////////////////////


    public function getDemoData()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        if (Request::input('id') != null) {
            $drm = DrmImport::find(Request::input('id'));
            $headers = json_decode($drm->csv_headers, true);
            $demo = json_decode($drm->demo_data, true);
            $demo_data = array_combine($headers, $demo);
            $demo_data = removeNullKeys($demo_data);
            $request = Request::input();
            // dd($request);
            return view('admin.drm_import.demo_data_table', compact('demo_data', 'request'));
        }
    }

    public function getCheckFormat()
    {
        $drm = DrmImport::find(Request::input('id'));
        $headers = json_decode($drm->csv_headers, true);
        $demo = json_decode($drm->demo_data, true);
        $demo_data = array_combine($headers, $demo);
        $demo_data = removeNullKeys($demo_data);
        $request = Request::input();

        $format = $request['money_format'];
        $ek_price = $demo_data[$request['ek_price']];
        $mod_ek = drm_convert_european_to_decimal($ek_price, $format);
        $return = true;
        if ($ek_price != 0 && $mod_ek == 0) {
            $return = false;
        }
        return json_encode(['status' => $return]);
    }


    public function postSaveFields()
    {
        $message = ['text' => 'Initializing Fields', 'percent' => '10'];
        sentProgress($message, 'import');

        $image_backup = Request::input('image_backup');

        $manual_category = Request::input('custom_category');
        $import_id = Request::input('drm_import_id');
        $drm_category = Request::input('category_from_drm');

        $delivery_days = Request::input('delivery_time');
        if ($delivery_days == null) {
            $delivery_days = Request::input('delivery_time_field');
        }
        $drm = DrmImport::find($import_id);

        $country = DB::table('countries')->where('id', $drm->country_id)->first();
        DB::table('drm_category')->where('drm_import_id', $import_id)->delete();
        DB::table('drm_import_categories')->where('drm_import_id', $import_id)->delete();
        DB::table('drm_product_fields')->where('drm_import_id', $import_id)->delete();

        $image_separator = Request::input('image_separator');
        if (Request::input('image_separator') == null || Request::input('image_separator') == "") {
            $image_separator = " ";
        }
        if (Request::input('select_stock') != "file") {
            $drm->unlimited_quantity = 1;
            if (Request::input('select_stock') == "manual") {
                $drm->fixed_stock = (int)Request::input('manual_stock');
            } else {
                $drm->fixed_stock = 10000;
            }
        }
        if ($image_backup) {
            $drm->image_backup = 1;
        }

        $drm->image_prefix = Request::input('prefix');
        $drm->image_suffix = Request::input('suffix');
        $drm->image_separator = $image_separator;
        $drm->item_number_prefix = Request::input('item_number_prefix');
        $drm->image_validation = Request::input('image_validation');
        $drm->money_format = Request::input('money_format');

        if (Request::input('fallback') != null) {
            $drm->fallback = Request::input('fallback');
        }


        DB::table('drm_import_settings')->updateOrInsert(
            ['drm_import_id' => $import_id],
            [
                'quantity_surcharge' => drm_convert_european_to_decimal(Request::input('quantity_surcharge'), $drm->money_format) ?? 0,
                'discount' => drm_convert_european_to_decimal(Request::input('discount'), $drm->money_format) ?? 0,
                'discount_type' => Request::input('discount_type') ?? "%",
                'description_prefix' => Request::input('description_prefix'),
                'description_suffix' => Request::input('description_suffix'),
            ]
        );


        // if($custom_category != null && $drm_category != null){
        // 	$category_from_drm = DB::table('drm_category')->where('id',$drm_category)->first();
        // 	if($category_from_drm!=null){
        // 		$category_from_drm = (array)$category_from_drm;
        // 		$category_from_drm = $category_from_drm["category_name"."_".$country->language_shortcode];
        // 	}
        // 	$allCategory = $category_from_drm . "|" . $custom_category;
        // }elseif($drm_category != null){
        // 	$drm->existing_category = $drm_category;
        // 	$category_from_drm = DB::table('drm_category')->where('id',$drm_category)->first();
        // 	   if($category_from_drm!=null){
        // 		   $category_from_drm = (array)$category_from_drm;
        // 		   $category_from_drm = $category_from_drm["category_name"."_".$country->language_shortcode];
        // 		   }
        // 	$allCategory = $category_from_drm;
        // }elseif($custom_category != null){
        //    $allCategory = $custom_category;
        // }

        // if($category_from_drm!=null){
        // 	$drm->custom_category = 1;
        // }

        if ($drm_category != null) {
            $drm->existing_category = $drm_category;
            $custom_category = DB::table('drm_category')->where('id', $drm_category)->first();
            if ($custom_category != null) {
                $custom_category = (array)$custom_category;
                $custom_category = $custom_category["category_name" . "_" . $country->language_shortcode];
            }
        }

        if ($custom_category != null) {
            $drm->custom_category = 1;
        }

        $message = ['text' => 'Processing Fields', 'percent' => '20'];
        sentProgress($message, 'import');
        $images = Request::input('image');

        foreach ($images as $key => $value) {
            if ($key == 0) {
                $image = $value;
            } else {
                $image = $image . "|" . $value;
            }
        }

        //   ($custom_category!=null)?$category=$custom_category:$category=Request::input('category');

        //  if(isLocal()){
        if ($manual_category != null) {
            $drm->manual_category = $manual_category;
            $drm->custom_category = 1;
        }
        //  }else{
        // 	if($manual_category!=null){
        // 	$drm->manual_category = $manual_category;
        // 	}
        //  }

        $categories = (array)Request::input('category');

        // if(isLocal()){
        if ($categories) {
            foreach ($categories as $key => $value) {
                if ($key == 0) {
                    $category = $value;
                } else {
                    $category = $category . "|" . $value;
                }
            }
        } else {
            $category = "";
        }

        $tax = (float)Request::input('manual_tax') ? [
                Tax::ORIGINAL => $country->tax_rate,
                Tax::REDUCED => $country->reduced_tax_rate,
            ][(float)Request::input('manual_tax')] ?? 0 : Request::input('tax');

        // }else{
        // 	foreach ($categories as $key => $value){
        // 		if($key == 0){
        // 		  $category = $value;
        // 		}
        // 		else{
        // 		  $category = $category."|".$value;
        // 		}
        // 	}
        // }

        //   }

        //   $csv_categories = (array)Request::input('category');

        //   if($csv_categories != null){
        // 	$csv_category = "";
        // 	foreach ($csv_categories as $key => $value){
        // 		if($key == 0){
        // 		  $csv_category = $value;
        // 		}
        // 		else{
        // 		  $csv_category = $csv_category."|".$value;
        // 		}
        // 	}
        //   }

        //   if($allCategory == ""){
        // 	$allCategory = $csv_category;
        //   }else{
        // 	$allCategory = $csv_category  . "|" . $allCategory;
        //   }
        //   dd($allCategory, $csv_category);

        // Industry Template

        // if (isLocal() || in_array(CRUDBooster::myParentId(),[212, 2650, 98])) {

            $industry_template = [];

            if(isset($_REQUEST["industry_template_name"])){

                // if (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])){

                    if( in_array(strtolower($_REQUEST["industry_template_name"]), array_keys(config('industry_template'))) ){
                        $template_name = 'industry_template.'.strtolower($_REQUEST["industry_template_name"]);
                        $template_property = config($template_name);

                        foreach($template_property as $key => $value){
                            if( isset( $_REQUEST[$key] ) ){
                                $industry_template_property[$key] = Request::input($key);
                            }else if( isset( $_REQUEST[$key.'_manual'] ) ){
                                $industry_template_property[$key] = Request::input($key.'_manual');
                            }
                        }

                        $industry_template[strtolower($_REQUEST["industry_template_name"])] = $industry_template_property;
                    }else{
                        $temp_fields = CustomIndustryTemplate::where('name', trim($_REQUEST["industry_template_name"]))->value('fields');

                        $custom_temp_property = [];
                        array_walk($temp_fields, function ($fields) use (&$custom_temp_property){
                            $custom_temp_property[] = $fields['name'];
                        });

                        foreach($custom_temp_property as $value){
                            // dd(str_replace(' ', '_', $value), $value, str_replace(' ', '_', $value).'_manual');
                            $value = trim($value);
                            if( isset( $_REQUEST[str_replace(' ', '_', $value)] ) ){
                                $industry_template_property[$value] = ( Request::input(str_replace(' ', '_', $value)) ) ?? '';
                            }else if( isset( $_REQUEST[str_replace(' ', '_', $value).'_manual'] ) ){
                                $industry_template_property[$value] = ( Request::input(str_replace(' ', '_', $value).'_manual') ) ?? '';
                            }
                        }

                        $industry_template[$_REQUEST["industry_template_name"]] = $industry_template_property;
                    }

                // }else{
                // $template_name = 'industry_template.'.strtolower($_REQUEST["industry_template_name"]);
                // $template_property = config($template_name);

                // foreach($template_property as $key => $value){
                //     $industry_template_property[$key] = Request::input($key);
                // }

                // $industry_template[strtolower($_REQUEST["industry_template_name"])] = $industry_template_property;
                // }

            }

        // }else{

        //     $industry_template = [
        //         'model' => Request::input('model'),
        //         'bracelet_color' => Request::input('bracelet_color'),
        //         'bracelet_material' => Request::input('bracelet_material'),
        //         'special_features' => Request::input('special_features'),
        //         'dial_color' => Request::input('dial_color'),
        //         'movement' => Request::input('movement'),
        //         'watch_case_color' => Request::input('watch_case_color'),
        //         'housing_material' => Request::input('housing_material'),
        //         'ad_format' => Request::input('ad_format'),
        //         'item_style' => Request::input('style'),
        //         'watch_shape' => Request::input('watch_shape'),
        //         'year_of_manufacture' => Request::input('year_of_manufacture'),
        //         'water_resistance' => Request::input('water_resistance'),
        //         'dial' => Request::input('dial'),
        //         'tailor_made' => Request::input('tailor_made'),
        //         'manufacturer_number' => Request::input('manufacturer_number'),
        //         'country_of_manufacture' => Request::input('country_of_manufacture'),
        //         'case_diameter' => Request::input('case_diameter'),
        //         'original_packaging' => Request::input('original_packaging'),
        //     ];

        // }

        // End Industry Template

        if(Request::input('shipping_cost_manual')){
            $shipping_cost = drm_convert_european_to_decimal(Request::input('shipping_cost_manual'), $drm->money_format);
        }else{
            $shipping_cost = Request::input('shipping_cost');
        }

        $item_unit = null;

        if( !empty(Request::input('manual_item_unit')) ){
            $item_unit = Request::input('manual_item_unit');
        }else if( !empty(Request::input('item_unit')) ){
            $item_unit = Request::input('item_unit');
        }

        (Request::input('item_number') != null) ? $item_number = Request::input('item_number') : $item_number = Request::input('ean');
        $message = ['text' => 'Saving Fields', 'percent' => '50'];
        sentProgress($message, 'import');

        // if (isLocal() || in_array(CRUDBooster::myParentId(),[212, 2650, 98])) {

        $drm_products = [
            'drm_import_id' => $import_id,
            'name' => Request::input('name'),
            'item_number' => $item_number,
            'item_weight' => Request::input('item_weight'),
            'item_unit' => $item_unit,
            'item_size' => Request::input('item_size'),
            'item_color' => Request::input('item_color'),
            'production_year' => Request::input('production_year'),
            'brand' => Request::input('brand'),
            'materials' => Request::input('materials'),
            'ean' => Request::input('ean'),
            'description' => Request::input('description'),
            'short_description' => Request::input('short_description'),
            'image' => $image,
            'ek_price' => Request::input('ek_price'),
            'vk_price' => Request::input('vk_price'),
            'stock' => Request::input('stock'),
            'category' => $category,
            'gender' => Request::input('gender'),
            'status' => Request::input('status'),
            'delivery_days' => $delivery_days,
            'shipping_cost' => $shipping_cost,
            'uvp' => Request::input('uvp'),
            'dropfunnel_tags_manual' => Request::input('dropfunnel_tags_manual'),
            'dropfunnel_tags' => implode('|', Request::input('dropfunnel_tags') ?? []),
            'tax' => $tax,
            'offer_options' => Request::input('offer_options')
        ];


        if(!empty($industry_template)){
            $drm_products['industry_template'] = json_encode($industry_template);
        }

        if(Request::input('brand_logo') != null){
            $drm_products['brand_logo'] = Request::input('brand_logo');
        }

        if(Request::input('brand_from_drm') != null){
            $drm_products['dropmatix_product_brands_id'] = (int) Request::input('brand_from_drm');
        }

        DrmProductField::create($drm_products);

        $fields = DB::table('drm_product_fields')->where('drm_import_id', $import_id)->first();
        $message = ['text' => 'Creating Categories', 'percent' => '70'];
        sentProgress($message, 'import');

        $template_purchased = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, CRUDBooster::myParentId());
        $drm->current_step = 'template';
        $drm->save();
        $message = ['text' => 'Mapping Completed', 'percent' => '100'];
        sentProgress($message, 'import');
        return "template";
    }

    public function drmSaveCsvCategories($fields)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', '-1');
        $id = $fields->drm_import_id;
        $import = DrmImport::find($id);
        $country = DB::table('countries')->where('id', $import->country_id)->first();
        $trans_cat = 'category_name_' . $country->language_shortcode;

        $drm_categories = [];
        if (\Schema::hasColumn('drm_category', $trans_cat)) {
            $categories_cache = $this->getCategoryCache($import->user_id);
            $drm_categories = $categories_cache->pluck($trans_cat)->toArray();
            if (is_array($drm_categories)) {
                $drm_categories = removeNulls($drm_categories);
            }
        }

        $all_imported_categories = [];
        if ($import->custom_category == 1) {
            if ($import->existing_category) {
                $category_name = $this->getCategoryById($import->existing_category, $import->user_id);
                if ($category_name != null) {
                    $category_array = (array)$category_name;
                    $category_name = $category_array[$trans_cat];
                }
                $all_imported_categories[] = $category_name;
            } else {
                if (!in_array($fields->category, $drm_categories)) {
                    DB::table('drm_category')
                        ->insert([
                            'drm_import_id' => $id,
                            $trans_cat => $fields->category,
                            'country_id' => $import->country_id,
                            'user_id' => $import->user_id,
                        ]);
                }
                $all_imported_categories[] = $fields->category;
            }
        } else {
            $path = $import->csv_file_path;
            $type = pathinfo($path, PATHINFO_EXTENSION);
            if ($type == 'xml') {
                $csv = $this->xmlToArray($path);
            } else {
                $csv = $this->getCsv($import);
                $key = $csv[0];
                $key = array_map('removeDots', $key);
                $key = makeArrayUtf8($key);
                unset($csv[0]);
                array_walk($csv, function (&$item) use ($key) {
                    $item = array_combine($key, $item);
                    $item = makeArrayUtf8(makeArrayUtf8($item));
                });
            }
            $collection = collect($csv);
            $all_imported_categories = $collection->pluck($fields->category)->unique()->toArray();


            $new_categories = array_diff($all_imported_categories, $drm_categories);
            $new_categories = removeNulls($new_categories);
            $insertValue = [];
            foreach ($new_categories as $key => $value) {
                $insertValue[] = [
                    'drm_import_id' => $id,
                    $trans_cat => $value,
                    'country_id' => $import->country_id,
                    'user_id' => $import->user_id
                ];
            }
            if (is_array($insertValue)) {
                DB::table('drm_category')->insert($insertValue);
            }
        }

        $inserted_categories = DB::table('drm_category')->whereIn($trans_cat, $all_imported_categories)->where('user_id', $import->user_id)->pluck('id')->toArray();
        $inserted_categories = json_encode($inserted_categories);
        $import->category_ids = $inserted_categories;
        $import->save();
    }

    public function processCategories($import, $fields)
    {
        $country = DB::table('countries')->where('id', $import->country_id)->first();
        $trans_cat = 'category_name_' . $country->language_shortcode;

        $path = $import->csv_file_path;
        $type = pathinfo($path, PATHINFO_EXTENSION);

        $csv = $this->getCsv($import);
        $key = $csv[0];
        $key = array_map('removeDots', $key);
        $key = makeArrayUtf8($key);
        unset($csv[0]);
        array_walk($csv, function (&$item) use ($key) {
            $item = array_combine($key, $item);
            $item = makeArrayUtf8(makeArrayUtf8($item));
        });

        $collection = collect($csv);
        $all_imported_categories = array();
        foreach ($fields as $field) {
            $all_imported_categories[] = $collection->pluck($field)->unique()->toArray();
        }
        $all_imported_categories = array_unique(array_filter(Arr::flatten($all_imported_categories)));

        $import_categories = json_decode($import->category_ids, true) ?? [];

        $insertValue = [];
        $inserted_categories = array();

        foreach ($all_imported_categories as $key => $value) {
            $inserted_categories[] = DrmCategory::updateOrCreate([
                'user_id' => $import->user_id,
                $trans_cat => $value
            ], [
                'drm_import_id' => $import->id,
                'country_id' => $import->country_id
            ])->id;
        }

        $inserted_categories = array_unique(array_merge($inserted_categories, $import_categories));
        $inserted_categories = json_encode($inserted_categories);
        $import->category_ids = $inserted_categories;
        $import->save();
        return json_decode($inserted_categories);
    }


    public function getColumnName($csv, $type, $delimiter, $deleteFile = true)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $paths = explode(';', $csv);
        $key = null;
        $key_count = 0;
        $array = array();
        $rand = Str::random(40);
        foreach ($paths as $path) {
            if ($deleteFile) {
                $path = Storage::disk('spaces')->url($path);
                $file_type = pathinfo($path, PATHINFO_EXTENSION);
                $file = file_get_contents($path);
                file_put_contents($rand . '.' . $file_type, $file);
                $localpath = $rand . '.' . $file_type;
            } else {
                $localpath = $path;
            }
            if ($type == 'csv' || $type == 'txt') {
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');

                if ($delimiter != 'auto') {
                    $reader->setDelimiter($delimiter);
                }

                $spreadsheet = $reader->load($localpath);
            } else {
                $spreadsheet = IOFactory::load($localpath);
            }

            $spreadsheet = $spreadsheet->getActiveSheet()->toArray();

            if ($key == null) {
                $key = array_map('trim', $spreadsheet[0]);
                $key_count = count($key);
            }

        }

        return $key;
    }

    public function xmlToArray($path)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $xml_file = file_get_contents_utf8($path);
        $xml = simplexml_load_string($xml_file);
        $array = $this->simpleXmlToArray($xml->product);

        foreach ($xml->product as $key => $value) {
            $array = $this->simpleXmlToArray($value);
            $final_array[] = $array;
            // yield $array;
        }
        return $final_array;
    }

    public function xmlToJsonHeader($xml)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $array = $this->simpleXmlToArray($xml);
        foreach ($array as $key => $value) {
            $headers_array[] = $key;
        }
        $Headers = json_encode($headers_array);
        Request::session()->put('demoData', json_encode($array));
        return $Headers;
    }

    function simpleXmlToArray($xmlObject)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        ini_set('memory_limit', -1);
        $array = [];
        foreach ($xmlObject->children() as $node) {
            $array[$node->getName()] = is_array($node) ? simplexml_to_array($node) : (string)$node;
        }
        return $array;
    }


    public function getDrmCategories()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $id = $_REQUEST['id'];
        $drm = DrmImport::find($id);
        $country = DB::table('countries')->where('id', $drm->country_id)->first();
        $data['drm_categories'] = [];
        if ($country != null) {
            $drm_categories = DB::table('drm_category')
                ->select('category_name_' . $country->language_shortcode . ' as category_name', 'id')
                ->where('user_id', $drm->user_id)
                ->where('country_id', $drm->country_id)
                ->whereNotNull('category_name_' . $country->language_shortcode)
                ->get();
        }
        $html = "<option value=''>Please select category</option>";
        foreach ($drm_categories as $key => $value) {

            $html .= "<option value='$value->id'>$value->category_name</option>";
        }
        return $html;
    }


    //END Step 2

    //////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Product Template (3rd Step) //////////////
    //////////////////////////////////////////////////////////////////////////

    public function postSaveProductTemplate()
    {
        $desc_template = $_REQUEST['desc_template'];
        $title_template = $_REQUEST['title_template'];
        $import_id = $_REQUEST['drm_import_id'];
        $import = DrmImport::find($import_id);

        $data = [];

        if (!empty($desc_template) && !empty($title_template)) {

            $data = [
                'drm_import_id' => $import_id,
                'description' => str_replace('<p><br></p>', '', $desc_template),
                'title' => str_replace('<p><br></p>', '', $title_template)
            ];

        } elseif (!empty($title_template)) {

            $data = [
                'drm_import_id' => $import_id,
                'description' => str_replace('<p><br></p>', '', "<p>#DESCRIPTION#<br></p>"),
                'title' => str_replace('<p><br></p>', '', $title_template)
            ];

        } elseif (!empty($desc_template)) {

            $data = [
                'drm_import_id' => $import_id,
                'description' => str_replace('<p><br></p>', '', $desc_template),
                'title' => str_replace('<p><br></p>', '', "<p>#TITLE#<br></p>")
            ];

        } else {

            $this->postImportTmpProducts($import_id);
            return redirect('admin/drm_imports/import?id=' . $import_id);

        }

        DrmImportTemplate::updateOrCreate(['drm_import_id' => $import_id], $data);
        $fields = $import->drm_product_fields;
        if (isset($_REQUEST['update'])) {
            if ($_REQUEST['update'] == '1') {
                return redirect('admin/drm_imports');
            }
        } else {
            $import->current_step = 'products';
            $import->save();
            $this->postImportTmpProducts($import_id);
            return redirect('admin/drm_imports/import?id=' . $import_id);
        }
    }

    public function getTemplatePreview()
    {
        $import_id = $_REQUEST['id'];
        $tempate = $_REQUEST['template'];
        $user_id = CRUDBooster::myParentId();
        $tempProduct = \Cache::remember('temp_product_' . $user_id, 05.0, function () use ($import_id) {
            $drm = DrmImport::find($import_id);
            $headers = json_decode($drm->csv_headers, true);
            $demo = json_decode($drm->demo_data, true);
            $demo_data = array_combine($headers, $demo);
            $data = removeNullKeys($demo_data);

            $fields = $drm->drm_product_fields;
            $image_headers = explode('|', $fields->image);
            $image_separator = $drm->image_separator;
            $image_array = array();

            foreach ($image_headers as $header) {
                $csv_images = explode($image_separator, $data[$header]);
                $image_array = array_merge($image_array, $csv_images);
            }

            $demo_product = [
                'drm_import_id' => $import_id,
                'title' => $data[$fields->name],
                'item_number' => strip_tags($data[$fields->item_number]),
                'item_weight' => strip_tags($data[$fields->item_weight]),
                'item_size' => strip_tags($data[$fields->item_size]),
                'item_color' => strip_tags($data[$fields->item_color]),
                'production_year' => strip_tags($data[$fields->production_year]),
                'brand' => strip_tags($data[$fields->brand]),
                'materials' => strip_tags($data[$fields->materials]),
                'ean' => strip_tags($data[$fields->ean]),
                'TransDescription' => $data[$fields->description],
                'image' => json_encode($image_array),
                'stock' => strip_tags($data[$fields->stock]),
                'category' => $category,
                'status' => $fields->status,
                'gender' => strip_tags($data[$fields->gender]),
            ];

            // if(isLocal() || in_array(CRUDBooster::myParentId(), [212])){

                $preview_field = json_decode($fields->industry_template, true);

                if($preview_field){
                    foreach($preview_field as $fields){
                        $filterd_field = array_filter($fields);
                        $industry_template_value_array = [];

                        foreach($filterd_field as $key => $value){
                            $industry_template_value_array[$key] = $data[$value];
                        }

                        if($industry_template_value_array){
                            $demo_product = array_merge($demo_product, $industry_template_value_array);
                        }
                    }
                }

            // }

            $product = (object)$demo_product;
            return $product;
        });

        $input = $tempate;
        $template = $this->generateTemplate($tempProduct, null, $input);
        return $template;
    }

    public function generateTemplate($product, $field, $template = null)
    {
        $import_id = $product->drm_import_id;
        $import = DrmImport::find($import_id);
        if ($template == null) {
            if ($field == "title") {
                $template = \App\DrmImportTemplate::where('drm_import_id', $import_id)->first()->title;
            }
            if ($field == "description") {
                $template = \App\DrmImportTemplate::where('drm_import_id', $import_id)->first()->description;
            }
        }
        if ($template != null) {
            $images = json_decode($product->image, true);
            if (!$images) {
                $images = [];
            }

            $product_tags = [
                '#ITEM_WEIGHT#',
                '#ITEM_SIZE#',
                '#ITEM_COLOR#',
                '#BRAND#',
                '#MATERIALS#',
                '#PRODUCTION_YEAR#',
                '#DESCRIPTION#',
                '#GENDER#',
                '#ITEM_NUMBER#',
                '#STOCK#',
                '#CATEGORY#',
                '#NOTE#',
                '#TAGS#',
                '#TITLE#',
                '#EAN#'
            ];

            if ($product->category == null) {
                $categories = DB::table('drm_product_categories')
                    ->select('drm_category.category_name_' . $import->lang . ' as category_name')
                    ->join('drm_category', 'drm_product_categories.category_id', '=', 'drm_category.id')
                    ->where('product_id', $product->id)
                    ->pluck('category_name')->toArray();
                $categories = implode('<br>', $categories);
            } else {
                $categories = $product->category;
            }

            $template_product = [];
            $template_product['#CATEGORY#'] = $categories;
            $template_product['#DESCRIPTION#'] = $product->TransDescription;
            foreach ($images as $key => $value) {
                $i = $key + 1;
                $template_product['#IMAGE_' . $i . "#"] = "<img width='300px' src='$value' />";
                $product_tags[] = '#IMAGE_' . $i . "#";
            }
            $template_product['#TITLE#'] = $product->title;
            $template_product['#ITEM_NUMBER#'] = $product->item_number;
            $template_product['#ITEM_WEIGHT#'] = $product->item_weight;
            $template_product['#ITEM_SIZE#'] = $product->item_size;
            $template_product['#ITEM_COLOR#'] = $product->item_color;
            $template_product['#BRAND#'] = $product->brand;
            $template_product['#MATERIALS#'] = $product->materials;
            $template_product['#PRODUCTION_YEAR#'] = $product->production_year;
            $template_product['#TAGS#'] = $product->tags;
            $template_product['#NOTE#'] = $product->note;
            $template_product['#STOCK#'] = $product->stock;
            $template_product['#GENDER#'] = $product->gender;
            $template_product['#EAN#'] = $product->ean;

            // if(isLocal() || in_array(CRUDBooster::myParentId(), [212])){
                $user_industry_template_name = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->select('name')->first();

                if($user_industry_template_name){
                    $user_industry_template_field = array_keys( config('industry_template.'.strtolower($user_industry_template_name->name)) );

                    if($user_industry_template_field){
                        $industry_template_tags = [];

                        foreach($user_industry_template_field as $field_name){
                            $industry_field_tag = '#' . strtoupper($field_name) . '#';
                            $industry_template_tags[] = $industry_field_tag;

                            if($product->$field_name){
                                $template_product[$industry_field_tag] = $product->$field_name;
                            }
                        }

                        if($industry_template_tags){
                            $product_tags = array_merge($product_tags, $industry_template_tags);
                        }
                    }
                }
            // }

            foreach ($product_tags as $key => $value) {
                $template = str_replace($value, $template_product[$value], $template);
            }

            return $template;
        } else {
            return false;
        }
    }

    //////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Price Calculation (3rd Step) //////////////
    //////////////////////////////////////////////////////////////////////////


    public function getCsv($import)
    {
        $import_id = $import->id;
        $file_url = str_replace('storage', 'public', $import->csv_file_path);
        $type = pathinfo($file_url, PATHINFO_EXTENSION);
        try {
            $redis = Redis::connection();
            $json = $redis->get('import_csv_' . $import_id);
            if ($json) {
                $csv_data = json_decode($json);
            }
        } catch (\Predis\Connection\ConnectionException $e) {
        }
        if (!$csv_data) {
            $csv_data = $this->csvToArrayModified($file_url, $type, $import->delimiter);
            try {
                $redis = Redis::connection();
                $redis->set('import_csv_' . $import_id, json_encode($csv_data));
            } catch (\Predis\Connection\ConnectionException $e) {
            }
        }
        return $csv_data;
    }

    public function setEanField($drm, $csv_fields)
    {
        if ($drm->ean_field == 1) {
            $this->ean_field = $csv_fields->ean;
            $this->ean = "ean";
        } else {
            $this->ean_field = $csv_fields->item_number;
            $this->ean = "item_number";
        }
    }

    public function postImportTmpProducts($import_id = null, $return_url = null)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $this->count = 0;
        $message = ['text' => 'Initializing Import'];
        sentProgress($message, 'import');
        if ($import_id == null) {
            $import_id = Request::input('drm_import_id');
        }

        $user_id = CRUDBooster::myParentId();
        $drm = DB::table('drm_imports')->where('id', $import_id)->first();
        $fallback = $_REQUEST['fallback'];
        $import_update['fallback'] = $fallback;
        if ($return_url == null) {
            $import_update['current_step'] = 'products';
            $return_url == Request::input('return_url');
        }

        $fields = DB::table('drm_product_fields')->where('drm_import_id', $import_id)->first();
        $this->setEanField($drm, $fields);

        DB::table('drm_imports')->where('id', $import_id)->update($import_update);
        $this->existing_products = $this->getExistingProducts($user_id, $import_id);

        $this->settings = DB::table('drm_import_settings')->where('drm_import_id', $import_id)->first();
        $file_url = str_replace('storage', 'public', $drm->csv_file_path);

        $this->service = new ImportService($drm);

        $type = pathinfo($file_url, PATHINFO_EXTENSION);

        $message = ['text' => 'Getting Data From Sheet'];
        sentProgress($message, 'import');

        if ($type == 'xml') {
            $csv_data = $this->xmlToArray($file_url);
        } else {
            $csv_data = $this->getCsv($drm);
        }

        DB::table('tmp_drm_products')->where('drm_import_id', $import_id)->delete();
        DB::table('drm_import_errors')->where('drm_import_id', $import_id)->delete();

        if ($drm->image_backup) {
            Storage::disk('spaces')->deleteDirectory('import/backup/images/' . $drm->id);
        }

        $message = ['text' => 'Starting Import'];
        // event(new progressEvent($message));
        sentProgress($message, 'import');

        $key = array_map('trim', $csv_data[0]);
        $key = array_map('removeDots', $key);
        $key = makeArrayUtf8(makeArrayUtf8($key));
        $key_count = count($key);
        unset($csv_data[0]);
        $total = count($csv_data);

        if( empty($fields->dropmatix_product_brands_id) && !empty($fields->brand)){

            $existing_brands = DropmatixProductBrand::where('user_id',  $user_id)->pluck('brand_name')->toArray();

            $brand_field_key = array_search($fields->brand, $key);
            $brand_logo_key = null;

            if( !empty($fields->brand_logo) ){
                $brand_logo_key = array_search($fields->brand_logo, $key);
            }

            $new_brands = [];

            array_walk($csv_data, function($data) use($brand_field_key, $brand_logo_key, &$new_brands){

                $brands = [];
                $brands['user_id'] = CRUDBooster::myParentId();
                $brands['brand_name'] = trim($data[$brand_field_key]);
                $brands['brand_logo'] = !empty($brand_logo_key) ? json_encode([$data[$brand_logo_key]]) : null;
                $brands['created_at'] = Carbon::now()->toDateTimeString();
                $brands['updated_at'] = Carbon::now()->toDateTimeString();

                if(!empty($brands['brand_name'])){
                    $new_brands[] = $brands;
                }
            });

            // $brands_unique_name = array_unique(array_column($new_brands, 'brand_name'));
            $brands_unique_name = array_unique(array_map('strtolower', array_column($new_brands, 'brand_name')));
            $new_brands = array_intersect_key($new_brands, $brands_unique_name);
            // $new_brands = array_unique($new_brands, SORT_REGULAR);

            $new_brands = array_filter($new_brands, function ($value) use ($existing_brands) {
                return !in_array(strtolower($value['brand_name']), array_map('strtolower', $existing_brands));
            });

            $new_brands = array_values($new_brands);

            if($new_brands){
                collect($new_brands)
                ->chunk(500)
                ->each(function($brand_chunk){
                    $brand_chunk = $brand_chunk->toArray();
                    DropmatixProductBrand::insert($brand_chunk);
                });
            }

        }

        $dropmatix_existing_brands = DropmatixProductBrand::where('user_id', $user_id)->select('brand_name', 'id')->get();



        $country = DB::table('countries')->where('id', $drm->country_id)->first();
        // $this->table = "drm_translation_".$country;


        LazyCollection::make(function () use ($csv_data, $key_count) {
            foreach ($csv_data as $line) {
                if (count($line) == $key_count && !containsOnlyNull($line)) {
                    yield makeArrayUtf8(makeArrayUtf8($line));
                }
            }
        })
            ->chunk(200)
            ->each(function ($lines) use ($import_id, $fields, $drm, $user_id, $total, $message, $key,$country, $dropmatix_existing_brands) {
                $this->insertArray = null;
//                $this->errors = null;
                array_walk($lines, function ($chunks) use ($drm, $fields, $key,$country, $dropmatix_existing_brands) {
                    array_walk($chunks, function ($chunk) use ($drm, $fields, $key,$country, $dropmatix_existing_brands) {
                        $data = array_combine($key, $chunk);
                        $data = makeArrayUtf8(makeArrayUtf8($data));
                        $valid = $this->drmErrorReport($data, $fields, $drm,$country);
//                        if (isset($valid['errors'])) {
//                            $this->errors[] = $valid['errors'];
//                        }

                            if ($valid['valid']) {
                                $this->insertArray[] = $this->insertSingleProduct($drm, $data, $fields, $country, $dropmatix_existing_brands);
                            } else {
                                $this->invalid++;
                            }

                        $this->count++;
                    });
                });
                if (is_array($this->insertArray)) {
                    $last = end($this->insertArray);
                    $name = $last['name'];
                    TmpDrmProduct::insert($this->insertArray);
                }
//                if ($this->errors != null) {
//                    try {
//                        DB::table('drm_import_errors')->insert($this->errors);
//                    } catch (\Exception $e) {
//                    }
//                }
                $message = ['total' => $total, 'count' => $this->count, 'percent' => round(($this->count / $total) * 100, 2), 'invalid' => $this->invalid, 'name' => $name];
                sentProgress($message, 'import');
            });

        $message = ['text' => 'Creating Categories', 'percent' => '100'];
        sentProgress($message, 'import');

        $categories = TmpDrmProduct::where('drm_import_id', $import_id)->pluck('category')->unique()->toArray();
        $categories = array_map('json_decode', $categories);
        $categories = removeNulls(array_unique(Arr::flatten($categories)));


        $trans_cat = "category_name_" . $country->language_shortcode;
        $drm_categories = [];
        if (\Schema::hasColumn('drm_category', $trans_cat)) {
            $categories_cache = $this->getCategoryCache($drm->user_id);
            $drm_categories = $categories_cache->pluck($trans_cat)->toArray();
            if (is_array($drm_categories)) {
                $drm_categories = removeNulls($drm_categories);
            }
        }

        $all_imported_categories = array();
        foreach ($categories as $category) {
            if (!in_array($category, $drm_categories)) {
                DB::table('drm_category')
                    ->insert([
                        'drm_import_id' => $id,
                        $trans_cat => $category,
                        'country_id' => $drm->country_id,
                        'user_id' => $drm->user_id,
                    ]);
            }
            $all_imported_categories[] = $category;
        }

        $inserted_categories = DB::table('drm_category')->whereIn($trans_cat, $all_imported_categories)->where('user_id', $drm->user_id)->pluck('id')->toArray();
        $inserted_categories = json_encode($inserted_categories);

        DB::table('drm_imports')->where('id', $drm->id)->update(['category_ids' => $inserted_categories]);
        return true;
    }

    public function insertSingleProduct($drm, $data, $fields,$country, $dropmatix_existing_brands): array
    {
        $duplicate_ean = 0;
        if (in_array($this->validateEan($data[$fields->ean]), $this->existing_products)) {
            $duplicate_ean = 1;
        }
        $image_headers = explode('|', $fields->image);
        $image_separator = $drm->image_separator;
        $final_array = array();
        foreach ($image_headers as $header) {
            $csv_images = explode($image_separator, $data[$header]);
            $final_array = array_merge($final_array, $csv_images);
        }
        $json_image = $this->getImageJson($final_array, $drm);
        $array_img = json_decode($json_image);
        if (count($array_img) < 1) {
            $json_image = json_encode(array("https://drm-file.fra1.digitaloceanspaces.com/public/images/icons/drm-logo.png"));
        } else {
            $json_image = json_encode($array_img);
        }

        $tag_fields = explode('|', $fields->dropfunnel_tags) ?? [];
        $tags = explode(',', $fields->dropfunnel_tags_manual) ?? [];

        foreach ($tag_fields as $tag_field) {
            $tags[] = $data[$tag_field];
        }

        $tags = generateTags($tags);
        if ($drm->item_number_prefix != null) {
            $item_number = $drm->item_number_prefix . "" . strip_tags($data[$fields->item_number]);
        } else {
            $item_number = strip_tags($data[$fields->item_number]);
        }

        $ean = $this->validateEan($data[$fields->ean]);

        if ($drm->unlimited_quantity) {
            $stock = $drm->fixed_stock;
        } else {
            $stock = strip_tags($data[$fields->stock]);
        }

        if (is_numeric($fields->delivery_days)) {
            $delivery_days = (int)$fields->delivery_days;
        } else {
            $delivery_days = (int)strip_tags($data[$fields->delivery_days]);
        }


        if ($item_number == null || $item_number == "") {
            $item_number = $ean;
        }

        $ek_price = drm_convert_european_to_decimal($data[$fields->ek_price], $drm->money_format);
        $price = $this->service->calculatePurchasePrice($ek_price, $this->settings);

        if($data[$fields->description]){
            $data[$fields->description] = preg_replace('/\r\n|\r|\n/', '<br>', $data[$fields->description]);
        }
        if($data[$fields->name]){
            $data[$fields->name] = preg_replace('/\r\n|\r|\n/', ' ', $data[$fields->name]);
        }
        $description = $this->service->generateDescription($data[$fields->description], $this->settings);

        $shipping_cost = is_numeric($fields->shipping_cost) ? $fields->shipping_cost : drm_convert_european_to_decimal($data[$fields->shipping_cost], $drm->money_format);

        $product_unit = null;

        if( in_array($fields->item_unit, array_keys(\App\Enums\ProductsWeightUnit::WEIGHT_UNITS)) ){
            $product_unit = $fields->item_unit;
        }else if( in_array($data[$fields->item_unit], array_keys(\App\Enums\ProductsWeightUnit::WEIGHT_UNITS)) ){
            $product_unit = $data[$fields->item_unit];
        }

        if ($drm->custom_category == 1) {
            $merge_category = [];
            $drm_existing_category = $drm->existing_category;
            $fetch_category_from_drm = DB::table('drm_category')->where('id', $drm_existing_category)->first();

            if ($fetch_category_from_drm != null) {
                $fetch_category_from_drm = (array)$fetch_category_from_drm;
                $fetch_category_from_drm = $fetch_category_from_drm["category_name" . "_" . $country->language_shortcode];
            }

            if ($fetch_category_from_drm != null) {
                $merge_category[] = $fetch_category_from_drm;
            }

            if ($drm->manual_category != null) {
                $drm_manual_category = $drm->manual_category;
                $merge_category[] = $drm_manual_category;
            }

            $drm_n_manual_category = json_encode(array_unique($merge_category));
            $field_category = $this->service->generateCategory($data, $fields->category);

            // if(isLocal()){

            $check_field_category = array_filter(json_decode($field_category, true));
            if (empty($check_field_category)) {
                $category = json_encode(array_unique(json_decode($drm_n_manual_category, true)));
            } else {
                $category = json_encode(array_unique(array_merge(json_decode($drm_n_manual_category, true), json_decode($field_category, true))));
            }

            // }else{
            // 	$category = json_encode(array_merge(json_decode($drm_n_manual_category, true),json_decode($field_category, true)));
            // }
        } else {
            $category = $this->service->generateCategory($data, $fields->category);
        }


        $industry_template_fields = $fields->industry_template ? json_decode($fields->industry_template, true) : [];

        // if (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) {

            if( in_array(strtolower(key($industry_template_fields)), array_keys(config('industry_template'))) ){
                $industry_template_data = [];

                if ($country->language_shortcode == 'de' && !empty($industry_template_fields)) {

                    foreach($industry_template_fields as $key => $field){

                        foreach($field as $property => $value){

                            $industry_template_data_property[$property] = ['de' => !empty($data[$value]) ? $data[$value] : null, 'en' => null];
                        }

                        $industry_template_data[$key] = $industry_template_data_property;
                    }

                } else if ($country->language_shortcode == 'en' && !empty($industry_template_fields)) {

                    foreach($industry_template_fields as $key => $field){

                        foreach($field as $property => $value){

                            $industry_template_data_property[$property] = ['en' => !empty($data[$value]) ? $data[$value] : null, 'de' => null];

                        }

                        $industry_template_data[$key] = $industry_template_data_property;
                    }

                }
            }else{
                $industry_template_data = [];

                if ($country->language_shortcode == 'de' && !empty($industry_template_fields)) {

                    foreach($industry_template_fields as $key => $field){

                        foreach($field as $property => $value){

                            $temp_value = null;

                            if(isset($data[$value]) && !empty($data[$value])){
                                $temp_value = $data[$value];
                            }else if(isset($data[$value]) && empty($data[$value])){
                                $temp_value = null;
                            }else if(!isset($data[$value])){
                                $temp_value = $value;
                            }

                            $industry_template_data_property[$property] = ['de' => $temp_value, 'en' => null];
                        }

                        $industry_template_data[$key] = $industry_template_data_property;
                    }

                } else if ($country->language_shortcode == 'en' && !empty($industry_template_fields)) {

                    foreach($industry_template_fields as $key => $field){

                        foreach($field as $property => $value){

                            $temp_value = null;

                            if(isset($data[$value]) && !empty($data[$value])){
                                $temp_value = $data[$value];
                            }else if(isset($data[$value]) && empty($data[$value])){
                                $temp_value = null;
                            }else if(!isset($data[$value])){
                                $temp_value = $value;
                            }

                            $industry_template_data_property[$property] = ['en' => $temp_value, 'de' => null];

                        }

                        $industry_template_data[$key] = $industry_template_data_property;
                    }

                }
            }
        // } else {
        //     $industry_template_data = [];

        //     if ($country->language_shortcode == 'de' && !empty($industry_template_fields)) {

        //         foreach($industry_template_fields as $key => $field){

        //             foreach($field as $property => $value){

        //                 $industry_template_data_property[$property] = ['de' => !empty($data[$value]) ? $data[$value] : null, 'en' => null];
        //             }

        //             $industry_template_data[$key] = $industry_template_data_property;
        //         }

        //     } else if ($country->language_shortcode == 'en' && !empty($industry_template_fields)) {

        //         foreach($industry_template_fields as $key => $field){

        //             foreach($field as $property => $value){

        //                 $industry_template_data_property[$property] = ['en' => !empty($data[$value]) ? $data[$value] : null, 'de' => null];

        //             }

        //             $industry_template_data[$key] = $industry_template_data_property;
        //         }

        //     }

        // }

        // dd($industry_template_data);
        // End Industry Template

        // if(isLocal() || in_array(CRUDBooster::myParentId(),[212, 2650, 98])){

            $tax = is_numeric($fields->tax) ? $fields->tax : $data[$fields->tax];
            $tax_type = array_search($tax,[
                Tax::ORIGINAL => $country->tax_rate,
                Tax::REDUCED => $country->reduced_tax_rate
            ]);

            $factory = [
                'drm_import_id' => $drm->id,
                'name' => trim($data[$fields->name]),
                'item_number' => $item_number,
                'item_weight' => strip_tags($data[$fields->item_weight]),
                'item_unit' => $product_unit,
                'item_size' => strip_tags($data[$fields->item_size]),
                'item_color' => strip_tags($data[$fields->item_color]),
                'production_year' => strip_tags($data[$fields->production_year]),
                'brand' => strip_tags($data[$fields->brand]),
                'materials' => strip_tags($data[$fields->materials]),
                'ean' => $ean,
                'description' => $description,
                'short_description' => $data[$fields->short_description],
                'image' => $json_image,
                'original_ek' => $ek_price,
                'ek_price' => $price,
                'shipping_cost' => $shipping_cost ?? 0,
                'vk_price' => 0,
                'uvp' => drm_convert_european_to_decimal($data[$fields->uvp], $drm->money_format),
                'stock' => $stock,
                'category' => $category,
                'duplicate_ean' => $duplicate_ean,
                'update_enabled' => '1',
                'status' => $fields->status,
                'gender' => strip_tags($data[$fields->gender]),
                'user_id' => $drm->user_id,
                'delivery_company_id' => $drm->delivery_company_id,
                'country_id' => $drm->country_id,
                'delivery_days' => $delivery_days,
                'tags' => $tags,
                'tax_type'  => $tax_type,
            ];

            if($fields->offer_options && isset($data[$fields->offer_options]))
            {
                $offer_options_str = $data[$fields->offer_options];
                if(drmIsJSON($offer_options_str))
                {
                   $factory['offer_options'] = $offer_options_str;
                }
            }

            if(!empty($industry_template_data)){
                $factory['industry_template_data'] = json_encode($industry_template_data);
                $factory['tax'] = $tax;
            }else{
                $factory['tax'] = $tax_type ? $tax : false;
            }

            // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 62])){
                unset($factory['brand']);

                if( empty($fields->dropmatix_product_brands_id) && !empty($fields->brand) ){

                    // $existing_brand = $dropmatix_existing_brands->where('brand_name', trim($data[$fields->brand]))->first();
                    $existing_brand = $dropmatix_existing_brands->filter(function ($value) use ($data, $fields) {
                        return strcasecmp($value->brand_name, trim($data[$fields->brand])) == 0;
                    })->values();

                    // $existing_brand =  $dropmatix_existing_brands->filter(function ($brand) use($data, $fields){
                    //     $pattern = '/'.preg_quote($brand->brand_name,'/').'/';

                    //     return preg_match($pattern, $data[$fields->brand]);
                    // });

                    $factory['brand'] = $existing_brand[0]->id;

                }elseif( !empty($fields->dropmatix_product_brands_id) ){
                    $factory['brand'] = $fields->dropmatix_product_brands_id;
                }
            // }

            return $factory;
    }

    public function validateEan($ean)
    {
        $ean_original = strip_tags($ean);
        $sanitize = explode('.', $ean_original);
        $ean_original = $sanitize[0];
        $ean = trim($ean_original);
        $int_ean = (int)$ean;

        $ean_length = strlen($ean);

        if ($ean_length >= 1 && $ean_length <= 13 && $int_ean) {
            return str_pad($int_ean, 13, '0', STR_PAD_LEFT);
        } else {
            return null;
        }
    }

    public function getExistingProducts($user_id, $import_id)
    {
        return Cache::remember('existing_products_' . $user_id, 05.0, function () use ($user_id, $import_id) {
            return DB::table('drm_products')->where('user_id', $user_id)->whereNull('deleted_at')->where('drm_import_id', '<>', $import_id)->pluck("ean")->toArray();
        });
    }

    public function getImageJson($array, $drm)
    {
        $final_img = array();
        $prefix = $drm->image_prefix;
        $suffix = $drm->image_suffix;
        foreach ($array as $key => $value) {
            $img = str_replace(' ', '', $value);
            $img = trim($img, '"');
            if ($img != null) {
                if (filter_var($img, FILTER_VALIDATE_URL) === FALSE && $prefix != null) {
                    $img = $this->importImagePrefix($prefix, $img);
                }

                if ($suffix != null) {
                    $img = $this->importImageSuffix($suffix, $img);
                }

                if ($drm->image_validation) {
                    $image_exists = checkImageUrl($img);
                } else {
                    $image_exists = true;
                }

                if ($image_exists) {
                    $final_img[] = $img;
                }
            }
        }
        return json_encode($final_img);
    }


    public function importImageSuffix($suffix, $image): string
    {
        return $image . $suffix;
    }


    public function drmErrorReport($data, $field, $drm,$country): array
    {
//        $ean_field = $this->ean_field;
//        $ean = $this->ean;
//        $line = $this->count + 1;
        $valid = true;
//        $errors = [
//            'drm_import_id' => $drm->id
//        ];

        if ((trim($data[$field->ean]) == '') && (trim($data[$field->item_number]) == '')) {
            $valid = false;
            trace('EAN: '.$data[$field->ean].'. Reason : EAN');
//            $errors['error_type'] = 'EAN & SKU Missing';
//            $errors['error'] = "Incorrect / Missing value under the field '" . $field->ean . "' and '" . $field->item_number . "' at line number: " . $line;
        }

        if (trim($data[$field->name]) == '') {
            $valid = false;
            trace('EAN: '.$data[$field->ean].'. Reason : Title');
//            $errors['error_type'] = 'Title Missing';
//            $errors['error'] = "Incorrect / Missing value under the field '" . $field->name . "' at line number: " . $line;
        }

        if (trim($data[$field->description]) == '') {
            $valid = false;
            trace('EAN: '.$data[$field->ean].'. Reason : Description');
//            $errors['error_type'] = 'Description Missing';
//            $errors['error'] = "Incorrect / Missing value under the field '" . $field->description . "' at line number: " . $line;
        }

        $categories = removeNulls(json_decode($this->service->generateCategory($data, $field->category)) )?? [];

        if (count($categories) == 0 && trim($field->category) != null && !$drm->custom_category && !$drm->existing_category) {
            $valid = false;
            trace('EAN: '.$data[$field->ean].'. Reason : Category');
//            $errors['error_type'] = 'Category Missing';
//            $errors['error'] = "Incorrect / Missing value under the field '" . $field->category . "' at line number: " . $line;
        }

        if (drm_convert_european_to_decimal(trim($data[$field->ek_price]), $drm->money_format) == 0) {
            $valid = false;
            trace('EAN: '.$data[$field->ean].'. Reason : Price');
//            $errors['error_type'] = 'Purchase Price Missing';
//            $errors['error'] = "Incorrect / Missing value under the field '" . $field->ek_price . "' at line number: " . $line;
        }

        if (trim($data[$field->description]) == '') {
            $valid = false;
            trace('EAN: '.$data[$field->ean].'. Reason : Description');
//            $errors['error_type'] = 'Description Missing';
//            $errors['error'] = "Incorrect / Missing value under the field '" . $field->description . "' at line number: " . $line;
        }


        $tax = is_numeric($field->tax) ? (float)$field->tax : (float)$data[$field->tax];
        if(!in_array($tax,[$country->tax_rate,$country->reduced_tax_rate])){
            $valid = false;
            trace('EAN: '.$data[$field->ean].'. Reason : Tax');
        }



        $report['valid'] = $valid;
//        if (!$valid) {
//            $report['errors'] = $errors;
//        }
        return $report;
    }


    //END


    //////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Porducts List (4th Step) //////////////////
    //////////////////////////////////////////////////////////////////////////


    public function getTmpDrmProducts()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $id = $_REQUEST['id'];

        $filter = DB::table('drm_import_filter')->where('drm_import_id', $id)->first();

        $excluded = $this->getFilteredIds($id);

        // if(isLocal()){
        if (!json_decode($filter->filter)) {
            $products = TmpDrmProduct::where('drm_import_id', $id);
        } else {
            if (!$filter->blacklist) {
                $products = TmpDrmProduct::whereIn('id', $excluded);
            } else {
                $products = TmpDrmProduct::where('drm_import_id', $id)->whereNotIn('id', $excluded);
            }
        }

        $products = $products->get() ?? [];
        // }
        // else{
        // 	$products = TmpDrmProduct::where('drm_import_id',$id)->get();
        // }

        $all_products = [];
        foreach ($products as $key => $value) {
            $image = '';
            $value->image = implode(',', $value->image);
            $all_products[] = $value;
        }
        return DataTables::of($all_products)->make(true);
    }


    public function getTmpDrmProduct()
    {
        return TmpDrmProduct::find(Request::input('id'));
    }

    public function postTmpDrmProductUpdate()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $product = TmpDrmProduct::find(Request::input('id'));
        $product->name = Request::input('name');
        $product->item_number = Request::input('item_number');
        $product->description = Request::input('description');
        $product->ek_price = Request::input('ek_price');
        $product->vk_price = Request::input('vk_price');
        // $product->vat=Request::input('vat');
        $product->stock = Request::input('stock');
        $product->save();
        return "success";
    }


    public function postSetOverwrite()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $import = DrmImport::findOrFail(Request::input('id'));
        $import->overwrite = Request::input('overwrite');
        $import->save();
        return "success";
    }

    public function getImportErrors()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $id = $_REQUEST['id'];
        $errors = DB::table('drm_import_errors')->where('drm_import_id', $id)->get();
        return view('admin.drm_import.drm_import_errors', compact('errors'));
    }

    public function getDeleteTmp()
    {
        $id = $_REQUEST['id'];
        $import_id = $_REQUEST['import_id'];
        TmpDrmProduct::where(['id' => $id, 'drm_import_id' => $import_id])->delete();
        return response()->json([
            'status' => 'SUCCESS',
            'code' => 200
        ]);
    }


    //////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Filter (5th Step) /////////////////////////
    //////////////////////////////////////////////////////////////////////////


    public function postFilter()
    {
        $id = $_REQUEST['drm_import_id'];
        $field = $_REQUEST['field'];
        $value = $_REQUEST['value'];

        $filter = DB::table('drm_import_filter')->where('drm_import_id', $id)->first();
        $filter = json_decode($filter->filter, true);

        if ($field != 'price_below' && $field != 'price_more_than') {
            if (is_array($filter[$field])) {
                if (in_array($value, $filter[$field])) {
                    return response('Filter already exists!', 422);
                }

                // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){

                if($_REQUEST['operator'] == '='){
                    foreach($filter['stock_operator'] as $operator){
                        if($operator == '<' || $operator == '>'){
                            return response('Please Remove "<", ">" Filter First to Filter With "=" Operator.', 422);
                        }
                    }
                }else if($_REQUEST['operator'] == '<' || $_REQUEST['operator'] == '>'){
                    foreach($filter['stock_operator'] as $operator){
                        if($operator == '='){
                        return response('Please Remove "=" Filter First to Filter With "<", ">" Operator.', 422);
                        }
                    }
                }

                $operator_exist = false;
                if( $field == 'stock' && in_array($_REQUEST['operator'], $filter['stock_operator']) ){
                    $operator_exist = true;
                    $matched_value_index = array_search($_REQUEST['operator'], $filter['stock_operator']);
                    $filter['stock'][$matched_value_index] = $value;
                }
                // }

            }

            // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){
            if( $field == 'stock' && !$operator_exist ){
                $filter[$field][] = $value;
            }else if($field != 'stock'){
                $filter[$field][] = $value;
            }
            // }else{
            //     $filter[$field][] = $value;
            // }
        } else {
            $filter[$field] = $value;
        }

        // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){
        if ($field == 'stock') {
            if( is_array($filter['stock_operator']) && !in_array($_REQUEST['operator'], $filter['stock_operator']) ){
                $filter["stock_operator"][] = $_REQUEST['operator'];
            }else if(!is_array($filter['stock_operator'])){
                $filter["stock_operator"][] = $_REQUEST['operator'];
            }
        }
        // }else{
        //     if ($field == 'stock') {
        //         $filter["stock_operator"][] = $_REQUEST['operator'];
        //     }
        // }

        $json = json_encode($filter);

        DB::table('drm_import_filter')->updateOrInsert(
            ['drm_import_id' => $id],
            ['filter' => $json]
        );
    }

    public function getRemoveFilter()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $id = $_REQUEST['id'];
        Session::remove('drm_import_' . $id);
        DB::table('drm_import_filter')->where('drm_import_id', $id)->delete();
        return 'success';
    }

    public function getFilterSession()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $filter_json = DB::table('drm_import_filter')->where('drm_import_id', $_REQUEST['id'])->first();
        if ($filter_json->filter == null || $filter_json->filter == '') {
            return 'null';
        } else {
            return $filter_json->filter;
        }
    }


    public function getExcludedProducts()
    {
        $id = $_REQUEST['id'];
        $ids = $this->getFilteredIds($id);

        $filter = DB::table('drm_import_filter')->where('drm_import_id', $id)->first();
        // if(isLocal()){
        // 	$products = TmpDrmProduct::where('drm_import_id',$id)->get();
        // }
        // else{
        if (!json_decode($filter->filter)) {
            $products = TmpDrmProduct::where('drm_import_id', $id);
        } else {
            if (!$filter->blacklist) {
                $products = TmpDrmProduct::whereIn('id', $ids);
            } else {
                $products = TmpDrmProduct::where('drm_import_id', $id)->whereNotIn('id', $ids);
            }
        }
        // }
        return DataTables::of($products)
            ->make(true);
    }

    public function getFilteredIds($import_id)
    {
        $filter_json = DB::table('drm_import_filter')->where('drm_import_id', $import_id)->first();
        $filters = json_decode($filter_json->filter, true);
        $stock_opertaor = $filters['stock_operator'];
        $products = TmpDrmProduct::where('drm_import_id', $import_id);

        if (!$filters) {
            return [];
        } else {
            $products = $products->where(function ($query) use ($filters, $stock_opertaor) {
                if (isset($filters['price_more_than'])) {
                    $query->orWhere('ek_price', '>', (float)$filters['price_more_than']);
                }

                if (isset($filters['price_below'])) {
                    $query->orWhere('ek_price', '<', (float)$filters['price_below']);
                }

                if (isset($filters['ean'])) {
                    foreach ($filters['ean'] as $ean) {
                        $query->orWhere('ean', $ean);
                    }
                }

                if (isset($filters['category'])) {
                    foreach ($filters['category'] as $category) {
                        $query->whereJsonContains('category', $category, "or");
                    }
                }

                if (isset($filters['stock'])) {
                    // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){
                    foreach ($filters['stock'] as $key => $value) {

                        if ($stock_opertaor[$key] == '=') {
                            $query->orWhere('stock', (int)$value);
                        }

                        if ($stock_opertaor[$key] == '<') {
                            $check_greater_than_operator = array_search(">", $stock_opertaor);
                            if(!empty($check_greater_than_operator)){
                                $query->orWhere(function($find_stock) use ($value, $filters, $check_greater_than_operator){
                                    $find_stock->where([
                                        [ 'stock', '<', (int)$value ],
                                        [ 'stock', '>', (int)$filters['stock'][$check_greater_than_operator] ]
                                    ]);
                                });
                                break;
                            }else{
                                $query->orWhere('stock', '<', (int)$value);
                            }
                        }

                        if ($stock_opertaor[$key] == '>') {
                            $check_less_than_operator = array_search("<", $stock_opertaor);

                            if(!empty($check_less_than_operator)){
                                $query->orWhere(function($find_stock) use ($value, $filters, $check_less_than_operator){
                                    $find_stock->where([
                                        [ 'stock', '>', (int)$value ],
                                        [ 'stock', '<', (int)$filters['stock'][$check_less_than_operator] ]
                                    ]);
                                });
                                break;
                            }else{
                                $query->orWhere('stock', '>', (int)$value);
                            }

                        }

                    }
                    // }else{
                    //     foreach ($filters['stock'] as $key => $value) {
                    //         if ($stock_opertaor[$key] == '=') {
                    //             $query->orWhere('stock', (int)$value);
                    //         }
                    //         if ($stock_opertaor[$key] == '<') {
                    //             $query->orWhere('stock', '<', (int)$value);
                    //         }
                    //         if ($stock_opertaor[$key] == '>') {
                    //             $query->orWhere('stock', '>', (int)$value);
                    //         }
                    //     }
                    // }
                }

                if (isset($filters['brand'])) {
                    foreach ($filters['brand'] as $brand) {
                        $query->orWhere('brand', $brand);
                    }
                }

                if (isset($filters['gender'])) {
                    foreach ($filters['gender'] as $gender) {
                        $query->orWhere('gender', $gender);
                    }
                }

                if (isset($filters['materials'])) {
                    foreach ($filters['materials'] as $material) {
                        $query->orWhere('materials', $material);
                    }
                }

                if (isset($filters['status'])) {
                    foreach ($filters['status'] as $status) {
                        $query->orWhere('status', $status);
                    }
                }
                return $query;
            });
        }
        return $products->pluck('id')->toArray();
    }




    //////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Search And Replace (Final Step) ///////////
    //////////////////////////////////////////////////////////////////////////


    public function postSearchAndReplace()
    {
        $drm_id = Request::input('drm_import_id');
        $keyword = makeUtf8(Request::input('keyword'));
        $replace_with = makeUtf8(Request::input('replace_with'));
        $queries = DB::select(DB::raw("select CONCAT(
				'UPDATE tmp_drm_products SET ',
				column_name,
				' = REPLACE(',COLUMN_NAME,',''$keyword'',''$replace_with'') where drm_import_id = $drm_id;') AS query
				from information_schema.columns
				where table_name = 'tmp_drm_products'"));
        $queries = collect($queries);
        $queries = $queries->unique();
        $filtered = $queries->filter(function ($item) {
            if (strpos($item->query, "SET id") || strpos($item->query, "SET drm_import_id")
                || strpos($item->query, "SET drm_import_id") || strpos($item->query, "SET country_id")
                || strpos($item->query, "SET user_id") || strpos($item->query, "SET delivery_company_id")
                || strpos($item->query, "SET duplicate_ean") || strpos($item->query, "SET created_at")
                || strpos($item->query, "SET updated_at") || strpos($item->query, "SET update_enabled")
                || strpos($item->query, "SET language_id")
            ) {
                return false;
            } else {
                return true;
            }
        });
        foreach ($filtered as $query) {
            $res = DB::statement($query->query);
        }
        return "search_and_replace";
    }


    public function getAssignEan()
    {
        $import_id = $_REQUEST['import_id'];
        $user_id = CRUDBooster::myId();
        $query = DB::table('tmp_drm_products')->where('drm_import_id', $import_id);
        $query = $query->where(function ($query) {
            $query->whereNull('ean')
                ->orWhere('ean', "");
        });
        $products = $query->get();
        $count = count($products);
        $ean_query = DB::table('custom_eans')->where('user_id', $user_id)->where('used', 0)->limit($count)->cursor();
        $eans = $ean_query->toArray();
        $ean_count = count($eans);
        $limit_products = $products->take($ean_count);
        foreach ($limit_products as $key => $product) {
            $ean = $eans[$key]->ean;
            DB::table('tmp_drm_products')->where('id', $product->id)->update(['ean' => $ean]);
            // DB::table('custom_eans')->where('ean',$ean)->where('user_id',$user_id)->update(['used' => 1,'product_id' => $product->id]);
        }
        $ean_ids = $ean_query->pluck('id')->toArray();
        $product_ids = $limit_products->pluck('id')->toArray();
        //
        foreach (array_chunk($ean_ids, 500) as $chunk) {
            DB::table('custom_eans')->whereIn('id', $chunk)->update(['used' => 1]);
        }

        foreach (array_chunk($product_ids, 500) as $chunk) {
            DB::table('tmp_drm_products')->whereIn('id', $chunk)->update(['ean_field' => 0]);
        }

        return true;
    }


    public function getCsvImportToDatabase()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        return redirect()->back();
    }

    public function postCsvImportToDatabase()
    {
        ini_set('memory_limit', -1);
        ini_set('max_execution_time', '0'); // for infinite time of execution
        $import_id = Request::input('drm_import_id');
        $user_id = CRUDBooster::myParentId();
        $enable_automagic = Request::input('automagic');
        DB::table('drm_import_settings')->updateOrInsert(
            ['drm_import_id' => $import_id],
            [
                'automagic' => $enable_automagic ?? 0,
            ]
        );

        $one_one_sync = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->get();


        $import_check = $this->importProductCheck();
        $drm = DrmImport::find($import_id);

        $fields = DB::table('drm_product_fields')->where('drm_import_id', $import_id)->first();
        $this->setEanField($drm, $fields);

        // if(isLocal()){
        $filter = DB::table('drm_import_filter')->where('drm_import_id', $import_id)->first();

        $excluded = array();
        $blacklist = 1;
        if (json_decode($filter->filter)) {
            $excluded = $this->getFilteredIds($import_id);
            $blacklist = $filter->blacklist;
        }

        if ($blacklist) {
            $data = TmpDrmProduct::where('drm_import_id', $import_id)->whereNotIn('id', $excluded)->distinct('ean')->get();
        } else {
            $data = TmpDrmProduct::where('drm_import_id', $import_id)->whereIn('id', $excluded)->distinct('ean')->get();
        }
        // }

        // else{
        // 	$excluded = $this->getFilteredIds($import_id);
        // 	$data = TmpDrmProduct::where('drm_import_id',$import_id)->whereNotIn('id',$excluded)->distinct('ean')->get();
        // }


        $total = count($data);
        $country = DB::table('countries')->where('id', $drm->country_id)->first();

        $trans_cat = "category_name_" . $country->language_shortcode;
        $lang = $country->language_shortcode;
        // $Import_filter = DB::table('drm_import_filter')->where('drm_import_id',$import_id)->first();
        // $filter = json_decode($Import_filter->filter, true);
        // DrmProduct::where('drm_import_id',$import_id)->delete();
        $category_ids = json_decode($drm->category_ids);
        $imported_categories = DB::table('drm_category')->select('id', $trans_cat)->whereIn('id', $category_ids)->cursor();
        $imported_categories_all = collect($imported_categories->all());

        $update_status = makeUpdateStatusJson();

        $chunk_size = 100;
        $count = 0;
        $trial_checked = 0;
        $used_magic = 0;


        foreach ($data->chunk($chunk_size) as $chunks) {
            $new_product_ids = array();
            $productCategories = array();
            foreach ($chunks as $record) {
                if (CRUDBooster::myPrivilegeId() == '3') {
                    if (($import_check['limit'] == '') and ($count >= $import_check['product_amount'])) break;
                }

//                $record->stock = str_replace(',00','',$record->stock);
//                if (filter_var($record->stock, FILTER_VALIDATE_INT) === false && (int)$record->stock == 0) {
//                    $this->invalid++;
//                    continue;
//                }
                $record->stock = (int)$record->stock;

                if ($record->stock < 0) {
                    $record->stock = 0;
                }

                if ($this->checkValid($record)) {
                    $record = $record->toArray();

                    $record['title'] = [$lang => $record['name']];
                    $record['description'] = [$lang => $record['description']];
                    $record['short_description'] = [$lang => $record['short_description']];

                    $categories = json_decode($record['category'], true) ?? [];

                    $product_category = array();
                    foreach ($categories as $cat) {
                        $tags_array[] = $cat;
                        $category = $imported_categories_all->where($trans_cat, $cat)->first();

                        if ($category != null) {
                            $category = (array)$category;
                            $category_id = $category['id'];
                        } else {
                            continue;
                        }

                        if ($category_id != null) {
                            $product_category[] = [
                                'category_id' => $category_id,
                                'country_id' => $record['country_id']
                            ];
                        }
                    }

                    unset($record['category']);
                    unset($record['id']);
                    unset($record['name']);
                    // unset($record['description']);
                    // unset($record['short_description']);

                    $record['original_images'] = $record['image'];

                    if ($record['duplicate_ean'] == 1) {
                        if ($drm->overwrite == 1) {
                            // unset($record['drm_import_id']);
                            $ean = $record["ean"];
                            unset($record["ean"]);
                            unset($record['duplicate_ean']);

                            $duplicate_product = DrmProduct::where([
                                'user_id' => $drm->user_id,
                                'ean' => $ean,
                            ])->where('drm_import_id', '<>', $drm->id)->first();

                            if ($duplicate_product != null) {
                                $duplicate_product->fill($record);
                                $duplicate_product->save();

                                DB::table('drm_product_categories')->where('product_id', $duplicate_product->id)->delete();
                                foreach ($product_category as $prod_cat) {
                                    if (is_array($prod_cat)) {
                                        $prod_cat['product_id'] = $duplicate_product->id;
                                        $productCategories[] = $prod_cat;
                                    }
                                }

                            }
                        }
                        continue;
                    }
                    $record['update_status'] = $update_status;

                    $p_id = DrmProduct::updateOrCreate([
                        'user_id' => $record['user_id'],
                        'ean' => $record['ean']
                    ], $record)->id;

                    \App\ProductMigration\API::syncProduct($p_id);

                    $new_product_ids[] = $p_id; #this will be dispatched for automagic;

                    DRMProductCategory::where('product_id', $p_id)->delete();

                    if ($trial_checked == 0) {
                        $this->setImportTrial($drm->user_id);
                        $trial_checked = 1;
                    }

                    foreach ($product_category as $prod_cat) {
                        if (is_array($prod_cat)) {
                            $prod_cat['product_id'] = $p_id;
                            $productCategories[] = $prod_cat;
                        }
                    }
                    $count++;
                } else {
                    $this->invalid++;
                }

            }
            /**
             * Dispatch csv imported products for automagic
             * chunk of 100 products
             */
            $automagic = new Automagic($user_id);
            $magic_left = $automagic->magicAvailable($used_magic);
            if( ($magic_left > 0 || $magic_left == 'unlimited') && $enable_automagic ){
                TrackAutomagic::create([
                    'user_id' => $user_id,
                    'import_id' => $import_id,
                    'product_ids' => json_encode($new_product_ids),
                ]);
            }

            $used_magic += count($new_product_ids);
            if($one_one_sync[0]->one_one_sync == 1){
                app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user_id);
            }


            if (count($productCategories)) {
                DB::table('drm_product_categories')->insert($productCategories);
            }

            if(professionalOrHigher($user_id)){
                AutoTransfer::dispatch($new_product_ids,$user_id,$lang);
            }

            $message = ['total' => $total, 'count' => $count, 'invalid' => $this->invalid, 'finalize' => 1, 'percent' => number_format(($count / $total) * 100, 2), 'name' => $value['title']];
            sentProgress($message, 'import');
        }

        $automagics = TrackAutomagic::select('product_ids', 'id')->where('import_id', $import_id)->get();

        foreach ($automagics as $key => $track_automagic) {
            DrmFeedAutomagic::dispatch($user_id, json_decode($track_automagic->product_ids), $track_automagic->id)->onQueue('feed_automagic');
        }

        $suggested_products = null;
        $template = DB::table('drm_product_fields')->select('industry_template')->where('drm_import_id', $drm->id)->first();
        if($template){
            $template = (array) json_decode($template->industry_template);
            $template_name = array_key_first($template);
            $marketplace_cat_id = templateMarketplaceCategory($template_name);

            if(isset($template_name)){
                $imported_products = DrmProduct::select('ean')->where('user_id', CRUDBooster::myId())->pluck('ean');
                $suggested_products = MarketplaceProducts::whereNotIn('ean', $imported_products)
                                            ->where('category_id', $marketplace_cat_id)->limit(15)->get();
            }
        }

        $drm->import_finished = 0;
        $drm->current_step = 'market_place';

        if($suggested_products){
            $drm->import_finished = 1;
        }
        Session::remove('unfinished_' . $drm->user_id);
        DB::table('drm_import_errors')->where('drm_import_id', $import_id)->delete();
        if ($drm->save()) {
            //Clear account activity step
            \App\Services\CheckListProgress\Checklist::cache_key_clear(4, $drm->user_id);
        }

        if ($drm->image_backup) {
            app(ImportService::class)->backupImages($import_id, $drm->user_id);
        }
        $drm->tmp_drm_products()->delete();
        // $message=DB::table('notification_trigger')->where('hook','DRMImport')->where('status',0)->first();
        // if($message){
        // $message_title = $e->getMessage();
        // if (isHookRemainOnSidebar('DRMImport') && isLocal()) {
        // 	User::find(Crudbooster::myId())->notify(new DRMTelegramNotification($message_title, 'DRMImport','#'));
        // }else{
        // User::find(Crudbooster::myId())->notify(new DRMNotification($message_title, 'DRMImport','#'));
        // }
        // }

        return "translate";
    }


    public function postImportFinal()
    {
        $import_id = Request::input('drm_import_id');
        $import_check = $this->importProductCheck();
        $import = DrmImport::find($import_id);
        $data = TmpDrmProduct::where('drm_import_id', $import_id)->get();
        $country = $import->country;
        $trans_cat = "category_name_" . $country->language_shortcode;
        $filter = $import->filter;
        $filter = $filter->filter;

        $total = $data->count();
        $tmp_done_array = [];
        if (\Schema::hasTable($table)) {
            $count = 0;
            foreach ($data->chunk(500) as $chunks) {
                $productCategories = [];
                $insert_array = [];
                foreach ($chunks as $record) {
                    if (CRUDBooster::myPrivilegeId() == '3') {
                        if (($import_check['limit'] == '') and ($count >= $import_check['product_amount'])) break;
                    }
                    $product_category = [];
                    if ($this->drmCheckExcluded($record, $filter)) {
                        $record = $record->toArray();
                        $record['title'] = $record['name'];
                        $category = $record['category'];
                        unset($record['category']);
                        unset($record['vat']);
                        unset($record['id']);
                        unset($record['update_enabled']);
                        unset($record['name']);

                        unset($record['language_id']);
                        $record['tags'] = generateTags([$category, $record['brand'], $record['gender']]);

                        if (!$drm->existing_category) {
                            $category_name = $this->getCategoryByName($trans_cat, $category, $drm->user_id);
                        } else {
                            $category_name = $this->getCategoryById($drm->existing_category, $drm->user_id);
                            if ($category != null) {
                                $category_array = (array)$category;
                                $category_name = $category_array[$trans_cat];
                            }
                        }
                        if ($category_name != null) {
                            $product_category[] = $category_name->id;
                        }
                        $product_category = json_encode($product_category);

                        //Action for Duplicate Products
                        if ($record['duplicate_ean'] == 1) {
                            if ($drm->overwrite == 1) {
                                $duplicate = DB::table('drm_products')->where('user_id', $drm->user_id)->where('ean', $record['ean'])->where('drm_import_id', '<>', $drm->id);
                                $duplicate_product = $duplicate->first();
                                if ($duplicate_product != null) {
                                    unset($record['ean']);
                                    unset($record['duplicate_ean']);
                                    unset($record['user_id']);
                                    unset($record['delivery_company_id']);
                                    unset($record['country_id']);
                                    DB::table($table)->where('product_id', $duplicate_product->id)->update($record);
                                }
                            }
                            $count++;
                            continue;
                        }
                        //END Action for Duplicate Products

                        unset($record['duplicate_ean']);
                        $record['category_ids'] = $product_category;
                        $record['update_status'] = makeUpdateStatusJson();
                        $insert_array[] = $record;
                    }
                    $count++;
                }
                DB::table($table)->insert($insert_array);
                $message = ['total' => $total, 'count' => $count, 'finalize' => 1, 'percent' => number_format(($count / $total) * 100, 2), 'name' => strip_tags($value['title'])];
                // event(new progressEvent($message));
                sentProgress($message, 'import');
            }
            $drm->import_finished = 1;
            DB::table('drm_import_errors')->where('drm_import_id', $import_id)->delete();
            $drm->tmp_drm_products()->delete();
            Request::session()->put('curr_tab', null);
            $drm->current_step = null;
            // $message=DB::table('notification_trigger')->where('hook','DRMImport')->where('status',0)->first();
            // if($message){
            // 	$message_title = generateNotificationMessage($message->title);
            // if (isHookRemainOnSidebar('DRMImport') && isLocal()) {
            // 	User::find(Crudbooster::myId())->notify(new DRMTelegramNotification($message_title, 'DRMImport','#'));
            // }else{
            // User::find(Crudbooster::myId())->notify(new DRMNotification($message_title, 'DRMImport','#'));
            // }
            $drm->save();
            // }
            return true;
        } else {
            return false;
        }
    }

    public function getCategoryByName($trans_cat, $category, $user_id = null)
    {
        if ($user_id == null) {
            $user_id = CRUDBooster::myId();
        }
        $categories = $this->getCategoryCache($user_id);
        return $categories->where($trans_cat, $category)->first();
    }

    public function getCategoryById($id, $user_id = null)
    {
        if ($user_id == null) {
            $user_id = CRUDBooster::myId();
        }
        $categories = $this->getCategoryCache($user_id);
        return $categories->where('id', $id)->first();
    }

    public function getCategoryCache($user_id)
    {
        return Cache::remember('Categories_' . $user_id, 05.0, function () use ($user_id) {
            $db_categories = DB::table('drm_category')->where('user_id', $user_id)->cursor();
            return collect($db_categories->all());
        });
    }

    public function drmCheckExcluded($import, $csv, $csv_fields, $filter)
    {
        if (!$filter) {
            return true;
        }
        if ($filter['price_below'] != null) {
            if (drm_convert_european_to_decimal($csv[$csv_fields->vk_price], $import->money_format) < $filter['price_below']) {
                return false;
            }
        }

        if ($filter['price_more_than'] != null) {
            if (drm_convert_european_to_decimal($csv[$csv_fields->vk_price], $import->money_format) > $filter['price_more_than']) {
                return false;
            }
        }

        if ($filter['ean'] != null) {
            foreach ($filter['ean'] as $value) {
                if ($value == $csv[$csv_fields->ean]) {
                    return false;
                }
            }
        }

        if ($filter['category'] != null) {
            foreach ($filter['category'] as $value) {
                if ($value == $csv[$csv_fields->category]) {
                    return false;
                }
            }
        }

        if ($filter['stock'] != null) {
            foreach ($filter['stock'] as $key => $value) {
                if ($filter['stock_operator'][$key] == '=') {
                    if ($csv[$csv_fields->stock] == (int)$value) {
                        return false;
                    }
                }
                if ($filter['stock_operator'][$key] == '>') {
                    if ($csv[$csv_fields->stock] > (int)$value) {
                        return false;
                    }
                }
                if ($filter['stock_operator'][$key] == '<') {
                    if ($csv[$csv_fields->stock] < (int)$value) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    public function checkValid($record)
    {
        ($record->ean_field == 1) ? $ean_field = "ean" : $ean_field = "item_number";
        if ($record->$ean_field == null || $record->$ean_field == "") {
            return false;
        } else {
            return true;
        }
    }

    public function getImportPayment($need_data = null)
    {

        // isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592]) ||
        if(checkTariffEligibility(CRUDBooster::myParentId())){
            return redirect(route('get_tariff'));
        }

        $data = $this->importProductCheck();
        $user = User::find(CRUDBooster::myParentId());
        $user_products = drmTotalProduct(CRUDBooster::myParentId());
        $url = '';
        $prev_url = session()->get('url');
        $limit_amount = $data['plan_limit'];// > $user_products ? $data['plan_limit'] : $user_products;
        if (!empty($prev_url)) {
            $url .= $prev_url;
        }

        if ($user) {
            $raw_plans = DB::table('import_plans')->orderBy('product_amount', 'asc')->where('status', 1)->where('tariff_active', 0)->where('dt_tariff_active', 0)->get();
            $avil_plans = DB::table('import_plans')
                // ->where('product_amount', '>', $limit_amount) // more than purchased tarif
                ->where('product_amount', '>', $user_products) // more than purchased tarif
                ->where('status', 1)
                ->where('tariff_active', 0)
                ->where('dt_tariff_active', 0)
                ->orderby('product_amount', 'asc')
                ->pluck('id')->toArray();

            //Is old plan acive
            $days = $data['days'];
            $btn_message = "If you downgrade now, then your remaining term will expire. You can still use your current plan for $days days";


            //User term
            $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
            $term = ($privacy) ? $privacy->page_content : '';
            $user_data = '<div id="customer_data_term"></div>';
            if ($user->billing_detail) {
                $billing = $user->billing_detail;
                $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
            }
            if (strpos($term, '{customer}') !== false) {
                $term = str_replace('{customer}', $user_data, $term);
            }

            // import plan payment discount remain days
            $user_import_discount = \App\ImportPlanGetDiscounts::where('user_id', CRUDBooster::myParentId())->where('status', 1)->where('end_date', '>=', Carbon::now()->toDateTimeString())->first();

            $hasPaypal = app(DRMService::class)->paypalCheck(User::DROPMATIX_ACCOUNT_ID);

            return view('app_store.import_payment_v1', compact('raw_plans', 'avil_plans', 'data', 'user', 'url', 'term', 'user_import_discount', 'limit_amount', 'btn_message', 'user_coupon', 'onboarding_coupon_valid', 'show_coupon_msg', 'hasPaypal'));
        }
        return redirect('/admin');
    }

    public function getNewImportPayment($need_data = null)
    {
        $data = $this->importProductCheck();
        $user = User::find(CRUDBooster::myParentId());
        $user_products = drmTotalProduct(CRUDBooster::myParentId());
        $user_total_channel_added = userTotalChannels($user->id);

        $mp_parent_cat_access = DB::connection('marketplace')->table('marketplace_user_accesses')->where('user_id', $user->id)->value('accessable_parent_categories');
        $user_total_mp_access = $mp_parent_cat_access ? count(array_filter( json_decode($mp_parent_cat_access, true) )) : 0;

        if(checkTariffEligibility($user->id)){
            // $credit_detail = app('App\Http\Controllers\tariffController')->getCreditInfo($user->id);
            $credit_detail = (new \App\Services\Tariff\Credit\CreditService())->credit();
        }

        $url = '';
        $prev_url = session()->get('url');
        $limit_amount = $data['plan_limit'];// > $user_products ? $data['plan_limit'] : $user_products;
        if (!empty($prev_url)) {
            $url .= $prev_url;
        }

        if ($user) {
            $raw_plans = DB::table('import_plans')->orderBy('product_amount', 'asc')->where('status', 1);

            if(checkTariffEligibility($user->id)){
                $raw_plans = $raw_plans->where('tariff_active', 1)->where('dt_tariff_active', 0);
            }else{
                $raw_plans = $raw_plans->where('tariff_active', 0)->where('dt_tariff_active', 0);

            }

            $raw_plans = $raw_plans->get();

            $avil_plans = DB::table('import_plans')
                // ->where('product_amount', '>', $limit_amount) // more than purchased tarif
                ->where('status', 1);

            if(checkTariffEligibility($user->id)){
                // if(!in_array($data['plan'], ['Trial'])){
                    $avil_plans = $avil_plans->where(function($q) use($user_products){
                        $q->where('product_amount', '=', $user_products);
                        $q->orWhere('product_amount', '>', $user_products);
                    });

                    $avil_plans = $avil_plans->where('tariff_active', 1)
                                            ->where('dt_tariff_active', 0)
                                            ->where(function($q) use($user_total_mp_access){
                                                $q->where('mp_category', '=', $user_total_mp_access);
                                                $q->orWhere('mp_category', '>', $user_total_mp_access);
                                            }); // more than mp category accessed

                    // if(in_array($data['plan'], ['Free', 'Trial', '500 Free Products'])){
                    //     $avil_plans = $avil_plans->whereRaw('CASE WHEN credit = 0 THEN credit = '.$credit_detail['used_credit'].' OR credit > '.$credit_detail['used_credit'].' ELSE credit > '.$credit_detail['used_credit']. ' END');
                    // }else{
                    //     $avil_plans = $avil_plans->whereRaw('CASE WHEN credit = 0 THEN credit = '.$credit_detail['remain_credit'].' OR credit > '.$credit_detail['remain_credit'].' ELSE credit > '.$credit_detail['remain_credit']. ' END');
                    // }

                    $avil_plans = $avil_plans->where(function($q) use($user_total_channel_added){
                        $q->where('no_of_online_channels', '=', $user_total_channel_added);
                        $q->orWhere('no_of_online_channels', '>', $user_total_channel_added);
                    });

                // }else{
                //     $avil_plans = $avil_plans->where('tariff_active', 1)->where('dt_tariff_active', 0);
                // }
            }else{
                $avil_plans = $avil_plans->where('product_amount', '>', $user_products) // more than purchased tarif
                ->where('tariff_active', 0)->where('dt_tariff_active', 0);

            }

            $avil_plans = $avil_plans->orderby('product_amount', 'asc')
            ->pluck('id')
            ->toArray();

            //Is old plan acive
            $days = $data['days'];
            $btn_message = "If you downgrade now, then your remaining term will expire. You can still use your current plan for $days days";


            //User term
            $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
            $term = ($privacy) ? $privacy->page_content : '';
            $user_data = '<div id="customer_data_term"></div>';
            if ($user->billing_detail) {
                $billing = $user->billing_detail;
                $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
            }
            if (strpos($term, '{customer}') !== false) {
                $term = str_replace('{customer}', $user_data, $term);
            }

            // import plan payment discount remain days
            $user_import_discount = \App\ImportPlanGetDiscounts::where('user_id', CRUDBooster::myParentId())->where('status', 1)->where('end_date', '>=', Carbon::now()->toDateTimeString())->first();

            # check onboarding coupon validity
            $user_coupon = $coupon_id = '';
            $onboarding_coupon_valid = 0;

            $is_greater_id_user = checkTariffEligibility(CRUDBooster::myParentId()) ? 1 : 0;
            // $coupon_type = $is_greater_id_user ? 'onboarding' : 'regular';
            $coupon_type = '';
            $user_voucher_data = app('\App\Services\CouponService')->getUserVoucherData(CRUDBooster::myParentId(), $coupon_type, false);

            if (!empty($user_voucher_data)) {
                $user_coupon = $user_voucher_data->coupon_code;

                if ($user_voucher_data->coupon_cat == config('global.onboarding.coupon_cat')) {
                    if ($is_greater_id_user) {

                        $valid_coupon_info = app('\App\Services\CouponService')->getValidCouponInfo($user_coupon);
                        if (!empty($valid_coupon_info)) {
                            $onboarding_coupon_valid = 1;
                            $coupon_id = $valid_coupon_info->coupon_id;
                        }
                    } else {
                        $user_coupon = null;
                    }
                }
            }

            $plan_state     = app('\App\Services\CouponService')->check_user_import_plan_state(CRUDBooster::myParentId());
            $plan_purchased = $plan_state['plan_purchased'] ? 1 : 0;
            $trial_ends     = $plan_state['trial_ends'] ? 1 : 0;

            $hasPaypal = app(DRMService::class)->paypalCheck(User::DROPMATIX_ACCOUNT_ID);

            $content = view('app_store.import_payment_selection_modal', compact('raw_plans', 'avil_plans', 'data', 'user', 'url', 'term', 'user_import_discount', 'limit_amount', 'btn_message', 'user_coupon', 'onboarding_coupon_valid', 'coupon_id', 'is_greater_id_user', 'plan_purchased', 'trial_ends', 'hasPaypal'))->render();

            return response()->json([
                'success' => true,
                'data'    => $content,
                'plan_purchase' => $plan_purchased,
                'trial_end' => $trial_ends,
            ], 200);
        }

        return redirect('/admin');
    }

    public function importProductCheck($user_id = null)
    {
        if ($user_id == null) {
            $user_id = CRUDBooster::myParentId();
        }

        $is_dt_new_user = checkDtUser($user_id) && checkTariffEligibility($user_id);

        $trial = $this->checkImportTrial($user_id);
        $check_trial = DB::table('app_trials')->where(['user_id' => $user_id, 'app_id' => 0])->count();
        $manual_tarrif = new AdminManualImportTarrifController;

        if(!$is_dt_new_user){
            $assigned_amount = $manual_tarrif->checkManualTarrif($user_id);
        }else{
            $assigned_amount = $manual_tarrif->checkDtManualTarrif($user_id);
        }

        $total_products = drmTotalProduct($user_id);

        if(!$is_dt_new_user){
            $plan = $this->checkImportPlan($user_id);
        }else{
            $plan = $this->checkDtImportPlan($user_id);
        }

        $plan_total = $assigned_amount['amount'] + $plan['amount'];

        //Plan amonunt
        $plan_limit = $plan['amount'] ?? 0;

        if ($plan_total) {
            $check_trial = true;
        }

        if ($check_trial && $plan['import_plan_payment_discount'] != 100) {
            if ($plan['amount'] != 0 && $plan['amount'] >= $total_products) {
                $data['product_amount'] = $plan['amount'] - $total_products;
                $data['blocked'] = '';
                $data['limit'] = '';
                $data['plan'] = "Purchased";
                $data['days'] = $plan['days'];
                $data['plan_id'] = $plan['id'];
                $data['plan_total'] = $plan_total;
                $data['plan_limit'] = $plan_limit;

            } elseif ($assigned_amount['amount'] != 0 && $assigned_amount['amount'] >= $total_products) {
                $data['product_amount'] = $assigned_amount['amount'] - $total_products;
                $data['blocked'] = '';
                $data['limit'] = '';
                $data['plan'] = "Assigned";
                $data['days'] = $assigned_amount['days'];
                $data['plan_total'] = $assigned_amount['amount'];
                $data['plan_limit'] = $plan_limit;
            } elseif ($trial > 0) {
                $data['product_amount'] = 1;
                $data['blocked'] = '';
                $data['limit'] = 'Unlimited';
                $data['plan'] = "Trial";
                $data['days'] = $trial;

                if($is_dt_new_user){
                    $dt_product_amount = DroptiendaPlan::DtPlanProductAmount($plan['id']);

                    $data['product_amount'] = $dt_product_amount - $total_products;
                    $data['limit'] = $dt_product_amount;

                    if(in_array($user_id, DroptiendaPlan::SPECIAL_USER_IDS)){
                        $data['product_amount'] = DroptiendaPlan::SPECIAL_USER_PRODUCT_AMOUNT[$user_id] - $total_products; //Special priveledge user, custom amount of product
                        $data['limit'] = DroptiendaPlan::SPECIAL_USER_PRODUCT_AMOUNT[$user_id];
                    }
                }
            } else {

                // if(isLocal()){
                $data['product_amount'] = 0;
                $data['blocked'] = 'blocked';
                $data['limit'] = '';
                $data['plan_total'] = $plan_total;
                $data['plan_limit'] = $plan_limit;
                // }


                if ($assigned_amount['amount']) {
                    $plan_total = (int)$assigned_amount['amount'];
                    $data['plan'] = "Assigned";
                    $data['days'] = $assigned_amount['days'];
                } elseif ($plan['amount']) {
                    $plan_total = (int)$plan['amount'];
                    $data['plan'] = "Purchased";
                    $data['days'] = $plan['days'];
                    $data['plan_id'] = $plan['id'];
                } else {
                    $plan_total = 0;
                    $data['days'] = 0;
                    $data['total_product'] = $total_products;
                    $data['blocked'] = '';
                    $data['product_amount'] = 500 - $total_products;
                    $data['plan'] = "500 Free Products";
                    $data['plan_total'] = 500;
                    $data['plan_limit'] = $plan_limit;

                    if($is_dt_new_user){
                        $data['plan_limit'] = DroptiendaPlan::DtPlanProductAmount($plan['id']);

                        if(in_array($user_id, DroptiendaPlan::SPECIAL_USER_IDS)){
                            $data['plan_limit'] = DroptiendaPlan::SPECIAL_USER_PRODUCT_AMOUNT[$user_id]; //Special priveledge user, custom amount of product
                        }
                    }
                }
            }
        } else {
            $data['product_amount'] = 1;
            $data['blocked'] = '';
            $data['limit'] = 'Unlimited';
            $data['plan'] = "none";
            $data['days'] = 0;
            $data['plan_total'] = 0;
            $data['plan_limit'] = $plan_limit;
            $data['import_plan_discount'] = $plan['import_plan_payment_discount'];
        }

        if (empty($check_trial) && empty($plan) && $total_products <= 500) {
            $data['product_amount'] = 500 - $total_products;
            $data['blocked'] = '';
            $data['limit'] = 500;
            $data['plan'] = "Free";
            $data['days'] = null;
            $data['plan_total'] = $assigned_amount['amount'] + 500;
            $data['plan_limit'] = $plan_limit;
            $data['free_500'] = true;
        }

        if (!empty($plan['is_unlimited']) && $plan['is_unlimited']) {
            $data['product_amount'] = $plan['amount'];
            $data['blocked'] = '';
            $data['limit'] = 'Unlimited';
            $data['plan'] = "Unlimited";
            $data['is_unlimited'] = true;
            $data['days'] = $plan['days'];
            $data['plan_total'] = $plan['amount'];
            $data['plan_limit'] = $plan_limit;
            $data['import_plan_discount'] = $plan['import_plan_payment_discount'];
        }

        // isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592]) ||
        if(checkTariffEligibility(CRUDBooster::myParentId())){
            if($data['plan'] == "500 Free Products"){
                $data['blocked'] = 'block';
            }
        }

        $data['import_plan_id'] = $plan['import_plan_id'];

        if($is_dt_new_user){
            if(!empty($assigned_amount)){
                $data['import_plan_id'] = $assigned_amount['plan_id'];
            }
        }

        if($data["plan"] === "Purchased" && empty($plan['stripe_subscription_id']))
        {
            $data['plan'] = "Assigned";
        }

        // TODO::MAX DATE
        if(isset($plan['end_date']))
        {
            $data['end_date'] = $plan['end_date'];
        }elseif(isset($assigned_amount['end_date']))
        {
            $data['end_date'] = $assigned_amount['end_date'];
        }

        return $data;
    }

    public function checkImportPlan($user_id)
    {
        $data = [];

        $date_diff = 0;
        $plan = DB::table('purchase_import_plans')
            ->where('cms_user_id', $user_id)
            ->first();

        $import_plan_discount = DB::table('import_plan_get_discounts')
            ->where('user_id', $user_id)
            ->where('end_date', '>=', Carbon::now()->toDateTimeString())
            ->where('status', 1)
            ->first();

        if ($plan) {
            $end_date = $plan->end_date;
            $date_diff = (int)DateTime::getRemainDays(date('Y-m-d'), $end_date);
            if($date_diff === 0) {
                $date_diff = 1;
            }

            $data['end_date'] = date('Y-m-d', strtotime($end_date));
        }
        if ($date_diff > 0) {
            $data['amount'] = (int)$plan->product_amount_import;
            $data['is_unlimited'] = $plan->import_plan_id == 23;
            $data['days'] = $date_diff;

            $data['stripe_subscription_id'] = $plan->stripe_subscription_id;
            $data['import_plan_id'] = $plan->import_plan_id;

            if ($import_plan_discount) {
                $data['import_plan_payment_discount'] = $plan->import_plan_percentage_discount;
            } else {
                $data['import_plan_payment_discount'] = 0;
            }

            $data['id'] = $plan->id;

            return $data;
        } else {
            return false;
        }
    }

    public function checkDtImportPlan($user_id)
    {
        $data = [];

        $date_diff = 0;
        $plan = DB::table('dt_tariff_purchases')
        ->where('user_id', $user_id)
        ->where(function($q){
            $q->whereNotNull('subscription_id');
            $q->where('subscription_id', '<>', '');
        })
        ->first();

        if ($plan) {
            $end_date = $plan->end_date;
            $date_diff = (int) DateTime::getRemainDays(date('Y-m-d'), $end_date);

            if($date_diff === 0) {
                $date_diff = 1;
            }

            $data['end_date'] = date('Y-m-d', strtotime($end_date));
        }

        if ($date_diff > 0) {
            $data['amount'] = DroptiendaPlan::DtPlanProductAmount($plan->plan_id);
            $data['is_unlimited'] = $plan->plan_id == 31;
            $data['days'] = $date_diff;

            $data['stripe_subscription_id'] = $plan->subscription_id;
            $data['import_plan_id'] = $plan->plan_id;

            $data['id'] = $plan->id;

            if(in_array($user_id, DroptiendaPlan::SPECIAL_USER_IDS)){
                $data['amount'] = DroptiendaPlan::SPECIAL_USER_PRODUCT_AMOUNT[$user_id]; //Special priveledge user, custom amount of product
            }

            return $data;
        } else {
            return false;
        }
    }

    public function checkImportTrial($user_id)
    {
        $remain_days = 0;
        $trial = DB::table('app_trials')
            ->select('trial_days', 'start_date')
            ->where(['user_id' => $user_id, 'app_id' => 0])->first();
        if ($trial) {
            $remain_days = DateTime::getTrialRemaining($trial->start_date, $trial->trial_days);
        }
        return $remain_days;
    }

    public function setImportTrial($user_id)
    {
        $check_trial = DB::table('app_trials')->where(['user_id' => $user_id, 'app_id' => 0])->count();
        $check_product = DB::table('drm_products')->where('user_id', $user_id)->first();
        if (!$check_trial && $check_product != null) {
            $date = date("Y-m-d");
            DB::table('app_trials')->insert([
                'user_id' => $user_id,
                'app_id' => 0,
                'trial_days' => 14,
                'start_date' => $date
            ]);
        }
    }

    public function checkIsProfessionalOrEnterprice($user_id)
    {
        $checkUserTariff = DB::table('purchase_import_plans')->where('cms_user_id', $user_id)
            ->whereIn('import_plan_id', [26, 27])
            ->whereDate('end_date', '>=', now())
            ->first();

        return $checkUserTariff;
    }

    //////////////////////////////////////////////////////////////////////////
    ////////////////////////////// Auto sync /////////////////////////////////
    //////////////////////////////////////////////////////////////////////////

    public function csvToArrayModified($path, $type, $delimiter, $cloud = true)
    {
        $rand = Str::random(40);
        if ($cloud == true) {
            $path = Storage::disk('spaces')->url($path);
            file_put_contents($rand . '.' . $type, fopen($path, 'r'));
            $localpath = $rand . '.' . $type;
        } else {
            $localpath = $path;
        }

        if ($type == 'csv' || $type == 'txt') {
            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
            // $reader->setInputEncoding('UTF-8');
            if ($delimiter != 'auto') {
                $reader->setDelimiter($delimiter);
            }
            $spreadsheet = $reader->load($localpath);
        } else {
            $spreadsheet = IOFactory::load($localpath);
        }
        if ($cloud) {
            unlink($localpath);
        }
        return $spreadsheet->getActiveSheet()->toArray();
    }


    public function importImagePrefix($prefix, $image): string
    {
        if (substr($prefix, -1) == '/') {
            $image = $prefix . $image;
        } else {
            $image = $prefix . '/' . $image;
        }
        return $image;
    }


    public function validateFile($collection): bool
    {
        $valid = true;
        $count = 0;
        foreach ($collection as $key => $value) {
            if ($key == 0) {
                if (containsOnlyNull($value)) {
                    $valid = false;
                }
                if (hasBigString($value)) {
                    $valid = false;
                }
                if (count($value) < 2) {
                    $valid = false;
                }
                $valid = checkArrayKey($value, $collection);

                // if(arrayNullCount($value)>4){
                // 	$valid = false;
                // }
            } else {
                // if(containsOnlyNull($value)){
                // 	$count++;
                // }
                $utf_8 = makeArrayUtf8(makeArrayUtf8($value));

                if (json_encode($utf_8) == false) {
                    $valid = false;
                }
            }
        }
        // if(count($collection)==5 && $count>2){
        // 	$valid = false;
        // }
        return $valid;
    }


    public function getManualUpdateImport()
    {
        return redirect()->back();
    }


    public function postManualUpdateImport()
    {
        $items = $_REQUEST['update_items'];
        if ($items == null) {
            return redirect()->back();
        }
        $url_file = "";
        $delimiter = $_REQUEST['delimiter'];
        $import_id = Request::input('drm_import_id');
        $object = DrmImport::find($import_id);

        if($object->latest_file){
            try {
                Storage::disk('spaces')->delete($object->latest_file);
            } catch (\Throwable $th) {
            }
        }

        $type = (Request::input('file_type') == 1) ? 'file' : 'url';

        if ($type == 'file') {
            if (!Request::hasFile('csv_file')) {
                return abort(500);
            }
            foreach (Request::file('csv_file') as $file) {
                $file_type = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
                $rand = Str::random(40);
                $path = $file->storeAs('public/update_csv_files', $rand . '.' . $file_type, ['visibility' => 'public', 'disk' => 'spaces']);
                // $path = str_replace('public','storage',$path);
            }
            $object->auto_update = 0;
            $object->save();
        } else {
            $url_file = trim(Request::input('csv_link'));
            $csv_data = getRemoteFile($url_file);
            $rand = Str::random(40);
            Storage::disk('spaces')->put('public/update_csv_files/' . $rand . '.' . $csv_data['ext'], $csv_data['data'], 'public');
            $path = 'public/update_csv_files/' . $rand . '.' . $csv_data['ext'];
            $file_type = $csv_data['ext'];
            $object->auto_update = 1;
            $object->save();
        }

        $header = $this->csvToCsvHeaderJson($path, $delimiter, true);

        if ($header == false) {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_imports'), "Invalid File", "danger");
        }

        if ($_POST['button'] == 'delete') {
            $csv = $this->csvToArray($path, $file_type, $delimiter, true);
            $collection = LazyCollection::make($csv);
            $fields = DB::table('drm_product_fields')->where('drm_import_id',$object->id)->first();
            $ean = $collection->pluck($fields->ean)->unique()->toArray();
            $drm_product = DB::table('drm_products')->whereIn('ean', $ean)->where('user_id', CRUDBooster::myParentId())->pluck('id')->toArray();

            $chunks = array_chunk($drm_product, 200);
            foreach ($chunks as $chunk) {
                app('App\Services\DRMProductService')->destroy($chunk,CRUDBooster::myParentId());
            }

            Storage::disk('spaces')->delete($path);
            return back()->with('success', 'Product Manually Deleted successfully');
        } else {
            $data['page_title'] = __("Feed List");
            $data['header'] = $header;
            $data['import_id'] = $import_id;
            $data['items'] = $items;
            $data['drm'] = $object;
            $data['step'] = 2;
            $data['product_matching_method'] = $_REQUEST['product_matching_method'];
            $data['path'] = $path;
            $data['delimiter'] = $delimiter;
            $data['url'] = $url_file;

            return view('admin.drm_import.after_import.manual_update_sync_fields', $data);
        }
    }

    public function postManualUpdateSave()
    {
        $items = $_REQUEST;
        unset($items['drm_import_id']);
        unset($items['_token']);
        $import_id = Request::input('drm_import_id');

        $delimiter = $items['delimiter'];
        $path = $items['path'];
        $url = $items['url'];
        $matching_method = $items['product_matching_method'];

        unset($items['product_matching_method']);

        unset($items['delimiter']);
        unset($items['path']);
        unset($items['url']);

        $object = DrmImport::find($import_id);
        $items_json = json_encode($items);

        $countryId = app('App\Services\UserService')->getProductCountry($object->user_id);

        DB::table('manual_update_mapping')
            ->updateOrInsert(
                ['drm_import_id' => $import_id],
                ['product_matching_method' => $matching_method, 'mapping' => $items_json, 'url' => $url, 'delimiter' => $delimiter, 'path' => $path, 'country_id' => $countryId]
            );
        $message = ['text' => 'Connecting to server...', 'percent' => '10'];
        sentProgress($message, 'manualUpdate');


        if (!empty($url)) {
            $url = config('import.base_url') . config('import.manual_url_sync');
        } else {
            $url = config('import.base_url') . config('import.manual_file_sync');
        }

        $data = [
            'import_id' => $object->id,
            'user_id' => $object->user_id
        ];

        $ch = curl_init();
        $headers = array("accept: application/json", "content-type: application/json");

        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_HEADER => 1,
            CURLOPT_CUSTOMREQUEST => "POST",
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_FOLLOWLOCATION => true
        ));

        curl_exec($ch);

        // ImportManualSync::dispatchNow($object,$object->user_id,$url?"auto":"manual");
    }

    public function enableManualAutoUpdate($import_id, $items, $url, $delimiter)
    {
        $items_json = json_encode($items);
        DB::table('manual_update_mapping')
            ->updateOrInsert(
                ['drm_import_id' => $import_id],
                ['mapping' => $items_json, 'url' => $url, 'delimiter' => $delimiter]
            );
    }


    public function createSyncReport($import_id)
    {
        $message = ['text' => 'Saving Report...', 'percent' => '10'];
        sentProgress($message, 'manualUpdate');
        $time_now = Carbon::now();
        $time_string = $time_now->toDateTimeString();
        $syncId = DB::table('drm_import_sync')->insertGetId([
            'drm_import_id' => $import_id,
            'created_at' => $time_string
        ]);
    }


    public function getDebugTool($import_id)
    {
        if (CRUDBooster::isSuperadmin()) {
            $import = DrmImport::find($import_id);
            $file_path = $import->latest_file ?? $import->csv_file_path;
            $type = pathinfo($file_path, PATHINFO_EXTENSION);
            $data['csv_data'] = $this->csvToArrayModified($file_path, $type, $import->delimiter);
            $data['fields'] = DB::table('drm_product_fields')->where('drm_import_id', $import_id)->first();

            $data['drm_products'] = DB::table('drm_products')
                ->select('ek_price', 'vk_price', 'ean', 'stock', 'id', 'item_number')
                ->where('drm_import_id', $import_id)->get();
            return view('admin.drm_import.after_import.debug-tool', $data);
        } else {
            echo "Access denied";
        }
    }

    public function postFilterByDataFetch()
    {
        $drm_import_id = $_REQUEST['drm_import_id'];
        $filter_by = $_REQUEST['filterValue'];

        $distinctColoumn = DB::table('tmp_drm_products')
            ->where('drm_import_id', $drm_import_id)
            ->where($filter_by, '<>', '')
            ->select($filter_by)
            ->distinct($filter_by)
            ->pluck($filter_by)
            ->toArray();

        if ($distinctColoumn) {
            foreach ($distinctColoumn as $key => $value) {
                // $selected = $filter_input == $key ? "selected" : "";

                $html .= "<option value='" . $value . "'" . $selected . " >" . $value . "</option>";
            }
        }

        return $html;

    }

    public function csvToArray($csv, $type, $delimiter, $deleteFile = true)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $paths = explode(';', $csv);
        $key = null;
        $key_count = 0;
        $array = array();
        $rand = Str::random(40);
        foreach ($paths as $path) {
            if ($deleteFile) {
                $path = Storage::disk('spaces')->url($path);
                $file_type = pathinfo($path, PATHINFO_EXTENSION);
                $file = file_get_contents($path);
                file_put_contents($rand . '.' . $file_type, $file);
                $localpath = $rand . '.' . $file_type;
            } else {
                $localpath = $path;
            }
            if ($type == 'csv' || $type == 'txt') {
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');

                if ($delimiter != 'auto') {
                    $reader->setDelimiter($delimiter);
                }
                $spreadsheet = $reader->load($localpath);
            } else {
                $spreadsheet = IOFactory::load($localpath);
            }
            $spreadsheet = $spreadsheet->getActiveSheet()->toArray();
            $collection = LazyCollection::make($spreadsheet);


            if ($key == null) {
                $key = array_map('trim', $collection->first());
                $key_count = count($key);
            }
            $key = array_map('removeDots', $key);
            $collection = $collection->except(0);
            foreach ($collection as $row) {

                if (count($row) == $key_count && !containsOnlyNull($row)) {
                    $array[] = array_combine($key, $row);
                }

            }

            if (!pathIsUrl($path) && $deleteFile) {
                unlink($localpath);
            }
        }
        return $array;
    }

    public function importPaymentDiscount()
    {
        $profit_share = $_REQUEST['profit_share'];
        $get_discount = $_REQUEST['get_discount'];

        $data = [
            'profit_share' => $profit_share,
            'get_discount' => $get_discount,
            'start_date' => Carbon::now()->toDateTimeString(),
            'end_date' => Carbon::now()->addMonth(6)->toDateTimeString(),
            'status' => 0,
            'ip_address' => getIpAddress(),
        ];

        try {
            $insertResponse = ImportPlanGetDiscounts::updateOrCreate(['user_id' => CRUDBooster::myParentId()], $data);

            return response()->json([
                'success' => true,
                'profit_share' => $profit_share,
                'get_discount' => $get_discount,
                'insert_id' => $insertResponse->id,
                'discount_status' => $insertResponse->status,
                'message' => 'Congratulations! You will get discount'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function importPaymentDiscountRemove()
    {
        $id = $_REQUEST['discount_remove_id'];
        try {
            ImportPlanGetDiscounts::where('id', $id)->delete();

            return response()->json([
                'success' => true,
                'message' => 'Discount Removed'
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function postApplyFilter()
    {
        $import_id = Request::get('drm_import_id');
        $user_id = CRUDBooster::myParentId();
        $import = DrmImport::where([
            'user_id' => $user_id,
            'id' => $import_id
        ])->first();

        if ($import) {
            try {
                $data = [
                    'import_id' => $import_id,
                    'user_id' => $user_id
                ];
                $this->curl(config('import.base_url') . config('import.apply_filter'), $data);

                if ($import->allow_new && $import->type == "url") {
                    $this->curl(config('import.base_url') . config('import.auto_url_sync'), $data);
                }
            } catch (\Throwable $th) {
            }
        }

    }

    public function curl($url, $data, $method = "POST")
    {
        $ch = curl_init();
        $headers = array("accept: application/json", "content-type: application/json");
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_HEADER => 1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_FOLLOWLOCATION => true
        ));
        return curl_exec($ch);
    }

    public function postSelectedTemplateFields(){
        $selected_template_name = $_REQUEST['selected_template_name'];

        if(in_array($selected_template_name, array_keys(config('industry_template')))){

            $selected_template_field_name = config("industry_template.".$selected_template_name);
            $selected_template_field_name = array_keys($selected_template_field_name);

            $popover_content = '<div id="template_field_list"> <ul class="list-group">';
            foreach($selected_template_field_name as $value){
                $trans_field_name = __('industry_template.'.$value);
                $popover_content .= '<li class="list-group-item">' . $trans_field_name . '</li>';
            }
            $popover_content .= '</ul> </div>';

        }else{
            // Here ***$selected_template_name*** refers to Custom Industry Template ID
            $selected_template_fields = CustomIndustryTemplate::where('id', $selected_template_name)->value('fields');

            $popover_content = '<div id="template_field_list"> <ul class="list-group">';
            foreach($selected_template_fields as $value){
                $trans_field_name = $value['name'];
                $popover_content .= '<li class="list-group-item">' . $trans_field_name . '</li>';
            }
            $popover_content .= '</ul> </div>';
        }

        return response()->json(['success' => true, 'popover_content' => $popover_content], 200);
    }

    public function postFetchImportedFile(){

        $imported_id = $_REQUEST['import_id'];

        $imported_csv = FileSource::where('drm_import_id', $imported_id)->select('source_config', 'file_name')->get();
        $imported_csv_file_detail = [];
        $html_table = '
            <table class="table">
            <thead>
                <tr>
                <th>SL</th>
                <th>File Name</th>
                <th>Action</th>
                </tr>
            </thead>

            <tbody>
        ';

        if($imported_csv){
            foreach($imported_csv as $csv_key => $csv_detail){
                $html_table .= '
                <tr>
                <td>'. ($csv_key + 1).'</td>
                <td>'.$csv_detail->file_name.'</td>
                <td><a class="btn btn-xs btn-primary" href="'.Storage::disk('dropmatix')->url($csv_detail->source_config['cloud_path']) . "?download=1".'" target="_blank" title="Download File"><i class="fa fa-download"></i> Download</a></td>
                </tr>';
            }
        }

        $html_table .= '
        </tbody>
        </table>
        ';

        return response()->json(["success" => true, "data" => $html_table]);

    }

    public function getDropmatixBrand()
    {
        $user_id = CRUDBooster::myParentId();
        $html = '<option value="">'.__('Please select Brand').'</option>';

        $dropmatix_brands = DropmatixProductBrand::where('user_id', $user_id)->pluck('brand_name', 'id')->toArray();

        if($dropmatix_brands){
            foreach($dropmatix_brands as $key => $brand_name){
                $html .= '<option value="'.$key.'">'.$brand_name.'</option>';
            }
        }

        return $html;
    }

    public function postSampleFileDownload()
    {
        $selected_industry_template_name = $_REQUEST['selected_template_name'];

        // $head_index = [
        //     'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
        // ];


        // $sheet = $spreadsheet->getActiveSheet();

        $default_fields = [
            'product name',
            'product item number',
            'product ean',
            'product description',
            'product category',
            'product image',
            'product ek price',
            'product tax',
            'product stock',
            'product handling time',
        ];

        $extra_industry_temp_fields = [];

        if( in_array(strtolower($selected_industry_template_name), array_keys(config('industry_template'))) ){
            $extra_industry_temp_fields = 'industry_template.'.strtolower($selected_industry_template_name);
            $extra_industry_temp_fields = array_keys( config($extra_industry_temp_fields) );

        }else{
            // Here ***$selected_industry_template_name*** refers to Custom Industry Template ID
            $all_fields = CustomIndustryTemplate::where('id', $selected_industry_template_name)->value('fields');

            $extra_industry_temp_fields = [];
            array_walk($all_fields, function ($fields) use (&$extra_industry_temp_fields){
                if(!array_key_exists("value", $fields)){
                    $extra_industry_temp_fields[] = $fields['name'];
                }
            });
        }

        $header = array_merge($default_fields, $extra_industry_temp_fields);

        // for($i = 0; $i < count($header); $i++){
        //     $sheet->setCellValue($head_index[$i].'1', $header[$i]);
        // };

        $fields_value = [
            'Adidas',
            'ASDF88J',
            '6654889011098',
            'World best shoe brand',
            'shoe',
            'https://postimg.cc/CzXD77jf',
            '457.35',
            '2.8',
            '49',
            '3',
        ];

        $extra_industry_temp_fields_value = [];

        for($i = 0; $i < count($extra_industry_temp_fields); $i++){
            $extra_industry_temp_fields_value[] = 'value ' . ($i + 1);
        };

        $body = array_merge($fields_value, $extra_industry_temp_fields_value);

        // for($i = 0; $i < count($body); $i++){
        //     $sheet->setCellValue($head_index[$i].'2', $body[$i]);
        // };

        $main_sheet = [
            $header,
            $body
        ];

        try{
            $spreadsheet = new Spreadsheet();
            $spreadsheet->getActiveSheet()->fromArray(
                $main_sheet  // The data to set
            );

            $writer = new Xlsx($spreadsheet);
            $filename = "sample_file.xlsx";

            // header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            // header('Content-Disposition: attachment; filename="'.$filename.'"');

            ob_start();
            $writer->save("php://output");
            $writter_output = ob_get_contents();
            ob_end_clean();

            return response()->json([
                'success' => true,
                'data' => "data:application/vnd.ms-excel;base64,".base64_encode($writter_output),
                'file_name' => $filename
            ], 200);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }


    }

}
