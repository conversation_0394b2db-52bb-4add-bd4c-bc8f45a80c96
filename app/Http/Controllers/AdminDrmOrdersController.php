<?php
namespace App\Http\Controllers;

    use Session;
    use Illuminate\Support\Carbon;
    use Request;
    use DB;
    use CRUDBooster;
    use App\Helper\LengowApi;
    use CB;
    use DateTime;
    use DateInterval;
    use Illuminate\Support\Facades\Validator;
    use ZipArchive;
    use Storage;

    use PhpOffice\PhpSpreadsheet\IOFactory;

	class AdminDrmOrdersController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "paymentType_title";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = false;
			// $this->button_add_name = 'Neue Rechnung';
			$this->button_edit = true;
			$this->button_delete = false;
			$this->button_detail = true;
			$this->button_show = false;
			$this->button_filter = false;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "drm_orders_new";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
            $this->col[] = ["label"=>"ID", "name"=>"id"];
            $this->col[] = ["label"=>"Order ID(API)", "name"=>"order_id_api"];

            if(CRUDBooster::isSuperadmin() )
			$this->col[] = ["label"=>"User","name"=>"cms_user_id","join"=>"cms_users,name"];

	    	$this->col[] = ["label"=>"Shop Name","name"=>"shop_id","join"=>"shops,shop_name" ];
            $this->col[] = ["label"=>"Customer Name", "name"=>"drm_customer_id","join"=>"drm_customers,full_name"];
			$this->col[] = ["label"=>"Invoice","name"=>"invoice_number"];
			// $this->col[] = ["label"=>"Customer Email", "name"=>"customer_id","join"=>"drm_customers,drm_customers.email" ];
            $this->col[] = ["label"=>"Order Date", "name"=>"order_date"];
			$this->col[] = ["label"=>"Delivery status","name"=>"status"];
			$this->col[] = ["label"=>"Amount","name"=>"total"];
			$this->col[] = ["label"=>"Mail Sent","name"=>"mail_sent"];
			$this->col[] = ["label"=>"Supplier","name"=>"supplier"];
			$this->col[] = ["label"=>"Insert Type","name"=>"insert_type"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			//$this->form[] = ['label'=>'Order Id','name'=>'orderId','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Status Id','name'=>'statusId','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Status Name','name'=>'statusName','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Total Sum','name'=>'totalSum','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Purchase Date','name'=>'purchaseDate','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Comment','name'=>'comment','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Customer Id','name'=>'customerId','type'=>'number','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Customer Name','name'=>'customerName','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Customer Email','name'=>'customerEmail','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Customer Status Id','name'=>'customerStatusId','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Customer Status Name','name'=>'customerStatusName','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Delivery FirstName','name'=>'d_firstName','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Delivery LastName','name'=>'d_lastName','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Delivery Company','name'=>'d_company','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Delivery Street','name'=>'d_street','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Delivery HouseNumber','name'=>'d_houseNumber','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Delivery AdditionalAddressInfo','name'=>'d_additionalAddressInfo','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Delivery Postcode','name'=>'d_postcode','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Delivery City','name'=>'d_city','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Delivery State','name'=>'d_state','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Delivery Country','name'=>'d_country','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Delivery CountryIsoCode','name'=>'d_countryIsoCode','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Billing FirstName','name'=>'b_firstName','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Billing LastName','name'=>'b_lastName','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Billing Company','name'=>'b_company','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Billing Street','name'=>'b_street','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Billing HouseNumber','name'=>'b_houseNumber','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Billing AdditionalAddressInfo','name'=>'b_additionalAddressInfo','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Billing Postcode','name'=>'b_postcode','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Billing City','name'=>'b_city','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Billing State','name'=>'b_state','type'=>'text','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Billing Country','name'=>'b_country','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Billing CountryIsoCode','name'=>'b_countryIsoCode','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'PaymentType Title','name'=>'paymentType_title','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'PaymentType Module','name'=>'paymentType_module','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'ShippingType Title','name'=>'shippingType_title','type'=>'text','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'ShippingType Module','name'=>'shippingType_module','type'=>'text','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ['label'=>'OrderId','name'=>'orderId','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'StatusId','name'=>'statusId','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'StatusName','name'=>'statusName','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'TotalSum','name'=>'totalSum','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'PurchaseDate','name'=>'purchaseDate','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Comment','name'=>'comment','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'CustomerId','name'=>'customerId','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'CustomerName','name'=>'customerName','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'CustomerEmail','name'=>'customerEmail','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'CustomerStatusId','name'=>'customerStatusId','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'CustomerStatusName','name'=>'customerStatusName','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D FirstName','name'=>'d_firstName','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D LastName','name'=>'d_lastName','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D Company','name'=>'d_company','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D Street','name'=>'d_street','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D HouseNumber','name'=>'d_houseNumber','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D AdditionalAddressInfo','name'=>'d_additionalAddressInfo','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D Postcode','name'=>'d_postcode','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D City','name'=>'d_city','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D State','name'=>'d_state','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D Country','name'=>'d_country','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'D CountryIsoCode','name'=>'d_countryIsoCode','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B FirstName','name'=>'b_firstName','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B LastName','name'=>'b_lastName','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B Company','name'=>'b_company','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B Street','name'=>'b_street','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B HouseNumber','name'=>'b_houseNumber','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B AdditionalAddressInfo','name'=>'b_additionalAddressInfo','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B Postcode','name'=>'b_postcode','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B City','name'=>'b_city','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B State','name'=>'b_state','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B Country','name'=>'b_country','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'B CountryIsoCode','name'=>'b_countryIsoCode','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'PaymentType Title','name'=>'paymentType_title','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'PaymentType Module','name'=>'paymentType_module','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'ShippingType Title','name'=>'shippingType_title','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'ShippingType Module','name'=>'shippingType_module','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
            $this->addaction = array();
            if(CRUDBooster::isSuperadmin()){
            	$this->addaction[] = ['label'=>'API Response','url'=>url('api/response/invoice/[id]'),'color'=>'info',"icon"=>"fa fa-list", 'showIf'=>"[insert_type] == API && isShopExist([shop_id])"];
        	}
            // $this->addaction[] = ['label'=>'Shop not exist','url'=>'javascript:void(0)','icon'=>'fa fa-exclamation-triangle','color'=>'warning' , 'showIf'=>"[insert_type] == API && !isShopExist([shop_id])"];
            $this->addaction[] = ['label'=>'','url'=>CRUDBooster::adminPath('drm_customers/detail/[drm_customer_id]'),'icon'=>'fa fa-user','color'=>'', 'showIf'=>"[drm_customer_id] != null"];
            // $this->addaction[] = ['label'=>'Products','url'=>CRUDBooster::mainpath('product-list/[id]/[drm_customer_id]'),'color'=>'warning', 'class' => ['order-popup'], 'showIf'=>"[drm_customer_id] != null"];
            $this->addaction[] = ['label'=>'Products','url'=>'javascript:show_order_products_popup([id], [drm_customer_id])','color'=>'warning', 'showIf'=>"[drm_customer_id] != null"];

            $this->addaction[] = ['label'=>'Send Email','url'=>CRUDBooster::mainpath('send-email/[id]'),'color'=>'info', 'showIf'=>"[mail_sent] == null"];
            $this->addaction[] = ['label'=>'Resend','url'=>CRUDBooster::mainpath('send-email/[id]'),'color'=>'success', 'showIf'=>"[mail_sent] != null"];

            // $this->addaction[] = ['label'=>'','url'=>CRUDBooster::mainpath("set-trash/[id]"),'color'=>'success btn-danger',"icon"=>"fa fa-trash" ];
	        $this->addaction[] = ['label'=>'Delivery Note','url'=>CRUDBooster::mainpath('delivery-notes/[id]'),'color'=>'info',"icon"=>"fa fa-list" ];




	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();
            $this->button_selected[] = ['label'=>'','icon'=>'fa fa-arrow-down','name'=>''];


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
            $this->index_button = array();
            $this->index_button[] = ['label'=>'Neue Rechnung','url'=>CRUDBooster::mainpath('add'),'icon'=>'fa fa-plus-circle','color'=>'warning'];
            $this->index_button[] = ['label'=>'Rechnung Setting','url'=>CRUDBooster::mainpath('invoice-setting'),'icon'=>'fa fa-cogs','color'=>'warning'];
            $this->index_button[] = ['label'=>'Email Setting','url'=>CRUDBooster::mainpath('email-setting'),'icon'=>'fa fa-cogs','color'=>'warning'];
            $this->index_button[] = ['label'=>'Import' ,'color'=>'btn btn-success import_btn',"icon"=>"fa fa-amazon"];
            // $this->index_button[] = ['label'=>'Trash','url'=>CRUDBooster::mainpath("trash"),'color'=>'btn btn-danger',"icon"=>"fa fa-filter"];
            $this->index_button[] = ['label'=>'Filter','url'=>"javascript:void(0)",'color'=>'info',"icon"=>"fa fa-filter"];
            // if(CRUDBooster::myid() == '98'){
            if(CRUDBooster::isSuperadmin()){
	            $this->index_button[] = ['label'=>'Paywall Invoice','url'=>CRUDBooster::mainpath('charged-amount-list'),'color'=>'info',"icon"=>"fa fa-list" ];
	        }


            //  $this->index_button[] = ['label'=>'Advanced Print','url'=>CRUDBooster::mainpath("print"),"icon"=>"fa fa-print"];

            // $user_id=crudBooster::myId();
            // $shops=\App\Shop::where('user_id',$user_id)->get();

            // foreach ($shops as $shop) {
            //     $this->index_button[] = ['label'=>$shop->shop_name,'url'=>CRUDBooster::mainpath("?shop=".$shop->id),"icon"=>"fa fa-filter"];
            // }



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
            */
            if(CRUDBooster::isSuperadmin()){
                $orders = DB::table('drm_orders_new')->where('drm_orders_new.status', '!=', 'Canceled');
                $total = $orders->count();
                $total_sum = DB::table('drm_orders_new')->where('trash', '=', 'No')->sum("total");
                $total_sum = number_format($total_sum, 2, ',', '.');
                $shiiped = $orders->where('status','Shipped')->where('trash', '=', 'No')->count();

                $proforma_invoice = DB::table('drm_orders_new')->where('invoice_number',-1)->count();
                $pro_sum = DB::table('drm_orders_new')->where('invoice_number',-1)->sum("total");

                $other = $total - $shiiped - $proforma_invoice;
            }else{
                $orders = DB::table('drm_orders_new')->where('cms_user_id',CRUDBooster::myId())->where('drm_orders_new.status', '!=', 'Canceled');
                $total = $orders->count();
                $total_sum = DB::table('drm_orders_new')->where('cms_user_id',CRUDBooster::myId())->whereNotIn('status',['Storniert','Canceled'])->sum("total");
                $total_sum = number_format($total_sum, 2, ',', '.');
                $shiiped = $orders->where('status','Shipped')->count() + $orders->where('status','Versendet')->count();

                $proforma_invoice = DB::table('drm_orders_new')->where('cms_user_id',CRUDBooster::myId())->where('invoice_number',-1)->count();
                $pro_sum = DB::table('drm_orders_new')->where('cms_user_id',CRUDBooster::myId())->where('invoice_number',-1)->sum("total");

                $other = $total - $shiiped - $proforma_invoice;
            }

            $pro_sum = number_format($pro_sum, 2, ',', '.');

	       	$this->index_statistic = array();
            $this->index_statistic[] = ['label'=>'Total Orders','count'=>$total,'icon'=>'fa fa-area-chart','color'=>'success btn-success total-order','width'=>'col-md-2'];
            $this->index_statistic[] = ['label'=>'Shipped','count'=>$shiiped,'icon'=>'fa fa-google-wallet','color'=>'success btn-waning shipped-order','width'=>'col-md-2'];
            $this->index_statistic[] = ['label'=>'Other','count'=>$other,'icon'=>'fa fa-google-wallet','color'=>'success btn-danger other-order','width'=>'col-md-2'];
            $this->index_statistic[] = ['label'=>'Total Sum','count'=>$total_sum . ' €' ,'icon'=>'fa fa-eur','color'=>'success btn-primary','width'=>'col-md-3'];
            $this->index_statistic[] = ['label'=>'Proforma Invoice','count'=>$proforma_invoice. " | " .$pro_sum.' €'  ,'icon'=>'fa fa-google-wallet','color'=>'success btn-warning proforma-invoice','width'=>'col-md-3'];

            // $this->index_statistic[] = ['label'=>'Total Charged','count'=>$_total_charge.' â‚¬'  ,'icon'=>'fa fa-eur','color'=>'success btn-warning proforma-invoice','width'=>'col-md-2'];
	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = '$(".total-order").click(function () {
                window.location.href = window.location.origin+ "/admin/drm_orders" ;
              });

              $(".shipped-order").click(function () {
                window.location.href = window.location.origin+ "/admin/drm_orders?filter=shipped" ;
              });

              $(".other-order").click(function () {
                window.location.href = window.location.origin+ "/admin/drm_orders?filter=other" ;
              });

              $(".proforma-invoice").click(function () {
                window.location.href = window.location.origin+ "/admin/drm_orders?filter=proforma-invoice" ;
              });

              $("#filter").click(function(e){
                e.preventDefault();

                $("#filter_modal").modal("show");
              });

              ';


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = '

            <!-- Modal -->
            <div id="import_modal" class="modal fade" role="dialog">
              <div class="modal-dialog">

                <!-- Modal content-->
                <form action="'.CRUDBooster::mainpath("import-order").'" method="POST" enctype="multipart/form-data" >
                <input type="hidden" name="_token" value="'.csrf_token().'">
                <div class="modal-content">
                  <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Amazon Import</h4>
                  </div>
                  <div class="modal-body">
                    <p>Amazon order reports upload in txt format.</p>
                    <input type="file" name="order_file" id="order_import" accept=".txt">
                  </div>
                  <div class="modal-footer">
                    <button type="submit" class="btn btn-success">Submit</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                  </div>
                </div>
                </form>

              </div>
            </div>

            <div id="filter_modal" class="modal fade" role="dialog">
              <div class="modal-dialog">

                <!-- Modal content-->
                <form action="'.CRUDBooster::mainpath("").'" method="get"  >

                <div class="modal-content">
                  <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    <h4 class="modal-title">Advance Filter</h4>
                  </div>
                  <div class="modal-body">

                    <div class="row">
                        <div class="col-md-12">
                            <h4>Search by Date range</h4>
                            <div class="form-group col-md-6 pl-0">
                                <strong>Start date</strong>
                                <input class="datepicker form-control" type="text" name="date_from" value="'.$_REQUEST['date_from'].'">
                            </div>
                            <div class="form-group col-md-6 pl-0">
                                <strong>End date</strong>
                                <input class="datepicker form-control" type="text" name="date_to" value="'.$_REQUEST['date_to'].'">
                            </div>
                        </div>
                    </div>
                  </div>

                  <input type="hidden" name="limit" value="'.$_REQUEST['limit'].'">

                  <div class="modal-footer">
                    <button type="submit" class="btn btn-orange">Submit</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                  </div>
                </div>
                </form>

              </div>
            </div>

            <!-- Modal -->
				<div class="modal fade view_modal" id="orderPopupModal" tabindex="-1" role="dialog">
				</div>
            ';




	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();
            $this->load_js[] = asset('js/order.js?v=10');



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = "
	        .small-box {
                cursor: pointer !important;
            }

            .export-btn, .export_delivery_btn{
                    border: none !important;
                    background-color: snow !important;
                    padding-left: 16px;
                    padding-top: 5px;
                }
                .pl-0{
                    padding-left: 0px;
                }
	        ";



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();

            /* ---- cb int end */
            $this->futureInoiceNumber();
        }

        /*
        | ----------------------------------------------------------------------
        | Hook for button selected
        | ----------------------------------------------------------------------
        | @id_selected = the id selected
        | @button_name = the name of button
        |
        */
        public function actionButtonSelected($id_selected,$button_name) {
            //Your code here

        }


        /*
        | ----------------------------------------------------------------------
        | Hook for manipulate query of index result
        | ----------------------------------------------------------------------
        | @query = current sql query
        |
        */
        public function hook_query_index(&$query) {
            //Your code here


            if(!CRUDBooster::isSuperadmin()){
                // if(CRUDBooster::myId() != 98){
                //     //$query->where('cms_user_id', CRUDBooster::myId());
                //     $query->where('cms_user_id', CRUDBooster::myId())->where('drm_orders_new.insert_type','!=','Stripe');
                // }
                $query->where('cms_user_id', CRUDBooster::myId());
            }

            $query->where('drm_orders_new.status', '!=', 'Canceled');


            if(isset($_REQUEST['filter']))
            {
                if($_REQUEST['filter']==="shipped")
                {
                    $query->where('drm_orders_new.status','Shipped');
                }
                else if($_REQUEST['filter']==="other")
                {
                    $query->where('drm_orders_new.status','!=','Shipped');
                }
                else if($_REQUEST['filter']==="proforma-invoice")
                {
                    $query->where('drm_orders_new.invoice_number','-1');
                }
            }

            if($_REQUEST['date_from'])
                $query->whereRaw('cast(order_date as date) >= \'' .$_REQUEST['date_from']. '\'');
            if($_REQUEST['date_to'])
                $query->whereRaw('cast(order_date as date) <= \'' .$_REQUEST['date_to']. '\'');

            // $query->where('trash', 'No');

        }

        /*
        | ----------------------------------------------------------------------
        | Hook for manipulate row of index table html
        | ----------------------------------------------------------------------
        |
        */

        public $order = [];
        public function hook_row_index($column_index,&$column_value) {

            $shop_col = 3;
            $customer_col = 4;
            $invoice_col = 5;
            $order_date_col = 6;
            $supplier_col = 10;
            $amount_col = 8;
            if(CRUDBooster::isSuperadmin())
            {
                $shop_col += 1;
                $customer_col += 1;
                $invoice_col +=1;
                $order_date_col += 1;
                $supplier_col += 1;
                $amount_col += 1;
            }


            if($column_index==1)
            {
                $this->order['id'] =$column_value;

                // dd($order['id']);
            }

            if($column_index==$shop_col)
            {
                if($column_value == null)
                    $column_value = "DRM Manual";
                $this->order['shop_name'] =$column_value;

            }

            if($column_index == $customer_col){
                // dd($this->order['shop_name']);
                if($column_value == null && $this->order['shop_name'] == "App Store" || $this->order['shop_name'] == "Monthly Paywall")
                    {
                        $cms_client_id = DB::table('drm_orders_new')->find($this->order['id'])->cms_client;
                        $column_value = DB::table('cms_users')->find($cms_client_id)->name;
                    }
            }

            if($column_index==$invoice_col)
            {
                // dd($this->order['id']);
                if($column_value == -1)
                    $column_value = "<span class ='label label-warning' style='font-size: 90%'> -1 (". DB::table('drm_orders_new')->find($this->order['id'])->invoice_date.')</span> ';
            }

            if($column_index == $order_date_col)
            {
                 $column_value = date('Y-m-d', strtotime($column_value));
            }

            if($column_index == $supplier_col)
            {
                // dd($column_value);
                $column_value = '<input data-id="'.$this->order['id'].'" class="supplier_toggle" type="checkbox" '.$column_value.' data-toggle="toggle" data-onstyle="success" data-size="mini" style="display:none">';
                //  $column_value = date('Y-m-d', strtotime($column_value));
            }
            if($column_index==$amount_col)
            {
              $column_value = number_format((float)$column_value,2,".","");
            }

        }

        /*
        | ----------------------------------------------------------------------
        | Hook for manipulate data input before add data is execute
        | ----------------------------------------------------------------------
        | @arr
        |
        */
        public function hook_before_add(&$postdata) {
            //Your code here

        }

        /*
        | ----------------------------------------------------------------------
        | Hook for execute command after add public static function called
        | ----------------------------------------------------------------------
        | @id = last insert id
        |
        */
        public function hook_after_add($id) {
            //Your code here

        }

        /*
        | ----------------------------------------------------------------------
        | Hook for manipulate data input before update data is execute
        | ----------------------------------------------------------------------
        | @postdata = input post data
        | @id       = current id
        |
        */
        public function hook_before_edit(&$postdata,$id) {
            //Your code here

        }

        /*
        | ----------------------------------------------------------------------
        | Hook for execute command after edit public static function called
        | ----------------------------------------------------------------------
        | @id       = current id
        |
        */
        public function hook_after_edit($id) {
            //Your code here

        }

        /*
        | ----------------------------------------------------------------------
        | Hook for execute command before delete public static function called
        | ----------------------------------------------------------------------
        | @id       = current id
        |
        */
        public function hook_before_delete($id) {
            //Your code here

        }

        /*
        | ----------------------------------------------------------------------
        | Hook for execute command after delete public static function called
        | ----------------------------------------------------------------------
        | @id       = current id
        |
        */
        public function hook_after_delete($id) {
            //Your code here

        }

        public function getDetail($id = null) {
            //Create an Auth

            if(!CRUDBooster::isRead() && $this->global_privilege==FALSE || $this->button_edit==FALSE || !$id) {
                CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
            }

            $data = [];
            $data['page_title'] = 'Invoice Details';
            $data['order'] = DB::table('drm_orders_new')->where('id',$id)->first();

            if( ! $data['order']->id)
            {
               return CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
            }

            return redirect($this->generate_invoice_pdf($id));

        }

        public function getOrderProducts($order_id)
        {
            $data['products'] = DB::table('drm_order_products')->where('drm_order_id',$order_id)->get();;
            return response()->json($data);
        }

        public function postToggleSupplier()
        {
            // return $_REQUEST['id'];
            DB::table('drm_orders_new')->where('id',$_REQUEST['id'])->update(['supplier' =>$_REQUEST['status']]);
        }


        public function getAdd()
        {


            // $date = new DateTime('2000-01-01');
            // $date->add(new DateInterval('PT'.date("H\Hi\Ms\S")));
            // echo $date->format('Y-m-d H:i:s') . "\n";
            // dd("");

            //Create an Auth
            if(!CRUDBooster::isCreate() && $this->global_privilege==FALSE || $this->button_add==FALSE) {
                CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
            }

            $data = [];
            $data['page_title'] = 'Add Order';

            // $data['columns'] = DB::getSchemaBuilder()->getColumnListing('drm_products');
            // $columns = DB::getSchemaBuilder()->getColumnListing('drm_products');

            // dd($columns) ;

            $data['columns'] = [
                "ean",
                "name",
                "id",

                // "drm_import_id",
                // "delivery_company_id",
                // "country_id",
                // "language_id",
                // "item_number",
                // "description",
                // "image",
                // "ek_price",
                // "vk_price",
                // "vat",
                // "stock",
                // "category",
                // "item_weight",
                // "update_enabled",
            ];

            if(CRUDBooster::isSuperadmin())
            {
                $customer_list = DB::table('drm_customers')->get();
                $products_list = DB::table('drm_products')->select('id','drm_products.name','description')->whereNotNull('vk_price')->limit(150)->get();
            }
            else{
                $customer_list = DB::table('drm_customers')->where('user_id',CRUDBooster::myId())->get();

                $products_list = DB::table('drm_products')
                ->join('drm_imports', 'drm_products.drm_import_id', '=', 'drm_imports.id')
                ->select('drm_products.id','drm_products.name','drm_products.description')
                ->where('drm_imports.user_id', CRUDBooster::myId())->whereNotNull('vk_price')->limit(150)->get();

            }

            // dd($products_list);

            /* --------------- invoice number --------------------- */

            $inv1 = DB::table('drm_orders_new')->where('cms_user_id',CRUDBooster::myId())->where('invoice_number','!=',-1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;

            $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->first()->start_invoice_number ;

            // dd($inv1,$inv2);
            $invoice_number = ($inv1 > $inv2 )? $inv1 : $inv2;
            // dd($inv1 , $inv2);

            // $data['languages'] = DB::table('countries')->get();
            $data['countries'] = DB::table('countries')->where('is_active',1)->get();
            $data['users'] = DB::table('cms_users')->get();

            $data['customer_list'] = $customer_list;
            $data['products_list'] = $products_list;
            $data['invoice_number'] = $invoice_number;

            return view('admin.drm_order.add',$data);
        }

        public function postAddSave()
        {
            // dd($_REQUEST);

            $validator = Validator::make($_REQUEST, [
                'drm_customer_id' => 'required',
                // 'invoice_number' => 'required',
                // 'invoice_date' => 'required|date',
                // 'due_date' => 'required|date',
                // 'payment_methods' => 'required|string',
                // 'currency' => 'required|string',
                // 'shipping' => 'required|string',
                // 'billing' => 'required|string',
                'customer_info' => 'required|string',
                // 'product_id' => 'required|array|min:1',

                // 'description' => 'required|array|min:1',
                // 'description.*' => 'required|string',

                'product_name' => 'required|array|min:1',
                'qty' => 'required|array|min:1',
                'qty.*' => 'required|numeric',

                'rate' => 'required|array|min:1',
                'rate.*' => 'required|numeric',

                'tax' => 'required|array|min:1',
                'tax.*' => 'required|numeric',

                // 'discount' => 'required|numeric',
                // 'discount_type' => 'required|string',
                // 'adjustment' => 'required|numeric',
            ]);

            if ($validator->fails()) {
                return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
                // return 0;
            }

            $customer = DB::table('drm_customers')->find($_REQUEST['drm_customer_id']);

            $shipping = DB::table('drm_customer_address')->where([
                'drm_customer_id' =>$customer->id,
                'type' => 'shipping'
            ])->first();

            $billing = DB::table('drm_customer_address')->where([
                'drm_customer_id' =>$customer->id,
                'type' => 'billing'
            ])->first();

            // dd($customer);

            $_REQUEST['insert_type'] = "Manual";

            // date seconds fixing
            $date1 = new DateTime($_REQUEST['order_date']);
            $date1->add(new DateInterval('PT'.date("H\Hi\Ms\S")));
            $_REQUEST['order_date'] = $date1->format('Y-m-d H:i:s');

            $order = $this->add_order($_REQUEST);
            // dd($order);


            if($order == null || $order == [] || $order->id == null)
            {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Please enter input correctly'), 'error');
            }


            // return redirect('admin/drm_invoice')->with('success','Invoice added');
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_orders'), trans('Order added'), 'success');
        }

        public function add_order($order_info)
        {
            // dd($order_info);


            /* ----------------- calculation ---------------------- */

            $product = $order_info['product_id'];
            $product_name = $order_info['product_name'];
            $description = $order_info['description'];
            $quantity = $order_info['qty'];
            $unit = $order_info['unit'];
            $rate = $order_info['rate'];
            $tax = $order_info['tax'];
            $product_discount = $order_info['product_discount'];
            // $final_price = $order_info['final_price'];
            $amount = $order_info['amount'];

            // dd($quantity,$rate);

            // foreach ((object)$quantity as $i => $item) {

            //     // if()
            //     $amount[$i] = $quantity[$i] * $rate[$i] + (($quantity[$i] * $rate[$i]) * $tax[$i]/100);

            //     if(gettype($product_discount[$i]) == "integer" || gettype($product_discount[$i]) ==  "double" )
            //     {
            //         $amount[$i] -= $product_discount[$i];
            //     }

            //     $sub_total += $amount[$i];

            //     // dd($amount[$i]);
            // }

            // dd($sub_total);
            // $total = $sub_total;

            // if( $order_info['discount_type'] == 'percentage')
            // {
            //     $total = $sub_total - ($sub_total*abs($order_info['discount'])/100) + $order_info['adjustment'] ;
            // }
            // else if ($order_info['discount_type'] == 'fixed')
            // {
            //     $total = $sub_total - abs($order_info['discount']) + $order_info['adjustment'];
            // }

            // dd($total);

            /* ----------------- End Calculation ------------------ */

            $check['cms_user_id']       = $order_info['user_id'] ?? CRUDBooster::myId();
            $check['drm_customer_id']   = $order_info['drm_customer_id'];
            $check['order_date']        = $order_info['order_date'];
            $check['insert_type']       = $order_info['insert_type'];

            $check['shop_id']           = $order_info['shop_id'];
            $check['order_id_api']      = $order_info['order_id_api'];


            /* --------------- invoice number --------------------- */

            if(DB::table('drm_orders_new')->where($check)->count() >1)
                dd("Duplicate order. Contact developer.", $check);

            $order_inv = DB::table('drm_orders_new')->where($check)->first();


            if( ! ($order_inv == null || $order_inv ==[] || $order_inv->id ==null) )
            {
                $invoice_number = $order_inv->invoice_number;
            }
            else
            {
                $inv1 = DB::table('drm_orders_new')->where('cms_user_id',$check['cms_user_id'])->where('invoice_number','!=',-1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;

                $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id',$check['cms_user_id'])->first()->start_invoice_number ;

                // dd($inv1,$inv2);
                $invoice_number = ($inv1 > $inv2 )? $inv1 : $inv2;
            }


            /* -------------- future invoice ------------------- */
            $status = "Shipped";

            if($order_info['invoice_date'] != "" || $order_info['invoice_date'] != null)
            {
                $now = new DateTime();
                $due = new DateTime($order_info['invoice_date']);

                if( $due > $now )
                {
                    $status = "In Progress";
                    $invoice_number = -1;
                }
            }

            /* ------------------ insert order ----------------- */

            $row['invoice_number']  = $invoice_number;
            $row['invoice_date']    = $order_info['invoice_date'];

            if(strpos($order_info['total'],"," ))
            {
                $have = [".", ","];
                $will_be   = ["", "."];
                $order_info['total'] = str_replace($have, $will_be, $order_info['total']);
            }
            $row['total']           = $order_info['total'] ;
            $row['sub_total']       = $order_info['sub_total'];
            $row['total_tax']       = $order_info['total_tax'];

            $row['discount']        = $order_info['discount'];
            $row['discount_type']   = $order_info['discount_type'];
            $row['adjustment']      = $order_info['adjustment'];
            $row['payment_type']    = $order_info['payment_type'];
            $row['currency']        = $order_info['currency'];
            $row['shipping_cost']   = $order_info['shipping_cost'];
            $row['customer_info']   = $order_info['customer_info'];
            $row['billing']         = $order_info['billing'];
            $row['shipping']        = $order_info['shipping'];
            $row['client_note']     = $order_info['client_note'];
            $row['status']          = ucfirst($order_info['status'] ?? $status);
            $row['cms_client']      = $order_info['cms_client'];
            $row['cart']            = $order_info['cart'];

            DB::table('drm_orders_new')->updateOrInsert( $check , $row );

            $order = DB::table('drm_orders_new')->where($check)->first();

            $count = 0;

            foreach ((array)$quantity as $i => $item) {
                DB::table('drm_order_products')->updateOrInsert([
                    'product_id' => $product[$i],
                    'name' => '"'. str_replace('"', '', $product_name[$i])  .'"',
                    'drm_order_id' => $order->id,
                ],
                [
                    'quantity' => $quantity[$i] ?? 0,
                    'rate' =>round($rate[$i],2)?? 0,
                    'description' =>'"'.str_replace('"', '', $description[$i]).'"',
                    'unit' => $unit[$i],
                    'tax' => round($tax[$i],2) ?? 0,
                    'discount' =>round($product_discount[$i],2) ?? 0,
                    'amount' => round($amount[$i],2)?? 0,
                ]);

                // if($i==0)
                $count++;

            }

            // if($count==0 || $order_info['status'] != "Cancel")
            // {
            //     dd("Contact devoloper. Product count should not be ".count((array)$quantity));
            // }

            if(DB::table('drm_order_mail')->where( 'cms_user_id', $check['cms_user_id'] )->first()->auto_mail)
            {
                $this->send_email($order->id);
            }

            return $order;
        }


        // --------------------- Edit -------------------------------

        public function getEdit($id)
        {
            if(!CRUDBooster::isUpdate() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {
                CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
            }

            $data = [];
            $data['page_title'] = 'Edit Data';

            $data['columns'] = [
                "ean",
                "name",
                "id",

                // "drm_import_id",
                // "delivery_company_id",
                // "country_id",
                // "language_id",
                // "item_number",
                // "description",
                // "image",
                // "ek_price",
                // "vk_price",
                // "vat",
                // "stock",
                // "category",
                // "item_weight",
                // "update_enabled",
            ];

            // dd();
            if(CRUDBooster::isSuperadmin())
            {
                $customer_list = DB::table('drm_customers')->get();
                // $products_list = DB::table('drm_products')->select('id','drm_products.name','description')->whereNotNull('vk_price')->limit(150)->get();
            }
            else{
                $customer_list = DB::table('drm_customers')->where('user_id',CRUDBooster::myId())->get();

                // $products_list = DB::table('drm_products')
                // ->join('drm_imports', 'drm_products.drm_import_id', '=', 'drm_imports.id')
                // ->select('drm_products.id','drm_products.name','drm_products.description')
                // ->where('drm_imports.user_id', CRUDBooster::myId())->whereNotNull('vk_price')->limit(150)->get();

            }

            $data['customer_list'] = $customer_list ;
            // $data['products_list'] = $products_list;

            $data['order'] = DB::table('drm_orders_new')->where('id', $id)->first();

            $data['order']->products = DB::table('drm_order_products')->where('drm_order_id',$id)->get();

            // dd($data['invoice']);

            return view("admin.drm_order.edit",$data);
        }

        // --------------------- Edit Save --------------------------

        public function postEditSave($id)
        {

            // dd($_REQUEST);

            $validator = Validator::make($_REQUEST, [
                'drm_customer_id' => 'required',
                // 'invoice_number' => 'required',
                // 'invoice_date' => 'required|date',
                // 'due_date' => 'required|date',
                // 'payment_methods' => 'required|string',

                'currency' => 'required|string',
                'customer_info' => 'required|string',
                // 'billing' => 'required|string',

                // 'product_id' => 'required|array|min:1',
                'product_name' => 'required|array|min:1',

                // 'description' => 'required|array|min:1',
                // 'description.*' => 'required|string',

                'qty' => 'array|min:1',
                'qty.*' => 'numeric',

                'rate' => 'array|min:1',
                'rate.*' => 'numeric',

                'tax' => 'array|min:1',
                'tax.*' => 'numeric',

                // 'discount' => 'numeric',
                // 'discount_type' => 'required|string',
                // 'adjustment' => 'numeric',
            ]);

            if ($validator->fails()) {
                return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
                // return 0;
            }

            $order_info = $_REQUEST;
            // dd($order_info);

            /* ----------------- calculation ---------------------- */
            $o_p_id = $order_info['o_p_id'];
            $product = $order_info['product_id'];
            $product_name = $order_info['product_name'];
            $description = $order_info['description'];
            $quantity = $order_info['qty'];
            $unit = $order_info['unit'];
            $rate = $order_info['rate'];
            $tax = $order_info['tax'];
            $product_discount = $order_info['product_discount'];
            $amount = $order_info['amount'];
            // $sub_total = $order_info['sub_total'];
            // $total_tax = $order_info['total_tax'];
            // $total = $order_info['total'];

            // dd($quantity,$rate);

            // foreach ((object)$quantity as $i => $item) {

            //     // if()
            //     $amount[$i] = $quantity[$i] * $rate[$i] + (($quantity[$i] * $rate[$i]) * $tax[$i]/100);

            //     if(gettype($product_discount[$i]) == "integer" || gettype($product_discount[$i]) ==  "double" )
            //     {
            //         $amount[$i] -= $product_discount[$i];
            //     }

            //     $sub_total += $amount[$i];

            //     // dd($amount[$i]);
            // }

            // dd($sub_total);
            // $total = $sub_total;

            // if( $order_info['discount_type'] == 'percentage')
            // {
            //     $total = $sub_total - ($sub_total*abs($order_info['discount'])/100) + $order_info['adjustment'] ;
            // }
            // else if ($order_info['discount_type'] == 'fixed')
            // {
            //     $total = $sub_total - abs($order_info['discount']) + $order_info['adjustment'];
            // }

            // dd($total);

            /* ----------------- End Calculation ------------------ */

            $order = DB::table('drm_orders_new')->where('id', $id)->first();

            /* -------------- future invoice ------------------- */
            $invoice_number = $order->invoice_number;
            $status = "Shipped";

            if($order_info['invoice_date'] != "" || $order_info['invoice_date'] != null)
            {
                $now = new DateTime();
                $due = new DateTime($order_info['invoice_date']);

                if( $due > $now )
                {
                    $status = "In Progress";
                    $invoice_number = -1;
                }
            }


            // dd($status,$invoice_number);


            $row['invoice_number']  = $invoice_number;
            $row['order_date']      = $order_info['order_date'];
            $row['invoice_date']    = $order_info['invoice_date'];

            $row['total']           = $order_info['total'];
            $row['sub_total']       = $order_info['sub_total'] ;
            $row['total_tax']       = $order_info['total_tax'];

            $row['discount']        = $order_info['discount'];
            $row['discount_type']   = $order_info['discount_type'];
            $row['adjustment']      = $order_info['adjustment'];
            $row['payment_type']    = $order_info['payment_type'];
            $row['currency']        = $order_info['currency'];
            $row['customer_info']   = $order_info['customer_info'];
            $row['billing']         = $order_info['billing'];
            $row['shipping']        = $order_info['shipping'];
            $row['client_note']     = $order_info['client_note'];
            $row['status']          = $order_info['status'] ?? $status;
            $row['insert_type']     = 'Manual Update';

            // $row['cart']            = $order_info['cart'];

            // dd($row);

            // ---------- updating order ----------
            DB::table('drm_orders_new')->where('id', $id)->update( $row );

            $count = 0;

            foreach ((array)$quantity as $i => $item) {
                DB::table('drm_order_products')->updateOrInsert([
                    'id' => $o_p_id[$i]
                ],
                [
                    'drm_order_id' => $order->id,
                    'product_id' => $product[$i],
                    'name' => '"'. str_replace('"', '', $product_name[$i])  .'"',
                    'description' =>'"'.str_replace('"', '', $description[$i]).'"',
                    'quantity' => $quantity[$i],
                    'unit' => $unit[$i],
                    'rate' =>round($rate[$i],2),
                    'tax' => round($tax[$i],2),
                    'discount' => round($product_discount[$i],2),
                    'amount' => round($amount[$i],2),
                ]);

                // if($i==0)
                // $count++;

            }

            // if($count==0)
            // {
            //     dd("Contact devoloper. Product count should not be ".count((array)$quantity));
            // }

            // dd($count);


            CRUDBooster::redirect(CRUDBooster::adminPath('drm_orders'), trans('Order Edited'), 'success');

        }

        // --------------------- product-search-by-field --------------
        public function getProductSearchByField()
        {
            // return response()->json($_REQUEST);

            $data['products'] = DB::table('drm_products')
            ->select('id','name as text','user_id')
            ->where($_REQUEST['col'] ,'like', "%".$_REQUEST['value']."%")
            ->where('user_id',CRUDBooster::myId())
            ->get();

            return response()->json($data);
        }

        public function futureInoiceNumber()
        {
            $inv1 = DB::table('drm_orders_new')->where('cms_user_id',CRUDBooster::myId())->where('invoice_number','!=',-1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
            $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->first()->start_invoice_number ;

            // dd($inv1,$inv2);
            $invoice_number = ($inv1 > $inv2 )? $inv1 : $inv2;

            $f_invoice =  DB::table('drm_orders_new')->where('invoice_number',-1)->get();

            foreach ( (object) $f_invoice as  $item) {

                $now = new DateTime();
                $due = new DateTime($item->invoice_date);

                if( $due > $now )
                {
                    continue;
                }

                DB::table('drm_orders_new')->where('id',$item->id)->update([
                    'invoice_number' => $invoice_number ++,
                    'status' => "shipped",
                ]);
            }

        }


        // customer invoice number
        // get-customer-invoice-number
        // ----------------------------also used for getting customer details
        public function getGetCustomerInvoiceNumber()
        {
            $customer_id = $_REQUEST['id'];

            $data['customer'] = DB::table('drm_customers')->where('id',$customer_id)->first();
            $data['shipping'] = DB::table('drm_customer_address')->where([
                'drm_customer_id' =>$customer_id,
                'type' => 'shipping'
            ])->first();

            $data['billing'] =DB::table('drm_customer_address')->where([
                'drm_customer_id' =>$customer_id,
                'type' => 'billing'
            ])->first();

            $data['invoice_number'] = DB::table('drm_invoices')->where('user_id',CRUDBooster::myId())->count() + 1;
            return response()->json($data);
        }

        // product details
        public function getProductById()
        {
            $product_id = $_REQUEST['id'];

            $product = DB::table('drm_products')->where('id',$product_id)->first();

            $product->name =str_replace('"', '', $product->name);

            // $text =
            return response()->json($product);
        }

        public function getShowInvoicePreview()
        {

            $data['page_title'] = 'Invoice Details';
            $data['order'] = DB::table('drm_orders_new')->inRandomOrder()->first();

            $data['product_list'] = DB::table('drm_order_products')->where('drm_order_id',$data['order']->id)->get();
            $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->first();

            $pdf_path = 'storage/order_invoice/order'.$data['order']->id.'.pdf';

            // return view('admin.drm_order.invoice_pdf', $data);

            // dd($data);
            $pdf=\PDF::loadView('admin.drm_order.invoice_pdf', $data)->setWarnings(false);
            // return $pdf->download($data['order']->id.'.pdf');
            return $pdf->stream('invoice.pdf');
        }

        public function getInvoiceSetting()
        {

            $data['page_title'] = "Invoice Setting";

            $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->first();

            $inv1 = DB::table('drm_orders_new')->where('cms_user_id',CRUDBooster::myId())->where('invoice_number','!=',-1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
            $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->first()->start_invoice_number ;

            // dd($inv1,$inv2);

            $data['invoice_number_may'] = $inv1;
            $data['invoice_number_now'] = ($inv1 > $inv2 )? $inv1 : $inv2;

            // dd($data['invoice_number']);

            return view("admin.drm_order.invoice_setting",$data);
        }

        public function postSaveInvoiceSetting()
        {
            // dd($_REQUEST);
            // ffffffffffffffffffffffffffffffffffffffffffffffff

            // $temp = explode(".",$_FILES['shop_logo']['name']);
            $row = [];

            if($_FILES['shop_logo']['name'] != null)
            {
                $ext  = end(explode(".",$_FILES['shop_logo']['name']));
                $name = 'sl'.time().'.'.$ext;

                if(! move_uploaded_file($_FILES['shop_logo']['tmp_name'], './storage/shop_logo/'.$name))
                {
                    CRUDBooster::redirect(CRUDBooster::adminPath('drm_invoice/invoice-setting'), trans('Picture not uploaded'), 'error');
                }

                $row['logo'] = $name;
            }
            else if($_REQUEST['is_delete'])
            {
                $row['logo'] = '';
            }
            // 'logo' => $name,
            $row += [
                'logo_position' => $_REQUEST['logo_position'],
                'head_text' => $_REQUEST['head_text'],
                'store_name' => $_REQUEST['store_name'],
                'email' => $_REQUEST['email'],
                'start_invoice_number' => $_REQUEST['start_invoice_number'],
                // 'currency' => $_REQUEST['currency'],
                'company_address' => $_REQUEST['company_address'],
                'color' => $_REQUEST['color'],
                // 'current_month' => $_REQUEST['current_month'],
                // 'url' => $_REQUEST['url'],
                // 'note' => $_REQUEST['note'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'col1_text' => $_REQUEST['col1_text'],
                'col2_text' => $_REQUEST['col2_text'],
                'col3_text' => $_REQUEST['col3_text'],
                'col4_text' => $_REQUEST['col4_text'],
            ];

            DB::table('drm_invoice_setting')->updateOrInsert([
                'cms_user_id' => CRUDBooster::myId()
            ],$row);

            CRUDBooster::redirect(CRUDBooster::adminPath('drm_orders/invoice-setting'), trans('Setting Changed'), 'success');
        }

        public function generate_invoice_pdf($order_id, $local= false)
        {
            // dd($order_id);

            $drm_order_new = DB::table('drm_orders_new')->where('id',$order_id)->first();

            $data = [];
            $data['page_title'] = 'Invoice Details';
            if($drm_order_new->insert_type == "Stripe" || $drm_order_new->insert_type == "Charge"){
                $data['order'] = DB::table('drm_orders_new')
                            ->select('*','drm_orders_new.id')
                            ->join('cms_users','drm_orders_new.cms_client','=','cms_users.id')
                            ->where('drm_orders_new.id',$order_id)
                            ->first();
            }
            elseif($drm_order_new->insert_type == "Stripe API"){
                $data['order'] = DB::table('drm_orders_new')
                            ->where('id',$order_id)
                            ->first();
            }
            else{
            $data['order'] = DB::table('drm_orders_new')
                            ->select('*','drm_orders_new.id', 'drm_orders_new.status')
                            ->join('drm_customers','drm_orders_new.drm_customer_id','=','drm_customers.id')
                            ->where('drm_orders_new.id',$order_id)
                            ->first();
            }

            //dd($data['order']);

            //DB::table('drm_orders_new')->where('id',$order_id)->first();

            $data['product_list'] = DB::table('drm_order_products')->where('drm_order_id',$order_id)->get();
            // dd($data['product_list']);
            if($drm_order_new->insert_type == "Stripe"){
                $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',98)->first();
            }
            else{
                $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->first();
            }

            $pdf_path = 'order_invoice/order'.$data['order']->id.'.pdf';
            if($local)
            {
                $pdf_path = 'storage/order_invoice/order'.$data['order']->id.'.pdf';
            }

            // dd($data);
            if($drm_order_new->insert_type == "Stripe"){
                // $data['drm_order_new'] = $drm_order_new;
                // $pdf=\PDF::loadView('admin.drm_order.stripe_invoice_pdf', $data)->setWarnings(false)->save($pdf_path);
                // $pdf=\PDF::loadView('admin.drm_order.stripe_invoice_pdf', $data)->setWarnings(false)->stream();
                $pdf_view = 'admin.drm_order.stripe_invoice_pdf';
            }
            else if($drm_order_new->insert_type == "Stripe API"){
                $pdf_view = 'admin.drm_order.stripe_api_invoice_pdf';
            }
            else if($drm_order_new->insert_type == "Charge"){
                // $data['drm_order_new'] = $drm_order_new;
                // $pdf=\PDF::loadView('admin.drm_order.paywall_invoice_pdf', $data)->setWarnings(false)->save($pdf_path);
                // $pdf=\PDF::loadView('admin.drm_order.paywall_invoice_pdf', $data)->setWarnings(false)->stream();

                $pdf_view = 'admin.drm_order.paywall_invoice_pdf';

            }
            else if($drm_order_new->insert_type == "API")
            {
                // $pdf=\PDF::loadView('admin.drm_order.invoice_pdf_api', $data)->setWarnings(false)->save($pdf_path);
                // $pdf=\PDF::loadView('admin.drm_order.invoice_pdf_api', $data)->setWarnings(false)->stream();
                $pdf_view = 'admin.drm_order.invoice_pdf_api';

            }
            else
            {
                // $pdf=\PDF::loadView('admin.drm_order.invoice_pdf', $data)->setWarnings(false)->save($pdf_path);
                // $pdf=\PDF::loadView('admin.drm_order.invoice_pdf', $data)->setWarnings(false)->stream();

                $pdf_view = 'admin.drm_order.invoice_pdf';
            }

            if($local)
            {
                $pdf=\PDF::loadView($pdf_view, $data)->setWarnings(false)->save($pdf_path);
                return $pdf_path;
            }

            $pdf=\PDF::loadView($pdf_view, $data)->setWarnings(false)->stream();
            Storage::disk('spaces')->put($pdf_path, $pdf, 'public');

            if(Storage::disk('spaces')->exists($pdf_path)){
				return Storage::disk('spaces')->url($pdf_path);
			}
            // return $pdf_path;
            return null;
        }



        // ------------- export-order-pdf ------------------
        public function postExportOrderPdf()
        {
            // fffffffffffffffffffffffffffffffffffffffff
            // return response()->json($_REQUEST);

            $zip = new ZipArchive(); // Load zip library

            $zip_name = "storage\order_invoice\Orders_pdf".CRUDBooster::myId().".zip"; // Zip name

            $zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE);

            foreach ($_REQUEST["orders"] as $order_id)
            {
                $pdf_path = 'storage/order_invoice/order'.$order_id.'.pdf';

                if(! $zip->addFile($pdf_path,"order".$order_id.".pdf"))
                {
                    $pdf_path = $this->generate_invoice_pdf($order_id,true);
                    $zip->addFile($pdf_path,"order".$order_id.".pdf");
                }

                // unlink(realpath($pdf_path));
            }

            $zip->close();

            //foreach ($_REQUEST["orders"] as $order_id)
            //{
            //    $pdf_path = 'storage/order_invoice/order'.$order_id.'.pdf';
            //    unlink(realpath($pdf_path));
            //}

            session(['order_pdf_url' => $zip_name]);

            // session('order_pdf_url');

            // dd(realpath($zip_name));
            // return response()->download(realpath($zip_name));

            // $data['url'] = asset('storage/order_invoice/Orders_pdf.zip');
            // return response()->json($data);
            return "success";

            // return response()->download(realpath('storage/order_invoice/order'.'49'.'.pdf'));
        }

        public function getDownloadOrderPdf()
        {

            header('Content-type: application/zip');
            header('Content-Disposition: attachment; filename="Orders.zip"');
            readfile(realpath(session('order_pdf_url')));
            unlink(realpath(session('order_pdf_url')));
        }



        // ------------------ generate delivery note pdf --------------

        public function generate_delivery_note($order_id)
        {


            $data['page_title'] = 'Delivery Note';
            // $data['order'] = DB::table('drm_orders_new')->where('id',$order_id)->first();
            // cms_client
            $drm_order_new = DB::table('drm_orders_new')->where('id',$order_id)->first();
            if($drm_order_new->insert_type == "Stripe" || $drm_order_new->insert_type == "Charge"){
                $data['order'] = DB::table('drm_orders_new')
                            ->select('*','drm_orders_new.id')
                            ->join('cms_users','drm_orders_new.cms_client','=','cms_users.id')
                            ->where('drm_orders_new.id',$order_id)
                            ->first();
            }
            elseif($drm_order_new->insert_type == "Stripe API"){
                $data['order'] = DB::table('drm_orders_new')
                            ->where('id',$order_id)
                            ->first();
            }
            else{
                $data['order'] = DB::table('drm_orders_new')
                            ->select('*','drm_orders_new.id')
                            ->join('cms_users','drm_orders_new.cms_user_id','=','cms_users.id')
                            ->where('drm_orders_new.id',$order_id)
                            ->first();
            }

            // dd($data['order']->insert_type);


            $data['product_list'] = DB::table('drm_order_products')->where('drm_order_id',$order_id)->get();
            if($drm_order_new->insert_type == "Stripe"){
                $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',98)->first();
            }
            else{
                $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->first();
            }
            $pdf_path = 'storage/order_delivery_note/delivery_note'.$data['order']->id.'.pdf';
            // dd($data);
            if($data['order']->insert_type == "Stripe"){
                $pdf=\PDF::loadView('admin.drm_order.stripe_delevery_pdf', $data)->setWarnings(false)->save($pdf_path);
            }
            else if($drm_order_new->insert_type == "Stripe API"){
                $pdf=\PDF::loadView('admin.drm_order.stripe_api_delevery_pdf', $data)->setWarnings(false)->save($pdf_path);
            }
            else if($drm_order_new->insert_type == "Charge"){
                $pdf=\PDF::loadView('admin.drm_order.paywall_delevery_pdf', $data)->setWarnings(false)->save($pdf_path);
            }
            else{
            $pdf=\PDF::loadView('admin.drm_order.delevery_pdf', $data)->setWarnings(false)->save($pdf_path);
            }

            return $pdf_path;

        }


        // ---------------- download delivery note -----------
        public function getDeliveryNotes($order_id){

            $pdf_path = $this->generate_delivery_note($order_id);

            header("Content-Type: application/octet-stream");
            header('Content-Disposition: attachment; filename="delivery_note'.$order_id.'.pdf"');
            readfile(realpath($pdf_path));

        }


        // --------------------- mass delivery note generate ---------------
        public function postExportDeleveryNotePdf()
        {
            // dddddddddddddddddddddddddddddddddddddd
            // return response()->json($_REQUEST);

            $zip = new ZipArchive(); // Load zip library

            $zip_name = "storage\order_delivery_note\delivery_notes_pdf".CRUDBooster::myId().".zip"; // Zip name

            $zip->open($zip_name, ZIPARCHIVE::CREATE | ZIPARCHIVE::OVERWRITE);

            foreach ($_REQUEST["orders"] as $order_id)
            {
                $pdf_path = 'storage/order_delivery_note/delivery_note'.$order_id.'.pdf';

                if(! $zip->addFile($pdf_path,"delivery_note".$order_id.".pdf"))
                {
                    $pdf_path = $this->generate_delivery_note($order_id);
                    $zip->addFile($pdf_path,"delivery_note".$order_id.".pdf");
                }

            }

            $zip->close();

            session(['delivery_note_pdf_url' => $zip_name]);

            // session('order_pdf_url');

            // dd(realpath($zip_name));
            // return response()->download(realpath($zip_name));

            // $data['url'] = asset('storage/order_invoice/Orders_pdf.zip');
            // return response()->json($data);
            return "success";
        }


        public function getDownloadDeliveryNotePdf()
        {
            header('Content-type: application/zip');
            header('Content-Disposition: attachment; filename="DeliveryNotes.zip"');
            readfile(realpath(session('delivery_note_pdf_url')));
            unlink(realpath(session('delivery_note_pdf_url')));
        }


        public function getTrash()
        {

            $data['orders'] = DB::table('drm_orders_new')
            ->where('cms_user_id', CRUDBooster::myId())
            ->where('trash', 'Yes')
            ->get();

            // dd($data['orders']);

            return view('admin.drm_order.trash',$data);
        }

        public function getSetTrash($id)
        {
            // dd($id);
            DB::table('drm_orders_new')->where('id',$id)->update([
                'trash' => 'Yes'
            ]);

            CRUDBooster::redirect(CRUDBooster::adminPath('drm_orders'), trans('Order sent to trash.'), 'success');

        }
        public function getRemoveTrash($id)
        {
            // dd($id);
            DB::table('drm_orders_new')->where('id',$id)->update([
                'trash' => 'No'
            ]);

            CRUDBooster::redirect(CRUDBooster::adminPath('drm_orders'), trans('Order sent to trash.'), 'success');
        }

        public function getSendEmail($id) {
            //Create an Auth
            if(!CRUDBooster::isRead() && $this->global_privilege==FALSE || $this->button_edit==FALSE) {
                CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
            }

            $this->send_email($id);

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Sent'), 'success');

        }

        public function send_email($order_id)
        {

            $data = [];
            $data['page_title'] = 'Invoice Details';
            $data['order'] = $order = DB::table('drm_orders_new')->where('id',$order_id)->first();
            // dd($data['order']->insert_type);
            // $data['product_list'] = DB::table('drm_order_products')->where('drm_order_id',$order_id)->get();

            $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->first();

            $data['mail'] = DB::table('drm_order_mail')->where('cms_user_id',CRUDBooster::myId())->first();

            // $data['pdf_link'] = asset('storage/order_invoice/order'.$data['order']->id.'.pdf');


            if($data['order']->insert_type == "Stripe" || $data['order']->insert_type == "Charge"){
                $data['customerEmail']=DB::table('cms_users')->where('id',$data['order']->cms_client)->first()->email;
            }
            else{
                $data['customerEmail']=DB::table('drm_customers')->where('id',$data['order']->drm_customer_id)->first()->email;
            }

            if ( !( filter_var( $data['setting']->email, FILTER_VALIDATE_EMAIL) && filter_var( $data['customerEmail'], FILTER_VALIDATE_EMAIL)) )
            {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Something Wrong! Email Not Sent.'), 'warning');
            }

			$order_data['page_title'] = 'Invoice Details';
            $order_data['order'] = DB::table('drm_orders_new')->inRandomOrder()->first();
            $order_data['product_list'] = DB::table('drm_order_products')->where('drm_order_id',$data['order']->id)->get();
            $order_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->first();

            $pdf_stream = \PDF::loadView('admin.drm_order.invoice_pdf', $order_data)->setWarnings(false)->stream();

 			app('drm.mailer')->getMailer($order->cms_user_id,$data['setting']->email)->send('admin.drm_order.send_email_invoice', $data, function($messages) use ($data, $pdf_stream){
 	            // $messages->from($data['setting']->email);
                $messages->to($data['customerEmail']);
 	            $messages->subject($data['mail']->mail_subject);
				$messages->attachData($pdf_stream, 'invoice.pdf', [
                    'mime' => 'application/pdf',
                ]);
 	        });

            DB::table('drm_orders_new')->where('id', $order_id)->update(['mail_sent' => date('Y-m-d H:i:s')]);
        }



        public function getProductList($id, $drm_customer_id)
        {
        	$data = [];

        	$data['order']= DB::table('drm_orders_new')->find($id);
        	$data['product_list']=DB::table('drm_order_products')->where('drm_order_products.drm_order_id',$id)->paginate(10);
        	$data['customer'] = DB::table('drm_customers')->where('drm_customers.id', $drm_customer_id)->select('full_name', 'company_name')->first();
        	$data['address'] = DB::table('drm_customer_address')->where('drm_customer_address.drm_customer_id', $drm_customer_id)->get()->toArray();
        	// return response()->json($data);
        	return view('admin.drm_order.product_list',$data)->render();
        }

        public function postImportOrder()
        {
            // dd($_FILES);

            // dd(file_exists($_FILES["order_file"]["tmp_name"]));

            $spreadsheet = IOFactory::load($_FILES["order_file"]["tmp_name"]);
            $data_arr = $spreadsheet->getActiveSheet()->toArray();
            unset($data_arr[0]);


            // dd($data_arr);


            // $orderCollection = new Collection($data_arr);

            // dd($orderCollection->first());
            // $order_ids = [];


            // dd($row);



            // foreach ($data_arr as $key => $value) {
            //     if(in_array($value[0], (array) $order_ids["id"]))
            //     {
            //         $order_ids["dup"][] = $value;
            //     }

            //     $order_ids["id"][] = $value[0];

            // }
            // dd($data_arr[0],$order_ids["dup"]);



            foreach ($data_arr as $value) {

                $customer_info = $order_info = [];

                $customer_info = [
                    "customer_full_name" => $value[5],
                    "currency" => $value[10] ,
                    'email' => $value[4] ,
                    'address' =>  $value[17] ,
                    'city' => $value[20],
                    'country' => $value[23] ,
                    'phone' => $value[24] ,
                    'zip_code' => $value[22] ,
                    'insert_type'=>'Import',

                    //shipping
                    // 'street_shipping' => $value[] ,
                    'city_shipping' => $value[20] ,
                    'state_shipping' => $value[21] ,
                    'zipcode_shipping' => $value[22] ,
                    'country_shipping' => $value[23] ,
                    'status' => 1 ,
                    'source' => 56 ,
                ];

                $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);
                // dd($customer_id);

                $order_info['drm_customer_id'] = $customer_id ;
                $order_info['order_date'] = $value[2];
                $order_info['insert_type'] = "Import";
                // $order_info['total'] = ;
                // $order_info['shop_id'] = ;
                $order_info['order_id_api'] = $value[0];

                // $order_info['sub_total'] = $total_sum;
                $order_info['discount'] = 0 ;
                $order_info['discount_type'] = "fixed";
                $order_info['adjustment'] = 0;
                $order_info['payment_type'] = $value[15];
                $order_info['currency'] =  $customer_info["currency"];

                $order_info['customer_info'] = $customer_info['customer_full_name'].'<br>'. $customer_info['company_name'] .'<br>'. $customer_info['address'] .'<br>'.$customer_info['zip_code'].' '.$customer_info['city'] .'<br>'. $customer_info['state'].'<br>'. $customer_info['country'];


                $ship['d_street'] 	  = $customer_info['street_shipping']  .'<br>';
                $ship['d_state'] 	  = $customer_info['city_shipping'] .'<br>';
                $ship['d_postcode'] 	  = $customer_info['zipcode_shipping'];
                $ship['d_city']  	  = $customer_info['city_shipping'] .'<br>';
                $ship['d_country'] = $customer_info['country_shipping'];

                $order_info['shipping'] = $ship['d_street'].  $ship['d_state'].$ship['d_postcode'].' '.$ship['d_city'].$ship['d_country'];

                $order_info['client_note'] = $value[7];
                $order_info['status'] = "Shipped";

                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT',$value[8]);
                // $order_info['description'][] =  iconv('UTF-8', 'ASCII//TRANSLIT', );
                $order_info['qty'][] = $value[9];
                $order_info['rate'][] = $value[11];
                // $order_info['unit'][] = $item->quantityUnitName;
                $order_info['tax'][] = $value[12];
                $order_info['product_discount'][]= 0;
                // $order_info['final_price'][] = $item->finalPrice;

                $this->add_order($order_info);


            }

            // dd($count);
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_orders'), trans('Order Edited'), 'success');
        }

        public function getEmailSetting()
        {
            // dd($_REQUEST);

            $data['mail'] = DB::table('drm_order_mail')->where('cms_user_id',CRUDBooster::myId())->first();

            return view("admin.drm_order.email_setting",$data);
        }

        public function postSaveEmailSetting()
        {
            // dd($_REQUEST);

            DB::table('drm_order_mail')->updateOrInsert([
                'cms_user_id' => CRUDBooster::myId()
            ],
            [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => $_REQUEST['auto_mail'],
            ]);

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');

        }

        public function getFixInvoiceNumber()
        {
            DB::table('shops')
            ->orderBy('id')->each(function($item){

                $d_in = DB::table('drm_orders_new')
                ->select(DB::raw('invoice_number, COUNT(invoice_number) as count'))
                ->where([
                    'cms_user_id' => $item->user_id,
                    // 'shop_id' => $item->id
                ])
                ->groupBy('invoice_number')
                ->having('count','>',1)
                ->first();

                echo $d_in->count."<br>";

                if($d_in->count > 1)
                {
                    // dd($d_in->invoice_number);
                    $all_dup = DB::table('drm_orders_new')->where([
                        'cms_user_id' => $item->user_id,
                        'invoice_number' => $d_in->invoice_number,
                    ])->get();

                    $total_inv = DB::table('drm_orders_new')->where('cms_user_id' , $item->user_id)->where('invoice_number','!=',-1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;

                    foreach ((object) $all_dup as $value) {
                        // dd($value->id);
                        DB::table('drm_orders_new')->where('id',$value->id)
                        ->update([
                            'invoice_number' => $total_inv++
                        ]);
                    }
                }
            });

            dd('Successfull');
            // dd($all_shop);

            // SELECT  cms_user_id,invoice_number,COUNT(invoice_number) FROM drm_orders_new
            // WHERE drm_orders_new.cms_user_id = 1 AND drm_orders_new.shop_id = 4
            // GROUP BY invoice_number
            // HAVING COUNT(invoice_number) >1;

        }

        public function getChargedAmountList(){
		  //  $data = [];
            $data['page_title'] = 'User List';
            $users = \App\User::with('paywall')->where('id_cms_privileges', 3)
                ->select('id','name','email')
                ->get();
            foreach($users as $key => $id){

            	$charge = $id->paywall_charge;
            	$additional_charge = $id->paywall_additional_charge;

            $data['content'][$key]['users'] = $id;
            $data['content'][$key]['payable_charged'] = DB::table('drm_orders_new')
                        ->where('cms_user_id',$id->id)
                        ->where('char_status',0)
                        ->select(DB::raw('SUM(total) as total_price'),DB::raw('count(Distinct id) as total_order'))
                        ->first();
            $data['content'][$key]['_payable'] = number_format((($charge * $data['content'][$key]['payable_charged']->total_price) / 100 + $data['content'][$key]['payable_charged']->total_order * $additional_charge), 2, ',', '.');

            $paid_charged = DB::table('drm_orders_new')
                        ->where('cms_user_id',$id->id)
                        ->where('char_status',1)
                        ->select(DB::raw('SUM(total) as total_price'),DB::raw('count(Distinct id) as total_order'))
                        ->first();
            $data['content'][$key]['_paid'] = number_format((($charge * $paid_charged->total_price) / 100 + $paid_charged->total_order * $additional_charge), 2, ',', '.');
            }

            return view('admin.drm_order.charged_amount_list',$data);
        }



        public function internalOrderInsert($order_info, $customer_info = [])
        {
            // dd($customer_info);

            $validator = Validator::make($order_info, [
                // 'invoice_date' => 'required|date',
                // 'due_date' => 'required|date',
                // 'payment_methods' => 'required|string',
                'currency' => 'required|string',
                // 'customer_info' => 'required|string',
                // 'shipping' => 'required|string',
                // 'billing' => 'required|string',
                'insert_type'   => 'required|min:1',
                'status' => 'required|min:1',
                // 'qty' => 'required|array|min:1',
                // 'qty.*' => 'required|numeric',

                // 'rate' => 'required|array|min:1',
                // 'rate.*' => 'required|numeric',

                // 'tax' => 'required|array|min:1',
                // 'tax.*' => 'required|numeric',
            ]);


            if ($validator->fails()) {
                // dd();
                return response()->json($validator->errors(),403);
                // return 0;
            }
            // $order_info['insert_type'] = "Stripe";

            return $this->add_order($order_info);

        }

        public function syncOrder(){
        	// \Artisan::call('schedule:run');
        	\Artisan::call('order:sync');
        	// \Artisan::queue('order:sync');
        }


	}
