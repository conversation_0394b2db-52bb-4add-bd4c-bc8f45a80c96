<?php

namespace App\Http\Controllers;

use App\ProjectTasks;
use App\AppointmentProjectSlot;
use App\Appointment;
use App\AppointmentBooking;
use App\DropfunnelCustomerTag;
use App\DropfunnelTag;
use AppStore;
use App\Mail\DRMSEndMail;
use App\Notifications\DRMNotification;
use App\User;
use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request as LaravelRequest;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Mews\Purifier\Facades\Purifier;
use App\Exports\ProjectTimeExport;
use Maatwebsite\Excel\Facades\Excel;
use App\Services\DRM\TaskActivity;
use Notification;
use Request;
use App\Helper\DrmHelper;
use App\NewCustomer;
use App\OrderTrackings as OrderTracking;
use App\OrderStatus;
use Exception;

class AdminDrmProjectsController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {

        //jahidulhasanzahid
        // this is used for privacy access, like when user have no purchase plan, then it protect to visit incoming invoice feature
        // $this->CheckAppPurchase('DRM-Projekt');
        // if (app()->environment('local')) {
        // $app_details  = AppStore::ActiveApp('DRM-Projekt');
        // }
        //jahidulhasanzahid

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "title";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = true;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
//        $this->button_add = true;
//        if (!$this->app_purchased() && !CRUDBooster::isSuperadmin()) {
//            $this->button_add = false;
//        }
        // $this->button_delete = false;
        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "drm_projects";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "Title", "name" => "id"];
        $this->col[] = ["label" => "Title", "name" => "title", 'visible' => false];
        // $this->col[] = ["label"=>"Company","name"=>"drm_customer_id","join"=>"drm_customers,company_name"];
        $this->col[] = ["label" => "Created By", "name" => "cms_user_id", "join" => "cms_users,name"];
        $this->col[] = ["label" => "Start Date", "name" => "start_date"];
        $this->col[] = ["label" => "Due Date", "name" => "due_date"];
        $this->col[] = ["label" => "Status", "name" => "status"];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'Title', 'name' => 'title', 'type' => 'text', 'validation' => 'required|string|min:3|max:70', 'width' => 'col-sm-10', 'placeholder' => 'You can only enter the letter only'];
        // $this->form[] = ['label'=>'Drm Customer Id','name'=>'drm_customer_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'drm_customer,id'];
        // $this->form[] = ['label'=>'Tags','name'=>'tags','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        $this->form[] = ['label' => 'Start Date', 'name' => 'start_date', 'type' => 'date', 'validation' => 'required|date', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Due Date', 'name' => 'due_date', 'type' => 'date', 'validation' => 'required|date', 'width' => 'col-sm-10'];
        // $this->form[] = ['label'=>'Rate Type','name'=>'rate_type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        // $this->form[] = ['label'=>'Total Cost','name'=>'total_cost','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        $this->form[] = ['label' => 'Status', 'name' => 'status', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Description', 'name' => 'description', 'type' => 'textarea', 'validation' => 'required|string|min:5|max:5000', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Created By', 'name' => 'cms_user_id', 'type' => 'select2', 'validation' => 'required|integer|min:0', 'width' => 'col-sm-10', 'datatable' => 'cms_users,name'];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ["label"=>"Title","name"=>"title","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
        //$this->form[] = ["label"=>"Drm Customer Id","name"=>"drm_customer_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"drm_customer,id"];
        //$this->form[] = ["label"=>"Tags","name"=>"tags","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Start Date","name"=>"start_date","type"=>"date","required"=>TRUE,"validation"=>"required|date"];
        //$this->form[] = ["label"=>"Due Date","name"=>"due_date","type"=>"date","required"=>TRUE,"validation"=>"required|date"];
        //$this->form[] = ["label"=>"Rate Type","name"=>"rate_type","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Total Cost","name"=>"total_cost","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Status","name"=>"status","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Description","name"=>"description","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Cms User Id","name"=>"cms_user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"cms_user,id"];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();
        // $this->addaction[] = ['label'=>'Set Active','url'=>CRUDBooster::mainpath('edit/[id]'),'icon'=>'fa fa-check','color'=>'success','showIf'=>"[cms_user_id] == [cms_user_id]"];
        $this->addaction[] = ['label' => 'Project Acitivity', 'url' => CRUDBooster::mainpath('project/[id]'), 'icon' => 'fa fa-check', 'color' => 'warning'];


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
                */

        $projects = DB::table('drm_project_members')->Join('drm_projects', 'drm_projects.id', '=', 'drm_project_members.drm_project_id')
            ->where('drm_project_members.cms_user_id', CRUDBooster::myId())->get();

        $not_started = $projects->where('status', 'not_started')->count();
        $in_progress = $projects->where('status', 'in_progress')->count();
        $on_hold = $projects->where('status', 'on_hold')->count();
        $finished = $projects->where('status', 'finished')->count();
        $canceled = $projects->where('status', 'canceled')->count();
        $total = $not_started + $in_progress + $on_hold + $finished + $canceled;

        $this->index_statistic = array();
        $this->index_statistic[] = ['label' => 'Not started', 'count' => $not_started, 'icon' => 'fa fa-area-chart', 'color' => 'success btn-primary not_started', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'In progress', 'count' => $in_progress, 'icon' => 'fa fa-google-wallet', 'color' => 'success btn-primary in_progress', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'On hold', 'count' => $on_hold, 'icon' => 'fa fa fa-line-chart', 'color' => 'success btn-warning on_hold', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'Finished', 'count' => $finished, 'icon' => 'fa fa-briefcase', 'color' => 'success btn-success finished', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'Canceled', 'count' => $canceled, 'icon' => 'fa fa-file-image-o', 'color' => 'success btn-danger canceled', 'width' => 'col-md-2 '];
        $this->index_statistic[] = ['label' => 'Total Data', 'count' => $total, 'icon' => 'fa fa-pie-chart', 'color' => 'success btn-default total', 'width' => 'col-md-2 '];


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
                */

        // $projects->Join('drm_project_members','drm_projects.id', '=', 'drm_project_members.drm_project_id')
        // 		->where('drm_project_members.cms_user_id',CRUDBooster::myParentId())/* ->orWhere('drm_projects.cms_user_id',CRUDBooster::myParentId()) */->get();
        // $project_ids = DB::table('drm_project_members')->where("cms_user_id",CRUDBooster::myParentId())->pluck('drm_project_id');


        $projects = DB::table('drm_project_members')->Join('drm_projects', 'drm_projects.id', '=', 'drm_project_members.drm_project_id')
            ->where('drm_project_members.cms_user_id', CRUDBooster::myId())->get();


        // dd($projects);
        $this->script_js = "
			let projects = " . $projects . ";
			let user_id = " . CRUDBooster::myParentId() . ";

			$('#table_dashboard tr').find('a[title=Delete]').hide();
			$('#table_dashboard tr').find('a[title=\"Edit Data\"]').hide();

			let tr = $('#table_dashboard tr');


			projects.forEach(function(item, i) {

				if(item.cms_user_id == user_id)
				{
						let string = item.title + item.start_date + item.status;
						// console.log(string);

						$.each(tr,function(index,value){

							let td = $(value).find('td') ;

							let title = $(td[1]).html(),
							date = $(td[3]).html(),
							status = $(td[5]).html();
							let string1 = title + date + status;
							if(string == string1)
							{
								$(value).find('a[title=Delete]').show() ;
								$(value).find('a[title=\"Edit Data\"]').show() ;
							}
						});
				}
			});" .

            '
			$(".total").click(function () {
				window.location.href = window.location.origin+ "/admin/drm_projects" ;
			});

			$(".not_started").click(function () {
				window.location.href = window.location.origin+ "/admin/drm_projects?filter=not_started";
			});

			$(".in_progress").click(function () {
				window.location.href = window.location.origin+ "/admin/drm_projects?filter=in_progress";
			});

			$(".on_hold").click(function () {
				window.location.href = window.location.origin+ "/admin/drm_projects?filter=on_hold";
			});

			$(".finished").click(function () {
				window.location.href = window.location.origin+ "/admin/drm_projects?filter=finished";
			});

			$(".canceled").click(function () {
				window.location.href = window.location.origin+ "/admin/drm_projects?filter=canceled";
			});
		';


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = ".small-box{cursor: pointer;border-radius: 5px;padding:15px 10px;}
				.small-box .icon i{vertical-align: top;font-size:35px;transition: .5s;vertical-align: top;margin-top: 18px;}
				.small-box:hover i{font-size: 40px;transition: .5s;}
				.margin-style{width: 159px;}
				.small-box>.inner{padding: 0px;}
				.small-box.bg-success.btn-default {background: #fff !important;}
				.small-box.bg-success.btn-default:hover {color: #444;background: #e7e7e7 !important;transition: .5s;}";


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here
        // dd($id_selected,$button_name);

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        //Your code here
        // if(!CRUDBooster::isSuperadmin()){

        // }
        $query
            ->Join('drm_project_members', 'drm_projects.id', '=', 'drm_project_members.drm_project_id')
            ->where('drm_project_members.cms_user_id', CRUDBooster::myId());

        if (isset($_REQUEST['filter'])) {
            if ($_REQUEST['filter'] === 'not_started') {
                $query->where('drm_projects.status', 'not_started');
            } else if ($_REQUEST['filter'] === 'in_progress') {
                $query->where('drm_projects.status', 'in_progress');
            } else if ($_REQUEST['filter'] === 'on_hold') {
                $query->where('drm_projects.status', 'on_hold');
            } else if ($_REQUEST['filter'] === 'finished') {
                $query->where('drm_projects.status', 'finished');
            } else if ($_REQUEST['filter'] === 'canceled') {
                $query->where('drm_projects.status', 'canceled');
            }
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        $id_col_index = 1;


        if ($column_index == $id_col_index) {
            $row_data = DB::table('drm_projects')->find($column_value, ['id', 'title']);
            $url = 'project/' . $row_data->id;
            $column_value = '<a href="' . CRUDBooster::mainpath($url) . '">' . $row_data->title . '</a>';
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {

        if (DB::table("drm_projects")->find($id)->cms_user_id != CRUDBooster::myParentId()) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */

    public function hook_before_delete($id)
    {

        if (DB::table("drm_projects")->find($id)->cms_user_id != CRUDBooster::myParentId()) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        // drm_projects, drm_project_cards, drm_project_card_lists, drm_project_members,
        // drm_project_milestones, drm_project_tasks,
        // drm_task_checklist, drm_task_comments, drm_task_members

        $card_ids = DB::table('drm_project_cards')->where('drm_project_id', $id)->pluck('id')->toArray();
        // dd($card_ids);
        $task_ids = DB::table('drm_project_tasks')->whereIn('drm_project_card_id', $card_ids)->pluck('id')->toArray();
        // dd($task_ids);

        DB::table('drm_task_checklist')->whereIn('task_id', $task_ids)->delete();
        DB::table('drm_task_comments')->whereIn('task_id', $task_ids)->delete();
        // DB::table('drm_task_members')->whereIn('task_id', $task_ids)->delete();


        // dd($task_ids);
        // $cards;
        // DB::table('drm_project_card_lists')->where()-delete();

        DB::table('drm_project_tasks')->whereIn('id', $task_ids)->delete();
        DB::table('drm_project_cards')->where("drm_project_id", $id)->delete();
        // DB::table('drm_project_milestones')->where("drm_project_id", $id)->delete();
        DB::table('drm_project_members')->where("drm_project_id", $id)->delete();
        Appointment::where('slug', $id)->where('user_id', CRUDBooster::myParentId())->delete();

        /* drm project will be deleted by crud booster  */
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }


    public function getIndex()
    {
      //First, Add an auth
      $user_id = CRUDBooster::myParentId();
      if(empty($user_id)) CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));

      //Create your own query
      $data = [];
      $data['hide_title_and_icon'] = true;
      $data['page_title'] = __('Drm Projects');
      $data['hide_add_data_title'] = true;
    //   $data['action_add_data_title'] = __('Add project');
      $project_slots = AppointmentProjectSlot::where('user_id',CRUDBooster::myParentId())->get();
      $data['projects'] = DB::table('drm_project_members')->Join('drm_projects', 'drm_projects.id', '=', 'drm_project_members.drm_project_id')
      ->where('drm_project_members.cms_user_id', CRUDBooster::myId())
      ->orderBy('drm_projects.sort')
      ->get();
      $data['not_started'] = $data['projects']->where('status', 'not_started')->count();
      $data['in_progress'] = $data['projects']->where('status', 'in_progress')->count();
      $data['on_hold'] = $data['projects']->where('status', 'on_hold')->count();
      $data['finished'] = $data['projects']->where('status', 'finished')->count();
      $data['canceled'] = $data['projects']->where('status', 'canceled')->count();
      $data['total'] = $data['not_started'] + $data['in_progress'] + $data['on_hold'] + $data['finished'] + $data['canceled'];

      // Appointment Booking
      $appointments = Appointment::where('user_id',$user_id)->get();
      $available_slots = AppointmentBooking::where('user_id',$user_id)->first();

      $appointment_array = [];
      foreach($appointments as $app){
        foreach ($app->appointment as  $value) {
          $temp = [];
          $temp['title'] = $value['title'];
          $temp['start'] = $value['start'];
          $temp['end'] = $value['end'];
          $temp['color'] = $value['color'];
          $temp['slot'] = $value['slot'];
          $temp['phone'] = $value['phone'];
          $temp['email'] = $value['email'];
          $temp['slug'] = $app->slug;
          $temp['status'] = $app->status;
        }
        if($app->booking_info != null){
          $temp['title'] = $app->booking_info['first_name'].' '.$app->booking_info['last_name'];
          $temp['email'] = $app->booking_info['email'];
          $temp['phone'] = $app->booking_info['phone'];
        }
        array_push($appointment_array,$temp);
      }
      //get project slots
      $all_project = [];
      foreach($project_slots as $project) {
        foreach ($project->slots as  $value) {
          array_push($all_project,$value);
        }
      }
      $data['events'] = array_merge(($available_slots->event_slot)?$available_slots->event_slot : [],$appointment_array,$all_project);
      $data['api_key'] = $available_slots->api_key;
      $data['calendar_id'] = $available_slots->calendar_id;
      $data['app_id'] = config('global.project_app_id');
      $data['is_puchase'] = $data['projects']->isEmpty() || DrmUserHasPurchasedApp(CRUDBooster::myParentId(), $data['app_id']) || CRUDBooster::isSuperAdmin();
      $data['current_lang'] = \Config::get('app.locale');
      $data['groups'] = DB::table('drm_project_groups')->where('user_id', CRUDBooster::myParentId())->get();
      //Create a view. Please use `cbView` method instead of view method from laravel.

      if(CRUDBooster::isSubUser()) {
        $data['permission'] = DB::table('sub_user_project_permissions')->where('user_id', CRUDBooster::myId())->get();
        $data['group_permission'] =   $data['permission']->pluck('group_id');
        $data['project_permission'] =   $data['permission']->where('group_id',null)->pluck('project_id');
        //dd($data['project_permission']);
        //$data['check'] = DB::table('sub_user_project_permissions')->where('user_id', CRUDBooster::myId())->pluck('group_id');
        //dd($data['permission']);
        $this->cbView('admin.drm_projects.sub_index', $data);
      }else{
        $this->cbView('admin.drm_projects.index', $data);
      }

    }

    public function getAdd()
    {

        if(!(CRUDBooster::isSuperadmin() || $this->app_purchased())) {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_projects'), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = __('Add Project Details');

        if (CRUDBooster::isSuperadmin()) {
            $data['customers'] = DB::table('drm_customers')->get();
        } else {
            $data['customers'] = DB::table('drm_customers')->where('user_id', CRUDBooster::myParentId())->get();
        }

        $data['members'] = DB::table('cms_users')->whereNotNull('email_verified_at')->get();
        $data['groups'] = DB::table('drm_project_groups')->where('user_id', CRUDBooster::myParentId())->get();

        // Please use cbView method instead view method from laravel
        $this->cbView('admin.drm_projects.add', $data);
    }

    public function postAddSave()
    {

        if(!(CRUDBooster::isSuperadmin() || $this->app_purchased())) {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_projects'), trans("crudbooster.denied_access"));
        }

      $validator = Validator::make($_REQUEST, [

        "title" => "required|string",
        // "drm_customer_id" => "required",
        "start_date" => "required|date",
      ]);


      if ($validator->fails()) {
        return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
        // return 0;
      }

     // generate appointment booking slot
      $startDate = new Carbon($_REQUEST['start_date']);
      $endDate = new Carbon($_REQUEST['due_date']) ?? $startDate;
      // $project_slots = array();
      // while ($startDate->lte($endDate)){
      //   $project_slots[] = [
      //     'groupId' => 'availableForMeeting',
      //     'start' =>  $startDate->toDateString().'T20:00:00',
      //     'end' =>  $startDate->toDateString().'T24:00:00',
      //     'date' => $startDate->toDateString(),
      //     'start_time' => '20:00',
      //     'end_time' => '24:00',
      //     'slot' => 'available',
      //     'display' => 'background',
      //     'color' => 'green'
      //   ];
      //   $startDate->addDay();
      // }





      $project_id = DB::table('drm_projects')->insertGetId([
        'title' => $_REQUEST['title'],
        'drm_customer_id' => $_REQUEST['drm_customer_id'],
        // 'tags' => $_REQUEST['tags'],
        'start_date' => $_REQUEST['start_date'],
        'due_date' => $_REQUEST['due_date'],
        // 'rate_type' => $_REQUEST['rate_type'],
        // 'total_cost' => $_REQUEST['total_cost'],
        'status' => $_REQUEST['status'],
        'description' => $_REQUEST['description'],
        'project_group_id' => $_REQUEST['groups'],
        'cms_user_id' => $_REQUEST['user_id'] ?? CRUDBooster::myParentId(),
      ]);

      $project_slots[] = [
        'title' => $_REQUEST['title'],
        'start' => $_REQUEST['due_date'],
        'slot' => 'available',
        'display' => 'background',
        'color' => '#fd6500'
      ];

      $start_time = Carbon::parse($_REQUEST['due_date'])->format('Y-m-d H:i:00');

        Appointment::Create(
        [
            'user_id' => CRUDBooster::myParentId(),
            'my_id' => CRUDBooster::myId(),
            'slug' => $project_id,
            'appointment' => $project_slots,

            'start_time' => $start_time,
            'end_time'   => $start_time,
            'start_at'   => $start_time,
            'end_at'     => $start_time,
        ]);

      // AppointmentProjectSlot::create(
      //   [
      //     'user_id' => CRUDBooster::myParentId(),
      //     'project_id' => $project_id,
      //     'slots' => $project_slots
      //   ]);


        DB::table('drm_project_members')->insert([
          'drm_project_id' => $project_id,
          'cms_user_id' => CRUDBooster::myParentId()
        ]);


        // add project mermbers
        if (isset($_REQUEST['drm_project_member_ids'])) {
            foreach ($_REQUEST['drm_project_member_ids'] as $member_id) {
                DB::table('drm_project_members')->insert([
                    'drm_project_id' => $project_id,
                    'cms_user_id' => $member_id
                ]);
                $message_title = 'You have been assign to an new project !';
                User::find($member_id)->notify(new DRMNotification($message_title, 'PROJECT_MANAGEMENT_MODULE', CRUDBooster::adminPath() . '/drm_projects/project/' . $project_id));
            }
        }

        return redirect('admin/drm_projects')->with('success', 'Project added');

    }

    // get projects details
    public function getProject($project_id)
    {
        if (!CRUDBooster::isSubUser() && !CRUDBooster::isRead() && $this->global_privilege == FALSE || $this->button_edit == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        //permission check for sub user
        if (CRUDBooster::isSubUser()) {
            $permission =  DB::table('sub_user_project_permissions')->where('user_id',CRUDBooster::myId())->get();
            $checkGroupPermission = $permission->where('project_id', null)->pluck('group_id');
            $checkProjectPermission = $permission->where('group_id', null)->pluck('project_id');

            $getProjectIds = DB::table('drm_project_members')->Join('drm_projects', 'drm_projects.id', '=', 'drm_project_members.drm_project_id')
            ->whereIn('drm_projects.project_group_id', $checkGroupPermission)
            ->where('drm_project_members.cms_user_id', CRUDBooster::myId())
            ->pluck('drm_projects.id');

            $checkPermission = array_merge($getProjectIds->toArray() ?? [],$checkProjectPermission->toArray() ?? []);

            if(!in_array($project_id,$checkPermission)){
                CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
            }
        }


        $data['project_id'] = $project_id;

        session(['project_id' => $project_id]);

        $data['project'] = $project = DB::table('drm_projects')->find($project_id);

        $data['page_title'] = "Project: " . $project->title;

        $members = DB::table("drm_project_members as pm")->join('cms_users as u', 'u.id', 'pm.cms_user_id')->select('pm.cms_user_id', 'u.name')->where('drm_project_id', $project_id)->get();

        foreach ($members as $item) {

            $users[] = $item->cms_user_id;

            if ($item->cms_user_id == CRUDBooster::myParentId()) {
                continue;
            }

            $users[] = $user["cms_user_id"] = $item->cms_user_id;
            $user["name"] = $item->name;
            $data["members"][] = (object)$user;
        }

        if ($users)
            if (!in_array(CRUDBooster::myParentId(), $users) && !in_array(CRUDBooster::myId(), $users)) {
                CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
            }

        // dd($data["members"]);
        $data['user_campaigns_tags'] = $this->getUserCampaignsTags(CRUDBooster::myId());
        // $this->cbView('admin.menudashboard.board_detail_view',$data);
        // $this->cbView('admin.drm_projects.project_detail',$data);
        $customer_list = DB::table('new_customers')->where('user_id', CRUDBooster::myParentId())->orderBy('full_name')->orderBy('email')->get();
        $data['customer_list'] = $customer_list;

        return view('admin.drm_projects.project_detail', $data);
    }

    private function getUserCampaignsTags($user_id = 0) {
        $user_campaigns_tags = DropfunnelTag::join('dropfunnel_customer_tags as drop_cus_tags', 'dropfunnel_tags.id', '=', 'drop_cus_tags.tag_id')
            ->join('new_customers as new_cus', 'drop_cus_tags.customer_id', '=', 'new_cus.id')
            ->where([
                'dropfunnel_tags.user_id'   => $user_id,
                'new_cus.user_id'           => $user_id,
                'drop_cus_tags.insert_type' => 5 // campaign
            ])
            ->whereNull('drop_cus_tags.deleted_at')
            ->whereNull('new_cus.deleted_at')
            ->orderBy('tag', 'asc')
            ->pluck('tag', 'dropfunnel_tags.id')
            ->toArray();

        return $user_campaigns_tags;
    }

    // get project edit
    public function getEdit($id)
    {
        //Create an Auth
        if (!CRUDBooster::isUpdate() && $this->global_privilege == FALSE || $this->button_edit == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = __('Edit Data');
        $data['project'] = DB::table('drm_projects')->where('id', $id)->first();
        // $data['users'] = DB::table('cms_users')->get();
        $data['subusers'] =  DB::table('cms_users')->where('parent_id',CRUDBooster::myParentId())->get();
        $data['permissionMenus'] = DB::table('drm_project_access_menus')->get();
        if (CRUDBooster::isSuperadmin()) {
            $data['customers'] = DB::table('drm_customers')->get();
        } else {
            $data['customers'] = DB::table('drm_customers')->where('user_id', CRUDBooster::myParentId())->get();
        }

        $data['members'] = DB::table('cms_users')->get();
        $data['project_members'] = DB::table('drm_project_members')->where('drm_project_id', $id)->get();
        $data['groups'] = DB::table('drm_project_groups')->where('user_id', CRUDBooster::myParentId())->get();
        session(['project_id' => $id]);

        //Please use cbView method instead view method from laravel
        // $this->cbView('admin.drm_projects.edit',$data);
        return view('admin.drm_projects.edit', $data);
    }

    // post -- project edit
    public function postEditSave($p_id)
    {
        $project_id = $p_id;

        $project = DB::table('drm_projects')->where('id', $project_id);
        if ($project->first() == null) {
            return redirect('admin/drm_projects')->with('error', 'Project id not found');
        }

        $project->update([
            'title' => $_REQUEST['title'],
            'drm_customer_id' => $_REQUEST['drm_customer_id'],
            // 'tags' => $_REQUEST['tags'],
            'start_date' => $_REQUEST['start_date'],
            'due_date' => $_REQUEST['due_date'],
            // 'rate_type' => $_REQUEST['rate_type'],
            // 'total_cost' => $_REQUEST['total_cost'],
            'status' => $_REQUEST['status'],
            'project_group_id' => $_REQUEST['groups'],
            'description' => $_REQUEST['description'],
        ]);


        // generate appointment booking slot
         // $startDate = new Carbon($_REQUEST['start_date']);
         // $endDate = new Carbon($_REQUEST['due_date']) ?? $startDate;
         // $project_slots = array();
         // while ($startDate->lte($endDate)){
         //   $project_slots[] = [
         //     'groupId' => 'availableForMeeting',
         //     'start' =>  $startDate->toDateString().'T20:00:00',
         //     'end' =>  $startDate->toDateString().'T24:00:00',
         //     'date' => $startDate->toDateString(),
         //     'start_time' => '20:00',
         //     'end_time' => '24:00',
         //     'slot' => 'available',
         //     'display' => 'background',
         //     'color' => 'green'
         //   ];
         //   $startDate->addDay();
         // }
         //
         // AppointmentProjectSlot::where('project_id',$project_id)->update(
         //   [
         //     'slots' => $project_slots
         //   ]);

         $project_slots[] = [
           'title' => $_REQUEST['title'],
           'start' => $_REQUEST['due_date'],
           'slot' => 'available',
           'display' => 'background',
           'color' => '#fd6500'
         ];

         $start_time = Carbon::parse($_REQUEST['due_date'])->format('Y-m-d H:i:00');

         Appointment::where('slug', $project_id)->where('user_id', CRUDBooster::myParentId())->update(
           [
             'appointment' => $project_slots,
             'start_time'  => $start_time,
             'end_time'    => $start_time,
             'start_at'    => $start_time,
             'end_at'      => $start_time,
           ]);


        // dd(($project->first())->id);
        if (CRUDBooster::isSuperadmin()){
            DB::table('drm_project_members')->where('drm_project_id', ($project->first())->id)->delete();

            DB::table('drm_project_members')->Insert([
                'cms_user_id' => ($project->first())->cms_user_id,
                'drm_project_id' => ($project->first())->id,
            ]);
        }

        if (isset($_REQUEST['drm_project_member_ids'])) {
            // $hasPermission = DB::table('notification_trigger')
            //                  ->where('hook', 'PROJECT_MANAGEMENT_MODULE')->where('status', 0)->first();
            foreach ($_REQUEST['drm_project_member_ids'] as $member_id) {
                DB::table('drm_project_members')->Insert([
                    'cms_user_id' => $member_id,
                    'drm_project_id' => ($project->first())->id,
                ]);
                $message_title = 'A update in project is available!';
                User::find($member_id)->notify(new DRMNotification($message_title, 'PROJECT_MANAGEMENT_MODULE', CRUDBooster::adminPath() . '/drm_projects/project/' . $project_id));
            }
        }
        if (isset($_REQUEST['sub_user'])) {
            DB::table('sub_user_project_permissions')->Insert([
                'user_id' => $_REQUEST['sub_user'],
                'project_id' => $project_id,
                'permission_menus'=>json_encode($_REQUEST['permission'])
            ]);
            DB::table('drm_project_members')->updateOrInsert(
                [
                'cms_user_id' => $_REQUEST['sub_user'],
                'drm_project_id' => $project_id,
                ],
                [
                'cms_user_id' => $_REQUEST['sub_user'],
                'drm_project_id' => $project_id,
            ]);
        }

        CRUDBooster::redirect(CRUDBooster::adminPath('drm_projects'), trans('Save seccessfull.'), 'success');
    }

    public function postCopyProject()
    {
        // return response()->json($_REQUEST);

        $project = DB::table('drm_projects')->find($_REQUEST['project_id']);

        $data['project_copyed_id'] = $project_id = DB::table('drm_projects')->insertGetId([
            'title' => $_REQUEST['project_name'],
            'start_date' => $project->start_date,
            'due_date' => $project->due_date,
            'status' => $project->status,
            'description' => $project->description,
            'cms_user_id' => CRUDBooster::myParentId(),
        ]);

        DB::table('drm_project_members')->insert([
            'drm_project_id' => $project_id,
            'cms_user_id' => CRUDBooster::myParentId()
        ]);


        DB::table('drm_project_cards')->where('drm_project_id', $project->id)->orderBy('id')->each(function ($card) use ($project_id) {

            $card_id = DB::table('drm_project_cards')->insertGetId([
                'title' => $card->title,
                'position' => $card->position,
                'drm_project_id' => $project_id,
            ]);

            $_GET['count_card'] = 0;
            if ($_REQUEST['keep_task'] == 'on')
                DB::table('drm_project_tasks')->where('drm_project_card_id', $card->id)->orderBy('id')->each(function ($task) use ($card_id) {

                    // add task
                    $cover_image = null;

                    if ($task->cover_image && realpath('./storage/list_cover/' . $task->cover_image)) {
                        $ext = end(explode(".", $task->cover_image));
                        $cover_image = 'tc' . (++$_GET['count_card']) . time() . '.' . $ext;
                        $data['copyed'][] = copy(realpath('./storage/list_cover/' . $task->cover_image), './storage/list_cover/' . $cover_image);

                    }

                    $task_id = DB::table('drm_project_tasks')->insertGetId([
                        'drm_project_card_id' => $card_id,

                        'name' => $task->name,
                        'title' => $task->title,
                        'description' => $task->description,
                        'start_date' => $task->start_date,
                        'due_date' => $task->due_date,
                        'priority' => $task->priority,
                        'status' => $task->status,
                        'position' => $task->position,
                        'cover_image' => $cover_image,
                    ]);

                    foreach (DB::table('drm_task_checklist')->where('task_id', $task->id)->get() as $checklist) {

                        $image_name = null;
                        if ($checklist->image && realpath('./storage/checklist_images/' . $checklist->image)) {
                            $ext = end(explode(".", $checklist->image));
                            $image_name = 'cl' . (++$_GET['count_card']) . time() . '.' . $ext;
                            $data['name'][] = $image_name;
                            $data['copyed'][] = copy(realpath('./storage/checklist_images/' . $checklist->image), './storage/checklist_images/' . $image_name);
                        }

                        $checklist_id = DB::table('drm_task_checklist')->insertGetId([
                            'task_id' => $task_id,
                            'title' => $checklist->title,
                            'status' => $checklist->status,
                            'image' => $image_name,
                            'cms_user_id' => CRUDBooster::myParentId(),
                        ]);
                    }

                });


        });

        return response()->json($data);
    }

    // ----------------- card -----------------------
    // add new card ajax
    public function postAddNewCard()
    {
        $card_id = DB::table('drm_project_cards')->insertGetId([
            'title' => $_REQUEST['title'],
            'position' => $_REQUEST['position'],
            'drm_project_id' => $_REQUEST['project_id'],
        ]);

        return response()->json($card_id);
    }

    // update card position
    public function postUpdateCardPosition()
    {
        // $_REQUEST['card_position'][0]["id"]
        foreach ($_REQUEST['card_position'] as $key => $card) {
            DB::table('drm_project_cards')->where("id", $card["id"])
                ->update(['position' => $card["position"]]);
        }
        return response()->json("updated");
    }

    // get all records
    public function getGetAllCards()
    {
        // return response()->json($_REQUEST);

        $cards = DB::table('drm_project_cards')
                ->leftJoin('dropfunnel_tags','drm_project_cards.tag_id','=','dropfunnel_tags.id')
                ->where('drm_project_cards.drm_project_id', $_REQUEST['project_id'])
                ->orderBy('drm_project_cards.position')->select('drm_project_cards.*','dropfunnel_tags.tag')->get();

        $data = [];

        foreach ($cards as $card) {
            // $lists = DB::table('drm_project_card_lists')->where('drm_project_card_id',$card->id)->orderBy('position')->orderBy('created_at','DESC')->get();
            $tasks = DB::table('drm_project_tasks')->where('drm_project_card_id', $card->id)->orderBy('position')->orderBy('created_at', 'DESC')->get();
            // dd($tasks);


            foreach ($tasks as $task) {

                // $task = DB::table('drm_project_tasks')->where('drm_card_list_id', $list->id)->first();

                $checklist = DB::table('drm_task_checklist')->where('task_id', $task->id);
                $task->total_checklist = $checklist->count();
                $task->done_checklist = $checklist->where('drm_task_checklist.status', 'true')->count();

                $task->comment_count = DB::table('drm_task_comments')->where('task_id', $task->id)->count();
            }

            $priorities = DB::table('drm_task_priorities')->get();

            $data[] = [
                "card" => $card,
                "tasks" => $tasks,
                "priorities"=>$priorities,
            ];
        }

        return response()->json($data);
    }

    public function postAssignCustomer(){

        $card_id = DB::table('drm_project_tasks')->where('id',request()->task_id)->first()->drm_project_card_id;

        $tag = DB::table('drm_project_cards')->where('id',$card_id)->first()->tag_id;


            DB::table('drm_project_tasks')->where('id',request()->task_id)->update([
                'customer_id' => request()->customer_id,
            ]);

            if ($tag) {

            $title = DB::table('dropfunnel_tags')->where('id',$tag)->first()->tag;
            // DB::table('dropfunnel_customer_tags')->insertOrUpdate(['tag_id'=> $tag , 'customer_id' => request()->customer_id],[])
            $res = DropfunnelCustomerTag::insertTag($title, CRUDBooster::myParentId(), request()->customer_id, 20);
            }

            return response()->json([
                'success' => true,
                'message'  => 'Customer assigned successfully'
            ]);

        // return response()->json([
        //     'success' => false,
        //     'message'  => 'Tag is not Valid'
        // ]);
    }
    // /edit-card-name
    public function postEditCardName()
    {
        // return $_REQUEST;

        $data['changed'] = DB::table('drm_project_cards')->where('id', $_REQUEST['card_id'])->update([
            'title' => $_REQUEST['card_title']
        ]);
        return response()->json($data);
    }


    public function postCopyCard()
    {
        // return $_REQUEST;
        // $card = DB::table('drm_projects')->find($_REQUEST['card_id']);
        // dd($project);


        $data['card_id'] = $card_id = DB::table('drm_project_cards')->insertGetId([
            'title' => DB::table('drm_project_cards')->find($_REQUEST['card_id'])->title,
            'position' => DB::table('drm_project_cards')->where('drm_project_id', $_REQUEST['project_id'])->count() + 1,
            'drm_project_id' => $_REQUEST['project_id'],
        ]);

        $_GET['count_card'] = 0;
        DB::table('drm_project_tasks')->where('drm_project_card_id', $_REQUEST['card_id'])->orderBy('id')->each(function ($task) use ($card_id) {
            // $list_id = DB::table('drm_project_card_lists')->insertGetId([
            //     'title' => $list->title,
            //     'position' => $list->position,
            //     'drm_project_card_id' => $card_id,
            // ]);

            // $task = DB::table('drm_project_tasks')->where('drm_project_card_id',$card_id)->first();

            // add task
            $cover_image = null;

            if ($task->cover_image && realpath('./storage/list_cover/' . $task->cover_image)) {
                $ext = end(explode(".", $task->cover_image));
                $cover_image = 'tc' . (++$_GET['count_card']) . time() . '.' . $ext;
                $data['copyed'][] = copy(realpath('./storage/list_cover/' . $task->cover_image), './storage/list_cover/' . $cover_image);

            }

            $task_id = DB::table('drm_project_tasks')->insertGetId([

                'drm_project_card_id' => $card_id,

                'name' => $task->name,
                'title' => $task->title,
                'description' => $task->description,
                'start_date' => $task->start_date,
                'due_date' => $task->due_date,
                'priority' => $task->priority,
                'status' => $task->status,
                'position' => $task->position,
                'cover_image' => $cover_image,
            ]);

            foreach (DB::table('drm_task_checklist')->where('task_id', $task->id)->get() as $checklist) {

                $image_name = null;
                if ($checklist->image && realpath('./storage/checklist_images/' . $checklist->image)) {
                    $ext = end(explode(".", $checklist->image));
                    $image_name = 'cl' . (++$_GET['count_card']) . time() . '.' . $ext;
                    $data['name'][] = $image_name;
                    $data['copyed'][] = copy(realpath('./storage/checklist_images/' . $checklist->image), './storage/checklist_images/' . $image_name);
                }

                $checklist_id = DB::table('drm_task_checklist')->insertGetId([
                    'task_id' => $task_id,
                    'title' => $checklist->title,
                    'status' => $checklist->status,
                    'image' => $image_name,
                    'cms_user_id' => CRUDBooster::myParentId(),
                ]);
            }

        });

        return response()->json($data);
    }

    // ------------- edit delete card ----------------
    public function postEditDeleteCard()
    {
        // return response()->json($_REQUEST["card_id"]);

        if ($_REQUEST["card_id"]) {
            $data["card_deleted"] = DB::table('drm_project_cards')->where('id', $_REQUEST["card_id"])->delete();
        }

        return response()->json($data);

    }

    // ----------------------------- tasks ------------------------//

    public function postAddNewTask()
    {
        // return $_REQUEST;
        // add task
        $task_id = DB::table('drm_project_tasks')->insertGetId([
            'name' => $_REQUEST['task_name'],
            'drm_project_id' => $_REQUEST['project_id'],
            'position' => DB::table('drm_project_tasks')->where('drm_project_card_id', $_REQUEST['card_id'])->count() + 1,
            'drm_project_card_id' => $_REQUEST['card_id'],
        ]);

        $task['id'] = $task_id;
        $task['name'] = $_REQUEST['task_name'];
        $task['position'] = DB::table('drm_project_tasks')->where('drm_project_card_id', $_REQUEST['card_id'])->count() + 1;
        // $data['task']->id = $task_id;
        // $data['task']->position = DB::table('drm_project_tasks')->where('drm_project_card_id',$_REQUEST['card_id'])->count()+1;
        $data['task'] = $task;
        $data['priorities'] = DB::table('drm_task_priorities')->get();
        $data['comment_count'] = DB::table('drm_task_comments')->where('task_id', $task_id)->count();
        $checklist = DB::table('drm_task_checklist')->where('task_id', $task_id);
        $data['total_checklist'] = $checklist->count();
        $data['done_checklist'] = $checklist->where('drm_task_checklist.status', 'true')->count();
        //dd()
        return response()->json($data);
    }

    // get task details
    public function getTaskDetails()
    {
        $data['task'] = DB::table('drm_project_tasks')->find($_REQUEST['task_id']);

        $data['tags'] = DB::table('drm_task_tags')->where('user_id', CRUDBooster::myParentId())->get();

        $data['cardposition'] = DB::table('drm_project_cards')->find($data['task']->drm_project_card_id);
        //dd( $data['cardposition']->position);

        $data['checklist'] = DB::table('drm_task_checklist')
            ->where('task_id', $data['task']->id)
            ->orderBy('position', 'asc')
            ->get();
        foreach ($data['checklist'] as $item) {
            $item->user_name = (DB::table('cms_users')->find($item->cms_user_id))->name;
        }

        $comments = DB::table('drm_task_comments')->where('task_id', $data['task']->id)->orderBy('created_at')->get();

        foreach ($comments as $key => $value) {

            $value->user_name = (DB::table('cms_users')->find($value->cms_user_id))->name;

            $data['comment'][] = $value;

        }
        return response()->json($data);
    }

    // post save task
    public function postSaveTask()
    {
        $data['request'] = $_REQUEST;

        $data['updated'] = DB::table('drm_project_tasks')->where('id', $_REQUEST['task_id'])->update([
            'title' => $_REQUEST['title'],
            'description' => $_REQUEST['description'],
            'start_date' => !empty($_REQUEST['start_date']) ? Carbon::parse($_REQUEST['start_date']) : null,
            'due_date' => !empty($_REQUEST['due_date']) ? Carbon::parse($_REQUEST['due_date']) : null,
            'priority' => $_REQUEST['priority'],
            'status' => $_REQUEST['status'],
        ]);
        $task_info = DB::table('drm_project_tasks')->where('id',$_REQUEST['task_id'])->select('name','description','cover_image')->first();
        $task_checklist = DB::table('drm_task_checklist')->where('id',$_REQUEST['task_id'])->select('title','image')->first();
        $taskeEvent[] = [
            'title' => $task_info->name,
            'start' => $_REQUEST['due_date'],
            'description' => $task_info->description,
            'constraint' => "availableForMeeting",
            'slot' => "task",
            'tast_id' => $_REQUEST['task_id'],
            'type' => 6,    //6 for task slot
            'display' => 'background',
            'start_date' => $_REQUEST['start_date'],
            'end_date' => $_REQUEST['due_date'],
            'task_title' => $task_checklist->title,
            'image' => $task_info->cover_image,
            'color' => '#fd6500'
        ];

          Appointment::updateOrCreate(
            ['slug' =>  $_REQUEST['task_id']],
            [
                'user_id' => CRUDBooster::myParentId(),
                'my_id' => CRUDBooster::myId(),
                'appointment' => $taskeEvent,
            ]);

        return response()->json($data);
    }

    public function postEditDeleteTask()
    {
        // return response()->json($_REQUEST);

        if ($_REQUEST['task_id'] && $_REQUEST['option'] == 'delete') {
            // deletting checklist
            DB::table('drm_task_checklist')->where('task_id', $_REQUEST['task_id'])->orderBy('id')->each(function ($item) {
                if ($item->image) {
                    unlink(realpath('storage/checklist_images/' . $item->image));
                }
            });

            $data['deleted_checklist'] = DB::table('drm_task_checklist')->where('task_id', $_REQUEST['task_id'])->delete();

            // deleting comments
            $data['deleted_comments'] = DB::table('drm_task_comments')->where('task_id', $_REQUEST['task_id'])->delete();

            // deleting task
            $data['deleted_task'] = DB::table('drm_project_tasks')->where('id', $_REQUEST['task_id'])->delete();

             //delete task event from calander
             Appointment::where('slug', $_REQUEST['task_id'])->delete();

        }

        return response()->json($data);
    }

    public function postAddTaskCover(LaravelRequest $request)
    {
        Validator::make($request->all(),[
            'task_cover' => 'required|image'
        ])->validate();

        try {
            $name = null;
            if ($request->hasFile('task_cover')) {
                $name = uploadImage($request->file('task_cover'),'task_cover_image');
            }
            $task = ProjectTasks::find($request->task_id); // ->update(['cover_image' => $name]);

            if (!empty($task)) {
                $task->cover_image = $name;
                $task->save();
                return response()->json(['success' => true, 'message' => 'Cover Image Successfully Uploaded', 'url' => $task->cover_image]);
            }
            $url = $task->cover_image;
            if (!strpos($url,'drm-file')) {
                $url = asset('storage/list_cover/').$task->cover_image;
            }
            return response()->json(['success'=> false,'message' => 'Failed to Upload','url' => $url]);
        } catch (\Exception $exception) {
            return response()->json(['success'=> false,'message' => $exception->getMessage()],400);
        }



//        dd('no ok');
//        // return response()->json($_POST);
//        if ($_FILES['task_cover']['name'] == null) {
//            return response()->json('Picture not found', 500);
//        }

//        $ext = end(explode(".", $_FILES['task_cover']['name']));
//        $name = 'tc' . time() . '.' . $ext;
//        if (!Storage::exists('public/list_cover')) {
//            Storage::makeDirectory('public/list_cover');
//        }
//        if (!move_uploaded_file($_FILES['task_cover']['tmp_name'], './storage/list_cover/' . $name)) {
//            return response()->json('File not moved', 500);
//        }
//        $data['cover_image'] = $name;
//        return response()->json($data);
    }


    public function getModalDismisRefresh()
    {
        // return response()->json($_REQUEST);

        // $data['cover_image'] = DB::table('drm_project_tasks')->find($_REQUEST['task_id'])->cover_image;

        $data['task'] = $task = DB::table('drm_project_tasks')->find($_REQUEST['task_id']);

        $checklist = DB::table('drm_task_checklist')->where('task_id', $task->id);
        $data['total'] = $checklist->count();
        $data['done'] = $checklist->where('drm_task_checklist.status', 'true')->count();

        $data['comment_count'] = DB::table('drm_task_comments')->where('task_id', $task->id)->count();

        return response()->json($data);
    }

    public function postUpdateTaskPosition()
    {
        foreach ((object)$_REQUEST['task_position'] as $item) {
            DB::table('drm_project_tasks')->where("id", $item['task_id'])
                ->update([
                    'position' => $item["position"],
                    'drm_project_card_id' => $item["card_id"],
                ]);

                // activity track
                $data = [];
                $data['description'] = 'Changed Status';
                $data['activity'] = 'Changed_Status';
                $data['user_id'] = CRUDBooster::myParentId();
                $data['task_id'] = $item['task_id'];
                TaskActivity::insert($data);
        }

        return $_REQUEST['task_position'];
    }

    public function postAddCardTagToCustomer()
    {
        $customer_id = DB::table('drm_project_tasks')->where('id', $_REQUEST['task_id'])->value('customer_id');
        if ($customer_id) {
            
            $tag = DB::table('drm_project_cards')->where('id', $_REQUEST["card_id"])->value('tag_id');
            if ($tag) {
                $title = DB::table('dropfunnel_tags')->where('id', $tag)->value('tag');
                // \Log::info($tag . ' ' . $title);
                $res = DropfunnelCustomerTag::insertTag($title, CRUDBooster::myParentId(), $customer_id, 20);
            }
        }
    }

    public function postCopyTask()
    {
        // return $_REQUEST;

        $task = DB::table('drm_project_tasks')->find($_REQUEST['task_id']);
        // add task
        $count = 0;
        $cover_image = null;
        if ($task->cover_image && realpath('./storage/list_cover/' . $task->cover_image)) {
            $ext = end(explode(".", $task->cover_image));
            $cover_image = 'tc' . (++$count) . time() . '.' . $ext;
            $data['copyed'][] = copy(realpath('./storage/list_cover/' . $task->cover_image), './storage/list_cover/' . $cover_image);

        }


        $task_id = DB::table('drm_project_tasks')->insertGetId([
            'drm_project_card_id' => $_REQUEST['card_id'],

            'name' => $task->name,
            'title' => $task->title,
            'description' => $task->description,
            'start_date' => $task->start_date,
            'due_date' => $task->due_date,
            'priority' => $task->priority,
            'status' => $task->status,
            'position' => $task->position,
            'cover_image' => $cover_image,
        ]);


        //copy all check list
        foreach ((object)DB::table('drm_task_checklist')->where('task_id', $_REQUEST['task_id'])->get() as $item) {

            $image_name = null;
            if ($item->image && realpath('./storage/checklist_images/' . $item->image)) {
                $ext = end(explode(".", $item->image));
                $image_name = 'cl' . (++$count) . time() . '.' . $ext;
                $data['copyed'][] = copy(realpath('./storage/checklist_images/' . $item->image), './storage/checklist_images/' . $image_name);

            }

            $checklist_id = DB::table('drm_task_checklist')->insertGetId([
                'task_id' => $task_id,
                'title' => $item->title,
                'status' => $item->status,
                'image' => $image_name,
                'cms_user_id' => CRUDBooster::myParentId(),
            ]);

        }

        return response()->json($data, 200);

    }

    // insert-task-id-name
    public function getInsertTaskIdName()
    {
        // $_GET['count'] = 0;
        // DB::table('drm_project_card_lists')->orderBy('id')->each(function ($list) {

        //     DB::table('drm_project_tasks')->where('drm_card_list_id', $list->id)->update([
        //         'name' => $list->title,
        //         'drm_project_card_id' => $list->drm_project_card_id,
        //         'position' => $list->position,
        //         'cover_image' => $list->image
        //     ]);

        //     $_GET['count'] = $_GET['count'] + 1;
        //     echo $_GET['count'] . "<br> \n";
        // });

    }

    // -------------------- check list ---------------- //
    public function postSaveChecklist()
    {
        if ($_REQUEST['task_id'] && $_REQUEST['title']) {
            $checklist_id = DB::table('drm_task_checklist')->insertGetId([
                'task_id' => $_REQUEST['task_id'],
                'title' => $_REQUEST['title'],
                'status' => "false",
                'cms_user_id' => CRUDBooster::myParentId(),
            ]);
            $checklist = DB::table('drm_task_checklist')->where('task_id', '=', $_REQUEST['task_id'])->orderBy('position', 'desc')->get();
            if ($checklist->isNotEmpty()) {
                $checklist = $checklist->first();
                DB::table('drm_task_checklist')
                    ->where('id', '=', $checklist_id)
                    ->update(['position' => $checklist->position + 1]);
            } else {
                DB::table('drm_task_checklist')
                    ->where('id', '=', $checklist_id)
                    ->update(['position' => 1]);
            }
        } else if ($_REQUEST['checklist_id'] && $_FILES['checklist_image']['name']) {
            // return response()->json($_REQUEST);
            // return response()->json($_FILES);

            if ($_FILES['checklist_image']['name'] == null) {
                return response()->json('Picture not found', 500);
            }

            $ext = end(explode(".", $_FILES['checklist_image']['name']));
            $name = 'cl' . time() . '.' . $ext;
            if (!Storage::exists('public/checklist_images')) {
                Storage::makeDirectory('public/checklist_images');
            }
            if (!move_uploaded_file($_FILES['checklist_image']['tmp_name'], './storage/checklist_images/' . $name)) {
                return response()->json('File not moved', 500);
            }

            DB::table('drm_task_checklist')->where('id', $_REQUEST['checklist_id'])->update(['image' => $name]);
        } else {
            return response()->json('Invalid request', 500);
        }

        $data['checklist']['id'] = $checklist_id ?? $_REQUEST['checklist_id'];
        $data['checklist']['image'] = $name;
        $data['checklist']['user_name'] = DB::table('cms_users')->find(DB::table('drm_task_checklist')->find($data['checklist']['id'])->cms_user_id)->name;

        return response()->json($data);
    }

    /* check-a-list */
    public function postCheckAList()
    {
        DB::table('drm_task_checklist')->where([
            'id' => $_REQUEST['checklist_id'],
        ])->update([
            'status' => $_REQUEST['status'],
        ]);

        return response()->json($_REQUEST);
    }

    // --------------------- edit delete checklist ----------
    // edit-delete-checklist
    public function postEditDeleteChecklist()
    {
        // return response()->json($_REQUEST);
        // edit
        if ($_REQUEST['checklist_id'] && $_REQUEST['title']) {

        } // delete
        else if ($_REQUEST['checklist_id']) {
            $data['deleted'] = DB::table('drm_task_checklist')->where('id', $_REQUEST['checklist_id'])->delete();
        }

        return response()->json($data);
    }

    // -------------------------- Comments ----------------------------- //

    // add-comment
    public function postAddComment(LaravelRequest $request)
    {
        if ($_POST['task_id'] != "" || $_POST['comment']) {
            $have = [];
            $will_be = [];
            $regex = '~(@\w+)~';
            // $comment = $_POST['comment'];
            $comment = Purifier::clean($_POST['comment']);
            $mention_user_id = $_POST['mention_user_id'] ? json_decode($_POST['mention_user_id']) : collect();
            $user_ids = [];
            if (preg_match_all($regex, $comment, $matches)) {
                foreach ($matches[0] as $key => $word) {
                    if (!in_array($word, $have)) {
                        $have[] = $word;
                        $will_be[] = '<b>' . $word . '</b>';
                    }
                }
            }

            foreach ($mention_user_id as $key => $value) {
                if (array_search($value, $have) !== false) {
                    array_push($user_ids, $key);
                }
            }
            $replacedComment = str_replace($have, $will_be, $comment);

            $coment_id = DB::table('drm_task_comments')->insertGetId([
                'task_id' => $_POST['task_id'],
                'comment' => makeUtf8($replacedComment),
                'cms_user_id' => CRUDBooster::myParentId(),
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            if ($request->hasFile('audio_data')) {
                $file = $request->file('audio_data');
                $name = $file->hashName();
                Storage::disk('spaces')->put($name, file_get_contents($file), 'public'); // store into digital ocean
                $fullPath = (Storage::disk('spaces')->exists($name)) ? Storage::disk('spaces')->url($name) : null;

                DB::table('drm_task_comments')
                    ->where('id', '=', $coment_id)
                    ->update(['audio_file' => $fullPath]);
            }

            $task = DB::table('drm_project_tasks')->find($_POST['task_id']);
            $message_title = 'You have been mentioned in ' . $task->name . ' Task';
            $users = User::whereIn('id', $user_ids)->get();
            $notificationUrl = url('/admin/drm_projects/project') . '/' . $_POST['project_id'] . '?task_id=' . $task->id;
            foreach ($users as $user) {
                $user->notify(new DRMNotification($message_title, 'PROJECT_MANAGEMENT_MODULE', $notificationUrl));
            }
        } else if ($_POST['comment_id'] != "" || isset($_FILES["comment_files"])) {
            // return response()->json($_REQUEST);
            // return response()->json($_FILES);
            // $file = Request::file('comment_files');
            // $file_type = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
            // $rand=Str::random(40);
            // $path = $file->storeAs('public/csv_files',$rand.'.'.$file_type,['visibility'=>'public','disk'=>'local']);
            $coment_id = $_POST['comment_id'];

            if ($_FILES['comment_files']['name'] == null) {
                return response()->json('Picture not found', 500);
            }

            $ext = end(explode(".", $_FILES['comment_files']['name']));
            $name = 'cfs' . $_POST["file_no"] . time() . '.' . $ext;
            if (!Storage::exists('public/comment_files')) {
                Storage::makeDirectory('public/comment_files');
            }
            if (!move_uploaded_file($_FILES['comment_files']['tmp_name'], './storage/comment_files/' . $name)) {
                return response()->json('File not moved', 500);
            }

            // $files = [];
            $files = json_decode(DB::table('drm_task_comments')->where('id', $coment_id)->first()->files, true);
            $files['file' . $_POST["file_no"]] = $name;

            DB::table('drm_task_comments')->where('id', $coment_id)->update([
                // 'file'.$_POST["file_no"] => $name,
                'files' => json_encode($files),
            ]);
        } else {
            return response()->json('Nothing defined', 500);
        }

        $data['comment'] = DB::table('drm_task_comments')->find($coment_id);
        $data['comment']->user_name = (DB::table('cms_users')->find($data['comment']->cms_user_id))->name;
        $data['comment']->file_name = $name;

        //dd($data['comment']->comment);
        //activity track
        $datas=[];
        $datas['description'] = $data['comment']->comment;
        $datas['activity'] = 'add_comment';
        $datas['user_id'] = CRUDBooster::myParentId();
        $datas['task_id'] = request()->task_id;

        $datas['projectId'] = $_REQUEST['project_id'];
        $datas['message'] = 'Added New Comment';
        TaskActivity::insert($datas);
        TaskActivity::notify($datas);

        // $data['comment_id'] = $coment_id;
        return response()->json($data);
    }

    public function getEditComment()
    {
        $data["comment"] = DB::table('drm_task_comments')->find($_REQUEST['comment_id']);
        return response()->json($data);
    }

    // delete comment file
    public function postDeleteCommentFile()
    {
        $files_str = DB::table('drm_task_comments')->find($_REQUEST['comment_id'])->files;
        $files = json_decode($files_str, true);

        unset($files[array_search($_REQUEST['file_name'], $files)]);

        $i = 1;
        foreach ($files as $file) {
            $new_files['file' . $i++] = $file;
        }

        DB::table('drm_task_comments')->where('id', $_REQUEST['comment_id'])->update([
            'files' => json_encode($new_files)
        ]);

        $data['comment']['files'] = $new_files;
        $data['comment']['id'] = $_REQUEST['comment_id'];

        return response()->json($data);
    }

    // ------- edit detete comment
    public function postEditDeleteComment()
    {
        // return response()->json($_REQUEST);

        // edit comment
        if ($_REQUEST['comment_id'] && $_REQUEST['comment']) {
            $have = [];
            $will_be = [];
            $regex = '~(@\w+)~';
            // $comment = $_REQUEST['comment'];
            $comment = Purifier::clean($_REQUEST['comment']);;

            if (preg_match_all($regex, $comment, $matches)) {
                foreach ($matches[0] as $word) {
                    if (!in_array($word, $have)) {
                        $have[] = $word;
                        $will_be[] = '<b>' . $word . '</b>';
                    }
                }
            }
            $replacedComment = str_replace($have, $will_be, $comment);

            $data["edited"] = DB::table('drm_task_comments')->where('id', $_REQUEST['comment_id'])->update([
                'comment' => makeUtf8($replacedComment),
            ]);
            $data['comment'] = makeUtf8($replacedComment);
        } else if ($_REQUEST['comment_id'] && isset($_FILES["comment_files"])) {
            // return response()->json($_REQUEST);
            // return response()->json($_FILES);
            $coment_id = $_POST['comment_id'];

            if ($_FILES['comment_files']['name'] == null) {
                return response()->json('Picture not found', 500);
            }

            $ext = end(explode(".", $_FILES['comment_files']['name']));
            $name = 'cfs' . $_POST["file_no"] . time() . '.' . $ext;

            if (!Storage::exists('public/comment_files')) {
                Storage::makeDirectory('public/comment_files');
            }
            if (!move_uploaded_file($_FILES['comment_files']['tmp_name'], './storage/comment_files/' . $name)) {
                return response()->json('File not moved', 500);
            }

            // $files = [];
            $files = json_decode(DB::table('drm_task_comments')->where('id', $coment_id)->first()->files, true);
            $files['file' . $_POST["file_no"]] = $name;

            $data["updated"] = DB::table('drm_task_comments')->where('id', $coment_id)->update([
                // 'file'.$_POST["file_no"] => $name,
                'files' => json_encode($files),
            ]);

            $data['comment']['files'] = $files;
            $data['comment']['id'] = $coment_id;

        } // delete comment
        else if ($_REQUEST['comment_id']) {
            $data["delected"] = DB::table('drm_task_comments')->where('id', $_REQUEST['comment_id'])->delete();

        }
        // delete comment
        return response()->json($data);
    }

    public function postSendInvitation()
    {

        $data['have_account'] = DB::table('cms_users')->where('email', $_REQUEST['shared_email'])->first();

        // dd($_REQUEST['shared_email']);
        $key = \Illuminate\Support\Str::random(16);
        // dd($_REQUEST);

        DB::table('drm_projects')->where('id', $_REQUEST['project_id'])->update([
            'shared_key' => $key,
        ]);

        $data = [];
        $data['page_title'] = 'Project Invitation';
        $data['project'] = DB::table('drm_projects')->where('id', $_REQUEST['project_id'])->first();
        $data['user'] = DB::table('cms_users')->find(CRUDBooster::myParentId());

        $url = CRUDBooster::mainPath('accept-invitation/' . $data['project']->id . '/' . $data['project']->shared_key . '/' . CRUDBooster::myParentId());
        $project_name = $data['project']->title;


        // return view('admin.drm_projects.invitation_mail',$data);

        if (!(filter_var($_REQUEST['shared_email'], FILTER_VALIDATE_EMAIL))) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), "Email is not valid", "error");
        }

        $user = \App\User::where('email', $_REQUEST['shared_email'])->first();
        if (!empty($user)) {
            $user->notify(new DRMNotification('Invitation Send. Project Shared. Project name: ' . $project_name, 'PROJECT_SHARE', $url));

        }

        // if (!empty($data['user'])) {
        // 	\App\User::find($data['user']->id)->notify(new DRMNotification('Invitation Send. Project Shared.','PROJECT_SHARE'));
        // }
        $this->sendInvitationMail($data, $_REQUEST['shared_email']);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), "Invitation Send. Project Shared.", "success");
    }

    public function getAcceptInvitation($project_id, $key, $user_id)
    {
        $project = DB::table('drm_projects')->find($project_id);

        if ($project->cms_user_id != $user_id || $project->shared_key == null) {
            CRUDBooster::redirect(CRUDBooster::mainPath(''), "Link expired");
        }

        $user_found = DB::table('drm_project_members')->where([
            'cms_user_id' => CRUDBooster::myParentId(),
            'drm_project_id' => $project->id,
        ])->first();

        if ($user_found) {
            DB::table('drm_projects')->where('id', $project_id)->update([
                'shared_key' => null,
            ]);

            CRUDBooster::redirect(CRUDBooster::adminPath('drm_projects'), "Project already joined.");
            return "Project already joined.";
        }

        DB::table('drm_project_members')->Insert([
            'cms_user_id' => CRUDBooster::myParentId(),
            'drm_project_id' => $project->id,
        ]);

        DB::table('drm_projects')->where('id', $project_id)->update([
            'shared_key' => null,
        ]);
        $message_title = 'Your request have been accepted.';
        User::find($user_id)->notify(new DRMNotification($message_title, 'PROJECT_MANAGEMENT_MODULE', CRUDBooster::adminPath() . '/drm_projects/project/' . $project_id));
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_projects'), "Project joined.");

    }

    private function sendInvitationMail($data, $target_email)
    {
        //Use in method
        $has_user = ($data['have_account']->id) ? true : false;

        $tags = [
            'user_name' => $data['user']->name,
            'project_accept_url' => CRUDBooster::mainPath('accept-invitation/' . $data['project']->id . '/' . $data['project']->shared_key . '/' . CRUDBooster::myParentId()),
            'project_title' => $data['project']->title,
            'USER' => $has_user,
            'GUEST' => !$has_user,
        ];

        $slug = 'drm_project_invitation'; //Page slugdrm_project_invitation
        $lang = getUserSavedLang($target_email);

        $mail_data = DRMParseMailTemplate($tags, $slug, $lang); //Generated html
        app('drm.mailer')->getMailer()->to($target_email)->send(new DRMSEndMail($mail_data)); //Send
    }

    public function app_purchased()
    {

        return true;
        $app_id = config('global.project_app_id');

        $purchased =  DrmUserHasPurchasedApp(CRUDBooster::myParentId(), $app_id);
        $projects = DB::table('drm_projects')->where('cms_user_id', CRUDBooster::myParentId())->count();

        return ($purchased || ($projects < 1));
    }

    public function getDetail($id)
    {
        $this->cbLoader();
        if (!CRUDBooster::isCreate() && $this->global_privilege == false || $this->button_add == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
        $data = [];
        $data['page_title'] = 'Detail Email marketing';
        $data['projects'] = DB::table('drm_projects')
            ->select('drm_projects.*', 'users.name as created_by')
            ->join('cms_users as users', 'users.id', '=', 'drm_projects.cms_user_id')
            ->where('drm_projects.id', $id)
            ->first();

        return view('admin.drm_projects.details', $data);
    }

    public function postSortUpdate()
    {

//        return response()->json([
//            'success' => true,
//            'message' => 'Update success!',
//        ]);
        try {
            $data = DB::table('drm_projects')
                ->where('id', $_REQUEST['id'])
                ->update(['sort' => $_REQUEST['position']]);
            if (is_null($data)) throw new \Exception("Something went wrong!");
            return response()->json([
                'success' => true,
                'message' => 'Update success!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function postRatingUpdate()
    {
        DB::table('drm_projects')->where('id',request()->id)->update([
            'star_rating' => request()->rating
        ]);

        return response()->json("Rating Updated");

    }

    public function postOpenTaskList()
    {
        $cardIds = DB::table('drm_project_cards')->where('drm_project_id',request()->id)->pluck('id');
        $projects = DB::table('drm_project_tasks')
                    ->whereIn('drm_project_card_id', $cardIds)
                    ->get();

        return response()->json($projects);

    }

    public function postOpenSelectedProject()
    {
        $project = DB::table('drm_projects')->find(request()->id);

        return response()->json($project);

    }

    public function postSaveTimer()
    {
        //first stop all running task of the user
        DB::table('drm_project_time_tracker')->where('user_id',CRUDBooster::myParentId())->update([
            'running_status' =>  0,
        ]);

        $task = DB::table('drm_project_time_tracker')->insert([
            'project_id' => request()->project,
            'user_id' => CRUDBooster::myParentId(),
            'start_time' => request()->time,
            'note' =>  request()->note,
            'title' =>  request()->title,
            'date' =>  date("y-m-d"),
            'running_status' =>  1,
        ]);

        return response()->json($task);

    }
    // public function postStopTimer()
    // {

    //     $startTime = DB::table('drm_project_time_tracker')->where('id',request()->taskid)->first();
    //     $endTime = date('H:i:s');
    //     $start_sec = strtotime("1970-01-01 $startTime->start_time UTC");
    //     $end_sec = strtotime("1970-01-01 $endTime UTC");
    //     $duration = $end_sec -  $start_sec ;
    //     $output = sprintf('%02d:%02d:%02d', (round($duration)/ 3600),(round($duration)/ 60 % 60), round($duration)% 60);

    //     $task = DB::table('drm_project_time_tracker')->where('id',request()->taskid)
    //         ->update([
    //         'end_time' => date('H:i'),
    //         'duration' =>  $output,
    //         'running_status' =>  0,
    //     ]);

    //     return response()->json($task);

    // }

    public function postStopTimer()
    {
        $taskID = request()->taskid;

        // Fetch the task's start time and date
        $task = DB::table('drm_project_time_tracker')->where('id', $taskID)->first();
        $startTime = $task->start_time;
        $startDate = $task->date;

        // Current date and time
        $endTime = date('H:i:s');
        $endDate = date('Y-m-d');

        // Calculate the total duration
        $startDateTime = strtotime("$startDate $startTime");
        $endDateTime = strtotime("$endDate $endTime");
        $durationSeconds = $endDateTime - $startDateTime;

        // Convert duration to hours, minutes, and seconds
        $hours = floor($durationSeconds / 3600);
        $minutes = floor(($durationSeconds % 3600) / 60);
        $seconds = $durationSeconds % 60;

        // Format the duration as HH:MM:SS
        $output = sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);

        // Update the task record
        DB::table('drm_project_time_tracker')
            ->where('id', $taskID)
            ->update([
                'end_time' => $endTime,
                'duration' => $output,
                'running_status' => 0,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

        return response()->json(['message' => 'Task updated successfully']);
    }

    public function getCheckRunningTask()
    {
        $task = DB::table('drm_project_time_tracker')
                ->where('user_id',CRUDBooster::myParentId())
                ->where('running_status',1)->latest()->first();

        return response()->json($task);

    }

    public function getDrmProjectReports(){

        if(!empty($_GET['start_date'])){

            $projects = DB::table('drm_project_time_tracker')
            ->leftJoin('drm_projects','drm_project_time_tracker.project_id','drm_projects.id')
            ->leftJoin('drm_customers','drm_projects.drm_customer_id','drm_customers.id')
            ->join('cms_users','cms_users.id','drm_project_time_tracker.user_id')
            ->select('drm_project_time_tracker.*','drm_project_time_tracker.id as tid','cms_users.*','drm_projects.title as ptitle','drm_projects.drm_customer_id','drm_customers.full_name')
            ->whereDate('drm_project_time_tracker.date','>=', $_GET['start_date'])
            ->whereDate('drm_project_time_tracker.date','<=', $_GET['end_date'])
            ->where('drm_project_time_tracker.user_id',CRUDBooster::myParentId())
            //->whereBetween('drm_project_time_tracker.date' ,[$_GET['start_date'],$_GET['end_date']])
            ->paginate(20);

        }else{
            $projects = DB::table('drm_project_time_tracker')
            ->leftJoin('drm_projects','drm_project_time_tracker.project_id','drm_projects.id')
            ->leftJoin('drm_customers','drm_projects.drm_customer_id','drm_customers.id')
            ->join('cms_users','cms_users.id','drm_project_time_tracker.user_id')
            ->select('drm_project_time_tracker.*','drm_project_time_tracker.id as tid','drm_project_time_tracker.note as pnote','cms_users.*','drm_projects.title as ptitle','drm_projects.drm_customer_id','drm_customers.full_name')
            ->where('drm_project_time_tracker.user_id',CRUDBooster::myParentId())
            ->paginate(20);
        }

        $projectList = DB::table('drm_projects')
        ->get();

        $duration = DB::table('drm_project_time_tracker')->where('user_id',CRUDBooster::myParentId())->pluck('duration');
        $users =  DB::table('drm_project_time_tracker')->select('user_id', DB::raw('count(*) as total'))
        ->groupBy('user_id')
        ->get();

        $projectsNum =  DB::table('drm_project_time_tracker')
        ->leftJoin('drm_projects','drm_project_time_tracker.project_id','drm_projects.id')
        ->select('drm_project_time_tracker.*','drm_projects.title as ptitle','drm_projects.id as pid')
        ->where('drm_project_time_tracker.project_id','!=',null)
        ->where('drm_project_time_tracker.user_id',CRUDBooster::myParentId())
        ->groupBy('project_id')->get();

       $worktime = DB::table('drm_project_time_tracker')
                   ->where('user_id',CRUDBooster::myParentId())
                   ->groupBy('date')->get();


       $customers = DB::table('drm_project_time_tracker')
                    ->leftJoin('drm_projects','drm_project_time_tracker.project_id','drm_projects.id')
                    ->leftJoin('drm_customers','drm_projects.drm_customer_id','drm_customers.id')
                    ->select('drm_projects.*','drm_projects.drm_customer_id as cid','drm_project_time_tracker.*','drm_customers.full_name')
                    ->where('drm_project_time_tracker.project_id','!=',null)
                    ->where('drm_projects.drm_customer_id','!=',null)
                    ->where('drm_project_time_tracker.user_id',CRUDBooster::myParentId())
                    ->groupBy('drm_project_time_tracker.project_id')->get();

       //dd($customers);

        return view('admin.drm_projects.reports',compact('projects','duration','users','projectsNum','worktime','customers','projectList'));
    }

    function exportReport(){
        ini_set('max_execution_time', '0');
        $projects = DB::table('drm_project_time_tracker')
        ->leftJoin('drm_projects','drm_project_time_tracker.project_id','drm_projects.id')
        ->leftJoin('drm_customers','drm_projects.drm_customer_id','drm_customers.id')
        ->join('cms_users','cms_users.id','drm_project_time_tracker.user_id')
        ->select('drm_project_time_tracker.*','cms_users.*','drm_projects.title as ptitle','drm_projects.drm_customer_id','drm_customers.full_name')
        ->where('drm_project_time_tracker.user_id',CRUDBooster::myParentId())
        ->get();

        return Excel::download(new ProjectTimeExport, 'report.xlsx');
    }

    public function postNoteUpdate()
    {
        $data = DB::table('drm_project_time_tracker')->where('id',request()->id)->update([
            'note' => request()->value
        ]);

        return response()->json($data);

    }
    public function addManualTaskReportInsert(){

        $req = request();
        $start_sec = strtotime("1970-01-01 $req->start_time UTC");
        $end_sec = strtotime("1970-01-01 $req->end_time UTC");

        $duration = $end_sec -  $start_sec ;

        $output = sprintf('%02d:%02d:%02d', (round($duration)/ 3600),(round($duration)/ 60 % 60), round($duration)% 60);


        $data = DB::table('drm_project_time_tracker')->insert([
            'project_id' => $req->project_id,
            'user_id' => CRUDBooster::myParentId(),
            'title'=>$req->title,
            'note'=>$req->note,
            'start_time' => $req->start_time,
            'end_time' =>$req->end_time,
            'duration' => $output,
            'date' => $req->date,
            'ismanual'=> 1,
        ]);
        return response()->json($data);

    }

    //group crud
    public function getProjectGroups(){

        $groups = DB::table('drm_project_groups')->where('user_id',CRUDBooster::myParentId())->get();
        return view('admin.drm_projects.project_groups',compact('groups'));
    }

    public function postSaveGroup(){
        $req = request();

        $save = DB::table('drm_project_groups')->insert([
            'name'=>$req->name,
            'user_id'=>CRUDBooster::myParentId(),
        ]);

        return response()->json($save);
    }

    public function postDeleteGroup(){
        $req = request();

        $delete = DB::table('drm_project_groups')->where('id',$req->id)->where('user_id', CRUDBooster::myParentId())->delete();

        return response()->json($delete);
    }

    public function postEditGroup(){
        $req = request();

        $edit = DB::table('drm_project_groups')->where('id',$req->id)->where('user_id', CRUDBooster::myParentId())->update(['name'=>$req->name]);

        return response()->json($edit);
    }



    public function getGetAllTasksDatesorting(){

        if(request()->sort == 'asc'){
            $tasks = DB::table('drm_project_tasks')->where('drm_project_card_id', request()->card_id)->orderBy('due_date', 'ASC')->get();
        }else if(request()->sort == 'desc'){
            $tasks = DB::table('drm_project_tasks')->where('drm_project_card_id', request()->card_id)->orderBy('due_date', 'DESC')->get();
        }else if(request()->sort == 'priority'){
            $tasks = DB::table('drm_project_tasks')->where('drm_project_card_id', request()->card_id)->orderBy(DB::raw('ISNULL(priority), priority'), 'ASC')->get();
        }else{
            $tasks = DB::table('drm_project_tasks')->where('drm_project_card_id', request()->card_id)->orderBy('position')->orderBy('created_at', 'DESC')->get();
        }

        //dd($tasks);


        foreach ($tasks as $task) {

            // $task = DB::table('drm_project_tasks')->where('drm_card_list_id', $list->id)->first();

            $checklist = DB::table('drm_task_checklist')->where('task_id', $task->id);
            $task->total_checklist = $checklist->count();
            $task->done_checklist = $checklist->where('drm_task_checklist.status', 'true')->count();

            $task->comment_count = DB::table('drm_task_comments')->where('task_id', $task->id)->count();
        }

        $priorities = DB::table('drm_task_priorities')->get();
        $data[] = [
            "tasks" => $tasks,
            "priorities" => $priorities
        ];

        return response()->json($data);
    }

    public function getGetAllTasksUrgentsorting(){


        $tasks = DB::table('drm_project_tasks')->where('drm_project_card_id', request()->card_id)->orderBy(DB::raw('ISNULL(priority), priority'), 'ASC')->get();
        //dd($tasks);
        //dd($tasks);
        foreach ($tasks as $task) {

            // $task = DB::table('drm_project_tasks')->where('drm_card_list_id', $list->id)->first();

            $checklist = DB::table('drm_task_checklist')->where('task_id', $task->id);
            $task->total_checklist = $checklist->count();
            $task->done_checklist = $checklist->where('drm_task_checklist.status', 'true')->count();

            $task->comment_count = DB::table('drm_task_comments')->where('task_id', $task->id)->count();
        }

        $priorities = DB::table('drm_task_priorities')->get();
        $data[] = [
            "tasks" => $tasks,
            "priorities" => $priorities
        ];

        return response()->json($data);
    }

    public function getGetSetDueDate(){

        $tasks = DB::table('drm_project_tasks')->where('id', request()->task_id)->update([
            'due_date' => request()->date,
        ]);

         //activity track
         $data=[];
         $data['description'] = 'Sets Due Date';
         $data['activity'] = 'set_due_date';
         $data['user_id'] = CRUDBooster::myParentId();
         $data['task_id'] = request()->task_id;

         $data['projectId'] = $_REQUEST['project_id'];
         $data['message'] = 'Sets a new date';
         TaskActivity::insert($data);
         TaskActivity::notify($data);



        return response()->json($tasks);
    }

    public function postSetTaskPriority(){

        $req = request();
        $task = DB::table('drm_project_tasks')->where('id', request()->task_id)->update([
            'priority' => request()->priority,
        ]);
        $taskDetail = DB::table('drm_project_tasks')->where('id', request()->task_id)->first();

        //activity track
        $data=[];
        $data['description'] = 'Sets Task Priority';
        $data['activity'] = 'Set_Task_Priority';
        $data['user_id'] = CRUDBooster::myParentId();
        $data['task_id'] = request()->task_id;
        $data['projectId'] = $_REQUEST['project_id'];
        $data['message'] = 'Sets Task Priority';
        TaskActivity::insert($data);
        TaskActivity::notify($data);

        return response()->json($taskDetail);
    }
    public function postRemoveTaskPriority(){

        $req = request();
        $task = DB::table('drm_project_tasks')->where('id', request()->task_id)->update([
            'priority' => '',
        ]);


        return response()->json($task);
    }
    ///task label
    public function getGetTaskLabel(){

        $req = request();
        $task = DB::table('drm_project_tasks')->where('id', request()->task_id)->first();
        return view('admin.drm_projects.partials.labelmodal', compact('task'));
    }

    public function postSaveTaskLabel(){

        $req = request();
        $tags = json_encode($req->tags);
        $task = DB::table('drm_project_tasks')->where('id', request()->task_id)->update([
            'tags' => $tags,
        ]);
        $taskDetail = DB::table('drm_project_tasks')->where('id', request()->task_id)->first();

         //activity track
         $data=[];
         $data['description'] = 'Added New Label';
         $data['activity'] = 'set_task_label';
         $data['user_id'] = CRUDBooster::myParentId();
         $data['task_id'] = request()->task_id;

         $data['projectId'] = $_REQUEST['project_id'];
         $data['message'] = 'Added New Label';
         TaskActivity::insert($data);
         TaskActivity::notify($data);

        return response()->json($taskDetail);
    }
    //change card
    public function postChangeTaskCard(){
        $task = DB::table('drm_project_tasks')->where('id', request()->task_id)->update([
            'drm_project_card_id' => request()->card_id,
        ]);

         //activity track
         $data=[];
         $data['description'] = 'Status Changed';
         $data['activity'] = 'set_new_status';
         $data['user_id'] = CRUDBooster::myParentId();
         $data['task_id'] = request()->task_id;

         $data['projectId'] = $_REQUEST['project_id'];
         $data['message'] = 'Task Status Changed';
         TaskActivity::insert($data);
         TaskActivity::notify($data);

         //dd($users);
        return response()->json($task);
    }

    public function saveTaskDescription(){
        $request = request();
        $validated = $request->validate([
            'description' => 'required'
       ]);
        $dom = new \DomDocument();
        $dom->loadHtml($validated['description'], LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $image_file = $dom->getElementsByTagName('img');

        if (!File::exists(public_path('uploads'))) {
            File::makeDirectory(public_path('uploads'));
        }

        foreach($image_file as $key => $image) {
            $data = $image->getAttribute('src');

            list($type, $data) = explode(';', $data);
            list(, $data) = explode(',', $data);

            $img_data = base64_decode($data);
            $image_name = "/uploads/" . time().$key.'.png';
            $path = public_path() . $image_name;
            file_put_contents($path, $img_data);

            $image->removeAttribute('src');
            $image->setAttribute('src', $image_name);
        }

        $validated['description'] = $dom->saveHTML();
        dd($description);
    }

    //save-view-style
    function postViewStyle(){

        $req = request();
        $result = DB::table('drm_project_settings')
        ->updateOrInsert(['type'=>'view_setting','user_id'=>CRUDBooster::myParentId(),'project_id'=>$req->project_id],[
            'type'=>'view_setting',
            'name'=> 'view_style',
            'project_id'=>$req->project_id,
            'value'=> $req->value,
            'user_id' => CRUDBooster::myParentId()
        ]);

        return response()->json($result);

    }

    //change project card view style
    function getViewStyle(){

        $req = request();
        $result = DB::table('drm_project_settings')->where('user_id',CRUDBooster::myParentId())
        ->where('type','view_setting')
        ->where('project_id',$req->project_id)->first();

        return response()->json($result);

    }

    function getBoardCards(){

        $req = request();
        $result=[];

        // $data['projects'] = DB::table('drm_project_members')->Join('drm_projects', 'drm_projects.id', '=', 'drm_project_members.drm_project_id')
        // ->where('drm_project_members.cms_user_id', CRUDBooster::myParentId())
        // ->orderBy('drm_projects.sort')
        // ->get();


        $tasks = DB::table('drm_project_tasks')->join('drm_project_activity','drm_project_activity.task_id','drm_project_tasks.id')
        ->join('drm_projects','drm_projects.id','drm_project_tasks.drm_project_id')
        ->Join('drm_project_members', 'drm_projects.id', '=', 'drm_project_members.drm_project_id')
        ->where('drm_project_members.cms_user_id', CRUDBooster::myId())
        //->where('drm_project_activity.user_id',CRUDBooster::myParentId())
        ->groupBy('drm_project_activity.task_id')
        ->orderBy('drm_project_activity.id','DESC')
       ->get();

       foreach ($tasks as $task) {

        $activity = DB::table('drm_project_activity')->join('cms_users','cms_users.id','drm_project_activity.user_id')->where('drm_project_activity.task_id',$task->task_id)
        ->select('drm_project_activity.*','cms_users.name')
        ->where('drm_project_activity.activity','!=','add_comment')
        ->orderBy('drm_project_activity.id','DESC')->get();

        $comment  = DB::table('drm_project_activity')->join('cms_users','cms_users.id','drm_project_activity.user_id')->where('drm_project_activity.task_id',$task->task_id)
        ->select('drm_project_activity.*','cms_users.name')
        ->where('drm_project_activity.activity','add_comment')
        ->orderBy('drm_project_activity.id','DESC')->get();


        $result[] = [
            "task" => $task,
            "activity" => $activity,
            "comment" => $comment,

        ];
        }

       //dd($result);

        return response()->json($result);

    }

    function getDeleteBoardCard(){

        $result = DB::table('drm_project_activity')->where('task_id',request()->id)->delete();
        return response()->json($result);

    }

    //add tag from task details direct input
    function postAddTaskTag(){
        $find = DB::table('drm_project_tasks')->where('id',request()->taskId)->first();
        //dd($find);
        if($find->tags!=null){
        //$explode_tags = explode(',', $find->tags );
          $array_tag = json_decode($find->tags,true);
          $array_tag[]=request()->tag;
        }else{
            $array_tag[] = request()->tag;
        }

        $update = DB::table('drm_project_tasks')->where('id',request()->taskId)
        ->update([
            'tags'=>$array_tag,
        ]);
        $insert_tag = DB::table('drm_task_tags')->updateOrInsert(
            ['tag_name'=> request()->tag],
            [
            'user_id'=>CRUDBooster::myParentId(),
            'tag_name'=>request()->tag,
        ]);


         //activity track
         $data=[];
         $data['description'] = 'Added new label';
         $data['activity'] = 'added_new_tag';
         $data['user_id'] = CRUDBooster::myParentId();
         $data['task_id'] = request()->taskId;

         $data['projectId'] = request()->project_id;
         $data['message'] =  'Added new label';

         TaskActivity::insert($data);
         TaskActivity::notify($data);


        $taskDetail = DB::table('drm_project_tasks')->where('id', request()->taskId)->first();
        return response()->json($taskDetail);

    }


    //open popover from project details page
    function getOpenPopover(){
        $data = [];
        $data['tags'] = DB::table('drm_task_tags')->where('user_id', CRUDBooster::myParentId())->get();

        return response()->json($data);

    }

    //summernote image upload & return url
    function postUploadImage(){
       $request = request();
        $file = $request->file('file');
        $fileName = time().'_'.$file->getClientOriginalName();
        $getUrl = uploadImage($file);

        return response()->json($getUrl);

    }

    //remove task labels
    function postRemoveTaskTag(){

        $find = DB::table('drm_project_tasks')->where('id',request()->taskId)->first();

        if($find->tags!=null){
          $array_tag = json_decode($find->tags,true);
          //dd($array_tag);
          $result = array_diff($array_tag , [request()->tag]);
        }
        $update = DB::table('drm_project_tasks')->where('id',request()->taskId)
        ->update([
            'tags'=>$result,
        ]);
        $taskDetail = DB::table('drm_project_tasks')->where('id', request()->taskId)->first();

         //activity track
         $data=[];
         $data['description'] = 'Removed label';
         $data['activity'] = 'added_tag';
         $data['user_id'] = CRUDBooster::myParentId();
         $data['task_id'] = request()->taskId;

         $data['projectId'] = request()->project_id;
         $data['message'] =  'Removed label';


         TaskActivity::insert($data);
         TaskActivity::notify($data);
        return response()->json($taskDetail);

    }

    function getGetSidebarProjects(){

        $projects = DB::table('drm_project_members')->Join('drm_projects', 'drm_projects.id', '=', 'drm_project_members.drm_project_id')
        ->where('drm_project_members.cms_user_id', CRUDBooster::myParentId())
        ->orderBy('drm_projects.sort')
        ->get();

        return response()->json($projects);
    }

    function getUserSuggestions(){
        $request = request();
        $query = $request->word;
        $project_id = $request->project_id;
        // $users = User::where('name', 'like', '%' . $query . '%')->pluck('name');
        $users = DB::table("drm_project_members as pm")->join('cms_users as u', 'u.id', 'pm.cms_user_id')->where('drm_project_id', $project_id)->where('u.name', 'like', '%' . $query . '%')->pluck('u.name');


        return response()->json($users);

    }

    function postAddDropfunnelTag()
    {
        try{
            $new_tag = DropfunnelTag::updateOrCreate([
                'tag' => request()->tag,
                'user_id' => CRUDBooster::myParentId(),
            ]);

            $tag_id = $new_tag->id;

            DB::table('drm_project_cards')->where('id', request()->card_id)->update([
                'tag_id' => $tag_id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Tag added successfully!',
            ]);

        }catch(\Exception $e){

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }

    }

    function postEditDropfunnelTag()
    {
        try{

            $new_tag = DropfunnelTag::updateOrCreate([
                'tag' => request()->tag,
                'user_id' => CRUDBooster::myParentId(),
            ]);

            $tag_id = $new_tag->id;

            DB::table('drm_project_cards')->where('id', request()->card_id)->update([
                'tag_id' => $tag_id,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Tag Updated successfully!',
            ]);

        }catch(\Exception $e){

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }

    }

    function postRemoveDropfunnelTag()
    {
        try{


            DB::table('drm_project_cards')->where('id', request()->card_id)->update([
                'tag_id' => null,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Tag Removed successfully!',
            ]);

        }catch(\Exception $e){

            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }

    }



    public function getOrderPm($orderId)
    {
        try {

            $order = \App\NewOrder::where('id', $orderId)->where('cms_user_id', CRUDBooster::myParentId())->exists();
            if(blank($order)) throw new Exception(__('Invalid access'));

            $chat = request()->query('chat' , '');
            $threadId = request()->query('threadId' , '');

            return response()->json([
                'success' => true,
                'html' => view('admin.drm_projects.partials.order_transfer_modal_form', ['order_id' => $orderId, 'chat' => $chat, 'threadId' => $threadId])->render(),
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }

    }

    public function getTaskCards($id)
    {
        $cards = DB::table('drm_project_cards')
        ->where('drm_project_id', $id)
        ->select('id', 'title')
        ->get()
        ->map(function($card) {
            return [
                'id' => $card->id,
                'name' => $card->title,
            ];
        })
        ->values()
        ->toArray();

        return response()->json($cards);
    }


    // Save order card
    public function postTransferOrder()
    {
        try {

            $validator = Validator::make($_REQUEST, [
                'project_id' => 'required',
                'card_id' => 'required',
                'order_id' => 'required',
                'chat' => 'nullable',
                'threadId' => 'nullable',
            ], [], [
                'project_id' => 'Project',
                'card_id' => 'Card',
                'order_id' => 'Order',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors()->messages(),
                    'message' => $validator->errors()->first(),
                ]);
            }

            $input = $validator->validate();

            $project = DB::table('drm_projects')
            ->where('id', $input['project_id'])
            ->select('id', 'title')
            ->first();

            $card = DB::table('drm_project_cards')
            ->where('id', $input['card_id'])
            ->where('drm_project_id', $input['project_id'])
            ->select('id', 'title')
            ->first();

            $order = \App\NewOrder::with('customer:id,full_name')->where('id', $input['order_id'])
                ->select('id', 'cart', 'supplier_id', 'status', 'order_date', 'marketplace_order_ref', 'invoice_number', 'inv_pattern', 'cms_user_id', 'drm_customer_id', 'dropmatix_tax_rate')
                ->first();

            if(DB::table('order_logs')->where('order_id', $order->id)->where('payload->status', 'send_to_pm')->exists())
            {
                throw new \Exception('Order already transferred to project management');
            }


            $title = trim($order->customer->full_name) .' '. inv_number_string($order->invoice_number, $order->inv_pattern);

            $trackings = OrderTracking::with('parcel:id,parcel_name')
                ->where('order_id', $order->id)
                ->select('parcel_id', 'package_number')
                ->get()
                ->map(function ($t) {
                    return [
                        'name' => $t->parcel ? $t->parcel->parcel_name : '',
                        'number' => $t->package_number,
                    ];
                })
                ->toArray();

            $products = collect($order->products)->map(function($product) {
                return ['name' => $product->product_name, 'ean' => $product ? ($product->ean ?? '') : ''];
            })->toArray();

            $description = view('support.order-header-project', ['order' => [
                'order_id' => $order->id,
                'status' => OrderStatus::statusLabel($order->status),
                'date' => date('Y-m-d', strtotime($order->order_date)),
                'trackings' => $trackings,
                'products' => $products,
            ]])->render();


            if($input['chat'] && $input['chat'] == 'yes' && $threadId = $input['threadId'])
            {
                $supportService = resolve(\App\Services\Support\SupportService::class);
                $description.= '<style>.chat-reply-btn{display: none;}</style>'.view('support.body', ['messages' => $supportService->pullThread($threadId), 'threadId' => $threadId])->render();
            }

            $task_id = DB::table('drm_project_tasks')
            ->insertGetId([
                'name' => $title,
                'drm_project_id' => $input['project_id'],
                'position' => DB::table('drm_project_tasks')->where('drm_project_card_id', $input['card_id'])->count() + 1,
                'drm_project_card_id' => $input['card_id'],
                'description' => $description,
                'start_date' => now(),
            ]);

            $url = CRUDBooster::adminPath('drm_projects/project/'.$project->id.'?openProject='.$task_id);
            $messageUrl = '<a href="'.$url.'"><i class="fa fa-share-square-o" aria-hidden="true"></i></a>';
            $message = "Transfer to board {$project->title} to column {$card->title}";

            $payload = [
                'status' => 'send_to_pm',
                'time' => date('Y-m-d H:i:s'),
                'action_by' => CRUDBooster::myId(),
                'user_name' => CRUDBooster::myName(),
                'message' => "{$message} {$messageUrl}",
            ];

            DB::table('order_logs')->insert([
                'order_id' => $order->id,
                'payload' => json_encode($payload),
                'created_at' => now(),
                'updated_at' => now(),
            ]);


            return response()->json([
                'success' => true,
                'message' => $message,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }
}
