<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\User;
use App\NewOrder;
use App\Mail\DRMSEndMail;
use App\Enums\Channel;
use App\Services\DRM\DRMService;

class AdminDtTariffController extends Controller
{
    //Purchase droptienda Tariff
    public function dtTariffPurchaseModal()
    {
        try {

            $user_id = CRUDBooster::myParentId();
            $used_plan_id = DB::table('dt_tariff_purchases')->where('user_id', $user_id)->where('end_date', '>=', \Carbon\Carbon::now())->value('plan_id');

            $is_used = false;
            if(!empty($used_plan_id)){
                $is_used = true;
            }

            // $plans = config('dt_licenses.plans') ?? [];
            $plans = DB::table('import_plans')->where('status', 1)->where('dt_tariff_active', 1)->get();

            $item = [
                'name' => "Droptienda Tarif Plan",
                'description' => 'Deine Supportlizenz ist 365 Tage gültig. Unseren Support erreichst du per E-Mail oder telefonisch rund um die Uhr, täglich. Du bekommst auf deine Fragen eine direkte und vertrauliche Antwort. Deine Fragen oder Anliegen werden nicht öffentlich in der Gruppe diskutiert. Ein direkter Ansprechpartner und eine vertrauliche Kommunikation sind von unschätzbarem Wert, wenn du deinen Webshop professionell betreiben möchtest. Auf Wunsch installieren dir auch unkompliziert und kostenfrei deinen Shop bei deinem Wunschprovider, damit du direkt starten kannst.',
                'plans' => $plans,
                'used_plan' => $used_plan_id,
                'is_used' => $is_used,
            ];

            if(empty($plans)) throw new \Exception('No license plan avaliable');

            $item['image'] = asset('images/icons/dt_support.png');

            //User term
            $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
            $user = User::with('billing_detail')->find($user_id);
            $term = ($privacy) ? $privacy->page_content : '';
            $user_data = '<div id="customer_data_term"></div>';
            if ($user->billing_detail) {
                $billing = $user->billing_detail;
                $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
            }
            if (strpos($term, '{customer}') !== false) {
                $term = str_replace('{customer}', $user_data, $term);
            }

            $dt_user_plan_status = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);

            $hasPaypal = app(DRMService::class)->paypalCheck(User::DROPTIENDA_ACCOUNT_ID);

            return response()->json([
                'success' => true,
                'html' => view('template_store.partials.dt_license_purchase_modal', compact('item', 'term', 'used_plan_id', 'hasPaypal'))->render(),
                'user_status' => $dt_user_plan_status,
            ], 200);

            throw new \Exception('Invalid action');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    //Purchase droptienda Tariff
    public function purchaseDroptiendaTariff($purchase_data)
    {
        DB::beginTransaction();
        try {
            $intend_id = $purchase_data['id'];
            $plan_id = $purchase_data['plan_id'];
            $interval = $purchase_data['interval_type'];


            if (is_null($plan_id)) {
                throw new \Exception('You are not accessible to use this feature, Please Contact with DRM Customer Care!');
            } else {


                $discount = $purchase_data['discount'] ?? 0;
                $total = $purchase_data['total'] ?? 0;
                $sub_total = $purchase_data['sub_total'] ?? 0;

                if(NewOrder::where(['order_id_api' => $intend_id, 'cms_user_id' => 2439, 'shop_id' => 8])->exists()) throw new \Exception('Already purchased!');  //STRIPE_CLIENT_DT

                $item_name = 'Droptienda tariff '.$interval.'ly subscription';
                $item_description = $intend_id;
                $subscription_id = $purchase_data['subscription_id'];

                $tax_rate = 21;
                $total_tax = 0;
                $has_vat_number = false;
                $vat_number = null;
                $tax_version = null;

                if( isset($purchase_data['main_amount']) ) {
                    $sub_total = $purchase_data['main_amount'];
                    $tax_version = 1;
                    $vat_number = $purchase_data['vat_number'] ?? null;
                    $has_vat_number = !empty($vat_number);
                    $tax_rate = $has_vat_number ? 0 : 21;
                    $total_tax = $purchase_data['total_tax'];
                }


                //Increment single pay coupon usages
                if (isset($purchase_data['coupon']) && $purchase_data['coupon']) {
                    DB::table('coupons')->where('coupon_id', $purchase_data['coupon'])->increment('single_pay');
                }

                //cms client
                $cms_client = $purchase_data['user_id'];
                $user = User::with('billing_detail')->find($cms_client);
                if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

                //Initial purchase data
                $subscription_data = [
                    'identity' => $purchase_data['id'],
                    'purchase_data' => json_encode($purchase_data)
                ];

                //Initialize subscription data
                $subscription_data['subscription_id']       =   $purchase_data['subscription_id'];
                $subscription_data['start_date']            =   $purchase_data['period_start'];
                $subscription_data['end_date']              =   $purchase_data['period_end'];
                $subscription_data['plan_id']               =   $plan_id;
                $subscription_data['stripe_customer_id']    =   $purchase_data['stripe_customer_id']?? null;

                $subscription_data['created_at']            =   now();
                $subscription_data['updated_at']            =   now();
                $subscription_data['total']                 =   $total;
                $subscription_data['sub_total']             =   $sub_total;
                $subscription_data['discount']              =   $discount;
                $subscription_data['type']                  =   $purchase_data['interval_type'];

                //Status
                $subscription_data['status'] = 1;

                //Unsubscribe old plan
                $old_subscription_id = DB::table('dt_tariff_purchases')->where('user_id', $cms_client)->where('subscription_id', '!=', $subscription_id)->value('subscription_id');

                if(!empty($old_subscription_id)){
                    resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2439', '', $old_subscription_id);
                }

                //Subscription create or update
                DB::table('dt_tariff_purchases')->updateOrInsert(['user_id' => $cms_client], $subscription_data);

                // Hide Dashboard Onboarding Progres Bar for <<<< DT USERS >>>>>
                if (is_dt_user() && checkTariffEligibility($cms_client)){
                    DB::table('dashboard_onboarding')->where('user_id', $cms_client)->update([
                        'visible'    => 0,
                        'hide'       => 1,
                        'updated_at' => now()
                    ]);
                }

                $payment_intend_id = $purchase_data['intend_id'] ?? null;

                $user_shop_id = DB::table('shops')->where('user_id', $cms_client)->where('channel', Channel::DROPTIENDA)->value('id');

                $order_info = [
                    'user_id' => 2439,  //STRIPE_CLIENT_DT
                    'cms_client' => $cms_client,
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => round(($total), 2),
                    'sub_total' => round($sub_total, 2),
                    'discount' => round($discount, 2),
                    'discount_type' => 'fixed',
                    'total_tax' => $total_tax,
                    'payment_type' => 'Stripe',
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => \App\Enums\InsertType::DT_LICENSE,
                    'shop_id' => $user_shop_id ?? NULL,
                    'order_id_api' => $intend_id,
                    'intend_id' => $payment_intend_id,
                    'vat_number' => $vat_number,
                    'tax_version' => $tax_version,
                ];

                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = $item_name;
                $cart_item['description'] = $item_description;
                $cart_item['qty'] = 1;
                // $cart_item['rate'] = round($sub_total, 2);
                $cart_item['tax'] = $tax_rate;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($sub_total, 2);
                $cart_item['plan_id'] = $plan_id;
                $cart_item['sub_start_date'] = $purchase_data['period_start'];
                $cart_item['sub_end_date'] = $purchase_data['period_end'];
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);
                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $cms_client);

                //Dt shop unlock
                $lock_status = 'dt_tariff_purchased';
				app('App\Http\Controllers\AdminDrmAllCustomersController')->lockUnlockCustomer($cms_client, $lock_status);

            }

            \Session::forget('currentPlan'.$cms_client);
            DB::commit();    // Commiting  ==> There is no problem whatsoever
            return ['success' => true, 'message' => 'Successfully purchase droptienda tariff plan!'];
        } catch (\Exception $e) {
            DB::rollback();   // rollbacking  ==> Something went wrong
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }


    //DT Subscription Cancel
    public function dtTariffSubscriptionCancel($id)
    {
        try {
            $stripe_cancel = false;
            $message = "";

            //Unsubscribe old plan
            $purchase_tariff_paln = DB::table('dt_tariff_purchases')
            ->join('import_plans','import_plans.id','=','dt_tariff_purchases.plan_id')
            ->select('dt_tariff_purchases.user_id', 'dt_tariff_purchases.subscription_id', 'dt_tariff_purchases.end_date', 'dt_tariff_purchases.contract_end_at', 'import_plans.plan', 'import_plans.interval')
            ->where('dt_tariff_purchases.id', $id)
            ->first();

            $has_payment_contract = false;
            if (!empty($purchase_tariff_paln->contract_end_at) && ($purchase_tariff_paln->contract_end_at >= \Carbon\Carbon::now())) {
                $has_payment_contract = true;
            }

            if ($has_payment_contract) {
                return response(['status'=> false, 'message' => __('Subscription can only be canceled after 3 months of subscription date.')]);
            }

            $user = User::with('billing_detail')->find($purchase_tariff_paln->user_id);

            if(!empty($purchase_tariff_paln->subscription_id)){
                resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2439', '', $purchase_tariff_paln->subscription_id);
                $stripe_cancel = true;
            }

            if($stripe_cancel){

                DB::table('dt_tariff_purchases')->where('id', $id)->update(['is_renew_cancel' => 1]);

                $tags = [
                    'app_name' =>  $purchase_tariff_paln->plan,
                    'subscription_interval' =>  ucfirst($purchase_tariff_paln->interval),
                    'period_end' =>  $purchase_tariff_paln->end_date,
                    'period_start' =>  date('Y-m-d'),
                ];


                $slug = 'subscription_cancel'; //Page slug
                $lang = app('\App\Services\UserService')->getLang($purchase_tariff_paln->user_id);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang); //Generated html

                // if(!isLocal()){
                    app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data)); //Send
                // }

                return response()->json([
                    'status' => true,
                    'message' => "Subscription canceled!"
                ]);
            }

            throw new \Exception("Subscription cancelation failed!");

        }catch(\Exception $e){
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function getDtTariffPlanPopup()
    {
        try {
            $data = [];
            $content = '';

            $data['modal_title'] = 'Droptienda Tariff Plan';

            $user_id = CRUDBooster::myParentId();
            if (checkTariffEligibility($user_id)) {

                $purchased_plan_id = DB::table('dt_tariff_purchases')->where('user_id', $user_id)->where('end_date', '>=', \Carbon\Carbon::now())->value('plan_id');

                $dt_tariff_plans = DB::table('import_plans')->where('status', 1)->where('dt_tariff_active', 1)->get();

                // $pack_one = [
                //     "Sortiment & Absatzforderung" => 1,
                //     "(GrenzUberschreitender) Handel einfach wie nie" => 1,
                //     "E-Mail-Marketing via Dropfunnels" => 1,
                //     "SEO Onpage-Analyse & Tracking" => 1,
                //     "Rechtssicherheit, DSGVO & Backups" => 1,
                //     "Webseiten, Landingpages, Salespages & Blog" => 1,
                //     "Zahlungsmoglichkeiten & Versandeinstellungen" => 1,
                //     "Dropmatix ERP I CRM Vollintegration" => 1,
                //     "Experten-Hilfe fUr deinen Erfolg" => 1,
                // ];

                // $pack_two = [
                //     "Sortiment & Absatzforderung" => 3,
                //     "(GrenzUberschreitender) Handel einfach wie nie" => 2,
                //     "E-Mail-Marketing via Dropfunnels" => 0,
                //     "SEO Onpage-Analyse & Tracking" => 5,
                //     "Rechtssicherheit, DSGVO & Backups" => 5,
                //     "Webseiten, Landingpages, Salespages & Blog" => 5,
                //     "Zahlungsmoglichkeiten & Versandeinstellungen" => 4,
                //     "Dropmatix ERP I CRM Vollintegration" => 4,
                //     "Experten-Hilfe fUr deinen Erfolg" => 2,
                // ];

                // $pack_three = [
                //     "Sortiment & Absatzforderung" => 4,
                //     "(GrenzUberschreitender) Handel einfach wie nie" => 4,
                //     "E-Mail-Marketing via Dropfunnels" => 2,
                //     "SEO Onpage-Analyse & Tracking" => 5,
                //     "Rechtssicherheit, DSGVO & Backups" => 5,
                //     "Webseiten, Landingpages, Salespages & Blog" => 5,
                //     "Zahlungsmoglichkeiten & Versandeinstellungen" => 4,
                //     "Dropmatix ERP I CRM Vollintegration" => 4,
                //     "Experten-Hilfe fUr deinen Erfolg" => 3,
                // ];

                // $pack_four = [
                //     "Sortiment & Absatzforderung" => 5,
                //     "(GrenzUberschreitender) Handel einfach wie nie" => 5,
                //     "E-Mail-Marketing via Dropfunnels" => 5,
                //     "SEO Onpage-Analyse & Tracking" => 5,
                //     "Rechtssicherheit, DSGVO & Backups" => 5,
                //     "Webseiten, Landingpages, Salespages & Blog" => 5,
                //     "Zahlungsmoglichkeiten & Versandeinstellungen" => 5,
                //     "Dropmatix ERP I CRM Vollintegration" => 5,
                //     "Experten-Hilfe fUr deinen Erfolg" => 5,
                // ];

                $data['dt_tariff_plans'] = $dt_tariff_plans;
                $data['purchased_plan_id'] = $purchased_plan_id;

                // $data['tariff_feature'] = [
                //     0 => $pack_one,
                //     1 => $pack_two,
                //     2 => $pack_three,
                //     3 => $pack_four,
                // ];

                $progress_bar = DB::table('dashboard_onboarding')->where('user_id', $user_id)->first();
                $progress = isset($progress_bar->progress_data) ? json_decode($progress_bar->progress_data, true) : [];

                $purchase_btn_class = 'dt_license_purchase_btn';

                $user_billing_exists = DB::table('billing_details')->where('user_id', $user_id)->exists();

                if (!$user_billing_exists || !array_key_exists('contract', $progress)){
                    $purchase_btn_class = 'contact_modal_open';
                }

                $data['purchase_btn_class'] = $purchase_btn_class;

                $data['dt_tariff_purchased'] = dtTariffPurchased($user_id);

                $data['dt_plan_detail'] = [
                    'Ideal als Webvisitenkarte, Unternehmenswebseite oder Blog. Ideal für erste Sichtbarkeit im Netz',
                    'Einstieg in die E-Commerce-Welt inkl. Shop, Google Shopping und unbegrenzt Produkten/Kategorien',
                    'Großer Funktionsumfang für professionelle Seiten und Webshops inklusive Online-Marketing-Videokurs',
                    'Alle Funktionen inklusive - großartig für mehrsprachige Webshops und Cooperate-Webseiten‌',
                ];

                $data['plan_images'] = [
                    'SiteCard.svg',
                    'Starten.svg',
                    'Wachsen.svg',
                    'Skalieren.svg',
                ];

                $data['plan_titles'] = [
                    'SiteCard',
                    'Starten',
                    'Wachsen',
                    'Skalieren',
                ];

                $content = view('dashboard.dt_tariff.dt_tariff_plan_modal', $data)->render();
            }

            return response()->json([
                'success'    => true,
                'html'       => $content,
            ], 200);
        } catch(\Exception $ex) {
            return response()->json([
                'success' => false,
                'html' => $ex->getMessage()
            ], 422);
        }
    }

    public function dtSidebarLockModal()
    {
        $data = [];

        $html = view('dashboard.dt_tariff.dt_sidebar_lock_modal', $data)->render();

        return response()->json([
            'success' => true,
            'html' => $html
        ], 200);
    }

    public function dtSidebarPlanLockModal()
    {
        $data = [];
        $user_id = CRUDBooster::myParentId();

        $data['page_url'] = $_GET['page_url'];
        $data['user_plan'] = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);

        $html = view('dashboard.dt_tariff.dt_sidebar_plan_lock_modal', $data)->render();

        return response()->json([
            'success' => true,
            'html' => $html
        ], 200);
    }

    public function dtAccountLockUnlock($user_id, $user_account_status = 0)
    {
        $user_dt_shops = DB::table('shops')->where([
            'user_id' => $user_id,
            'channel' => Channel::DROPTIENDA,
        ])->get();

        foreach($user_dt_shops as $user_dt_shop)
        {
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $this->getPrefix($user_dt_shop->url).'api/v1/shop-control',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => array(
                    'isLocked' => ($user_account_status == 1) ? 'true' : 'false'
                ),
                CURLOPT_HTTPHEADER => array(
                    'userToken: '.$user_dt_shop->username,
                    'userPassToken: '.$user_dt_shop->password
                ),
            ));

            curl_exec($curl);
            curl_close($curl);
        }
    }

    private function getPrefix($url): string
    {
        return trim( $url, '/').'/';
    }

}
