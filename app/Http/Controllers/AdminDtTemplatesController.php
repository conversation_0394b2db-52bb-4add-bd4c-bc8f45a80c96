<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\TemplatePurchase;
use App\Temp;
use App\User;
use App\NewOrder;
use App\Services\TemplateStoreService;
use Illuminate\Support\Facades\Validator;
use App\Notifications\DRMNotification;
use Illuminate\Support\Facades\Log;
use App\Enums\Channel;
use App\Services\DRM\DRMService;

class AdminDtTemplatesController extends Controller
{
    public TemplateStoreService $template_service;

    public function __construct(TemplateStoreService $template_service)
    {
        if(!isLocal()) {
            abort(404);
        }
        
        $this->template_service = $template_service;
    }

    public function templateStore()
    {
        $data = [];

        $query = [];
        $query['search'] = $_REQUEST['search']?? null;
        $query['filter'] = $_REQUEST['filter']?? null;
        $query['id'] = $_REQUEST['template']?? null;


        $user_id = CRUDBooster::myParentId();
        $templates = collect($this->template_service->getTemplateDetails($query)?? []);
        $active_flat_rate =  $this->template_service->isActiveDTFlatRate($user_id)?? false;

        $free_templates = config('dt_store.free_templates')?? [];

        if($active_flat_rate){
            $data['templates'] = $templates->map(function($template) use($active_flat_rate, $free_templates){
                $is_free = in_array($template['id'], $free_templates)? true : false;

                $prices = $template['prices']?? [11 => $template['price']];
                $prices_labels = collect($prices)->map(function($p, $k){
                    return dtStorePlanLabel($k).': '.number_format(($p??0), 2, ',', '.').formatCurrency('EUR');
                })->toArray();

                $sorted_price_labels = array_replace(array_flip([12, 13, 11, 10]), $prices_labels);
                $p_labels = collect($sorted_price_labels)->filter(function($s){
                    return !is_numeric($s);
                })
                ->toArray();

                if(empty($p_labels)){
                    $p_labels[] = number_format(($template['price']??0), 2, ',', '.').formatCurrency('EUR');
                }

                return [
                    'id' => $template['id'],
                    'name' => $template['name'],
                    'template_file' => $template['template_file'],
                    'price' => $template['price'],
                    'description' => $template['description'],
                    'versions' => implode(', ', $template['versions']),
                    'images' => $template['images'],
                    'flat_rate' => true,
                    'is_used' => true,
                    'used_plan' => null,
                    'trial_time' => 0,
                    'can_update' => false,
                    'is_free' => $is_free,
                    'prices' => $p_labels,
                    'used_label' => dtStorePlanLabel($active_flat_rate),
                ];
            })->toArray();

        }else{

            $is_used_flat_rate = $this->template_service->isUsedFlatRate($user_id)? true : false;
            $used_templates = $this->template_service->templatesUsed($user_id)?? [];
            $purchased_templates = $this->template_service->purchaseTemplates($user_id)?? [];

            $data['templates'] = $templates->map(function($template) use($is_used_flat_rate, $used_templates, $purchased_templates, $user_id, $free_templates){
                $t_id = $template['id'];
                $is_free = in_array($t_id, $free_templates)? true : false;

                $is_used = $is_used_flat_rate? true : (in_array($t_id, $used_templates)? true : false);
                $used_plan = $purchased_templates[$t_id]?? null;
                $can_update = ($used_plan == 11)? false : true;

                $prices = $template['prices']?? [11 => $template['price']];

                if($used_plan){
                    $is_used = true;
                }


                if(empty($used_plan) && !$is_free){
                    $prices[10] = 0;
                }
                
                $prices_labels = collect($prices)->map(function($p, $k){
                    return dtStorePlanLabel($k).': '.number_format(($p??0), 2, ',', '.').formatCurrency('EUR');
                })->toArray();

                $sorted_price_labels = array_replace(array_flip([12, 13, 11, 10]), $prices_labels);
                $p_labels = collect($sorted_price_labels)->filter(function($s){
                    return !is_numeric($s);
                })
                ->toArray();

                if(empty($p_labels)){
                    $p_labels[] = number_format(($template['price']??0), 2, ',', '.').formatCurrency('EUR');
                }

                $trial_time = ($used_plan == 10)? $this->template_service->trialRemainsSeconds($user_id, $template['id']) : 0;

                return [
                    'id' => $template['id'],
                    'name' => $template['name'],
                    'template_file' => $template['template_file'],
                    'price' => $template['price'],
                    'description' => $template['description'],
                    'versions' => implode(', ', $template['versions']),
                    'images' => $template['images'],
                    'flat_rate' => false,
                    'is_used' => $is_used,
                    'used_plan' => $used_plan,
                    'trial_time' => $trial_time,
                    'can_update' => $can_update,
                    'is_free' => $is_free,
                    'prices' => $p_labels,
                    'used_label' => $used_plan? dtStorePlanLabel($used_plan) : '',
                ];
            })->toArray();
        }

        $data['active_flat_rate'] = $active_flat_rate;
        $data['page_title'] = 'Droptienda template store';    
        return view('template_store.index', $data);
    }

    public function purchaseTemplateView(Request $request)
    {
        try {

            $template_detail = $this->template_service->singleTemplate($request->input('id'));
            if(empty($template_detail)) throw new \Exception('Invalid template!');

            $user_id = CRUDBooster::myParentId();

            $active_flat_rate =  $this->template_service->isActiveDTFlatRate($user_id)?? false;
            if($active_flat_rate)  throw new \Exception('You have active flat rate!');

            $template_id = $template_detail['id'];

            $free_templates = config('dt_store.free_templates')?? [];
            if(in_array($template_id, $free_templates)) throw new \Exception('Sorry it\'s a free template. You can use it without purchase!');

            $is_used_flat_rate = $this->template_service->isUsedFlatRate($user_id)? true : false;
            $used_templates = $this->template_service->templatesUsed($user_id)?? [];
            $is_used = ($is_used_flat_rate || isset($used_templates[$template_id]))? true : false;

            $purchased_templates = $this->template_service->purchaseTemplates($user_id)?? [];
            $used_plan = $purchased_templates[$template_id]?? null;

            if(!empty($used_plan)){
                $is_used = true;
            }

            $price = $template_detail['price'];
            $prices = $template_detail['prices'];

            if(!$is_used){
                $prices[10] = 0;
            }
            asort($prices);

            $template = [
                'name' => $template_detail['name'],
                'description' => $template_detail['description'],
                'price' => $price,
                'template_id' => $template_id,
                'prices' => $prices,
                'used_plan' => $used_plan,
                'is_used' => $is_used,
            ];

            $template['image'] = ( is_array($template_detail['images']) && !empty($template_detail['images']) )? reset($template_detail['images']) : null;

            if ($template) {
                $user = User::find(CRUDBooster::myParentId());
                //User term
                $privacy = DB::table('drm_pages')->where('page_name', 'dt_template_term_and_conditions')->first();
                $term = ($privacy) ? $privacy->page_content : '';
                $user_data = '<div id="customer_data_term"></div>';
                if ($user->billing_detail) {
                    $billing = $user->billing_detail;
                    $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
                }
                if (strpos($term, '{customer}') !== false) {
                    $term = str_replace('{customer}', $user_data, $term);
                }

                return response()->json([
                    'success' => true,
                    'html' => view('template_store.partials.purchase_modal', compact('template', 'term', 'used_plan'))->render(),
                ], 200);
            }

            throw new \Exception('Invalid action');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    //Flat rate purchase form
    public function dtFlatRatePurchaseForm()
    {
        try {

            $user_id = CRUDBooster::myParentId();
            $used_plan =  $this->template_service->isActiveDTFlatRate($user_id)?? false;
            $price = 0;
            $template = [
                'name' => 'Flat rate',
                'description' => 'Sichere Dir Zugriff auf ALLE Droptienda-Templates. Mit unserer Flatrate sicherst Du dir zusätzlich auch alle Neuheiten. Außerdem sind alle Updates inklusive.',
                'price' => $price,
                'template_id' => 'all',
                'prices' => config('dt_store.flat_price')?? [],
            ];

            $template['image'] = asset('images/dt_store/flat_rate.png');

            if ($template) {
                $user = User::find(CRUDBooster::myParentId());
                //User term
                $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
                $term = ($privacy) ? $privacy->page_content : '';
                $user_data = '<div id="customer_data_term"></div>';
                if ($user->billing_detail) {
                    $billing = $user->billing_detail;
                    $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
                }
                if (strpos($term, '{customer}') !== false) {
                    $term = str_replace('{customer}', $user_data, $term);
                }

                return response()->json([
                    'success' => true,
                    'html' => view('template_store.partials.purchase_modal', compact('template', 'term', 'used_plan'))->render(),
                ], 200);
            }

            throw new \Exception('Invalid action');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    //Purchase Template
    public function purchaseTemplate($purchase_data)
    {

        //DB::beginTransaction();
        try {
            $intend_id = $purchase_data['id'];
            $template_id = $purchase_data['template_id'];
            if (is_null($template_id)) {
                throw new \Exception('You are not accessible to use this feature, Please Contact with DRM Customer Care!');
            } else {

                if(NewOrder::where(['order_id_api' => $intend_id, 'cms_user_id' => 2439, 'shop_id' => 8])->exists()) throw new \Exception('Already purchased!');  //STRIPE_CLIENT_DT

                $template_name = 'Droptienda template';
                $template_description = $intend_id;
                $is_flat_rate = false;
                $create_invoice = true;

                try{
                    $template = $this->template_service->singleTemplate($template_id);
                    $template_name = (!empty($template) && isset($template['name']))? $template_name.': '.$template['name'] : $template_name;
                }catch(\Exception $exc){}

                //Increment single pay coupon usages
                if (isset($purchase_data['coupon']) && $purchase_data['coupon']) {
                    DB::table('coupons')->where('coupon_id', $purchase_data['coupon'])->increment('single_pay');
                }

                //cms client
                $cms_client = $purchase_data['user_id'];
                $user = User::with('billing_detail')->find($cms_client);
                if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

                //Initial purchase data
                $subscription_data = [
                    'identity' => $purchase_data['id'],
                    'purchase_data' => $purchase_data
                ];

                $subscription_data['plan_id'] = 11;
                $interval_type = $purchase_data['interval_type']?? [];
                if(!empty($interval_type) && in_array($interval_type, ['month', 'year'])){
                    //Initialize subscription data
                    $subscription_data['subscription_id']       =   $purchase_data['subscription_id'];
                    $subscription_data['start_date']            =   $purchase_data['period_start'];
                    $subscription_data['end_date']              =   $purchase_data['period_end'];
                    $subscription_data['plan_id']               =   $purchase_data['plan_id'];
                    $subscription_data['stripe_customer_id']    =   $purchase_data['stripe_customer_id']?? null;

                    $is_flat_rate = ($purchase_data['is_flat_rate'] === 'yes')? true : false;
                    $f_r_text = ($is_flat_rate)? 'flat rate' : 'plan';
                    $template_description = ucfirst($purchase_data['interval_type']).'ly '.$f_r_text.' purchase. End date: '. $purchase_data['period_end'];
                }elseif(isset($purchase_data['type'])){
                    $subscription_data['subscription_id']   =   null;
                    $subscription_data['start_date']        =   null;
                    $subscription_data['end_date']          =   null;

                    if($purchase_data['type'] == 'trial'){
                        $subscription_data['plan_id'] = 10;
                        $subscription_data['start_date']        =   $purchase_data['period_start'];
                        $subscription_data['end_date']          =   $purchase_data['period_end'];
                        $template_description = 'Trial end date: '. $purchase_data['period_end'];

                        $create_invoice = false;
                    }                   
                }

                //Unsubscripe old plan
                if($is_flat_rate){
                    $templates_active = DB::table('template_purchases')->where('user_id', $user->id)->pluck('template_id')->toArray();
                    if(!empty($templates_active)){
                        foreach ($templates_active as $active_item) {
                            $this->unsubscribeOldPlan($active_item, $user->id, $purchase_data['subscription_id']);
                        }
                    }
                }else{
                    $this->unsubscribeOldPlan($template_id, $user->id, $purchase_data['subscription_id']);
                }

                //Unsubccribe old flat rates
                $this->unsubscribeFatRateSubscription($user->id, $purchase_data['subscription_id']);
                
                //Status
                $subscription_data['status'] = 1;

                //Flat rate check
                if($is_flat_rate){
                    DB::table('dt_flat_rates')->where('user_id', $user->id)->update(['status' => 0]);
                    $subscription_data['user_id'] = $cms_client;
                    $subscription_data['purchase_data'] = json_encode($subscription_data['purchase_data']);

                    $subscription_data['created_at'] = \Carbon\Carbon::now();
                    $subscription_data['updated_at'] = \Carbon\Carbon::now();
                    $subscription_data['status'] = 1;

                    DB::table('dt_flat_rates')->insert($subscription_data);
                    DB::table('template_purchases')->where('user_id', $user->id)->update(['status' => 0]);
                }else{
                    $subscription_data['deleted_at'] = null;
                    $template_purchase = TemplatePurchase::updateOrCreate([
                        'user_id' => $cms_client,
                        'template_id' => $purchase_data['template_id'],
                    ], $subscription_data); 
                }

                $discount = $purchase_data['discount'] ?? 0;
                $total = $purchase_data['total'] ?? 0;
                $sub_total = $purchase_data['sub_total'] ?? 0;

            $payment_intend_id = $purchase_data['intend_id'] ?? null;


            $tax_rate = 21;
            $total_tax = 0;
            $has_vat_number = false;
            $vat_number = null;
            $tax_version = null;

            if( isset($purchase_data['main_amount']) ) {
                $vat_number = $purchase_data['vat_number'] ?? null;
                $has_vat_number = !empty($vat_number);
                $tax_rate = $has_vat_number ? 0 : 21;
                $total_tax = $purchase_data['total_tax'];
                $sub_total = $purchase_data['main_amount'];
                $tax_version = 1;
            }
                $order_info = [
                    'user_id' => 2439,  //STRIPE_CLIENT_DT
                    'cms_client' => $cms_client,
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => round(($total), 2),
                    'sub_total' => round($sub_total, 2),
                    'discount' => round($discount, 2),
                    'discount_type' => 'fixed',
                    'total_tax' => $total_tax,
                    'payment_type' => 'Stripe',
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => \App\Enums\InsertType::DT_TEMPLATE,
                    'shop_id' => 8,
                    'order_id_api' => $intend_id,
                    'intend_id' => $payment_intend_id,
                    'vat_number' => $vat_number,
                    'tax_version' => $tax_version,
                ];

                $template_name = 'Droptienda template';
                try{
                    if($is_flat_rate){
                        $template_name = 'Droptienda '.$interval_type.' flat rate';
                    }else{
                        $template = $this->template_service->singleTemplate($template_id);
                        $template_name = (!empty($template) && isset($template['name']))? $template_name.': '.$template['name'] : $template_name;
                    }
                }catch(\Exception $exc){}

                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = $template_name;
                $cart_item['description'] = $template_description;
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($sub_total, 2);
                $cart_item['tax'] = $tax_rate;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($sub_total, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);
                if ($create_invoice) {
                    app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $cms_client);
                }  
            }

            //DB::commit();    // Commiting  ==> There is no problem whatsoever
            // return ['success' => true, 'message' => 'Successfully purchase template: ' . $quiz->name];
            return ['success' => true, 'message' => 'Successfully purchase template: '];
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }


    //Trial template
    public function trialTemplate(Request $request){
        try{

            $template_id = $request->template_id;
            $user_id = $request->user_id;
            if(empty($template_id) || empty($user_id)) throw new \Exception("Something went wrong!");

            $data = [
              "id" => "trialdttrnx".$template_id.$user_id,
              "type" => "trial",
              "total" => 0,
              "plan_id" => 10,
              "user_id" => $user_id,
              "discount" => 0,
              "sub_total" => 0,
              "template_id" => $template_id,
              "is_flat_rate" => "no",
              "interval_type" => "trial",
              "purchase_type" => 10,
              "stripe_plan_id" => null,
              "stripe_customer_id" => null,
              "status" => 1,
              "period_start" => \Carbon\Carbon::now(),
              "period_end" => \Carbon\Carbon::now()->addDays(14),
              "subscription_id" => null,
            ];

            $res = $this->purchaseTemplate($data);
            if(!empty($res)){
                if($res['success'] == true){
                    return response()->json([
                        'success' => true,
                        'message' => $res['message']
                    ], 200);
                }else{
                    throw new \Exception($res['message']);
                }
            }

            throw new \Exception("Something went wrong!");
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }


    //Purchase list
    public function purchaseList(){

        $data['page_title'] = 'Droptienda template purchase list';

        $templates = collect($this->template_service->getTemplateDetails()?? []);
        if(!blank($templates)){
            $templates = $templates->keyBy('id');
        }
        $templates = $templates->toArray();

        $data['results'] =  DB::table('template_purchases')->where('user_id', \CRUDBooster::myParentId())
            ->whereNull('deleted_at')
            ->select('identity', 'template_id', 'subscription_id', 'start_date', 'end_date', 'status', 'plan_id', 'id')
            ->get()
            ->map(function($item) use($templates) {

                $plan = $item->plan_id? dtStorePlanLabel($item->plan_id) : null;
                $expired = $item->end_date? $item->end_date < \Carbon\Carbon::now() : false;
                $is_trial = ($item->plan_id == 10)? true : false;
                $renew = (!$is_trial && $item->end_date && (int)$item->status === 1)? 'Active Renew' : null;

                $start_date = $item->start_date? date('Y-m-d', strtotime($item->start_date)) : null;
                $end_date = $item->end_date? date('Y-m-d', strtotime($item->end_date)) : null;
                $template = $templates[$item->template_id]? $templates[$item->template_id]['name'] : null;
                $template_file = $templates[$item->template_id]? $templates[$item->template_id]['template_file'] : null;

                return [
                    'id' => $item->id,
                    'template_id' => $item->template_id,
                    'template' => $template,
                    'plan_id' => $item->plan_id,
                    'plan' => $plan,
                    'status' => $item->status,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'expired' => $expired,
                    'trial' => $is_trial,
                    'renew' => $renew,
                    'subscription_id' => $item->subscription_id,
                    'template_file' => $template_file
                ];
            })
            ->toArray();

        $plans = config('dt_licenses.plans') ?? [];
        $data['dt_licenses'] = DB::table('dt_licenses')
        ->where('user_id', CRUDBooster::myParentId())
        ->get()
        ->map(function($item) use ($plans) {
            $plan = $plans[$item->plan_id];
            $expired = $item->end_date? $item->end_date < \Carbon\Carbon::now() : false;
            $is_trial = false;
            $renew = (!$is_trial && $item->end_date && (int)$item->status === 1)? 'Active Renew' : null;

            $start_date = $item->start_date? date('Y-m-d', strtotime($item->start_date)) : null;
            $end_date = $item->end_date? date('Y-m-d', strtotime($item->end_date)) : null;

            $interval   = $plan['interval'];
            $item_name  = 'Droptienda license '.$interval.'ly subscription';

            return [
                'id' => $item->id,
                'item_name' => $item_name,
                'plan_id' => $item->plan_id,
                'plan' => $interval,
                'status' => $item->status,
                'start_date' => $start_date,
                'end_date' => $end_date,
                'expired' => $expired,
                'trial' => $is_trial,
                'renew' => $renew,
                'subscription_id' => $item->subscription_id,
            ];
        })
        ->toArray();

        $flat_rate = DB::table('dt_flat_rates')->where('user_id', \CRUDBooster::myParentId())->where('end_date', '>=', \Carbon\Carbon::now())->select('end_date', 'plan_id')->first();
        $flat_rate_data = [];

        if($flat_rate){
            $flat_rate_data['plan'] = dtStorePlanLabel($flat_rate->plan_id);
            $flat_rate_data['end_date'] = $flat_rate->end_date? date('Y-m-d', strtotime($flat_rate->end_date)) : null;
        }
        
        $data['flat_rate'] = $flat_rate_data;
        return view('template_store.updatePlan', $data);
    }

    //Invoice list
    public function invoiceList(){
        $data['page_title'] = 'DT template invoices';
        $data['orders'] = \App\NewOrder::where('cms_user_id', 2439)->where('cms_client', \CRUDBooster::myParentId())->orderBy('id','desc')->get();
        return view('template_store.invoiceList', $data);
    }

    public function purchase_invoice_view($order_id){
        return app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_invoice_pdf($order_id);
    }

    //Cancel subscription
    public function templateCancelSubscription($id){

        //DB::beginTransaction();
        try{
            $subscription_id = DB::table('template_purchases')->where('user_id', \CRUDBooster::myParentId())->where('id', $id)->value('subscription_id');
            if(empty($subscription_id)) throw new \Exception("Invalid action!");

            resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2439', '', $subscription_id);


            DB::table('template_purchases')->where('user_id', \CRUDBooster::myParentId())->where('id', $id)->update(['status' => 0]);
            //DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Subscription canceled successfully!'
            ], 200);
            
        }catch(\Exception $e){
            //DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    //Cancel flat rate
    public function templateCancelFlatRate(){
        //DB::beginTransaction();
        try{
            $subscription = DB::table('dt_flat_rates')->where('user_id', \CRUDBooster::myParentId())->where('end_date', '>=', \Carbon\Carbon::now())->where('status', 1)->select('subscription_id', 'id')->first();
            if(empty($subscription)) throw new \Exception("Invalid action!");
            $subscription_id = $subscription->subscription_id;

            resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2439', '', $subscription_id);

            DB::table('dt_flat_rates')->where('user_id', \CRUDBooster::myParentId())->where('id', $subscription->id)->update(['status' => 0]);
            //DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Subscription canceled successfully!'
            ], 200);
            
        }catch(\Exception $e){
            //DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    //Unsubscribe old subscriptions
    private function unsubscribeOldPlan($template_id, $user_id, $current_subscription){
        //Unsubccribe old subscription
        $old_subscription_id = DB::table('template_purchases')->where(['user_id' => $user_id, 'template_id' => $template_id])->whereNotNull('subscription_id')->value('subscription_id');
        //Cancel old subscription
        
        if(!empty($old_subscription_id)){
            resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2439', '', $old_subscription_id);
        }
    }

    //unsubscribe flat rate subscription
    private function unsubscribeFatRateSubscription($user_id, $current_subscription){
        //Unsubccribe old flat rates
        $old_flat_subscription_id = DB::table('dt_flat_rates')->where(['user_id' => $user_id])->whereNotNull('subscription_id')->value('subscription_id');
        
        //Cancel old subscription
        if(!empty($old_flat_subscription_id)){
            resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2439', '', $old_flat_subscription_id);
        }
    }



    //Stripe webhook recuring invoices
    public function DtPaymentSuccessWebhook()
    {
        $data = json_decode(request()->getContent(), true);
        Log::channel('uscreen')->info($data);

        if ($data['type'] == 'invoice.payment_succeeded') {

            $sub_id = $data['data']['object']['lines']['data'][0]['subscription'];
            $price = ($data['data']['object']['lines']['data'][0]['amount'] / 100);
            $startDate = date("Y-m-d", $data['data']['object']['lines']['data'][0]['period']['start']);
            $endDate = date("Y-m-d", $data['data']['object']['lines']['data'][0]['period']['end']);

            $inv_id = $data['data']['object']['id'];

            //Template purchase
            $template = DB::table('template_purchases')->whereNotNull('subscription_id')->where('subscription_id', $sub_id)->select('id', 'user_id', 'template_id', 'plan_id')->first();

            //Template flat tariff
            $template_tariff = DB::table('dt_flat_rates')->whereNotNull('subscription_id')->where('subscription_id', $sub_id)->select('id', 'user_id', 'plan_id')->first();

            //Dt new tariff
            $dt_new_tariff = DB::table('dt_tariff_purchases')->whereNotNull('subscription_id')->where('subscription_id', $sub_id)->select('id', 'user_id', 'plan_id')->first();

            $table = null;
            $table_id = null;
            $user_id = null;

            $inv_name = null;


            //Plan related data
            $plan_id = null;
            $template_id = null;

            //Generate inv global data
            if($template){
                $table = 'template_purchases';
                $table_id = $template->id;
                $user_id = $template->user_id;
                $template_id = $template->template_id;
                $plan_id = $template->plan_id;

                $inv_name = 'DT template ID: '.$template->template_id.' subscription';

            }elseif($template_tariff){

                $table = 'dt_flat_rates';
                $table_id = $template_tariff->id;
                $user_id = $template_tariff->user_id;

                $template_id = 'all';
                $plan_id = $template->plan_id;

               $inv_name = 'DT template flat-rate subscription';
            }else if($dt_new_tariff){

                $table = 'dt_tariff_purchases';
                $table_id = $dt_new_tariff->id;
                $user_id = $dt_new_tariff->user_id;

                $plan_id = $dt_new_tariff->plan_id;

                $inv_name = 'DT tariff subscription';

            }


            //Check data
            if(empty($table) || empty($table_id) || empty($user_id)) return false;

            //Update subscription date
            $is_update = DB::table($table)->where('id', $table_id)->update(['start_date' => $startDate, 'end_date' => $endDate]);
            // if(!$is_update) return false;


            //Get template name from api
            try{
                $item = app('App\Services\TemplateStoreService')->purchaseItem($template_id, $plan_id);
                if(!empty($item)){
                    $template_name = $item['name']?? null;;
                    $interval = $item['interval']?? null;

                    $name = $template_name.' '.$interval.'ly subscription';
                    $name = trim(preg_replace('/\s\s+/', ' ', str_replace("\n", " ", $name)));
                    $inv_name = $name?? $inv_name;
                }
            }catch(\Exception $e){}

            //Order data
            $taxShow = config('global.tax_for_invoice');
            $total_tax = ($price * $taxShow) / 100;
            $order_info = [
                'user_id' => 2439,  //STRIPE_CLIENT_DT
                'cms_client' => $user_id,
                'order_date' => date('Y-m-d H:i:s'),
                'total' => round(($price), 2),
                'sub_total' => round($price - $total_tax, 2),
                'total_tax' => round($total_tax, 2),
                'payment_type' => "Stripe Card",
                'status' => 'paid',
                'currency' => "EUR",
                'adjustment' => 0,
                'insert_type' => 3,
                'shop_id' => 8,
                'order_id_api' => $inv_id,
            ];

            if($dt_new_tariff){
                $user_shop_id = DB::table('shops')->where('user_id', $user_id)->where('channel', Channel::DROPTIENDA)->value('id');

                $order_info['insert_type'] = \App\Enums\InsertType::DT_LICENSE;
                $order_info['shop_id'] = $user_shop_id ?? NULL;
            }


            $carts = [];
            $cart_item = [];
            $cart_item['id'] = 1;
            $cart_item['product_name'] = $inv_name;
            $cart_item['description'] = 'Subscription Start from ' . $startDate . ' to ' . $endDate . '. Payment by Stripe.';
            $cart_item['qty'] = 1;
            $cart_item['rate'] = round($price, 2);
            $cart_item['tax'] = $taxShow;
            $cart_item['product_discount'] = 0;
            $cart_item['amount'] = round($price, 2);

            if($dt_new_tariff){
                unset($cart_item['rate']);
                $cart_item['plan_id'] = $plan_id;
                $cart_item['sub_start_date'] = $startDate;
                $cart_item['sub_end_date'] = $endDate;
            }

            $carts[] = $cart_item;
            $order_info['cart'] = json_encode($carts);

            // $order_exist = NewOrder::where(['order_id_api' => $sub_id, 'cms_client' => $result->cms_user_id, 'shop_id' => 8, 'cms_user_id' => 98])->whereYear('created_at', '=', date('Y'))->whereMonth('created_at', '=', date('m'))->select('id')->first();
            // if ($order_exist && $order_exist->id) return;

            if(NewOrder::where('order_id_api', $inv_id)->exists()){
                DB::table('new_orders')->where('order_id_api', $inv_id)->update(['status' => 'paid']);
               return 'App subscription Inv already Exist';
            }

            app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $user_id);
        }
    }




    //Purchase droptienda license
    public function dtLicensePurchaseModal(Request $request)
    {
        try {

            $user_id = CRUDBooster::myParentId();
            $used_plan = \DB::table('dt_licenses')->where('user_id', $user_id)->where('end_date', '>=', \Carbon\Carbon::now())->value('plan_id');

            $is_used = false;
            if(!empty($used_plan)){
                $is_used = true;
            }

            // $plans = config('dt_licenses.plans') ?? [];
            $plans = DB::table('import_plans')->where('status', 1)->where('tariff_active', 1)->get();

            $item = [
                'name' => "Droptienda Tariff Plan",
                'description' => 'Deine Supportlizenz ist 365 Tage gültig. Unseren Support erreichst du per E-Mail oder telefonisch rund um die Uhr, täglich. Du bekommst auf deine Fragen eine direkte und vertrauliche Antwort. Deine Fragen oder Anliegen werden nicht öffentlich in der Gruppe diskutiert. Ein direkter Ansprechpartner und eine vertrauliche Kommunikation sind von unschätzbarem Wert, wenn du deinen Webshop professionell betreiben möchtest. Auf Wunsch installieren dir auch unkompliziert und kostenfrei deinen Shop bei deinem Wunschprovider, damit du direkt starten kannst.',
                'plans' => $plans,
                'used_plan' => $used_plan,
                'is_used' => $is_used,
            ];

            if(empty($plans)) throw new \Exception('No license plan avaliable');

            $item['image'] = asset('images/icons/dt_support.png');

            //User term
            $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
            $user = User::with('billing_detail')->find($user_id);
            $term = ($privacy) ? $privacy->page_content : '';
            $user_data = '<div id="customer_data_term"></div>';
            if ($user->billing_detail) {
                $billing = $user->billing_detail;
                $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
            }
            if (strpos($term, '{customer}') !== false) {
                $term = str_replace('{customer}', $user_data, $term);
            }

            $hasPaypal = app(DRMService::class)->paypalCheck(User::DROPMATIX_ACCOUNT_ID);

            return response()->json([
                'success' => true,
                'html' => view('template_store.partials.dt_license_purchase_modal', compact('item', 'term', 'used_plan', 'hasPaypal'))->render(),
            ], 200);

            throw new \Exception('Invalid action');
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }


    //Purchase droptienda license 
    public function purchaseDroptiendaLicense($purchase_data)
    {
        //DB::beginTransaction();
        try {
            $intend_id = $purchase_data['id'];
            $plan_id = $purchase_data['plan_id'];
            $interval = $purchase_data['interval_type'];


            if (is_null($plan_id)) {
                throw new \Exception('You are not accessible to use this feature, Please Contact with DRM Customer Care!');
            } else {


                $discount = $purchase_data['discount'] ?? 0;
                $total = $purchase_data['total'] ?? 0;
                $sub_total = $purchase_data['sub_total'] ?? 0;

                if(NewOrder::where(['order_id_api' => $intend_id, 'cms_user_id' => 2439, 'shop_id' => 8])->exists()) throw new \Exception('Already purchased!');  //STRIPE_CLIENT_DT

                $item_name = 'Droptienda license '.$interval.'ly subscription';
                $item_description = $intend_id;
                $subscription_id = $purchase_data['subscription_id'];

                $tax_rate = 21;
                $total_tax = 0;
                $has_vat_number = false;
                $vat_number = null;
                $tax_version = null;

                if( isset($purchase_data['main_amount']) ) {
                    $sub_total = $purchase_data['main_amount'];
                    $tax_version = 1;
                    $vat_number = $purchase_data['vat_number'] ?? null;
                    $has_vat_number = !empty($vat_number);
                    $tax_rate = $has_vat_number ? 0 : 21;
                    $total_tax = $purchase_data['total_tax'];
                }


                //Increment single pay coupon usages
                if (isset($purchase_data['coupon']) && $purchase_data['coupon']) {
                    DB::table('coupons')->where('coupon_id', $purchase_data['coupon'])->increment('single_pay');
                }

                //cms client
                $cms_client = $purchase_data['user_id'];
                $user = User::with('billing_detail')->find($cms_client);
                if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

                //Initial purchase data
                $subscription_data = [
                    'identity' => $purchase_data['id'],
                    'purchase_data' => json_encode($purchase_data)
                ];

                //Initialize subscription data
                $subscription_data['subscription_id']       =   $purchase_data['subscription_id'];
                $subscription_data['start_date']            =   $purchase_data['period_start'];
                $subscription_data['end_date']              =   $purchase_data['period_end'];
                $subscription_data['plan_id']               =   $plan_id;
                $subscription_data['stripe_customer_id']    =   $purchase_data['stripe_customer_id']?? null;

                $subscription_data['created_at']            =   now();
                $subscription_data['updated_at']            =   now();
                $subscription_data['total']                 =   $total;
                $subscription_data['sub_total']             =   $sub_total;
                $subscription_data['discount']              =   $discount;

                //Status
                $subscription_data['status'] = 1;

                //Unsubscribe old plan
                $old_subscription_id = DB::table('dt_licenses')->where('user_id', $cms_client)->where('subscription_id', '!=', $subscription_id)->value('subscription_id');

                if(!empty($old_subscription_id)){
                    resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2439', '', $old_subscription_id);
                }
                
                //Subscription create or insert
                DB::table('dt_licenses')->updateOrInsert(['user_id' => $cms_client], $subscription_data);

                $payment_intend_id = $purchase_data['intend_id'] ?? null;

                $order_info = [
                    'user_id' => 2439,  //STRIPE_CLIENT_DT
                    'cms_client' => $cms_client,
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => round(($total), 2),
                    'sub_total' => round($sub_total, 2),
                    'discount' => round($discount, 2),
                    'discount_type' => 'fixed',
                    'total_tax' => $total_tax,
                    'payment_type' => 'Stripe',
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => \App\Enums\InsertType::DT_LICENSE,
                    'shop_id' => 8,
                    'order_id_api' => $intend_id,
                    'intend_id' => $payment_intend_id,
                    'vat_number' => $vat_number,
                    'tax_version' => $tax_version,
                ];

                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = $item_name;
                $cart_item['description'] = $item_description;
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($sub_total, 2);
                $cart_item['tax'] = $tax_rate;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($sub_total, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);
                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $cms_client);
            }

            //DB::commit();    // Commiting  ==> There is no problem whatsoever
            return ['success' => true, 'message' => 'Successfully purchase droptienda license!'];
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }


    //Cancel license 
    public function dtLicenseCancel($id) {
        try {
            $stripe_cancel = false;
            $message = "";

            //Unsubscribe old plan
            $old_subscription_id = DB::table('dt_licenses')->where('user_id', CRUDBooster::myId())->where('subscription_id', '!=', $subscription_id)->value('subscription_id');

            if(!empty($old_subscription_id)){
                resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2439', '', $old_subscription_id);
            }

            if($stripe_cancel) {
                DB::table('dt_licenses')->where('user_id', CRUDBooster::myId())->update(['status' => 0]);
                return response()->json([
                    'success' => true,
                    'message' => "Subscription canceled!"
                ]);
            }

            throw new \Exception("Subscription cancelation failed!");

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

}
