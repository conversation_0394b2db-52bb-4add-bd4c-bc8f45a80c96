<?php namespace App\Http\Controllers;

	use App\Enums\Apps;
    use App\Services\AppStoreService;
    use crocodicstudio\crudbooster\controllers\CBController;
    use crocodicstudio\crudbooster\helpers\CRUDBooster;
	use DB;
	use Illuminate\Support\Facades\Cache;
	use Illuminate\Support\Facades\Storage;
	use Illuminate\Support\Str;
	use Illuminate\Support\Facades\Redis;
	use Illuminate\Support\LazyCollection;
	use Illuminate\Support\Arr;
    use Request;
	class AdminEanController extends CBController {
			public $insertArray = [];
			public $existing_eans = [];
			public $count;
			public $total;



	    public function cbInit() {
			$this->checkPurchased();
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = true;
			$this->button_export = false;
			$this->table = "custom_eans";
			$user_id = CRUDBooster::myId();
			$this->col = [];
			$this->col[] = ["label"=>"EAN","name"=>"ean"];
			$this->col[] = ["label"=>"Used","name"=>"(SELECT id FROM drm_products where drm_products.ean = custom_eans.ean and drm_products.user_id=".$user_id." limit 1) as product_id"];
			// $this->col[] = ["label"=>"Connected Product","name"=>"(SELECT image FROM drm_products WHERE id = custom_eans.product_id) as image","image"=>true];
			$this->col[] = ["label"=>"Product URL","name"=>"(SELECT id FROM drm_products where drm_products.ean = custom_eans.ean and drm_products.user_id=".$user_id." limit 1) as product_id"];
			$this->form = [];

			$this->form[] = ['label'=>'Ean','name'=>'ean','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
	        $this->sub_module = array();
	        $this->addaction = array();
	        $this->button_selected = array();
	        $this->alert= array();
	        $this->index_button = array();

					if((session()->get('unfinished_'.$user_id))!=null){
						$import_id = session()->get('unfinished_'.$user_id);
						$this->index_button[] = ['label'=>'Complete Product Import','url'=>CRUDBooster::adminPath('drm_imports/import?id='.$import_id),'icon'=>'fa fa-forward','color'=>'warning'];
					}


	        $this->table_row_color = array();
	        $this->index_statistic = array();
	        $this->script_js = NULL;
	        $this->pre_index_html = null;
	        $this->post_index_html = null;
	        $this->load_js = array();
	        $this->style_css = NULL;
	        $this->load_css = array();


	    }

	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


			public function getAdd(){
				$this->checkPurchased();
				return view('admin.ean_module.add');
			}

			public function postAddSave(){
				$this->checkPurchased();
				$request = Request::input();
				$eans = $request['ean'];
				$existing_eans = $this->getEanCache();
				$insert = [];
				foreach($eans as $key => $value){
					if(strlen($value) == 13){
						if(!in_array($value,$existing_eans)){
							$insert[] = [
								'ean' => $value,
								'user_id' => CRUDBooster::myId(),
								'used' => 0
							];
						}
					}
				}
				if(count($insert)){
					DB::table('custom_eans')->insert($insert);
					CRUDBooster::redirect(CRUDBooster::mainpath(),"EAN Added Successfully","success");
				}
				else {
					CRUDBooster::redirect(CRUDBooster::mainpath(),"No Valid EAN","warning");
				}
			}

	    public function hook_query_index(&$query) {
					if(!CRUDBooster::isSuperAdmin()){
						$query->where('user_id',CRUDBooster::myId());
					}
	    }

	    public function hook_row_index($column_index,&$column_value) {
				if($column_index == 2){
					if(!$column_value){
						$column_value = "<span style='color:green;font-weight:bold'>Not Used</span>";
					}
					else{
						$column_value = "<span style='color:red;font-weight:bold'>Used</span>";
					}
				}

				// if($column_index == 3){
				// 	$images = json_decode($column_value);
				// 	if($images!=null){
				// 		$column_value = "<a data-lightbox='roadtrip' href='".$images[0]->src."'><img src='".$images[0]->src."' alt='' /></a>";
				// 	}
				// 	else {
				// 		$column_value = "";
				// 	}
				// }

				if($column_index == 3){
					if($column_value!=null){
						$column_value = "<a href='".CRUDBOOster::adminPath('drm_products/detail/'.$column_value)."'><button type='button' class='btn btn-info btn-xs'>View Product</button></a>";
					}

				}

	    }

	    public function hook_before_add(&$postdata) {
					$exists = $this->checkExists($postdata['ean']);
					if($exists){
						CRUDBooster::redirect(CRUDBooster::mainpath(),"EAN already exists","warning");
					}
					else {
						if(strlen($postdata['ean'])==13){
							$postdata['user_id'] = CRUDBooster::myId();
							$postdata['used'] = 0;
						}
						else {
							CRUDBooster::redirect(CRUDBooster::mainpath(),"Invalid EAN","warning");
						}
					}
	    }


	    public function hook_after_add($id) {
	        //Your code here

	    }

	    public function hook_before_edit(&$postdata,$id){
					if(strlen($postdata['ean'])==13){
						$postdata['user_id'] = CRUDBooster::myId();
					}
					else {
						CRUDBooster::redirect(CRUDBooster::mainpath(),"Invalid EAN","warning");
					}
	    }

	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    public function hook_after_delete($id){

	    }

			public function getImportData(){
				return view('admin.ean_module.import');
			}

		public function postImportData(){
			$this->checkPurchased();
			$request = Request::input();
			$type = $request['file_type'];
			$delimiter = $request['delimiter'];
			$rand=Str::random(40);
			if($type == 1){
				$file = Request::file('csv_file');
				$extention = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
				if($extention == 'csv' || $extention == 'txt'){
					$data = file_get_contents($file->getRealPath());
					Storage::disk('spaces')->put('public/csv_files/'.$rand.'.'.$extention,$data,'public');
					$path = 'public/csv_files/'.$rand.'.'.$extention;
				}
				else {
					$path = $file->storeAs('public/csv_files',$rand.'.'.$extention,['visibility'=>'public','disk'=>'spaces']);
				}
			}
			else{
				$type='url';
				$url_file=trim($request['csv_link']);
				$csv_data= file_get_contents($url_file);
				Storage::disk('spaces')->put('public/csv_files/'.$rand.'.csv',$csv_data,'public');
				$path = 'public/csv_files/'.$rand.'.csv';
			}

			$import_controller = new AdminDrmImportsController;
			$csv = $import_controller->csvToCsvHeaderJson($path,$delimiter,true,true);

			if($csv!=false){
				$csv_headers = makeArrayUtf8(makeArrayUtf8($csv[0]));
				$csv_headers = array_map('removeDots',$csv_headers);
				$csv_headers = removeNulls($csv_headers);
				try{
					$redis = Redis::connection();
					$redis->set('ean_import_'.CRUDBooster::myId(),json_encode($csv));
				}catch (\Predis\Connection\ConnectionException $e){}

				$data =[
					'headers' => $csv_headers,
					'path' => $path,
					'delimiter' => $delimiter
				];
				return view('admin.ean_module.import_mapping',$data);
			}
			else {
				CRUDBooster::redirect(CRUDBooster::mainpath(),"Invalid File","danger");
			}
		}

			public function postImportDataSave(){
				$message = ['text' => 'Processing...'];
				sentProgress($message,'import');
				$this->checkPurchased();
				$request = Request::input();
				$path = $request['path'];
				$ean_field = $request['ean'];
				$delimiter = $request['delimiter'];
				$user_id = CRUDBooster::myId();
				$csv_data = $this->getCsv($user_id,$path,$delimiter);

				$this->existing_eans = $this->getEanCache();
				$key = array_map('trim', $csv_data[0]);
				$key = array_map('removeDots',$key);
				$key = makeArrayUtf8(makeArrayUtf8($key));
				$key_count = count($key);
			  unset($csv_data[0]);
				$this->total = count($csv_data);

				$message = ['text' => 'Starting Import...'];
				sentProgress($message,'import');

				LazyCollection::make(function () use ($csv_data,$key_count){
					foreach($csv_data as $line)
					{
						if(count($line)==$key_count && !containsOnlyNull($line)){
							yield makeArrayUtf8(makeArrayUtf8($line));
						}
					}
				})
				->chunk(500)
				->each(function ($lines) use ($ean_field,$user_id,$key){
					$this->insertArray = array();
					array_walk($lines, function ($chunks) use($ean_field,$key,$user_id){
						array_walk($chunks, function ($chunk) use($ean_field,$key,$user_id){
							$data = array_combine($key, $chunk);
							$data = makeArrayUtf8(makeArrayUtf8($data));
							$ean = trim($data[$ean_field]);
							if($ean!=null && !in_array($ean,$this->existing_eans) && strlen($ean)==13){
								$this->insertArray[] = [
									'ean' => $ean,
									'user_id' => $user_id,
									'used' => 0
								];
							}
							$this->count++;
						});
					});

					if(is_array($this->insertArray)){
						$last = end($this->insertArray);
						$name = $last['ean'];
						DB::table('custom_eans')->insert($this->insertArray);
					}
					$message = ['total' => $this->total, 'count' => $this->count,'percent' => round(($this->count/$this->total)*100,2),'name' => $name];
					sentProgress($message,'import');
				});
				Storage::disk('spaces')->delete($path);
				CRUDBooster::redirect(CRUDBooster::mainpath(),"EAN Imported Successully","success");
			}

			public function getCsv($user_id,$path,$delimiter){
				$type = pathinfo($path, PATHINFO_EXTENSION);
				try{
					$redis = Redis::connection();
					$json = $redis->get('ean_import_'.$user_id);
					if($json){
						$csv_data = json_decode($json);
					}
				}catch (\Predis\Connection\ConnectionException $e){}
					if(!$csv_data){
							$import_controller = new AdminDrmImportsController;
							$csv_data = $import_controller->csvToArrayModified($path,$type,$delimiter);
							try{
								$redis = Redis::connection();
								$redis->set('ean_import_'.$user_id,json_encode($csv_data));
							}catch (\Predis\Connection\ConnectionException $e){}
					}
					return $csv_data;
			 }


			public function checkExists($ean){
				$all_eans = $this->getEanCache();
				if(!in_array($ean,$all_eans)){
					return false;
				}
				else {
					return true;
				}
			}

			public function getEanCache(){
				$user_id = CRUDBooster::myId();
				$existing_products = Cache::remember('existing_product_eans_'.$user_id,05.0, function() use($user_id){
					$existing = DB::table('drm_products')->where('user_id',$user_id)->whereNull('deleted_at')->pluck('ean')->toArray();
					return $existing;
				});

				$existing_eans = Cache::remember('existing_eans_'.$user_id,05.0, function() use($user_id){
					$existing = DB::table('custom_eans')->where('user_id',$user_id)->pluck('ean')->toArray();
					return $existing;
				});

				$all_eans = array_merge($existing_products,$existing_eans);
				return $all_eans;
			}

			public function getUnusedCount(){
				$user_id = CRUDBooster::myId();
				$eans = Cache::remember('eans_count_'.$user_id,05.0, function() use($user_id){
					$existing = DB::table('custom_eans')->where('user_id',$user_id)->where('used',0)->count();
					return $existing;
				});
				return $eans;
			}

			public function getInsertMultiple(){
				$this->checkPurchased();
				return view('admin.ean_module.insert_multiple');
			}

			public function postSaveMultipleEans(){
				$message = ['text' => 'Processing...'];
				sentProgress($message,'import');

				$this->checkPurchased();

				$eans = $_REQUEST['eans'];
				$user_id = CRUDBooster::myId();

				$message = ['text' => 'Validating EANs...'];
				sentProgress($message,'import');

				preg_match_all('/\d+/', $eans, $matches);
				$flattened = Arr::flatten($matches);
				$unique_eans = array_unique($flattened);
				$existing_eans = $this->getEanCache();
				$new_unique_eans = array_diff($unique_eans,$existing_eans);

				$total = count($new_unique_eans);
				foreach (array_chunk($new_unique_eans,500) as $chunk) {
					$insertArray = [];
					foreach ($chunk as $key => $value) {
						if(strlen($value) == 13){
							$insertArray[] = [
								'ean' => $value,
								'user_id' => $user_id,
								'used' => 0
							];
						}
						$count++;
					}
					if(is_array($insertArray)){
						$last = end($insertArray);
						$name = $last['ean'];
						DB::table('custom_eans')->insert($insertArray);
					}
					$message = ['total' => $total, 'count' => $count,'percent' => round(($count/$total)*100,2),'name' => $name];
					sentProgress($message,'import');
				}
				return $count;
			}

			public function checkPurchased(): bool
            {
                $purchased = app(AppStoreService::class)->checkAppPurchased(Apps::EAN_MANAGER,CRUDBooster::myParentId());
				if(!$purchased){
					CRUDBooster::redirect(CRUDBooster::adminPath(),"Please purchase the app","warning");
				}
				return true;
			}

	}
