<?php

namespace App\Http\Controllers;

use App\BlockUserHistory;
use App\CampainTag;
use App\DropFunnelCountDown;
use App\DropfunnelCustomerTag;
use App\EmailMarketing;
use App\Enums\Channel;
use App\Facades\DRM;
use App\HtmlDropFunnelTemplate;
use App\DropFunnelSignature;
use App\Mail\DRMSEndMail;
use App\NewCustomer;
use App\SenderEmailSetting;
use App\Traits\EmailMarketings;
use App\Traker;
use App\User;
use App\DropfunnelTag;
use App\TrakerPageView;
use App\TrakerTimeSpent;
use PDF;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Http\Request as LaravelRequest;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Request;
use Exception;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use crocodicstudio\crudbooster\helpers\CB;
use App\Notifications\DRMNotification;
use App\MailGunWebHookHistory;
use League\Csv\Writer;
use App\Services\ChannelProductService;
use Illuminate\Support\Arr;
use App\DrmProduct;
Use App\MarketingEmailSettingByChannel;
use App\DropfunnelStepMail;
use Illuminate\Http\JsonResponse;
use App\Services\Mailgun\Dropfunel;


class AdminEmailMarketingsController extends \crocodicstudio\crudbooster\controllers\CBController
{
    use EmailMarketings;
    use Dropfunel;


    public function cbInit()
    {


//        if (!CRUDBooster::isSuperadmin()) {
//            $app_id = config('global.email_marketing_app_id');
//            if (!DrmUserHasPurchasedApp(CRUDBooster::myParentId(), $app_id)) {
//                CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
//            }
//        }

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "name";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = true;
        $this->button_table_action = false;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
//        if(isLocal()){
            $this->button_add = false;
//        }else{
//            $this->button_add = true;
//        }
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "email_marketings";

        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => __("Campaign Edit"), "name" => "id"];
        $this->col[] = ["label" => __("Campaign Name"), "name" => "campaign_name", 'type' => 'title'];
        // $this->col[] = ["label" => __("Subject"), "name" => "name"];
        $this->col[] = ["label" => __("Email Campaign Status"), "name" => "status", "skip_translate" => true];
        // $this->col[] = ["label" => __("Number of mails"), "name" => "id"];
        // $this->col[] = ["label"=>"Active At The Moment","name"=>"active_at_the_moment"];
//        $this->col[] = ["label" => __("Ever Started"), "name" => "ever_started"];
        $this->col[] = ["label" => __("Tags"), "name" => "id"];
        // $this->col[] = ["label"=>"Number of mails","name" => "id"];
        // $this->col[] = ["label" => __("Delivered Rate"), "name" => "id"];

        // $this->col[] = ["label" => __("Click Rate"), "name" => "id"];
        // $this->col[] = ["label" => __("Open Rate"), "name" => "id"];
        // $this->col[] = ["label" => __("Unsubscribe"), "name" => "id"];

        $this->col[] = ["label" => __("Ever Started"), "name" => "id"];
        $this->col[] = ["label" => __("Campaign Active"), "name" => "id"];
        $this->col[] = ["label" => __("Campaign Finished"), "name" => "completed"];
        // $this->col[] = ["label" => __("Click Rate"), "name" => "id"];


        $this->col[] = ["label" => __("Action")];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'Name', 'name' => 'name', 'type' => 'text', 'validation' => 'required|string|min:3|max:190', 'width' => 'col-sm-10', 'placeholder' => 'You can only enter the letter only'];
        $this->form[] = ['label' => 'Email Template', 'name' => 'email_template', 'type' => 'wysiwyg', 'validation' => 'required|string|min:5', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Status', 'name' => 'status', 'type' => 'text', 'validation' => 'nullable|min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Active At The Moment', 'name' => 'active_at_the_moment', 'type' => 'number', 'validation' => 'required|integer|min:0', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Ever Started', 'name' => 'ever_started', 'type' => 'number', 'validation' => 'nullable|integer|min:0', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Completed', 'name' => 'completed', 'type' => 'number', 'validation' => 'nullable|integer|min:0', 'width' => 'col-sm-10'];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ["label"=>"Name","name"=>"name","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
        //$this->form[] = ["label"=>"Email Template","name"=>"email_template","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Status","name"=>"status","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Active At The Moment","name"=>"active_at_the_moment","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
        //$this->form[] = ["label"=>"Ever Started","name"=>"ever_started","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
        //$this->form[] = ["label"=>"Completed","name"=>"completed","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key    = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color       = Default is primary. (primary, warning, succecss, info)
        | @showIf      = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
//        $this->addaction = array();
//        $this->addaction[] = ['label' => 'Send Test Email', 'url' => 'javascript:send_test_mailable([id])', 'color' => 'default'];
//        if (CRUDBooster::isSuperadmin() || in_array(CRUDBooster::myParentId(), [62, 212])) $this->addaction[] = ['label' => 'Log', 'url' => 'javascript:drm_campaign_log([id])', 'color' => 'warning'];
//        $this->addaction[] = ['label' => 'Share', 'url' => 'javascript:drm_campaign_share([id])', 'color' => 'info'];
//        if (isLocal() || in_array(CRUDBooster::myParentId(), [212])) {
//            $this->addaction[] = ['label' => 'Add Tag', 'url' => 'javascript:step_mail_tag_add([id])', 'color' => 'success'];
//            $this->addaction[] = ['label' => 'Remove Tag', 'url' => 'javascript:step_mail_tag_remove([id])', 'color' => 'danger'];
//        }
        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon        = Icon from fontawesome
        | @name        = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = [];


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $app_id = config('global.marketing_plan_app_id');
        $data_url = CRUDBooster::adminPath('app_form');
        $this->index_button = array();

        if(isLocal()){
            // $this->index_button[] = ['label' => 'Neues Optin', 'url' => CRUDBooster::adminPath('opln_mails/add'), 'icon' => 'fa fa-optin-monster', 'color' => 'gray'];
            // $this->index_button[] = ['label' => 'Listenübersicht', 'url' => CRUDBooster::adminPath('opln_mails'), 'icon' => 'fa fa-th-list', 'color' => 'gray'];
            // if (!CRUDBooster::isSuperadmin()) {
            //     if (!DrmUserHasPurchasedApp(CRUDBooster::myParentId(), $app_id)) {
            //         $this->index_button[] = ['label' => 'Funnel Planner', 'url' => 'javascript:void(0)', 'icon' => 'fa fa-object-ungroup', 'color' => 'gray btn-modal plan-modal'];
            //     } else {
            //         $this->index_button[] = ['label' => 'Funnel Planner', 'url' => CRUDBooster::adminPath('marketing_plan'), 'icon' => 'fa fa-object-ungroup', 'color' => 'gray'];
            //     }
            // } else {
            //     $this->index_button[] = ['label' => 'Funnel Planner', 'url' => CRUDBooster::adminPath('marketing_plan'), 'icon' => 'fa fa-object-ungroup', 'color' => 'gray'];
            // }
            // $this->index_button[] = ['label' => 'Contact Form', 'url' => CRUDBooster::adminPath('contact_forms'), 'icon' => 'fa fa-file-o ', 'color' => 'gray'];
            // $this->index_button[] = ['label' => 'Email Sender Configuration', 'url' => CRUDBooster::mainPath('sender-email-setting'), 'icon' => 'fa fa-envelope-o', 'color' => 'danger'];
            // $this->index_button[] = ['label' => 'Signature', 'url' => CRUDBooster::adminPath('drop_funnel_signatures'), 'icon' => 'fa fa-pencil', 'class ' => 'check-app-purchase', 'color' => 'gray'];
        }else{
            // $this->index_button[] = ['label' => 'Neues Optin', 'url' => CRUDBooster::adminPath('opln_mails/add'), 'icon' => 'fa fa-optin-monster', 'color' => 'gray'];
            // $this->index_button[] = ['label' => 'Listenübersicht', 'url' => CRUDBooster::adminPath('opln_mails'), 'icon' => 'fa fa-th-list', 'color' => 'gray'];
            // $this->index_button[] = ['label' => 'Signature', 'url' => CRUDBooster::adminPath('drop_funnel_signatures'), 'icon' => 'fa fa-pencil', 'class ' => 'check-app-purchase', 'color' => 'gray'];
            // if (!CRUDBooster::isSuperadmin()) {
            //     if (!DrmUserHasPurchasedApp(CRUDBooster::myParentId(), $app_id)) {
            //         $this->index_button[] = ['label' => 'Funnel Planner', 'url' => 'javascript:void(0)', 'icon' => 'fa fa-object-ungroup', 'color' => 'gray btn-modal plan-modal'];
            //     } else {
            //         $this->index_button[] = ['label' => 'Funnel Planner', 'url' => CRUDBooster::adminPath('marketing_plan'), 'icon' => 'fa fa-object-ungroup', 'color' => 'gray'];
            //     }
            // } else {
            //     $this->index_button[] = ['label' => 'Funnel Planner', 'url' => CRUDBooster::adminPath('marketing_plan'), 'icon' => 'fa fa-object-ungroup', 'color' => 'gray'];
            // }

            // $this->index_button[] = ['label' => 'Countdown', 'url' => CRUDBooster::adminPath('drop_funnel_count_downs'), 'icon' => 'fa fa-clock-o ', 'color' => 'gray'];
            // $this->index_button[] = ['label' => 'Contact Form', 'url' => CRUDBooster::adminPath('contact_forms'), 'icon' => 'fa fa-file-o ', 'color' => 'gray'];
            // $this->index_button[] = ['label' => 'Callback Calendar', 'url' => CRUDBooster::adminPath('appointment_booking'), 'icon' => 'fa fa-calendar-o ', 'color' => 'gray'];


            // $this->index_button[] = ['label' => 'Email Sender Configuration', 'url' => CRUDBooster::mainPath('sender-email-setting'), 'icon' => 'fa fa-envelope-o', 'color' => 'danger'];
        }


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = "
            $('.plan-modal').attr('data-url','" . $data_url . "');
            $('.plan-modal').attr('data-id','" . $app_id . "');
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name=\"csrf-token\"]').attr('content')
                }
            });
            $('#btn_add_new_data').removeClass('btn-orange').addClass('btn-gray');
            let checkedValue = [];
            $(document).on('click','.shareCheck',function(){
                if ($(this).children('input').is(':checked')) {
                    let val = $(this).children('input').val();
                    let index = checkedValue.indexOf(val);
                    if (index < 0) {
                        checkedValue.push(val);
                    } else {
                        checkedValue.splice(index, 1);
                    }
                }
            });

            $(document).on('click','.share-with-user',function() {
                let id = $(this).data('id');
                $.ajax({
                    method: 'post',
                    url: '" . route('share-campaign-with-user') . "',
                    data: {id:id,user_ids:checkedValue},
                    success:function(response) {
                        $('#testMailForEmailMarketing').modal('hide');
                        if (response.success) {
                            swal('Success!!',response.message,'success');
                        }
                    },
                    error:function(jqXHR, textStatus, errorThrown) {
                        swal('Oops!!',jqXHR.responseJSON.message,'error');
                    }
                });
            });

            function copyToClipBoard(id) {
              /* Get the text field */
              var copyText = document.getElementById(id);
              copyText.select();
              copyText.setSelectionRange(0, 99999); /* For mobile devices */
              document.execCommand('copy');
              return copyText.value;
            }

            $(document).on('click','.generate-shareable-link, .copy-shareable-link',function(){
                let id = $(this).data('id');
                $.ajax({
                    method: 'post',
                    url: '" . route('generate-sharable-link') . "',
                    data: {id:id},
                    success: function(response) {
                        if (response.success) {
                            $('#testMailForEmailMarketing').modal('hide');
                            $(document).find('#generate-shareable-url-inp').val(response.url);
                            $(document).find('#generate-shareable-url-inp').attr('type','text');
                            let copiedText = copyToClipBoard('generate-shareable-url-inp');
                            copiedText = response.message+'<br>'+copiedText;
                            $(document).find('#generate-shareable-url-inp').attr('type','hidden');
                            swal({
                                type: 'success',
                                title: 'Success!!',
                                text: copiedText,
                                html: true
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        $('#testMailForEmailMarketing').modal('hide');
                        swal('Oops!!',jqXHR.responseJSON.message,'error');
                    }
                });
            });

            $(document).on('click','.revoke-shareable-link',function(){
                let id = $(this).data('id');
                swal({
                    title: 'Do you want to Delete this?',
                    text: 'The proccess can not be undone!!!',
                    type:'warning',
                    showCancelButton:true,
                    allowOutsideClick:true,
                    confirmButtonColor: '#DD6B55',
                    confirmButtonText: 'Delete',
                    cancelButtonText: 'Cancel',
                    closeOnConfirm: true,
                },function() {
                    $.ajax({
                        method: 'GET',
                        url: '" . url('/admin/revoke-shareable-link') . "'+'/'+id,
                        beforeSend: function (){
                            swal({
                                title: 'Loading...',
                                imageUrl: window.ASSET_URL+ 'images/loading.gif',
                                showConfirmButton: false,
                                allowOutsideClick: false,
                                confirm: true,
                                showLoaderOnConfirm: true
                            })
                        },
                        success:function(response) {
                            $('#testMailForEmailMarketing').modal('hide');
                            if (response.success) {
                                swal('Success!!',response.message,'success');
                            } else {
                                swal('Hi There!!',response.message,'info');
                            }
                        }
                    });
                });
            });

            $(document).on('keyup','.search-term',function(){
                let searchValue = $(this).val().trim();
                if (searchValue.length >= 3) {
                    if (searchValue) {
                        $.ajax({
                            method: 'post',
                            url: '" . route('search-user-name') . "',
                            data:{term:searchValue},
                            success:function(response) {
                                if (response.success) {
                                    $(document).find('.check-list-items').html(response.data);
                                }
                            }
                        });
                    }
                }
            });

             $(document).on('click','.check-app-purchase',function() {
                let id = $(this).data('id');
                $.ajax({
                    method: 'post',
                    url: '" . route('share-campaign-with-user') . "',
                    data: {id:id,user_ids:checkedValue},
                    success:function(response) {
                        $('#testMailForEmailMarketing').modal('hide');
                        if (response.success) {
                            swal('Success!!',response.message,'success');
                        }
                    },
                    error:function(jqXHR, textStatus, errorThrown) {
                        swal('Oops!!',jqXHR.responseJSON.message,'error');
                    }
                });
            });


            $(document).on('submit','#zapier-tag-form',function(event){
                event.preventDefault();
                $.ajax({
                    method: 'post',
                    url: '" . route('store-zapier-tag') . "',
                    data: new FormData(this),
                    dataType:'JSON',
                    contentType: false,
                    cache: false,
                    processData: false,
                    success: function(response) {
                        if (response.success) {
                            $('#testMailForEmailMarketing').modal('hide');
                            let typeVal = 'info';
                            let messageVal = 'Hi There!!';
                            if (!response.info) {
                                typeVal = 'success';
                                messageVal = 'Successful!!';
                            }
                            swal({
                                type: typeVal,
                                title: messageVal,
                                text: response.message,
                                html: true
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        $('#testMailForEmailMarketing').modal('hide');
                        swal({
                            type: 'error',
                            title: 'Oops!!',
                            text: jqXHR.responseJSON.message,
                            html: true
                        });
                    }
                });
            });

            $(document).on('click','.delete-zapier-tag',function(event){
                event.preventDefault();
                swal({
                    title: 'Do you want to Delete this?',
                    text: 'The proccess can not be undone!!!',
                    type:'warning',
                    showCancelButton:true,
                    allowOutsideClick:true,
                    confirmButtonColor: '#DD6B55',
                    confirmButtonText: 'Delete',
                    cancelButtonText: 'Cancel',
                    closeOnConfirm: true,
                },function() {

                    let form = $('.remove_zapier_tag_form')[0];
                    $.ajax({
                        method: 'post',
                        url: '" . route('remove-step-tag') . "',
                        data: new FormData(form),
                        dataType:'JSON',
                        contentType: false,
                        cache: false,
                        processData: false,
                        success: function(response) {
                            $('#testMailForEmailMarketing').modal('hide');
                            if (response.success) {
                                swal({
                                    type: 'success',
                                    title: 'Success!!',
                                    text: response.message,
                                    html: true
                                });
                            } else {
                                swal({
                                    type: 'error',
                                    title: 'Oops!!',
                                    text: response.message,
                                    html: true
                                });
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            $('#testMailForEmailMarketing').modal('hide');
                            swal('Oops!!',jqXHR.responseJSON.message,'error');
                        }
                    });
                });
            });

            $(document).on('click','.delete_campaign',function(){
                let id = $(this).data('id');

                swal({
                    title: '".__('Do you want to Delete this?')."',
                    text: '".__('The proccess can not be undone!!!')."',
                    type:'warning',
                    showCancelButton:true,
                    allowOutsideClick:true,
                    confirmButtonColor: '#DD6B55',
                    confirmButtonText: '".__('Delete')."',
                    cancelButtonText: '".__('Cancel')."',
                    closeOnConfirm: true,
                },function() {
                    $.ajax({
                        method: 'GET',
                        url: '" . url('/admin/email_marketings/delete') . "'+'/'+id,
                        success: function(response) {
                            if (response.success) {
                                swal({
                                    type: 'success',
                                    title: '".__('Success!!')."',
                                    text: response.message,
                                    html: true
                                });
                            } else {
                                swal({
                                    type: 'error',
                                    title: '".__('Failed!!')."',
                                    text: response.message,
                                    html: true
                                });
                            }
                            location.reload();
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            swal('Oops!!',jqXHR.responseJSON.message,'error');
                        }
                    });
                });
            });
        ";


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = '



        ';


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = '
                <div class="modal fade" id="testMailForEmailMarketing" tabindex="-1" role="dialog">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">×</span>
                                </button>
                                <h4 class="modal-title changable-title">Send A Test Email</h4>
                            </div>
                            <div class="modal-body changable-body">

                            </div>
                        </div>
                      </div>
                </div>


                <div id="campaign_log_modal" class="modal fade" tabindex="-1" role="dialog">
                  <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                      <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Log</h4>
                      </div>
                      <div class="modal-body">
                      </div>
                      <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                      </div>
                    </div>
                  </div>
                </div>
            ';


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();
        $this->load_js[] = asset('js/jquery.json-viewer.js');
        $this->load_js[] = asset('js/email_marketings.js?v=9');


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();
        $this->load_css[] = asset('css/jquery.json-viewer.css');
    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        $query->where('user_id', '=', CRUDBooster::myParentId());
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    private $emailMarketing, $customers, $customerCount, $stepCount, $totalMail, $mainCampCompletedCustomers, $tagCustomerIds, $lastEmailReceivedCustomerCount;

    public function hook_row_index($column_index, &$column_value)
    {
        if($column_index == 1){
            // Variable initializing. Please don't remove this line

            $this->emailMarketing = EmailMarketing::with('tags.dropfunnel_tag', 'webhooks:id,campaign_id,delivered,opened,clicked')->find($column_value);
            $this->customers = $this->findCustomersByCampaignId($column_value);
            $this->customerCount = $this->customers->count();
            $this->stepCount = $this->emailMarketing->steps()->count() + 1;
            $this->totalMail = $this->stepCount * $this->customerCount;

            $this->tagCustomerIds = $this->customers->pluck('id')->toArray();
            $this->mainCampCompletedCustomers = DB::table('email_marketings AS em')
                        // ->join('dropfunnel_step_mails AS s', 'em.id', '=', 's.campaign_id')
                        ->join('mail_gun_web_hook_histories AS h', 'em.id', '=', 'h.campaign_id')
                        ->where('em.id', $column_value)
                        ->whereNull('h.failed')
                        ->whereIntegerInRaw('h.customer_id', $this->tagCustomerIds)
                        ->groupBy('h.customer_id')
                        // ->havingRaw('COUNT(DISTINCT s.id) = (SELECT COUNT(*) FROM dropfunnel_step_mails WHERE campaign_id = ' . $campaign_id . ')')
                        ->distinct('h.customer_id')
                        ->select('h.customer_id')->get();

            $this->lastEmailReceivedCustomerCount = $this->getLastStepEmailSendCount($this->emailMarketing->id);

            // Variable initializing. Please don't remove this line End

            $column_value = '<a style="color: #222; text-align: left" href="' . url('/admin/email_marketings/edit') . '/' . $this->emailMarketing->id . '" class="btn btn-sm text-capitalize"><i class="fa fa-pencil"></i> ' . __('Edit') . ' </a>';
        }

        if ($column_index == 3) {
            if ($column_value)
                $column_value = '<span class="label label-success">Active</span>';
            else
                $column_value = '<span class="label label-danger">Not live</span>';
        }

        // if ($column_index == 3) {
            // $this->emailMarketing = EmailMarketing::with('tags.dropfunnel_tag', 'webhooks:id,campaign_id,delivered,opened,clicked')->find($column_value);
            // $this->customers = $this->findCustomersByCampaignId($column_value);
            // $this->customerCount = $this->customers->count();
            // $this->stepCount = $this->emailMarketing->steps()->count() + 1;
            // $this->totalMail = $this->stepCount * $this->customerCount;
        //     $column_value = '<a class="btn btn-xs btn-info" title="Number of mails" href="javascript:customer_email_list(' . $column_value . ')"><i class="fa fa-envelope" style="font-size: 11px;"></i> ' . $this->customerCount . '</a>';

        //     if(isLocal() || in_array(CRUDBooster::myParentId(), [2454, 2455, 98, 2439, 212, 62]))
        //     {
        //         $download_url = CRUDBooster::mainpath('download-email/'.$this->emailMarketing->id);
        //         $column_value .= ' <a class="btn btn-xs btn-info" style="background-color:#0072b1;" data-url="'.$download_url.'" href="javascript:download_email_list(' . $this->emailMarketing->id . ')"><i class="fa fa-linkedin" aria-hidden="true"></i></a>';
        //     }
        // }

        if ($column_index == 4) {
            $html = '';
            if (!empty($this->emailMarketing) && $this->emailMarketing->tags->isNotEmpty()) {
                foreach ($this->emailMarketing->tags as $tag) {
                    $tag_color = 'success';
                    $tag_label = 'OR condition';
                    if ($tag->relation) {
                        $tag_color = "danger";
                        $tag_label = 'AND condition';
                    }
                    $html .= '<span style="max-width: 100px;display: inline-block;margin: 0 5px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;" title="' . $tag_label . '" class="label label-' . $tag_color . '">' . $tag->dropfunnel_tag->tag . '</span>';
                }
            }
            $column_value = $html;
        }

        if ($column_index == 5) {
            // $deliveryCount = dropFunnelAverageRateCalculation($this->emailMarketing, $this->customerCount, 'delivered', true);
            // $delivery = "$deliveryCount / $this->totalMail";
            // $deliveryRate = dropFunnelAverageRateCalculation($this->emailMarketing, $this->customerCount, 'delivered');
            // $is_completed_data = $this->emailMarketing->completed > 0;

            // $cls_ex = ($is_completed_data) ? ' class="dr_email_delivered_btn" data-type="delivered" data-id="' . $this->emailMarketing->id . '" style="cursor: pointer;"' : '';

            // $html = '<div' . $cls_ex . '>  <div>' . $deliveryRate . ' %</div>';
            // if ($is_completed_data) {
            //     $html .= '
            //             <div class="progress" style="height: 8px;margin-bottom: 5px;">
            //              <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: ' . $deliveryRate . '%;background:#00a65a;">
            //                <span class="sr-only">60% Complete</span>
            //              </div>
            //           </div>';
            // }
            // $html .= '<p>' . $delivery . '</p>';
            // $column_value = $html . '</div>';


            $column_value = '<a class="btn btn-xs btn-success" title="Number of mails" href="javascript:customer_email_list(' . $column_value . ')">' . $this->customerCount . '</a>';

            // if(isLocal() || in_array(CRUDBooster::myParentId(), [2454, 2455, 98, 2439, 212, 62]))
            // {
            //     $download_url = CRUDBooster::mainpath('download-email/'.$this->emailMarketing->id);
            //     $column_value .= ' <a class="btn btn-xs btn-info" style="background-color:#0072b1;" data-url="'.$download_url.'" href="javascript:download_email_list(' . $this->emailMarketing->id . ')"><i class="fa fa-linkedin" aria-hidden="true"></i></a>';
            // }
        }


        // if ($column_index == 7) {
        //     $clickRateCount = dropFunnelAverageRateCalculation($this->emailMarketing, $this->customerCount, 'clicked', true);
        //     $clicked_label = "$clickRateCount / $this->totalMail";
        //     $clickRate = dropFunnelAverageRateCalculation($this->emailMarketing, $this->customerCount, 'clicked');

        //     $is_completed_data = $this->emailMarketing->completed > 0;
        //     $cls_ex = ($is_completed_data) ? ' class="dr_email_delivered_btn" data-type="clicked" data-id="' . $this->emailMarketing->id . '" style="cursor: pointer;"' : '';
        //     $html = '<div' . $cls_ex . '>  <div>' . $clickRate . '%</div>';
        //     $html .= '<div class="progress" style="height: 8px;margin-bottom: 5px;">
        //               <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: ' . $clickRate . '%;background:#00a65a;">
        //                 <span class="sr-only">60% Complete</span>
        //               </div>
        //             </div>
        //             ';

        //     $html .= '<p>' . $clicked_label . '</p>';
        //     $column_value = $html . '</div>';
        // }

        // if ($column_index == 8) {
        //     $openRateCount = dropFunnelAverageRateCalculation($this->emailMarketing, $this->customerCount, 'opened', true);
        //     $openRate_label = "$openRateCount / $this->totalMail";
        //     $openRate = dropFunnelAverageRateCalculation($this->emailMarketing, $this->customerCount, 'opened');

        //     $is_completed_data = $this->emailMarketing->completed > 0;
        //     $cls_ex = ($is_completed_data) ? ' class="dr_email_delivered_btn" data-type="opened" data-id="' . $this->emailMarketing->id . '" style="cursor: pointer;"' : '';
        //     $html = '<div' . $cls_ex . '>  <div>' . $openRate . '%</div>';
        //     $html .= '<div class="progress" style="height: 8px;margin-bottom: 5px;">
        //               <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: ' . $openRate . '%;background:#00a65a;">
        //                 <span class="sr-only">60% Complete</span>
        //               </div>
        //             </div>
        //             ';

        //     $html .= '<p>' . $openRate_label . '</p>';
        //     $column_value = $html . '</div>';
        // }


        if($column_index == 6) {
            $totalDeliveryCount = !$this->mainCampCompletedCustomers->isEmpty() ? count($this->mainCampCompletedCustomers) : 0;

            $lastEmailGotCustomers = $this->getLastStepEmailSendCount($this->emailMarketing->id, false);
            $lastEmailGotCustomerIds = $lastEmailGotCustomers->pluck('id')->toArray();

            $unsubCustomerIds = $this->getUnsubscribeCustomerIds($this->emailMarketing->user_id, $this->emailMarketing->id);

            $totalCustomerExcludedIds = array_values( array_unique(array_merge($lastEmailGotCustomerIds, $unsubCustomerIds)) );

            // $totalDeliveryCount = dropFunnelAverageRateCalculation($this->emailMarketing, $this->customerCount, 'delivered', true);
            // $unsubscriberCount = DB::table('campaign_unsubscribers')->where('campaign_id', $column_value)->groupBy('customer_id')->count();

            // Total waiting customer of last setp email
            $lastMailWaitingCustomerCount = $totalDeliveryCount ? ($totalDeliveryCount - (count($totalCustomerExcludedIds))) : 0;

            // $column_value = $column_value;

            $column_value = '<a class="btn btn-xs btn-success" title="Completed Email list" href="javascript:drf_completd_email(' . $this->emailMarketing->id . ')">' . $lastMailWaitingCustomerCount . '</a>';
        }

        // if($column_index == 10) {
        //     $column_value = DB::table('campaign_unsubscribers')->where('campaign_id', $column_value)->groupBy('customer_id')->count();
        //     $column_value = ($column_value * 100) / ($this->customerCount < 1 ? 1 : $this->customerCount);
        //     $column_value = number_format($column_value, 2).'%';
        // }

        if ($column_index == 7) {
            // $customers = $this->findCustomersByCampaignId($column_value);
            // $customer_ids = $customers->pluck('id')->toArray();
            // // $stepComplete = $this->emailMarketing->steps()->selectRaw('SUM(complete) as complete')->first();
            // $completedCustomers = DB::table('email_marketings AS em')
            //             // ->join('dropfunnel_step_mails AS s', 'em.id', '=', 's.campaign_id')
            //             ->join('mail_gun_web_hook_histories AS h', 'em.id', '=', 'h.campaign_id')
            //             ->where('em.id', $column_value)
            //             ->whereNull('h.failed')
            //             ->whereIntegerInRaw('h.customer_id', $customer_ids)
            //             ->groupBy('h.customer_id')
            //             // ->havingRaw('COUNT(DISTINCT s.id) = (SELECT COUNT(*) FROM dropfunnel_step_mails WHERE campaign_id = ' . $campaign_id . ')')
            //             ->distinct('h.customer_id')
            //             ->select('h.customer_id')->get();
            // $count = !$completedCustomers->isEmpty() ? count($completedCustomers) : 0;
            // $column_value = $count;
            // $column_value = '<a class="btn btn-xs btn-success" title="Completed Email list" href="javascript:drf_completd_email(' . $this->emailMarketing->id . ')"><i class="fa fa-paper-plane" style="font-size: 11px;"></i> ' . $column_value . '</a>';

            $mainDeliveryCount = !$this->mainCampCompletedCustomers->isEmpty() ? count($this->mainCampCompletedCustomers) : 0;

            $unsubscribeRate = $this->getUnsubscribeRateBylastEmailSend($mainDeliveryCount, $this->lastEmailReceivedCustomerCount);
            $unsubscribeShow = $this->lastEmailReceivedCustomerCount . " / " . $mainDeliveryCount;

            $html = '<div>  <div>' . $unsubscribeRate . ' %</div>';

            $html .= '
                    <div class="progress" style="height: 8px;margin-bottom: 5px;">
                        <div class="progress-bar" role="progressbar" aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width: ' . $unsubscribeRate . '%;background:#00a65a;">
                        <span class="sr-only">60% Complete</span>
                        </div>
                    </div>';

            $html .= '<p>' . $unsubscribeShow . '</p>';
            $column_value = $html . '</div>';
        }

        if ($column_index == 8) {
            $column_value = '<div class="dropdown">
                      <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu' . $this->emailMarketing->id . '" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">'
                            . __('Actions') .
                        ' <span class="caret"></span>
                      </button>
                      <ul class="dropdown-menu pull-right action_dropdown" aria-labelledby="dropdownMenu' . $this->emailMarketing->id . '">';
            //if(!CRUDBooster::isSubUser() || CRUDBooster::isDropmatixSupport() || CRUDBooster::isSubUser() && (sub_account_can('edit_campaigns', 120) || sub_account_can('all_modules', 122))){
            // $column_value .=  '<li style="margin-bottom: 2px;"><a style="color: #222; text-align: left" href="' . url('/admin/email_marketings/edit') . '/' . $this->emailMarketing->id . '" class="btn btn-sm text-capitalize"><i class="fa fa-pencil"></i> ' . __('Edit') . ' </a></li>';
            //}

            $column_value .=  '<li style="margin-bottom: 2px;"><a style="color: #222; text-align: left" href="javascript:void(0)" onclick="send_test_mailable(' . $this->emailMarketing->id . ')" class="btn btn-sm btn-default text-capitalize"><i class="fa fa-paper-plane-o"></i> ' . __('Send Test Email') . ' </a></li>
                        <li style="margin-bottom: 2px;"><a style="color: #fff; text-align: left" href="javascript:void(0)" onclick="drm_campaign_share(' . $this->emailMarketing->id . ')" class="btn btn-sm btn-info text-capitalize"><i class="fa fa-share"></i> ' . __('Share') . ' </a></li>
                        <li style="margin-bottom: 2px;"><a style="color: #fff; text-align: left" href="javascript:void(0)" onclick="step_mail_tag_add(' . $this->emailMarketing->id . ')" class="btn btn-sm btn-success text-capitalize"><i class="fa fa-plus"></i> ' . __('Add Tag') . ' </a></li>
                        <li style="margin-bottom: 2px;"><a style="color: #fff; text-align: left" href="javascript:void(0)" onclick="step_mail_tag_remove(' . $this->emailMarketing->id . ')" class="btn btn-sm btn-danger text-capitalize"><i class="fa fa-times"></i> ' . __('Remove Tag') . ' </a></li>';

            if (CRUDBooster::isSuperadmin() || in_array(CRUDBooster::myParentId(), [62, 212])) {
                $column_value .= '<li style="margin-bottom: 2px;"><a style="color: #fff; text-align: left" href="javascript:void(0)" onclick="drm_campaign_log(' . $this->emailMarketing->id . ')" class="btn btn-sm btn-drm text-capitalize"><i class="fa fa-info"></i> ' . __('Log') . ' </a></li>';
            }
            $column_value .= '<li style="margin-bottom: 2px;"><a style="color: #fff; text-align: left" href="' . url('/admin/email_marketings/detail') . '/' . $this->emailMarketing->id . '" class="btn btn-sm btn-primary text-capitalize"><i class="fa fa-eye"></i> ' . __('Preview') . ' </a></li>
                        <li style="margin-bottom: 2px;"><a style="text-align: left" href="javascript:void(0)" data-id="' . $this->emailMarketing->id . '" class="btn btn-sm btn-warning text-capitalize delete_campaign"><i class="fa fa-trash"></i> ' . __('Delete') . '</a></li>
                      </ul>
                </div>';
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }


    public function getIndex()
    {
        $this->cbLoader();

        if(CRUDBooster::isKeyAccount()) abort(404);

        $module = CRUDBooster::getCurrentModule();

        if (!CRUDBooster::isView() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $this->createDefaultSignature();

        $is_exists = DB::table('email_marketings')->where('user_id',CRUDBooster::myParentId())->first();
        if(!$is_exists){
            CRUDBooster::redirect(CRUDBooster::adminPath('email_marketings/dropfunnel-home'), 'You do not have created email campaign yet !', 'info');
        }

        if (\Illuminate\Support\Facades\Request::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
            if (Request::get('foreign_key')) {
                $data['parent_field'] = Request::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($parent_field) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $parent_field) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        $data['page_title'] = __('menu.CAMPAIGN_MANAGEMENNT');
        $data['page_description'] = trans('crudbooster.default_module_description');
        $data['date_candidate'] = $this->date_candidate;
        $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::table($this->table)->select(DB::raw($this->table . "." . $this->primary_key));

        if (Request::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . Request::get('foreign_key'), Request::get('parent_id'));
        }

        $this->hook_query_index($result);

        if (in_array('deleted_at', $table_columns)) {
            $result->where($this->table . '.deleted_at', null);
        }

        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;
        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {

                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if (Request::get('q')) {
            $result->where(function ($w) use ($columns_table, $request) {
                foreach ($columns_table as $col) {
                    if (!$col['field_with']) {
                        continue;
                    }
                    if ($col['is_subquery']) {
                        continue;
                    }
                    $w->orwhere($col['field_with'], "like", "%" . Request::get("q") . "%");
                }
            });
        }

        if (Request::get('where')) {
            foreach (Request::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }

        $filter_is_orderby = false;
        if (Request::get('filter_column')) {

            $filter_column = Request::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {
                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];

                if ($sorting != '') {
                    if ($key) {
                        $result->orderby($key, $sorting);
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];

        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(Request::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (Request::get('page')) ? Request::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            if ($this->button_bulk_action) {

                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];

                if (isset($col['image'])) {
                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('vendor/crudbooster/avatar.jpg') . "'><img width='40px' height='40px' src='" . asset('vendor/crudbooster/avatar.jpg') . "'/></a>";
                    } else {
                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='" . $pic . "'><img width='40px' height='40px' src='" . $pic . "'/></a>";
                    }
                }

                if (@$col['download']) {
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = \Illuminate\Support\Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                if(isset($col['type']) && $col['type'] == 'title') {
                    // $value = '<a href="'.url('/admin/email_marketings/edit') . '/' . $row->id.'">'.$row->campaign_name.'</a>';
                    $value = '<a href="#" onclick="stat_modal_showw(' . $row->id . ')">'.$row->campaign_name.'</a>';
                }

                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action):

                $button_action_style = $this->button_action_style;
                $html_content[] = "<div class='button_action' style='text-align:right'>" . view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render() . "</div>";

            endif;//button_table_action

            foreach ($html_content as $i => $v) {
                if($i == 7){
                    $v = $row->id;
                }
                $this->hook_row_index($i, $v);
                $html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

        $data['html_contents'] = $html_contents;
        return view('admin.drm_email_marketings.index', $data);
    }

    public function getAdd()
    {
        $this->cbLoader();
        if (!CRUDBooster::isCreate() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
        $data = [];
        $data['page_title'] = __('Add Email Marketing');
        $data['campaign_name'] = (request()->campaign_name) ? request()->campaign_name : '';
        $authId = CRUDBooster::myParentId();
        $data['shortcodes'] = DropFunnelSignature::where('user_id', $authId)->get()->toArray();
        $data['countDowns'] = DropFunnelCountDown::where('user_id', $authId)->get(['id']);
        $shopExits = isChannelExists(Channel::DROPTIENDA, $authId);
        $data['isShopConnected'] = !empty($shopExits);
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();

        $userEmail = DB::table('cms_users')->where('id', $authId)->value('email');
        $userEmailArr = explode('@', $userEmail);
        $userDomain = array_pop($userEmailArr);

        $dkim_verified = DB::table('dkim_users_log')
            ->where([
                'user_id' => $authId,
                'email' => $userDomain,
                'status' => 1,
                'user_confirm_status' => 1,
            ])
            ->exists();

        $userEmail = $dkim_verified ? $userEmail : '';
        $data['userEmail'] = $userEmail;
        
        // DB::table('temp_email_builder_template')->where('user_id', $authId)->whereDate('created_at', '<', Carbon::today())->delete();
        return view('admin.drm_email_marketings.create', $data);
    }


    // Create campaign step
    public function createCampaignStep($campaignId, $template_id = null, $step_name = '' , $body = ' ')
    {
        $position = DB::table('dropfunnel_step_mails')->where('campaign_id', $campaignId)->orderBy('position', 'desc')->value('position') + 1;
        $product_tags = !empty(trim($body)) ? $this->getMatchProductTags(trim($body)) : null;

        return DB::table('dropfunnel_step_mails')->insertGetId([
            'campaign_id' => $campaignId,
            'step_name' => $step_name,
            'subject' => ' ',
            'email_body' => $body ?? ' ',
            'builder_template_id' => $template_id,
            'position' => $position,
            'type' => 'hour',
            'value' => 0,
            'product_tags' => $product_tags,
            'complete' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }


    // Create main campaign
    public function createMainCampaign($req, $template_id)
    {
        $pattern = ['/\[drm-sign-\d*]/', '/\[# {0}(\_{1}|\w* ?\w*|\d*)]/'];
        $authId = CRUDBooster::myParentId();

        $payload = [
            'name' => '', //$req['name'],
            'email_template' => $req['template'],
            'calculation_percentage' => $req['percentage'],
            'calculation_tax' => $req['tax'],
            'status' => 0,
            'main_step_builder_template' => $template_id,
            'campaign_name' => 'Draft campaign',
            'sender_email' => CRUDBooster::me()->email,
        ];

        $template = $payload['email_template'];
        $signatures = $this->findValidSignature($pattern, $template, $authId);

        $signature = $signatures['signatures'][0];
        $matches = $signatures['matches'];
        $templateWithSignature = $this->replaceSignatureFromTemplate($template, $matches, $signature);

        $templateArray = [];
        $templateArray['template'] = $templateWithSignature;
        $productTags = $this->getMatchProductTags($template);
        $products = $this->getMatchTagProducts($templateArray);
        $this->transferAndCreateProductToDtoptienda($products, $authId, $request['calculation_percentage'], $request['calculation_tax']);

        $emailMarketing = new EmailMarketing();
        $template = str_replace('"/admin/','"',$template);
        $linkPattern = '/\[drm-link-\d*]/';
        if (preg_match_all($linkPattern, $template, $matches)) {

            foreach ($matches[0] as $key => $value){
                preg_match('/\d+/',$value, $matchedId);
                $link_id = $matchedId[0];
                $external_link = DB::table('dropfunnel_external_links')->where('id', $link_id)->first();
                $link_new = url('/insert-tag-by-link').'?slug='.$external_link->slug.'-[customer_id]';
                if (!empty($external_link)) {
                    $template = preg_replace('/\[drm-link-'.$link_id.']/', $link_new, $template);
                }
            }
        }

        $campaign = $this->storeCampaign($productTags, $payload, $template, $authId, $emailMarketing);
        if (is_null($campaign)) {
            throw new Exception('Failed To Store');
        }

        return $campaign->id;
    }

    /**
     * Add save campaign
     * @deprecated
     *
     */
    public function postAddSave()
    {

        abort(404);

        $request = $_REQUEST;
        $pattern = ['/\[drm-sign-\d*]/', '/\[# {0}(\_{1}|\w* ?\w*|\d*)]/'];
        $authId = CRUDBooster::myParentId();
        // $template_id = DB::table('temp_email_builder_template')->where('user_id', $authId)->where('is_delete','0')->value('template_id');
        // if(!empty($template_id)){
        //     DB::table('builder_templates')->where('id', '=', $template_id)->delete();
        //     DB::table('builder_template_blocks')->where('template_id', '=', $template_id)->delete();
        // }
//        if (in_array($authId, [98, 212])) {
            // $templates = DB::table('temp_email_builder_template')->where('user_id', $authId)
            // ->whereDate('created_at', Carbon::today())->orderBy('step')->select('template', 'calculation_percentage', 'calculation_tax', 'template_id')
            // ->get();
            // DB::table('temp_email_builder_template')->where('user_id', $authId)->delete();

            foreach ($templates as $KEY => $emailTemplate) {
                if ($KEY == 0) {
                    $request['email_template'] = $emailTemplate->template;
                    $request['calculation_percentage'] = $emailTemplate->calculation_percentage;
                    $request['calculation_tax'] = $emailTemplate->calculation_tax;
                    $request['main_step_builder_template'] = $emailTemplate->template_id;
                } else {
                    $request['email_body'][] = $emailTemplate->template;
                    $request['sub_step_builder_template'][] = $emailTemplate->template_id;
                }
            }
//        }

        $message = 'Data Successfully Stored';
        if(!preg_match_all($pattern[0], $request['email_template'], $matches)){
            $request['status'] = 0;
            $message = __('we have saved your changes. Since there is no sender signature added yet, the campaign has been saved as a draft. After adding the signature, you can change the status to Active. However, your changes have been successfully saved.');
        }

        $this->dfValidation($request, $pattern);

        $status = isset($request['status']) ? (int)$request['status'] : 0;
        try {
            $template = $request['email_template'];
            $signatures = $this->findValidSignature($pattern, $template, $authId);
            if (request()->ajax() && request()->has('email_subject') && (count(array_filter($request['email_subject'])) == count($request['email_subject'])) ) {

                if($request['email_body'])
                {
                    foreach ($request['email_body'] as $key => $emailBody) {
                        $stepSign = $this->findValidSignature($pattern, $emailBody, $authId, $key + 2);
                        if (!array_key_exists('signatures', $stepSign)) {
                            $signatures = $stepSign;
                            break;
                        }
                    }
                }
            }

            if ($status && !array_key_exists('signatures', $signatures)) {
                if (request()->ajax()) {
                    return response()->json($signatures);
                }
                return redirect()->back()->with($signatures);
            }

            $signature = $signatures['signatures'][0];
            $matches = $signatures['matches'];
            $templateWithSignature = $this->replaceSignatureFromTemplate($template, $matches, $signature);

            $templateArray = [];
            $templateArray['template'] = $templateWithSignature;
            $productTags = $this->getMatchProductTags($template);
            $products = $this->getMatchTagProducts($templateArray);
            $this->transferAndCreateProductToDtoptienda($products, $authId, $request['calculation_percentage'], $request['calculation_tax']);

            $emailMarketing = new EmailMarketing();

            $template = str_replace('"/admin/','"',$template);
            $linkPattern = '/\[drm-link-\d*]/';
            if (preg_match_all($linkPattern, $template, $matches)) {

                foreach ($matches[0] as $key => $value){
                    preg_match('/\d+/',$value, $matchedId);
                    $link_id = $matchedId[0];
                    $external_link = DB::table('dropfunnel_external_links')->where('id', $link_id)->first();
                    $link_new = url('/insert-tag-by-link').'?slug='.$external_link->slug.'-[customer_id]';
                    if (!empty($external_link)) {
                        $template = preg_replace('/\[drm-link-'.$link_id.']/', $link_new, $template);
                    }
                }
            }

            $campaign = $this->storeCampaign($productTags, $request, $template, $authId, $emailMarketing);

            if (is_null($campaign)) {
                throw new Exception('Failed To Store');
            }

            // $this->storeStepCampaign($request, $campaign);

            $this->storeCampaignTagAndSendEmail($request, $campaign);
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'url' => CRUDBooster::adminPath('dropfunnel?step=2&page=edit&edit='.$campaign->id),
                    'first_step_edit' => CRUDBooster::adminPath('dropfunnel?step=1&page=edit&edit='.$campaign->id),
                    'redirect_to_edit' => CRUDBooster::adminPath('email_marketings/edit/'.$campaign->id),
                ]);
            }
            CRUDBooster::redirect(CRUDBooster::adminPath('email_marketings'), 'Data Successfully Stored', 'success');
        } catch (Exception $exception) {



            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to Store',
                ]);
            }
            return redirect()->back()->with(['message' => 'Failed to Store', 'message_type' => 'warning']);
        }
    }

    public function getDetail($id)
    {

        $this->cbLoader();
        if (!CRUDBooster::isCreate() && $this->global_privilege == false) { // || $this->button_add == false
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $campaign = EmailMarketing::with(['steps' => function ($query) {
            $query->orderBy('position');
        }])->with('tags.dropfunnel_tag')->find($id);

        if (empty($campaign)) {
            abort(404);
        }

        $data = [];
        $data['page_title'] = __('Detail Email marketing');
        $data['email_marketing'] = $campaign->toArray();
        $data['email_campaign'] = $campaign;
        $data['customers'] = $this->findCustomersByCampaignId($data['email_marketing']['id']);
        $customers = $data['customers'];
        $customerCount = $customers->count();

        $deliveryCount = $this->dropFunnelRateCalculation($campaign, $customers, 'delivered', true);
        $deliveryLabel = "$deliveryCount / $customerCount";
        $deliveryRate = $this->dropFunnelRateCalculation($campaign, $customers, 'delivered');

        $clickRateCount = $this->dropFunnelRateCalculation($campaign, $customers, 'clicked', true);
        $clickedLabel = "$clickRateCount / $customerCount";
        $clickRate = $this->dropFunnelRateCalculation($campaign, $customers, 'clicked');

        $openRateCount = $this->dropFunnelRateCalculation($campaign, $customers, 'opened', true);
        $openRateLabel = "$openRateCount / $customerCount";
        $openRate = $this->dropFunnelRateCalculation($campaign, $customers, 'opened');

        $data['customerCount'] = $customerCount;
        $data['deliveryLabel'] = $deliveryLabel;
        $data['clickedLabel'] = $clickedLabel;
        $data['openRateLabel'] = $openRateLabel;
        $data['deliveryRate'] = $deliveryRate;
        $data['clickRate'] = $clickRate;
        $data['openRate'] = $openRate;
        return view('admin.drm_email_marketings.show', $data);
    }


    // update campaign steps data
    private function updateStepsData($req)
    {
        if(!empty($req['position']) && is_array($req['position']))
        {
            $position = 1;
            foreach($req['position'] as $stepId => $k)
            {
                $step = DB::table('dropfunnel_step_mails')->where('id', $stepId)->select('email_body', 'step_name')->first();

                $body = $step->email_body;
                $step_name = $step->step_name;

                $product_tags = !empty(trim($body)) ? $this->getMatchProductTags(trim($body)) : null;

                // if (!localDevelopment('dropfunnle_new_step')) {
                //     $payload = [
                //         'subject' => $req['email_subject'][$stepId],
                //         'type' => $req['day_type'][$stepId],
                //         'value' => $req['value'][$stepId],
                //         'position' => $position,
                //         'product_tags' => $product_tags,
                //     ];
                // } else {
                    $payload = [
                        'subject'           => $req['email_subject'][$stepId],
                        'type'              => $req['day_type'][$stepId] ?? 'hours',
                        'value'             => $req['value'][$stepId] ?? 24,
                        'position'          => $position,
                        'product_tags'      => $product_tags,
                        'extra_campaign_id' => !empty($req['extra_campaign_id'][$stepId]) ? intval($req['extra_campaign_id'][$stepId]) : null,
                        'extra_tag'         => !empty($req['extra_tag'][$stepId]) ? $req['extra_tag'][$stepId] : null,
                        'remove_tag'        => !empty($req['remove_tag'][$stepId]) ? intval($req['remove_tag'][$stepId]) : null,
                        'goal_notify'       => $req['goal_notify'][$stepId] ?? null,
                        'open_mail_notify'  => $req['open_mail_notify'][$stepId] ?? null,
                        'send_mail_again'   => $req['send_mail_again'][$stepId] ?? null,
                    ];
                // }

                if($step_name && str_contains($step_name , ' #'))
                {
                    $payload['step_name'] = $payload['subject'];
                }

                DB::table('dropfunnel_step_mails')
                ->where('id', $stepId)
                ->update($payload);
                $position++;
            }
        }
    }

    public function postEditSave($id)
    {
        $request = $_REQUEST;
        $pattern = ['/\[drm-sign-\d*]/', '/\[# {0}(\_{1}|\w* ?\w*|\d*)]/'];
        $authId = CRUDBooster::myParentId();


        $emailMarketing = EmailMarketing::with(['steps' => function ($query) {
            $query->orderBy('position', 'asc');
        }])->where('user_id', $authId)->find($id);

        if(empty($emailMarketing)) return redirect()->back()->with(['message' => 'Invalid access!', 'message_type' => 'danger']);


        $request['email_template'] = $emailMarketing->email_template;
        $request['calculation_percentage'] = $emailMarketing->calculation_percentage;
        $request['calculation_tax'] = $emailMarketing->calculation_tax;
        $request['main_step_builder_template'] = $emailMarketing->builder_template_id;

        $stepNames = [];
        foreach ($emailMarketing->steps as $step) {
            $request['email_body'][$step->id] = $step->email_body;
            // $request['email_subject'][$step->id] = $step->subject;
            $stepNames[$step->id] = $step->step_name;
        }

        $this->dfValidation($request, $pattern, $stepNames);


        //DB::beginTransaction();
        try {
            $template = $request['email_template'];
            $signatures = $this->findValidSignature($pattern, $template, $authId);
            if (request()->ajax() && request()->has('email_subject') && $request['email_body']) {
                foreach ($request['email_body'] as $key => $emailBody) {
                    $stepSign = $this->findValidSignature($pattern, $emailBody, $authId, $key + 2);
                    if (!array_key_exists('signatures', $stepSign)) {
                        $signatures = $stepSign;
                        break;
                    }
                }
            }

            //Status
            $status = isset($request['status']) ? (int)$request['status'] : 0;

            if ($status && !array_key_exists('signatures', $signatures)) {
                if (request()->ajax()) {
                    return response()->json($signatures);
                }
                return redirect()->back()->with($signatures);
            }

            $signature = $signatures['signatures'][0];
            $matches = $signatures['matches'];
            $templateWithSignature = $this->replaceSignatureFromTemplate($template, $matches, $signature);

            $productTags = $this->getMatchProductTags($template);

            $template = str_replace('"/admin/','"',$template);
            $linkPattern = '/\[drm-link-\d*]/';
            if (preg_match_all($linkPattern, $template, $matches)) {

                foreach ($matches[0] as $key => $value){
                    preg_match('/\d+/',$value, $matchedId);
                    $link_id = $matchedId[0];
                    $external_link = DB::table('dropfunnel_external_links')->where('id', $link_id)->first();
                    $link_new = url('/insert-tag-by-link').'?slug='.$external_link->slug.'-[customer_id]';
                    if (!empty($external_link)) {
                        $template = preg_replace('/\[drm-link-'.$link_id.']/', $link_new, $template);
                    }
                }
            }

            $campaign = $this->storeCampaign($productTags, $request, $template, $authId, $emailMarketing, 'update');
            if (is_null($campaign)) {
                throw new Exception('Failed To Update');
            }

            // Update steps data
            $this->updateStepsData($request);

            // Campaign tags
            DB::table('campaign_tags')->where('campaign_id', '=', $campaign->id)->delete();
            $this->storeCampaignTagAndSendEmail($request, $campaign);

            //DB::commit();

            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Data Successfully Updated',
                    'back_id' => null,
                ]);
            }
            CRUDBooster::redirect(CRUDBooster::adminPath('email_marketings'), 'Data Successfully Updated', 'success');
        } catch (Exception $exception) {
            //DB::rollBack();
            if (request()->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to Store'
                ]);
            }
            return redirect()->back()->with(['message' => 'Failed to Store', 'message_type' => 'warning']);
        }
    }

    //Test campaign email
    public function postSendTestMailable()
    {
        try {
            $url = config('app.drm_background_url').'/api/send-test-campaign';

            if( $_REQUEST['step_id'] === 'full_test' || $_REQUEST['step_id'] === 'full_campaign' ) {
                $_REQUEST['step_id'] = null;
                $_REQUEST['full_test'] = "yes";
            }

            $body = [
                'campaign' => $_REQUEST['id'],
                'step' => $_REQUEST['step_id'],
                "email" => $_REQUEST['email'],
                "full_test" => $_REQUEST['full_test'],
            ];

            $params = [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/json',
                    'token' => 'dftkns6Tc',
                ],
                'json' => $body,
            ];

            $client = new \GuzzleHttp\Client();
            $response = $client->request('POST', $url, $params);
            return response()->json(['success' => true, 'message' => 'Email Send Successfully']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Failed to Send' . PHP_EOL . $e->getMessage()]);
        }
    }

    //Get edit
    public function getEdit($id)
    {
        if(empty($id))
        {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $this->cbLoader();
        if (!CRUDBooster::isCreate() && $this->global_privilege == false) { // || $this->button_add == false
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $authId = CRUDBooster::myParentId();
        $campaign = EmailMarketing::with('tags', 'tags.dropfunnel_tag')->where('user_id', $authId)->find($id);

        if(empty($campaign)) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $this->createDefaultSignature();

        $data = [];
        $data['page_title'] = __('Edit Email Marketing');
        $data['id'] = $id;
        $data['email_marketing'] = $campaign;
        $data['step_mails'] = DB::table('dropfunnel_step_mails')->where('campaign_id', $id)->orderBy('position', 'asc')->get();
        $data['shortcodes'] = DropFunnelSignature::where('user_id', $authId)->get()->toArray();
        $shopExits = isChannelExists(\App\Enums\Channel::DROPTIENDA, $authId);
        $data['isShopConnected'] = !empty($shopExits);
        $data['iframe'] = "<iframe src='" . url('admin/email_marketings/edit-preview-iframe') . '/' . $id . "' height='300' width='800'></iframe>";
        $data['countDowns'] = DropFunnelCountDown::where('user_id', $authId)->get(['id']);
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();

        $userEmail = DB::table('cms_users')->where('id', $authId)->value('email');
        $userEmailArr = explode('@', $userEmail);
        // $userDomain = $userEmailArr[1];
        $userDomain = array_pop($userEmailArr);

        $dkim_verified = DB::table('dkim_users_log')
            ->where([
                'user_id' => $authId,
                'email' => $userDomain,
                'status' => 1,
                'user_confirm_status' => 1,
            ])
            ->exists();

        $userEmail = $dkim_verified ? $userEmail : '';

        $data['userEmail'] = $userEmail;
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myParentId());

        $data['user_campaigns']      = $this->getUserCampaigns($authId);
        $data['user_campaigns_tags'] = $this->getUserCampaignsTags($authId);
        return view('admin.drm_email_marketings.edit', $data);
    }

    public function getEditPreviewIframe($id, $step = false)
    {
        if ($step) {
            $template = DB::table('dropfunnel_step_mails')->where('id', $id)->value('email_body');
        } else {
            $template = EmailMarketing::where('id', $id)->value('email_template');
        }
        if (!empty($template)) {
            return view('admin.drm_email_marketings.partials._template_preview', compact('template'));
        }
        return '<p>No Template Found</p>';
    }

    public function getDelete($id)
    {
        try {
            $emailMarketing = EmailMarketing::find($id);
            if (!empty($emailMarketing)) {
                if ($emailMarketing->tags()->exists()) {
                    $emailMarketing->tags()->delete();
                }
                if (request()->ajax()) {
                    if ($emailMarketing->steps()->exists()) {
                        $emailMarketing->steps()->delete();
                    }
                }
                $emailMarketing->delete();

                if (request()->ajax()) {
                    return response()->json(['success' => true, 'message' => __('Data Successfully Deleted')]);
                }
                CRUDBooster::redirect(CRUDBooster::adminPath('email_marketings'), __('Data Successfully Deleted'), 'success');
            }

            throw new Exception(__('No valid data!'));

        } catch (Exception $exception) {
            if (request()->ajax()) {
                return response()->json(['success' => false, 'message' => __('Failed to Delete')]);
            }
            CRUDBooster::redirect(CRUDBooster::adminPath('email_marketings'), __('Failed to Delete'));
        }
    }


    public function updateHeaderTelegramPermission()
    {
        if ($_REQUEST['is_checked'] == 'true') {
            DB::table('cms_users')->where('id', '=', CRUDBooster::myParentId())->update(['is_telegram' => 1]);
            return response()->json(['success' => true], 200);
        }
        DB::table('cms_users')->where('id', '=', CRUDBooster::myParentId())->update(['is_telegram' => 0]);
        return response()->json(['success' => true], 200);
    }

    public function updateHeaderMobilePermission()
    {
        // if ($_REQUEST['is_checked'] == 'true') {
        //     DB::table('cms_users')->where('id', '=', CRUDBooster::myParentId())->update(['is_mobile_notify' => 1]);
        //     return response()->json(['success' => true], 200);
        // }
        // DB::table('cms_users')->where('id', '=', CRUDBooster::myParentId())->update(['is_mobile_notify' => 0]);
        return response()->json(['success' => true], 200);
    }

    //Search tags
    public function searchTags()
    {
        $term = $_REQUEST['term'];
        $result = [];

        if (isset($term)) {
            $result = DropfunnelTag::where('tag', 'LIKE', '%' . $term . '%')->where('user_id', '=', CRUDBooster::myParentId())->take(15)->select('id', 'tag as value')->get()->toArray();
            if (!empty($result)) return $result;
        }
        return $result;
    }

    public function addUserTags()
    {
        try {
            $tag = $_REQUEST['tag'];
            if (empty($tag)) throw new Exception('Tag can not be empty!');
            $user_id = CRUDBooster::myParentId();
            if (is_null($user_id)) throw new Exception('Invalid action!');

            $message = 'This tag already exist!';
            $status = 200;
            $user_tag = DropfunnelTag::where('user_id', $user_id)->where('tag', $tag)->select('id', 'tag')->first();
            if (empty($user_tag)) {
                $user_tag = DropfunnelTag::create(['tag' => $tag, 'user_id' => $user_id]);
                $message = 'Tag added successfully!';
                $status = 201;
            }

            if ($user_tag) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'id' => $user_tag->id,
                    'tag' => $user_tag->tag,
                ], $status);
            }

            throw new Exception('Tag not created. Please try again!');

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }


    //Delete campaign pivot data
    public function deleteExistingCampaignPivot()
    {
        $campaign_tag = DB::table('campaign_tags')
            ->where('campaign_id', '=', $_REQUEST['id'])
            ->where('tag_id', '=', $_REQUEST['tag_id'])
            ->delete();
        if ($campaign_tag) {
            return response(['success' => true]);
        }
        return response(['success' => false]);
    }

    public function loadModalEmailForm(LaravelRequest $request)
    {
        $step_id = $request->step;
        $campaign = EmailMarketing::with('steps:campaign_id,id,step_name,position')->select('id', 'campaign_name')->where('user_id', CRUDBooster::myParentId())->find($request->campaign_id);
        if (empty($campaign)) {
            $campaign = null;
        }
        $html = view('admin.drm_email_marketings.partials.test_email', compact('campaign','step_id'))->render();
        return response()->json(['success' => true, 'data' => $html, 'title' => __('Send A Test Email')]);
    }

    //Load all customer list
    public function loadCustomerEmailList()
    {
        $customers = $this->findCustomersByCampaignId($_REQUEST['id']);
        if ($customers->isNotEmpty()) {
            $customerIds = $customers->pluck('id')->toArray();
            $campaign_id = $_REQUEST['id'];
            $user_id = CRUDBooster::myParentId();

            $customers = DB::table('new_customers')->whereIntegerInRaw('new_customers.id', $customerIds)->leftJoin('campaign_history', function ($join) use ($campaign_id) {
                $join->on('new_customers.id', '=', 'campaign_history.customer_id')
                ->where('campaign_history.campaign_id', '=', $campaign_id);
            })
            ->orderBy('campaign_history.id', 'DESC');

            if (!CRUDBooster::isSuperadmin()) {
                $customers->where('new_customers.user_id', $user_id);
            }

            $customers = $customers->select('new_customers.id', 'new_customers.email', 'new_customers.full_name', 'campaign_history.created_at')->get();

            $html = view('admin.drm_email_marketings.partials.customer_email', compact('customers'))->render();
        } else {
            $html = '<p><strong>No Customer Found With Matching Tags</strong></p>';
        }
        return response()->json(['success' => true, 'data' => $html], 200);
    }


    public function getDownloadEmail($id)
    {
        $user_id = CRUDBooster::myParentId();

        $campaign = EmailMarketing::where('id', '=', $id);
        if(!CRUDBooster::isSuperadmin())
        {
            $campaign->where('user_id', $user_id);
        }
        $campaign = $campaign->select('id')->first();

        if(empty($campaign)) return back()->with(['message' => 'Invalid access!']);

        $customers = $this->findCustomersByCampaignId($id);
        if(!DrmUserHasPurchasedApp($user_id, 92))
        {
            $customers = $customers->slice(0, 25);
        }


        $file = new \SplTempFileObject();
        $file->setFlags(\SplFileObject::READ_CSV);
        $file->setCsvControl(';');
        $csv = Writer::createFromFileObject($file);


        foreach($customers as $customer)
        {
            $csv->insertOne([$customer->email]);
        }

        $csv->output('campaign_email_list.csv');
        die;
    }

    //All completed customer email
    public function postCompletedEmailList()
    {
        try {
            $request = $_REQUEST;
            $valid = Validator::make($request, [
                'id' => 'required|exists:email_marketings,id',
            ]);

            if ($valid->fails()) throw new Exception("Invalid action!");

            $user_id = CRUDBooster::myParentId();
            $id = $request['id'];

            // $customers = NewCustomer::whereHas('campaign_history', function ($c) use ($id) {
            //     return $c->where('campaign_id', $id);
            // });
            // if (!CRUDBooster::isSuperadmin()) {
            //     $customers->where('user_id', $user_id);
            // }

            // $customers = $customers->select('id', 'email', 'full_name')->get();
            $customers = $this->findCustomersByCampaignId($id);
            $customerIds = $customers->isEmpty() ? [] : $customers->pluck('id')->toArray();

            $completedCustomers = DB::table('email_marketings AS em')
                        // ->join('dropfunnel_step_mails AS s', 'em.id', '=', 's.campaign_id')
                        ->join('mail_gun_web_hook_histories AS h', 'em.id', '=', 'h.campaign_id')
                        ->where('em.id', $id)
                        ->whereNull('h.failed')
                        ->whereIntegerInRaw('h.customer_id', $customerIds)
                        ->groupBy('h.customer_id')
                        // ->havingRaw('COUNT(DISTINCT s.id) = (SELECT COUNT(*) FROM dropfunnel_step_mails WHERE campaign_id = ' . $id . ')')
                        ->distinct('h.customer_id')
                        ->pluck('h.customer_id')->toArray();


            // $customers = DB::table('new_customers')->join('campaign_history', function ($join) use ($id) {
            //     $join->on('new_customers.id', '=', 'campaign_history.customer_id')
            //     ->where('campaign_history.campaign_id', '=', $id);
            // })
            // ->orderBy('campaign_history.id', 'DESC');

            $customers = DB::table('new_customers')->whereIntegerInRaw('new_customers.id', $completedCustomers)->leftJoin('campaign_history', function ($join) use ($id) {
                $join->on('new_customers.id', '=', 'campaign_history.customer_id')
                ->where('campaign_history.campaign_id', '=', $id);
            })
            ->orderBy('campaign_history.id', 'DESC');

            if (!CRUDBooster::isSuperadmin()) {
                $customers->where('new_customers.user_id', $user_id);
            }
            $customers = $customers->select('new_customers.id', 'new_customers.email', 'new_customers.full_name', 'campaign_history.created_at')->get();

            // Here we are finding last email got customer ids, unsubscribe customer ids and subtract it from total first email got customers
            $lastEmailGetCustomerIds = $this->getLastStepEmailSendCount($id, false)->pluck('id')->toArray();
            $unsubscribeCustomerIds = $this->getUnsubscribeCustomerIds($user_id, $id);

            $totalCustomerExcludedIds = array_values( array_unique(array_merge($lastEmailGetCustomerIds, $unsubscribeCustomerIds)) );

            $customers = $customers->filter(function($customer) use($totalCustomerExcludedIds){
                if(!in_array($customer->id, $totalCustomerExcludedIds)) return $customer;
            })->values();
            // Here we are finding last email got customer ids, unsubscribe customer ids and subtract it from total first email got customers END

            $html = '';
            if ($customers->isNotEmpty()) {
                $html = view('admin.drm_email_marketings.partials.completed_list', compact('customers'))->render();
            } else {
                $html = '<p><strong>No Customer Found</strong></p>';
            }
            return response()->json(['success' => true, 'data' => $html]);

        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    //All devivered customer email
    public function postDeliveredEmailList()
    {
        try {
            $request = $_REQUEST;
            $valid = Validator::make($request, [
                'id' => 'required|exists:email_marketings,id',
                'event' => 'required|in:delivered,opened,clicked'
            ]);

            if ($valid->fails()) throw new Exception("Invalid action!");

            $user_id = CRUDBooster::myParentId();
            $id = $request['id'];
            $event = $request['event'];

            $customers = NewCustomer::whereHas('mailgun_history', function ($c) use ($id, $event) {
                return $c->where('campaign_id', $id)->whereNotNull($event);
            });
            if (!CRUDBooster::isSuperadmin()) {
                $customers->where('user_id', $user_id);
            }

            $customers = $customers->select('id', 'email', 'full_name')->get();

            $html = '';
            if ($customers->isNotEmpty()) {
                $html = view('admin.drm_email_marketings.partials.delivered_list', compact('customers'))->render();
            } else {
                $html = '<p><strong>No Customer Found</strong></p>';
            }
            return response()->json(['success' => true, 'data' => $html]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    //Get stepwise delivered emails
    public function postStepwiseDeliveredEmailList()
    {


        try {
            $request = $_REQUEST;
            $valid = Validator::make($request, [
                'id' => 'nullable',
                'event' => 'required|in:delivered,opened,clicked',
                'nullCheck' => 'nullable',
                'campaign_id' => 'nullable'
            ]);

            if ($valid->fails()) throw new Exception("Invalid action!");

            $user_id = CRUDBooster::myParentId();
            $id = $request['id'];
            $campaign_id = $request['campaign_id'];
            $event = $request['event'];
            $nullCheck = $request['nullCheck'];

            if($id == -1){
                $id = null;
            }

            $all_customers = $this->findCustomersByCampaignId($campaign_id);
            $customer_ids = $all_customers->pluck('id')->toArray();

            if($event == 'delivered' && $nullCheck == '1'){
                $customers = NewCustomer::whereIntegerInRaw('id', $customer_ids)->whereDoesntHave('mailgun_history', function ($c) use ($id, $campaign_id) {
                    return $c->where('step_id', $id)->where('campaign_id', $campaign_id);
                });
            }

            else{
                $customers = NewCustomer::whereIntegerInRaw('id', $customer_ids)->whereHas('mailgun_history', function ($c) use ($id, $campaign_id, $event, $nullCheck) {
                    if($nullCheck == '1'){
                        return $c->where('step_id', $id)->where('campaign_id', $campaign_id)->whereNull($event);
                    }
                    else{
                        return $c->where('step_id', $id)->where('campaign_id', $campaign_id)->whereNotNull($event);
                    }
                });
            }
            if (!CRUDBooster::isSuperadmin()) {
                $customers->where('user_id', $user_id);
            }

            $customers = $customers->select('id', 'email', 'full_name')->get();

            $html = '';
            if ($customers->isNotEmpty()) {
                $html = view('admin.drm_email_marketings.partials.delivered_list', compact('customers'))->render();
            } else {
                $html = '<p><strong>No Customer Found</strong></p>';
            }
            return response()->json(['success' => true, 'data' => $html]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }


    //All devivered customer email

    public function banUsers()
    {
        $apps = DB::table('purchase_apps as app')
            ->join('cms_users as users', 'app.cms_user_id', '=', 'users.id')
            ->where('app.type', '!=', 'Free Trail')
            ->groupBy('app.cms_user_id')
            ->select('app.*')
            ->get();

        $canNotBlock = [];
        if ($apps->isNotEmpty()) {
            foreach ($apps as $app) {
                $canNotBlock[] = $app->cms_user_id;
            }
        }

        $plans = DB::table('purchase_import_plans as plan')
            ->join('cms_users as users', 'plan.cms_user_id', '=', 'users.id')
            ->groupBy('plan.cms_user_id')
            ->select('plan.*')
            ->get();

        if ($plans->isNotEmpty()) {
            foreach ($plans as $plan) {
                if (!in_array($plan->cms_user_id, $canNotBlock)) {
                    $canNotBlock[] = $plan->cms_user_id;
                }
            }
        }

        $userLogs = DB::table('cms_logs as log')
            ->join('cms_users as users', 'log.id_cms_users', '=', 'users.id')
            ->groupBy('log.id_cms_users')
            ->select('log.*')
            ->get();
        if ($userLogs->isNotEmpty()) {
            $old = Carbon::now()->subMonths(3);
            foreach ($userLogs as $log) {
                if (Carbon::parse($log->created_at)->isSameDay($old)) { // if active before 3 month
                    continue;
                }
                if (!in_array($log->id_cms_users, $canNotBlock)) {
                    $canNotBlock[] = $log->id_cms_users;
                }
            }
        }

        $orders = DB::table('new_orders as order')
            ->join('cms_users as users', 'order.cms_user_id', '=', 'users.id')
            ->groupBy('order.cms_user_id')
            ->select('order.*')
            ->get();
        if ($orders->isNotEmpty()) {
            $old = Carbon::now()->subMonths(3);
            foreach ($orders as $order) {
                if (Carbon::parse($order->created_at)->isSameDay($old)) { // if active before 3 month
                    continue;
                }
                if (!in_array($order->cms_user_id, $canNotBlock)) {
                    $canNotBlock[] = $order->cms_user_id;
                }
            }
        }
        // block user
        DB::table('cms_users')->whereNotIn('id', $canNotBlock)->update(['status' => null]);
        $blocked_user = DB::table('cms_users')->whereNotIn('id', $canNotBlock)->pluck('id')->toArray();
        $blockHistory = new BlockUserHistory();
        $blockHistory->user_id = $blocked_user;
        $blockHistory->save();
        echo "Older users are blocked";
    }

    public function storeUserSpentTime()
    {
        $redirect = false;
        $inactive_seconds = 0;
        if (!empty($_REQUEST['user_id'])) {
            //DB::beginTransaction();
            try {
                $request = $_REQUEST;
                Validator::make($request, [
                    'user_id' => 'required|exists:cms_users,id',
                ])->validate();

                $id = $_REQUEST['user_id'];
                $superadmins = DB::table('cms_privileges')->where('is_superadmin', 1)->select('id')->pluck('id')->toArray();
                $user = DB::table('cms_users')->where('id', $id)->select('id_cms_privileges as priv', 'status')->first();

                $is_superadmin = in_array($user->priv, $superadmins);
                $is_blocked = empty($user->status) && !$is_superadmin;

                if ($is_blocked) {
                    // OFF $redirect = true;
                    throw new Exception('Sorry! Your account has been blocked. Please contact support.');
                }

                //Is inactive for 6 hours
                //calculate user activity
                $last_tracker_spend_time = TrakerTimeSpent::where('user_id', $id)->max('created_at');
                $last_tracker_time = Traker::where('user_id', $id)->max('created_at');
                $last_activity = max($last_tracker_spend_time, $last_tracker_time);

                //if user has last activity
                if ($last_activity) {
                    $now = Carbon::now();
                    $inactive_time = Carbon::parse($last_activity)->diffInHours($now);
                    $inactive_seconds = Carbon::parse($last_activity)->diffInSeconds($now);
                    $inactive_seconds = @($inactive_seconds - 5);
                    //if inactive for 6 hours
                    if ($inactive_time >= 6) {
                        // OFF $redirect = true;
                        throw new Exception('Inactive for ' . $inactive_time->h . ' hours.');
                    }
                }

                if (!empty(CRUDBooster::isSuperadmin()) || !empty(session()->get('logged_as')) ) {
                    $redirect = false;
//                    throw new Exception('Login via admin');
                    return response()->json(['success' => false, 'error' => 'Login via admin', 'redirect' => $redirect], 400);
                }

                $timeSpent = new TrakerTimeSpent();
                $timeSpent->user_id = $_REQUEST['user_id'];
                $timeSpent->href = $_REQUEST['href'];
                $timeSpent->seconds = $_REQUEST['seconds'];
                $timeSpent->save();

                if ($this->isPageViewExists($_REQUEST['user_id'], $_REQUEST['href'])) {
                    $pageView = new TrakerPageView();
                    $pageView->user_id = $_REQUEST['user_id'];
                    $pageView->href = $_REQUEST['href'];
                    $pageView->save();
                }

                //DB::commit();
                return response()->json(['success' => true, 'last_activity' => $last_activity, 'redirect' => false, 'inactive_seconds' => $inactive_seconds], 200);
            } catch (Exception $exception) {
                //DB::rollBack();
                return response()->json(['success' => false, 'error' => $exception->getMessage(), 'redirect' => $redirect], 400);
            }
        }
    }

    private function isPageViewExists($id, $href)
    {
        $flag = true;
        $tracker = TrakerPageView::latest()->first();
        if (!empty($tracker)) {
            if (empty($id)) {
                $flag = false;
            }
            $now = Carbon::now();
            if (($tracker->user_id == $id) && ($now->isSameDay($tracker->created_at)) && ($href == $tracker->href)) {
                $flag = false;
            }
        }
        return $flag;
    }

    public function getSearchProduct()
    {
        $request = $_REQUEST;
        $output = [];
        $tags = [];
        $messages = [];
        try {
            if (isset($request['col']) && isset($request['val'])) {
                $col = trim($request['col']);
                $val = trim($request['val']);

                $products = [];
                if ($col && $val) {
                    $products['drm_products'] = DB::table('drm_products')
                        ->select('drm_products.id', 'drm_products.title', 'drm_products.description', 'drm_products.image', 'drm_products.stock', 'drm_products.vk_price', 'drm_products.tags', 'drm_products.ek_price')
                        ->whereNull('drm_products.deleted_at')
                        ->whereNotNull('drm_products.title')
                        ->whereNotNull('drm_products.tags')
                        ->where('drm_products.tags', '<>', '')
                        ->whereRaw('cast(drm_products.stock as SIGNED) > 0')
                        ->where('drm_products.user_id', CRUDBooster::myParentId());
                    switch ($col) {
                        case 'tags':
//                            $products['drm_products'] = $products['drm_products']->whereRaw('FIND_IN_SET(?,tags)', [$val]);
                            $products['drm_products'] = $products['drm_products']
                                ->leftJoin('product_tags', 'product_tags.product_id', '=', 'drm_products.id')
                                ->where(function ($query) use ($val) {
                                    $query->where('drm_products.tags', 'LIKE', '%' . $val . '%')->orWhere('product_tags.tag', 'LIKE', '%' . $val . '%');
                                });
                            break;

                        case 'title':
                            $products['drm_products'] = $products['drm_products']->whereRaw("CONVERT(drm_products.title using 'utf8') LIKE '%" . $val . "%'");
                            break;

                        case 'ean':
                            $products['drm_products'] = $products['drm_products']->where('drm_products.ean', 'LIKE', '%' . $val . '%');
                            break;
                    }
                    $products['drm_products'] = $products['drm_products']->get()->toArray();
                }

                $item_array = collect($products)->filter()->all();
                if ($item_array) {
                    foreach ($item_array as $key => $items) {
                        foreach ($items as $p) {
                            $p = (array)$p;
                            $image = null;
                            if (isset($p['image']) && !is_null($p['image'])) {
                                $img_data = $p['image'];
                                if (drmIsJSON($img_data)) {
                                    $json = reset(json_decode($img_data, true));
                                    if ($json && is_array($json)) {
                                        $image = $json['src'];
                                    } else {
                                        $array = array_filter(explode(';', $json));
                                        $image = $array[0];
                                    }
                                }
                            }
                            $i = [];
                            if (!empty($p['title'])) {
                                $titleData = $p['title'];
                                if (drmIsJSON($titleData)) {
                                    $jsonTitle = reset(json_decode($titleData, true));
                                    if ($jsonTitle && is_array($jsonTitle)) {
                                        $titleData = $jsonTitle;
                                    } else {
                                        $titleData = $jsonTitle;
                                    }
                                }
                                $i['name'] = $titleData;
                            }
                            $i['id'] = $p['id'];
                            $i['price'] = $p['ek_price'];
                            $i['image'] = $image;
                            $i['desc'] = $p['description'];
                            $i['quantity'] = $p['stock'];
                            $tag = $p['tags'];
                            if ($tag && preg_match_all('~(#\w+\W?\w+)~', $tag, $matches)) {
                                $product_tags = implode(", ", $matches[1]);
                                $i['tags'] = $tags[] = rtrim($product_tags, ',');
                            }
                            $output[] = $i;
                        }
                    }
                }

                $output = collect($output);
                $tags = array_unique($tags);
                $tags = implode(", ", $tags);
                $tags = rtrim($tags, ',');
            }
        } catch (Exception $e) {
            return $e->getMessage();
        }
        return response()->json([
            'data' => $output,
            'tags' => $tags,
            'messages' => $messages
        ]);
    }

    public function loadTemplateList()
    {
        $templates = DB::table('html_drop_funnel_templates as template')
            ->select('template.*')
            ->join('cms_users as users', 'users.id', '=', 'template.user_id')
            ->leftJoin('cms_privileges as cp', 'users.id_cms_privileges', '=', 'cp.id')
            ->where('template.user_id', '=', CRUDBooster::myParentId())
            ->orWhere('cp.is_superadmin', '=', 1) // if super admin
            ->get();
        $html = view('admin.drm_email_marketings.html_drop_funnel.index', compact('templates'))->render();
        return response()->json(['success' => true, 'html' => $html], 200);
    }

    public function storeDisplayProduct()
    {
        if (Session::has('display_products')) {
            Session::forget('display_products');
        }

        session(['display_products' => $_REQUEST['display_product']]);
        return response()->json(['success' => true, 'data' => Session::get('display_products')], 200);
    }

    public function createEmailTemplate()
    {
        $data = [];
        $data['page_title'] = 'Create Email Template';
        return view('admin.drm_email_marketings.html_drop_funnel.create', $data);
    }

    public function getTemplateData()
    {
        $template = HtmlDropFunnelTemplate::find($_REQUEST['id']);
        if (!empty($template)) {
            return response()->json(['success' => true, 'data' => $template], 200);
        }
        return response()->json(['success' => false], 200);
    }

    public function storeNewTemplate(LaravelRequest $request)
    {
        Validator::make($request->all(), [
            'name' => 'required|string',
            'thumbnail' => 'required|image|max:300',
            'email_template' => 'required'
        ])->validate();

        try {
            $drop_funnel = new HtmlDropFunnelTemplate();
            $drop_funnel->name = $request->name;
            $drop_funnel->template = $request->email_template;
            $drop_funnel->thumbnail = uploadImage($request->file('thumbnail'), 'user/drop_template_thumb');
            $drop_funnel->user_id = CRUDBooster::myParentId();
            $drop_funnel->save();
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Successfully Stored', 'success');
        } catch (Exception $exception) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Something Went Wrong.' . PHP_EOL . $exception->getMessage(), 'warning');
        }
    }


    public function saveAsTemplate(LaravelRequest $request)
    {
        try {
            $data = $request->except('_token');
            $name = $data['template_name'];
            $file = $request->hasFile('template_image');
            $templateFile = $data['template'];

            $dropTemplate = new HtmlDropFunnelTemplate();
            $dropTemplate->user_id = CRUDBooster::myParentId();
            $dropTemplate->name = $name;
            $dropTemplate->save();

            $dom = new \DOMDocument();
            @$dom->loadHtml($templateFile, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);

            $images = $dom->getElementsByTagName('img');
            foreach ($images as $k => $img) {
                $image = $img->getAttribute('src');
                $pattern = '/templates\/template_/';
                if (preg_match($pattern, $image)) {
                    continue;
                } else {
                    $image = preg_replace('/^data:image\/\w+;base64,/', '', $image);
                    $image = str_replace(' ', '+', $image);
                    if (!is_null($image)) {
                        $url = $this->uploadImage($dropTemplate->id, base64_decode($image), $k);
                        $img->removeAttribute('src');
                        $img->setAttribute('src', $url);
                    }
                }
            }

            $stringHtml = $dom->saveHTML($dom->documentElement);

            $dropTemplate->template = $stringHtml;
            if ($file) {
                $realImage = @file_get_contents($request->template_image);
                $dropTemplate->thumbnail = $this->uploadImage($dropTemplate->id, $realImage, Str::random(20));
            }
            $dropTemplate->save();

            return response()->json(['success' => true], 200);
        } catch (Exception $ex) {
            return response()->json(['success' => false], 200);
        }
    }

    public function uploadTemplate(LaravelRequest $request)
    {
        Validator::make($request->all(), [
            'zip_template' => 'required|file|mimes:zip',
        ])->validate();
        $file = $request->file('zip_template');

        $path = $this->extractFile($file); // return path or null
        if ($path) {
            $htmlFiles = $thumb = [];
            $filesInFolder = File::files($path);
            // $filesInFolder = File::allFiles($path);
            foreach ($filesInFolder as $filePath) {
                $file = pathinfo($filePath);
                if ($file['extension'] === 'html') {
                    $htmlFiles[] = $file['basename'];
                }
                if ($file['extension'] === 'png') {
                    $thumb[] = $file['basename'];
                }
            }
            //DB::beginTransaction();
            try {
                foreach ($htmlFiles as $key => $name) {
                    $dropTemplate = new HtmlDropFunnelTemplate();
                    $dropTemplate->user_id = CRUDBooster::myParentId();
                    $dropTemplate->name = $name;
                    $dropTemplate->save();
                    $this->modifyAndUploadData($path, $name, $dropTemplate->id);
                    $search = array_search(explode('.', $name)[0] . '.png', $thumb);
                    if ($search !== false) {
                        $realImage = @file_get_contents($path . '/' . $thumb[$search]);
                        if ($realImage) {
                            $url = $this->uploadImage($dropTemplate->id, $realImage, $key);
                            DB::table('html_drop_funnel_templates')->where('id', '=', $dropTemplate->id)
                                ->update(['thumbnail' => $url]);
                        }
                    }
                }
                //DB::commit();
                if (File::isDirectory($path)) {
                    File::deleteDirectory($path);
                }
                return response()->json(['success' => true], 200);
            } catch (Exception $exception) {
                if (File::isDirectory($path)) {
                    File::deleteDirectory($path);
                }
                //DB::rollBack();
            }
        }

        return response()->json(['success' => false], 200);
    }

    private function extractFile($file)
    {
        $zip = new \ZipArchive;
        if ($zip->open($file) === TRUE) {
            $path = public_path('temp_drop_funnel_template');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }
            $zip->extractTo($path);
            $zip->close();
            return $path;
        }
        return null;
    }

    private function modifyAndUploadData($path, $fileName, $id)
    {
        $myfile = @file_get_contents($path . '/' . $fileName);
        if ($myfile) {
            $template = $this->uploadImageAndModifyDom($myfile, $path, $id);
            if (!empty($template)) {
                DB::table('html_drop_funnel_templates')->where('id', '=', $id)
                    ->update(['template' => $template]);
            }
        }
    }

    private function uploadImageAndModifyDom($myfile, $path, $id)
    {
        $dom = new \DOMDocument();
        @$dom->loadHtml($myfile, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        $images = $dom->getElementsByTagName('img');
        foreach ($images as $k => $img) {
            $image = $img->getAttribute('src');
            if (!is_null($image)) {
                $realImage = @file_get_contents($path . '/' . $image);
                if ($realImage) {
                    $url = $this->uploadImage($id, $realImage, $k);
                    $img->removeAttribute('src');
                    $img->setAttribute('src', $url);
                }
            }
        }
        $stringHtml = $dom->saveHTML($dom->documentElement); // dom object to html string
        // take inside body tag doms
        $startPos = strpos($stringHtml, '<style');
        $endPos = strpos($stringHtml, '</head>');
        $expectedOutput = substr($stringHtml, $startPos, $endPos);
        $expectedOutput = strstr($expectedOutput, '</head>', true);
        $expectedOutput = preg_replace('/\* {1}/', '.template-cls *', $expectedOutput);
        $expectedOutput = preg_replace('/\. {0}/', '.template-cls .', $expectedOutput); // after dot no space
        $expectedOutput = preg_replace('/html/', '.template-cls', $expectedOutput);
        $expectedOutput = preg_replace('/body/', '.template-cls', $expectedOutput);

        $start = strpos($stringHtml, '<body');
        $end = strpos($stringHtml, '</body');
        $expected = substr($stringHtml, $start, $end);
        $finalResult = str_replace("</body>", "</div>", str_replace("<body", "<div", str_replace("</html>", "", $expected)));
        return '<div class="template-cls">' . PHP_EOL . $expectedOutput . $finalResult . '</div>';
    }

    public function previewTemplateData()
    {
        $request = $_REQUEST;
        $request['user_id'] = CRUDBooster::myParentId();
        $request['email_template'] = $request['template'];

        $signature = $this->getMatchSignature($request);
        $products = $this->getMatchTagProducts($request);

        $template = $_REQUEST['template'];
        $countDown = $this->getMatchingCountDown($template);

        $linkPattern = '/\[drm-link-\d*]/';
        if (preg_match_all($linkPattern, $template, $matches)) {

            foreach ($matches[0] as $key => $value){
                preg_match('/\d+/',$value, $matchedId);
                $link_id = $matchedId[0];
                $external_link = DB::table('dropfunnel_external_links')->where('id', $link_id)->first();
                $link_new = url('/insert-tag-by-link').'?slug='.$external_link->slug.'-[customer_id]';
                if (!empty($external_link)) {
                    $template = preg_replace('/\[drm-link-'.$link_id.']/', $link_new, $template);
                }
            }
        }

        $pattern = '/\[# {0}(\_{1}|\w* ?\w*|\d*)]/'; //'/\[# {0}\w*]/';
        if ($products->isNotEmpty()) {
            $percentage = $request['percentage'];
            $tax = $request['tax'];
            $renderedFile = view('admin.drm_email_marketings.partials._preview_template', compact('products', 'percentage', 'tax'))->render();
            $html = preg_replace($pattern, $renderedFile, $template, 1);
            $html = preg_replace($pattern, '', $html);
        } else {
            $html = preg_replace($pattern, '', $template);
        }

        $invoice_setting = DB::table('drm_invoice_setting')->select('email', 'logo', 'store_name')->where('cms_user_id', CRUDBooster::myParentId())->orderBy('id', 'desc')->first();
        if (empty($invoice_setting)) {
            $img = '';
        } else {
            $logo = ($invoice_setting && $invoice_setting->logo) ? $invoice_setting->logo : '';
            $imgAltName = $invoice_setting && $invoice_setting->store_name ? $invoice_setting->store_name : 'Dummy Shop Logo';
            $img = '<div><img width="150" src="' . $logo . '" alt="' . $imgAltName . '" ></div>';
        }
        if(isLocal() || in_array($request['user_id'], [212,2661])){
            $firstCustomer = NewCustomer::where('user_id',$request['user_id'])->orderby('id','desc')->select('id')->first();
        }

        $signaturePattern = '/\[drm-sign-\d*]/';
        $faker = \Faker\Factory::create();
        $html = preg_replace('/\[shop_logo]/', $img, $html);
        $html = preg_replace('/\[customer_name]/', $faker->name, $html);
        $html = preg_replace('/\[company_name]/', $faker->company, $html);
        $html = preg_replace($signaturePattern, $signature, $html, 1);
        $html = preg_replace($signaturePattern, '', $html);
        $countDownPattern = '/\[drm-countdown-\d*]/';
        $html = preg_replace($countDownPattern, $countDown, $html, 1);
        $html = preg_replace($countDownPattern, '', $html);


        $html = str_replace('"/admin/','"',$html);
        $html = preg_replace('/\[customer_id]/', $firstCustomer->id ?? '', $html);

        return response()->json(['success' => true, 'data' => $html], 200);
    }


    private function getMatchTagProducts($request)
    {
        $html = $request['template'];
        $withOutTag = strip_tags($html);
        if (preg_match_all('/\[# {0}(\_{1}|\w* ?\w*|\d*)]/', $withOutTag, $matches)) {
            $shapedArr = $this->array_flatten($matches[1]);
            $tags = !empty($shapedArr) ? array_unique($shapedArr) : [];
            $tags = !empty($tags) ? array_map(function ($value) {
                return '#' . trim($value);
            }, $tags) : [];
            $query = DB::table('drm_products')
                ->select('id', 'title', 'description', 'image', 'stock', 'vk_price', 'tags', 'ek_price')
                ->whereNull('deleted_at')
                ->whereRaw('cast(stock as SIGNED) > 0')
                ->where('user_id', CRUDBooster::myParentId());

            $query = $query->where(function ($q) use ($tags) {
                foreach ($tags as $key => $tag) { // for comma separated value search
                    if ($key == 0)
                        $q->whereRaw('FIND_IN_SET(?,drm_products.tags)', [$tag]);
                    else
                        $q->orWhereRaw('FIND_IN_SET(?,drm_products.tags)', [$tag]);
                }
            });
            $query = $query->orderByRaw('RAND()');
            if (Session::has('display_products')) {
                $query = $query->take(Session::get('display_products'));
            } else {
                $query = $query->take(1);
            }
            $query = $query->get();
            if ($query->isNotEmpty())
                return $query;
        }
        return collect();
    }

    public function getMarketingEmailSettings()
    {
        if (CRUDBooster::isSubUser() && !((sub_account_can('email_setting') || sub_account_can('all_modules', 122)) || \CRUDBooster::isDropmatixSupport())) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $data['page_title'] = __('emailSetting.Handling_time_email_settings');
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('marketing_email_settings')->where('cms_user_id', $authId)->whereNull('channel')->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myParentId());
        return view('admin.drm_email_marketings.email_settings', $data);
    }

    public function postAddSaveEmailSettings()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $content = str_replace('<p>', '<p style="margin:0 !important;padding: 0 !important;">', $_REQUEST['email_template']);

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'email_template' => $content,
            'created_at' => now(),
            'updated_at' => now(),
        ];
        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        if (!empty($_REQUEST['channel'])) {
            $data['channel'] = $_REQUEST['channel'];
        }

        DB::table('marketing_email_settings')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myParentId(),
                'channel' => !empty($_REQUEST['channel']) ? $_REQUEST['channel'] : NULL
            ],
            $data
        );

        if(!empty($_REQUEST['channel'])){
            Cache::forget('marketing_email_settings_' . CRUDBooster::myParentId() . '_' . $_REQUEST['channel']);
        }else{
        Cache::forget('marketing_email_settings_' . CRUDBooster::myParentId());
        }

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postSendTestEmail()
    {
        try {
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order(); //$this->generate_fake_order();
            $invoice_data['page_title'] = 'Test order email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => $order->invoice_number,
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $template = DRMParseHandlingTimeEmailTemplate($tags, $order->cms_user_id);
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $pdf_stream = PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream();
            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
                $messages->attachData($pdf_stream, 'invoice.pdf', [
                    'mime' => 'application/pdf',
                ]);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function getMarketingEmailSettingsByChannel()
    {
        if (CRUDBooster::isSubUser() && !((sub_account_can('email_setting') || !sub_account_can('all_modules', 122)) || \CRUDBooster::isDropmatixSupport())) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $data['page_title'] = __('emailSetting.Handling_time_email_settings');

        $channel = $_REQUEST['channel'];
        $authId = CRUDBooster::myParentId();

        $data['mail'] = DB::table('marketing_email_settings')->where('cms_user_id', $authId)->where('channel', $channel)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['channel'] = (int) $channel;

        // return view('admin.drm_email_marketings.email_settings_by_channel', $data);
        return view('admin.drm_email_marketings.email_settings', $data);
    }

    public function postAddSaveEmailSettingsByChannel()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $content = str_replace('<p>', '<p style="margin:0 !important;padding: 0 !important;">', $_REQUEST['email_template']);

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'email_template' => $content,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        DB::table('marketing_email_setting_by_channels')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myParentId(),
                'channel' => $_REQUEST['channel']
            ],
            $data
        );

        Cache::forget('marketing_email_setting_by_channels_' . CRUDBooster::myParentId() . '_' . $_REQUEST['channel']);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postSendTestEmailByChannel()
    {
        try {
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order(); //$this->generate_fake_order();
            $invoice_data['page_title'] = 'Test order email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => $order->invoice_number,
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $data['email_to'] = $_REQUEST['test_email'];
            $data['channel'] = (int) $_REQUEST['channel'];

            $template = DRMParseHandlingTimeEmailTemplate($tags, $order->cms_user_id, $data['channel']);

            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $pdf_stream = PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream();
            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
                $messages->attachData($pdf_stream, 'invoice.pdf', [
                    'mime' => 'application/pdf',
                ]);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    // Droptinda Order Confirmation Template

    public function getDroptiendaOrderConfirmationTemplate()
    {
        if (CRUDBooster::isSubUser() && !((sub_account_can('email_setting') || !sub_account_can('all_modules', 122)) || \CRUDBooster::isDropmatixSupport())) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $data['page_title'] = __('emailSetting.Order_Confirmation_from_Droptienda');
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('droptienda_order_confirmation_template')->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        return view('admin.drm_email_marketings.order_confirmation_from_droptienda', $data);
    }

    public function postAddSaveDroptiendaOrderConfirmationTemplate()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'email_template' => $_REQUEST['email_template'],
            'created_at' => now(),
            'updated_at' => now(),
        ];
        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        DB::table('droptienda_order_confirmation_template')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myParentId()
            ],
            $data
        );

        Cache::forget('droptienda_order_confirmation_template_' . CRUDBooster::myParentId());

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    //Droptienda email template test email
    public function postSendTestDroptiendaOrderConfirmationTemplateEmail()
    {
        try {
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order(); //$this->generate_fake_order();
            $invoice_data['page_title'] = 'Test droptienda order confirmation email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => $order->invoice_number,
                'payment_method' => $order->payment_type? ucfirst($order->payment_type) : '',
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $template = DRMParseDroptiendaOrderEmailTemplate($tags, $order->cms_user_id);
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $pdf_stream = null;//PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream();
            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
                // $messages->attachData($pdf_stream, 'invoice.pdf', [
                //     'mime' => 'application/pdf',
                // ]);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function postSendTestStockShortageTemplateEmail()
    {
        try {
            $user_list = DrmProduct::select('user_id')->where('stock', '!=', null)->where('min_stock', '!=', null)->where('stock', '<', 'min_stock')->distinct()->get();
            foreach($user_list as $user){
                $email = DB::table('cms_users')->where('id', $user->user_id)->first();
                $product_list = DrmProduct::where('user_id', $user->user_id)->where('stock', '!=', null)->where('min_stock', '!=', null)->where('stock', '<', 'min_stock')->get();
                $product_data['page_title'] = 'Test drm product stock shortage email template';
                $product_data['product_list'] = $product_list;
                $product_data['user'] = DB::table('cms_users')->where('id', 212)->first();
                $product_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

                $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

                $logo = $product_data['setting']->logo ?? '';
                $tags = [
                    'user' => $product_data['user'],
                    'user_name' => $product_data['user']->name,
                    'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $producte_data['setting']->store_name . '" >',
                    'product_name' => $product->title['de'],
                    'product_table' => view('admin.drm_products.email_product_table', compact('product_list'))->render(),
                ];
                // $template = DRMParseStockShortageTemplate($tags, 212);
                // $data['email_to'] = "<EMAIL>";
                // $data['email_from'] = "<EMAIL>";

                // if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                //     throw new Exception("Something Wrong! Email Not Sent!.");
                // }

                // $data['subject'] = $template['subject'];

                // Mail::send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                //     $messages->from($data['email_from']);
                //     $messages->to($data['email_to']);
                //     $messages->subject($data['subject']);
                // });

                $slug = 'drm_product_stock_shortage_email_template';
                $mail_data = DRMParseMailTemplate($tags, $slug, 'de');
                app('drm.mailer')->getMailer()->to('<EMAIL>')->send(new DRMSEndMail($mail_data));
            }


            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent failed!') . ' ' . $e, 'error');
        }
    }

    // End Droptinda Order Confirmation Template

    public function deleteDropFunnelTemplate()
    {
        $template = HtmlDropFunnelTemplate::find($_REQUEST['id']);
        if (!empty($template)) {
            if ($template->delete()) {
                if (isLocal()) {
                    $cloud_image_location = 'drop_funnel_team_templates/template_' . $template->id;
                } else {
                    $cloud_image_location = 'drop_funnel_templates/template_' . $template->id;
                }
                if (Storage::disk('spaces')->exists($cloud_image_location)) {
                    Storage::disk('spaces')->deleteDirectory($cloud_image_location);
                }
                // delete thumbnail
                if (Storage::disk('spaces')->exists($template->thumbnail)) {
                    Storage::disk('spaces')->delete($template->thumbnail);
                }
                return response()->json(['success' => true, 'message' => 'Template Successfully Deleted']);
            }
        }
        return response()->json(['success' => false, 'message' => 'Failed to Delete']);
    }

    //Response log data
    public function postResponseLog()
    {
        $id = $_REQUEST['id'];
        $data = EmailMarketing::where('id', $id)->whereNotNull('log')->select('log')->first();
        if ($data) {
            return response()->json([
                'success' => true,
                'data' => $data->log,
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Not found!',
                'data' => null,
            ]);
        }
    }

    //mailgun log
    public function mailgunWebhookResponse()
    {
        try {

            $data = $_REQUEST;

            $this->sendTelegramNotificationForTanjil('mailgunWebhookResponse called with Data = ' . json_encode($data));

            //Phase-1 validation
            $validator = Validator::make($data, [
                'event' => ['required', Rule::in(['delivered'])],
                'X-Mailgun-Tag' => 'required|in:drm_dropfunnel_email',
            ]);

            if ($validator->fails()) {
                return;
            }

            //Phase-2 validation
            $validation = Validator::make($data, [
                'user_id' => 'required',
                'customer_id' => 'required',
                'campaign_id' => 'required',

                'campaign_name' => 'required',
                'timestamp' => 'required',
                'recipient' => 'required',

            ]);

            if ($validation->fails()) {
                return;
            }

            //assign values
            $user_id = $data['user_id'];
            $customer_id = $data['customer_id'];
            $campaign_id = $data['campaign_id'];

            $campaign_name = $data['campaign_name'];
            $timestamp = $data['timestamp'];
            $recipient = $data['recipient'];
            $event = $data['event'];

            $time = $timestamp ? Carbon::createFromTimestamp($timestamp)->format('Y-m-d H:i:s') : null;

            $insert_data = [
                $event => $time,
                'customer_email' => $recipient,
            ];

            $check_arr = [
                'user_id' => $user_id,
                'customer_id' => $customer_id,
                'campaign_id' => $campaign_id,
            ];

            if (!empty($data['step_mail_id'])) { // for step mail
                $check_arr['step_id'] = $data['step_mail_id'];
            }

            $insert_arr = array_filter($insert_data);
            $this->sendTelegramNotificationForTanjil('mailgunWebhookResponse called Before insert = ' . json_encode($insert_arr));
            MailGunWebHookHistory::updateOrCreate($check_arr, $insert_arr);

            if ($event == 'delivered' && !empty($data['last_step'])) {
                $customerIds = DB::table('campaign_history')->where(['campaign_id' => $campaign_id, 'user_id' => $user_id])->pluck('customer_id')->toArray();
                if (!empty($customerIds)) {
                    $campaign = DB::table('email_marketings')->select('stop_tag')->where(['id' => $campaign_id, 'user_id' => $user_id])->first();
                    foreach ($customerIds as $customerId) {
                        DropfunnelCustomerTag::insertTag($campaign->stop_tag, $user_id, $customerId, 12);
                    }
                }
            }

        } catch (Exception $e) {
            Log::channel('command')->info($e);
            $this->sendTelegramNotificationForTanjil('mailgunWebhookResponse called inside Exception = ' . $e->getMessage() . ' In File ' . $e->getFile() . ' In Line = ' . $e->getLine());
        }
    }


    //mailgun failed log
    public function mailgunWebhookFailed()
    {

        try {

            $raw_data = @json_decode(request()->getContent(), true);
            if (!is_array($raw_data)) throw new Exception('Data must be array!');

            //Phase-1 validation
            $validator = Validator::make($raw_data, [
                'event-data' => 'required|array',
                'event-data.tags' => ['required', 'array', function ($attribute, $value, $fail) {
                    if (!in_array('drm_dropfunnel_email', $value)) {
                        $fail('Event must have part of dropfunnel');
                    }
                }],
                'event-data.event' => 'required|in:failed',
                'event-data.recipient' => 'required',
                'event-data.timestamp' => 'required',
                'event-data.user-variables' => 'required|array',
            ]);
            $event_data_telegram = $raw_data['event-data'];
            $user_data_telegram = $event_data_telegram['user-variables'];
            $this->sendTelegramNotificationForTanjil('mailgunWebhookFailed called Before Validation Error = ' . json_encode($user_data_telegram));

            if ($validator->fails()) {
                throw new Exception($validator->errors()->first());
            }


            //event data
            $event_data = $raw_data['event-data'];


            $timestamp = $event_data['timestamp'];
            $recipient = $event_data['recipient'];
            $event = $event_data['event'];

            $user_data = $event_data['user-variables'];

            //Phase 2 - User data validation
            $validation = Validator::make($user_data, [
                'user_id' => 'required',
                'customer_id' => 'required',
                'campaign_id' => 'required',

                'campaign_name' => 'required',
            ]);

            if ($validation->fails()) {
                throw new Exception($validation->errors()->first());
            }


            //assign values
            $user_id = $user_data['user_id'];
            $customer_id = $user_data['customer_id'];
            $campaign_id = $user_data['campaign_id'];

            $campaign_name = $user_data['campaign_name'];


            if ($validator->fails()) {
                throw new Exception($validator->errors());
            }

            $time = $timestamp ? Carbon::createFromTimestamp($timestamp)->format('Y-m-d H:i:s') : null;

            $insert_data = [
                'failed' => $time,
                'customer_email' => $recipient,
            ];

            $check_arr = [
                'user_id' => $user_id,
                'customer_id' => $customer_id,
                'campaign_id' => $campaign_id,
            ];

            if (!empty($user_data['step_mail_id'])) { // for step mail
                $check_arr['step_id'] = $user_data['step_mail_id'];
            }

            $insert_arr = array_filter($insert_data);

            $this->sendTelegramNotificationForTanjil('mailgunWebhookFailed called Before insert = ' . json_encode($insert_arr));
            MailGunWebHookHistory::updateOrCreate($check_arr, $insert_arr);

            if ($event == 'delivered' && !empty($user_data['last_step'])) {
                $customerIds = DB::table('campaign_history')->where(['campaign_id' => $campaign_id, 'user_id' => $user_id])->pluck('customer_id')->toArray();
                if (!empty($customerIds)) {
                    $campaign = DB::table('email_marketings')->select('stop_tag')->where(['id' => $campaign_id, 'user_id' => $user_id])->first();
                    foreach ($customerIds as $customerId) {
                        DropfunnelCustomerTag::insertTag($campaign->stop_tag, $user_id, $customerId, 12);
                    }
                }
            }

            $delivery_message = @$event_data['delivery-status']['message'];
            if ($delivery_message) {
                User::find(71)->notify(new DRMNotification($campaign_name . ' mail sending to ' . $recipient . ' is failed. Error: ' . $delivery_message, 'DROPFUNNEL_CAMPAIGN_MAIL_FAILED', '#'));
            }

        } catch (Exception $e) {
            Log::channel('command')->info('Mailgun failed webhook exception:');
            Log::channel('command')->info($e);
            $this->sendTelegramNotificationForTanjil('mailgunWebhookFailed called inside Exception = ' . $e->getMessage() . ' In File ' . $e->getFile() . ' In Line = ' . $e->getLine());
            print($e->getMessage());
        }
    }

    //mailgun log
    public function mailgunWebhookResponseLegacy()
    {
        try {
            $raw_data = @json_decode(request()->getContent(), true);

            if (!is_array($raw_data)) throw new Exception('Data must be array!');

            //Phase-1 validation
            $validator = Validator::make($raw_data, [
                'event-data' => 'required|array',
                'event-data.tags' => ['required', 'array', function ($attribute, $value, $fail) {
                    if (!in_array('drm_dropfunnel_email', $value)) {
                        $fail('Event must have part of dropfunnel');
                    }
                }],
                'event-data.event' => ['required', function ($attribute, $value, $fail) {
                    if (!in_array(strtolower($value), ['delivered', 'opened', 'clicked', 'complained'])) {
                        $fail('Event must have opened or clicked');
                    }
                }],
                'event-data.recipient' => 'required',
                'event-data.timestamp' => 'required',
                'event-data.user-variables' => 'required|array',
            ]);
            $event_data_telegram = $raw_data['event-data'];
            $user_data_telegram = $event_data_telegram['user-variables'];
            $this->sendTelegramNotificationForTanjil('mailgunWebhookResponseLegacy called Before Validation Error = ' . json_encode($user_data_telegram));

            if ($validator->fails()) {
                throw new Exception($validator->errors()->first());
            }


            //event data
            $event_data = $raw_data['event-data'];


            $timestamp = $event_data['timestamp'];
            $recipient = $event_data['recipient'];
            $event = strtolower($event_data['event']);

            $user_data = $event_data['user-variables'];

            //Phase 2 - User data validation
            $validation = Validator::make($user_data, [
                'user_id' => 'required',
                'customer_id' => 'required',
                'campaign_id' => 'required',

                'campaign_name' => 'required',
            ]);

            if ($validation->fails()) {
                throw new Exception($validation->errors()->first());
            }


            //assign values
            $user_id = $user_data['user_id'];
            $customer_id = $user_data['customer_id'];
            $campaign_id = $user_data['campaign_id'];

            $campaign_name = $user_data['campaign_name'];


            if ($validator->fails()) {
                throw new Exception($validator->errors());
            }

            $time = $timestamp ? Carbon::createFromTimestamp($timestamp)->format('Y-m-d H:i:s') : null;

            $insert_data = [
                $event => $time,
                'customer_email' => $recipient,
            ];

            $check_arr = [
                'user_id' => $user_id,
                'customer_id' => $customer_id,
                'campaign_id' => $campaign_id,
            ];

            if (!empty($user_data['step_mail_id'])) { // for step mail
                $check_arr['step_id'] = $user_data['step_mail_id'];
            }

            $insert_arr = array_filter($insert_data);
            $this->sendTelegramNotificationForTanjil('mailgunWebhookResponseLegacy called Before insert = ' . json_encode($insert_arr));

            MailGunWebHookHistory::updateOrCreate($check_arr, $insert_arr);

            if ($event == 'delivered' && !empty($user_data['last_step'])) {
                $customerIds = DB::table('campaign_history')->where(['campaign_id' => $campaign_id, 'user_id' => $user_id])->pluck('customer_id')->toArray();
                if (!empty($customerIds)) {
                    $campaign = DB::table('email_marketings')->select('stop_tag')->where(['id' => $campaign_id, 'user_id' => $user_id])->first();
                    foreach ($customerIds as $customerId) {
                        DropfunnelCustomerTag::insertTag($campaign->stop_tag, $user_id, $customerId, 12);
                    }
                }
            }

        } catch (Exception $e) {
            Log::channel('command')->info($e);
            $this->sendTelegramNotificationForTanjil('mailgunWebhookResponseLegacy called inside Exception = ' . $e->getMessage() . ' In File ' . $e->getFile() . ' In Line = ' . $e->getLine());
        }
    }

    public function searchUserNameOrEmail(LaravelRequest $request)
    {
        if (!empty($request->term)) {
            $users = User::select('id', 'name', 'email')
                ->whereNotNull('email_verified_at')
                ->where(function ($query) use ($request) {
                    $query->where('email', 'like', '%' . $request->term . '%')->orWhere('name', 'like', '%' . $request->term . '%');
                })
                ->take(15)
                ->get();

            if ($users->isNotEmpty()) {
                $html = '';
                foreach ($users as $user) {
                    $html .= '<div class="checkbox">
                                <label class="shareCheck">
                                  <input type="checkbox" value="' . $user->id . '"> ' . $user->name . '(' . $user->email . ')
                                </label>
                            </div>';
                }
                return response()->json(['success' => true, 'data' => $html], 200);
            }
        }
        return response()->json(['success' => false, 'data' => null], 200);
    }

    public function shareCampaignWithUser(LaravelRequest $request)
    {
        try {
            $id = $request->id ?? null;
            $emailMarketing = EmailMarketing::where('user_id', CRUDBooster::myParentId())->where('id', $id)->first();
            if (!empty($emailMarketing)) {
                $userIds = $request->user_ids ?? [];
                $authId = CRUDBooster::myParentId();
                foreach ($userIds as $id) {
                    if ($id == $authId) { // prevent self sharing
                        continue;
                    }
                    $newCampaign = $emailMarketing->replicate();
                    $newCampaign->save();
                    $newCampaign->user_id = $id;
                    $newCampaign->shareable_url = null;
                    $newCampaign->slug = uniqid() . $id;
                    $newCampaign->save();
                }
                return response()->json(['success' => true, 'message' => 'Campaign Successfully Shared']);
            }
            return response()->json(['success' => false, 'message' => 'Something Went Wrong'], 400);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage()], 400);
        }
    }

    public function generateSharableUrl(LaravelRequest $request)
    {
        try {
            $id = $request->id ?? null;
            $authId = CRUDBooster::myParentId();
            $emailMarketing = EmailMarketing::where('user_id', $authId)->where('id', $id)->first();
            if (!empty($emailMarketing)) {
                $slug = $emailMarketing->slug;
                if (empty($slug)) {
                    $slug = uniqid() . $authId;
                    $emailMarketing->slug = $slug;
                    $emailMarketing->save();
                }
                $url = $emailMarketing->shareable_url;
                if (empty($url)) {
                    $url = url("/admin/email_marketings_share/$slug");
                    $emailMarketing->shareable_url = $url;
                    $emailMarketing->save();
                }
                return response()->json(['success' => true, 'message' => 'Campaign Url Successfully copied', 'url' => $url]);
            }
            return response()->json(['success' => false, 'message' => 'Something Went Wrong', 'url' => null], 400);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage(), 'url' => null], 400);
        }
    }

    public function replicateCampaignToUser($slug)
    {
        $emailMarketing = EmailMarketing::where('slug', '=', $slug)->first();
        if (!empty($emailMarketing)) {
            if (empty($emailMarketing->shareable_url)) {
                CRUDBooster::redirect(CRUDBooster::adminPath(), "Invalid URL Detected");
            }
            $authId = CRUDBooster::myParentId();
            if ($emailMarketing->user_id == $authId) { // prevent self sharing
                CRUDBooster::redirect(CRUDBooster::adminPath(), "You are currently owner of  this campaign");
            }
            $newCampaign = $emailMarketing->replicate();
            $newCampaign->save();
            $newCampaign->user_id = $authId;
            $newCampaign->shareable_url = null;
            $newCampaign->slug = uniqid() . $authId;
            $newCampaign->save();
            return redirect('/admin/email_marketings');
        }
        CRUDBooster::redirect(CRUDBooster::adminPath(), "Something Went Wrong");
    }

    public function getShareableModal($id)
    {
        $emailMarketing = EmailMarketing::where('user_id', CRUDBooster::myParentId())->where('id', $id)->first();
        if (!empty($emailMarketing)) {
            $html = view('admin.drm_email_marketings.partials._share_campaign_modal', compact('emailMarketing'))->render();
            return response()->json(['success' => true, 'data' => $html, 'title' => __('Campaign Share with')]);
        }
        return response()->json(['success' => false, 'data' => null]);
    }

    public function revokeShareableLink($id)
    {
        $emailMarketing = EmailMarketing::where('user_id', CRUDBooster::myParentId())->where('id', $id)->first();
        if (!empty($emailMarketing)) {
            $emailMarketing->shareable_url = null;
            $emailMarketing->save();
            return response()->json(['success' => true, 'message' => 'Successfully Url Revoked']);
        }
        return response()->json(['success' => false, 'message' => 'No Data Found']);
    }

    public function loadIframeCountDown($id)
    {
        $countDown = DropFunnelCountDown::where('user_id', CRUDBooster::myParentId)->find($id);
        if (!empty($countDown)) {
            $dateTime = "$countDown->event_date $countDown->event_time";
            $timeZone = $countDown->timezone ?? 'Europe/Berlin';
            $now = Carbon::now($timeZone);
            $eventDate = Carbon::parse($dateTime)->setTimezone($timeZone);
            if ($eventDate->lte($now))
                return 0;

            $seconds = $now->diffInSeconds($eventDate);
            return view('admin.drm_email_marketings.partials._count_down_data', compact('seconds'))->render();
        }
        return 0;
    }


    public function coundownOutput($slug)
    {
        try {
            $countDown = DropFunnelCountDown::withTrashed()->where('slug', $slug)->first();
            if (is_null($countDown)) return '';
            //expire this image instantly
            header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
            header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
            header('Cache-Control: no-store, no-cache, must-revalidate');
            header('Cache-Control: post-check=0, pre-check=0', false);
            header('Pragma: no-cache');

            $future_date = $countDown->event_date;
            $now = $countDown->current_date;

//            $now = Carbon::parse('2021-12-21');
//            $future_date = Carbon::parse('2021-12-22');

            $background = $this->hex2rgb( $countDown->bg_color );
            $color =  $this->hex2rgb( $countDown->color );
            return $this->generateGifCountDown($now, $future_date, $background, $color);


        } catch (Exception $e) {
            return '';
        }
    }

    function hex2rgb ( $hex_color ) {
        $values = str_replace( '#', '', $hex_color );
        switch ( strlen( $values ) ) {
            case 3;
                list( $r, $g, $b ) = sscanf( $values, "%1s%1s%1s" );
                return [ hexdec( "$r$r" ), hexdec( "$g$g" ), hexdec( "$b$b" ) ];
            case 6;
                return array_map( 'hexdec', sscanf( $values, "%2s%2s%2s" ) );
            default:
                return false;
        }
    }

    public function getSendTestDro($id)
    {

        $this->sendCampaignByCampaignId($id);
        return 'process done';
    }


    public function DeleteStepEmail()
    {
        $step_id = $_REQUEST['step_id'];
        $campaign_id = $_REQUEST['campaign_id'];

        if($campaign_id && $step_id)
        {
            DB::table('dropfunnel_step_mails')
            ->where('id', $step_id)
            ->where('campaign_id', $campaign_id)
            ->delete();
            return response(['status' => 'True', 'message' => ' Data Delete Successfully!']);
        } else {
            return response(['status' => 'Failed', 'message' => 'Something Went Wrong!']);
        }



        return;

        if ($_REQUEST['id']) {
            $deleted_step_position = DB::table('dropfunnel_step_mails')
                ->where('id', $_REQUEST['id'])
                ->pluck('campaign_id','position')
                ->toArray();


            foreach($deleted_step_position as $position => $campaign_id){

                $rePositionSteps = DB::table('dropfunnel_step_mails')
                ->where('campaign_id', $campaign_id)
                ->where('position', '>', $position)
                ->select('id','position')
                ->get()
                ->toArray();

                if($rePositionSteps){
                    foreach($rePositionSteps as $step){
                        $new_position = $step->position - 1;

                        DB::table('dropfunnel_step_mails')
                        ->where('id', $step->id)
                        ->where('campaign_id', $campaign_id)
                        ->update(['position' => $new_position]);
                    }
                }
            }

            DB::table('dropfunnel_step_mails')
                ->where('id', $_REQUEST['id'])
                ->delete();
            return response(['status' => 'True', 'message' => ' Data Delete Successfully!']);
        } else {
            return response(['status' => 'Failed', 'message' => 'Something Went Wrong!']);
        }
    }


    // Create new step
    private function createNewStep($campaign_id, $position = 1)
    {

        DB::table('dropfunnel_step_mails')->insert([
            'campaign_id' => $campaign_id,
            'position' => $position,
            'step_name' => '' . __('Step') . ' #' . $position,
            'subject' => ' ',
            'email_body' => ' ',
            'type' => 'hours',
            'value' => 24,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }

    private function campaignStepsHtml($campaign_id)
    {
        $user_id = CRUDBooster::myParentId();

        $step_mails = DB::table('dropfunnel_step_mails')
        ->where('campaign_id', $campaign_id)
        ->orderBy('position', 'asc')->get();
        $email_marketing = EmailMarketing::with('tags', 'tags.dropfunnel_tag')->find($campaign_id);

        $user_campaigns      = $this->getUserCampaigns($user_id);
        $user_campaigns_tags = $this->getUserCampaignsTags($user_id);

        return view('admin.drm_email_marketings.partials._mail_steps', compact('step_mails', 'email_marketing', 'user_campaigns', 'user_campaigns_tags'))->render();
    }



    public function postAddNewStepEmail()
    {
        $request = $_REQUEST;
        $position = 1;
        $campaign_id = $request['email_marketing_id'];

        $campaign = EmailMarketing::where('user_id', CRUDBooster::myParentId())->find($campaign_id);
        if(empty($campaign)) {
            return response()->json(['success' => false, 'message' => 'Invalid access!']);
        }

        if(isset($request['position']) && !empty($request['position']))
        {
            foreach ($request['position'] as $key => $p) {
                $step_id = $request['step_email_id'][$key];

                $payload = [
                    'position' => $position,
                    'step_name' => $request['step_name'][$key],
                    'subject' => $request['email_subject'][$key],
                    'type' => $request['day_type'][$key],
                    'value' => $request['value'][$key],
                    'updated_at' => now(),
                ];


                DB::table('dropfunnel_step_mails')->where([
                    'campaign_id' => $campaign_id,
                    'id' => $step_id,
                ])
                ->update($payload);
                $position++;
            }
        }

        // Create step
        $this->createNewStep($campaign_id, $position);
        $html = $this->campaignStepsHtml($campaign_id);

        return response([
            'status' => 'True',
            'html' => $html,
            'message' => 'Add new step successfully!'
        ]);
    }


    /**
     * String to array
     */
    public function processStringToArray(string $input): array {
        // Check if the string contains a comma
        if (strpos($input, ',') !== false) {
            // Explode the string into an array and trim each element
            return array_map('trim', explode(',', $input));
        }
        // Return the trimmed string as a single-element array if no comma is found
        return [trim($input)];
    }


    // this if for zapier customer tag insert
    public function storeTag(LaravelRequest $request)
    {
        try {

            $response = [
                'success' => false,
            ];

            $tags = $this->processStringToArray((string)$request->tag);
            if(!empty($tags))
            {
                foreach($tags as $tag)
                {
                    $response = DropfunnelCustomerTag::insertTag($tag, $request->user_id, $request->customer_id, $request->insert_type);
                }
            }

            if (!$response['success']) {
                throw new Exception('Failed to Insert Tag');
            }
            return response()->json(['success' => true, 'message' => 'Successfully Tag Inserted']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }


    // Create customer lead
    public function createCustomerLead(LaravelRequest $request)
    {
        try {

            $data = $request->validate([
                'user_id' => 'required', 
                'customer_id' => 'required',
            ]);

            $userId = $data['user_id'];
            $customerId = $data['customer_id'];

            // transfer to pipeline
            if ( !empty($userId) && !empty($customerId)) {
                $customerInfo = NewCustomer::find($customerId);
                
                $dealPayload = [
                    'source'       => 'zapier',
                    'product_name' => $customerInfo->email . ' - ' . 'Zapier Lead', 
                    'deal_title'   => $customerInfo->email . ' - ' . 'Zapier Lead',
                    'deal_price'   => 0,
                    'stage_id'     => 1,
                    'email_addr'   => $customerInfo->email,
                    'contact_name' => $customerInfo->full_name,
                    'user_id'      => $userId,
                ];

                app('App\Http\Controllers\AdminPipelineController')->autoTypeDealCreation($dealPayload);
            }

            return response()->json(['success' => true, 'message' => 'Lead created inserted']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function addTagModal($id)
    {
        $campaign = EmailMarketing::withCount('steps')->where('user_id', CRUDBooster::myParentId())->find($id);
        if (!empty($campaign) && ($campaign->steps_count >= 2)) {
            $campaignId = $campaign->id;
            $html = view('admin.drm_email_marketings.partials._add_step_tag_modal', compact('campaignId'))->render();
            return response()->json(['success' => true, 'html' => $html, 'title' => __('Add Tag to Customer')]);
        }
        return response()->json(['success' => false, 'html' => '<p><strong>'.__("No data Found Or Your Campaign has less than 2 steps").'</strong></p>', 'title' => __('Add Tag to Customer')]);
    }

    public function saveZapierTag(LaravelRequest $request)
    {
        try {
            $userId = CRUDBooster::myParentId();
            $campaign = EmailMarketing::where('user_id', $userId)->find($request->campaign_id);
            if (!empty($campaign)) {
                $campaign->stop_tag = $request->tag_name;
                $campaign->save();
                return response()->json(['success' => true, 'message' => 'Successfully Added']);
            }
            throw new Exception('No Valid Data Found');

        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }

    public function removeTagModal($id)
    {
        $campaign = EmailMarketing::withCount('steps')->where('user_id', CRUDBooster::myParentId())->whereNotNull('stop_tag')->find($id);
        if (!empty($campaign) && ($campaign->steps_count >= 2)) {
            $customers = $this->findCustomersByCampaignId($campaign->id);
            $tag = DropfunnelTag::has('customerTags')->with(['customerTags' => function ($query) {
                $query->where('insert_type', 12);
            }])->where('user_id', $campaign->user_id)->where(DB::raw('LOWER(`tag`)'), strtolower($campaign->stop_tag))->first();

            if (empty($tag) || empty($tag->customerTags)) {
                return response()->json(['success' => true, 'html' => '<p><strong>'. __('No Stop Tag Assigned for this Campaign') . '</strong></p>', 'title' => __('Remove Tag')]);
            }

            $html = view('admin.drm_email_marketings.partials._remove_step_tag', compact('tag', 'customers'))->render();
            return response()->json(['success' => true, 'html' => $html, 'title' => __('Remove Tag')]);
        }
        return response()->json(['success' => false, 'html' => '<p><strong>' . __('No data Found Or Your Campaign has less than 2 steps') . '</strong></p>', 'title' => __('Remove Tag')]);
    }

    public function deleteCustomerTag(LaravelRequest $request)
    {
        try {
            $conditions = [
                'insert_type' => 12,
                'tag_id' => $request->zapier_tag_id
            ];
            $customerIds = is_array($request->customers) && !empty($request->customers) ? $request->customers : [];

            $tags = DropfunnelCustomerTag::where($conditions)->whereIn('customer_id', $customerIds)->get();

            if ($tags->isNotEmpty()) {
                foreach ($tags as $tag) {
                    $tag->delete();
                }
                return response()->json(['success' => true, 'message' => 'Delete Permanently']);
            }
            return response()->json(['success' => false, 'message' => 'Customer Does not have this tag']);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => 'Failed to delete']);
        }
    }


    /*====================================================
    ========== Order status mail =========================
    =====================================================*/
    public function getStatusEmailSetting($order_status)
    {
        if (empty($order_status)) abort(404);
        if (CRUDBooster::isSubUser() && !((sub_account_can('email_setting') || !sub_account_can('all_modules', 122)) || \CRUDBooster::isDropmatixSupport()) ) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }
        $authId = CRUDBooster::myParentId();
        $mail_setting = DB::table('order_mail_templates')->where('order_status', $order_status)->where('user_id', $authId)->whereNull('channel')->first();
        $data['page_title'] = drmHistoryLabel($order_status) . ' - ' . __('emailSetting.pageTittle');
        $data['order_status'] = $order_status;
        $data['mail'] = $mail_setting;
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myParentId());
        return view("admin.new_order.status_email_setting", $data);
    }

    public function postSaveStatusEmailSetting($order_status)
    {
        if (empty($order_status)) abort(404);

        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $authId = CRUDBooster::myParentId();
        $data = [
            'cms_user_id' => $authId,
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => (int)$_REQUEST['auto_mail'],
            'send_invoice' => (int)$_REQUEST['send_invoice'],
            'email_template' => $_REQUEST['email_template'],
            'bcc_email' => $_REQUEST['bcc_email'],
            'created_at' => now(),
            'updated_at' => now()
        ];
        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        if (!empty($_REQUEST['channel'])) {
            $data['channel'] = $_REQUEST['channel'];
        }

        DB::table('order_mail_templates')->updateOrInsert(
            [
                'user_id' => $authId,
                'order_status' => $order_status,
                'channel' => !empty($_REQUEST['channel']) ? $_REQUEST['channel'] : NULL
            ], $data
        );

        if(!empty($_REQUEST['channel'])){
            $cache_name = $order_status . '_email_' . $authId  . '_' . $_REQUEST['channel'];
            \Cache::forget($cache_name);
            clear_remote_cache($cache_name);
        }else{
        $cache_name = $order_status . '_email_' . $authId;
        \Cache::forget($cache_name);
        clear_remote_cache($cache_name);
        }


        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postTestStatusEmail($order_status)
    {
        try {
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $invoice_data['page_title'] = 'Test order email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            // (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) &&
            if( ($order_status == 'Shipped') ){
                $tags['package_number'] = 'PXOO5TEST';
                $tags['parcel_service'] = 'TEST_PARCEl';
            }

            $template = DRMParseOrderStatusEmailTemplate($tags, $order->cms_user_id, $order_status);
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $is_send_invoice = $template['send_invoice'] ?? 0;
            $pdf_stream = $is_send_invoice ? PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream() : '';

            $data['subject'] = $template['subject'];
            $data['bcc'] = $template['bcc'];
            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $is_send_invoice) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                //Send invoice
                if ($is_send_invoice === 1) {
                    $messages->attachData($pdf_stream, 'invoice.pdf', [
                        'mime' => 'application/pdf',
                    ]);
                }

                //BCC
                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (\Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    /*============================================================================
    ============================= END ============================================
    =============================================================================*/



    /*====================================================
    ========== Order status mail by Channel ==============
    =====================================================*/
    public function getStatusEmailSettingByChannel()
    {
        $order_status = $_REQUEST['status'];

        if (empty($order_status)) abort(404);
        if (CRUDBooster::isSubUser() && !((sub_account_can('email_setting') || !sub_account_can('all_modules', 122)) || \CRUDBooster::isDropmatixSupport()) ) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $channel = $_REQUEST['channel'];
        $authId = CRUDBooster::myParentId();

        $data['page_title'] = drmHistoryLabel($order_status) . ' - ' . __('emailSetting.pageTittle');

        $mail_setting = DB::table('order_mail_templates')->where('order_status', $order_status)->where('user_id', $authId)->where('channel', $channel)->first();
        $data['order_status'] = $order_status;
        $data['mail'] = $mail_setting;
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        $data['channel'] = (int) $channel;
        $data['user_id'] = $authId;

        // return view("admin.new_order.system_email_by_channel.status_email_setting", $data);
        return view("admin.new_order.status_email_setting", $data);
    }

    public function postSaveStatusEmailSettingByChannel()
    {
        $order_status = $_REQUEST['status'];

        if (empty($order_status)) abort(404);

        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => (int)$_REQUEST['auto_mail'],
            'send_invoice' => (int)$_REQUEST['send_invoice'],
            'email_template' => $_REQUEST['email_template'],
            'bcc_email' => $_REQUEST['bcc_email'],
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        $authId = CRUDBooster::myParentId();

        DB::table('order_mail_template_by_channels')->updateOrInsert(
            [
                'user_id' => $authId,
                'order_status' => $order_status,
                'channel' => $_REQUEST['channel'],
            ], $data
        );

        $cache_name = $order_status . '_email_' . $authId . '_' . $_REQUEST['channel'];
        \Cache::forget($cache_name);
        clear_remote_cache($cache_name);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postTestStatusEmailByChannel()
    {
        try {
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $invoice_data['page_title'] = 'Test order email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $order_status = $_REQUEST['status'];
            $channel = $_REQUEST['channel'];

            // (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) &&
            if( ($order_status == 'Shipped') ){
                $tags['package_number'] = 'PXOO5TEST';
                $tags['parcel_service'] = 'TEST_PARCEl';
            }

            $template = DRMParseOrderStatusEmailTemplate($tags, $order->cms_user_id, $order_status, $channel);
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $is_send_invoice = $template['send_invoice'] ?? 0;
            $pdf_stream = $is_send_invoice ? PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream() : '';

            $data['subject'] = $template['subject'];
            $data['bcc'] = $template['bcc'];
            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $is_send_invoice) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                //Send invoice
                if ($is_send_invoice === 1) {
                    $messages->attachData($pdf_stream, 'invoice.pdf', [
                        'mime' => 'application/pdf',
                    ]);
                }

                //BCC
                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (\Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    /*============================================================================
    ============================= END ============================================
    =============================================================================*/

    /**
     * @deprecated
     */
    public function loadStepName(LaravelRequest $request)
    {
        try {
            $campaign = EmailMarketing::with('steps')->where('user_id', CRUDBooster::myParentId())->find($request->campaign_id);
            if (!empty($campaign)) {
                $step = null;
                $stepCount = $request->step_count;
                if (!empty($request->step_id) && $campaign->steps->isNotEmpty()) {
                    $step = $campaign->steps->where('id', $request->step_id)->first();
                    if (empty($step)) {
                        $step = null;
                    }
                }
                $step_name = $step ? $step->step_name : $campaign->step_name;
                $step_name = $step_name ?? 'Step #'.$stepCount;
                $html = view('admin.drm_email_marketings.partials._step_name_modal', compact('step_name','campaign', 'step', 'stepCount'))->render();
                return response()->json(['success' => true, 'message' => 'Update Successful', 'data' => $html]);
            }
            throw new Exception('No Data Found');
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage()]);
        }

    }


    public function storeStepName(LaravelRequest $request)
    {
        try {
            $campaign = EmailMarketing::where('user_id', CRUDBooster::myParentId())->find($request->campaign_id);
            if (!empty($campaign)) {
                if (empty($request->step_id)) {
                    $campaign->step_name = empty($request->step_name) ? null : $request->step_name;
                    $campaign->save();
                    return response()->json(['success' => true, 'message' => 'Campaign Name Update Successful', 'data' => $campaign->step_name]);
                }

                $step = $campaign->steps()->where('id', $request->step_id)->first();
                if (!empty($step)) {
                    $step->step_name = empty($request->step_name) ? null : $request->step_name;
                    $step->save();
                    return response()->json(['success' => true, 'message' => 'Step Name Update Successful', 'data' => $step->step_name]);
                }
            }
            throw new Exception('No Data Found');
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage()]);
        }
    }

    public function stepCampaignPositionUpdate()
    {
        $request = $_REQUEST;
        $position = 1;
        $campaign_id = $request['email_marketing_id'];

        $campaign = EmailMarketing::where('user_id', CRUDBooster::myParentId())->find($campaign_id);
        if(empty($campaign)) {
            return response()->json(['success' => false, 'message' => 'Invalid access!']);
        }

        if(isset($request['position']) && !empty($request['position']))
        {
            foreach ($request['position'] as $key => $p) {
                $step_id = $request['step_email_id'][$key];

                $d[] = $position;

                $payload = [
                    'position' => $position,
                    'step_name' => $request['step_name'][$key],
                    'subject' => $request['email_subject'][$key],
                    'type' => $request['day_type'][$key],
                    'value' => $request['value'][$key],
                    'updated_at' => now(),
                ];

                DB::table('dropfunnel_step_mails')->where([
                    'campaign_id' => $campaign_id,
                    'id' => $step_id,
                ])
                ->update($payload);
                $position++;
            }
        }

        $html = $this->campaignStepsHtml($campaign_id);
        return response([
            'success' => true,
            'html' => $html,
        ]);
    }

    public function stepMailSend()
    {
        $campaign = EmailMarketing::find(36);
        $this->sendStepEmail($campaign);
    }

    private function sendTelegramNotificationForTanjil($txt)
    {
        // try {
        //     $telegramUrl = 'https://api.telegram.org/bot';
        //     $token = '**********************************************';
        //     $chatId = 1226369782;
        //     file_get_contents($telegramUrl . $token . '/sendMessage?chat_id=' . $chatId . '&text=' . $txt . '&parse_mode=HTML');
        // } catch (Exception $exception) {

        // }
    }

    public function getSenderEmailSetting()
    {
        $this->cbLoader();
        if (!CRUDBooster::isCreate() && $this->global_privilege == false) { // || $this->button_add == false
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $this->createDefaultSignature();

        $data = [];
        $data['page_title'] = __('Sender Email Setting');
        $authId = CRUDBooster::myParentId();
        $appId = isLocal() ? config('global.sender_mail_selectable_team') : config('global.sender_mail_selectable');
        $data['senderEmails'] = SenderEmailSetting::where('user_id', $authId)->get();

        $data['canCreateCount'] = $this->showInfoForSenderEmail($authId, 'count');
        $data['isPurchaseApp'] = DrmUserHasPurchasedApp($authId, $appId);
        $baseCount = 8;
        $userTemplate = DRM::userTemplates($authId);
        $campaigns = EmailMarketing::with('steps:campaign_id,id,subject')
            ->select('id', 'sender_email', 'name')
            ->where('user_id', $authId)
            ->get();
        $baseCount += @count($userTemplate['status_templates']) ?? 0;
        $matchCount = 0;
        $urlArr = [
            'drm_order_mail' => 'admin/drm_all_orders/email-setting',
            'droptienda_order_confirmation_template' => 'admin/email_marketings/droptienda-order-confirmation-template',
            'remainder_email_settings' => 'admin/drm_all_orders/remainder-email-setting',
            'marketing_email_settings' => 'admin/email_marketings/marketing-email-settings',
            'drm_supplier_mail' => 'admin/delivery_companies/supplier-email-setting',
            'order_tracking_emails' => 'admin/drm_all_orders/tracking-email-template',
            'appointment_email_settings' => 'admin/appointment_booking/appointment-email-settings',
            'appointment_cancel_email_settings' => 'admin/appointment_booking/appointment-cancel-email-settings',
        ];
        $html = '<ul style="padding-left: 10px;"><h3 style="margin-top: 0;">System Email</h3>';
        foreach ($userTemplate as $key => $configuredTemplate) {
            if ($key == 'status_templates') {
                continue;
            }

            if(isset($urlArr[$key])){
                if (!empty($configuredTemplate->sender_email)) {
                    ++$matchCount;
                    $html .= '<li><a href="' . url($urlArr[$key]) . '">' . ucwords(str_replace('_', ' ', $key)) . '</a> <span class="label label-success">Verified</span></li>';
                } else {
                    $html .= '<li><a href="' . url($urlArr[$key]) . '">' . ucwords(str_replace('_', ' ', $key)) . '</a></li>';
                }
            }
        }
        foreach ($userTemplate['status_templates'] as $index => $statusTemplate) {
            if (!empty($statusTemplate->sender_email)) {
                ++$matchCount;
                $html .= '<li><a href="' . url('admin/email_marketings/status-email-setting') . '/' . $statusTemplate->order_status . '">' . ucwords(str_replace('_', ' ', $index)) . '</a> <span class="label label-success">Verified</span></li>';
            } else {
                $html .= '<li><a href="' . url('admin/email_marketings/status-email-setting') . '/' . $statusTemplate->order_status . '">' . ucwords(str_replace('_', ' ', $index)) . '</a></li>';
            }
        }
        $html .= '<hr><h3 style="margin-top: 0;">Campaign Email</h3>';
        foreach ($campaigns as $campKey => $campaign) {
            ++$baseCount;
            if (!empty($campaign->sender_email)) {
                ++$matchCount;
                $html .= '<li><a href="' . url('admin/email_marketings/edit') . '/' . $campaign->id . '">' . ucwords(str_replace('_', ' ', $campaign->name)) . '</a> <span class="label label-success">Verified</span></li>';
            } else {
                $html .= '<li><a href="' . url('admin/email_marketings/edit') . '/' . $campaign->id . '">' . ucwords(str_replace('_', ' ', $campaign->name)) . '</a></li>';
            }
            if ($campaign->steps->isNotEmpty()) {
                $html .= '<ol>';
                foreach ($campaign->steps as $step) {
                    ++$baseCount;
                    if (!empty($campaign->sender_email)) {
                        ++$matchCount;
                        $html .= '<li><a href="' . url('admin/email_marketings/edit') . '/' . $campaign->id . '">' . ucwords(str_replace('_', ' ', $step->subject)) . '</a> <span class="label label-success">Verified</span></li>';
                    } else {
                        $html .= '<li><a href="' . url('admin/email_marketings/edit') . '/' . $campaign->id . '">' . ucwords(str_replace('_', ' ', $step->subject)) . '</a></li>';
                    }
                }
                $html .= '</ol>';
            }
        }
        $html .= '</ul>';
        $percentage = ($matchCount * 100) / $baseCount;
        $data['percentage'] = number_format($percentage, 2);
        $data['popOverHtml'] = $html;
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['isVarifiedUserEmail'] = ($data['userEmail']) ? SenderEmailSetting::where('email', $data['userEmail'])->exists() : false;

        $data['signatures'] = DB::table('drop_funnel_signatures')->where('user_id', $authId)->get();
        $data['smtp'] = DB::table('user_mail_configurations')->where('user_id', $authId)->first();
        return view('admin.drm_email_marketings.sender_email_setting', $data);
    }

    public function getVerifySenderEmail(LaravelRequest $request)
    {
        Validator::make($request->all(), [
            'email' => 'required|email',
        ])->validate();

        $authId = CRUDBooster::myParentId();

        //match domain with dkim_users_log table , if not matched then show mesaage
        $domain = explode('@', $request->email);

        $checkDomain = DB::table('dkim_users_log')->where('email',$domain[1])->exists();

        if(!$checkDomain){
            $key = $this->dkimKeyGenerate($domain[1]);

            DB::table('dkim_users_log')->insert([
                'email' => $domain[1],
                'user_id' => $authId,
                'dkim_key' => $key,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }

        $dkim_emails = DB::table('dkim_users_log')->pluck('email')->toArray() ?? [];

        // $dkDomainArray = array_map(function($email) {
        //     $parts = explode("@", $email);
        //     return isset($parts[1]) ? $parts[1] : "";
        // }, $dkim_emails);

        // if(!in_array($domain[1],$dkDomainArray)){
        //     return response()->json(['success' => false, 'message' => __('Domain is not registered on CSA Server.'), 'info' => false, 'email' => $request->email]);
        // }

        $senderEmail = SenderEmailSetting::where('user_id', $authId)->where('email', $request->email)->first();
        if (empty($senderEmail)) {
            $response = $this->showInfoForSenderEmail($authId);
            if ($response) {
                return response()->json(['success' => true, 'message' => __('Can not add any more email.'), 'info' => true, 'email' => null]);
            }
            $newSenderEmail = new SenderEmailSetting();
            $newSenderEmail->user_id = $authId;
            $newSenderEmail->email = $request->email;
            if(in_array($domain[1],$dkim_emails)){
                $newSenderEmail->dkim_verified = 1;
            }
            $newSenderEmail->verification_code = $this->generateNumericOTP(8);
            $newSenderEmail->save();

            $mailData = $this->getSenderMailData($newSenderEmail, $authId);
            app('drm.mailer')->getMailer()->to($newSenderEmail->email)->send(new DRMSEndMail($mailData));

            return response()->json(['success' => true, 'message' => __('Enter the code that sent to your email address'), 'info' => false, 'email' => $newSenderEmail->email]);
        }

        if (empty($senderEmail->verification_code)) {
            return response()->json(['success' => true, 'message' => __('Already Verified'), 'info' => true, 'email' => null]);
        }

        $mailData = $this->getSenderMailData($senderEmail, $authId);
        app('drm.mailer')->getMailer()->to($senderEmail->email)->send(new DRMSEndMail($mailData));

        return response()->json(['success' => true, 'message' => __('Enter the code that sent to your email address'), 'info' => false, 'email' => $senderEmail->email]);
    }

    public function postSmtpSetting(LaravelRequest $request)
    {
        $request->validate([
            'username' => 'required|max:40',
            'password' => 'required|max:40',
            'host' => 'required|max:40',
            'port' => 'required|max:10',
            'encryption' => 'nullable|max:6',
            'name' => 'required|max:40',
            'address' => 'required|max:40|email',
            'test_email' => 'nullable|max:40|email',
        ]);

        $payload = $request->only(['username', 'password', 'host', 'port', 'encryption', 'name', 'address']);
        $payload['active'] = null;
        $payload['driver'] = 'smtp';

        try {

            $user_id = CRUDBooster::myParentId();

            if($request->test && $request->test === 'yes')
            {
                $req = $payload;
                $req['test_email'] = $request->test_email;
                app(\App\Services\MailSender\MailSender::class)->testCustomMailer((object)$req);
                return response()->json([
                    'success' => true,
                    'message' => 'Test mail send successfully!',
                ]);
            }

            if($request->active && $request->active === 'yes')
            {
                $req = $payload;
                $req['test_email'] = $payload['address'];
                app(\App\Services\MailSender\MailSender::class)->testCustomMailer((object)$req);
                $payload['active'] = 1;
            }

            if(DB::table('user_mail_configurations')->where('user_id', $user_id)->exists())
            {
                $payload['updated_at'] = now();
                DB::table('user_mail_configurations')->where('user_id', $user_id)->update($payload);
            } else {

                $payload['updated_at'] = $payload['created_at'] = now();
                $payload['user_id'] = $user_id;
                DB::table('user_mail_configurations')->insert($payload);
            }

            return response()->json([
                'success' => true,
                'message' => 'Configuration saved successfully!',
                'smtp_active' => $payload['active'] === 1 ? 'no' : 'yes',
            ]);

        } catch(\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    private function generateNumericOTP($n)
    {
        $generator = "1357902468";
        $result = "";
        for ($i = 1; $i <= $n; $i++) {
            $result .= substr($generator, (rand() % (strlen($generator))), 1);
        }
        return $result;
    }

    private function getSenderMailData($newSenderEmail, $authId)
    {
        $userName = DB::table('cms_users')->where('id', $authId)->value('name');
        $current_lang = \Config::get('app.locale');
        $emailTemplate = DB::table('cms_email_templates')->select(['subject','slug', DB::raw('IF(`content_'.$current_lang.'` IS NOT NULL, `content_'.$current_lang.'`, `content`) `content`')])->where('slug', '=', 'sender_email_verification_design')->first();
        if (!empty($emailTemplate)) {
            $content = $emailTemplate->content;
            $content = preg_replace('/\[user_name]/', $userName, $content);
            $content = preg_replace('/\[sender_email]/', $newSenderEmail->email, $content);
            $content = preg_replace('/\[otp]/', $newSenderEmail->verification_code, $content);
            return ['subject' => $emailTemplate->subject, 'body' => $content];
        }
    }

    private function showInfoForSenderEmail($authId, $flag = null)
    {
        $canCreateCount = 1;
        $appId = isLocal() ? config('global.sender_mail_selectable_team') : config('global.sender_mail_selectable');
        $plans = [25 => 2, 45 => 3, 60 => 4, 125 => -1, 250 => 2, 450 => 3, 600 => 4, 1250 => -1];
        $purchased = DB::table('purchase_apps')->where(['app_id' => $appId, 'cms_user_id' => $authId])->whereDate('subscription_date_end', '>=', Carbon::now())->first();
        if (!empty($purchased)) {
            $plan = DB::table('subscription_plans')->find($purchased->plan_id);
            $key = (int)$plan->price;
            if (array_key_exists($key, $plans)) {
                $canCreateCount = $plans[$key];
            }
        } else {
            $assigned = DB::table('app_assigns')->where(['app_id' => $appId, 'user_id' => $authId])
                ->where(function ($query) {
                    $query->whereNull('end_date')->orWhereDate('end_date', '>=', Carbon::now());
                })
                ->first();
            if (!empty($assigned)) {
                $plan = DB::table('subscription_plans')->find($assigned->plan_id);
                $key = (int)$plan->price;
                if (array_key_exists($key, $plans)) {
                    $canCreateCount = $plans[$key];
                }
            }
        }
        if ($flag == 'count') {
            return $canCreateCount;
        }
        if (($canCreateCount != -1) && (SenderEmailSetting::where('user_id', $authId)->count() == $canCreateCount)) {
            return true;
        }
        return false;
    }

    public function postResendOtp(LaravelRequest $request)
    {
        Validator::make($request->all(), [
            'email' => 'required|email',
        ])->validate();

        $authId = CRUDBooster::myParentId();
        $senderEmail = SenderEmailSetting::where('user_id', $authId)->where('email', $request->email)->first();

        if (empty($senderEmail->verification_code)) {
            return response()->json(['success' => true, 'message' => 'Already Verified', 'info' => true]);
        }

        $senderEmail->verification_code = mt_rand(0, 99999999);
        $senderEmail->save();

        $mailData = $this->getSenderMailData($senderEmail, $authId);
        app('drm.mailer')->getMailer()->to($senderEmail->email)->send(new DRMSEndMail($mailData));

        return response()->json(['success' => true, 'message' => 'Enter otp and submit', 'info' => false]);
    }

    public function postVerifyOtp(LaravelRequest $request)
    {
        Validator::make($request->all(), [
            'email' => 'required|email',
            'otp' => 'required|digits:8'
        ])->validate();

        $authId = CRUDBooster::myParentId();
        $senderEmail = SenderEmailSetting::where('user_id', $authId)->where('email', $request->email)->first();
        $domain = explode('@', $request->email);

        if (empty($senderEmail->verification_code)) {
            return response()->json(['success' => true, 'message' => 'Already Verified']);
        }

        $senderEmail->verification_code = null;
        $senderEmail->save();
        return response()->json(['success' => true, 'message' => 'Verification Successful','domain' => $domain[1]]);
    }

    public function postDeleteSenderMail(LaravelRequest $request)
    {
        Validator::make($request->all(), [
            'email' => 'required|email',
        ])->validate();

        $authId = CRUDBooster::myParentId();
        $senderEmail = SenderEmailSetting::where('user_id', $authId)->where('email', $request->email)->first();

        if (empty($senderEmail)) {
            return response()->json(['success' => false, 'message' => 'No Data Found']);
        }
        $senderEmail->delete();
        return response()->json(['success' => true, 'message' => 'Delete Successful']);
    }

    public function postDeleteExistingTag(LaravelRequest $request)
    {
        $tagId = $request->tag_id;
        $id = $request->campaign_id;
        $campaign = EmailMarketing::where('user_id', CRUDBooster::myParentId())->find($id);
        if (!empty($campaign)) {
            $tags = $campaign->tags()->where('tag_id', $tagId)->exists();
            if ($tags) {
                DB::table('campaign_tags')->where('tag_id', $tagId)->where('campaign_id', $id)->delete();
                return response()->json(['success' => true, 'message' => 'Delete Successful']);
            }
        }
        return response()->json(['success' => false, 'message' => 'No Data Found'], 400);
    }

//    public function getTemplateInProgress($step,$editId = 0)
//    {
//        $template = DB::table('temp_email_builder_template')->where('user_id', CRUDBooster::myParentId())->where('step', $step)->whereDate('created_at', Carbon::today());
//        if (!empty($editId)) {
//            $template = $template->where('edit_id', $editId);
//            if(empty($template)){
//                $template = DB::table('email_marketings')->where('user_id', CRUDBooster::myParentId())->where('id', $editId)->value('email_template');
//                return view('admin.drm_email_marketings.partials._template_preview', compact('template'));
//            }
//        } else {
//            $template = $template->whereNull('edit_id');
//        }
//        $template = $template->value('template');
//        if (!empty($template)) {
//            return view('admin.drm_email_marketings.partials._template_preview', compact('template'));
//        }
//        return '<p>No Template Designed Yet</p>';
//    }


    public function getTemplateInProgressNew($campaignId, $stepId = null)
    {
        $user_id = CRUDBooster::myParentId();

        if(!empty($stepId))
        {
            $template = DB::table('dropfunnel_step_mails')->where('id', $stepId)->where('campaign_id', $campaignId)->value('email_body');
            $template = $this->replacePreviewSignature($template);
            return view('admin.drm_email_marketings.partials._template_preview', compact('template'));
        }

        $template = DB::table('email_marketings')->where('user_id', $user_id)->where('id', $campaignId)->value('email_template');
        $template = $this->replacePreviewSignature($template);
        return view('admin.drm_email_marketings.partials._template_preview', compact('template'));
    }



    public function getTemplateInProgress($step = 1,$editId = 0)
    {
        // $user_id = CRUDBooster::myParentId();
        // $template = DB::table('temp_email_builder_template')->where('user_id', $user_id)->where('step', $step)->whereDate('created_at', Carbon::today());

        // if (!empty($editId)) {
        //     $template = $template->where('edit_id', $editId);
        // }
        // $template = $template->value('template');


        // if (!empty($template)) {
        //     $template = $this->replacePreviewSignature($template);
        //     return view('admin.drm_email_marketings.partials._template_preview', compact('template'));
        // }else{
        //     if($step > 1){
        //         $template = DB::table('dropfunnel_step_mails')->where('position', $step)->where('campaign_id', $editId)->value('email_body');
        //         $template = $this->replacePreviewSignature($template);
        //         return view('admin.drm_email_marketings.partials._template_preview', compact('template'));
        //     }
        //     $template = DB::table('email_marketings')->where('user_id', $user_id)->where('id', $editId)->value('email_template');
        //     $template = $this->replacePreviewSignature($template);
        //     return view('admin.drm_email_marketings.partials._template_preview', compact('template'));
        // }
        // return '<p>No Template Designed Yet</p>';
    }

    public function replacePreviewSignature($template)
    {
        $request['user_id'] = CRUDBooster::myParentId();
        $request['email_template'] = $template;
        $signature = $this->getMatchSignature($request);
        $template = preg_replace('/\[drm-sign-\d*]/', $signature, $template, 1);
        $template = preg_replace('/\[drm-sign-\d*]/', '', $template);

        return $template;
    }

    /**
     * @deprecated
     */
    public function getTemplatePreview()
    {
        $request = $_REQUEST;
        $edit = !empty($request['edit']) ? $request['edit'] : 0;
        $iframe = "<iframe src='" . url('admin/email_marketings/template-in-progress') . '/' . $request['step'] . '/' . $edit ."' height='300' width='800'></iframe>";
        return response()->json(['success' => true, 'html' => $iframe]);
    }

    public function getDropfunnelHome()
    {
        if(CRUDBooster::isKeyAccount()) abort(404);

        $data = [];
        // $data['index_button'][] = ['label' => 'Manage Email Marketings', 'url' => url('admin/email_marketings'), 'icon' => 'fa fa-cog', 'color' => 'drm'];
        $data['page_title'] = __('Email Marketing');
        $data['templates'] = DB::table('builder_templates')->whereIn('UserId', [71, CRUDBooster::myParentId()])->orderBy('id', 'desc')->get();

        return view('admin.drm_email_marketings.home', $data);
    }

    public function getDropfunnelTemplates()
    {
        $templates     = '';
        $request       = $_REQUEST;
        $content_for   = !empty($request['content_for']) ? $request['content_for'] : 'summary';
        $template_type = !empty($request['template_type']) ? $request['template_type'] : 'allTemplates';

        $possible_users = [];
        if ($template_type == 'drmTemplates') {
            $possible_users = [71];
        } else if ($template_type == 'customTemplates') {
            $possible_users = [CRUDBooster::myParentId()];
        } else {
            $possible_users = [71, CRUDBooster::myParentId()];
        }

        $template_list_query =  DB::table('builder_templates')->whereIn('UserId', $possible_users);

        // folder filter
        $folder_name = $request['folder_name'];
        $user_folder_options = '';
        if (empty($folder_name)) { // tab clicked: show folders
            $user_folders = DB::table('builder_templates')
                ->whereIn('UserID', $possible_users)
                ->orderBy('folder')
                ->distinct()
                ->pluck('folder');

            $user_folder_options = "<option></option>";
            if (!empty($user_folders)) {
                foreach ($user_folders as $user_folder) {
                    $user_folder_options .= '<option value="'. $user_folder .'">'. $user_folder .'</option>';
                }
            }
        } else {
            $folder_name_exists = DB::table('builder_templates')
                ->whereIn('UserId', $possible_users)
                ->where('folder', $folder_name)
                ->exists();

            if ($folder_name_exists) { // full folder name filter
                $template_list_query = $template_list_query->where('folder', $folder_name);
            } else { // partial folder name filter
                $template_list_query = $template_list_query->where('folder', 'LIKE', '%' .$folder_name. '%');
            }
        }

        // query execution
        $last_page_no = 1;
        if ($content_for == 'summary') {
            $template_list = $template_list_query->orderBy('folder', 'asc')->get();
        } else {
            $template_list = $template_list_query->orderBy('id', 'desc')->paginate(24);
            $last_page_no  = $template_list->lastPage();
        }

        // generate html view of template listing
        if (!empty($template_list)) {
        if ($content_for == 'summary') {
            $template_list = $template_list->groupBy('folder');
            foreach ($template_list as $fold_name => $folder_templates) {

                // thumbnail generation
                // $folder_thumbs = [];
                // foreach ($folder_templates as $index => $template_info) {
                //     if (count($folder_thumbs) == 5) break; // will show final thaumbnail by 5 images

                //     if (!empty($template_info->thumbnail) && !in_array($template_info->thumbnail, $folder_thumbs)) {
                //         array_push($folder_thumbs, $template_info->thumbnail);
                //     }
                // }

                $folder_thumbs = $folder_templates->unique('thumbnail')
                    ->pluck('thumbnail')
                    ->filter()
                    ->take(5)
                    ->toArray();

                $thumb_img = '';
                if (empty($folder_thumbs)) { // dafault thumbnail
                    $thumb_img = '<img src="https://via.placeholder.com/190x256" alt="things-for-cuties-baby-kids-open-1" style="height: 120px">';
                } else {
                    $each_img_width = 100 / count($folder_thumbs);
                    foreach ($folder_thumbs as $index => $folder_thumb) {
                        $thumb_img .= '<img src="' . $folder_thumb . '" alt="things-for-cuties-baby-kids-open-1" style="height: 120px; width: ' . $each_img_width.'%">';
                    }
                }

				$templates .= '<li id="' . $fold_name . '">';
				$templates .= '<a href="javascript:void(0)" class="folder-details" data-id="' . $fold_name . '">';
				$templates .= '<div class="temp-img">';
				$templates .= $thumb_img;
				$templates .= '</div>';
				$templates .= '<span>' . $fold_name . ' (' . count($folder_templates) . ')' . '</a></span>';
                if ((CRUDBooster::myParentId() == 71) || (CRUDBooster::myId() == 2943)) {
                    $templates .= '<i class="fa fa-pencil" style="margin-left:5px" data-name="' . $fold_name . '" onclick="changeFolderName(this)" ></i>';
                }
				$templates .= '</a></li>';
			}
        } else {
            foreach ($template_list as $template) { // details
                $thumbnail     = !empty($template->thumbnail) ? $template->thumbnail : 'https://via.placeholder.com/190x256';
                $template_name = ($template->UserId != 71) ? $template->name : '';

                $templates .= '<li id="' . $template->id . '">';
                $templates .= '<a href="javascript:void(0)" class="template-preview" style="position: relative" title="'. __("Preview Template") .'" data-id="'.$template->id.'" data-url="'.url('admin/dropfunnel').'?step=1&page=create&template_id=' . $template->id.'">';
                $templates .= '<div class="temp-img">';
                $templates .= '<img src="' . $thumbnail . '" alt="things-for-cuties-baby-kids-open-1" style="height: 120px">';
                $templates .= '</div>';
                if (($folder_name == 'Default') && ($template->UserId == CRUDBooster::myParentId())) { // del btn for templates inside only Default folder
                    $templates .= '<div class="del-btn-inside-folder" title="' . __("Delete Template") . '" data-id="' . $template->id . '"><i class="fa fa-trash" aria-hidden="true"></i></div>';
                }
                if (($template->UserId == CRUDBooster::myParentId() || CRUDBooster::myId() == 2943)) { // del btn for templates inside only Default folder
                    $templates .= '<div class="del-btn-inside-folder-check" title="' . __("Delete Template") . '" data-id="' . $template->id . '">
                    <input type="checkbox" name="delete_template[]" class="itemCheckbox" value="' . $template->id . '"  >
                    </div>';
                }
                $templates .= '</a>';
                $templates .= '<span class="template-name" style="padding-top: 3px">' . $template_name . '</span>';
                $templates .= '</li>';
            }
        }
        }

        return response()->json([
            'templates'           => $templates,
            'last_page_no'        => $last_page_no,
            'user_folder_options' => $user_folder_options,
        ], 200);
    }


    public function getSystemEmails(){

        $currentUserId = CRUDBooster::myParentId();
        $isSuperAdmin = CRUDBooster::isSuperadmin();
        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $data = [];
        $data['page_title'] = __('menu.SYSTEM_EMAILS');

        $data['channels'] = config('channel.list');
        // dd($data['channels']);

        if (!$isSuperAdmin) {
            $data['shop'] = app(ChannelProductService::class)->getUserShops($currentUserId, $lang);
            $data['channels'] = array_values(collect($data['channels'])->whereIn('type', $data['shop']->pluck('channel'))->toArray());
        }

        // dd($data['channels'], $data['shop']);

        return view('admin.drm_email_marketings.system_email', $data);
    }

    public function getSystemEmailsByChannel(LaravelRequest $request)
    {
        $currentUserId = CRUDBooster::myParentId();
        $isSuperAdmin = CRUDBooster::isSuperadmin();
        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $data = [];
        $data['page_title'] = __('menu.SYSTEM_EMAILS');

        $data['channels'] = config('channel.list');

        if (!$isSuperAdmin) {
            $data['shop'] = app(ChannelProductService::class)->getUserShops($currentUserId, $lang);
            $data['channels'] = array_values(collect($data['channels'])->whereIn('type', $data['shop']->pluck('channel'))->toArray());
        }

        $data['activeChannel'] = $request->get('channel', Arr::get($data, 'channels.0.type'));

        return view('admin.drm_email_marketings.system_email_by_channel', $data);
    }

    public function getCustomerEmailVerification()
    {
        $data['page_title'] = 'Customer Email Verification';
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('customer_email_verification')->where('cms_user_id', $authId)->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myParentId());
        return view('admin.drm_customer.email_verification', $data);
    }

    public function postAddSaveCustomerEmailVerification()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'email_template' => $_REQUEST['email_template'],
            'created_at' => now(),
            'updated_at' => now(),
        ];
        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        DB::table('customer_email_verification')->updateOrInsert(
            [
                'cms_user_id' => CRUDBooster::myParentId()
            ],
            $data
        );

        $authId = CRUDBooster::myParentId();
        Cache::forget('customer_email_verification_' . $authId);
        clear_remote_cache('customer_email_verification_' . $authId);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Verification email setting changed', 'success');
    }

    public function postSendTestVerificationEmail()
    {
        try {
            $authId = CRUDBooster::myParentId();
            $template = config('system_email_settings.email_verification_body');
            $subject = config('system_email_settings.email_verification_subject');
            $emailSetting = DB::table('customer_email_verification')->where('cms_user_id', $authId)->first();
            $userEmail = DB::table('cms_users')->where('id', $authId)->value('email');
            $data = [];
            $data['email_to'] = $_REQUEST['test_email'];
            if (!empty($emailSetting)) {
                $data['subject'] = empty($emailSetting->mail_subject) ? $subject : $emailSetting->mail_subject;
                $data['email_from'] = empty($emailSetting->sender_email) ? $userEmail : $emailSetting->sender_email;
                $template = empty($emailSetting->email_template) ? $template : $emailSetting->email_template;
            }

            $logo = '<img id="display_logo" width="150" src="" alt="">';
            $template = preg_replace('/\[logo]/', $logo, $template);

            $link ='<a target="_blank" href="'.url('customer-email-verification/12345678910').'">'.url('customer-email-verification/12345678910').'</a>'; ;
            $template = preg_replace('/\[link]/', $link, $template);

            $faker = \Faker\Factory::create();
            $template = preg_replace('/\[customer_name]/', $faker->name, $template);
            $template = preg_replace('/\[appointment_date]/', date('Y-m-d'), $template);
            $template = preg_replace('/\[appointment_time]/', date('H:i:s'), $template);

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();
                $signature_tags = [];

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        // $signature_tags['drm-sign-'.$key] = $signature;
                        $template = preg_replace('/\[drm-sign-'.$key.']/', $signature, $template);
                    }
                }

            // }

            $data['subject'] = preg_replace('/\[appointment_date]/', date('Y-m-d'), $data['subject']);
            $data['subject'] = preg_replace('/\[appointment_time]/', date('H:i:s'), $data['subject']);

            app('drm.mailer')->getMailer($authId,$data['email_from'])->send('admin.new_order.email_invoice_template', ['body' => $template], function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }


    public function getIframeData($id, $userId)
    {
        try {
            $shop = isChannelExists(\App\Enums\Channel::DROPTIENDA, $userId);
            // $this->telegramNotification(json_encode($shop));
            $link = $url = null;
            if (!empty($shop)) {
                $link = rtrim($shop->url,'/');
            }
            if ($link) {
                $channelProduct = DB::table('channel_products')->where(['user_id' => $userId, 'id' => $id])->first();
                $url = !empty($channelProduct) ? "$link/api/v1/product/$channelProduct->id" : 'javascript:void(0)';
            }
            if ($url) {
                return view('channel_products.partials._iframe', compact('channelProduct', 'url'));
            }
        } catch (Exception $exception) {
            return 'Something Went wrong';
        }
    }

    private function telegramNotification($txt)
    {
        try {
            $telegramUrl = 'https://api.telegram.org/bot';
            $token = '**********************************************';
            $chatId = 1226369782;
            file_get_contents($telegramUrl . $token . '/sendMessage?chat_id=' . $chatId . '&text=' . $txt . '&parse_mode=HTML');
        } catch (Exception $exception) {

        }
    }

    public function getDTData(LaravelRequest $request)
    {
        try {
            DropfunnelCustomerTag::insertTag($request->tag, $user_id, $customerId, 12);
            return response()->json(['success' => true, 'message' => 'Tag inserted successful']);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage()]);
        }
    }

    public function getProductImportData(LaravelRequest $request)
    {
        try {
            $userToken = request()->header('userToken');
            $userPassToken = request()->header('userPassToken');

            $userId = \App\Shop::where(['user' => $userToken, 'password' => $userPassToken])->value('user_id');

            $data = resolve('App\Services\DRM\Sidebar')->productInfoHtml('api', $userId);
            return response()->json(['success' => true, 'data' => $data]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'data' => $exception]);
        }
    }

    public function loadCampaignList()
    {
        try {
            $campaigns = EmailMarketing::with('steps:id,campaign_id,subject')->select('id', 'user_id', 'name')->where('user_id', CRUDBooster::myParentId())->get();
            $html = '<option value="new_campaign">New Campaign</option>';
            foreach ($campaigns as $campaign) {
                $html .= '<option value="' . $campaign->id . '">'. $campaign->name .'</option>';
                foreach ($campaign->steps as $step) {
                    $html .= '<option value="' . $step->id . '" class="step">'. $step->subject .'</option>';
                }
            }
            return response()->json(['success' => true, 'data' => $html]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'data' => $exception->getMessage()]);
        }
    }

    /*====================================================
    ============= Droptienda Stock mail ==================
    =====================================================*/
    //Droptienda stock mail
    public function postSaveDroptiendaStockEmailSetting()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        try{
            $data = [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
                'email_template' => $_REQUEST['email_template'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];
            if (!empty($_REQUEST['bcc_email'])) {
                $data['bcc_email'] = $_REQUEST['bcc_email'];
            }

            if (!empty($_REQUEST['sender_email'])) {
                $data['sender_email'] = $_REQUEST['sender_email'];
            }
            $authId = CRUDBooster::myParentId();
            DB::table('drm_dt_stock_mail')->updateOrInsert(
                [
                    'cms_user_id' => $authId
                ],
                $data
            );

            Cache::forget('drm_dt_stock_mail_' . $authId);
            clear_remote_cache('drm_dt_stock_mail_' . $authId);

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Setting Changed'), 'success');
        }catch(Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }
    }


    //Droptienda stock entry mail
    public function postSaveDroptiendaStockEntryEmailSetting()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        try{
            $data = [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
                'email_template' => $_REQUEST['email_template'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];
            if (!empty($_REQUEST['bcc_email'])) {
                $data['bcc_email'] = $_REQUEST['bcc_email'];
            }

            if (!empty($_REQUEST['sender_email'])) {
                $data['sender_email'] = $_REQUEST['sender_email'];
            }
            $authId = CRUDBooster::myParentId();
            DB::table('drm_dt_stock_mail_entry')->updateOrInsert(
                [
                    'cms_user_id' => $authId
                ],
                $data
            );

            Cache::forget('drm_dt_stock_mail_entry_' . $authId);
            clear_remote_cache('drm_dt_stock_mail_entry_' . $authId);

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Setting Changed'), 'success');
        }catch(Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }
    }

    //Test DT products stock email
    public function postTestDtProductStockEmail()
    {
        try {

            $stock_data = [
                'product' => [
                    'name' => 'Test product',
                    'channel_product_id' => 10,
                    'ean' => '123456789012',
                    'url' => 'example-shop.com/product/123',
                ],
                'customer' => [
                    'name' => 'Test customer',
                    'email' => '<EMAIL>',
                    'company_name' => 'Fake company',
                ],
            ];

            $user_id = CRUDBooster::myParentId(); // User id

            $customer = $stock_data['customer'];
            $product = $stock_data['product'];

            $invoice_data['page_title'] = __('emailSetting.drm_dt_stock_mail');
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();
            $logo = ($invoice_data['setting']->logo) ? '<div style="width:150px"><img style="width:100%" src="'.$invoice_data['setting']->logo.'" /></div>' : '';

            $tags = [
                'logo' => $logo,
                'customer_name' => $customer['name'],
                'company_name' => $customer['company_name'],
                'customer_email' => $customer['email'],
                'product_name' => $product['name'],
                'product_ean' => $product['ean'],
                'product_url' => $product['url'],
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                // if($order->marketplace_order_ref){
                //     $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                //     $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                // }else{
                //     $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                // }

                $tags['shipping_address'] = 'Shipping Address';
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $template = DRMParseDtProductStockTemplate($tags, $user_id);
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Offer email Not Sent!.");
            }

            $data['subject'] = $template['subject'];
            app('drm.mailer')->getMailer($user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Offer email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Offer email sent failed!') . ' ' . $e->getMessage(), 'error');
        }
    }

    //Test DT products stock entry email
    public function postTestDtProductStockEntryEmail()
    {
        try {

            $stock_data = [
                'product' => [
                    'name' => 'Test product',
                    'channel_product_id' => 10,
                    'ean' => '123456789012',
                    'url' => 'example-shop.com/product/123',
                ],
                'customer' => [
                    'name' => 'Test customer',
                    'email' => '<EMAIL>',
                    'company_name' => 'Fake company',
                ],
            ];

            $user_id = CRUDBooster::myParentId(); // User id

            $customer = $stock_data['customer'];
            $product = $stock_data['product'];

            $invoice_data['page_title'] = __('emailSetting.drm_dt_stock_mail_entry');
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();
            $logo = $invoice_data['setting']->logo ?? '';

            $tags = [
                'logo' => $logo,
                'customer_name' => $customer['name'],
                'company_name' => $customer['company_name'],
                'customer_email' => $customer['email'],
                'product_name' => $product['name'],
                'product_ean' => $product['ean'],
                'product_url' => $product['url'],
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                // if($order->marketplace_order_ref){
                //     $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                //     $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                // }else{
                //     $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                // }

                $tags['shipping_address'] = 'Shipping_Address';
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $template = DRMParseDtProductStockTemplate($tags, $user_id, 'drm_dt_stock_mail_entry');
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Offer email Not Sent!.");
            }

            $data['subject'] = $template['subject'];
            app('drm.mailer')->getMailer($user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Offer email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Offer email sent failed!') . ' ' . $e->getMessage(), 'error');
        }
    }
    //Droptienda stock email end


    //Stock email
    public function getDroptiendaStockEmailSetting()
    {
        $data['page_title'] = __('emailSetting.drm_dt_stock_mail');
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('drm_dt_stock_mail')->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        $data['template_save_url'] = 'email_marketings/save-droptienda-stock-email-setting';
        $data['template_test_url'] = 'email_marketings/test-dt-product-stock-email';

        $data['item'] = 'drm_dt_stock_mail';

        return view('admin.new_order.dt_stock_email_template', $data);
    }

    //Stock entry email
    public function getDroptiendaStockEntryEmailSetting()
    {
        $data['page_title'] = __('emailSetting.drm_dt_stock_mail_entry');
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('drm_dt_stock_mail_entry')->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        $data['template_save_url'] = 'email_marketings/save-droptienda-stock-entry-email-setting';
        $data['template_test_url'] = 'email_marketings/test-dt-product-stock-entry-email';

        $data['item'] = 'drm_dt_stock_mail_entry';

        return view('admin.new_order.dt_stock_email_template', $data);
    }


    /*====================================================
    ========== Droptienda Subscription cancel mail =========================
    =====================================================*/


    public function getDtSubscriptionEmailSetting()
    {
        // dd("Hello");
        if (CRUDBooster::isSubUser() && !((sub_account_can('email_setting') || !sub_account_can('all_modules', 122)) || \CRUDBooster::isDropmatixSupport()) ) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $authId = CRUDBooster::myParentId();
        $data['page_title'] = __('emailSetting.drm_dt_subscription_mail');
        $data['mail'] = DB::table('dt_subscription_mails')->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        return view('admin.new_order.droptienda_subscription_email_template', $data);
    }


    public function postSaveDtSubscriptionEmailSetting()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        try{
            $data = [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => (int)$_REQUEST['auto_mail'],
                'email_template' => $_REQUEST['email_template'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];

            if (!empty($_REQUEST['bcc_email'])) {
                $data['bcc_email'] = $_REQUEST['bcc_email'];
            }

            if (!empty($_REQUEST['sender_email'])) {
                $data['sender_email'] = $_REQUEST['sender_email'];
            }

            $authId = CRUDBooster::myParentId();
            DB::table('dt_subscription_mails')->updateOrInsert(
                [
                    'cms_user_id' => $authId
                ],
                $data
            );

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Setting Changed'), 'success');
        }catch(Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }
    }

    public function postTestDtSubscriptionEmailSetting(){
        try {
            $tags = $_REQUEST;
            $tags['username'] = 'Peter';
            $tags['agreement_id'] = '998';

            $authId = CRUDBooster::myParentId();

            $template = droptiendaSubscriptionMail($tags, $authId);

            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Droptienda Subscription Email Not Sent!.");
            }

            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($authId,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Droptienda Subscription email sent!'), 'success');

        }catch (Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Droptienda Subscription email sent failed!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function dtSubscriptionEmail(LaravelRequest $request){
        try {
            $authId = CRUDBooster::myParentId();
            $data = $request->toArray();

            $parsedData = droptiendaSubscriptionMail($data, $authId);

            app('drm.mailer')->getMailer($authId,$parsedData['senderEmail'])->send('admin.new_order.email_invoice_template', ['body' => $parsedData['body']], function ($messages) use ($parsedData) {
                // $messages->from($parsedData['senderEmail']);
                $messages->to($parsedData['toEmail']);
                $messages->subject($parsedData['subject']);

                if (!empty($parsedData['bcc'])) {
                    $messages->bcc($parsedData['bcc']);
                }
            });

            return "Success";

        }catch (Exception $e){
            return response()->json([
            'success' => false,
            'message' => $e->getMessage(),
          ]);
        }
    }

    public function getDtSubscriptionCancelEmailSetting()
    {
        if (CRUDBooster::isSubUser() && (!sub_account_can('email_setting') && !sub_account_can('all_modules', 122))) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $data['page_title'] = __('emailSetting.drm_dt_subscription_cancel_mail');
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('subscription_cancel_mail')->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        return view('admin.new_order.droptienda_subscription_cancel_email_template', $data);

        // dd("Hello");
        // if (CRUDBooster::isSubUser() && (!sub_account_can('email_setting') && !sub_account_can('all_modules', 122))) {
        //     CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        // }

        // $mail['page_title'] = 'Email Marketing';
        // $authId = CRUDBooster::myParentId();
        // $mail['mail'] = DB::table('subscription_cancel_mail')->where('cms_user_id', $authId)->first();
        // $mail['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        // $mail['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        // $mail['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        // return view("admin.new_order.droptienda_subscription_email_template", $mail);
    }


    public function postSaveDtSubscriptionCancelEmailSetting()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        try{
            $data = [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => (int)$_REQUEST['auto_mail'],
                'email_template' => $_REQUEST['email_template'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];
            if (!empty($_REQUEST['bcc_email'])) {
                $data['bcc_email'] = $_REQUEST['bcc_email'];
            }

            if (!empty($_REQUEST['sender_email'])) {
                $data['sender_email'] = $_REQUEST['sender_email'];
            }
            $authId = CRUDBooster::myParentId();
            DB::table('subscription_cancel_mail')->updateOrInsert(
                [
                    'cms_user_id' => $authId
                ],
                $data
            );

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Setting Changed'), 'success');
        }catch(Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }

        // Validator::make($_REQUEST, [
        //     'sender_email' => 'required|email'
        // ])->validate();

        // $data = [
        //     'mail_subject' => $_REQUEST['mail_subject'],
        //     'head_text' => $_REQUEST['head_text'],
        //     'auto_mail' => (int)$_REQUEST['auto_mail'],
        //     'email_template' => $_REQUEST['email_template'],
        //     'bcc_email' => $_REQUEST['bcc_email'],
        //     'created_at' => now(),
        //     'updated_at' => now()
        // ];
        // if (!empty($_REQUEST['sender_email'])) {
        //     $data['sender_email'] = $_REQUEST['sender_email'];
        // }
        // $authId = CRUDBooster::myParentId();
        // DB::table('subscription_cancel_mail')->updateOrInsert(
        //     [
        //         'cms_user_id' => $authId
        //     ], $data
        // );

        // // $cache_name = $order_status . '_email_' . $authId;
        // // \Cache::forget($cache_name);
        // // clear_remote_cache($cache_name);

        // CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postTestDtSubscriptionCancelEmailSetting(){
        try {
            $tags = $_REQUEST;
            $tags['username'] = 'Peter';
            $tags['agreement_id'] = '998';

            $authId = CRUDBooster::myParentId();

            $template = droptiendaSubscriptionCancelMail($tags, $authId);

            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Droptienda Subscription Cancel Email Not Sent!.");
            }

            $data['subject'] = $template['subject'];

            app('drm.mailer')->getMailer($authId,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Droptienda Subscription Cancel email sent!'), 'success');

        }catch (Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Droptienda Subscription Cancel email sent failed!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function subscriptionCancelEmail(LaravelRequest $request){
        // dd($request->toArray());
        try {
            // $password = $request->header('userPassToken');
            // $token = $request->header('userToken');
            $data = $request->toArray();
            $authId = CRUDBooster::myParentId();
            // dd(CRUDBooster::myParentId(), "Hello");
            $parsedData = droptiendaSubscriptionCancelMail($data, $authId);
            // dd($parsedData);
            app('drm.mailer')->getMailer($authId,$parsedData['senderEmail'])->send('admin.new_order.email_invoice_template', ['body' => $parsedData['body']], function ($messages) use ($parsedData) {
                // $messages->from($parsedData['senderEmail']);
                $messages->to($parsedData['toEmail']);
                $messages->subject($parsedData['subject']);
                if (!empty($parsedData['bcc'])) {
                $messages->bcc($parsedData['bcc']);
                }
            });
            return "Success";

        }catch (Exception $e){
            return response()->json([
            'success' => false,
            'message' => 'Something went wrong!',
          ]);
        }
    }

    //order query template
    public function getOrderQueryEmailTemplate()
    {
        $data['page_title'] = __('emailSetting.mp_chat_email_template');
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('mp_chat_email_template')->where('cms_user_id', $authId)->whereNull('channel')->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        $data['template_save_url'] = 'email_marketings/save-order-query-email-template';
        $data['template_test_url'] = 'email_marketings/test-order-query-email-template';

        $data['item'] = 'mp_chat_email_template';
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myParentId());
        return view('admin.new_order.mp_order_query_template', $data);
    }


    public function postTestOrderQueryEmailTemplate()
    {
        try {

            $user_id = CRUDBooster::myParentId(); // User id

            $supplier_name = 'Fake Supplier';

            //Fake order
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $product_list = json_decode($order->cart);
            $customer = (object)$order->customer;

            $invoice_data['page_title'] = __('emailSetting.mp_chat_email_template');
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();
            $logo = $invoice_data['setting']->logo ?? '';

            $tags = [
                'logo' => $logo,
                'content' => 'Sample message content',
                'supplier_name' => $supplier_name,
                'customer_name' => $customer->full_name,
                'company_name' => $customer->company_name,
                'order_items' => view('admin.new_order.email_supplier_order_items', compact('product_list', 'order'))->render(),
                'order_items_n_article' => view('admin.new_order.email_supplier_order_items_n_article', compact('product_list', 'order'))->render(),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $template = DRMParseDtProductStockTemplate($tags, $user_id, 'mp_chat_email_template');
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Order query email Not Sent!.");
            }

            $data['subject'] = $template['subject'];
            app('drm.mailer')->getMailer($user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Order query email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Order query email sent failed!') . ' ' . $e->getMessage(), 'error');
        }
    }


    public function postSaveOrderQueryEmailTemplate()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        try{
            $data = [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
                'email_template' => $_REQUEST['email_template'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];
            if (!empty($_REQUEST['bcc_email'])) {
                $data['bcc_email'] = $_REQUEST['bcc_email'];
            }

            if (!empty($_REQUEST['sender_email'])) {
                $data['sender_email'] = $_REQUEST['sender_email'];
            }

            if (!empty($_REQUEST['channel'])) {
                $data['channel'] = $_REQUEST['channel'];
            }

            $authId = CRUDBooster::myParentId();
            DB::table('mp_chat_email_template')->updateOrInsert(
                [
                    'cms_user_id' => $authId,
                    'channel' => !empty($_REQUEST['channel']) ? $_REQUEST['channel'] : NULL
                ],
                $data
            );

            if(!empty($_REQUEST['channel'])){
                Cache::forget('mp_chat_email_template_' . $authId . '_' . $_REQUEST['channel']);
            }else{
            Cache::forget('mp_chat_email_template_' . $authId);
            }

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Setting Changed'), 'success');
        }catch(Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }
    }

    //order query template by channel
    public function getOrderQueryEmailTemplateByChannel()
    {
        $data['page_title'] = __('emailSetting.mp_chat_email_template');

        $channel = $_REQUEST['channel'];
        $authId = CRUDBooster::myParentId();

        $data['mail'] = DB::table('mp_chat_email_template')->where('cms_user_id', $authId)->where('channel', $channel)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        $data['template_save_url'] = 'email_marketings/save-order-query-email-template';
        $data['template_test_url'] = 'email_marketings/test-order-query-email-template';

        $data['item'] = 'mp_chat_email_template';
        $data['channel'] = (int) $channel;

        // return view('admin.new_order.mp_order_query_template_by_channel', $data);
        return view('admin.new_order.mp_order_query_template', $data);
    }

    public function postSaveOrderQueryEmailTemplateByChannel()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        try{
            $data = [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
                'email_template' => $_REQUEST['email_template'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];
            if (!empty($_REQUEST['bcc_email'])) {
                $data['bcc_email'] = $_REQUEST['bcc_email'];
            }

            if (!empty($_REQUEST['sender_email'])) {
                $data['sender_email'] = $_REQUEST['sender_email'];
            }
            $authId = CRUDBooster::myParentId();
            DB::table('mp_chat_email_template_by_channels')->updateOrInsert(
                [
                    'cms_user_id' => $authId,
                    'channel' => $_REQUEST['channel']
                ],
                $data
            );

            Cache::forget('mp_chat_email_template_by_channels_' . $authId . '_' . $_REQUEST['channel']);

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Setting Changed'), 'success');
        }catch(Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }
    }

    public function postTestOrderQueryEmailTemplateByChannel()
    {
        try {

            $user_id = CRUDBooster::myParentId(); // User id

            $supplier_name = 'Fake Supplier';

            //Fake order
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $product_list = json_decode($order->cart);
            $customer = (object)$order->customer;

            $invoice_data['page_title'] = __('emailSetting.mp_chat_email_template');
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();
            $logo = $invoice_data['setting']->logo ?? '';

            $tags = [
                'logo' => $logo,
                'content' => 'Sample message content',
                'supplier_name' => $supplier_name,
                'customer_name' => $customer->full_name,
                'company_name' => $customer->company_name,
                'order_items' => view('admin.new_order.email_supplier_order_items', compact('product_list', 'order'))->render(),
                'order_items_n_article' => view('admin.new_order.email_supplier_order_items_n_article', compact('product_list', 'order'))->render(),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $data['email_to'] = $_REQUEST['test_email'];
            $data['channel'] = (int) $_REQUEST['channel'];

            $template = DRMParseDtProductStockTemplate($tags, $user_id, 'mp_chat_email_template_by_channels', $data['channel']);

            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Order query email Not Sent!.");
            }

            $data['subject'] = $template['subject'];
            app('drm.mailer')->getMailer($user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Order query email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Order query email sent failed!') . ' ' . $e->getMessage(), 'error');
        }
    }


    /*===============================================
    ==== Dropfunnen CSV interval email template =====
    ================================================*/
        //order query template
    public function getDfCsvIntervalEmailTemplate()
    {
        $data['page_title'] = __('emailSetting.df_csv_email_template');
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('df_csv_email_template')->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        $data['template_save_url'] = 'email_marketings/save-df-csv-interval-email-template';
        $data['template_test_url'] = 'email_marketings/test-df-csv-interval-email-template';

        $data['item'] = 'df_csv_email_template';
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myParentId());
        return view('admin.drm_email_marketings.df_csv_email_template', $data);
    }


    public function postTestDfCsvIntervalEmailTemplate()
    {
        try {

            $user_id = CRUDBooster::myParentId(); // User id

            $tags = [
                'campaign' => 'Campaign Name',
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $template = DRMParseDtProductStockTemplate($tags, $user_id, 'df_csv_email_template');
            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];
            $data['bcc'] = $template['bcc'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Order query email Not Sent!.");
            }

            $data['subject'] = $template['subject'];
            app('drm.mailer')->getMailer($user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if (!empty($data['bcc'])) {
                    $messages->bcc($data['bcc']);
                }
            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent failed!') . ' ' . $e->getMessage(), 'error');
        }
    }


    public function postSaveDfCsvIntervalEmailTemplate()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        try{
            $data = [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
                'email_template' => $_REQUEST['email_template'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];
            if (!empty($_REQUEST['bcc_email'])) {
                $data['bcc_email'] = $_REQUEST['bcc_email'];
            }

            if (!empty($_REQUEST['sender_email'])) {
                $data['sender_email'] = $_REQUEST['sender_email'];
            }
            $authId = CRUDBooster::myParentId();
            DB::table('df_csv_email_template')->updateOrInsert(
                [
                    'cms_user_id' => $authId
                ],
                $data
            );

            Cache::forget('df_csv_email_template_' . $authId);

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Setting Changed'), 'success');
        }catch(Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }
    }

    //Dropfunnel csv interval template end

    //Linkedin view
    public function postLinkedinView($id)
    {

        $campaign = DB::table('email_marketings')
        ->where('user_id', CRUDBooster::myParentId())
        ->where('id', '=', $id)
        ->select('csv_email', 'csv_interval')
        ->first();

        $data = [
            'csv_email' => $campaign ? $campaign->csv_email : null,
            'csv_interval' => $campaign && $campaign->csv_interval ? $campaign->csv_interval : 30,
            'id' => $id,
            'has_setting' => $campaign && $campaign->csv_email,
        ];

        $view = view('admin.drm_email_marketings.linkedin_instruction', $data)->render();

        return response()->json([
            'success' => true,
            'data' => $view,
        ]);
    }

    //Save csv interval
    public function postSaveCsvInterval($id)
    {
        $request = $_REQUEST;

        Validator::make($request, [
            'email' => 'required|email',
            'interval' => 'required'
        ])
        ->validate();

        DB::table('email_marketings')
        ->where('user_id', CRUDBooster::myParentId())
        ->where('id', '=', $id)
        ->update([
            'csv_email' => $request['email'],
            'csv_interval' => $request['interval'],
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Settings update successfully!',
        ]);
    }

    public function insertTagByLink()
    {

        $info = explode('-', $_REQUEST['slug']);
        $link_info = DB::table('dropfunnel_external_links as de')
            ->join('dropfunnel_tags as dt', 'dt.id', '=', 'de.tag_id')
            ->where('de.slug', $info[0])
            ->select('dt.tag as tag', 'de.*')
            ->first();

        $customer_id = $info[1];
        if ($link_info) {
            DropfunnelCustomerTag::insertTag($link_info->tag, $link_info->user_id, $customer_id, 25);
        }

        $redirect_link = $link_info->link ?? url('/');
        return redirect()->away($redirect_link);
    }

    public function postRemoveCsvInterval($id)
    {
        DB::table('email_marketings')
            ->where('user_id', CRUDBooster::myParentId())
            ->where('id', '=', $id)
            ->update([
                'csv_email' => null,
                'csv_interval' => null,
            ]);

        return response()->json([
            'success' => true,
            'message' => 'Settings removed successfully!',
        ]);
    }

    public function postAutoMailUpdate(){
        $user_id = CRUDBooster::myParentId();

        $table_id = $_REQUEST['table_id'];
        $table_name = $_REQUEST['table_name'];
        $update_value = (int) $_REQUEST['update_value'];

        try{
            if($table_name == 'order_mail_templates'){
                $update_response = DB::table($table_name)->where(['id' => $table_id, 'user_id'=> $user_id])->update(['auto_mail' => $update_value]);
            }else{
                $update_response = DB::table($table_name)->where(['id' => $table_id, 'cms_user_id'=> $user_id])->update(['auto_mail' => $update_value]);
            }

            $message = '';

            if($update_value == 1){
                $message = 'Auto Emailing Activated!';
            }else{
                $message = 'Auto Emailing Deactivated!';
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ], 200);
        }catch(Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function postInvoiceNPaymentUpdate(){
        $user_id = CRUDBooster::myParentId();

        $table_id = $_REQUEST['table_id'];
        $table_name = $_REQUEST['table_name'];
        $update_value = (int) $_REQUEST['update_value'];
        $message = $_REQUEST['message'];

        try{
            if($table_name == 'order_mail_templates'){
                DB::table($table_name)->where(['id' => $table_id, 'user_id'=> $user_id])->update(['send_invoice' => $update_value]);
            }else if( in_array($table_name, ['combine_status_email_settings', 'payment_progress_email_settings']) ){
                DB::table($table_name)->where(['id' => $table_id, 'cms_user_id'=> $user_id])->update(['send_invoice' => $update_value]);
            }else{
                DB::table($table_name)->where(['id' => $table_id, 'cms_user_id'=> $user_id])->update(['payment_link' => $update_value]);
            }

            // $message = '';

            // if($update_value == 1){
            //     if($table_name == 'order_mail_templates' || $table_name == 'combine_status_email_settings'){
            //         $message = 'Attach Invoice / Credit note Activated!';
            //     }else{
            //         $message = 'Payment Link Activated!';
            //     }
            // }else{
            //     if($table_name == 'order_mail_templates' || $table_name == 'combine_status_email_settings'){
            //         $message = 'Attach Invoice/Credit note Deactivated!';
            //     }else{
            //         $message = 'Payment Link Deactivated!';
            //     }
            // }

            return response()->json([
                'success' => true,
                'message' => $message
            ], 200);
        }catch(Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function postCacheForget()
    {
        $user_id = CRUDBooster::myParentId();

        $table_id = $_REQUEST['table_id'];
        $table_name = $_REQUEST['table_name'];


        if($table_name == 'order_mail_templates'){
            $order_status = DB::table($table_name)->where(['id' => $table_id, 'user_id'=> $user_id])->value('order_status');
            $cache_name = $order_status . '_email_' . $user_id;
            Cache::forget($cache_name);
            clear_remote_cache($cache_name);
        }else{
            Cache::forget($table_name . '_' . $user_id);
            clear_remote_cache($table_name . '_' . $user_id);
        }
    }

    public function postChannelCacheForget()
    {
        $user_id = CRUDBooster::myParentId();

        $table_id = $_REQUEST['table_id'];
        $table_name = $_REQUEST['table_name'];
        $channel = $_REQUEST['channel'];


        if($table_name == 'order_mail_templates'){
            $order_status = DB::table($table_name)->where(['id' => $table_id, 'channel' => $channel, 'user_id'=> $user_id])->value('order_status');
            $cache_name = $order_status . '_email_' . $user_id . '_' . $channel;
            Cache::forget($cache_name);
            clear_remote_cache($cache_name);
        }else{
            Cache::forget($table_name . '_' . $user_id . '_' . $channel);
            clear_remote_cache($table_name . '_' . $user_id . '_' . $channel);
        }
    }

    public function getInvoiceSetting(){
        $user_id = CRUDBooster::myParentId();
        $setting_table = $_REQUEST['settings_table'];

        if($setting_table == 'drm_offer_setting'){
            $invoice_settings = DB::table($setting_table)->where('cms_user_id', $user_id)->select('id', 'eamil')->orderBy('id', 'desc')->first();
        }else{
            $invoice_settings = DB::table($setting_table)->where('cms_user_id', $user_id)->select('id', 'email')->orderBy('id', 'desc')->first();
        }

        if(empty($invoice_settings)){
            $invoice_settings = '';
        }

        return response()->json([
            'setting' => $invoice_settings
        ]);
    }

    public function postAutoMailUpdateByChannel(){
        $user_id = CRUDBooster::myParentId();

        $table_id = $_REQUEST['table_id'];
        $table_name = $_REQUEST['table_name'];
        $channel = (int) $_REQUEST['channel'];
        $update_value = (int) $_REQUEST['update_value'];

        try{
            if($table_name == 'order_mail_templates'){
                $update_response = DB::table($table_name)->where(['id' => $table_id, 'user_id'=> $user_id])->update(['auto_mail' => $update_value]);
            }else{
                $update_response = DB::table($table_name)->where(['id' => $table_id, 'cms_user_id'=> $user_id, 'channel' => $channel])->update(['auto_mail' => $update_value]);
            }

            $message = '';

            if($update_value == 1){
                $message = 'Auto Emailing Activated!';
            }else{
                $message = 'Auto Emailing Deactivated!';
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ], 200);
        }catch(Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function postInvoiceNPaymentUpdateByChannel(){
        $user_id = CRUDBooster::myParentId();

        $table_id = $_REQUEST['table_id'];
        $table_name = $_REQUEST['table_name'];
        $channel = (int) $_REQUEST['channel'];
        $update_value = (int) $_REQUEST['update_value'];
        $message = $_REQUEST['message'];

        try{
            if($table_name == 'order_mail_templates'){
                DB::table($table_name)->where(['id' => $table_id, 'user_id'=> $user_id, 'channel' => $channel])->update(['send_invoice' => $update_value]);
            }else if( in_array($table_name, ['combine_status_email_settings', 'payment_progress_email_settings']) ){
                DB::table($table_name)->where(['id' => $table_id, 'cms_user_id'=> $user_id, 'channel' => $channel])->update(['send_invoice' => $update_value]);
            }else{
                DB::table($table_name)->where(['id' => $table_id, 'cms_user_id'=> $user_id, 'channel' => $channel])->update(['payment_link' => $update_value]);
            }

            // $message = '';

            // if($update_value == 1){
            //     if($table_name == 'order_mail_template_by_channels' || $table_name == 'combine_status_email_settings'){
            //         $message = 'Attach Invoice / Credit note Activated!';
            //     }else{
            //         $message = 'Payment Link Activated!';
            //     }
            // }else{
            //     if($table_name == 'order_mail_template_by_channels' || $table_name == 'combine_status_email_settings'){
            //         $message = 'Attach Invoice/Credit note Deactivated!';
            //     }else{
            //         $message = 'Payment Link Deactivated!';
            //     }
            // }

            return response()->json([
                'success' => true,
                'message' => $message
            ], 200);
        }catch(Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function getCombineStatusEmailSetting()
    {
        if (CRUDBooster::isSubUser() && (!sub_account_can('email_setting') && !sub_account_can('all_modules', 122))) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $authId = CRUDBooster::myParentId();

        $data['page_title'] = __('emailSetting.combine_status_email');
        $data['mail'] = DB::table('combine_status_email_settings')->where('cms_user_id', $authId)->whereNull('channel')->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        return view("admin.new_order.combine_status_email_setting", $data);
    }

    public function postSaveCombineStatusEmailSetting()
    {

        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => (int)$_REQUEST['auto_mail'],
            'send_invoice' => (int)$_REQUEST['send_invoice'],
            'email_template' => $_REQUEST['email_template'],
            'bcc_email' => $_REQUEST['bcc_email'],
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        if (!empty($_REQUEST['channel'])) {
            $data['channel'] = $_REQUEST['channel'];
        }


        $authId = CRUDBooster::myParentId();

        DB::table('combine_status_email_settings')->updateOrInsert(
            [
                'cms_user_id' => $authId,
                'channel' => !empty($_REQUEST['channel']) ? $_REQUEST['channel'] : NULL
            ], $data
        );

        if(!empty($_REQUEST['channel'])){
            Cache::forget('combine_status_email_settings_' . $authId . '_' . $_REQUEST['channel']);
            clear_remote_cache('combine_status_email_settings_' . $authId . '_' . $_REQUEST['channel']);
        }else{
        Cache::forget('combine_status_email_settings_' . $authId);
        clear_remote_cache('combine_status_email_settings_' . $authId);
        }

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postTestCombineStatusEmail()
    {
        try {
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $invoice_data['page_title'] = 'Test order email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $template = DRMParseOrderCombineStatusEmailTemplate($tags, $order->cms_user_id);

            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $is_send_invoice = $template['send_invoice'] ?? 0;
            $pdf_stream = $is_send_invoice ? PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream() : '';

            $data['subject'] = $template['subject'];
            $data['bcc'] = $template['bcc'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $is_send_invoice) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                //Send invoice
                if ($is_send_invoice === 1) {
                    $messages->attachData($pdf_stream, 'invoice.pdf', [
                        'mime' => 'application/pdf',
                    ]);
                }

                //BCC
                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (\Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function getCombineStatusEmailSettingByChannel()
    {
        if (CRUDBooster::isSubUser() && (!sub_account_can('email_setting') && !sub_account_can('all_modules', 122))) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $authId = CRUDBooster::myParentId();
        $channel = $_REQUEST['channel'];

        $data['page_title'] = __('emailSetting.combine_status_email');
        $data['mail'] = DB::table('combine_status_email_settings')->where('cms_user_id', $authId)->where('channel', $channel)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['channel'] = (int) $channel;

        // return view("admin.new_order.system_email_by_channel.combine_status_email_setting", $data);
        return view("admin.new_order.combine_status_email_setting", $data);
    }

    public function postSaveCombineStatusEmailSettingByChannel()
    {

        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => (int)$_REQUEST['auto_mail'],
            'send_invoice' => (int)$_REQUEST['send_invoice'],
            'email_template' => $_REQUEST['email_template'],
            'bcc_email' => $_REQUEST['bcc_email'],
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        $authId = CRUDBooster::myParentId();

        DB::table('combine_status_email_setting_by_channels')->updateOrInsert(
            [
                'cms_user_id' => $authId,
                'channel' =>  $_REQUEST['channel']
            ], $data
        );

        Cache::forget('combine_status_email_setting_by_channels_' . $authId . '_' . $_REQUEST['channel']);
        clear_remote_cache('combine_status_email_setting_by_channels_' . $authId . '_' . $_REQUEST['channel']);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postTestCombineStatusEmailByChannel()
    {
        try {
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $invoice_data['page_title'] = 'Test order email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }


            $data['email_to'] = $_REQUEST['test_email'];
            $data['channel'] = (int) $_REQUEST['channel'];

            $template = DRMParseOrderCombineStatusEmailTemplate($tags, $order->cms_user_id, $data['channel']);

            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $is_send_invoice = $template['send_invoice'] ?? 0;
            $pdf_stream = $is_send_invoice ? PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream() : '';

            $data['subject'] = $template['subject'];
            $data['bcc'] = $template['bcc'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $is_send_invoice) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                //Send invoice
                if ($is_send_invoice === 1) {
                    $messages->attachData($pdf_stream, 'invoice.pdf', [
                        'mime' => 'application/pdf',
                    ]);
                }

                //BCC
                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (\Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function getMpReturnEmailSetting()
    {
        $data['page_title'] = __('Mp Return Email Setting');
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('drm_email_templete_mp_return')->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        $data['template_save_url'] = 'email_marketings/save-mp-return-email-setting';
        // $data['template_test_url'] = 'email_marketings/test-dt-product-stock-entry-email';

        return view('admin.new_order.mp_return_mail', $data);
    }

    public function postSaveMpReturnEmailSetting()
    {
        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        try{
            $data = [
                'mail_subject' => $_REQUEST['mail_subject'],
                'head_text' => $_REQUEST['head_text'],
                'bottom_text' => $_REQUEST['bottom_text'],
                'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
                'email_template' => $_REQUEST['email_template'],
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ];
            if (!empty($_REQUEST['bcc_email'])) {
                $data['bcc_email'] = $_REQUEST['bcc_email'];
            }

            if (!empty($_REQUEST['sender_email'])) {
                $data['sender_email'] = $_REQUEST['sender_email'];
            }

            if (!empty($_REQUEST['channel'])) {
                $data['channel'] = $_REQUEST['channel'];
            }

            $authId = CRUDBooster::myParentId();
            DB::table('drm_email_templete_mp_return')->updateOrInsert(
                [
                    'cms_user_id' => $authId,
                    'channel' => !empty($_REQUEST['channel']) ? $_REQUEST['channel'] : NULL
                ],
                $data
            );

            Cache::forget('drm_email_templete_mp_return_' . $authId);
            clear_remote_cache('drm_email_templete_mp_return_' . $authId);

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Setting Changed'), 'success');
        }catch(Exception $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), 'error');
        }
    }


    public function getEmailSettingCommon($table)
    {
        if(!in_array($table, ['drm_email_templete_mp_return', 'drm_email_templete_return_received', 'drm_email_templete_return_shipped', 'drm_email_templete_mp_rejected', 'drm_email_templete_stock_unavailable'])) abort(404);
        if (!CRUDBooster::isSupportAccount() && CRUDBooster::isSubUser() && !((sub_account_can('email_setting') || !sub_account_can('all_modules', 122)) || \CRUDBooster::isDropmatixSupport())) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $data['page_title'] = __('emailSetting.'.$table);
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table($table)->where('cms_user_id', $authId)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['table'] = $table;
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myParentId());
        return view("admin.new_order.email_setting_common", $data);
    }

    public function postSaveEmailSettingCommon($table)
    {
        if(!in_array($table, ['drm_email_templete_mp_return', 'drm_email_templete_return_received', 'drm_email_templete_return_shipped', 'drm_email_templete_mp_rejected', 'drm_email_templete_stock_unavailable'])) abort(404);

        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])
        ->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => $_REQUEST['auto_mail'] ?? 0,
            'email_template' => $_REQUEST['email_template'],
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];
        if (!empty($_REQUEST['bcc_email'])) {
            $data['bcc_email'] = $_REQUEST['bcc_email'];
        }

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }
        $authId = CRUDBooster::myParentId();
        DB::table($table)->updateOrInsert(
            [
                'cms_user_id' => $authId
            ],
            $data
        );

        Cache::forget("{$table}_{$authId}");
        clear_remote_cache("{$table}_{$authId}");

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }


    public function postTestEmailCommon($table)
    {
        try {

            if(!in_array($table, ['drm_email_templete_mp_return', 'drm_email_templete_return_received', 'drm_email_templete_return_shipped', 'drm_email_templete_mp_rejected', 'drm_email_templete_stock_unavailable'])) throw new Exception('Something went wrong!');

            app(\App\Services\Order\ReturnLabel::class)->sendTestMail($table);
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function getPaymentProgressEmailSetting()
    {
        if (CRUDBooster::isSubUser() && (!sub_account_can('email_setting') && !sub_account_can('all_modules', 122))) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $authId = CRUDBooster::myParentId();

        $data['page_title'] = __('emailSetting.payment_progress_email');
        $data['mail'] = DB::table('payment_progress_email_settings')->where('cms_user_id', $authId)->whereNull('channel')->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['creditBalance'] = @get_token_credit(CRUDBooster::myParentId());
        return view("admin.new_order.payment_progress_email_setting", $data);
    }

    public function postSavePaymentProgressEmailSetting()
    {

        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => (int)$_REQUEST['auto_mail'],
            'send_invoice' => (int)$_REQUEST['send_invoice'],
            'email_template' => $_REQUEST['email_template'],
            'bcc_email' => $_REQUEST['bcc_email'],
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        if (!empty($_REQUEST['channel'])) {
            $data['channel'] = $_REQUEST['channel'];
        }


        $authId = CRUDBooster::myParentId();

        DB::table('payment_progress_email_settings')->updateOrInsert(
            [
                'cms_user_id' => $authId,
                'channel' => !empty($_REQUEST['channel']) ? $_REQUEST['channel'] : NULL
            ], $data
        );

        if(!empty($_REQUEST['channel'])){
            Cache::forget('payment_progress_email_settings_' . $authId . '_' . $_REQUEST['channel']);
            clear_remote_cache('payment_progress_email_settings_' . $authId . '_' . $_REQUEST['channel']);
        }else{
        Cache::forget('payment_progress_email_settings_' . $authId);
        clear_remote_cache('payment_progress_email_settings_' . $authId);
        }

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postTestPaymentProgressEmail()
    {
        try {
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $invoice_data['page_title'] = 'Test order email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }

            $template = DRMParsePaymentProgressEmailTemplate($tags, $order->cms_user_id);

            $data['email_to'] = $_REQUEST['test_email'];
            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $is_send_invoice = $template['send_invoice'] ?? 0;
            $pdf_stream = $is_send_invoice ? PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream() : '';

            $data['subject'] = $template['subject'];
            $data['bcc'] = $template['bcc'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $is_send_invoice) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                //Send invoice
                if ($is_send_invoice === 1) {
                    $messages->attachData($pdf_stream, 'invoice.pdf', [
                        'mime' => 'application/pdf',
                    ]);
                }

                //BCC
                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (\Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    public function getPaymentProgressEmailSettingByChannel()
    {
        if (CRUDBooster::isSubUser() && (!sub_account_can('email_setting') && !sub_account_can('all_modules', 122))) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $authId = CRUDBooster::myParentId();
        $channel = $_REQUEST['channel'];

        $data['page_title'] = __('emailSetting.payment_progress_email');
        $data['mail'] = DB::table('payment_progress_email_settings')->where('cms_user_id', $authId)->where('channel', $channel)->first();
        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');
        $data['channel'] = (int) $channel;

        return view("admin.new_order.payment_progress_email_setting", $data);
    }

    public function postSavePaymentProgressEmailSettingByChannel()
    {

        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'bottom_text' => $_REQUEST['bottom_text'],
            'auto_mail' => (int)$_REQUEST['auto_mail'],
            'send_invoice' => (int)$_REQUEST['send_invoice'],
            'email_template' => $_REQUEST['email_template'],
            'bcc_email' => $_REQUEST['bcc_email'],
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now()
        ];

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        $authId = CRUDBooster::myParentId();

        DB::table('payment_progress_email_settings')->updateOrInsert(
            [
                'cms_user_id' => $authId,
                'channel' =>  $_REQUEST['channel']
            ], $data
        );

        Cache::forget('payment_progress_email_settings_' . $authId . '_' . $_REQUEST['channel']);
        clear_remote_cache('payment_progress_email_settings_' . $authId . '_' . $_REQUEST['channel']);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email Setting Changed'), 'success');
    }

    public function postTestPaymentProgressEmailByChannel()
    {
        try {
            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order();
            $invoice_data['page_title'] = 'Test order email template';
            $invoice_data['order'] = $order;
            $invoice_data['product_list'] = json_decode($order->cart);
            $invoice_data['customer'] = (object)$order->customer;
            $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->orderBy('id', 'desc')->first();

            $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439])) ? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4) ? 'admin.invoice.charge_inv' : $pdf_view;

            $product_list = json_decode($order->cart);

            $billing = '<p>' . formatBillingAddress($order->billing, false, $order->id) . '</p>';

            $logo = $invoice_data['setting']->logo ?? '';
            $tags = [
                'customer_name' => $invoice_data['customer']->full_name,
                'company_name' => $invoice_data['customer']->company_name,
                'billing_address' => $billing,
                'order_items' => view('admin.new_order.email_order_items', compact('product_list', 'order'))->render(),
                'order_date' => Carbon::parse($order->order_date)->format('l jS \\of F Y h:i:s A'),
                'logo' => '<img id="display_logo" width="150" src="' . $logo . '" alt="' . $invoice_data['setting']->store_name . '" >',
                'order_number' => $order->id,
                'invoice_number' => inv_number_string($order->invoice_number, $order->inv_pattern),
                'pay_url' => null,
                'PAYWALL' => false,
            ];

            // if( (isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])) ){

                if($order->marketplace_order_ref){
                    $ref_order = DB::table('new_orders')->where(['id' => $order->marketplace_order_ref])->select('shipping')->first();

                    $shipping = '<p>' . formatBillingAddress($ref_order->shipping, false, $ref_order->id) . '</p>';
                }else{
                    $shipping = '<p>' . formatBillingAddress($order->shipping, false, $order->id) . '</p>';
                }

                $tags['shipping_address'] = $shipping;
                $tags['credit_note'] = 'TEST_PARCEl';

                $email_signatures = DB::table('drop_funnel_signatures')->where('user_id', CRUDBooster::myParentId())->pluck('signature','id')->toArray();

                if($email_signatures){
                    foreach($email_signatures as $key => $signature){
                        $tags['drm-sign-'.$key] = $signature;
                    }
                }
            // }


            $data['email_to'] = $_REQUEST['test_email'];
            $data['channel'] = (int) $_REQUEST['channel'];

            $template = DRMParsePaymentProgressEmailTemplate($tags, $order->cms_user_id, $data['channel']);

            $data['email_from'] = $template['senderEmail'];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $is_send_invoice = $template['send_invoice'] ?? 0;
            $pdf_stream = $is_send_invoice ? PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream() : '';

            $data['subject'] = $template['subject'];
            $data['bcc'] = $template['bcc'];

            app('drm.mailer')->getMailer($order->cms_user_id,$data['email_from'])->send('admin.new_order.email_invoice_template', $template, function ($messages) use ($data, $pdf_stream, $is_send_invoice) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                //Send invoice
                if ($is_send_invoice === 1) {
                    $messages->attachData($pdf_stream, 'invoice.pdf', [
                        'mime' => 'application/pdf',
                    ]);
                }

                //BCC
                if ($data['bcc']) {
                    $messages->bcc($data['bcc']);
                }

            });

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent!'), 'success');
        } catch (\Exception $e) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    // Signature default signature
    public function createDefaultSignature()
    {
        $user_id = CRUDBooster::myParentId();
        if(DropFunnelSignature::where('user_id', $user_id)->exists()) return true;

        DropFunnelSignature::create([
            'user_id' => $user_id,
            'title' => 'Default signature',
            'signature' => DB::table('cms_users')->where('id', $user_id)->value('email'),
        ]);
    }

    public function statShowModal($id)
    {

        $campaign = EmailMarketing::with(['steps' => function ($query) {
            $query->orderBy('position');
        }])->find($id);

        $customers = $this->findCustomersByCampaignId($id);
        $customerCount = $customers->count();

        $steps = $campaign->steps;

        $data = $this->getStepStats($campaign, $customers);

        $html = view('admin.drm_email_marketings.partials._show_campaign_stats_modal', compact('data'))->render();
        return response()->json(['success' => true, 'html' => $html, 'title' => __('Statistics')]);
    }

    public function statShowDetail($id){
        $this->cbLoader();

        $step = DropfunnelStepMail::with(['campaign', 'webhookHistories'])->orderBy('position')->find($id);

        $campaign = $step->campaign;
        $webhooks = $step->webhookHistories;

        $customers = $this->findCustomersByCampaignId($campaign->id);
        $customer_ids = $customers->pluck('id')->toArray();
        $customerCount = $customers->count();

        $arr['id'] = $step->id;
        $arr['title-link'] = '<a href="' . url('/admin/stat-show-details') . '/' . $step->id . '">' . $step->step_name . '</a>';
        $arr['title'] = $step->step_name;
        $arr['subject'] = $step->subject;
        $arr['campaign_id'] = $campaign->id;

        // $stepDeliveryCount = dropFunnelStepRateCalculation($campaign, $customerCount, 'delivered', $step->id, true);
        $stepDeliveryCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNotNull('delivered')->count();
        $stepNotDeliveryCount = $customerCount - $stepDeliveryCount;
        // $stepDeliveryRate = dropFunnelStepRateCalculation($campaign, $customerCount, 'delivered', $step->id);
        $stepDeliveryRate = $customerCount > 0 ? number_format((($stepDeliveryCount * 100) / $customerCount), 2) : 0;
        // $stepNotDeliveryRate = 100 - $stepDeliveryRate;

        $stepClickedCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNotNull('clicked')->count();
        $stepNotClickedCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNull('clicked')->count();
        $stepClickedRate = $customerCount > 0 ? number_format((($stepClickedCount * 100) / $customerCount), 2) : 0;
        // $stepNotClickedRate = 100 - $stepClickedRate;

        $stepOpenCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNotNull('opened')->count();
        $stepNotOpenCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNull('opened')->count();
        $stepOpenRate = $customerCount > 0 ? number_format((($stepOpenCount * 100) / $customerCount), 2) : 0;
        // $stepNotOpenRate = 100 - $stepOpenRate;

        $data['stepData'] = $arr;

        $data['campaign'] = $campaign;


        //Overview Graph Data
        $overview = [];

        $delivered[] = ['x' => __('Delivered'), 'y' => (float)$stepDeliveryRate ?? 0];
        $opened[] = ['x' => __('Opened'), 'y' => (float)$stepOpenRate ?? 0];
        $clicked[] = ['x' => __('Clicked'), 'y' => (float)$stepClickedRate ?? 0];

        $overviewData[] = [
            'name' => __('Delivered'),
            'data'=> $delivered
        ];

        $overviewData[] = [
            'name' => __('Opened'),
            'data'=> $opened
        ];

        $overviewData[] = [
            'name' => __('Clicked'),
            'data'=> $clicked
        ];

        $data['overview'] = $overviewData;


        //Delivery Graph Data
        $deliveredGraphData[] = $stepDeliveryCount ?? 0;

        $deliveredGraphData[] = $stepNotDeliveryCount ?? 0;

        $data['deliveredGraphData'] = $deliveredGraphData;


        //Openings Graph Data
        $openedGraphData[] = $stepOpenCount ?? 0;

        $openedGraphData[] = $stepNotOpenCount ?? 0;

        $data['openedGraphData'] = $openedGraphData;


        //Clicks Garph Data
        $clickedGraphData[] = $stepClickedCount ?? 0;

        $clickedGraphData[] = $stepNotClickedCount ?? 0;

        $data['clickedGraphData'] = $clickedGraphData;

        $data['deliveryCount'] = $stepDeliveryCount;
        $data['notDeliveryCount'] = $stepNotDeliveryCount;
        $data['clickedCount'] = $stepClickedCount;
        $data['notClickedCount'] = $stepNotClickedCount;
        $data['openCount'] = $stepOpenCount;
        $data['notOpenCount'] = $stepNotOpenCount;
        $data['categories'] = [__('Delivered'), __('Opened'), __('Clicked')];

        return view('admin.drm_email_marketings.step_stats', $data);
    }

    public function statMainShowDetail($id){
        $this->cbLoader();

        $campaign = EmailMarketing::with('mainStepsWebhooks')->find($id);
        $webhooks = $campaign->mainStepsWebhooks;

        $customers = $this->findCustomersByCampaignId($campaign->id);
        $customer_ids = $customers->pluck('id')->toArray();
        $customerCount = $customers->count();

        $arr['campaign_id'] = $campaign->id;
        $arr['id'] = -1;

        // $stepDeliveryCount = $this->dropFunnelRateCalculation($campaign, $customers, 'delivered', true);
        $stepDeliveryCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNotNull('delivered')->count();
        $stepNotDeliveryCount = $customerCount - $stepDeliveryCount;
        // $stepDeliveryRate = $this->dropFunnelRateCalculation($campaign, $customers, 'delivered');
        $stepDeliveryRate = $customerCount > 0 ? number_format((($stepDeliveryCount * 100) / $customerCount), 2) : 0;
        // $stepNotDeliveryRate = 100 - $stepDeliveryRate;

        $stepClickedCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNotNull('clicked')->count();
        $stepNotClickedCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNull('clicked')->count();
        $stepClickedRate = $customerCount > 0 ? number_format((($stepClickedCount * 100) / $customerCount), 2) : 0;
        // $stepNotClickedRate = 100 - $stepClickedRate;

        $stepOpenCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNotNull('opened')->count();
        $stepNotOpenCount = $webhooks->whereIn('customer_id', $customer_ids)->whereNull('opened')->count();
        $stepOpenRate = $customerCount > 0 ? number_format((($stepOpenCount * 100) / $customerCount), 2) : 0;
        // $stepNotOpenRate = 100 - $stepOpenRate;

        $data['stepData'] = $arr;

        $data['campaign'] = $campaign;


        //Overview Graph Data
        $overview = [];

        $delivered[] = ['x' => __('Delivered'), 'y' => (float)$stepDeliveryRate ?? 0];
        $opened[] = ['x' => __('Opened'), 'y' => (float)$stepOpenRate ?? 0];
        $clicked[] = ['x' => __('Clicked'), 'y' => (float)$stepClickedRate ?? 0];

        $overviewData[] = [
            'name' => __('Delivered'),
            'data'=> $delivered
        ];

        $overviewData[] = [
            'name' => __('Opened'),
            'data'=> $opened
        ];

        $overviewData[] = [
            'name' => __('Clicked'),
            'data'=> $clicked
        ];

        $data['overview'] = $overviewData;


        //Delivery Graph Data
        $deliveredGraphData[] = $stepDeliveryCount ?? 0;

        $deliveredGraphData[] = $stepNotDeliveryCount ?? 0;

        $data['deliveredGraphData'] = $deliveredGraphData;


        //Openings Graph Data
        $openedGraphData[] = $stepOpenCount ?? 0;

        $openedGraphData[] = $stepNotOpenCount ?? 0;

        $data['openedGraphData'] = $openedGraphData;


        //Clicks Garph Data
        $clickedGraphData[] = $stepClickedCount ?? 0;

        $clickedGraphData[] = $stepNotClickedCount ?? 0;

        $data['clickedGraphData'] = $clickedGraphData;

        $data['deliveryCount'] = $stepDeliveryCount;
        $data['notDeliveryCount'] = $stepNotDeliveryCount;
        $data['clickedCount'] = $stepClickedCount;
        $data['notClickedCount'] = $stepNotClickedCount;
        $data['openCount'] = $stepOpenCount;
        $data['notOpenCount'] = $stepNotOpenCount;
        $data['categories'] = [__('Delivered'), __('Opened'), __('Clicked')];

        return view('admin.drm_email_marketings.step_stats', $data);
    }

    public function getStepStats($campaign, $customers){
        $data = [];
        $stepData = [];
        $steps = $campaign->steps;
        $customerCount = $customers->count();

        $data['titleLink'] = '<a href="' . url('/admin/stat-main-show-details') . '/' . $campaign->id . '">' . ($campaign->step_name ?? "N/A") . '</a>';

        $deliveryCount = $this->dropFunnelRateCalculation($campaign, $customers, 'delivered', true);
        $data['deliveryLabel'] = "$deliveryCount / $customerCount";
        $data['deliveryRate'] = $this->dropFunnelRateCalculation($campaign, $customers, 'delivered');

        $clickRateCount = $this->dropFunnelRateCalculation($campaign, $customers, 'clicked', true);
        $data['clickedLabel'] = "$clickRateCount / $customerCount";
        $data['clickRate'] = $this->dropFunnelRateCalculation($campaign, $customers, 'clicked');

        $openRateCount = $this->dropFunnelRateCalculation($campaign, $customers, 'opened', true);
        $data['openRateLabel'] = "$openRateCount / $customerCount";
        $data['openRate'] = $this->dropFunnelRateCalculation($campaign, $customers, 'opened');

        foreach($steps as $step){
            $arr['title-link'] = '<a href="' . url('/admin/stat-show-details') . '/' . $step->id . '">' . $step->step_name ?? "N/A" . '</a>';
            $arr['title'] = $step->step_name ?? "N/A";
            $arr['subject'] = $step->subject;

            $stepDeliveryCount = dropFunnelStepRateCalculationNew($campaign, $customers, 'delivered', $step->id, true);
            $arr['stepDeliveryLabel'] = "$stepDeliveryCount / $customerCount";
            $arr['stepDeliveryRate'] = dropFunnelStepRateCalculationNew($campaign, $customers, 'delivered', $step->id);

            $stepClickRateCount = dropFunnelStepRateCalculationNew($campaign, $customers, 'clicked', $step->id, true);
            $arr['stepClickedLabel'] = "$stepClickRateCount / $customerCount";
            $arr['stepClickRate'] = dropFunnelStepRateCalculationNew($campaign, $customers, 'clicked', $step->id);

            $stepOpenRateCount = dropFunnelStepRateCalculationNew($campaign, $customers, 'opened', $step->id, true);
            $arr['stepOpenRateLabel'] = "$stepOpenRateCount / $customerCount";
            $arr['stepOpenRate'] = dropFunnelStepRateCalculationNew($campaign, $customers, 'opened', $step->id);
            $stepData[] = $arr;
        }

        $data['stepData'] = $stepData;

        return $data;
    }

    public function getUserCampaigns($user_id = 0) {
        $user_campaigns = EmailMarketing::from('email_marketings AS em')
            ->join('builder_templates AS bt', 'bt.id', '=', 'em.builder_template_id') // in builder_templates user_id is available
            ->where([
                'UserId' => $user_id,
                'status' => 0 // not live
            ])
            ->pluck('em.id', 'em.campaign_name')
            ->toArray();

        return $user_campaigns;
    }

    public function getUserCampaignsTags($user_id = 0) {
        $user_campaigns_tags = DropfunnelTag::join('dropfunnel_customer_tags as drop_cus_tags', 'dropfunnel_tags.id', '=', 'drop_cus_tags.tag_id')
            ->join('new_customers as new_cus', 'drop_cus_tags.customer_id', '=', 'new_cus.id')
            ->where([
                'dropfunnel_tags.user_id'   => $user_id,
                'new_cus.user_id'           => $user_id,
                'drop_cus_tags.insert_type' => 5 // campaign
            ])
            ->whereNull('drop_cus_tags.deleted_at')
            ->whereNull('new_cus.deleted_at')
            ->orderBy('tag', 'asc')
            ->pluck('tag', 'dropfunnel_tags.id')
            ->toArray();

        return $user_campaigns_tags;
    }

    public function checkCampaignValidation(): JsonResponse
    {
        try {
            $message = '';
            $data = $error_messages = [];

            $campaign_id   = request()->get('campaign_id', 0);

            $campaign_info = EmailMarketing::find($campaign_id);
            if (!empty($campaign_info)) {
                // campaign current status
                if ($campaign_info->status != 0) { // it must be empty here for selection
                    array_push($error_messages, 'Campaign is already live. It cant be selected here again.');
                }

                if (empty($campaign_info->campaign_name)) {
                    array_push($error_messages, 'Campaign name is missing.');
                } else if (!is_string($campaign_info->campaign_name)) {
                    array_push($error_messages, 'Campaign name must be string.');
                }

                if (empty($campaign_info->name)) {
                    array_push($error_messages, 'Campaign subject is missing.');
                } else if (!is_string($campaign_info->name)) {
                    array_push($error_messages, 'Campaign subject must be string.');
                }

                if (empty( trim($campaign_info->email_template) )) {
                    array_push($error_messages, 'Email template body is missing.');
                } else if (!is_string($campaign_info->email_template)) {
                    array_push($error_messages, 'Email template body must be string.');
                }

                // if (empty($campaign_info->calculation_percentage)) {
                //     array_push($error_messages, 'You must set calculation percentage to add product.');
                // } else if (!is_numeric($campaign_info->calculation_percentage)) {
                //     array_push($error_messages, 'Calculation percentage value must be numeric.');
                // }

                // if (!empty($campaign_info->calculation_tax)) {
                //     if (!is_numeric($campaign_info->calculation_tax)) {
                //         array_push($error_messages, 'Calculation tax value must be numeric.');
                //     }
                // }

                // campagin signature
                if (empty($campaign_info->signature_id)) {
                    array_push($error_messages, 'Campaign singnature is missing.');
                } else {
                    if(!(DropFunnelSignature::where('id', $campaign_info->signature_id)->exists())) {
                        array_push($error_messages, 'Invalid signature!');
                    }
                }

                // campagin sender_email
                if (empty($campaign_info->sender_email)) {
                    array_push($error_messages, 'Sender Email is missing.');
                } else {
                    if (!filter_var($campaign_info->sender_email, FILTER_VALIDATE_EMAIL)) {
                        array_push($error_messages, 'Sender Email is invalid.');
                    }
                }
            } else {
                array_push($error_messages, 'Campaign doesnt exist.');
            }

            $tag_exists = CampainTag::where('campaign_id', $campaign_id)->exists();
            if (empty($tag_exists)) {
                array_push($error_messages, 'Campaign should have at least one tag.');
            }

            $stepMails = DB::table('dropfunnel_step_mails')->where('campaign_id', $campaign_id)->orderBy('position', 'asc')->get();
            if (!empty($stepMails)) {
                foreach($stepMails as $index => $stepMail) {
                    $step_pos = $stepMail->position;
                    $error_msg_step_name =  $stepMail->step_name ? $stepMail->step_name : 'Step ' . $step_pos;

                    // step email subject
                    if (empty($stepMail->subject)) {
                        array_push($error_messages, $error_msg_step_name . ' subject is missing.');
                    } else if (strlen($stepMail->subject) < 10) {
                        array_push($error_messages, $error_msg_step_name . ' subject length must be minimum 10 characters.');
                    } else if (!is_string($stepMail->subject)) {
                        array_push($error_messages, $error_msg_step_name . ' subject must be string.');
                    }

                    // step email body
                    if (empty( trim($stepMail->email_body) )) {
                        array_push($error_messages, $error_msg_step_name . ' email body is missing.');
                    } else if (!is_string($stepMail->email_body)) {
                        array_push($error_messages, $error_msg_step_name . ' email body must be string.');
                    }

                    // if (empty($campaign_info->calculation_percentage)) {
                    //     array_push($error_messages, 'You must set calculation percentage to add product in your email body Step ' . $step_pos);
                    // }

                    // step followup email time duration
                    if (empty($stepMail->type)) {
                        array_push($error_messages, $error_msg_step_name . ' day type is missing.');
                    } else if (!is_string($stepMail->type)) {
                        array_push($error_messages, $error_msg_step_name . ' day type must be string.');
                    }
                    if (empty($stepMail->value)) {
                        array_push($error_messages, $error_msg_step_name . ' day type value is missing.');
                    } else if (!is_integer((int)$stepMail->value)) {
                        array_push($error_messages, $error_msg_step_name . ' day type must be integer.');
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data'    => $data,
                'message' => $error_messages,
            ], 200);
        } catch(\Exception $ex) {
            return response()->json([
                'success' => false,
                'message' => $ex->getMessage()
            ], 422);
        }
    }

    public function getAddActions()
    {
        $id = $_GET['id'];

        $data['id'] = $id;

        $data['saved_buttons'] =  DropfunnelStepMail::where('id', $id)->value('action_buttons');

        $data['all_action_buttons'] = config('dropfunnel_action_buttons.action_buttons');
        $data['all_action_buttons_name'] = config('dropfunnel_action_buttons.action_buttons_name');
        $data['all_action_buttons_color'] = config('dropfunnel_action_buttons.action_buttons_color');
        $data['all_action_buttons_icon'] = config('dropfunnel_action_buttons.action_buttons_icon');

        $add_action = view('admin.drm_email_marketings.partials.add_action_modal', $data)->render();

        return response()->json([
            'success'=> true,
            'html' => $add_action,
        ], 200);
    }

    public function storeActions(LaravelRequest $request)
    {
        $user_data = $request->all();
        $id = $user_data['camp_or_step_id'];

        $user_action_button = DropfunnelStepMail::where('id', $id)->select('action_buttons', 'goal_notify', 'extra_campaign_id', 'open_mail_notify', 'send_mail_again', 'extra_tag', 'remove_tag')->first();

        $user_data_key = array_keys($user_data);
        $data_insert = [];

        if(!empty($user_data)){

            $temp_button_position = 1;

            if(!empty($user_action_button->action_buttons)){         //Finding user saved action last position to update action buttons position
                $previous_last_position = max(array_column($user_action_button->action_buttons, 'position'));

                $temp_button_position = $previous_last_position + 1;
            }

            foreach($user_data as $user_active_buttons => $value){   //Updating user selected actions and position
                if($value == 'on'){
                    $data_insert[$user_active_buttons] = [
                        'active' => 1,
                    ];

                    $user_saved_position = $user_action_button->action_buttons[$user_active_buttons]['position'];

                    if(empty($user_saved_position)){
                        $data_insert[$user_active_buttons]['position'] = $temp_button_position;
                    }else{
                        $data_insert[$user_active_buttons]['position'] = $user_saved_position;
                    }

                    $temp_button_position ++ ;
                }
            }

        }

        if (($key = array_search('_token', $user_data_key)) !== false) {
            unset($user_data_key[$key]);
        }

        $all_action_buttons = config('dropfunnel_action_buttons.action_buttons');

        $user_not_active_buttons = array_diff($all_action_buttons, $user_data_key);

        $action_buttons_map = config('dropfunnel_action_buttons.action_buttons_map_to_db');
        $all_data_insert = [];

        if(!empty($user_not_active_buttons)){
            foreach($user_not_active_buttons as $button){
                $data_insert[$button] = [
                    'active' => 0,
                    'position' => null
                ];

                if($button != 'action_waiting_time'){
                    if(!empty($user_action_button[$action_buttons_map[$button]])){
                        $all_data_insert[$action_buttons_map[$button]] = null;
                    }
                }else{
                    // $all_data_insert['type'] = 'hours';
                    // $all_data_insert['value'] = '24';
                }
            }
        }

        $all_data_insert['action_buttons'] = $data_insert;

        if(!empty($all_data_insert)){
            DropfunnelStepMail::where('id', $id)->update($all_data_insert);
        }

        return redirect()->back()->with(['message' => 'Action Buttons Succesfunlly Added', 'message_type' => 'success']);
    }

    public function stepActionButtonsPositionUpdate()
    {
        $request = $_REQUEST;

        $campaign_id = $request['email_marketing_id'];

        $campaign = EmailMarketing::where('user_id', CRUDBooster::myParentId())->find($campaign_id);
        if(empty($campaign)) {
            return response()->json(['success' => false, 'message' => 'Invalid access!']);
        }

        if(isset($request['action_positin']) && !empty($request['action_positin']))
        {
            foreach ($request['action_positin'] as $step_id => $step_action_buttons) {

                $db_stored_acitons = DropfunnelStepMail::where('id', $step_id)->value('action_buttons');

                if($step_action_buttons){
                    $new_position = 1;
                    foreach($step_action_buttons as $key_name => $key_value){
                        $db_stored_acitons[$key_name]['position'] = $new_position;
                        $new_position ++ ;
                    }
                }

                DropfunnelStepMail::where('id', $step_id)->update([
                    'action_buttons' => $db_stored_acitons,
                ]);
            }
        }

        $html = $this->campaignStepsHtml($campaign_id);
        return response([
            'success' => true,
            'html' => $html,
            'message' => "Successfully Updated",
        ]);
    }
}
