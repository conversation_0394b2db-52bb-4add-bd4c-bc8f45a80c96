<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;

	use App\User;
	use App\NewOrder;
	use App\InvoicePayment;
	use App\Notifications\DRMNotification;

	class AdminInvoicePaymentsController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}
			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field         = "id";
			$this->limit               = "20";
			$this->orderby 			   = "id,desc";
			$this->show_numbering      = FALSE;
			$this->global_privilege    = FALSE;
			$this->button_table_action = true;
			$this->button_action_style = "icon";
			$this->button_add          = FALSE;
			$this->button_delete       = FALSE;
			$this->button_edit         = FALSE;
			$this->button_detail       = FALSE;
			$this->button_show         = FALSE;
			$this->button_filter       = FALSE;
			$this->button_export       = FALSE;
			$this->button_import       = FALSE;
			$this->button_bulk_action  = FALSE;
			$this->sidebar_mode		   = "normal"; //normal,mini,collapse,collapse-mini
			$this->table 			   = "invoice_payments";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = array("label"=>"ID","name"=>"id");
			$this->col[] = array("label"=>"Customer","name"=>"user_id","join"=>"cms_users,name");
			$this->col[] = array("label"=>"Invoice Number","name"=>"order_id","join"=>"new_orders,invoice_number");
			$this->col[] = array("label"=>"Amount","name"=>"order_id","join"=>"new_orders,total");
			$this->col[] = array("label"=>"Start date","name"=>"start_at");
			$this->col[] = array("label"=>"Paid at","name"=>"paid_at" );
			$this->col[] = array("label"=>"Status","name"=>"order_id" ,"join"=>"new_orders,status");
			$this->col[] = array("label"=>"Mail Send Count","name"=>"mail_send_count" );
			$this->col[] = array("label"=>"Payment Id","name"=>"payment_id");
			$this->col[] = array("label"=>"Collect Total","name"=>"total" );



			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
			//$this->form[] = ["label"=>"Order Id","name"=>"order_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"order,id"];
			//$this->form[] = ["label"=>"Paat","name"=>"paid_at","type"=>"datetime","required"=>TRUE,"validation"=>"required|date_format:Y-m-d H:i:s"];
			//$this->form[] = ["label"=>"Mail Send Count","name"=>"mail_send_count","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
			//$this->form[] = ["label"=>"Payment Id","name"=>"payment_id","type"=>"select2","required"=>TRUE,"validation"=>"required|min:1|max:255","datatable"=>"payment,id"];
			//$this->form[] = ["label"=>"Total","name"=>"total","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Sub Total","name"=>"sub_total","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Discount","name"=>"discount","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Response","name"=>"response","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
			//$this->form[] = ["label"=>"Error Log","name"=>"error_log","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();
	       	// $this->addaction[] = ['title'=>'Send', 'label' => 'Send', 'url'=>CRUDBooster::mainpath('send/[id]'), 'confirmation_text' => 'Are you sure to send!', "icon" => 'fa fa-envelope', 'confirmation' => true, 'color' => 'info', 'showIf'=>"(strtolower([status]) != 'paid')"];
	        // $this->addaction[] = ['title'=>'Mark as Paid', 'label' => 'Mark as Paid', 'url'=>CRUDBooster::mainpath('paid/[id]'), 'confirmation_text' => 'Are you sure to mark this paywall as Paid!', "icon" => 'fa fa-money', 'confirmation' => true, 'color' => 'danger', 'showIf'=>"(strtolower([status]) != 'paid')"];

	        if(CRUDBooster::isSuperadmin()){
	        	$this->addaction[] = ['title'=>'Payment', 'label' => 'Payment', 'url'=> CRUDBooster::adminPath('paywall-payment/[id]'), "icon" => 'fa fa-money', 'color' => 'info'];
	        }


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	$amount_col = 3;
	    	$status_col = 6;
	    	$amount_collect_col = 9;

	    	if($column_index == $amount_col){
	    		$column_value = number_format((float)$column_value, 2, ',', '.').formatCurrency('eur');
	    	}	    	

	    	if($column_index == $amount_collect_col){
	    		$column_value = number_format((float)$column_value, 2, ',', '.').formatCurrency('eur');
	    	}	    		    	

	    	if($column_index == $status_col){
	    		$color = in_array(strtolower($column_value), ['succeeded', 'paid'])? '#398439' : '#f39c12';
	    		$color = in_array(strtolower($column_value), ['over_due'])? '#ff0000' : $color;
	    		$color = in_array(strtolower($column_value), ['processing'])? '#00c0ef' : $color;
	    		$column_value = '<label class="btn btn-xs" style="background-color:'.$color.';color:#fff;">'.ucfirst(str_replace('_', ' ', $column_value)).'</label>';
	    	}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }


		//invoice stripe payment
		public function invoicePaymentSCA($purchase_data){
//			//DB::beginTransaction();
			try{

				$invoice = NewOrder::find($purchase_data['order_id']);
				if(is_null($invoice)) throw new \Exception("Invalid invoice charge!");
				$intend_id = $purchase_data['intend_id'] ?? null;

				// $discount = $purchase_data['discount']?? 0;
    //             $total = $purchase_data['total']?? 0;
    //             $sub_total = $purchase_data['sub_total']?? 0;

    //             $update_data = [];
    //             $update_data['payment_id'] = $purchase_data['id'];
    //             $update_data['total'] = $total;
    //             $update_data['sub_total'] = $sub_total;
    //             $update_data['discount'] = $discount;
    //             $update_data['paid_at'] = now();
    //             $update_data['status'] = 'paid';

    //             $update_data = array_filter($update_data);
                // $invoice->update($update_data);

                // DB::table('new_orders')->where('id', $invoice->id)->update(['status' => 'paid']);
                $message = ' (Payment by Stripe)';
				call_user_func('\App\Helper\OrderHelper' . '::callerOfTrigger', 'paid', $invoice, ['message' => $message, 'intend_id' => $intend_id]);

//				//DB::commit();    // Commiting  ==> There is no problem whatsoever
            	return ['success' => true, 'message' => 'Invoice charge payment successfully!'];
			}catch(\Exception $e){
//				//DB::rollBack();   // rollbacking  ==> Something went wrong
            	return ['success' => false, 'message' => $e->getMessage()];
			}
		}

	}
