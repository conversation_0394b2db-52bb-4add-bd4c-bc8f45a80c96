<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use Google\Cloud\Translate\V2\TranslateClient;
	use App\InvoiceTranslation;
	// use App\Helper\DRMInvoice;

	class AdminInvoiceTranslationsController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

	    	if(!CRUDBooster::isSuperAdmin() && !in_array(CRUDBooster::myID(), [71, 98])){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
	    	}

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = false;
			$this->button_bulk_action = false;
			$this->button_action_style = "button_icon";
			$this->button_add = false;
			$this->button_edit = false;
			$this->button_delete = false;
			$this->button_detail = false;
			$this->button_show = false;
			$this->button_filter = false;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "invoice_translations";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Lang","name"=>"lang"];
			$this->col[] = ["label"=>"Data","name"=>"data"];
			$this->col[] = ["label"=>"User Id","name"=>"user_id","join"=>"cms_users,id"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Lang','name'=>'lang','type'=>'select2','validation'=>'required','width'=>'col-sm-10','datatable'=>'tax_rates,country'];
			$this->form[] = ['label'=>'Data','name'=>'data','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'cms_users,name'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Lang","name"=>"lang","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Data","name"=>"data","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }

		public function getIndex() {
			if(!CRUDBooster::isSuperAdmin() && !in_array(CRUDBooster::myID(), [71, 98])){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
	    	}

		   //Create your own query 
		   $data = [];
		   $data['page_title'] = 'Invoice Languages';
		   $data['result'] = [];
		   $data['countries'] = DB::table('tax_rates')->join('invoice_translations', 'invoice_translations.tax_id', '=', 'tax_rates.id')->select('tax_rates.country', 'tax_rates.country_code', 'invoice_translations.id', 'tax_rates.lang_kod')->get();
		   $this->cbView('admin.invoice_translate.index',$data);
		}

		public function getDetail($id) {
		  	//Create an Auth
		  	if(!CRUDBooster::isSuperAdmin() && !in_array(CRUDBooster::myID(), [71, 98])){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
	    	}
			
			$data = [];
	    	$translation_data = InvoiceTranslation::find($id);
			$data['country'] = DB::table('tax_rates')->select('country', 'country_code', 'id', 'lang_kod')->find($translation_data->tax_id);
		  
		  	$data['page_title'] = 'Invoice Translation';
		  	$order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order($data['country']->country_code);
	        $data['order'] = $order;
	        $data['product_list'] = json_decode($order->cart);
	        $data['customer'] = (object)$order->customer;
	        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->orderBy('id', 'desc')->first();

		    $default = (object)config('inv_tr.DAILY');

			$data['LABEL'] = ($translation_data)? (object)$translation_data->data : $default;
	        $data['id'] = $id;
		  
		  	//Please use cbView method instead view method from laravel
		  	$this->cbView('admin.invoice_translate.detail',$data);
		}

		//Save translate
		public function postSaveTranslate() {
			try{
				if(!CRUDBooster::isSuperAdmin() && !in_array(CRUDBooster::myID(), [71, 98])){
		    		throw new \Exception(trans("crudbooster.denied_access"));
		    	}
				$request = request();

				$id = $request->id;
				$string_id = $request->string_id;
				$string_text = $request->string_text;

				$translation = InvoiceTranslation::find($id);
				if($translation){
					$data = $translation->data;
					$data[$string_id] = $string_text;
					if($translation->update(['data' => $data])){

						if( isset($request->filter) && (strpos($string_text, '[_]') !== false) ){
							$string_text = str_replace('[_]', $request->filter, $string_text);
						}

						return response()->json([
							'success'	=> true,
							'message'	=> 'Update success!',
							'text'		=> $string_text
						]);

					}else{
						throw new \Exception('Nothing changed!');
					}
				}
			}catch(\Exception $e){
				return response()->json([
					'success'	=> false,
					'message'	=> $e->getMessage()
				]);
			}
		}



	    //By the way, you can still create your own method in here... :)

	    public function getInvoiceTranslate() {
	    	if(!CRUDBooster::isSuperAdmin() && !in_array(CRUDBooster::myID(), [71, 98])){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
	    	}

	    	try{
	    		$current = null;

		        $translate = new TranslateClient(['key' =>'AIzaSyCXmhbZQ5pBqXjKgAKwYQVoXF7FHZEE9FA']);
		        $charCount = 0;

		        // $taxes = DB::table('tax_rates')->whereNotIn('country_code', ['usa', 'dk', 'ee', 'ie', 'lu', 'at', 'se', 'cz', 'gb', 'ch'])->select('country_code', 'id', 'lang_kod', 'country_code')->get()->toArray();

		        $taxes = DB::table('tax_rates')->whereNotNull('lang_kod')->select('country_code', 'id', 'lang_kod')->get()->toArray();


		        $source_data = config('inv_tr.GENERAL');
		        $source = 'de';

		        //->whereNotNull('lang_kod')

		        foreach ($taxes as $tax) {

		        	$tax_id = $tax->id;
		        	$target_lang = strtolower($tax->lang_kod);// $tax->lang_kod;

		        	$data = [];

		        	if($target_lang == 'de'){
		        		InvoiceTranslation::updateOrCreate(['tax_id' => $tax_id], ['data' => config('inv_tr.GENERAL'), 'lang' => $target_lang]);
		        	}else if ($target_lang == 'fr') {
		        		InvoiceTranslation::updateOrCreate(['tax_id' => $tax_id], ['data' => config('inv_tr.DAILY'), 'lang' => $target_lang]);
		        	}else{

			          	foreach ($source_data as $key => $text) {

							$current = [  $text , $tax_id, ['source'=> $source, 'target' => $target_lang] ];

			          		$charCount += strlen($text);
			          		if($charCount > 95000) { sleep(100); $charCount = 0; }


							$result = $translate->translate($text , ['target' => $target_lang]);


							$data[$key] = isset($result['text'])? $result['text'] : $text;
			          	}

						InvoiceTranslation::updateOrCreate(['tax_id' => $tax_id], ['data' => $data, 'lang' => $target_lang]);
		        	}		          	
		        }
	    	}catch(\Exception $e){

				dd($e->getMessage(), $current);        
			}

			dd('ok');
		}

		public function getPreview($country){
			if(!CRUDBooster::isSuperAdmin() && !in_array(CRUDBooster::myID(), [71, 98])){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
	    	}

			$data = [];
		  	$data['page_title'] = 'Invoice Preview';
		  	$order = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_fake_order($country);
	        $data['order'] = $order;
	        $data['product_list'] = json_decode($order->cart);
	        $data['customer'] = (object)$order->customer;
	        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id',CRUDBooster::myId())->orderBy('id', 'desc')->first();

	        $data['preview'] = true;

	        $pdf_view = (in_array($order->cms_user_id, [98, 2454, 2455, 2439]))? 'admin.invoice.daily' : 'admin.invoice.general';
            $pdf_view = ($order->insert_type == 4)? 'admin.invoice.charge_inv' : $pdf_view;

            $pdf=\PDF::loadView($pdf_view, $data)->setWarnings(false);
            return $pdf->stream('invoice.pdf');
		}
	}