<?php namespace App\Http\Controllers;

use App\Http\Resources\ExportRequestResource;
use App\Http\Resources\OrderSyncResource;
use App\Models\DroptiendaSyncHistory;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use GuzzleHttp\Client;
use Session;
use Request;
use DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use App\Http\Resources\DTSyncResource;
use App\TrackAutomagic;
use Carbon\Carbon;

class AdminJobsController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = false;
        $this->button_bulk_action = false;
// 			$this->button_action_style = "button_icon";
        $this->button_add = false;
        $this->button_edit = false;
        $this->button_delete = false;
        $this->button_detail = false;
        $this->button_show = false;
        $this->button_filter = false;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "jobs";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "Queue", "name" => "queue"];
        $this->col[] = ["label" => "Reserved At", "name" => "reserved_at"];
        $this->col[] = ["label" => "Available At", "name" => "available_at"];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'Queue', 'name' => 'queue', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Reserved At', 'name' => 'reserved_at', 'type' => 'number', 'validation' => 'required|integer|min:0', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Available At', 'name' => 'available_at', 'type' => 'number', 'validation' => 'required|integer|min:0', 'width' => 'col-sm-10'];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ["label"=>"Queue","name"=>"queue","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Payload","name"=>"payload","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Attempts","name"=>"attempts","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Reserved At","name"=>"reserved_at","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
        //$this->form[] = ["label"=>"Available At","name"=>"available_at","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }

    public function retryOrderSync()
    {
        try {
            $id = $_REQUEST['id'];
            $failed_sync = DB::table('new_order_sync_reports')->where(['id' => $id, 'status' => 2, 'retry' => 0])->select('shop_id', 'id')->first();

            if ($failed_sync && $failed_sync->shop_id) {
                $curl = curl_init('http://165.22.24.129/sync/' . $failed_sync->shop_id);
                // Returns the data/output as a string instead of raw data
                curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

                curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
                // get stringified data/output. See CURLOPT_RETURNTRANSFER
                $data = curl_exec($curl);
                // get info about the request
                $info = curl_getinfo($curl);
                // close curl resource to free up system resources
                $responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
                curl_close($curl);
                if ($responseCode == 401) {
                    throw new \Exception("Something went wrong!");
                }

                if ($responseCode != 200) {
                    throw new \Exception("Process failed!");
                }
                return response()->json(json_decode($data));
            } else {
                throw new \Exception("This process might be running!");
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getIndex()
    {
        if (CRUDBooster::isSuperAdmin()) {
            $data['url'] = url('/horizon/dashboard');
            $this->cbView('admin.admin_background_processes_page', $data);
        } elseif (CRUDBooster::myPrivilegeName() == 'Customer' || CRUDBooster::isSupportAccount()) {
            $data['automagic_processes'] = DB::table('track_automagic')
                ->select('track_automagic.created_at')
                ->addSelect('drm_imports.csv_file_name')
                ->join('drm_imports', 'drm_imports.id', '=', 'track_automagic.import_id')
                ->where('track_automagic.user_id', CRUDBooster::myParentId())
                ->groupBy('track_automagic.import_id')->get();


            $cp_exports = DB::table('cp_export_request')->whereIn('user_id', [CRUDBooster::myParentId(), CRUDBooster::myId()])->selectRaw("id,url,last_error,status,ebay_synced,amazon_synced,google_synced,created_at")->orderBy('id', 'desc')->get()
            ->map(function($item) {

                $progress = (int)($item->ebay_synced + $item->amazon_synced + $item->google_synced);
                $progress *= 20;

                if($status  < 10)
                {
                    $progress += (int)$item->status * 5;
                }

                if($item->status == 5)
                {
                    $progress = 100;
                }

                $progress = $progress > 100 ? 100 : $progress;

                return [
                    'id' => $item->id,
                    'url' => $item->url,
                    'progress' => $progress,
                    'last_error' => $item->last_error,
                    'time' => $item->created_at,
                ];
            });
            $data['page_title'] = __('Background Processes');
            $data['cp_exports'] = $cp_exports;

            $comparison_exports = DB::table('comparison_export_requests')->where('user_id', '=', CRUDBooster::myParentId())->selectRaw("id,url,last_error,created_at")->orderBy('id', 'desc')->get()
            ->map(function($item) {

                $progress = $item->url ? 100 : 50;

                return [
                    'id' => $item->id,
                    'url' => $item->url,
                    'progress' => $progress,
                    'last_error' => $item->last_error,
                    'time' => $item->created_at,
                ];
            });

            $data['comparison_exports'] = $comparison_exports;

            $keepa_bestseller_exports = DB::table('keepa_bestseller_export')->where('user_id', '=', CRUDBooster::myParentId())->selectRaw("id,url,last_error,created_at")->orderBy('id', 'desc')->get()
            ->map(function($item) {

                $progress = $item->url ? 100 : 50;

                return [
                    'id' => $item->id,
                    'url' => $item->url,
                    'progress' => $progress,
                    'last_error' => $item->last_error,
                    'time' => $item->created_at,
                ];
            });

            $data['keepa_bestseller_exports'] = $keepa_bestseller_exports;

            $decathlon_exports = DB::table('decathlon_export_requests')->where('user_id', '=', CRUDBooster::myParentId())->selectRaw("id,url,last_error,created_at")->orderBy('id', 'desc')->get()
            ->map(function($item) {

                $progress = $item->url ? 100 : 50;

                return [
                    'id' => $item->id,
                    'url' => $item->url,
                    'progress' => $progress,
                    'last_error' => $item->last_error,
                    'time' => $item->created_at,
                ];
            });

            $data['decathlon_exports'] = $decathlon_exports;

            $customer_exports = DB::table('customer_data_export_requests')->where('user_id', '=', CRUDBooster::myParentId())->selectRaw("id,url,last_error,created_at")->orderBy('id', 'desc')->get()
            ->map(function($item) {

                $progress = $item->url ? 100 : 50;

                return [
                    'id' => $item->id,
                    'url' => $item->url,
                    'progress' => $progress,
                    'last_error' => $item->last_error,
                    'time' => $item->created_at,
                ];
            });

            $data['customer_exports'] = $customer_exports;

            return view('admin.customer_background_processes_page', $data);
        }
    }


    public function getSyncOrderData(): ?\Illuminate\Http\Resources\Json\AnonymousResourceCollection
    {
        $sync_order = null;

        if (CRUDBooster::isSuperAdmin()) {
            $sync_order = DB::table('new_order_sync_reports')->join('shops', 'shops.id', 'new_order_sync_reports.shop_id')->orderBy('new_order_sync_reports.end_time', 'DESC')->select('shops.shop_name', 'shops.id as shopId', 'new_order_sync_reports.*', 'shops.user_id')->get();
        } else {
            $sync_order = DB::table('new_order_sync_reports')->join('shops', 'shops.id', 'new_order_sync_reports.shop_id')->orderBy('new_order_sync_reports.end_time', 'DESC')->where('shops.user_id', CRUDBooster::myParentId())->select('shops.shop_name', 'shops.id as shopId', 'new_order_sync_reports.*', 'shops.user_id as user_id')->get();
        }
        return ($sync_order) ? OrderSyncResource::collection($sync_order) : null;
    }

    public function getDTProgress(): ?\Illuminate\Http\Resources\Json\AnonymousResourceCollection
    {
        $user_id = CRUDBooster::myParentId();
        $histories = DroptiendaSyncHistory::where([
            'user_id' => $user_id
        ])->whereDate('created_at', '>=', \Carbon\Carbon::yesterday())
            ->orderBy('id','desc')->get();
        return ($histories) ? DTSyncResource::collection($histories) : null;
    }

    public function getExportProgress(\Illuminate\Http\Request $request): ?\Illuminate\Http\Resources\Json\AnonymousResourceCollection
    {
        $user_id = CRUDBooster::myParentId();

        $url = config('export.base_url').'/api/v1/requests?channel='.$request->channel.'&merchant_id='.$user_id;
        $client = new Client();
        $response = $client->get($url);
        $res = json_decode($response->getBody());
        return ExportRequestResource::collection($res->data);
    }

    //By the way, you can still create your own method in here... :)


    //PDF archive jobs
    public function getOrderArchiveProgress()
    {
        try {

            $url = 'http://*************/api/archive-progress';
            // $url = 'http://localhost:8005/api/archive-progress';


            $curl = curl_init($url);
            // Returns the data/output as a string instead of raw data
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
            // get stringified data/output. See CURLOPT_RETURNTRANSFER
            $data = curl_exec($curl);
            // get info about the request
            $info = curl_getinfo($curl);
            // close curl resource to free up system resources
            $responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);
            if ($responseCode == 401) {
                throw new \Exception("Something went wrong!");
            }

            if ($responseCode != 200) {
                throw new \Exception("Process failed!");
            }
            return response()->json(json_decode($data));


        } catch (\Exception $e) {
        }


    }


}
