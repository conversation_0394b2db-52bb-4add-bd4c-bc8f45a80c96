<?php namespace App\Http\Controllers;

use App\Notifications\DRMTelegramNotification;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use App\User;
    use App\Notifications\DRMNotification;
    use Illuminate\Support\Facades\Storage;
    use Illuminate\Support\Facades\Validator;
    use Illuminate\Support\Str;

	class AdminManualNotificationController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "title";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = false;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "manual_notification";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Title","name"=>"title"];
			$this->col[] = ["label"=>"Description","name"=>"description"];
			if($this->superPermission()){
			    $this->col[] = ["label"=>"Publish","name"=>"publish"];
			    // $this->col[] = ["label"=>"Discord", "name"=>"discord_history"];
			}

			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Title','name'=>'title','type'=>'text','validation'=>'required|string|min:3|max:70','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
			$this->form[] = ['label'=>'Description','name'=>'description','type'=>'wysiwyg','validation'=>'required|string|min:5','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Publish','name'=>'publish','type'=>'checkbox', 'width'=>'col-sm-10', 'dataenum'=>"1|published or not"];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Title","name"=>"title","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
			//$this->form[] = ["label"=>"Description","name"=>"description","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
			//$this->form[] = ["label"=>"Publish","name"=>"publish","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();
	        if($this->superPermission()){
	        	$this->addaction[] = ['label' => 'Send to Mobile!', 'url' => CRUDBooster::mainpath('send-to-mobile/[id]'), 'icon' => 'fa fa-mobile', 'title' => 'Send to Discord', 'color' => 'info'];
	        	// $this->addaction[] = ['label' => 'Send to discord!', 'url' => "javascript:sendToDiscordManual([id])", 'icon' => 'fa fa-share', 'title' => 'Send to Discord', 'color' => 'info'];
	    	}


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = "
	        function sendToDiscordManual(id){
				$.ajax({
					url: ADMIN_PATH+'/manual_notification/discord-preview/'+id,
					type: 'POST',
					dataType: 'json',
					beforeSend: function(){
				      swal({
				        title: 'Loading...',
				        imageUrl: window.ASSET_URL+ 'images/loading.gif',
				        showConfirmButton: false,
				        allowOutsideClick: false,
				        confirm: true,
				        showLoaderOnConfirm: true
				      })
					},
					success: function (response) {
						if(response.success && response.html){
							$('#myModal').html(response.html).modal('show');
                            discordImageElement(id, response.image_src || []);
							$('#discord_message_content').focus();
							$('#discord_message_content').summernote();
						}
					},
					complete: function(){
						swal.close()
					}
				});
	        }


	        //Image upload dropzone
	        function discordImageElement(id, mocks = []){
                Dropzone.autoDiscover = false;
                let discordDropzone = new Dropzone('#discordDropzoneUpload', {
                    url: ADMIN_PATH+'/manual_notification/send-message-to-discord',
                    autoProcessQueue: false,
                    uploadMultiple: true,
                    parallelUploads: 5,
                    maxFiles: 5,
                    maxFilesize: 5,
                    acceptedFiles: 'image/*',
                    addRemoveLinks: true,
                    dictDefaultMessage: '',
                    init: function(){

                        //Load moc files
                        if(mocks.length){
                        	let tmp_discord_file_id = 1;
                        	for(let moc of mocks){
                                let mockFile = { name: 'image', size: 1024, accepted: true, url: moc, id: (id + tmp_discord_file_id)};
                                this.files.push(mockFile);
                                this.emit('addedfile', mockFile);
                                this.options.thumbnail.call(this, mockFile, moc);
                                ++tmp_discord_file_id;
                        	}
                            $('#discordFileuploadlabel').hide();
                            $($('.dz-button')[0]).text('');
                        }
                    },
                    error: function(file, response){
                        if(response.errors){
                            toastr.error(response.message);
                            $.each(response.errors, function(i, item) {
                                $('#'+i).removeClass('input-error').addClass('input-error');
                                toastr.error(item);
                            });
                        }else{
                            toastr.error(response);
                        }
                        file.previewElement.remove();
                    },
                    success: function(file, response){

                    	console.log(response)
                    }
                });

                //add form data on queue
                discordDropzone.on('sendingmultiple', function(data, xhr, formData) {
                  for(let inp of $('#discord_message_form').serializeArray()){
                    formData.append(inp.name, inp.value);
                  }
                });

                //Add file event
                discordDropzone.on('addedfile',function(file){
		          $('#file.file-error').removeClass('file-error');
		          $('#discordFileuploadlabel').hide();
		        });

		        //Remove file event
		        discordDropzone.on('removedfile',function(data){
		          if(data.id){
		          let rm_f_id = data.id;
		            if(rm_f_id.includes('acc_cache_file')){
		              $('#'+rm_f_id).remove();
		              $('#preview_'+rm_f_id).show();
		              $('#preview_'+rm_f_id).removeClass('cache_search_item').addClass('cache_search_item');
		            }
		          }

		          if(discordDropzone.files.length == 0){
		            $('#file').removeClass('file-error').addClass('file-error');
		            $('#discordFileuploadlabel').show();
		          }
		        });


			    //Click discord message btn
		        $(document).on('click', 'a#send_discord_message_btn', function(e){
		        	e.preventDefault();

		        	let message = $('#discord_message_content').val() || '';
		        	message = $.trim(message);

		        	let channels = [];

		        	let id = $(this).data('id');
		        	if(!id){
			        	toastr.info('Invalid action!');
			        	return;
			        }

		        	let selected_channel = [];
			        $('input.discord_message_client_id:checked').each(function(i){
			          selected_channel[i] = $(this).val();
			          channels[i] = $(this).attr('title');
			        });

			        if(!selected_channel.length){
			        	toastr.info('Please select at least one connection!');
			        	return;
			        }

			        if(!message.length || message == ''){
			        	toastr.info('Message can not be empty!');
			        	$('#discord_message_content').focus();
			        	return;
			        }

			        let channel_info = channels.toString();
			        let confirmation = 'Are you sure to post messages on Discord: '+channel_info+' connection!';


			        swal({
		                title: 'Hi',
		                text: confirmation,
		                type: 'info',
		                showCancelButton: true,
		                confirmButtonColor: '#00a65a',
		                confirmButtonText: 'Yes',
		                cancelButtonText: 'No',
		                closeOnConfirm: false
		            }, function () {

	                    let is_file_selected = discordDropzone.getQueuedFiles().length;
	                    let has_old_file = (discordDropzone.files.length - is_file_selected);

	                    //add old files
	                    if(discordDropzone.files.length){
	                    	let hidden_files_el = '';
	                    	let hidden_image_id = 1;
		                  	for(let old_file of discordDropzone.files){
		                  		if(old_file.url){
		                  			hidden_files_el += '<input type=\"hidden\" value=\"'+old_file.url+'\" name=\"images[]\">';
		                  		}

		                  		if(old_file.dataURL){
		                  			let data_images_el = `<input type=\"hidden\" value=\"`+old_file.dataURL+`\" name=\"data_images[`+hidden_image_id+`][data]\"><input type=\"hidden\" value=\"`+old_file.name+`\" name=\"data_images[`+hidden_image_id+`][file_name]\">`;
		                  			hidden_files_el += data_images_el;
		                  		}

		                  		++hidden_image_id;
		                  	}
		                  	$('#discord_hidden_images').html(hidden_files_el)
	                    }

	                    postDiscordMessage();
		            })
		        })
	        }


	        function postDiscordMessage(){
				$.ajax({
					url: ADMIN_PATH+'/manual_notification/send-message-to-discord',
					type: 'POST',
					dataType: 'json',
					data: $('#discord_message_form').serializeArray(),
					beforeSend: function(){
				      swal({
				        title: 'Loading...',
				        imageUrl: window.ASSET_URL+ 'images/loading.gif',
				        showConfirmButton: false,
				        allowOutsideClick: false,
				        confirm: true,
				        showLoaderOnConfirm: true
				      })
					},
					success: function (response) {
						console.log(response)

						if(response.success && response.data){
							const response_data = response.data;
							if(!response_data.length){
								console.log('sorry!')
							}
	                        $.each(response_data, function(i, v) {
	                        	let channel_el_id = '.discord_message_client_id#discord_'+v.channel_id;
	                        	let channel_el_date_id = '#discord_'+v.channel_id+'_date';
	                        	if(v.success){
	                        		toastr.success(v.message);
	                        		$(channel_el_id).parent('label').css({'color' : 'green'})
	                        		$(channel_el_date_id).text(v.time);
	                        	}else{
	                        		toastr.warning(v.message);
	                        		$(channel_el_id).parent('label').css({'color' : 'red'})
	                        	}
	                        });
						}
					},
					error: function(err){
						if(err.responseJSON && err.responseJSON.message){
							toastr.warning(err.responseJSON.message);
						}
					},
					complete: function(){
						swal.close()
					}
				});
	        }
	        ";


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();
	        if($this->superPermission()){
                $this->load_js[] = 'https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.7.0/dropzone.min.js';
            }



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here
	        if(!$this->superPermission()){
				$query->where('publish','=', 1);
			}

			$totalnotification = DB::table('notifications')->where('notifiable_id',CRUDBooster::myId())->whereNull('read_at')->get();

            if($totalnotification!=0){
	            foreach($totalnotification as $notification){
	                $hook=json_decode($notification->data,true);
	                if('MENUNOTIFICATIONADD' == $hook['hook']){
	                	$query->where('created_at','>=',\Carbon\Carbon::parse($notification->created_at)->format('Y-m-d'));
	                	DB::table('notifications')->where('notifiable_id',CRUDBooster::myId())->where('id',$notification->id)->update(['read_at'=>now()]);
	                }
	            }
            }


	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    	if($column_index==2){
    	        $column_value= Str::limit(strip_tags($column_value),100,' .....');
    	    }

	    	if($this->superPermission()){
    	    	if($column_index==3){
    	    	    if($column_value==1){
    	    	        $column_value='Enable';
    	    	    }else{
        	    	    $column_value='Disable';
        	    	}
    	    	}

    	    	// if($column_index == 4){

				// 	//Calculate last send history
				// 	$history_arr = @json_decode($column_value, true) ?? [];
				// 	$tokens = config('discord.tokens', []);

				// 	$last_history_value = '';
				// 	if($tokens){
				// 		foreach($tokens as $key_id => $active_token){
				// 			$h = collect($history_arr)->where('success')->firstWhere('channel_id', $key_id);
				// 			if(!empty($h)){
				// 				$last_history_value .= '<span class="label label-success label-xs" title="'.$active_token['account_name'].' - '.$h['time'].'">'.$active_token['account_name'].'</span> ';
				// 			}
				// 		}
				// 	}
				// 	//history canculation end
				// 	$column_value = $last_history_value;
    	    	// }
	    	}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here
	        if($postdata['publish']!=1){
				$postdata['publish']=0;
			};

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

           // $message=DB::table('notification_trigger')->where('hook','MENUNOTIFICATIONADD')->where('status',0)->first();
	       // $url = CRUDBooster::adminPath('manual_notification');

	       // if($message){
	       //     User::find(Crudbooster::myId())->notify(new DRMNotification($message->title, 'MENUNOTIFICATIONADD', $url));
	       // }

	        $url = CRUDBooster::adminPath('manual_notification');
	        // $message=DB::table('notification_trigger')->where('hook','MENUNOTIFICATIONADD')->where('status',0)->first();
	        // if (!empty($message)) {
	        	$message_title = 'Notification Added Successfully';
	    //     	if (isHookRemainOnSidebar('MENUNOTIFICATIONADD') && isLocal()) {
					// User::find(Crudbooster::myId())->notify(new DRMTelegramNotification($message_title, 'MENUNOTIFICATIONADD', $url));
	    //     	}else{
					User::find(CRUDBooster::myId())->notify(new DRMNotification($message_title, 'MENUNOTIFICATIONADD', $url));
	        	// }
	        // }
	    }

	    public function getSendToMobile($id)
	    {
	    	$notification = DB::table('manual_notification')
	    	->where('id', $id)
	    	->first();

	    	if(blank($notification)) return CRUDBooster::redirectBack('Access denied', 'error');

	    	$title = Str::limit(strip_tags($notification->title), 20);
	    	$description = Str::limit(strip_tags($notification->description), 40);

	    	DB::table('personal_access_tokens')
	    	->orderBy('id')
	    	->chunk(10, function ($chunk) use ($title, $description, $id) {
	    		$chunk->each(function($token) use ($title, $description, $id) {
	    			(new \App\Services\Notification\PushLatestNews)->send($token->tokenable_id, $id, $title, $description);
	    		});
	    	});

	    	return CRUDBooster::redirectBack('Notification send to mobile successfully', 'success');
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }

	    public function getDetail($id){
			if($this->superPermission()){
				$data['detail'] = DB::table('manual_notification')
				->where('id', $id)
				->first();
			}else{
				$data['detail'] = DB::table('manual_notification')
				->where('publish','=', 1)
				->where('id', $id)
				->first();
			}


			return view('admin.manual_notification.details_manual_notification', $data);
		}



	    //Get Discord Send preview
	    public function postDiscordPreview($id)
	    {
	    	try{

		    	if(!$this->superPermission()){
				    throw new \Exception('Access denied!');
				}

				$notification =  DB::table('manual_notification')->where('id', '=', $id)->select('description', 'discord_history')->first();

				$description = $notification->description ?? '';


                $doc = new \DOMDocument();
                @$doc->loadHTML($description);
                $images = $doc->getElementsByTagName('img') ?? [];


                $image_src = [];
                // Find all images
                foreach($images as $k => $image){
                    $src = $image->getAttribute('src');
                    if(!empty($src)){
                        $image_src[] = $src;
                    }
                }

				$body = $description;

				$tokens = config('discord.tokens', []);



				//Calculate last send history
				$history = $notification->discord_history ?? null;
				$history_arr = @json_decode($history, true) ?? [];


				$last_history = [];
				$active_tokens = array_keys($tokens);
				if($active_tokens){
					foreach($active_tokens as $active_token){
						$h = collect($history_arr)->where('success')->firstWhere('channel_id', $active_token);
						if(!empty($h)){
							$last_history[$active_token] = $h['time'];
						}
					}
				}
				//history canculation end


				$token_field = '<br><hr>';
				if(!empty($tokens)){
					$option_html = 'Select Discord connections:';
					foreach ($tokens as $key => $token) {
						$last_send = isset($last_history[$key])? 'Last send: '.$last_history[$key] : '';
						$option_html .= '<li><label><input type="checkbox" name="discord_message_client_id[]" id="discord_'.$token['account_id'].'" class="discord_message_client_id" title="'.$token['account_name'].'" value="'.$token['account_id'].'"> '.$token['account_name'].'</label> <small id="discord_'.$token['account_id'].'_date">'.$last_send.'</small></li>';
					}
					$token_field .= '<ul class="nav">'.$option_html.'</ul>';
				}

				$html = '
				<style>
				textarea#discord_message_content{
		        	max-width: 100%;
				    min-width: 100%;
				    min-height: 220px!important;
	        	}

				.drug-drop-status h2 {
				    font-size: 18px;
				    line-height: 20px;
				    font-weight: 500;
				    display: block;
				}

				.drug-drop-status p {
				    font-size: 14px;
				    font-weight: 300;
				    line-height: 13px;
				}

				.dd-s-i-icon i {
				    font-size: 56px;
				    color: #c4c1c1;
				    display: block;
				}

				.dd-s-i-text p {
				    font-size: 14px;
				    letter-spacing: 0;
				    line-height: 17px;
				    margin-top: 7px;
				}
				.dd-s-item {
				    margin-top: 18px;
				}

				.drug-drop-section {
				    position: relative;
				    border: 1px dotted #c4c1c1;
				    border-width: 2px;
				    border-radius: 10px;
				    cursor: pointer;
				    padding: 5px;
				}


				.dz-default.dz-message {
				    position: relative;
				    width: 100%;
				    height: 100%;
				}

				button.dz-button {position: absolute;content: "";left: 0;width: 100%; min-height: 181px;background: none;border: none;}

				div#discordDropzoneUpload {
				    padding: 0 30px;
				}

				.dz-preview.dz-image-preview {
				    width: 160px;
				    display: inline-block;
				    margin: 5px 0;
				    overflow: hidden;
				}

				.dz-image img {
				    width: 125px;
				    height: 125px;
				}

				button.dz-button:focus {
				    outline: 0;
				    border: none;
				}

				.dz-size {
				}

				.dz-progress {
				    display: none;
				}

				.dz-error-message {
				    display: none;
				}

				.dz-success-mark {
				    display: none;
				}

				.dz-error-mark {
				    display: none;
				}

				.dz-filename {
				    display: none;
				}
				</style>

				<div class="modal-dialog modal-lg">
				  <!-- Modal content-->
				  <div class="modal-content">
				    <div class="modal-header">
				      <h5 class="modal-title" id="myModallLabel">Discored - Preview</h5>
				    </div>
				    <div class="modal-body">
                        <form id="discord_message_form">
                          <input type="hidden" name="_token" value="'.csrf_token().'">
                          <input type="hidden" name="id" value="'.$id.'">
                          <div class="row">

                            <div class="col-md-12">
                                <label for="discord_message_content">Content</label>
                                <textarea id="discord_message_content" name="discord_message_content" class="form-control">'.$body.'</textarea>
                            </div>

                            <div id="discord_hidden_images"></div>

                            <div class="col-md-12">
                            <br>
						      <div class="drug-drop-section">
						        <div class="row ">
						          <div class="col-md-12 dz-message" >
						            <div class="drug-drop-status text-center">
						              <h2>Upload image</h2>
						              <p>JPEF, PNG, 5MB Max</p>
						            </div>
						          </div>
						        <div id="discordFileuploadlabel">
						          <div class="col-md-4 dz-message">
						            <div class="dd-s-item text-center">
						              <div class="dd-s-i-icon">
						                <i class="fa fa-file-image-o" aria-hidden="true"></i>
						              </div>
						              <div class="dd-s-i-text">
						                <!-- <p>Belege hierher ziehen</p> -->
						              </div>
						            </div>
						          </div>
						          <div  class="col-md-4 dz-message">
						            <div class="dd-s-item text-center">
						              <div class="dd-s-i-icon">
						                <i class="fa fa-cloud-upload" aria-hidden="true"></i>
						              </div>
						              <div  class="dd-s-i-text">
						                <p>Click / Drag file here</p>
						              </div>
						            </div>
						          </div>
						          <div class="col-md-4 dz-message">
						            <div class="dd-s-item text-center">
						              <div class="dd-s-i-icon">
						                <i class="fa fa-files-o" aria-hidden="true"></i>
						              </div>
						              <div class="dd-s-i-text">

						              </div>
						            </div>
						          </div>
						        </div>
						          <div class="dropzone" id="discordDropzoneUpload"></div>
						        </div>
						      </div>
                            </div>

                            <div class="col-md-12">
                            '.$token_field.'
                            </div>

                          </div>

                        </form>
				    </div>
				    <div class="modal-footer">
				    	<a href="#" class="btn btn-drm" id="send_discord_message_btn" data-id="'.$id.'"><i class="fa fa-share"></i> Send to Discord</a>
				      	<button type="button" class="btn btn-danger" data-dismiss="modal"><i class="fa fa-close" ></i></button>
				    </div>
				  </div>
				</div>';

				return response()->json([
					'success' => true,
					'image_src' => $image_src,
					'html' => $html
				], 200);

	    	}catch(\Exception $e){
				return response()->json([
					'success' => false,
					'message' => $e->getMessage()
				], 403);
	    	}

	    }


	    //Send message to discord
	    public function postSendMessageToDiscord()
	    {
	    	$temp_paths = [];

	        Validator::make($_REQUEST, [
	            'discord_message_content' => 'required',
	            'discord_message_client_id' => 'required|array|min:1',
	            'images' => 'nullable|array',
	            'data_images' => 'nullable|array',
	        ],
	        [
	            'discord_message_content.required' => 'Content can\'t be empty!',
	            'discord_message_client_id.required' => 'Please select at least one channel!',
	        ])->validate();

	    	try{

	    		if(!$this->superPermission()){
				    throw new \Exception('Access denied!');
				}

	    		$message = $_REQUEST['discord_message_content'];
	    		$channels = $_REQUEST['discord_message_client_id'];
	    		$id = $_REQUEST['id'];


	    		//Images
	    		$images = []; //$_REQUEST['images'] ?? [];

	    		$raw_images_url = $_REQUEST['images'] ?? [];

	    		if(empty($message)) throw new \Exception('Content can\'t be empty!');
	    		if(empty($channels)) throw new \Exception('Channel can\'t be empty!');

				$tokens = config('discord.tokens', []);
				if(empty($tokens)) throw new \Exception('Tokens can\'t be empty!');

				if(empty($id)) throw new \Exception('Invalid action!');
				$notification = DB::table('manual_notification')->where('id', '=', $id)->select('discord_history', 'id', 'description')->first();
				if(empty($notification) || empty($notification->id)) throw new \Exception('Invalid action!');


				$data_images = $_REQUEST['data_images'] ?? [];
				if(!empty($data_images)){
					foreach($data_images as $k => $data_image){

						$image = $data_image['data'];
						if (!is_null($image) && preg_match('/data:image/', $image)){
						    list($type, $image) = explode(';', $image);
						    list(, $image)      = explode(',', $image);
						    $base64Image = base64_decode($image);

		 					$rand = Str::random(40);
		 					$file_path = 'discord_notification/'.$id.'/'.$rand.$k.'.png';
		 					Storage::disk('spaces')->put($file_path, $base64Image, 'public');
		 					if(Storage::disk('spaces')->exists($file_path)){
		 						$images[] = Storage::disk('spaces')->url($file_path);
		 						$temp_paths[] = $file_path;
		 					}
						}
					}
				}

				if(!empty($raw_images_url)){
					foreach($raw_images_url as $k => $raw_url){
						//If same domain
						if (strpos($raw_url, url('/')) !== false) {
						    $path = array_pop(explode(url('/'), $raw_url));
						    $ext = pathinfo($path, PATHINFO_EXTENSION);
						    $rand = Str::random(40);
		 					$file_path = 'discord_notification/'.$id.'/'.$rand.$k.'.'.$ext;
		 					Storage::disk('spaces')->put($file_path, Storage::get($path), 'public');
		 					if(Storage::disk('spaces')->exists($file_path)){
		 						$images[] = Storage::disk('spaces')->url($file_path);
		 						$temp_paths[] = $file_path;
		 					}
						}else{
							$images[] = $raw_url;
						}
					}
				}

				$response = [];
				foreach ($channels as $channel) {
					$token = $tokens[$channel];
					if($token){
				    	$response[] = $this->sendToDoscord($message, $token, $images);
					}
				}

				if(empty($response)) throw new \Exception('Something went wrong!');

				try{
					$history = $response;
					$old_history = $notification->discord_history;
					$old_history_arr = @json_decode($old_history, true);
					if(!empty($old_history_arr) && is_array($old_history_arr)){
						$history = collect($history)->concat($old_history_arr);
					}
					DB::table('manual_notification')->where('id', '=', $id)->update(['discord_history' => json_encode($history)]);
				}catch(\Exception $ex){}


				return response()->json(['success' => true, 'data' => $response], 200);


	    	}catch(\Exception $e){
	    		response()->message([
	    			'success' => false,
	    			'message' => $e->getMessage()
	    		], 403);
	    	}
	    }



	    private function sendToDoscord($message, $token, $images = [])
	    {
	    	try{

	    		$image_data = collect($images)->map(function($image){
	    			return [
	    				"image" => [
							"url" => $image
						],
    				];
	    		})
	    		->toArray();

	    		$send_data = [
		    		// Message
					"content" => $message,

					// Text-to-speech
					"tts" => false,

					"embeds" => $image_data
				];

	    		$content_data = json_encode($send_data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

		    	$ch = curl_init( $token['webhook'] );
				curl_setopt( $ch, CURLOPT_HTTPHEADER, array('Content-type: application/json'));
				curl_setopt( $ch, CURLOPT_POST, 1);
				curl_setopt( $ch, CURLOPT_POSTFIELDS, $content_data);
				curl_setopt( $ch, CURLOPT_FOLLOWLOCATION, 1);
				curl_setopt( $ch, CURLOPT_HEADER, 0);
				curl_setopt( $ch, CURLOPT_RETURNTRANSFER, 1);

	            $response = curl_exec($ch);
	            $err = curl_error($ch);
	            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	            curl_close($ch);
	            $res = json_decode($response, true);

	            $res_message = @$res['message'];

	            if($http_code == 204){

	            	return [
	            		'success' => true,
	            		'message' => 'Message sent to '.$token['account_name'],
	            		'channel_id' => $token['account_id'],
	            		'status_code' => $http_code,
	            		'time' => now(),
	            	];
	            }

	            $err_message = empty($err)? $res_message : $err;

	            if(empty($err_message)){
	            	$err_message = 'Something went wrong.';
	            }
	            $err_message = 'Message send failed. Channel: '.$token['account_name'].'. Error: '.$err_message;
	            throw new \Exception($err_message, $http_code);

	    	}catch(\Exception $e){
    			return [
            		'success' => false,
            		'message' => $e->getMessage(),
            		'channel_id' => $token['account_id'],
            		'status_code' => $e->getCode(),
            		'time' => now()
            	];
	    	}
	    }


	    private function superPermission()
	    {
	    	return CRUDBooster::isSuperAdmin() || CRUDBooster::isDropmatixSupport() || CRUDBooster::isDroptiendaSupport();
	    }
	}
