<?php

namespace App\Http\Controllers;

use App\DropFunnelSignature;
use App\EmailMarketing;
use App\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Storage;

class AdminMarketingPlanController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {

//        if (!CRUDBooster::isSuperadmin()) {
//            $app_id = config('global.marketing_plan_app_id');
//            if (!DrmUserHasPurchasedApp(CRUDBooster::myParentId(), $app_id)) {
//                CRUDBooster::redirect(CRUDBooster::adminPath('app-list'), 'You must purchase Funnel Planner app.');
//            }
//        }

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = true;
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = false;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "marketing_plan";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
//        $this->col[] = ["label" => "User Id", "name" => "cms_users.name", "join" => "cms_users,id"];
        $this->col[] = ["label" => "Plan Name", "name" => "plan_name"];
        $this->col[] = ["label" => "Image", "name" => "marketing_plan.image", "image" => true];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'User Id', 'name' => 'user_id', 'type' => 'select2', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'datatable' => 'user,id'];
        $this->form[] = ['label' => 'Image', 'name' => 'image', 'type' => 'upload', 'validation' => 'required|image|max:3000', 'width' => 'col-sm-10', 'help' => 'File types support : JPG, JPEG, PNG, GIF, BMP'];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'user,id'];
        //$this->form[] = ['label'=>'Image','name'=>'image','type'=>'upload','validation'=>'required|image|max:3000','width'=>'col-sm-10','help'=>'File types support : JPG, JPEG, PNG, GIF, BMP'];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();
        $this->addaction[] = ['label' => 'Share', 'url' => 'javascript:drm_plan_share([id])', 'color' => 'info'];


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = "
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name=\"csrf-token\"]').attr('content')
                }
            });
            $('#signature').css({backgroundColor: '#00c0ef', border : '1px solid #00c0ef'});
            let checkedValue = [];
            $(document).on('click','.shareCheck',function(){
                if ($(this).children('input').is(':checked')) {
                    let val = $(this).children('input').val();
                    let index = checkedValue.indexOf(val);
                    if (index < 0) {
                        checkedValue.push(val);
                    } else {
                        checkedValue.splice(index, 1);
                    }
                }
            });

            $(document).on('click','.plan-share-with-user',function() {
                let id = $(this).data('id');
                $.ajax({
                    method: 'post',
                    url: '" . route('share-plan-with-user') . "',
                    data: {id:id,user_ids:checkedValue},
                    success:function(response) {
                        $('#testMailForEmailMarketing').modal('hide');
                        if (response.success) {
                            swal('Success!!',response.message,'success');
                        }
                    },
                    error:function(jqXHR, textStatus, errorThrown) {
                        swal('Oops!!',jqXHR.responseJSON.message,'error');
                    }
                });
            });
            function copyToClipBoard(id) {
              /* Get the text field */
              var copyText = document.getElementById(id);
              copyText.select();
              copyText.setSelectionRange(0, 99999); /* For mobile devices */
              document.execCommand('copy');
              return copyText.value;
            }

             $(document).on('click','.generate-shareable-link, .copy-shareable-link',function(){
                let id = $(this).data('id');
                $.ajax({
                    method: 'post',
                    url: '" . route('generate-sharable-plan-link') . "',
                    data: {id:id},
                    success: function(response) {
                        if (response.success) {
                            $('#testMailForEmailMarketing').modal('hide');
                            $(document).find('#generate-shareable-url-inp').val(response.url);
                            $(document).find('#generate-shareable-url-inp').attr('type','text');
                            let copiedText = copyToClipBoard('generate-shareable-url-inp');
                            $(document).find('#generate-shareable-url-inp').attr('type','hidden');
                            swal('Success!!',copiedText,'success');
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        $('#testMailForEmailMarketing').modal('hide');
                        swal('Oops!!',jqXHR.responseJSON.message,'error');
                    }
                });
            });

            $(document).on('click','.revoke-shareable-link',function(){
                let id = $(this).data('id');
                swal({
                    title: 'Do you want to Delete this?',
                    text: 'The proccess can not be undone!!!',
                    type:'warning',
                    showCancelButton:true,
                    allowOutsideClick:true,
                    confirmButtonColor: '#DD6B55',
                    confirmButtonText: 'Delete',
                    cancelButtonText: 'Cancel',
                    closeOnConfirm: true,
                },function() {
                    $.ajax({
                        method: 'GET',
                        url: '" . url('/admin/revoke-plan-shareable-link') . "'+'/'+id,
                        beforeSend: function (){
                            swal({
                                title: 'Loading...',
                                imageUrl: window.ASSET_URL+ 'images/loading.gif',
                                showConfirmButton: false,
                                allowOutsideClick: false,
                                confirm: true,
                                showLoaderOnConfirm: true
                            })
                        },
                        success:function(response) {
                            $('#testMailForEmailMarketing').modal('hide');
                            if (response.success) {
                                swal('Success!!',response.message,'success');
                            } else {
                                swal('Hi There!!',response.message,'info');
                            }
                        }
                    });
                });
            });

            $(document).on('keyup','.search-term',function(){
                let searchValue = $(this).val().trim();
                if (searchValue.length >= 3) {
                    if (searchValue) {
                        $.ajax({
                            method: 'post',
                            url: '" . route('search-user-name') . "',
                            data:{term:searchValue},
                            success:function(response) {
                                if (response.success) {
                                    $(document).find('.check-list-items').html(response.data);
                                }
                            }
                        });
                    }
                }
            });
        ";


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = '

                    <div class="modal fade" id="testMailForEmailMarketing" tabindex="-1" role="dialog">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                                <h4 class="modal-title changable-title">Marketing Plan Share</h4>
                            </div>
                            <div class="modal-body changable-body">

                            </div>
                        </div>
                      </div>
                  </div>
                  ';


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();
        $this->load_js[] = asset('js/marketing_plan.js?v=2');


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        $query->where('user_id', '=', CRUDBooster::myParentId());
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }


    public function getIndex(){
      $data = [];
      $data['page_title'] = 'Marketing Plan';
      $data['title_list'] = EmailMarketing::where('user_id', CRUDBooster::myParentId())->get();
      $data['plan_list'] =  DB::table('marketing_plan')->where('user_id',crudbooster::myParentId())->orderBy('id','desc')->get();
      return view('admin.drm_marketing_plan.index', $data);
    }

    public function getAdd()
    {
        $this->cbLoader();
        $is_exists = DB::table('marketing_plan')->where('user_id',CRUDBooster::myParentId())->exists();
        if (!CRUDBooster::isSuperadmin()) {
            $app_id = config('global.marketing_plan_app_id');
            if (!DrmUserHasPurchasedApp(CRUDBooster::myParentId(), $app_id) && $is_exists) {
                CRUDBooster::redirect(CRUDBooster::adminPath('app-list'), 'You must purchase Funnel Planner app.');
            }
        }
        $data = [];
        $data['page_title'] = 'Marketing Plan Add';
        $data['title_list'] = EmailMarketing::where('user_id', CRUDBooster::myParentId())->get();
        return view('admin.drm_marketing_plan.add', $data);
    }

    //By the way, you can still create your own method in here... :)

    public function getEdit($id)
    {
        $this->cbLoader();
        $data = [];
        $data['plan_data'] = DB::table('marketing_plan')->select('id', 'plan_name')->where('id', $id)->first();
        $data['page_title'] = 'Marketing Plan';
        $data['title_list'] = EmailMarketing::where('user_id', CRUDBooster::myParentId())->get();
        return view('admin.drm_marketing_plan.edit', $data);
    }

    private function deleteFile($path)
    {
        if (is_string($path)) {
            $path = (array)$path;
        }
        foreach ($path as $url) {
            if (File::isDirectory($url)) {
                File::deleteDirectory($url);
            }
        }
    }

    public function postPlanningStore(Request $request)
    {
      try{
        $image_name = CRUDBooster::myParentId() . '-' . rand();
        $image_path = $this->uploadBase64Image($request->image, $image_name);
        $html_file_url = $this->generateHtmlFile($request->html_design);
        $image_url = $this->urlToImage($html_file_url);
        // $image_url = 'https://drm-file.fra1.digitaloceanspaces.com/uploads/71-1974790557.octet-stream';
        $authId = CRUDBooster::myParentId();
//        $file_name = "marketings_plan/$authId/$image_name.png";
//        $fileContent = @file_get_contents($image_url);
//
//        if ($fileContent) {
//            Storage::disk('spaces')->put($file_name, $fileContent, 'public');
//            if(Storage::disk('spaces')->exists($file_name)){
//                $image_url = Storage::disk('spaces')->url($file_name);
//            }
//            $path = [public_path('/tmp_html'),public_path('/tmp')];
//            $this->deleteFile($path);
//        }

        $now = Carbon::now();
        $id = DB::table('marketing_plan')
            ->updateOrInsert(
                ['id' => $request->plan_id],
                ['user_id' => $authId,
                    'image' => $image_url,
                    'plan_name' => $request->plan_name,
                    'plan' => $request->planing_design,
                    'created_at' => $now,
                    'updated_at' => $now
                ]);

        $planning = DB::table('marketing_plan')->find($id);
        $url = CRUDBooster::mainpath();
        return response()->json(['success'=>true, 'message' => 'success', 'url' => $url]);
        } catch (\Exception $exception) {
        return response()->json(['success'=>false, 'message' => 'Something went wrong!', 'url' => $url]);
      }
    }

    public function getPlanning($id)
    {
        if (!empty($id)) {
            $planning = DB::table('marketing_plan')->where('id', $id)->first();
            return response()->json(['data' => json_decode($planning->plan)]);
        }
    }

    public function searchUserName(Request $request)
    {
        if (!empty($request->term)) {
            $users = User::select('id', 'name', 'email')
                ->whereNotNull('email_verified_at')
                ->where(function ($query) use ($request) {
                    $query->where('email', 'like', '%' . $request->term . '%')->orWhere('name', 'like', '%' . $request->term . '%');
                })
                ->take(15)
                ->get();

            if ($users->isNotEmpty()) {
                $html = '';
                foreach ($users as $user) {
                    $html .= '<div class="checkbox">
                                <label class="shareCheck">
                                  <input type="checkbox" value="' . $user->id . '"> ' . $user->name . '(' . $user->email . ')
                                </label>
                            </div>';
                }
                return response()->json(['success' => true, 'data' => $html], 200);
            }
        }
        return response()->json(['success' => false, 'data' => null], 200);
    }

    public function sharePlanWithUser(Request $request)
    {
        try {
            $id = $request->id ?? null;
            $marketingPlan = DB::table('marketing_plan')->where('user_id', CRUDBooster::myParentId())->where('id', $id)->first();
            $userIds = $request->user_ids ?? [];
            if (!empty($marketingPlan) && !empty($userIds)) {
                foreach ($userIds as $id) {

                    DB::table('marketing_plan')->insert([
                        'user_id' => $id,
                        'image' => $marketingPlan->image,
                        'plan_name' => $marketingPlan->plan_name,
                        'plan' => $marketingPlan->plan,
                        'created_at' => Carbon::now(),
                        'updated_at' => Carbon::now()
                    ]);
                }
                return response()->json(['success' => true, 'message' => 'Plan Successfully Shared']);
            }
            return response()->json(['success' => false, 'message' => 'Something Went Wrong'], 400);
        } catch (\Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage()], 400);
        }
    }

    public function generateSharablePlanUrl(Request $request)
    {
        try {
            $id = $request->id ?? null;
            $marketingPlan = DB::table('marketing_plan')->where('user_id', CRUDBooster::myParentId())->where('id', $id)->first();
            if (!empty($marketingPlan)) {
                $url = $marketingPlan->shareable_url;
                if (empty($url)) {
                    $url = url("/admin/marketings_plan_share/$marketingPlan->id");
                    DB::table('marketing_plan')->where('id', $id)->update([
                        'shareable_url' => $url,
                        'updated_at' => Carbon::now()
                    ]);
                }
                return response()->json(['success' => true, 'message' => 'Plan Url Successfully copied', 'url' => $url]);
            }
            return response()->json(['success' => false, 'message' => 'Something Went Wrong', 'url' => null], 400);
        } catch (\Exception $exception) {
            return response()->json(['success' => false, 'message' => $exception->getMessage(), 'url' => null], 400);
        }
    }

    public function replicatePlanToUser($id)
    {
        $marketingPlan = DB::table('marketing_plan')->where('id', $id)->first();
        if (!empty($marketingPlan)) {
            if (empty($marketingPlan->shareable_url)) {
                CRUDBooster::redirect(CRUDBooster::adminPath(), "Invalid URL Detected");
            }
            if ($marketingPlan->user_id != CRUDBooster::myParentId()) {
                DB::table('marketing_plan')->insert([
                    'user_id' => CRUDBooster::myParentId(),
                    'image' => $marketingPlan->image,
                    'plan_name' => $marketingPlan->plan_name,
                    'plan' => $marketingPlan->plan,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);
                return redirect('/admin/marketing_plan');
            }

        }
        CRUDBooster::redirect(CRUDBooster::adminPath(), "Something Went Wrong");
    }

    public function revokePlanShareableLink($id)
    {
        $marketingPlan = DB::table('marketing_plan')->where('user_id', CRUDBooster::myParentId())->where('id', $id)->first();
        if (!empty($marketingPlan)) {
            DB::table('marketing_plan')->where('id', $id)->update([
                'shareable_url' => null,
                'updated_at' => Carbon::now()
            ]);
            return response()->json(['success' => true, 'message' => 'Successfully Url Plan Revoked']);
        }
        return response()->json(['success' => false, 'message' => 'No Data Found']);
    }

    public function getShareablePlanModal($id)
    {
        $marketingPlan = DB::table('marketing_plan')->where('user_id', CRUDBooster::myParentId())->where('id', $id)->first();
        if (!empty($marketingPlan)) {
            $html = view('admin.drm_marketing_plan._share_campaign_modal', compact('marketingPlan'))->render();
            return response()->json(['success' => true, 'data' => $html]);
        }
        return response()->json(['success' => false, 'data' => null]);
    }

    public function getEmailContent($id)
    {
        $email_marketing_content = EmailMarketing::select('email_template', 'name', 'id')->find($id);
        if (!empty($email_marketing_content)) {
            $html = '
                      <div class="form-group" style="width: 100%;">
                        <label> Email Template</label>
                    </div>
                    <div class="form-group" style="width: 100%;overflow: hidden;">
                        ' . $email_marketing_content->email_template . '
                    </div>';
            $url = url('/admin/email_marketings/edit') . '/' . $email_marketing_content->id;
            $button = '
                        <button onclick="hideModal()" type="button" class="btn btn-info">Cancel</button>
                        <a href="' . $url . '" target="_blank" class="btn btn-success" role="button"><i class="fa fa-edit"></i>&nbsp;Edit</a>
                        ';
            return response()->json(['success' => true, 'data' => $html, 'button' => $button, 'name' => $email_marketing_content->name]);
        }
        return response()->json(['success' => false, 'data' => null]);
    }

    public function getCampaignList(Request $request)
    {
        $campaigns = EmailMarketing::select('email_template', 'name', 'id')->where('name', 'LIKE', '%' . $request->term . '%')->get();

        if (!empty($campaigns)) {
            $html = '';
            foreach ($campaigns as $cam) {
                $html .= '<a style="color:black;font-family: sans-serif;font-size:18px;" href="#about" onclick="selectCampaign(' . $cam->id . ')">' . $cam->name . '</a></br>';
            }
            // $html = '<a href="#" onclick="selectCampaign(1)">demo</a>';
            $html .= '<hr>';
            return response()->json(['success' => true, 'data' => $html]);
        }
        return response()->json(['success' => false, 'data' => null]);
    }

    public function getImage(Request $request)
    {


        $target_url = $request->url;
        $url = $this->urlToImage($target_url);

        return response()->json(['success' => true, 'data' => $url]);


//        set_error_handler(
//            function ($severity, $message, $file, $line) {
//                throw new \ErrorException($message, $severity, $severity, $file, $line);
//            }
//        );

        try {
            $screenshot_json_data = @file_get_contents($url);
            if (!$screenshot_json_data) {
                return response()->json(['success' => false, 'data' => '<p>5282852</p>']);
            }
            $screenshot_result = json_decode($screenshot_json_data, true);
            $screen_shot = $screenshot_result['lighthouseResult']['audits']['final-screenshot']['details']['data'];
            return response()->json(['success' => true, 'data' => $screen_shot]);
//            $screenshot_image = "<img src=".$screen_shot." class='img-responsive img-thumbnail'/>";
        } catch (Exception $e) {
            return response()->json(['success' => false, 'data' => '']);

        }

        restore_error_handler();


//        $url = $request->url;
//        $params['fullpage']  = '';
//        $params['width'] = '';
//        $params['viewport']  = '';
//        $params['format'] = '';
//        $params['css_url'] = '';
//        $params['delay'] = '';
//        $params['ttl'] = '';
//        $params['force']     = '';
//        $params['placeholder'] = '';
//        $params['user_agent'] = '';
//        $params['accept_lang'] = '';
//        $params['export'] = '';
//        if(!empty($url)){
//            $call = $this->screenshotlayer($url, $params);
//            return response()->json(['success' => true, 'data' => $call]);
//        }
    }

    function screenshotlayer($url, $args)
    {

        // set access key
        $access_key = "3ca7620de9e0f878152473c586392305";

        // set secret keyword (defined in account dashboard)
        $secret_keyword = "url2image";

        // encode target URL
        $params['url'] = urlencode($url);

        $params += $args;

        // create the query string based on the options
        foreach ($params as $key => $value) {
            $parts[] = "$key=$value";
        }

        // compile query string
        $query = implode("&", $parts);

        // generate secret key from target URL and secret keyword
        $secret_key = md5($url . $secret_keyword);

        return "https://api.screenshotlayer.com/api/capture?access_key=$access_key&secret_key=$secret_key&$query";

    }

    /**
     * @param $base64
     * @param $filename
     * @param string $path
     * @return string|null
     */
    function uploadBase64Image($base64, $filename, $path = 'uploads'): ?string
    {
        $split = explode(",", substr($base64, 5), 2);
        $mime_split = explode(";", $split[0], 2);
        $mime = explode("/", $mime_split[0], 2);
        if (count($mime) == 2) {
            $extension = $mime[1];
            $fullPath = $path . '/' . $filename . "." . $extension;
            Storage::disk('spaces')->put($fullPath, base64_decode($split[1]), 'public');
            if (Storage::disk('spaces')->exists($fullPath)) {
                return Storage::disk('spaces')->url($fullPath);
            }
            return null;
        }
        return null;
    }


    public function generateHtmlFile($html_design)
    {
        $pool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $rand_name = substr(str_shuffle(str_repeat($pool, 5)), 0, 20);
        $file_name = 'tmp_html/' . $rand_name . '.html';
        try {
            $path = public_path('/tmp_html');
            if (!File::isDirectory($path)) {
                File::makeDirectory($path, 0777, true, true);
            }
            $chrome_app = env('CHROME_URL', null);
            if (empty($chrome_app)) throw new \Exception('No app specified!');
            file_put_contents(public_path($file_name), $html_design);
            return asset($file_name);

        } catch (\Exception $e) {
            dd($e);
        }
    }

    //Generate screenshoot
    public function url2image()
    {

        $url = $_REQUEST['target_url'];
        $name = !empty($_REQUEST['name']) ? $_REQUEST['name'] : null;
        $width = $_REQUEST['width'] ?? 3000;
        $height = $_REQUEST['height'] ?? 3000;

        $pool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $rand_name = $name ?? substr(str_shuffle(str_repeat($pool, 5)), 0, 20);
        $image_name = 'tmp/' . $rand_name . '.png';

        try {
            if (empty($url)) throw new \Exception('Empty url');
            if ($_REQUEST['token'] != 'xvamc4s3wesses4f') throw new \Exception("Invalid token");

            $chrome_app = env('CHROME_URL', null);
            if (empty($chrome_app)) throw new \Exception('No app specified!');
            $chrome = new \App\Services\PHPChrome\Chrome($url, $chrome_app);

            //not necessary to set window size
            $chrome->setWindowSize($width, $height);
            $image_url = $chrome->getScreenShot(public_path($image_name));

            if (is_null($image_url) || empty($image_url)) throw new \Exception('Failed');
            // return response()->json([
            //     'success' => true,
            //     'url' => asset($image_name),
            //     'message' => 'Image generated successfully'
            // ]);
            return asset($image_name);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'url' => null,
                'message' => $e->getMessage()
            ]);
        }
    }


    //Get screenshoot png
    public function urlToImage($target_url, $name = null)
    {
        try {

            $url = 'https://drm.software/api/url-to-image-convert';
            $client = new \GuzzleHttp\Client();
            $response = $client->request('POST', $url, [
                'form_params' => [
                    'target_url' => $target_url,
                    'token' => 'xvamc4s3wesses4f',
                    'name' => $name,
                ]
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new \Exception('Connection problem!');
            }

            return $response->getBody()->getContents();

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'url' => null,
                'message' => $e->getMessage()
            ]);
        }
    }

}
