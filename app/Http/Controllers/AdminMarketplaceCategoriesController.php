<?php namespace App\Http\Controllers;

use DB;
use Request;
use Session;
use CRUDBooster;
use DOMDocument;
use App\DrmProduct;
use SimpleXMLElement;
use Illuminate\Support\Str;
use App\Enums\PermissionStatus;
use App\Enums\Marketplace\Status;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\Category;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use App\Enums\Marketplace\ProductStatus;
use App\MarketplaceProducts;
use App\Models\Marketplace\MarketplaceParentCategory;
use App\Models\Marketplace\MarketplaceProductExportFeed;
use App\Models\Marketplace\TempUserAccess;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Models\Marketplace\ApiCategory;
use App\Jobs\Marketplace\CategoryWiseDiscountSyncToDrm;
use App\Jobs\Marketplace\BulkMarketplaceProductDelete;


	class AdminMarketplaceCategoriesController extends \crocodicstudio\crudbooster\controllers\MarketplaceCBController {

        protected $categories;
        protected $categoryOptionsHTML;
        protected $industryTemplatesOptionsHtml;
        protected $industryTemplateSubmitUrl;
        protected $product_fields;
        protected $product_field;
        protected $product_export_url;
	    public function cbInit() {
			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "name";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = false;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "marketplace_categories";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Catgory ID","name"=>"id"];
			$this->col[] = ["label"=>"Name","name"=>"name"];
			$this->col[] = ["label"=>"Slug","name"=>"slug"];
			$this->col[] = ["label"=>"Parent Category","name"=>"parent_id","callback_php"=>'app(\App\Services\Marketplace\CategoryService::class)->getParentName($row->parent_id)'];
			// $this->col[] = ["label"=>"Photo","name"=>"photo","callback_php"=>'app(\App\Services\Marketplace\CategoryService::class)->imageColumnProcess($row->photo)'];// for task Delate "Top Angebote"
            $this->col[] = ["label"=>"Zolltarifnummer","name"=>"tariff_number"];
			$this->col[] = ["label"=>"Status","name"=>"is_active","callback_php"=>'app(\App\Services\Marketplace\CategoryService::class)->statusColumnProcess($row->is_active)'];
			if(CRUDBooster::isSuperadmin() || CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport())
				$this->col[] = ["label"=>"Top Category","name"=>"is_top_category","callback_php" => 'app(\App\Services\Marketplace\CategoryService::class)->topCategoryColumnProcess($this->categories,$row->id)'];

			$this->col[] = ["label"=>"Industry Template","name"=>"industry_template", "callback_php" => '$this->industryTemplateColumn($row)'];
			$this->col[] = ["label"=>"Products","name"=> "count_product"];
			$this->col[] = ["label"=>"Minimum Price","name"=>"minimum_price"];

			$this->col[] = ["label"=>"Discount Percentage","name"=>"discount_percentage"];
			$this->col[] = ["label"=>"Offer Status","name"=>"is_offer_active","callback_php"=>'app(\App\Services\Marketplace\CategoryService::class)->statusColumnProcess($row->is_offer_active)'];
            $this->col[] = ["label"=>__("marketplace.comment"),"name"=>"comment"];

            if(CRUDBooster::isSuperadmin() || CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()){
                $this->col[] = [
                    'label' => 'Exclusive User',
                    'name' => 'exclusive_user_id',
                    'callback' => function($row) {
                        $users = \App\User::where('id_cms_privileges',3)->where('status','Active')->where('marketplace_access', 1)->pluck('name', 'id')->toArray();
                        $exclusive_users = json_decode($row->exclusive_user_id, true) ?? [];
                        $options = '<option value="">Select Exclusive User</option>';
                        foreach ($users as $userId => $userName) {
                            $selected = in_array($userId, $exclusive_users) ? 'selected' : '';
                            $options .= '<option value="' . $userId . '" ' . $selected . '>' . $userName . '</option>';
                        }
                        return '<select class="form-control select_exclusive_users_category" id="select_exclusive_users_category_'. $row->id .'"  data-mp_exclusive_category_id="'. $row->id .'" name="user_id[]" multiple="multiple">' . $options . '</select>';
                    },
                ];
            }

            // $this->col[] = ["label"=>"Parent Category","name"=>"parent_id","join"=>"marketplace_categories,name"];
			//$this->col[] = ["label"=>"Parent Id","name"=>"parent_id","join"=>"parent,id"];
			// $this->col[] = ["label"=>"Photo","name"=>"photo","image"=>true];
			// $this->col[] = ["label"=>"Price","name"=>"price"];




			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Name','name'=>'name','type'=>'text','validation'=>'required|string|min:3|max:70','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
            $this->form[] = ['label'=>'Minimum Price','name'=>'minimum_price','type'=>'number','validation'=>'number|min:3|max:70','width'=>'col-sm-10','placeholder'=>'You can only enter the number only'];
            $this->form[] = ['label'=>'Zolltarifnummer','name'=>'tariff_number','type'=>'text','validation'=>'string','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
            $parent_categories = MarketplaceParentCategory::where('is_active',1)->orderBy('name')->get();
            foreach ($parent_categories as $value) {
                $label = $value->name;
                $tables_list[] = $value->id."|".$label;

            }
            foreach( range(1, 100) as $elements) {
                $label = $elements.'%';
                $discount_list[] = $elements."|".$label;
            }
            // $this->form[] = ["label" => "Table Name", "name" => "table_name", "type" => "select", "dataenum" => $tables_list, 'required' => true];
			$this->form[] = ['label'=>'Parent Id','name'=>'parent_id','type'=>'select2','width'=>'col-sm-10',"dataenum" => $tables_list];
			$this->form[] = ['label'=>'Active','name'=>'is_active','type'=>'radio','dataenum'=>'1|Active;2|Inactive', 'value' => 1];
			$this->form[] = ['label'=>'Discount Amount(%)', 'name'=>'discount_percentage','type'=>'select2', 'width'=>'col-sm-10', "dataenum" => $discount_list];
			$this->form[] = ['label'=>'Start Date','name'=>'start_date','type'=>'date', 'width'=>'col-sm-10', 'value' => ""];
			$this->form[] = ['label'=>'End Date','name'=>'end_date','type'=>'date', 'width'=>'col-sm-10', 'value' => ""];
			$this->form[] = ['label'=>'Offer Active','name'=>'is_offer_active','type'=>'radio','dataenum'=>'1|Offer Active;2|Offer Inactive', 'value' => 2];
            $this->form[] = ['label'=> __("marketplace.comment"),'name'=>'comment','type'=>'textarea','width'=>'col-sm-10'];
			//$this->form[] = ['label'=>'Slug','name'=>'slug','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//  $this->form[] = ['label'=>'Photo','name'=>'photo','type'=>'upload','validation'=>'required|image|max:3000','width'=>'col-sm-10','help'=>'File types support : JPG, JPEG, PNG, GIF, BMP'];// for task Delate "Top Angebote"
			// $this->form[] = ['label'=>'Price','name'=>'price','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			//if(CRUDBooster::isSuperadmin())
			//$this->form[] = ['label'=>'Top Category','name'=>'is_top_category','type'=>'checkbox', 'dataenum'=>'1|', 'value' => 0];
			//$this->form[] = ['label'=>'Is Active','name'=>'is_active','type'=>'radio','validation'=>'nullable|integer','width'=>'col-sm-10','dataenum'=>'Array'];
			# END FORM DO NOT REMOVE THIS LINE

			$default_column=['Catgory ID', 'Name', 'Slug', 'Parent Category','Photo', 'Status', 'Industry Template','Minimum Price','Discount Percentage','Offer Status'];
			$saved_colum=DB::table('drm_user_saved_columns')
						->where('user_id',CRUDBooster::myParentId())
						->where('table_name',$this->table)->pluck('columns')->first();
			if($saved_colum){
				$default_column=json_decode($saved_colum);
			}
			$action=CRUDBooster::mainpath("columnsave");
			$token=csrf_token();
			$checkboxdata= '<form id="category_column_hide_show" style="display:none;position: relative;width: 100%;background: #fff;padding: 10px 15px;margin-bottom:5px;" method="post" action="'.$action.'"><input type="hidden" name="_token" value="'.$token.' "><input type="hidden" name="table_name" value="'.$this->table.' "><h5 class="box-title">Table column:</h5>';
            foreach($this->col as $key=>$single_column){
			$selected='checked';
			 if(!in_array($single_column['label'], $default_column)){
				unset($this->col[$key]);
				$selected='';
			 }

			 $checkboxdata.= '<div class="single-ct-checkbox"><input  type="checkbox" id="" class="" name="saved_columns[]" value="'.$single_column['label'].'" '.$selected.' > '.$single_column['label'].'
			</div>';
			}
			$checkboxdata.= '<div class="ct-btn-wrap"><input type="submit" class="btn btn-drm" value="Apply"></div></form>';





			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
			$this->addaction = array();
			// if(CRUDBooster::isSuperadmin() || CRUDBooster::isDropMatrix())
			// $this->addaction[] = ['title'=>'Remove From Top Category','name'=>'is_top_category', 'url'=>CRUDBooster::mainpath('top-category/[id]'),'icon'=>'fa fa-minus','color'=>'primary','showIf' => '[is_top_category] == 1'];
			// $this->addaction[] = ['title'=>'Mark As Top Category','name'=>'is_top_category', 'url'=>CRUDBooster::mainpath('top-category/[id]'),'icon'=>'fa fa-plus','color'=>'success','showIf' => '[is_top_category] == 0'];

            $this->addaction[] = ['title' => 'Info', 'icon' => 'fa fa-info-circle', 'url' => 'javascript:cateoryInfo([id])'];
            $this->addaction[] = ['title' => 'Delete Catgory', 'icon' => 'fa fa-trash', 'color'=> 'warning','url' => 'javascript:deleteCategory([id])'];


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
			*/

			$this->button_selected = array();
			$this->button_selected[] = ['label'=>'Import XML','icon'=>'fa-solid fa-download','name'=>'import_xml_product',];

	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        if ( CRUDBooster::getCurrentMethod() == 'getIndex' ) {
	        	$this->index_button[] = [
	        		'label'=>'Industry Templates','url'=>'javascript:showIndustryTemplates()',"icon"=>"fa fa-edit"
	        	];
	        }



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = 'function showIndustryTemplates(){
	        	$("#industryTemplates").modal("show");
	        }
	        $(".remove-industry-template-btn").click(function () {
	        	var url = $(this).attr(`href`);
	        	swal({
	        	  title: "Are you sure to remove ?",
	        	  type: "warning",
	        	  showCancelButton: true,
	        	  confirmButtonClass: "btn-danger",
	        	  confirmButtonText: "Yes !",
	        	  cancelButtonText: "No",
	        	  closeOnConfirm: false,
	        	  closeOnCancel: true
	        	},
	        	function(isConfirm) {
	        	  if (isConfirm) {
	        	    window.location = url;
	        	  } else {

	        	  }
	        	});
	        })
			$("#parent_categories_select").select2();
            $("#table-column-option-btns").click(function(){
                $("#category_column_hide_show").slideToggle();
                $(this).children(".caret").toggleClass("rotate-180");
            })
            ';


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = $checkboxdata;
            // echo '<pre>';
            // echo ($this->pre_index_html);
            // echo '</pre>';
            // die();


	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
            $product_fields = [
                'name' => __("Title"),
                'description'=> __("Description"),
                'ean'=> __("EAN"),
                'item_number'=> __("Item Number"),
                'uvp'=> __("UVP"),
                'vk_price' => __("Vk Price"),
                'stock'=> __("Stock"),
                'delivery_days'=> __("Handling Time"),
                'shipping_cost' => __("Shipping Cost"),
                'item_weight'=> __("Item Weight"),
                'item_size'=> __("Item Size"),
                'item_color'=> __("Item Color"),
                'note'=> __("Note"),
                'production_year'=> __("Production Year"),
                'brand'=> __("Brand"),
                'materials'=> __("Materials"),
                'tags'=> __("Tags"),
                'gender'=> __("Gender"),
                'status'=> __("Status"),
                'image'=> __("Image"),
                'industry_template_data'=> __("Industry Template"),
            ];

            foreach($product_fields as $key => $field){
                $this->product_field .= '<div class="col-sm-3">
                    <input type="checkbox" id="p_transfer_field__'.$key.'" class="field_checkbox" name="selected_columns[]" value="'.$key.'"> '.$field.'
                </div>';
            }


	        $this->categoryOptionsHTML .= '<option value="">Select a category</option>';
	        foreach ( \App\Models\Marketplace\Category::orderBy('name')->get() as $category ) {
	        	$this->categoryOptionsHTML .= '<option value="'.$category->id.'">'.$category->name.'</option>';
	        }

	        $this->industryTemplatesOptionsHtml .= '<option value="">Select a template</option>';
	        foreach ( \App\Enums\Marketplace\IndustryTemplates::TEMPLATES as $templateKey => $valueArray) {
	        	$this->industryTemplatesOptionsHtml .= '<option value="'.$templateKey.'">'.ucfirst( strtolower($templateKey))
.'</option>';
	        }

	        $this->industryTemplateSubmitUrl = CRUDBooster::adminPath('marketplace_categories/save-template-industries');
	        $this->product_export_url = CRUDBooster::adminPath('marketplace_categories/xml-feed-url');


	        $this->post_index_html = '
			<div class="modal fade" id="industryTemplates" tabindex="-1" role="dialog" aria-hidden="true">
			  <div class="modal-dialog" role="document">
			    <div class="modal-content">
			    <form action="'.$this->industryTemplateSubmitUrl.'" method="POST">
			    	<input type="hidden" value="'.csrf_token().'" name="_token" />
			      <div class="modal-header">
			        <h5 class="modal-title" id="exampleModalLabel">Industry Templates Mapping</h5>
			        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
			          <span aria-hidden="true">&times;</span>
			        </button>
			      </div>
			      <div class="modal-body">


			          <div class="form-group">
			            <label for="">Select a Category</label>
			            <select class="form-control" name="category_id" required>'.
			            	$this->categoryOptionsHTML
			            .'
			            </select>
			          </div>

			          <div class="form-group">
			            <label for="">Select Industry template</label>
			            <select class="form-control" name="industry_template" required>'.
			            	$this->industryTemplatesOptionsHtml
			            .'</select>
			          </div>
			      </div>
			      <div class="modal-footer">
			        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
			        <button type="submit" class="btn btn-primary">Save changes</button>
			      </div>
			      </form>
			    </div>
			  </div>
			</div>
            <div class="modal fade" id="mp_export_xml" role="dialog">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title" id="transferModalLabel">Product Export Fields</h4>
                        </div>
                        <form action="'.$this->product_export_url.'" method="get" id="mp_export_xml_form">
                            <div class="modal-body">
                                <input type="hidden" id="transfer_channel">
                                <div class="row" style="margin-bottom: 8px">
                                    <div class="col-sm-12">
                                        <input type="checkbox" id="checkAll"> Check All
                                    </div>
                                </div>
                                <div class="row" id="marketplace_product_transfer_filed">
                                '.$this->product_field.'
                                </div>
                                <input type="hidden" name="categoryIds" value="" class="categoryIds >
                            </div>
                            <div class="modal-footer>
                                <button type="button" class="btn btn-default" data-dismiss="modal" style="margin-top:30px;">Close</button>
                                <button type="button" class="btn btn-primary" id="category_export_submit_btn" style="margin-top:30px;">Save changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="feed_url_modal" role="dialog">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <p class="modal-title" id="transferModalLabel">Marketplace Product XML Feed URL</p>
                        </div>
                        <div class="modal-body">
                            <div class="row" style="text-align:center;">
                                <h4>Congratulations Your XML Feed URL Generated</h4>
                                <h6 id="md_feed_url"> </h6>
                                <button onclick="copyFeedUrl()">Copy Feed</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
			<div class="modal fade" id="assign_parent_category_modal" role="dialog">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <p class="modal-title" id="transferModalLabel">Assign Parent Category</p>
                        </div>
                        <div class="modal-body">
						<form action="'.CRUDBooster::adminPath('marketplace_categories/assign-parent-category').'" method="get" id="assign_parent_category_form">
						<div class="modal-body">
							<input type="hidden" id="transfer_channel">
							<div class="row" id="marketplace_product_transfer_filed">
							<div class="form-group col-sm-12 col-md-12">
								<label>Parent Category<span class="text-danger"> *</span></label>
								<select class="form-control" id="parent_categories_select" name="parent_categories_select" value="" required>

								</select>
							</div>
							</div>
							<input type="hidden" name="categoryIds" value="" class="sub_categoryIds >
						</div>
						<div class="modal-footer>
							<button type="button" class="btn btn-default" data-dismiss="modal" style="margin-top:30px;">Close</button>
							<button type="button" class="btn btn-primary" id="assign_parent_category_submit_btn" style="margin-top:30px;">Assign Parent Category</button>
						</div>
					</form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="category_info_modal" role="dialog">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <p class="modal-title" id="transferModalLabel">Category Product Stock Information</p>
                        </div>
                        <div class="modal-body">

                        </div>
                    </div>
                </div>
            </div>
            <div class="modal fade" id="category_delete_info_modal" role="dialog">
                <div class="modal-dialog modal-md">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <p class="modal-title" id="transferModalLabel">Category Delete Confirmation Check</p>
                        </div>
                        <div class="modal-body">

                        </div>
                    </div>
                </div>
            </div>';



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();
			$this->load_js[] = asset('js/mp_category_btn.js?v=19');


	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();
			$this->load_css[] = asset('css/mp_category_btn.css?v=1');

	    }

	    public function industryTemplateColumn($row)
	    {
	    	$html = "-";
	    	if ( $row->industry_template ) {
	    		$removeUrl = CRUDBooster::mainpath('remove-industry-template')."?id=".$row->id;
	    		$html = '<span>'.$row->industry_template.'  </span><span href="'.$removeUrl.'" class="remove-industry-template-btn"><i class="ml-2  text-danger fa fa-trash"></i></span>';
	    	}
	    	return $html;
	    }

        public function __construct(Category $categories)
        {
            $this->categories = $categories;
        }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
            if($button_name == 'import_xml_product') {
                DB::table('products')->whereIn('id',$id_selected)->update(['status'=>'active']);
              }
			// dd('t');
			// $top_cat_yes = DB::table('marketplace_categories')->where('is_top_category', PermissionStatus::YES)->get();
			// $top_cat_no = DB::table('marketplace_categories')->where('is_top_category', PermissionStatus::NO)->get();
			// if($top_cat_yes)
			// $this->addaction[] = ['title'=>'Remove From Top Category','name'=>'is_top_category', 'url'=>CRUDBooster::mainpath('[id]'),'icon'=>'fa fa-minus','color'=>'primary','dataenum'=>'0|'];
			// elseif($top_cat_no)
			// $this->addaction[] = ['title'=>'Mark As Top Category','name'=>'is_top_category', 'url'=>CRUDBooster::mainpath('[id]'),'icon'=>'fa fa-plus','color'=>'success','dataenum'=>'1|'];

	    }

		public function postActionSelected()
    {
        $this->cbLoader();
        $id_selected = Request::input('checkbox');
        $button_name = Request::input('button_name');

        if (!$id_selected) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans("crudbooster.alert_select_a_data"), 'warning');
        }

        try {
            if ($button_name == 'export_xml') {
                return $this->getExportXml($id_selected);
            }
        } catch (Exception $e) {
            return redirect()->back()->with(['message_type' => 'error', 'message' => $e->getMessage()]);
        }

        if ($button_name == 'delete_category') {
            if (!CRUDBooster::isDelete()) {
                CRUDBooster::insertLog(trans("crudbooster.log_try_delete_selected", ['module' => CRUDBooster::getCurrentModule()->name]));
                CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
            }

            $this->deleteCategory($id_selected);

            $message = trans("crudbooster.alert_delete_selected_success");

            return redirect()->back()->with(['message_type' => 'success', 'message' => $message]);
        }

        $action = str_replace(['-', '_'], ' ', $button_name);
        $action = ucwords($action);
        $type = 'success';
        $message = trans("crudbooster.alert_action", ['action' => $action]);

        if ($this->actionButtonSelected($id_selected, $button_name) === false) {
            $message = !empty($this->alert['message']) ? $this->alert['message'] : 'Error';
            $type = !empty($this->alert['type']) ? $this->alert['type'] : 'danger';
        }

        return redirect()->back()->with(['message_type' => $type, 'message' => $message]);
    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
			if($_REQUEST['filter_by'] == 'hasParent'){
				$query->where('parent_id','!=',null)->where('parent_id','!=',0);
			}else if($_REQUEST['filter_by'] == 'hasNoParent'){
				$query->where('parent_id','=',null)->orWhere('parent_id',0);
			}else if($_REQUEST['filter_by'] == 'discount'){
				$query->where('discount_percentage','!=',null)->where('is_offer_active','=',1);
			}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
		//		protected $current_id = 0;
	    public function hook_row_index($column_index,&$column_value) {
            //$this->current_id = $column_value;
            //dd(request()->checkbox);
            // $products = \DB::connection('marketplace')->table('marketplace_products')->where('category_id',$id)->count();
            // dd($products);
            //$this->button_delete = false;
            // if ($column_index == 9) {
            //     $column_value =
            // }
           // dd($row->id);

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
			$postdata['slug']  = Str::slug($postdata['name']);
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
           cache()->forget('UserCategoryAccess_' . CRUDBooster::myParentId());

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
			//Your code here
			$postdata['slug'] = Str::slug($postdata['name']);


	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
           cache()->forget('UserCategoryAccess_' . CRUDBooster::myParentId());

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here



	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here
			ini_set('max_execution_time', '0');
            ini_set('memory_limit',-1);

			$products = Product::where('category_id',$id)->get();
			foreach($products as $product){
				DrmProduct::where('marketplace_product_id',$product->id)->delete();
				$product->delete();
			}

			Category::where('id',$id)->delete();
            ApiCategory::whereIn('mp_category_id',$id)->update(['mp_category_id'=>'','is_complete'=>0]);

	    }




		//By the way, you can still create your own method in here... :)

		public function getTopCategory($id)
		{
			try {
				$category = Category::find($id);
				Category::where('id', $id)->update(['is_top_category' => $category->is_top_category == PermissionStatus::YES ? PermissionStatus::NO : PermissionStatus::YES]);
				$event = $category->is_top_category == PermissionStatus::YES ? 'No' : 'Yes';
				return redirect()->back()->with(['_status' => 'success', '_msg' => 'Successfully '. $event .' Category']);
			} catch (\Exception $e) {
				return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to update!']);
			}


		}

        public function getSubCategoriesFromParentCategory ($categoryId) {
            // $category = Category::find($categoryId);
            $category = $this->categories->find($categoryId);
            $subCategories = '<option value="0"> - Select sub category - </option>';
            foreach ( $category->children()->get() as $child ) {
                $subCategories .= '<option value="'.$child->id.'">'.$child->name.'</option>';
            }
            return response()->json([
                'success' => 'yes',
                'subCategories' => $subCategories,
            ]);
        }

        public function getEdit($id)
        {
            $data = [];
            $data['page_title'] = __('marketplace.edit_marketplace_category_data');
            $data['row'] = $this->categories->where('id',$id)->first();
			$data['parent_categories'] = MarketplaceParentCategory::where('is_active',1)->orderBy('name')->get();

            $this->cbView('marketplace.category.edit',$data);
        }

        public function getDetail($id)
        {
            $data = [];
            $data['page_title'] = 'Detail MArketplace Category';
//             $data['row'] = DB::table('marketplace_categories')->where('id',$id)->first();
            $data['row'] = $this->categories->where('id',$id)->first();

            $this->cbView('marketplace.category.detail',$data);
//            return parent::getDetail($id); // TODO: Change the autogenerated stub
        }

        public function postEditSave($id)
        {
            ini_set('max_execution_time', '0');
            ini_set('memory_limit',-1);

            $category = $this->categories->find($id);
            $old_minimum_price = $category->minimum_price;
			if(request()->parent_category){
				$hasParentIndustryTemplate = $this->hasParentIndustryTemplate(request()->parent_category);
                $has_parent_offer = MarketplaceParentCategory::where('id', request()->parent_category)->where('is_offer_active', 1)->where('end_date', '>', now())->first();
			}
            try {

                $category->name          = request()->name;
                $category->minimum_price = request()->minimum_price;
                $category->slug          = Str::slug(request()->name);
                $category->is_active     = request()->is_active;
                $category->parent_id     = request()->parent_category;
                $category->tariff_number = request()->tariff_number;
                // $category->photo         = request()->photo ? uploadImage(request()->photo, 'marketplace-categories') : $category->photo;// for task Delate "Top Angebote"
				$category->industry_template = $hasParentIndustryTemplate;
                $category->start_date    = request()->start_date ?? $has_parent_offer->start_date;
                $category->end_date      = request()->end_date ?? $has_parent_offer->end_date;
                $category->discount_percentage = request()->discount_percentage ?? $has_parent_offer->discount_percentage;
                $category->is_offer_active      = $has_parent_offer->is_offer_active == 1 ? 1 : request()->is_offer_active;
                $category->is_parent_offer      = request()->is_offer_active == 1 ? 2 : ($has_parent_offer ? 1 : 0);
                $category->comment       = request()->comment ?? null;
                $category->update();

                if($category->start_date <= now() && $category->end_date >= now()){
                    $discount = $category->is_offer_active ? $category->discount_percentage : 0.0;
                } else {
                    $discount = 0.0;
                }

                $productIds = Product::where('category_id',$category->id)->pluck('id')->toArray();
                $checkExistInDrm = MpCoreDrmTransferProduct::whereIn('marketplace_product_id',$productIds)->get();
                if(count($checkExistInDrm) > 0){
                    foreach ($checkExistInDrm->chunk(500) as $product) {
                        dispatch(new CategoryWiseDiscountSyncToDrm($product, $discount));
                    }
                }

				if($category->is_active == 2){
					// $products = Product::where('category_id',$category->id)->get();
					// foreach($products as $product){
					// 	DrmProduct::where('marketplace_product_id',$product->id)->delete();
					// 	$product->delete();
					// }

					$productIds = Product::where('category_id',$category->id)->pluck('id')->toArray();
					$checkExistInDrm = MpCoreDrmTransferProduct::whereIn('marketplace_product_id',$productIds)->get();
					if(count($checkExistInDrm) > 0){
						foreach($checkExistInDrm as $product){
							app(\App\Services\DRMProductService::class)->destroy($product->drm_product_id,$product->user_id);
						}
					}
					Product::where('category_id',$category->id)->delete();


				}

                if($old_minimum_price < $category->minimum_price && $category->is_active == 1){
                    Product::where(['category_id'=>$category->id,'status'=>ProductStatus::ACTIVE])
                        ->where('ek_price','<',request()->minimum_price)->update(['status'=>ProductStatus::PENDING,'system_by_pending'=>1]);
                }else if($old_minimum_price > $category->minimum_price && $category->is_active == 1){

					Product::where(['category_id'=>$category->id,'status'=>ProductStatus::PENDING,'system_by_pending'=> 1])
                        ->where('ek_price','>=',request()->minimum_price)->update(['status'=>ProductStatus::ACTIVE,'system_by_pending'=>0]);

				}

                CRUDBooster::redirect(request()->return_url, __("The Category Info Updated Successfully")."!", "success");
            } catch (\Exception $e) {
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], __("Something Went Wrong")." !", "error");
            }
        }

        public function postAddSave()
        {
            try {
				if(request()->parent_id){
					$hasParentIndustryTemplate = $this->hasParentIndustryTemplate(request()->parent_id);
                    $has_parent_offer = MarketplaceParentCategory::where('id', request()->parent_id)->where('is_offer_active', 1)->where('end_date', '>', now())->first();
                }
                $this->categories->create([
                    'name'      => request()->name,
                    'is_active' => request()->is_active,
                    'minimum_price' => request()->minimum_price ?? 0.0,
                    'tariff_number' => request()->tariff_number ?? '',
                    'parent_id' => request()->parent_id,
                    // 'photo'     => uploadImage(request()->photo, 'marketplace-categories'),// for task Delate "Top Angebote"
                    'slug'      => Str::slug(request()->name),
					'industry_template'=>$hasParentIndustryTemplate,
                    'start_date'      => request()->start_date ?? $has_parent_offer->start_date,
                    'end_date' => request()->end_date ?? $has_parent_offer->end_date,
                    'discount_percentage' => request()->discount_percentage ?? $has_parent_offer->discount_percentage,
                    'is_offer_active' => $has_parent_offer->is_offer_active == 1 ? 1 : request()->is_offer_active,
                    'is_parent_offer' => request()->is_offer_active == 1 ? 2 : ($has_parent_offer ? 1 : 0),
                    'comment' => request()->comment ?? null,
                ]);
            } catch (\Exception $e) {
                CRUDBooster::redirect(request()->return_url, __("Something Went Wrong")."!", "error");
            }
            CRUDBooster::redirect(request()->return_url, __("The Category Created Successfully")."!", "success");
        }


        // public function getCheckAccessAndReturnAllCategories ($id)
        // {

        //     $user = \App\User::findOrfail($id);
		// 	   $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->orderBy('name')->get();
        //     $categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->orderBy('name')->get();
        //     $accessableCategoryIds = [];
        //     if ( $user->marketplace_access == 1 ) {
        //         $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->accessable_categories ?? [];
        //         $accessableCategoryIds = array_map('intval', $accessableCategoryIds);
        //     }

        //     return view('marketplace.category.category_access_modal_data',compact('parent_categories','categories','accessableCategoryIds'));
        // }

        public function getCheckAccessAndReturnAllCategories ($id)
        {

            $categories = [];
            $accessableCategoryIds = [];
            $accessableCategory = [];
            $allCategory = [];
            $access_status = [];
            $user = \App\User::findOrfail($id);
			$parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->orderBy('name')->get();
            $all_categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->orderBy('name')->get();
            $category_product = array_unique(MarketplaceProducts::where('status',1)->pluck('category_id')->toArray());
            foreach($all_categories as $category){
                if(in_array($category->id, $category_product)){
                    $categories[] = $category;
                }
            }
            // if ( $user->marketplace_access == 1 ) {
                $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
                foreach($accessableCategoryIds as $accessableCategoryId){
                    if(isset($accessableCategoryId['set_by_admin'])){
                       array_push($accessableCategory, [$accessableCategoryId['set_by_admin'] => $accessableCategoryId['accessable_categories']]);
                    }
                }
                foreach($accessableCategory as $Category){
                    $allCategory[] = current($Category);
                }
                if(empty($allCategory)){
                    $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
                }else{
                    $accessableCategoryIds = array_map('intval', $allCategory);
                }
                foreach($accessableCategory as $category){
                    $access_status[current($category)] = key($category);
                }

            // }

            return view('marketplace.category.category_access_modal_data',compact('parent_categories','categories','accessableCategoryIds','category_product','access_status'));
        }
        public function getCheckAccessAndReturnAllCategoriesOpen ($id)
        {

            $data=[];
            TempUserAccess::where('user_id',$id)->delete();       
            $data['user_info'] = DB::table('cms_users')->where('id',$id)->first();
            $data['access'] = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first();
            $data['countaccess'] = count(array_filter($data['access']->accessable_parent_categories ?? []));
            $data['mp_cat_limit'] = $this->userMpCategoryLimit($id);

            if(isset($_GET['db_plan_amount']) && !empty($_GET['db_plan_amount'])){
                $data['mp_cat_limit'] = $_GET['db_plan_amount'];
            }

            return response()->json($data);
        }

        public function getAccessAllCategories ($id)
        {
            TempUserAccess::where('user_id',$id)->delete();
            // $accessableCategoryIds = [];
            // $accessableCategory = [];
            // $access_status = [];
            // $categories = [];
            // $user = \App\User::findOrfail($id);
        // $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->orderBy('name')->get();
        // $all_categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->orderBy('name')->get();
        // $category_product = array_unique(MarketplaceProducts::where('status',1)->pluck('category_id')->toArray());

        // foreach($all_categories as $category){
        //     if(in_array($category->id, $category_product)){
        //         $categories[] = $category;
        //     }
        // }

        // // if ( $user->marketplace_access == 1 ) {
        //     $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
        //     foreach($accessableCategoryIds as $accessableCategoryId){
        //         if(isset($accessableCategoryId['set_by_admin'])){
        //             array_push($accessableCategory, [$accessableCategoryId['set_by_admin'] => $accessableCategoryId['accessable_categories']]);
        //         }
        //     }

        //     foreach($accessableCategory as $Category){
        //         $allCategory[] = current($Category);
        //     }
        //     if(empty($allCategory)){
        //         $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
        //     }else{
        //         $accessableCategoryIds = array_map('intval', $allCategory);
        //     }

        //     foreach($accessableCategory as $category){
        //     $access_status[current($category)] = key($category);
        //     }
        // if(empty($access_status)){
        //     $access_status = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
        // }
        // }

        // return view('marketplace.category.user_category_access_modal',compact('parent_categories','categories','access_status','category_product','accessableCategoryIds'));
        return view('marketplace.category.user_category_access_modal');
        }

        public function getAccessAllCategoriesCustomer ($id)
        {
            $accessableCategoryIds = [];
            $accessableCategory = [];
            $access_status = [];
            $categories = [];
            $user = \App\User::findOrfail($id);
            $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('name','like', request()->letter . '%')->get();
            $all_categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->where('name', 'like', '%' . request()->letter . '%')->get();
			//$parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->orderBy('id')->get();
            //$all_categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->orderBy('name')->get();
            $category_product = array_unique(MarketplaceProducts::where('status',1)->pluck('category_id')->toArray());

            foreach($all_categories as $category){
                if(in_array($category->id, $category_product)){
                    $categories[] = $category;
                }
            }

            // if ( $user->marketplace_access == 1 ) {
                $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
                foreach($accessableCategoryIds as $accessableCategoryId){
                    if(isset($accessableCategoryId['set_by_admin'])){
                        array_push($accessableCategory, [$accessableCategoryId['set_by_admin'] => $accessableCategoryId['accessable_categories']]);
                    }
                }

                foreach($accessableCategory as $Category){
                    $allCategory[] = current($Category);
                }
                if(empty($allCategory)){
                    $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
                }else{
                    $accessableCategoryIds = array_map('intval', $allCategory);
                }

                foreach($accessableCategory as $category){
                $access_status[current($category)] = key($category);
                }
                if(empty($access_status)){
                    $access_status = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
                }
            // }

            return view('marketplace.category.category_load',compact('parent_categories','categories','access_status','category_product','accessableCategoryIds'));
        }

        public function getAccessAllCategoriesCustomerSearch ()
        {
            $id = request()->user_id;
            $accessableCategoryIds = [];
            $accessableCategory = [];
            $access_status = [];
            $categories = [];
            $user = \App\User::findOrfail($id);
            $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('name','like', request()->letter . '%')->get();
            $all_categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->where('name', 'like', '%' . request()->letter . '%')->get();
			//$parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->orderBy('id')->get();
            //$all_categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->orderBy('name')->get();
            $category_product = array_unique(MarketplaceProducts::where('status',1)->pluck('category_id')->toArray());

            foreach($all_categories as $category){
                if(in_array($category->id, $category_product)){
                    $categories[] = $category;
                }
            }

            // if ( $user->marketplace_access == 1 ) {
                $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
                foreach($accessableCategoryIds as $accessableCategoryId){
                    if(isset($accessableCategoryId['set_by_admin'])){
                        array_push($accessableCategory, [$accessableCategoryId['set_by_admin'] => $accessableCategoryId['accessable_categories']]);
                    }
                }

                foreach($accessableCategory as $Category){
                    $allCategory[] = current($Category);
                }
                if(empty($allCategory)){
                    $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
                }else{
                    $accessableCategoryIds = array_map('intval', $allCategory);
                }

                foreach($accessableCategory as $category){
                $access_status[current($category)] = key($category);
                }
                if(empty($access_status)){
                    $access_status = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
                }
            // }

            return view('marketplace.category.category_load',compact('parent_categories','categories','access_status','category_product','accessableCategoryIds'));
        }

        public function getCheckAccessAndReturnAllUserCategories ($id)
        {
            TempUserAccess::where('user_id',$id)->delete();
            $access_status = [];
            $categories = [];
            $user = \App\User::findOrfail($id);
            $user_info = DB::table('cms_users')->where('id',$id)->first();

            $category_quantity = \App\User::where('id',$id)->get();
            $category_product = array_unique(MarketplaceProducts::where('status',1)->pluck('category_id')->toArray());

            // cache()->remember('category-search', 60*60*24, function () {
            //     return  MarketplaceParentCategory::withCount('category')->where('is_active',1);
            // });

            $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('name','like', "A" . '%')->get();
            // $categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->orderBy('name')->get();
            $access_status = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->accessable_categories ?? [];


            return view('marketplace.category.access_user_categories',compact('parent_categories','user_info','categories','access_status','category_product','user'));
        }

        public function getCheckAccessAndReturnAllUserCategoriesOpen ($id)
        {
            TempUserAccess::where('user_id',$id)->delete();
            $user_info = DB::table('cms_users')->where('id',$id)->first();
            return response()->json($user_info);

        }

        public function getTempAllCategories()
        {
            if(request()->status == "delete"){
                DB::connection('marketplace')->table('user_temp_category_access')->where('user_id', request()->user_id)->delete();
            }
        }
        public function getSerchedAllUserCategories ()
        {
            $request = request();
            $access_status = [];
            $categories = [];
            $access = TempUserAccess::where('user_id', $request->user_id)->first();
            $checked = $request->checked_category ?? [];
            $unchecked = $request->unchecked_category ?? [];
            $parentChecked = $request->checked_parent_category ?? [];
            $parentUnchecked = $request->unchecked_parent_category ?? [];

            if(!empty($access->checked) && !empty($unchecked)){
                $new_db_checked = array_diff($access->checked,$unchecked);//diff old temp access & current uncheck ==> new checked
            }elseif(!empty($access->checked) && empty($unchecked)){
                $new_db_checked = $access->checked; //has old temp access & current uncheck empty ==> set old checked
            }

            if(!empty($access->unchecked) && !empty($checked)){
                $new_db_unchecked = array_diff($access->unchecked,$checked);
            }elseif(!empty($access->unchecked) && empty($unchecked)){
                $new_db_unchecked = $access->unchecked;
            }
             //for parent category log
            if (!empty($access->parentChecked) && !empty($parentUnchecked)) {
                $new_db_parent_checked= array_diff($access->parentChecked, $parentUnchecked); //diff old temp parent access & current parent uncheck ==> new parent checked
            } elseif (!empty($access->parentChecked) && empty($parentUnchecked)) {
                $new_db_parent_checked = $access->parentChecked; //has old temp parent access & current parent uncheck empty ==> set old parent checked
            }

            if (!empty($access->parentUnchecked) && !empty($parentChecked)) {
                $new_db_parent_unchecked = array_diff($access->parentUnchecked, $parentChecked);
            } elseif (!empty($access->parentUnchecked) && !empty($parentUnchecked)) {
                $new_db_parent_unchecked = $access->parentUnchecked;
            }


            if ($access != null) {
                if(!empty($new_db_checked) && !empty($checked)){
                    $final_checked = array_values(array_unique(array_merge($new_db_checked,$checked)));
                }else{
                    $final_checked = $new_db_checked;
                }
                if(!empty($new_db_unchecked) && !empty($unchecked)){
                    $final_unchecked = array_values(array_unique(array_merge($new_db_unchecked,$unchecked)));
                }else{
                    $final_unchecked = $new_db_unchecked;
                }

                if (!empty($new_db_parent_checked) && !empty($parentChecked)) {
                    $final_parent_checked = array_values(array_unique(array_merge($new_db_parent_checked, $parentChecked)));
                } else {
                    $final_parent_checked = $new_db_parent_checked;
                }
                
                if (!empty($new_db_parent_unchecked) && !empty($parentUnchecked)) {
                    $final_parent_unchecked = array_values(array_unique(array_merge($new_db_parent_unchecked, $parentUnchecked)));
                } else {
                    $final_parent_unchecked = $new_db_parent_unchecked;
                }
                $data = [
                    'checked' => $final_checked ?? $checked,
                    'unchecked' => $final_unchecked ?? $unchecked,
                    'parentChecked'=> $final_parent_checked ?? $parentChecked,
                    'parentUnchecked' => $final_parent_unchecked ?? $parentUnchecked
                ];
                TempUserAccess::where('user_id', $request->user_id)->update($data);
            } else {
                $data = [
                    'user_id' => $request->user_id,
                    'checked' => $new_db_checked ?? $checked,
                    'unchecked' => $new_db_unchecked ?? $unchecked,
                    'parentChecked'=> $new_db_parent_checked ?? $parentChecked,
                    'parentUnchecked'=>$new_db_parent_unchecked ?? $parentUnchecked
                ];
                TempUserAccess::create($data);
            }
            if($request->letter == '% Offers'){
                $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('discount_percentage','!=', NULL)->where('end_date','>', now())->get();
                $categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->where('discount_percentage', '!=', NULL)->get();
            } else {
                $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('name','like', $request->letter . '%')->get();
                $categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->where('name', 'like', '%' . $request->letter . '%')->get();
            }
            $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $request->user_id)->select('accessable_categories', 'accessable_parent_categories')->first() ?? [];

            $checked_category = $access->parentChecked;
            $unchecked_category = $access->parentUnchecked;

            if(!empty($checked_category) && !empty($user_access_status->accessable_parent_categories)){
                $merge_all_access_category = array_values(array_unique(array_merge($checked_category, $user_access_status->accessable_parent_categories)));
            }else{
                $merge_all_access_category = $checked_category;
            }
            if(!empty($unchecked_category) && !empty($merge_all_access_category)){
                $temp_access_status = array_diff($merge_all_access_category,$unchecked_category);
            }else{
                $temp_access_status = $merge_all_access_category;
            }
            $access_status = $temp_access_status ? $temp_access_status : ($user_access_status->accessable_parent_categories ?? []);

            return view('marketplace.category.access_user_categories',compact('parent_categories','categories','access_status'));
        }

        public function getSerchedAllUserCategoriesCustomer ()
        {
            $request = request();
            $access_status = [];
            $old_access_status = [];
            $categories = [];
            $user = \App\User::findOrfail($request->user_id);
            $access = TempUserAccess::where('user_id', $request->user_id)->first() ?? [];
            $checked = $request->checked_category ?? [];
            $unchecked = $request->unchecked_category ?? [];
            $parentChecked = $request->checked_parent_category ?? [];
            $parentUnchecked = $request->unchecked_parent_category ?? [];

            if(!empty($access->checked ?? []) && !empty($unchecked)){
                $new_db_checked = array_diff($access->checked ?? [],$unchecked);
            }elseif(!empty($access->checked ?? []) && empty($unchecked)){
                $new_db_checked = $access->checked ?? [];
            }

            if(!empty($access->unchecked) && !empty($checked)){
                $new_db_unchecked = array_diff($access->unchecked,$checked);
            }elseif(!empty($access->unchecked) && empty($unchecked)){
                $new_db_unchecked = $access->unchecked;
            }
            //for parent category log
            if (!empty($access->parentChecked) && !empty($parentUnchecked)) {
                $new_db_parent_checked = array_diff($access->parentChecked, $parentUnchecked);
            } elseif (!empty($access->parentChecked) && empty($parentUnchecked)) {
                $new_db_parent_checked = $access->parentChecked;
            }

            if (!empty($access->parentUnchecked) && !empty($parentChecked)) {
                $new_db_parent_unchecked = array_diff($access->parentUnchecked, $parentChecked);
            } elseif (!empty($access->parentUnchecked) && !empty($parentUnchecked)) {
                $new_db_parent_unchecked = $access->parentUnchecked;
            }

            if ($access != null) {
                if(!empty($new_db_checked) && !empty($checked)){
                    $final_checked = array_values(array_unique(array_merge($new_db_checked,$checked)));
                }else{
                    $final_checked = $new_db_checked;
                }
                if(!empty($new_db_unchecked) && !empty($unchecked)){
                    $final_unchecked = array_values(array_unique(array_merge($new_db_unchecked,$unchecked)));
                }else{
                    $final_unchecked = $new_db_unchecked;
                }

                if (!empty($new_db_parent_checked) && !empty($parentChecked)) {
                    $final_parent_checked = array_values(array_unique(array_merge($new_db_parent_checked, $parentChecked)));
                } else {
                    $final_parent_checked = $new_db_parent_checked;
                }

                if (!empty($new_db_parent_unchecked) && !empty($parentUnchecked)) {
                    $final_parent_unchecked = array_values(array_unique(array_merge($new_db_parent_unchecked, $parentUnchecked)));
                } else {
                    $final_parent_unchecked = $new_db_parent_unchecked;
                }

                $data = [
                    'checked' => $final_checked ?? $checked,
                    'unchecked' => $final_unchecked ?? $unchecked,
                    'parentChecked' => $final_parent_checked ?? $parentChecked,
                    'parentUnchecked' => $final_parent_unchecked ?? $parentUnchecked
                ];            
                TempUserAccess::where('user_id', $request->user_id)->update($data);
            } else {
                $data = [
                    'user_id' => $request->user_id,
                    'checked' => $new_db_checked ?? $checked,
                    'unchecked' => $new_db_unchecked ?? $unchecked,
                    'parentChecked' => $new_db_parent_checked ?? $parentChecked,
                    'parentUnchecked' => $new_db_parent_unchecked ?? $parentUnchecked
                ];
                TempUserAccess::create($data);
            }

            if($request->letter == '% Offers'){
                $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('end_date', '>', now())->where('discount_percentage','!=', NULL)->get();
                $categories = $this->categories->where('is_active',1)->where('discount_percentage', '!=', NULL)->get();
            } else {
                $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('name','like', $request->letter . '%')->get();
                $categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->where('name', 'like', '%' . $request->letter . '%')->get();
            }
            $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $request->user_id)->select('accessable_categories', 'check_accessable_categories','accessable_parent_categories')->first() ?? [];
       
            $checked_parent_category = $access->parentChecked ?? [];
            $unchecked_parent_category = $access->parentUnchecked ?? [];
            $old_access_parent_category = $user_access_status->accessable_parent_categories ?? [];

            if(!empty($checked_parent_category) && !empty($old_access_parent_category)){
                $merge_all_access_parent_category = array_values(array_unique(array_merge($checked_parent_category,$old_access_parent_category)));
            }else{
                $merge_all_access_parent_category = $checked_parent_category;
            }
            if(!empty($unchecked_parent_category) && !empty($merge_all_access_parent_category)){
                $temp_access_status = array_diff($merge_all_access_parent_category,$unchecked_parent_category);
            }else{
                $temp_access_status = $merge_all_access_parent_category;
            }

            if(!is_array($temp_access_status))
            {
            	$temp_access_status = [];
            }
           
            $accessableCategoryIds = $user_access_status->check_accessable_categories ?? [];

            foreach ($accessableCategoryIds as $accessableCategoryId) {
                if (isset($accessableCategoryId['set_by_admin'])) {
                    $access_status[$accessableCategoryId['accessable_categories']] = $accessableCategoryId['set_by_admin'];
                }
            }
            $old_access_status = $access_status;//dont remove this

            $category_subscription = \App\Models\Marketplace\AutoTransferSubscription::where('status', 1)->where('user_id', $request->user_id)->where('end_date', '>', now())->pluck('category_id') ?? [];

            return view('marketplace.category.update_category_load',compact('parent_categories','categories','old_access_status','temp_access_status','user_access_status','category_subscription'));
        }


        public function paymentForMarketplaceAccess($id){

            $user = \App\User::find( CRUDBooster::myParentId() );

            $appDescription = 'Mit dieser automatischen Importfunktion fügen wir neue Produkte aus dieser Kategorie automatisch in deinen Lagerbestand ein. Sollte dein Importlimit erreicht sein, kannst du dieses Upgraden um weitere Produktneuheiten dieser Kategorie automatisch importieren zu lassen. Erfolgt kein Upgrade, werden wir immer bis zu deinem Limit auffüllen. Du kannst diese Automatisierung auch mit anderen Kategorien beliebig kombinieren.';

            if ($user->billing_detail) {
                $billing = $user->billing_detail;
                $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
            }

            \Session::put('mp_auto_subscription_category_id_'.$user->id, request()->category_id);

            $currentSubscription = app(\App\Services\Marketplace\CategoryService::class)->isSubscribedForThisUser(request()->category_id);

            return view('marketplace.marketplace_access_payment-modal', compact('user', 'billing', 'appDescription'));
        }

        // public function getCheckAccessAndReturnAllCategories ($id)
        // {
        //     $user = \App\User::findOrfail($id);
        //     if ( $user->marketplace_access == 1 ) {
        //         $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->accessable_categories ?? [];
        //         $accessableCategoryIds = array_map('intval', $accessableCategoryIds);

        //         // $allCategories                   = Category::select('id', 'name')->where('is_active', 1)->get();
        //         // $accessableCategories            = Category::select('id', 'name')->where('is_active', 1)
        //         // ->whereIn('id', $accessableCategoryIds)->get();
        //         $allCategories                   = $this->categories->select('id', 'name')->where('is_active', 1)->get();
        //         $accessableCategories            = $this->categories->select('id', 'name')->where('is_active', 1)->whereIn('id', $accessableCategoryIds)->get();

        //         $resp = [
        //             'all_categories'        => $allCategories,
        //             'accessable_categories' => $accessableCategories,
        //             'user_name'             => $user->name,
        //             'access_status'         => 1,
        //         ];
        //         return response()->json($resp);
        //     }

        //     // $categories     = Category::where('is_active', 1)->get();
        //     $categories     = $this->categories->where('is_active', 1)->get();
        //     $resp           = [];
        //     foreach ( $categories as $category ) {
        //         $resp[] = [
        //             'id'    => $category->id,
        //             'name'  => $category->name,
        //         ];
        //     }
        //     return response()->json($resp);
        // }

        public function postSaveTemplateIndustries()
        {
        	$request = request();
        	try {
        		if ( $request->category_id && $request->industry_template ) {
        			$category = \App\Models\marketplace\Category::findOrFail($request->category_id);
					$category->update([
						'industry_template' => $request->industry_template,
					]);
        		}
        		CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Industry Template info updated", "success");
        	} catch (\Exception $e) {
        		CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Something went wrong", "error");
        	}
        }

        public function getRemoveIndustryTemplate()
        {
        	try {
        		$category = \App\Models\Marketplace\Category::find(request()->id);
        		$category->update([
        			'industry_template' => null,
        		]);
        		CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Industry template removed", "success");
        	} catch (\Exception $e) {
        		CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Something went wrong", "error");
        	}

        }

		public function categoryProductCount($category_id){
			return Product::where(['status'=> ProductStatus::ACTIVE,'category_id'=> $category_id])->count('id');
		}

        public function postXmlFeedUrl(){
            $select_column = request()->select_column;
            $category_ids = request()->categoryIds;

            try {

                $str = str_shuffle('abscdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!');
                $str = rtrim(mb_strimwidth($str, 0, 12, '', 'UTF-8'));

                $product_feed = new MarketplaceProductExportFeed();
				$product_feed->parent_category_id = "";
                $product_feed->category_id      = $category_ids;
                $product_feed->product_column   = $select_column;
                $product_feed->xml_url   = $str.time();
                $product_feed->save();

                $url = url('').'/mp/downloadxml?id='.$product_feed->xml_url;

                return $url;

            }catch(\Exception $e){
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Something went wrong", "error");
            }
        }
		// public function getExportXml($id)
		public function genarateXml()
		{
            ini_set('max_execution_time', '0');
            ini_set('memory_limit',-1);

            $request_id = request()->id;

            if(empty($request_id)){
                return "Please set the URL feed id";
            }

            $product_feed = MarketplaceProductExportFeed::where('xml_url',$request_id)->first();

            $selected_columns = $product_feed->product_column;
            $cateogory_id = explode(',',$product_feed->category_id);

            if(empty($selected_columns) || empty($cateogory_id)){
                return "Something went wrong";
            }

			$products = Product::where('status',1)
						->whereIn('category_id',$cateogory_id)
						->get();

			if (is_null($products)) {
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Category Products not found", "error");
			}

			$xml = new SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><articles></articles>');

			foreach ($products as $key => $product) {
				$article = $xml->addChild('article');
				$basic_information = $article->addChild('product_basic_information');

                foreach($selected_columns as $column){

                    if($column == 'name'){
                        $basic_information->addChild('product_name', htmlspecialchars($product->name));
                    }else if($column == 'description'){
                        $basic_information->addChild('description', htmlspecialchars($product->description));
                    }else if($column == 'ean'){
                        $basic_information->addChild('ean', $product->ean);
                    }else if($column == 'item_number'){
                        $basic_information->addChild('item_number', $product->item_number);
                    }else if($column == 'uvp'){
                        $basic_information->addChild('uvp', $product->uvp);
                    }else if($column == 'vk_price'){
                        $basic_information->addChild('vk_price', $product->vk_price);
                    }else if($column == 'stock'){
                        $basic_information->addChild('stock', $product->stock);
                    }else if($column == 'delivery_days'){
                        $basic_information->addChild('delivery_days', $product->delivery_days);
                    }else if($column == 'shipping_cost'){
                        $basic_information->addChild('shipping_cost', $product->shipping_cost);
                    }else if($column == 'item_weight'){
                        $basic_information->addChild('item_weight', htmlspecialchars($product->item_weight));
                    }else if($column == 'item_size'){
                        $basic_information->addChild('item_size', htmlspecialchars($product->item_size));
                    }else if($column == 'item_color'){
                        $basic_information->addChild('item_color', htmlspecialchars($product->item_color));
                    }else if($column == 'production_year'){
                        $basic_information->addChild('production_year', htmlspecialchars($product->production_year));
                    }else if($column == 'brand'){
                        $basic_information->addChild('brand', htmlspecialchars($product->brand));
                    }else if($column == 'materials'){
                        $basic_information->addChild('materials', htmlspecialchars($product->materials));
                    }else if($column == 'gender'){
                        $basic_information->addChild('gender', htmlspecialchars($product->gender));
                    }else if($column == 'tags'){
                        $basic_information->addChild('tags', htmlspecialchars($product->tags));
                    }else if($column == 'status'){
                        $basic_information->addChild('status', htmlspecialchars($product->status));
                    }else if($column == 'note'){
                        $basic_information->addChild('note', htmlspecialchars($product->note));
                    }

                    if($column == 'image'){
                        $images = $article->addChild('images');
                        foreach($product->image as $image){
                            $images->addChild('image', htmlspecialchars($image));
                        }
                    }

                    if($column == 'industry_template_data'){
                        $industryTemplateData = json_decode(json_encode($product->industry_template_data), true);
                        $industryTemplateData = $industryTemplateData[$product->mainCategory->industry_template];
                        if(isset($industryTemplateData)){

                            $industry_template_data = $article->addChild('imagindustry_template_dataes');
                            $productCountry = \App\Country::find($product->country_id);

                            if ( $productCountry->country_shortcut == 'en' || $productCountry->country_shortcut == 'uk' ) {
                                $countryShortcut = 'en';
                            } else {
                                $countryShortcut = 'de';
                            }

                            foreach ( $industryTemplateData as $key=>$value ){
                                $industry_template_data->addChild($key, $value[$countryShortcut]);
                            }
                        }
                    }
                }
			}

			$file_path = 'marketplace_product_export/marketplace_products.xml';

			Storage::disk('spaces')->put($file_path, $xml->asXML(), 'public');
			$file = Storage::disk('spaces')->url($file_path);


			header('Content-type: application/x-www-form-urlencode');
			header('Content-Transfer-Encoding: Binary');
			header("Content-disposition: attachment; filename=\"".$file."\"");

        	readfile($file);

			if (Storage::disk('spaces')->exists($file_path)) {
                Storage::disk('spaces')->delete($file_path);
            }

		}

		public function deleteCategory($id){
			ini_set('max_execution_time', '0');
            ini_set('memory_limit',-1);

            $checkProducts = \DB::connection('marketplace')->table('marketplace_products')->whereIn('category_id',$id)->count();

            if($checkProducts>0){
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Can not Delete ! There are products inside selected categories', 'warning');
            }else{
                $products = Product::select('id', 'image')->with('MpCoreDrmTransferProduct:marketplace_product_id,drm_product_id,user_id', 'core_products:marketplace_product_id,id,user_id')->whereIn('category_id', $id)->get();
                foreach ($products->chunk(100) as $product) {
                    dispatch(new BulkMarketplaceProductDelete($product));

                }
                Category::whereIn('id',$id)->delete();
                ApiCategory::whereIn('mp_category_id',$id)->update(['mp_category_id'=>'','is_complete'=>0]);
            }
			
		}

		public function postAssignParentCategory(){
			try{
				$ids = explode(',',request()->categoryIds);
				if(isset(request()->parent_cat_id) && isset($ids)){
					Category::whereIn('id',$ids)->update(['parent_id'=> request()->parent_cat_id]);
					return response()->json(['data'=>true],200);
				}

			} catch (Exception $e) {
				return redirect()->back()->with(['message_type' => 'error', 'message' => $e->getMessage()]);
			}

		}

		public function hasParentIndustryTemplate($parent_category){
			$hasParentIndustryTemplate = MarketplaceParentCategory::where('id',$parent_category)->where('is_active',1)->pluck('industry_template')->first();
			$hasParentIndustryTemplate ? $hasParentIndustryTemplate = $hasParentIndustryTemplate : $hasParentIndustryTemplate = $hasParentIndustryTemplate;
			return $hasParentIndustryTemplate;
		}


        public function getSearchableCategory(){
            $search = request()->search_key;
            $alldata = [];
            $searchableparent = '';
            if($search !=null){
                if(\CRUDBooster::isSuperadmin() || \CRUDBooster::isDropMatrix()){
                    $marketplaceParentCategories = MarketplaceParentCategory::where('is_active',1)->where('name','like', $search. '%')->get();
                    $marketplaceCategories = Category::where('is_active',1)->where('name','like', $search. '%')->get();
                }
                foreach($marketplaceParentCategories as  $parentCategory ){
                    $alldata[] = [
                        'id' => $parentCategory->id,
                        'name' => $parentCategory->name
                    ];
                }
                // dd($alldata);
                // SomeController.php



                foreach($marketplaceCategories as $category){
                    if($category->parent_id){
                        $alldata[] = [
                            'id' => $category->parent_id,
                            'name' => $category->name
                        ];
                    }
                }
                return view('admin.drm_user.search_result',compact('alldata'))->render();
                // foreach($alldata as $data){
                //     $searchableparent .= '<li class="__get" data-id="'.$data['id'].'">'.$data['name'].'</li>';
                //     // $searchableparent .= '<li>'.$data['name'].'</li>';
                // }
                // dd($data);
            }else{
                $searchableparent = '';
            }
            return $searchableparent;
        }

        public function getSearchableCategoryCustomer(){
            $search = request()->search_key;
            $alldata = [];
            $searchableparent = '';
            if($search !=null){
                if(\CRUDBooster::isSuperadmin() || \CRUDBooster::isDropMatrix() ||  \CRUDBooster::isCustomer()){
                    $marketplaceParentCategories = MarketplaceParentCategory::where('is_active',1)->where('name','like', $search. '%')->get();
                    $marketplaceCategories = Category::where('is_active',1)->where('name','like', $search. '%')->get();
                }
                foreach($marketplaceParentCategories as  $parentCategory ){
                    $alldata[] = [
                        'id' => $parentCategory->id,
                        'name' => $parentCategory->name
                    ];
                }

                foreach($marketplaceCategories as $category){
                    if($category->parent_id){
                        $alldata[] = [
                            'id' => $category->parent_id,
                            'name' => $category->name
                        ];
                    }
                }
                return view('marketplace.category.search-result-customer-up',compact('alldata'))->render();

            }else{
                $searchableparent = '';
            }
            return $searchableparent;
        }

        public function getSearchableAllCategory(){
    
            $request = request();
            $access_status = [];
            $categories = [];
            $user = \App\User::findOrfail($request->user_id);
            $access = TempUserAccess::where('user_id', $request->user_id)->first();
            $checked = $request->checked_category ?? [];
            $unchecked = $request->unchecked_category ?? [];
            $parentChecked = $request->checked_parent_category ?? [];
            $parentUnchecked = $request->unchecked_parent_category ?? [];
            if(!empty($access->checked) && !empty($unchecked)){
                $new_db_checked = array_values(array_diff($access->checked,$unchecked));
            }
            if(!empty($access->unchecked) && !empty($checked)){
                $new_db_unchecked = array_values(array_diff($access->unchecked,$checked));
            }
            if (!empty($access->parentChecked) && !empty($parentUnchecked)) {
                $new_db_parent_checked = array_values(array_diff($access->parentChecked, $parentUnchecked));
            }
            if (!empty($access->parentUnchecked) && !empty($parentChecked)) {
                $new_db_parent_unchecked = array_values(array_diff($access->parentUnchecked, $parentChecked));
            }
            if ($access != null) {
                $final_checked = array_values(array_unique(array_merge(
                    $new_db_checked ?? [],
                    $checked ?? []
                )));
                $final_unchecked = array_values(array_unique(array_merge(
                    $new_db_unchecked ?? [],
                    $unchecked ?? []
                )));

                $final_parent_checked = array_values(array_unique(array_merge(
                    $new_db_parent_checked ?? [],
                    $parentChecked ?? []
                )));
                $final_parent_unchecked = array_values(array_unique(array_merge(
                    $new_db_parent_unchecked ?? [],
                    $parentUnchecked ?? []
                )));

                $data = [
                    'checked' => $final_checked ?? $checked,
                    'unchecked' => $final_unchecked ?? $unchecked,
                    'parentChecked' => $final_parent_checked ?? $parentChecked,
                    'parentUnchecked' => $final_parent_unchecked ?? $parentUnchecked
                ];
                $access->update($data);
            } else {
                $data = [
                    'user_id' => $request->user_id,
                    'checked' => $new_db_checked ?? $checked,
                    'unchecked' => $new_db_unchecked ?? $unchecked,
                    'parentChecked' => $new_db_parent_checked ?? $parentChecked,
                    'parentUnchecked' => $new_db_parent_unchecked ?? $parentUnchecked,
                    'created_at' => now(),
                    'updated_at' => now()
                ];
                TempUserAccess::create($data);
            }
            $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('id', $request->search_key)->get();
            $categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->where('id', $request->search_key)->get();
            $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $request->user_id)->select('accessable_categories', 'accessable_parent_categories')->first() ?? [];
            $checked_parent_category = $access->parentChecked;
            $unchecked_parent_category = $access->parentUnchecked;
            $old_access_parent_category = $user_access_status->accessable_parent_categories;

            if(!empty($checked_parent_category) && !empty($old_access_parent_category)){
                $merge_all_access_parent_category = array_values(array_unique(array_merge($checked_parent_category,$old_access_parent_category)));
            }elseif(empty($checked_parent_category) && !empty($old_access_parent_category)){
                $merge_all_access_parent_category = $old_access_parent_category;
            }elseif(!empty($checked_parent_category) && empty($old_access_parent_category)){
                $merge_all_access_parent_category = $checked_parent_category;
            }
  
            if(!empty($unchecked_parent_category) && !empty($merge_all_access_parent_category)){
                $temp_access_status = array_diff($merge_all_access_parent_category, $unchecked_parent_category);
            }elseif(empty($unchecked_parent_category) && !empty($merge_all_access_parent_category)){
                $temp_access_status = $merge_all_access_parent_category;
            }elseif(!empty($unchecked_parent_category) && empty($merge_all_access_parent_category)){
                $temp_access_status = $unchecked_parent_category;
            }
            $access_status = $temp_access_status ? $temp_access_status : $user_access_status->accessable_parent_categories;
            return view('marketplace.category.access_user_categories',compact('parent_categories','categories','access_status'));
        }

        public function getSearchableAllCategoryCustomer(){

            $request = request();
            $access_status = [];
            $old_access_status = [];
            $categories = [];
            $user = \App\User::findOrfail($request->user_id);
            $access = TempUserAccess::where('user_id', $request->user_id)->first() ?? [];
            $checked = $request->checked_category ?? [];
            $unchecked = $request->unchecked_category ?? [];
            $parentChecked = $request->checked_parent_category ?? [];
            $parentUnchecked = $request->unchecked_parent_category ?? [];
        
            if(!empty($access->checked) && !empty($unchecked)){
                $new_db_checked = array_values(array_diff($access->checked,$unchecked));
            }
            
            if(!empty($access->unchecked) && !empty($checked)){
                $new_db_unchecked = array_values(array_diff($access->unchecked,$checked));
            }

            if (!empty($access->parentChecked) && !empty($parentUnchecked)) {
                $new_db_parent_checked = array_values(array_diff($access->parentChecked, $parentUnchecked));
            }

            if (!empty($access->parentUnchecked) && !empty($parentChecked)) {
                $new_db_parent_unchecked = array_values(array_diff($access->parentUnchecked, $parentChecked));
            }

            if ($access != null) {
                if(!empty($new_db_checked) && !empty($checked)){
                    $final_checked = array_values(array_unique(array_merge($new_db_checked,$checked)));
                }else{
                    $final_checked = $new_db_checked;
                }
                if(!empty($new_db_unchecked) && !empty($unchecked)){
                    $final_unchecked = array_values(array_unique(array_merge($new_db_unchecked,$unchecked)));
                }else{
                    $final_unchecked = $new_db_unchecked;
                }

                if (!empty($new_db_parent_checked) && !empty($parentChecked)) {
                    $final_parent_checked = array_values(array_unique(array_merge($new_db_parent_checked, $parentChecked)));
                } else {
                    $final_parent_checked = $new_db_parent_checked;
                }

                if (!empty($new_db_parent_unchecked) && !empty($parentUnchecked)) {
                    $final_parent_unchecked = array_values(array_unique(array_merge($new_db_parent_unchecked, $parentUnchecked)));
                } else {
                    $final_parent_unchecked = $new_db_parent_unchecked;
                }

                $data = [
                    'checked' => $final_checked ?? $checked,
                    'unchecked' => $final_unchecked ?? $unchecked,
                    'parentChecked' => $final_parent_checked ?? $parentChecked,
                    'parentUnchecked' => $final_parent_unchecked ?? $parentUnchecked
                ];
                TempUserAccess::where('user_id', $request->user_id)->update($data);
            } else {
                $data = [
                    'user_id' => $request->user_id,
                    'checked' => $new_db_checked ?? $checked,
                    'unchecked' => $new_db_unchecked ?? $unchecked,
                    'parentChecked' => $new_db_parent_checked ?? $parentChecked,
                    'parentUnchecked' => $new_db_parent_unchecked ?? $parentUnchecked
                ];
                TempUserAccess::create($data);
            }
            $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('id', $request->search_key)->get();
            $categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->where('id', $request->search_key)->get();
            $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $request->user_id)->select('accessable_categories', 'check_accessable_categories', 'accessable_parent_categories')->first() ?? [];

            $checked_parent_category = $access->parentChecked ?? [];
            $unchecked_parent_category = $access->parentUnchecked ?? [];
            $old_access_parent_category = $user_access_status->accessable_parent_categories ?? [];

            if(!empty($checked_parent_category) && !empty($old_access_parent_category)){
                $merge_all_access_parent_category = array_values(array_unique(array_merge($checked_parent_category,$old_access_parent_category)));
            }elseif(empty($checked_parent_category) && !empty($old_access_parent_category)){
                $merge_all_access_parent_category = $old_access_parent_category;
            }elseif(!empty($checked_parent_category) && empty($old_access_parent_category)){
                $merge_all_access_parent_category = $checked_parent_category;
            }
            if(!empty($unchecked_parent_category) && !empty($merge_all_access_parent_category)){
                $temp_access_status = array_diff($merge_all_access_parent_category,$unchecked_parent_category);
            }elseif(empty($unchecked_parent_category) && !empty($merge_all_access_parent_category)){
                $temp_access_status = $merge_all_access_parent_category;
            }elseif(!empty($unchecked_parent_category) && empty($merge_all_access_parent_category)){
                $temp_access_status = $unchecked_parent_category;
            }

            if(!is_array($temp_access_status))
            {
            	$temp_access_status = [];
            }

            $accessableCategoryIds = $user_access_status->check_accessable_categories ?? [];
            foreach ($accessableCategoryIds as $accessableCategoryId) {
                if (isset($accessableCategoryId['set_by_admin'])) {
                    $access_status[$accessableCategoryId['accessable_categories']] = $accessableCategoryId['set_by_admin'];
                }
            }
            if (empty($access_status)) {
                $access_status = $accessableCategoryIds;
            } else {
                $accessableCategoryIds = array_keys($access_status);
            }
            $old_access_status = $access_status;//dont remove this

            $category_subscription = \App\Models\Marketplace\AutoTransferSubscription::where('status', 1)->where('user_id', $request->user_id)->where('end_date', '>', now())->pluck('category_id') ?? [];

            return view('marketplace.category.update_category_load',compact('parent_categories','categories', 'old_access_status','temp_access_status','user_access_status','category_subscription'));
        }


        // public function getSearchableAllCategoryCustomer(){
        //     $request = request();
        //     $id = $request->user_id;


        //     // dd(request()->search_key);
        //     // $marketplaceParentCategories = MarketplaceParentCategory::where('is_active',1)->where('id', request()->search_key)->get();

        //     // dd($marketplaceParentCategories);


        //     // dd($request->checked_category,$request->unchecked_category);
        //     //$access_status = [];
        //     $categories = [];
        //     $user = \App\User::findOrfail($request->user_id);

        //     $access = DB::connection('marketplace')->table('user_temp_category_access')->where('user_id', $request->user_id)->first();
        //     $checked = $request->checked_category;
        //     $unchecked = $request->unchecked_category;

        //     // if(empty($checked)){
        //     //     $checked=[null];
        //     // }

        //     // if(empty($unchecked)){
        //     //     $unchecked=[null];
        //     // }


        //     if(!empty(json_decode($access->checked)) && !empty($unchecked)){
        //             $new_db_checked = array_values(array_diff(json_decode($access->checked),$unchecked));
        //     }
        //     // dd($checked,$unchecked,json_decode($access->checked),json_decode($access->unchecked),$new_db_checked);
        //     if(!empty(json_decode($access->unchecked)) && !empty($checked)){
        //             $new_db_unchecked = array_values(array_diff(json_decode($access->unchecked),$checked));
        //     }

        //     if ($access != null) {
        //         if(!empty($new_db_checked) && !empty($checked)){
        //             $final_checked = array_values(array_unique(array_merge($new_db_checked,$checked)));
        //         }else{
        //             $final_checked = $new_db_checked;
        //         }
        //         if(!empty($new_db_unchecked) && !empty($unchecked)){
        //             $final_unchecked = array_values(array_unique(array_merge($new_db_unchecked,$unchecked)));
        //         }else{
        //             $final_unchecked = $new_db_unchecked;
        //         }

        //         $data = [
        //             'checked' => $final_checked ?? $checked,
        //             'unchecked' => $final_unchecked ?? $unchecked,
        //         ];
        //         // dd($data);
        //         DB::connection('marketplace')->table('user_temp_category_access')->where('user_id', $request->user_id)->update($data);
        //     } else {
        //         $data = [
        //             'user_id' => $request->user_id,
        //             'checked' => $new_db_checked ?? json_encode($request->checked_category),
        //             'unchecked' => $new_db_unchecked ?? json_encode($request->unchecked_category),
        //             'created_at' => now(),
        //             'updated_at' => now()
        //         ];
        //         // dd($data);
        //         DB::connection('marketplace')->table('user_temp_category_access')->insert($data);
        //     }

        //     $category_product = array_unique(MarketplaceProducts::where('status',1)->pluck('category_id')->toArray());
        //     $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->where('id', $request->search_key)->get();
        //     $categories = $this->categories->where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->where('id', $request->search_key)->get();
        //     $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $request->user_id)->first()->accessable_categories ?? [];


        //     $old_checked_category = DB::connection('marketplace')->table('user_temp_category_access')->where('user_id', request()->user_id)->get();
        //     $checked_category = json_decode($old_checked_category[0]->checked);
        //     $unchecked_category = json_decode($old_checked_category[0]->unchecked);


        //     $old_local_access = DB::connection('marketplace')->table('marketplace_user_accesses')->where('user_id', request()->user_id)->get();
        //     $old_access_category = json_decode($old_local_access[0]->accessable_categories);
        //     if(!empty($checked_category) && !empty($old_access_category)){
        //         $merge_all_access_category = array_values(array_unique(array_merge($checked_category,$old_access_category)));
        //     }elseif(empty($checked_category) && !empty($old_access_category)){
        //         $merge_all_access_category = $old_access_category;
        //     }elseif(!empty($checked_category) && empty($old_access_category)){
        //         $merge_all_access_category = $checked_category;
        //     }
        //     if(!empty($unchecked_category) && !empty($merge_all_access_category)){
        //         $temp_access_status = array_diff($merge_all_access_category,$unchecked_category);
        //     }elseif(empty($unchecked_category) && !empty($merge_all_access_category)){
        //         $temp_access_status = $merge_all_access_category;
        //     }elseif(!empty($unchecked_category) && empty($merge_all_access_category)){
        //         $temp_access_status = $unchecked_category;
        //     }

        //     $accessableCategory = [];
        //     $access_status = [];

        //     $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
        //         foreach($accessableCategoryIds as $accessableCategoryId){
        //             if(isset($accessableCategoryId['set_by_admin'])){
        //                 array_push($accessableCategory, [$accessableCategoryId['set_by_admin'] => $accessableCategoryId['accessable_categories']]);
        //             }
        //         }

        //         foreach($accessableCategory as $Category){
        //             $allCategory[] = current($Category);
        //         }
        //         if(empty($allCategory)){
        //             $accessableCategoryIds = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
        //         }else{
        //             $accessableCategoryIds = array_map('intval', $allCategory);
        //         }

        //         foreach($accessableCategory as $category){
        //         $access_status[current($category)] = key($category);
        //         }
        //         if(empty($access_status)){
        //             $access_status = \App\Models\Marketplace\UserAccess::where('user_id', $id)->first()->check_accessable_categories ?? [];
        //         }

        //     //$access_status = $temp_access_status ? $temp_access_status : $user_access_status;
        //     // dd($parent_categories);
        //     return view('marketplace.category.update_category_load',compact('parent_categories','categories','access_status','category_product'));
        // }

        public function userMpCategoryLimit($user_id){
            $mp_cat_limit = 0;
            $user_info = \App\User::find($user_id);

            $is_dt_user = DB::table('user_group_relations')
                ->where('user_id', $user_id)
                ->where('group_id', 2)
                ->exists();

            $is_dt_new_user = $is_dt_user && checkTariffEligibility($user_id);

            if($user_info){
                if(checkTariffEligibility($user_id)){

                    // $check_trial_start = DB::table('app_trials')->where('user_id', $user_id)->exists();

                    $user_plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
                    
                    if(in_array($user_plan['plan'], ['Free', 'Trial'])){
                        // $trial_remain_days = app('App\Http\Controllers\AdminDrmImportsController')->checkImportTrial($user_id);
                        
                        // if($trial_remain_days > 0){
                            $mp_cat_limit = 9999999999;
                        // }else{
                        //     $mp_cat_limit = $user_info->category_quantity ?? 0;
                        // }
                    }else if(in_array($user_plan['plan'], ['500 Free Products'])){
                        $total_user_mp_cat = \App\Models\Marketplace\UserAccess::where('user_id', $user_id)->value('accessable_parent_categories');
                        $total_user_mp_cat = $total_user_mp_cat ? count(array_filter($total_user_mp_cat)) : 1;

                        $mp_cat_limit = $total_user_mp_cat;
                    }else if(in_array($user_plan['plan'], ['Assigned', 'Purchased'])){
                        $mp_cat_limit = $user_info->category_quantity;
                    }

                    if($is_dt_new_user){
                        $mp_cat_limit = $user_id == 4047 ? 15 : 10;
                    }

                    if($user_id == 4293){
                        $mp_cat_limit = 9999999999;
                    }
                }else{
                    $mp_cat_limit = $user_info->category_quantity ?? 10;
                }
            }

            return $mp_cat_limit;
        }

    }
