<?php

use League\Flysystem\Cached\Storage\Predis;

    namespace App\Http\Controllers;
    use App\MarketplaceProducts;
    use DataTables as DataTables;
    use Illuminate\Support\Facades\Request;
    use Illuminate\Support\Facades\Session;
	use App\Enums\SourceType;
    use App\Enums\VisibilityStatus;
    use App\Model\UserGroup;
    use App\Models\Marketplace\Category;
    use App\Models\Marketplace\Collection;
    use App\Models\Marketplace\UserGroup as MarketplaceUserGroup;
    use App\Services\Marketplace\CategoryService;
    use App\Services\Marketplace\CollectionService;
    use App\Services\Marketplace\ProductService;
    use Illuminate\Support\Facades\Redis;
    use Carbon\Carbon;
    use Illuminate\Support\Arr;
    use Illuminate\Support\Facades\Cache;
    use Illuminate\Support\Facades\Route;
    use Illuminate\Support\Facades\Schema;
    use Illuminate\Support\Facades\Storage;
    use Illuminate\Support\Facades\Validator;
    use Illuminate\Support\LazyCollection;
    use Illuminate\Support\Str;
    use PhpOffice\PhpSpreadsheet\IOFactory;
    use DB;
    use CB;
    use CRUDBooster;
    use File;
    use ZipArchive;
    use App\User;
    use App\Enums\CollectionStatus;
    use App\Enums\Marketplace\ProductStatus;
    use App\Enums\Marketplace\FakeSuppliers;
    use App\Enums\Marketplace\PriceRange;
    use App\Enums\Marketplace\ShippingMethod;
    use App\Models\Marketplace\MarketplaceParentCategory;
    use Illuminate\Support\Facades\Mail;
    use App\Mail\DRMSEndMail;
    use App\Notifications\DRMNotification;
    use App\Models\Marketplace\Product;
    use Log;
    use App\Jobs\UpdateProductsUvp;
    use App\Jobs\DownloadEanList;
    use App\Models\Export\MarketplaceProductsExport;
    use Maatwebsite\Excel\Facades\Excel;
    use App\Jobs\Marketplace\MarketplaceReviewAndRating;
    use App\Models\Marketplace\AdditionalInfo;
    use App\Models\Marketplace\ProductSafetyGPSR;
    use App\Models\ChannelUserCategory;
    use App\Models\Marketplace\MpCoreDrmTransferProduct;
    use App\Models\MarketplaceProfitCalculation;
    use Illuminate\Http\Request as Req;
    use Illuminate\Http\JsonResponse;

	class AdminMarketplaceCollectionsController extends \crocodicstudio\crudbooster\controllers\MarketplaceCBController {
		protected $suppliersService;

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "name";
			$this->suppliersService = new \App\Services\Marketplace\SuppliersService();
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = true;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = false;
			$this->button_edit = false;
			$this->button_delete = true;
			$this->button_detail = false;
			$this->button_show = false;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "marketplace_collections";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
            $this->col[] = ["label"=>"Supplier","name"=>"delivery_company_id", 'callback_php'=>'$this->getDeliveryCompanyName($row->delivery_company_id)'];
			$this->col[] = ["label"=>"Name","name"=>"name"];
            $this->col[] = ["label"=> __('menu.source_type') ,"name"=>"source_type"];
            $this->col[] = ["label"=>"Download Collection","name"=>"csv_file_link"];
            $this->col[] = ["label"=>"Import Time","name"=>"created_at"];
            $this->col[] = ["label"=>"Total Products", "name"=>"id"];
			//$this->col[] = ["label"=>"Category","name"=>"category_id","join"=>"marketplace_categories,name"];
			$this->col[] = ["label"=>"Status", 'callback_php'=>'$this->generateCollectionStatusColumn($row->id)'];
			$this->col[] = ["label"=>"Last Update","name"=>"updated_csv"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Name','name'=>'name','type'=>'text','validation'=>'required|string|min:3|max:70','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
            $this->form[] = ['label'=>'Category','name'=>'category_id','type'=>'select','datatable'=>'marketplace_categories,name','width'=>'col-sm-10'];
//			$this->form[] = ['label'=>'Supplier','name'=>'supplier_id','type'=>'select','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'cms_users,name','datatable_where'=>'cms_users.id_cms_privileges=4'];
            $this->form[] = ['label'=>'Source Type','name'=>'source_type','type'=>'radio','dataenum'=>'0|URL;1|File', 'value' => 0];
//			$this->form[] = ['label'=>'Status','name'=>'status','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Name","name"=>"name","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
			//$this->form[] = ["label"=>"Category Id","name"=>"category_id","type"=>"select2","required"=>TRUE,"validation"=>"required|min:1|max:255","datatable"=>"category,id"];
			//$this->form[] = ["label"=>"Supplier Id","name"=>"supplier_id","type"=>"select2","required"=>TRUE,"validation"=>"required|min:1|max:255","datatable"=>"supplier,id"];
			//$this->form[] = ["label"=>"Source Type","name"=>"source_type","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Status","name"=>"status","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Product Profit Type","name"=>"product_profit_type","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			//$this->form[] = ["label"=>"Product Profit Price","name"=>"product_profit_price","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();
            $this->addaction[] = ['label'=>'Product Detail','url'=> CRUDBooster::mainpath('product-detail/[id]'),'color'=>'success'];
            $this->addaction[] = ['label'=>'Manual Update Csv','url'=>CRUDBooster::mainpath('manual-update-csv/[id]'),'color'=>'warning', 'icon'=>'fa fa-edit'];

	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();
            $this->button_selected[] = ['label'=>'New Collection','icon'=>'fa fa-plus','name'=>'generate_collection'];


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();

	        $this->pendingCountMapArr = [];
	        $this->mpProductCountMap = DB::connection('marketplace')->table('marketplace_products')
                ->select(DB::connection('marketplace')->raw('count(*) as pending_count, collection_id'))
                ->where('status', 0)
                ->whereNotNull('collection_id')
                ->groupBy('collection_id')
                ->get()->toArray();

            array_map(function($a) {
                $this->pendingCountMapArr[$a->collection_id] = $a->pending_count;
            }, $this->mpProductCountMap);

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here
            if ( \CRUDBooster::isSupplier() ) {
            $supplier_id = (CRUDBooster::myParentId()) ? CRUDBooster::myParentId() : CRUDBooster::myParentId();
            $query->where('supplier_id', $supplier_id);
            }
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    protected $current_id;

	    public function hook_row_index($column_index,&$column_value) {


            if($column_index == 3) {
                $column_value = trans('collection.source_type.'.$column_value);
            }
            if($column_index == 4){
                // $column_value = str_replace('https://drm-file.fra1.digitaloceanspaces.com/', '', $column_value);
				$html = '<a class="btn btn-xs btn-primary" href="'.$column_value.'?download=1" target="_blank" title="Download File"><i class="fa fa-download"></i> Download</a>';
				$column_value = $html;
			}
			if ($column_index == 6) {
				// $column_value = Product::where('collection_id', $column_value)->count();
                $column_value = DB::connection('marketplace')->table('marketplace_products')->where('collection_id', $column_value)->count();
			}
//            if($column_index == 7) {
//                $column_value = ($column_value == 0) ? '<span class="btn btn-xs btn-warning" style="width: 100%">'.trans('collection.status.'.$column_value).'</span>' : '<span class="btn btn-xs btn-success"  style="width: 100%">'.trans('collection.status.'.$column_value).'</span>';
//            }
            if ( $column_index == 8 ) {
                if ( $column_value != NULL ) {
                     $column_value = json_decode($column_value, true);
                    $column_value = '<a href="'.$column_value['file_link'].'" target="_blank"><button type="button" class="btn btn-xs btn-info"><i class="fa fa-download"></i> Last Update File</button><br />'.
                        Carbon::parse($column_value['updated_at'])->format('Y m d - h:i:s').'</a>';
                } else {
                    $column_value = 'N/A';
                }
            }
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here
            // \App\Models\Marketplace\Product::where('collection_id', $id)->delete();
            if(is_array($id)){
                // Product::whereIn('collection_id', $id)->delete();
                $productIds = Product::whereIn('collection_id', $id)->pluck('id')->toArray();
            }else{
                // Product::where('collection_id', $id)->delete();
                $productIds = Product::where('collection_id', $id)->pluck('id')->toArray();
            }
            foreach(array_chunk($productIds, 5000) as $mp_products){
                $mpCoreDrmTransferProducts = MpCoreDrmTransferProduct::whereIn('marketplace_product_id',  $mp_products)->select('drm_product_id', 'user_id')->get() ?? [];
                if(!empty($mpCoreDrmTransferProducts)){
                    foreach($mpCoreDrmTransferProducts as $mpCoreDrmTransferProduct){
                        app(\App\Services\DRMProductService::class)->destroy($mpCoreDrmTransferProduct->drm_product_id,$mpCoreDrmTransferProduct->user_id);
                    }
                }
                Product::whereIn('id', $mp_products)->delete();
            }
	    }



	    //By the way, you can still create your own method in here... :)
//        public function getAdd() {
//            //Create an Auth
//            if(!CRUDBooster::isCreate() && $this->global_privilege==FALSE || $this->button_add==FALSE) {
//                CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
//            }
//
//            $data = [];
//            $data['page_title'] = 'Add Collection';
//
//            //Please use cbView method instead view method from laravel
//            $this->cbView('marketplace.collection.add',$data);
//        }



        public function postActionSelected()
        {
            $this->cbLoader();
            $id_selected = \Illuminate\Support\Facades\Request::input('checkbox');
            $button_name = Request::input('button_name');

            if (! $id_selected) {
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans("crudbooster.alert_select_a_data"), 'warning');
            }

            if($button_name == 'generate_collection') {
                $collections = Collection::whereIn('id', $id_selected)->where('source_type', '<>', SourceType::GENERATED)->get();

                $userGroups = MarketplaceUserGroup::select('id', 'name')->where('status', VisibilityStatus::ACTIVE)->get();

                $categories = Category::all();

                return view('marketplace.collection.preview', compact('collections', 'categories', 'userGroups'));
            }

            if ($button_name == 'delete') {
                if (!CRUDBooster::isDelete()) {
                    CRUDBooster::insertLog(trans("crudbooster.log_try_delete_selected", ['module' => CRUDBooster::getCurrentModule()->name]));
                    CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
                }

                $this->hook_before_delete($id_selected);
                $tablePK = CB::pk($this->table);
                if (CRUDBooster::isColumnExists($this->table, 'deleted_at')) {

                    \Illuminate\Support\Facades\DB::connection('marketplace')->table($this->table)->whereIn($tablePK, $id_selected)->update(['deleted_at' => date('Y-m-d H:i:s')]);
                } else {
                    DB::connection('marketplace')->table($this->table)->whereIn($tablePK, $id_selected)->delete();
                }
                CRUDBooster::insertLog(trans("crudbooster.log_delete", ['name' => implode(',', $id_selected), 'module' => CRUDBooster::getCurrentModule()->name]));

                $this->hook_after_delete($id_selected);

                $message = trans("crudbooster.alert_delete_selected_success");

                return redirect()->back()->with(['message_type' => 'success', 'message' => $message]);
            }

            $action = str_replace(['-', '_'], ' ', $button_name);
            $action = ucwords($action);
            $type = 'success';
            $message = trans("crudbooster.alert_action", ['action' => $action]);

            if ($this->actionButtonSelected($id_selected, $button_name) === false) {
                $message = ! empty($this->alert['message']) ? $this->alert['message'] : 'Error';
                $type = ! empty($this->alert['type']) ? $this->alert['type'] : 'danger';
            }

            return redirect()->back()->with(['message_type' => $type, 'message' => $message]);
        }

        public function postCollectionProducts()
        {
            try {
                //DB::beginTransaction();
                $request = request();
                $productListingPrices = [];
                foreach ($request->product_listing_price as $userGroupId => $item) {
                    foreach ($item as $subscription => $price) {
                        if(!empty(floatval($price))) {
                            $productListingPrices[] = [
                                'subscription_type' => $subscription,
                                'price' => floatval($price),
                                'user_group' => $userGroupId,
                            ];
                        }
                    }
                }

                $collection = app(CollectionService::class)->store(array_merge(
                    $request->all(), ['status' => VisibilityStatus::ACTIVE]
                ));

                foreach ($request->collection_listing_price as $userGroupId => $item) {
                    foreach ($item as $subscription => $price) {
                        if(!empty(floatval($price))) {
                            $collection->listing_prices()->create([
                                'subscription_type' => $subscription,
                                'price' => floatval($price),
                                'user_group' => $userGroupId,
                                'listable_id' => $collection->id,
                                'listable_type' => get_class($collection),
                            ]);
                        }
                    }
                }

                app(ProductService::class)->saveCollectionProducts(
                    array_merge(
                        $request->only('product_ids', 'product_prices', 'product_titles'),
                        ['collection_id' => $collection->id, 'category_id' => $collection->category_id, 'product_listing_price' => $productListingPrices]
                    )
                );
                //DB::commit();

                CRUDBooster::redirect(CRUDBooster::mainpath(), trans("crudbooster.alert_add_data_success"), 'success');
            } catch (\Exception $exception) {
                //DB::rollBack();
                CRUDBooster::redirect(CRUDBooster::mainpath(), trans("crudbooster.alert_add_data_failed"), 'error');
            }
        }

        public function getImportData()
        {
            $this->cbLoader();

            if ( !$this->suppliersService->hasAccess('NEW_IMPORT_OF_PRODUCTS') ) {
                CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
            }

            // for no limit exicution
            ini_set('max_execution_time', '0');
            ini_set('memory_limit', -1);

            try {
                $data['page_menu']  = \Route::getCurrentRoute()->getActionName();
                // $data['page_title'] = 'Import Data '.(CRUDBooster::getCurrentModule()->name);
                $data['page_title'] = __('Import Data Marketplace Collection');
                $data['categories'] = app(CategoryService::class)->all();
                // $data['main_categories'] = app(CategoryService::class)->mainCategories();
                if(is_relavida() || in_array(CRUDBooster::myParentId(), [2817])){
                    $data['main_categories'] = ChannelUserCategory::where('user_id', 2694)->where('channel', 10)->where('parent',0)->orderBy('category_name')->get()->unique('category_name');
                    $data['parent_categories'] = ChannelUserCategory::with('child_category')->where('user_id', 2694)->where('channel', 10)->where('parent', '!=', 0)->orderBy('category_name')->get()->unique('category_name');
                } else {
                    $data['main_categories'] = Category::where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->orderBy('name')->get();
                    $data['parent_categories'] = MarketplaceParentCategory::withCount('category')->where('is_active',1)->orderBy('name')->get(['id','name']);
                }

                $data['suppliers'] = app(\App\Services\Marketplace\ProductService::class)->getDeliveryCompanies();
                $data['collections'] = DB::connection('marketplace')->table('marketplace_collections')->where('status', 1)->get();
                if(!request()->category_map && Request::get('file')){

                    $file = base64_decode(Request::get('file'));
                    $type = pathinfo($file, PATHINFO_EXTENSION);
                    $rows = $this->csvToArray($file,$type,'auto',false);
                    
                    $demoData = isset($rows[0]) ? $rows[0] : [];
                    Cache::store('file')->forget('mp_csv_content'.'-'.CRUDBooster::myParentId());
                    Session::forget('demo_preview');
                    Session::put('demo_preview', json_encode($demoData));

                    // Cache::put('mp_csv_content'.'-'.CRUDBooster::myParentId(),$rows);
                    Cache::store('file')->put('mp_csv_content'.'-'.CRUDBooster::myParentId(),json_encode($rows));

                    $countRows = ($rows)?count($rows):0;
                    Session::put('total_data_import', $countRows);

                    $data_import_column = [];
                    foreach ($rows as $value) {
                        $a = [];
                        foreach ($value as $k => $v) {
                            $a[] = $k;
                        }
                        if ($a && count($a)) {
                            $data_import_column = $a;
                        }
                        break;
                    }

                    $data['data_import_column'] = $data_import_column;
                    Session::put('data_import_column'.CRUDBooster::myParentId(),$data_import_column);
                    $data['category_map'] = '';
                }else if(request()->category_map){
                    $data['category_map'] = 'true';

                    $table_columns = Schema::connection('marketplace')->getColumnListing('marketplace_products');
                    $additionalInfo = Schema::connection('marketplace')->getColumnListing('mp_product_additional_info') ?? [];
                    $gpsrInfo = Schema::connection('marketplace')->getColumnListing('marketplace_product_safety_attributes') ?? [];
                    $table_columns = array_unique(array_merge($table_columns,$additionalInfo));
                    $table_columns = array_unique(array_merge($table_columns,$gpsrInfo));
                    $labels = [];
                    foreach( $table_columns as $col ) {
                        $labels[$col] = ucwords(str_replace("_", " ", $col));
                    }
                    $data['category_id'] = [];
                    if(!empty(Session::get('csv_category_column'.CRUDBooster::myParentId())) && !empty(Session::get('categories'.CRUDBooster::myParentId()))){
                            foreach(Session::get('categories'.CRUDBooster::myParentId()) as $key => $val){
                                if(!in_array($val,$data['category_id'])) {
                                    array_push($data['category_id'], $val);
                                }
                            }
                    }else{
                        array_push($data['category_id'], Session::get('system_category'.CRUDBooster::myParentId()));
                    }

                    $data['table_columns'] = $table_columns;
                    $data['data_import_column'] = Session::get('data_import_column'.CRUDBooster::myParentId());
                    $data['labels'] = $labels;
                    $data['all_country'] = DB::table('countries')->get();
                }
                
                if(Request::get('country_id')){
                    $data['country'] = DB::table('countries')->where('id', Request::get('country_id'))->first();
                }
                $this->cbView('marketplace.collection.import', $data);
            } catch (\Exception $exception) {
                return redirect(CRUDBooster::mainpath('import-data'))->with(['message' => 'Something went wrong', 'message_type' => 'error']);
            }
            
        }

        public function postPreviewData()
        {
            $colMap = [
                'Name' => 'name',
                'Item Number' => 'item_number',
                'Ean' => 'ean',
                'Description' => 'description',
                'Image' => 'image',
                'Ek price' => 'ek_price',
                'UVP' => 'uvp',
                'Stock' => 'stock',
                // 'Category' => 'category'
            ];
            if(request()->get('ship_method') == 1){
                $colMap = [
                    'Name' => 'name',
                    'Item Number' => 'item_number',
                    'Ean' => 'ean',
                    'Description' => 'description',
                    'Image' => 'image',
                    'Ek price' => 'ek_price',
                    'UVP' => 'uvp',
                    'Stock' => 'stock',
                    // 'Category' => 'category',
                    'Shipping Cost' => 'shipping_cost',
                    'Handling Time' => 'delivery_days',
                ];
            }else if(request()->get('ship_method') == 2){
                $colMap = [
                    'Name' => 'name',
                    'Item Number' => 'item_number',
                    'Ean' => 'ean',
                    'Description' => 'description',
                    'Image' => 'image',
                    'Ek price' => 'ek_price',
                    'UVP' => 'uvp',
                    'Item Weight' => 'item_weight',
                    'Item Length' => 'product_length',
                    'Item Width'  => 'product_width',
                    'Item Height' => 'product_height'
                ];
            }

            $row = [];
            $html = '<tr>';
            $selectedColumns = request()->get('selected_columns');

            $demoData = json_decode(Session::get('demo_preview'));
            $ek_price = 0;
            foreach ($colMap as $key => $col) {
                $filed_name[] =$col;
                $row[$key] = !empty($selectedColumns[$col]) ? data_get($demoData, (is_array($selectedColumns[$col])? $selectedColumns[$col][0]: $selectedColumns[$col])) : '';
                if($col == 'name'){
                    $html .= '{!! <td style="white-space: normal;">' . $row[$key] . '</td> !!}';
                } elseif ($col == 'image' && !empty($row[$key])) {
                    $images_array = [];
                    if(count($selectedColumns[$col])>1){
                        foreach($selectedColumns[$col] as $imageses){
                            array_push($images_array, data_get($demoData, $imageses));
                        }
                    }else{
                        $images_array[] =$row[$key];
                    }
                    $images = $this->image_processing($images_array,$selectedColumns['image_prefix'],$selectedColumns['image_suffix'],$selectedColumns['image_separator']);
                   
                    if(count($selectedColumns[$col])>1){
                        $img_string = '';
                        // foreach($selectedColumns[$col] as $imageses){
                        //     isset($imageses)? $img_string .= data_get($demoData, $imageses).',':'';
                        // }
                        foreach($images as $imageses){
                            isset($imageses)? $img_string .= $imageses.',':'';
                        }

                        $html .= '<td style="text-align:center"><a data-lightbox="roadtrip" href="'.$images[0].'" ><img id="import_image" data-allImages="'.$img_string.'" src="' . $images[0] . '" alt=""></a><br/><br/><a href="#" style="background: #fb683a;border-radius: 5px;padding: 5px;color: white;" data-toggle="modal" data-target="#allImagesModal"><i class="fa fa-eye"></i>  See All</a></td>';
                        // $html .= '<td style="text-align:center"><a data-lightbox="roadtrip" href="'.$images[0].'" ><img id="import_image" data-allImages="'.$images.'" src="' . $images[0] . '" alt=""></a><br/><br/><a href="#" style="background: #fb683a;border-radius: 5px;padding: 5px;color: white;" data-toggle="modal" data-target="#allImagesModal"><i class="fa fa-eye"></i>  See All</a></td>';
                    }else{
                        // $html .= '<td><a data-lightbox="roadtrip" href="'.$image_url.'" ><img id="import_image" data-allImages="" src="' . $image_url . '" alt=""></a></td>';
                        $html .= '<td><a data-lightbox="roadtrip" href="'.$images[0].'" ><img id="import_image" data-allImages="" src="' . $images[0] . '" alt=""></a></td>';
                    }

                } elseif ( $col == 'description' ) {
                	$html .= "{!! <td style='max-width: 350px; word-break:break-word; white-space: normal; text-align: left'>" . $row[$key] . "</td> !!}";
                } else if($col == "ek_price"){
                    if($row[$key]){
                        $ek_price = number_format(removeCommaFromPrice($row[$key]),2);
                        $html .= '{!! <td class="ek_price">' . $ek_price . '</td> !!}';
                    }else{
                        $html .= '{!! <td class="ek_price" ></td> !!}';
                    }
                } else if($col == "uvp"){
                    if(!empty($selectedColumns['manual_uvp'])){
                        $html .= '{!! <td>' . app(CollectionService::class)->uvpPercentangeCalculation($ek_price,$selectedColumns['manual_uvp']) . '</td> !!}';

                    }else{
                        if($row[$key]){
                            $html .= '{!! <td>' . number_format(removeCommaFromPrice($row[$key]),2) . '</td> !!}';
                        }else{
                            $html .= '{!! <td></td> !!}';
                        }
                    }

                } else if($col == "shipping_cost"){
                    if(!empty($selectedColumns['shipping_cost_manual'])){
                        $html .= '{!! <td id="map-sc">' . $selectedColumns['shipping_cost_manual'] . '</td> !!}';
                    }else if($row[$key]){
                        $html .= '{!! <td id="map-sc">' . number_format(removeCommaFromPrice($row[$key]),2) . '</td> !!}';
                    }else{
                        $html .= '{!! <td id="map-sc"></td> !!}';
                    }
                } else if($col == "delivery_days"){
                    if(!empty($selectedColumns['delivery_days_feed'])){
                        $html .= '{!! <td id="map-dd">' . $selectedColumns['delivery_days_feed'] . '</td> !!}';
                    }else{
                        $html .= '{!! <td id="map-dd">' . app(\App\Services\Marketplace\CollectionService::class)->stringToNumericAndGetMaxValue($row[$key]) . '</td> !!}';
                    }
                } else if($col == "stock"){
                    if(!empty($selectedColumns['stock'])){
                        $html .= '{!! <td>' . (int)number_format($row[$key]) . '</td> !!}';
                    }else{
                        $html .= '{!! <td>' . $row[$key] . '</td> !!}';
                    }

                } else {
                    $html .= '{!! <td>' . $row[$key] . '</td> !!}';
                }
            }
            $html .= '</tr>';
            return $html;
        }

        public function postDoneImport()
        {
            $this->cbLoader();
            $data['page_menu'] = Route::getCurrentRoute()->getActionName();
            $data['page_title'] = trans('crudbooster.import_page_title', ['module' => (CRUDBooster::getCurrentModule()->name)]);
            Session::put('select_column', Request::get('select_column'));

            if ( request()->industryField ) {
            	Session::put('industry_template_map', request()->industryField);
            } else {
            	Session::forget('industry_template_map');
            }

            Session::put('handling_time', intval(request()->handling_time) ?? 0 );
            Session::put('shipping_cost_manual', floatval(request()->shipping_cost_manual) ?? 0 );
            Session::put('manual_uvp', floatval(request()->manual_uvp) ?? 0 );
            Session::put('manual_vat', floatval(request()->manual_vat) ?? 0 );

            $collectionAllColumnMap = Request::get('select_column');
            $collectionAllColumnMap['handling_time']        =intval(request()->handling_time);
            $collectionAllColumnMap['shipping_cost_manual'] =floatval(request()->shipping_cost_manual);
            $collectionAllColumnMap['manual_uvp']           =floatval(request()->manual_uvp);
            $collectionAllColumnMap['manual_vat']           =floatval(request()->manual_vat);
            $collectionAllColumnMap['country_id']           = Request::get('country_id');
            Session::put('collectionAllColumnMap',$collectionAllColumnMap);

            $additionalInfo = [];
            // if(request()->shipping_method == 1){
                $additionalInfo['shipping_type'] = request()->shipping_type;
                // Session::put('additionalInfo',$additionalInfo); 
            // }else if(request()->shipping_method == 2){
                // $additionalInfo['manufacturer'] = request()->manufacturer; 
                // $additionalInfo['manufacturer_link'] = request()->manufacturer_link;
                // $additionalInfo['manufacturer_id'] = request()->manufacturer_id;
                // $additionalInfo['custom_tariff_number'] = request()->custom_tariff_number;
                // $additionalInfo['min_stock'] = request()->min_stock;
                // $additionalInfo['min_order'] = request()->min_order;
                // $additionalInfo['region_id'] = request()->region_id;
                // $additionalInfo['country_of_origin'] = \Illuminate\Support\Facades\Request::get('country_id');
                $additionalInfo['item_unit'] = request()->item_unit;
                $additionalInfo['packing_unit'] = request()->packing_unit;
                $additionalInfo['packaging_length'] = request()->packaging_length;
                $additionalInfo['packaging_width'] = request()->packaging_width;
                $additionalInfo['packaging_height'] = request()->packaging_height;
                $additionalInfo['volume_gross'] = request()->volume_gross;
                $additionalInfo['gross_weight'] = request()->gross_weight;
                $additionalInfo['price_labeling_obligation'] = request()->price_labeling_obligation;
                $additionalInfo['base_price_reference'] = request()->base_price_reference;
                $additionalInfo['electrical_appliance'] = request()->electrical_appliance;
                $additionalInfo['energy_label'] = request()->energy_label ?? [];
                Session::put('additionalInfo', $additionalInfo);    

            // }
            
            return view('marketplace.collection.import', $data);
        }

        public function postDoUploadImportData()
        {
	        $this->cbLoader();

            $supplier_selected 	= Request::post('company_user') ?? null;

            try {
                if(!request()->category_map){
                    Session::put('file_type',Request::post('file_type'));
                    Session::put('delimiter'.CRUDBooster::myParentId(),Request::post('delimiter'));
                    Session::put('shipping_method',Request::post('shipping_method'));
                    Session::put('country_id',Request::post('country_id'));
                    Session::put('supplier_id',Request::post('company_user') ?? \CRUDBooster::myParentId());
                    if(Request::hasFile('userfile')){
                        $category_map = "";
                        $file = Request::file('userfile');
                        $ext = $file->getClientOriginalExtension();
    
                        Session::put('collection_name',Carbon::now().'-'.pathinfo(Request::file('userfile')->getClientOriginalName(), PATHINFO_FILENAME));
                        $validator = Validator::make([
                            'extension' => $ext,
                        ], [
                            'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV,txt,TXT,XML,xml',
                        ]);
    
                        if ($validator->fails()) {
                            $message = $validator->errors()->all();
                            return redirect(CRUDBooster::mainpath('import-data'))->with(['message' => implode('<br/>', $message ?? []), 'message_type' => 'warning']);
                        }
    
                        // Upload csv to cloud
                        $filePath = 'marketplace-collections/' . CRUDBooster::myParentId() .'/'. md5(Str::random(5)) . '.' . $ext;
                        $file_data = file_get_contents($file->getRealPath());
    
                        Storage::disk('spaces')->put($filePath, $file_data, 'public');
    
                        $csv_url = Storage::disk('spaces')->url($filePath);
                        Session::put('uploaded_csv_path', $csv_url);
    
                        $url = CRUDBooster::mainpath('import-data').'?file='.base64_encode($csv_url).'&category_map='.$category_map;
                        return redirect($url);
    
                    } else if (request()->csv_link){
                        $path_parts = pathinfo(request()->csv_link);
                        Session::put('collection_name',Carbon::now().'-'.$path_parts['filename']);
                        $category_map = "";
                        Session::put('csv_original_url',request()->csv_link);
                        $ext = 'csv';
                        $url = app(CollectionService::class)->storeFileFromUrl_(request()->csv_link);
                        Session::put('uploaded_csv_path', $url);
                        $url = CRUDBooster::mainpath('import-data').'?file='.base64_encode($url).'&category_map='.$category_map;
                        return redirect($url);
                    }else{
                        return redirect(CRUDBooster::mainpath('import-data'))->with(['message' => 'Please select file', 'message_type' => 'warning']);
                    }
                }else if(request()->category_map){
                    Session::forget('categories'.CRUDBooster::myParentId());
                    Session::forget('csv_category_column'.CRUDBooster::myParentId());
                    Session::forget('system_category'.CRUDBooster::myParentId());
    
                    $category_map = "true";
                    if(request()->category){
                        $categories = [];
                        foreach(request()->all_category as $category){
                            $category_explode = explode('llmpcat',$category);
                            $categories[$category_explode[0]] = $category_explode[1];
                        }
    
                        Session::put('categories'.CRUDBooster::myParentId(),$categories);
                        Session::put('csv_category_column'.CRUDBooster::myParentId(),request()->category);
                    }else if(request()->system_category){
                        Session::put('system_category'.CRUDBooster::myParentId(),request()->system_category);
                    }
    
                    $url = CRUDBooster::mainpath('import-data').'?category_map='.$category_map.'&collection_name=' . '&shipping_method=' . Session::get('shipping_method') . '&supplier_id=' .Session::get('supplier_id').'&country_id='.Session::get('country_id');
    
                    return redirect($url);
                }
            } catch (\Exception $e) {
                return redirect(CRUDBooster::mainpath('import-data'))->with(['message' => 'Something went wrong', 'message_type' => 'error']);
            }
            
        }
        //URL Get Extention
        public function curl_read($url){
            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            $data = trim(curl_exec($ch));
            $mime = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
            if ($mime) {
                $ext = mime_to_ext($mime);
            } else {
                $ext = pathinfo($url, PATHINFO_EXTENSION);
            }
            curl_close($ch);
            $result = [
                'data' => makeUtf8($data),
                'ext' => $ext
            ];
            return $result;
        }


        public function postDoImportChunk()
        {
            if ( \CRUDBooster::isSupplier() ) {

            $myId = (CRUDBooster::myParentId()) ? CRUDBooster::myParentId() : CRUDBooster::myParentId();
            //sub supplier
            $deliveryCompanyId = \App\DeliveryCompany::where('supplier_id', $myId)
            ->first()->id;
            $oldProducts = Product::select('ean','supplier_id')->where('supplier_id',$myId )->get();

        	} else {
        		if ( Session::has('supplier_id') ) {
        			// For supper admin && dropmatrix
        			$deliveryCompanyId = Session::get('supplier_id');

        			$myId = null;
                    $oldProducts = Product::select('ean','delivery_company_id')->where('delivery_company_id',$deliveryCompanyId)->get();
        		}
        	}
	        $dupplicated_products_html = '';
	        $this->cbLoader();
            $transferable_product_ids = [];
	        $file_md5 = md5(Request::get('file'));

	        if (Request::get('file') && Request::get('resume') == 1) {
	            $total = Cache::get('total_product_count'.$file_md5) > 0? Cache::get('total_product_count'.$file_md5) : Session::get('total_data_import');
	            $prog = intval(Cache::get('success_'.$file_md5)) / $total * 100;
	            $prog = round($prog, 2);
	            if ($prog >= 100) {
	                // Cache::forget('success_'.$file_md5);
	                Cache::forget('mp_csv_' . CRUDBooster::myParentId());
	            }
	            return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_'.$file_md5), 'inserted' => Cache::get('insert_success_'.$file_md5) ]);
	        }
            // for no limit exicution
            ini_set('max_execution_time', '0');
            ini_set('memory_limit', -1);

	        $count = 0;
	        $data_inserted = 0;
	        try {
                if(!empty(Session::get('csv_category_column'.CRUDBooster::myParentId())) && !empty(Session::get('categories'.CRUDBooster::myParentId()))){
                    Session::put('select_column.category',Session::get('csv_category_column'.CRUDBooster::myParentId()));
                    Session::put('collectionAllColumnMap.category',Session::get('csv_category_column'.CRUDBooster::myParentId()));
                }
	            $select_column = Session::get('select_column');
	            $select_column['industry_template_data'] = Session::get('industry_template_map') ?? null;
	            $select_column = array_filter($select_column);

                $table_columns = [ "name", "ean", "ek_price", 'uvp', "description", "image", "item_color", "brand", "item_number", "purchase_price", "vat", "stock", "item_weight", "item_size", "note", "production_year", "materials", "tags", "gender", "is_top_product", "update_status", 'category', 'uvp_percentage', 'industry_template_data'];

                $csv_datas = json_decode(Cache::store('file')->get('mp_csv_content'.'-'.CRUDBooster::myParentId()),true);
               
                $rows =collect($csv_datas)->unique($select_column['ean'])->toArray();

                // CSV Brand Processing
                $mp_brands = [];
                if(isset($select_column['brand'])){
                    $csv_brands = collect($csv_datas)->unique($select_column['brand'])->pluck($select_column['brand'])->toArray() ?? [];
                    $gpsr_brands = collect($csv_datas)->unique($select_column['safety_brand_id'])->pluck($select_column['safety_brand_id'])->toArray() ?? [];
                    $csv_brands = array_unique(array_merge($csv_brands, $gpsr_brands));
                    $mp_brands = app(ProductService::class)->brandInsertOrUpdate($csv_brands);
                }
                $category_im_handel =  Category::pluck('im_handel', 'id')->toArray() ?? [];

                $all_country =  array_change_key_case(DB::table('all_country')->pluck('id','country')->toArray(), CASE_UPPER);
                $additional_info_columns = ['product_width', 'product_height','product_length', 'manufacturer', 'manufacturer_id', 'manufacturer_link', 'custom_tariff_number', 'country_of_origin', 'region', 'min_stock', 'min_order',
                        'model_number', 'item_description', 'item_sub_color', 'base_materials','quantity_in_ml', 'area_in_square', 'area_in_cube', 'target_group', 'age_recommendation', 'energy_efficiency_class', 'quantity_in_pieces'];

                $gpsr_info_columns = ['safety_company_name', 'safety_brand_id', 'safety_email', 'safety_phone', 'safety_street', 'safety_city', 'safety_zip_code', 'safety_country_id'];
                Cache::put('total_product_count' . $file_md5, count($rows),80000);

	            $data_import_column = [];
	            $collection = app(CollectionService::class)->store([
	                'name' => Session::get('collection_name') ?? 'Anonimous',
	                'category_id' => Session::get('system_category'.CRUDBooster::myParentId()) ?? 0,
	                'supplier_id' => $myId,
	                'delivery_company_id' => $deliveryCompanyId,
	                'shipping_method' => Request::get('shipping_method'),
	                'source_type' => Session::get('file_type'),
	                'status' => (in_array($myId, FakeSuppliers::FAKE_IDS)) ? CollectionStatus::ACTIVE : CollectionStatus::PENDING,
	                'csv_file_link' => Session::get('uploaded_csv_path') ?? '',
	                'source_url' => Session::get('csv_original_url') ?? '',
	                'column_mapping' => Session::get('collectionAllColumnMap') ?? '',
                    'category_mapping'=> Session::get('categories'.CRUDBooster::myParentId()) ?? '',
	                'industry_template' => Session::get('industry_template_map') ?? '',
	            ]);
                $image_links = [];
                $country = DB::table('countries')->where('id', Session::get('country_id'))->first();

                $calculation = MarketplaceProfitCalculation::find(24);

                if(!empty($rows)){
                    foreach(array_chunk($rows,500) as $data){
                        $productsArray = [];
                        foreach ($data as $value) {

                            $count++;
                            Cache::put('success_' . $file_md5, $count,80000);

                            $value = (object)$value;
                            $iamges = [];
                            $product_all_image = [];
                            $image_prefix = '';
                            $image_suffix = '';
                            $image_separator = '';
                            $ek_price = 0;
                            $vk_price = 0;
                            foreach ($select_column as $sk => $s) {
                                $colname = $sk;

                                if ($colname == "ek_price") {
                                    if (strpos($value->$s, '.') !== false) {
                                        $ek_price = floatval(str_replace(',', '', $value->$s));
                                    }else{
                                        $ek_price = removeCommaFromPrice($value->$s);
                                    }
                                }
                                if ($colname == "image_prefix") {
                                    $image_prefix = $s;
                                }
                                if ($colname == "image_suffix") {
                                    $image_suffix = $s;
                                }
                                if ($colname == "image_separator") {
                                    $image_separator = $s;
                                }

                                if ($colname == "vat") {
                                    $vat = removeCommaFromPrice($value->$s);
                                }

                                if ($colname == "uvp") {
                                    $productInfo['uvp'] = removeCommaFromPrice($value->$s);
                                }

                                if ($colname == 'image') {
                                    if (is_array($s)) {
                                        foreach ($s as $image) {
                                            $product_all_image[] = $value->$image;
                                        }
                                    }
                                }
                                if(!empty(Session::get('csv_category_column'.CRUDBooster::myParentId())) && !empty(Session::get('categories'.CRUDBooster::myParentId()))){
                                    if ($colname == "category") {
                                        $productInfo['category_id'] = Session::get('categories'.CRUDBooster::myParentId())[$value->$s];
                                    }
                                }else{
                                    $productInfo['category_id'] = Session::get('system_category'.CRUDBooster::myParentId());
                                }
                                
                                if (in_array($colname, $additional_info_columns) && !empty($value->$s)) {
                                    if(in_array($colname, ['region', 'country_of_origin'])){
                                        $countryKey = strtoupper($value->$s);
                                        $additionalProductInfo[$colname] = $all_country[$countryKey] ?? 
                                            $all_country[$countryKey] = DB::table('all_country')->insertGetId(['country' => $value->$s]);
                                    } else {
                                        $additionalProductInfo[$colname] = $value->$s;
                                    }
                                }

                                if(in_array($colname, $gpsr_info_columns)){
                                    if($colname == 'safety_country_id'){
                                        $countryKey = strtoupper($value->$s);
                                        $productSafetyInfo[$colname] = $all_country[$countryKey] ?? 
                                            $all_country[$countryKey] = DB::table('all_country')->insertGetId(['country' => $value->$s]);
                                    } else if($colname == 'safety_brand_id'){
                                        $productSafetyInfo[$colname] = $mp_brands[strtoupper($value->$s)] ?? '';
                                    } else {
                                        $productSafetyInfo[$colname] = $value->$s ?? null;
                                    }
                                }
                               
                                 if (!empty($value->$s) && !in_array($colname, $additional_info_columns) && !in_array($colname, $gpsr_info_columns)) {
                                    if($colname == 'delivery_days'){
                                        $productInfo['delivery_days']= app(\App\Services\Marketplace\CollectionService::class)->stringToNumericAndGetMaxValue($value->$s);

                                    }else if($colname == "shipping_cost") {
                                        $productInfo['real_shipping_cost'] = removeCommaFromPrice($value->$s);
                                        $productInfo[$colname] = removeCommaFromPrice($value->$s) * 1.10;
                                    } else if($colname == "vk_price"){
                                        $productInfo[$colname] = removeCommaFromPrice($value->$s);
                                    } else if ($colname == "ek_price") {
                                        $productInfo[$colname] = $ek_price;
                                    } else if ($colname == "brand") {
                                        $productInfo['brand'] = $mp_brands[strtoupper($value->$s)] ?? '';
                                    } else {
                                        $productInfo[$colname] = $value->$s;
                                    }
                                }
                            }

                            if(isset($additionalProductInfo['product_length']) && $additionalProductInfo['product_width'] && $additionalProductInfo['product_height']){
                                $additionalProductInfo['volume'] = ($additionalProductInfo['product_length'] * $additionalProductInfo['product_width'] * $additionalProductInfo['product_height'])/1000000 ?? '';
                            }

                            if(count($product_all_image) > 0){
                               $iamges =  $this->image_processing($product_all_image,$image_prefix,$image_suffix,$image_separator);
                               $image_array = (!empty($iamges)) ? array_merge([], $iamges) : null;
                            }

                            if (Session::get('handling_time') > 0) {
                                $productInfo['delivery_days'] = Session::get('handling_time');
                            }

                            if ( Session::get('manual_uvp') ) {
                                $productInfo['uvp'] = app(CollectionService::class)->uvpPercentangeCalculation(floatval($ek_price),Session::get('manual_uvp'));
                            }
                            if ( Session::get('manual_vat')) {
                                $vat = Session::get('manual_vat');
                            }

                            if (Session::get('shipping_cost_manual') > 0) {
                                $productInfo['real_shipping_cost'] = Session::get('shipping_cost_manual');
                                $productInfo['shipping_cost'] = Session::get('shipping_cost_manual') * 1.10;
                            }

                            if($collection->shipping_method == \App\Enums\Marketplace\ShippingMethod::FULFILLment){
                                $productInfo['real_shipping_cost'] = 5.20;
                                $productInfo['shipping_cost'] = 5.20 * 1.10;
                                $productInfo['delivery_days'] = 2;
                            }

                            if(isset($productInfo['category_id']) && isset($productInfo['ean']) && isset($productInfo["ek_price"])){
                                // For industry template
                                if(isset($productInfo['category_id'])){
                                    $category 		   = \App\Models\Marketplace\Category::find($productInfo['category_id']);
                                    $countryShortcut  = \App\Country::find( Session::get('country_id') )->country_shortcut;
                                    if ( $countryShortcut == 'en' || $countryShortcut == 'uk' ) {
                                        $countryShortcut = 'en';
                                    } else {
                                        $countryShortcut = 'de';
                                    }

                                    $industryTemplateMapping = Session::get('industry_template_map') ?? null;
                                    if ( $industryTemplateMapping ) {
                                        $industryTemplateName = $category->industry_template ? strtolower($category->industry_template) : null;
                                        $industryTemplateArray = \App\Enums\Marketplace\IndustryTemplates::TEMPLATES[$industryTemplateName];

                                        foreach ( $industryTemplateMapping as $industryTemplateKey=>$industryTemplateValue ) {
                                            $industryTemplateKey = trim($industryTemplateKey, "'\" ");
                                            if(isset($industryTemplateArray[$industryTemplateKey])){
                                                $industryTemplateArray[$industryTemplateKey][$countryShortcut] = $value->$industryTemplateValue ?? null;
                                            }
                                        }
                                        $industryTemplateData = [
                                            $industryTemplateName => $industryTemplateArray,
                                        ];
                                        $productInfo['industry_template_data'] = json_decode(json_encode($industryTemplateData), true) ?? NULL;
                                    }
                                }

                                $productInfo['image'] = $image_array;
                                $productInfo['old_images'] = $image_array;
                                $productInfo["collection_id"] = $collection->id ?? '';
                                $productInfo["delivery_company_id"] = $deliveryCompanyId ?? '';
                                $productInfo["supplier_id"] = $myId ?? '';
                                $productInfo["shipping_method"] = $collection->shipping_method ?? '';
                                $productInfo["status"] = ProductStatus::PENDING;
                                $productInfo["country_id"] = Session::get('country_id');
                                $productInfo["category"] = '';
                                $productInfo["vat"] = $vat;
                                $productInfo["tax_type"] = $vat == $country->reduced_tax_rate ? 2 : 1 ;
                                
                                if( is_relavida() || in_array(CRUDBooster::myParentId(), [2817]) ){
                                    $productInfo["vk_price"] = $productInfo['uvp'];
                                    $productInfo["status"] = ProductStatus::ACTIVE;
                                }

                                if (in_array($myId, FakeSuppliers::FAKE_IDS)) {
                                    $vk_price = $ek_price + ($ek_price * 0.05);
                                    $vk_price = (float)str_replace(',', '', number_format($vk_price, 2));
                                    $productInfo["vk_price"] = $vk_price;
                                    $productInfo["status"] = ProductStatus::ACTIVE;
                                }

                                if (app(\App\Services\Marketplace\ProductService::class)->validateEAN($productInfo['ean']) == false) {
                                    continue;
                                }

                                if ( in_array($productInfo["name"], ['', null]) || in_array($productInfo["description"], ['', null]) || in_array($productInfo['image'], ['', null]) || in_array($productInfo['category_id'], ['', null]) || in_array($productInfo['item_number'], ['', null]) || in_array($productInfo['ean'], ['', null]) ||
                                    in_array($productInfo['brand'], ['', null]) || in_array($productInfo['ek_price'], ['', null]) || in_array($productInfo['vat'], ['', null]) || in_array($productInfo['uvp'], ['', null]) || in_array($productInfo['delivery_days'], ['', null])) {
                                    $productInfo["status"] = ProductStatus::QUALITY_DEFECT;
                                }

                                $productNotExists = !$oldProducts->where('ean', $productInfo['ean'])->first();
                               
                                $validEkPrice = floatval($productInfo["ek_price"]) > 0;

                                if ($validEkPrice == false) {
                                    $dupplicated_products_html .= "<tr>
                                    <td>{$productInfo['name']}(EAN: {$productInfo['ean']})</td>
                                    <td class='danger'><b>Rejected(Ek price is not valid: {$productInfo['ek_price']})</b></td>
                                </tr>";
                                }

                                if ($productNotExists) {
                                    if($validEkPrice) {
                                        if($productInfo['ek_price'] > $productInfo['uvp']) $productInfo["status"] = ProductStatus::BLOCKED;
                                        $productInfo['shipping_type'] = Session::get('additionalInfo')['shipping_type'] ?? null;
                                        $productInfo['alarm_quantity'] = Session::get('additionalInfo')['min_order'] ?? 0;
                                        if($calculation){
                                            if($productInfo['shipping_cost'] > 35){
                                                $productInfo["vk_price"] = app(\App\Http\Controllers\AdminMarketplaceProductsController::class)->calculatePrice($productInfo['ek_price'], $calculation, $productInfo['uvp'], 35);
                                                $productInfo["vk_price"] += $productInfo['shipping_cost'] - 35;
                                                $productInfo['shipping_cost'] = 35;
                                            }else{
                                                $productInfo["vk_price"] = app(\App\Http\Controllers\AdminMarketplaceProductsController::class)->calculatePrice($productInfo['ek_price'], $calculation, $productInfo['uvp'], $productInfo['shipping_cost']);
                                            }
                                        }

                                        $productInfo["im_handel"] = 0;
                                        if(isset($category_im_handel[$productInfo['category_id']]) && $category_im_handel[$productInfo['category_id']] > 0){
                                            $productInfo["im_handel"] =  $productInfo["vk_price"] + (($productInfo["vk_price"] * $category_im_handel[$productInfo['category_id']]) / 100);
                                        }

                                        $newProduct =  Product::create($productInfo);
                                        $transferable_product_ids[] = $newProduct->id;
                                        $additionalProductInfo['product_id'] = $newProduct->id;
                                        $additionalProductInfo = array_merge($additionalProductInfo,Session::get('additionalInfo'));
                                        AdditionalInfo::create($additionalProductInfo);

                                        $productSafetyInfo['product_id'] = $newProduct->id;
                                        ProductSafetyGPSR::create($productSafetyInfo);
                                    }

                                } else {
                                    $dupplicated_products_html .= "<tr>
                                    <td>{$productInfo['name']}(EAN: {$productInfo['ean']})</td>
                                    <td class='warning'><b>Duplicate(Duplicate Ean Found.)</b></td>
                                </tr>";
                                }
                            }
                        }

                    }

                }

                if( !empty($transferable_product_ids) && (is_relavida() || in_array(CRUDBooster::myParentId(), [2817]))){
                    app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferAllFilteredProductsToDrm($transferable_product_ids, [], null, 2694);
                }
	            // Send notification to super admin
	            $supplier = \App\DeliveryCompany::find($collection->delivery_company_id);
	            $url = CRUDBooster::mainpath( 'product-detail/'.$collection->id );
	            $message_title = 'Supplier: '.$supplier->name.' has imported product collection.';
	            User::find(71)->notify( new DRMNotification($message_title, 'COLLECTION IMPORTED', $url) );
	            User::find( \App\Enums\Apps::DROPMATIX_ID )->notify( new DRMNotification($message_title, 'COLLECTION IMPORTED', $url) );

                if(isset($collection->delivery_company_id)){
                    $supplier_email = $supplier->email;

                    $tags = [
                        'name' =>  $supplier->name,
                    ];

                }else{
                    $supplier_email = User::where('id',$collection->supplier_id)->first();
                    $tags = [
                        'name' =>  $supplier_email->name,
                    ];
                    $supplier_email = $supplier_email->email;

                }

                if($collection->shipping_method == 1){
                    $slug = 'import_email_for_dropshipping';
                }else if($collection->shipping_method == 2){
                    $slug = 'import_email_for_fulfilment';
                }
                $lang = getUserSavedLang($supplier_email);

                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                if(isset($supplier_email)){
                    app('drm.mailer')->getMailer()->to($supplier_email)->send(new DRMSEndMail($mail_data)); //Send
                }


	        } catch (\Exception $e) {
	            Cache::put('error_'.$file_md5, $e->getMessage());
	        }

            Cache::store('file')->forget('mp_csv_content'.'-'.CRUDBooster::myParentId());
            Session::forget('demo_preview');
            Session::forget('supplier_id');
            Session::forget('collection_category');
            Session::forget('collection_name');
            Session::forget('shipping_method');
            Session::forget('country_id');
            Session::forget('handling_time');
            Session::forget('shipping_cost_manual');
            Session::forget('manual_uvp');
            Session::forget('manual_vat');
            Session::forget('csv_original_url');
            Session::forget('collectionAllColumnMap');

            Session::forget('categories'.CRUDBooster::myParentId());
            Session::forget('csv_category_column'.CRUDBooster::myParentId());
            Session::forget('system_category'.CRUDBooster::myParentId());
            Session::forget('uploaded_csv_path');
            Session::forget('file_type');
            Session::forget('delimiter'.CRUDBooster::myParentId());
            Session::forget('data_import_column'.CRUDBooster::myParentId());
            Session::forget('additionalInfo');

            return response()->json(['status' => true, 'inserted' => Cache::get('insert_success_'.$file_md5), 'dupplicated_products' => $dupplicated_products_html,'collection_id'=> $collection->id]);
        }

        private function csvToArray($csv,$type,$delimiter,$deleteFile = true){
            ini_set('max_execution_time', '0');
            ini_set('memory_limit',-1);
            $paths=explode(';',$csv);
            $key=null;
            $key_count=0;
            $array = array();
            $rand=Str::random(40);

            foreach($paths as $path){
                $file_type = $type;
                // $path = Storage::disk('spaces')->url($path);
                file_put_contents($rand . '.' . $file_type, fopen($path, 'r'));
                $localpath = $rand . '.' . $file_type;

                if($type =='csv'|| $type == 'txt'){
                    $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                    $reader->setInputEncoding('UTF-8');

                    if($delimiter!='auto'){
                        $reader->setDelimiter($delimiter);
                    }
                    $spreadsheet = $reader->load($localpath);
                }
                else{
                    $spreadsheet = IOFactory::load($localpath);
                }
                $spreadsheet = $spreadsheet->getActiveSheet()->toArray();
                $collection = LazyCollection::make($spreadsheet);

                if($key==null){
                    $key = array_map('trim', $collection->first());
                    $key_count=count($key);
                }
                $key = array_map('removeDots',$key);
                $collection = $collection->except(0);
                foreach($collection as $row){

                    if(count($row)==$key_count && !containsOnlyNull($row)){
                        $array[] = array_combine($key, $row);
                    }
                }

                if(!pathIsUrl($path) && $deleteFile){
                    unlink($localpath);
                }
            }

            return $array;
        }


        public function getproductDetail($id)
        {
            $page_title = 'Collection Detail';
            // $collections = DB::connection(env('DB_CONNECTION_MARKETPLACE'))->table('marketplace_collections')->find($id);
            $collections = Collection::find($id);
            $products = app(ProductService::class)->all(['collection_id' => $collections->id]);
            // $products = DB::table('marketplace_products')->where('collection_id', $collections->id)->all();
            return view('marketplace.collection.product-detail', compact('products', 'collections', 'page_title'));
        }

        public function getProducts(){
            ini_set('max_execution_time', '0'); // for infinite time of execution
            ini_set('memory_limit', -1);

            $id = $_REQUEST['id'];
            $products = Product::where('collection_id', $id)->get();
            $all_products = [];
            foreach ($products as $key => $value) {
                $value->image = $value->image;
                // $value->category = json_decode($value->category);
                $value->category = $value->mainCategory->name;
                $value->description = strip_tags($value->description);
                if ( \CRUDBooster::isSuperAdmin() ) {
					$value->duplicate_ean = MarketplaceProducts::where('ean', $value->ean)->count() > 1 ? 1 : 0;
                } else {
                	$value->duplicate_ean = 0;
                }

                $all_products[$key] = $value;
            }
            return DataTables::of($all_products)->make(true);
        }

        public function getstatusPermission($id)
        {
            try {
                $status = request()->only('status');
                $collection = app(CollectionService::class)->getById($id);
                if($collection){
                    $collection->update($status);
                }
                // $url = Storage::disk('spaces')->url($invoice_file_path.".zip");
                if ($status['status'] == CollectionStatus::ACTIVE) {
                    $message_title = 'Your '.$collection->name.' Collection is Accepted.';
                    // Product::where('collection_id', $collection->id)->update(['status' => ProductStatus::ACTIVE]);
                    $products = Product::where('collection_id', $collection->id)->get();
                    foreach($products as $product){
                        if($product->mainCategory->minimum_price < $product->ek_price){
                            $product->update(['status' => ProductStatus::ACTIVE]);
                            if($product->vk_price > 0){
                                app(ProductService::class)->dublicateBestOneApproved($product->id);
                            }else{
                                 $this->duplicateAssignee($product->id);
                            }
                        }
                    }

                } elseif($status['status'] == CollectionStatus::INACTIVE){
                	Product::where('collection_id', $collection->id)->update(['status' => ProductStatus::INACTIVE]);
                    $message_title = 'Your '.$collection->name. ' Collection is Rejected.';
                }
                // User::find($collection->supplier_id)->notify( new DRMNotification($message_title, 'COLLECTION_FEEDS'));
                // $message_title = $status['status'] == CollectionStatus::ACTIVE ? 'You have Accepted '.$collection->name.' Collection' : 'You have Rejected '.$collection->name.' Collection'
                // User::find(71)->notify( new DRMNotification($message_title, 'COLLECTION_FEEDS'));
                return redirect(CRUDBooster::mainpath())->with(['_status' => 'success', '_msg' => 'Successfully '. $event .' Product']);
            } catch (\Exception $e) {
                return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to update!']);
            }

        }

        public function duplicateAssignee($productId){
            $product = Product::findOrFail($productId);
            $isDuplicate = \App\Models\Marketplace\Product::where('ean',$product->ean)->get();
            if(count($isDuplicate) > 1){
                Product::where('ean',$product->ean)->where('best_price','!=',1)->where('status','!=',2)->update([
                    'status' => \App\Enums\Marketplace\ProductStatus::PENDING,
                    'is_dublicate'=>1,
                    'best_price'=> 0
                ]);
            }
        }
        public function getCollectionById(){
            ini_set('max_execution_time', '0'); // for infinite time of execution
            ini_set('memory_limit', -1);
            $product = MarketplaceProducts::find(Request::input('id'));
            return $product;
        }

        public function getDeleteCollection(){
            ini_set('max_execution_time', '0'); // for infinite time of execution
            ini_set('memory_limit', -1);
            $id = $_REQUEST['id'];
            $import_id = $_REQUEST['import_id'];
            MarketplaceProducts::where(['id' => $id, 'collection_id' => $import_id])->delete();
            return response()->json([
                'status' => 'SUCCESS',
                'code' => 200
            ]);
        }

        public function postCollectionUpdate(){
            ini_set('max_execution_time', '0'); // for infinite time of execution
            ini_set('memory_limit', -1);
            $collection = MarketplaceProducts::find(Request::input('id'));
            $collection->name = Request::input('name');
            $collection->item_number = Request::input('item_number');
            $collection->description = Request::input('description');
            $collection->ek_price = removeCommaFromPrice(Request::input('ek_price'));
            // $collection->vk_price = Request::input('vk_price');
            // $product->vat=Request::input('vat');
            if(in_array($collection->supplier_id, FakeSuppliers::FAKE_IDS)) {
                $vk_price = $collection->ek_price + ($collection->ek_price * 0.05);
                $vk_price = (float)str_replace(',', '', number_format($vk_price, 2));
                $collection->vk_price = $vk_price;
            }

            $collection->stock = Request::input('stock');

            if($collection->ek_price <= 0) {
                return response()->json([
                    'status' => 400,
                    'error'  => 'product_blocked',
                ]);
            }

            $collection->save();
            return response()->json([
                'status' => 200,
            ]);
        }

        public function getImportDataGetCollections ()
        {
            $request = $_REQUEST;
            $collection_category_id = $request['collection_category_id'];

            $collections = Collection::where('category_id', $collection_category_id)
                            ->get();
            return response()->json($collections);
        }

        public function getSubCategoriesFromParentCategory ($categoryId) {
	        $category = Category::find($categoryId);
	        $subCategories = '<option value="0">Select sub category</option>';
	        foreach ( $category->children() as $child ) {
	            $subCategories .= '<option value="'.$child->id.'">'.$child->name.'</option>';
            }
	        return response()->json([
	            'success' => 'yes',
                'subCategories' => $subCategories,
            ]);
        }

        function getAddSubCategory ()
        {
            $responseHtml = '';
            $slug = str_replace(' ', '-', $_REQUEST['sub_category_name']);
            try{
                if ( Category::where('slug', $slug)->exists() ) {
                    return response()->json([
                        'success' => 'no',
                        'message' => 'Duplicated not allowed.',
                    ]);
                }

                $s_category = app(CategoryService::class )->store([
                    'name' => $_REQUEST['sub_category_name'],
                    'parent_id' => $_REQUEST['category_id'],
                    'user_id' => CRUDBooster::myParentId(),
                    'slug' => $slug,
                    'is_active' => 1,
                ]);
                $subCategories = Category::where('is_active', 1)
                                ->where('parent_id', $_REQUEST['category_id'])
                                ->get();
                foreach ( $subCategories as $subCategory ) {
                    $isSelected = $s_category->id == $subCategory->id ? 'selected="true"' : 'selected="false"';
                    $responseHtml .= '<option value="'.$subCategory->id.'"'.$isSelected.'>'.$subCategory->name.'</option>';
                }

                return response()->json(['success' => 'yes', 'categories' => $responseHtml]);
            } catch (Exception $e) {
                return response()->json([
                    'success' => 'no',
                    'message' => 'Something went wrong.'
                ]);
            }
        }

        function getManualUpdateCsv ($collectionId) {
            $this->cbLoader();
            $data = [];
            $data['collection'] = Collection::findOrFail($collectionId);
            $data['collectionId'] = $collectionId;
            $data['suppliers'] = app(\App\Services\Marketplace\ProductService::class)->getDeliveryCompanies();

            if($data['collection']->shipping_method == ShippingMethod::DROPSHIPPING){
                $data['options'] = [
                    'name'          => 'Name',
                    'ek_price'      => 'EK Price',
                    'shipping_cost' => 'Shipping Cost',
                    'delivery_days' => 'Handling Time',
                    'uvp'           => 'UVP',
                    'vat'           => 'vat',
                    'description'   => 'Description',
                    'item_number'   => 'Item Number',
                    'image'         => 'Image',
                    'stock'         => 'Stock',
                    'brand'         => 'brand',
                    'item_color'    => 'Item Color',
                    'item_weight'   => 'Item Weight',
                    'product_height'=> 'Item Height',
                    'product_width' => 'Item Width',
                    'product_length'=> 'Item Length',
                    'item_size'     => 'Item Size',
                    'gender'        => 'Gender',
                    'materials' => 'Item Material',
                    'production_year' => 'Production Year',
                    'tags'          => 'Tags',
                    'category'      => 'Category',
                ];
            }else{
                $data['options'] = [
                    'name'          => 'Name',
                    'ek_price'      => 'EK Price',
                    'uvp'           => 'UVP',
                    'vat'           => 'vat',
                    'item_weight'   => 'Item Weight',
                    'product_height'=> 'Item Height',
                    'product_width' => 'Item Width',
                    'product_length'=> 'Item Length',
                    'item_size'     => 'Item Size',
                    'stock'         => 'Stock',
                    'brand'         => 'brand',
                    'item_color'    => 'Item Color',
                    'item_size'     => 'Item Size',
                    'item_number'   => 'Item Number',
                    'image'         => 'Image',
                    'gender'        => 'Gender',
                    'materials'     => 'Item Material',
                    'production_year' => 'Production Year',
                    'description'   => 'Description',
                    'category'      => 'Category',
                    'tags'          => 'Tags',
                ];
            }

            return view('marketplace.collection.manual-import', $data);
        }

        function postManualUpdateImportMapping ()
        {
            $this->cbLoader();
            $data = [];
            $from_csv_file = Request::file('csv_file');
            $from_csv_link = Request::post('csv_link');


            //Create Directory Monthly
            $filePath = 'uploads/'.\CRUDBooster::myParentId().'/'.date('Y-m');
            Storage::makeDirectory($filePath);

            if ( isset($from_csv_file )) {

                $file = Request::file('csv_file');
                $ext = $file->getClientOriginalExtension();
                $validator = Validator::make([
                    'extension' => $ext,
                ], [
                    'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV,txt,TXT,XML,xml',
                ]);
                if ($validator->fails()) {
                    $message = $validator->errors()->all();
                    return redirect()->back()->with(['message' => implode('<br/>', $message), 'message_type' => 'warning']);
                }
                //file name & path
                $filename = md5(Str::random(5)).'.'.$ext;
                $fullFilePath = $filePath.'/'.$filename;


            } else if ( isset($from_csv_link) ) {
                $ext = 'csv';
                $url_filename = app(CollectionService::class)->storeFileFromUrl(request()->csv_link);
                $file = storage_path('app/'.$url_filename);

                //file name & path
                $filename = md5(Str::random(5)).'.'.$ext;
                $fullFilePath = $filePath.'/'.$filename;
            }

            Storage::disk('spaces')->put($fullFilePath, file_get_contents($file), 'public');
            if (Storage::disk('spaces')->exists($fullFilePath)) {
                $csv_url = Storage::disk('spaces')->url($fullFilePath);
                Session::put('updated_csv_path', $csv_url);
                Session::put('update_collection_id', $_REQUEST['collection_id']);

            } else {
                Session::put('uploaded_csv_path', 'N/A');
            }

            if (Storage::putFileAs($filePath, $file, $filename)) {
                $data['url_filename'] = $filePath.'/'.$filename;
            }

            $type = pathinfo($file, PATHINFO_EXTENSION);
            $rows = $this->csvToArray($file,$ext,'auto',false);
            $data['csvColumns'] = array_keys($rows[0]);

            $data['items'] = ['ean'=>'EAN'];

            $auxArr = [];
            foreach ( $_REQUEST['update_items'] as $item ) {
                $auxArr[$item] = ucfirst(str_replace('_', ' ', $item));
            }
            $data['items'] = array_merge($data['items'], $auxArr);

            if($data['items']==null){
                return redirect()->back();
            }

            $data['collection_id'] = request()->collection_id;
            $data['supplier_id'] = request()->supplier_id ?? '';
            return view('marketplace.collection.manual-update-sync-fields', $data);
        }

        function postManualUpdateImport ()
        {
            try{
                $data = [];
                $collectionId = request()->collection_id;

                if (!empty($_REQUEST['file_url'])) {
                    $file = storage_path('app/'.$_REQUEST['file_url']);
                    $type = pathinfo($file, PATHINFO_EXTENSION);
                    $rows = $this->csvToArray($file,$type,'auto',false);

                    // CSV Brand Processing
                    $mp_brands = [];
                    if(isset($_REQUEST['brand'])){
                        $csv_brands = collect($rows)->unique($_REQUEST['brand'])->pluck($_REQUEST['brand'])->toArray() ?? [];
                        $mp_brands = app(ProductService::class)->brandInsertOrUpdate($csv_brands);
                    }

                    $all_products = Product::with('additionalInfo', 'productBrand', 'core_products', 'mainCategory')->where('collection_id', $collectionId)->get();

                    unset($_REQUEST['_token']);
                    unset($_REQUEST['file_url']);
                    unset($_REQUEST['collection_id']);
                    unset($_REQUEST['supplier_id']);

                    $keys = (object)$_REQUEST;
                    $wantedToUpdateEkPrice = property_exists($keys, 'ek_price') || property_exists($keys, 'shipping_cost');

                    $updateInfo = [];
                    $additionalInfo = [];
                    $data['updateCount'] = 0;
                    $fromDropMatrix = (\CRUDBooster::isSuperadmin() || \CRUDBooster::isDropMatrix() || \CRUDBooster::hasDropmatixMpSupport()) && isset(request()->supplier_id);
                    
                    foreach ($rows as $row) {
                        $current_ean = null;
                        foreach( $keys as $k=>$v ) {
                            if ( $k == 'category' ) $row[$v] = explode(' - ', $row[$v]);
    
                            if($k == "ek_price" || $k == "uvp" || $k == "vat"|| $k == "shipping_cost" || $k == "vk_price"){
                                if ($k == "ek_price" || $k == "vk_price") {
                                    if (isset($row[$v]) && $row[$v] > 0) {
                                        if (strpos($row[$v], '.') !== false) {
                                            $updateInfo[$k] = floatval(str_replace(',', '', $row[$v]));
                                        } else{
                                            $updateInfo[$k] = removeCommaFromPrice($row[$v]);
                                        }
                                    }
                                } else if ($k == "shipping_cost" ){
                                    $updateInfo["real_shipping_cost"] = removeCommaFromPrice($row[$v]);
                                    $updateInfo[$k] = removeCommaFromPrice($row[$v]) * 1.10;
                                } else {
                                    $updateInfo[$k] = removeCommaFromPrice($row[$v]);
                                }
                            } else if ($k == "product_height" || $k == "product_width" || $k == "product_length" ) {
                                $additionalInfo[$k] = $row[$v];
                            } else if ($k == "brand") {
                                $updateInfo['brand'] = $mp_brands[strtoupper($row[$v])] ?? '';
                            } else {
                                $updateInfo[$k] = $row[$v];
                            }
    
                            if( $k == 'image') {
                                if(count([$row[$v]]) > 0){
                                    $iamges =  $this->image_processing([$row[$v]], $image_prefix = '',$image_suffix='',$image_separator='');
                                    $image_array = (!empty($iamges)) ? array_merge([], $iamges) : null;
                                    $updateInfo['image'] = $image_array;
                                 }
                            }
    
                            if($k == "ean") $current_ean = $row[$v];

                        }

                        unset($updateInfo['ean']);

                        $cur_product = $all_products->where('ean', $current_ean)->first();
                        if (blank($cur_product)) continue;

                        // if ($wantedToUpdateEkPrice) {
                        //     if ($cur_product->calculation_id != null && intval($cur_product->calculation_id) > 0) {
                        //         app(\App\Http\Controllers\AdminMarketplaceProductsController::class)
                        //             ->updateAssignCalc($cur_product->id);
                        //     }
                        // }
    
                        if($updateInfo['image']){
                           $newImages = $updateInfo['image'] ?? [];
                           $oldImages = $cur_product->old_images ?? [];
                           if(array_diff($newImages,$oldImages)){
                                $updateInfo['is_image_process'] = 0;
                           }
                           $updateInfo['image']  = array_merge($cur_product->image,array_diff($newImages,$oldImages)); //update new images
                           $updateInfo['old_images']  = array_merge($oldImages,array_diff($newImages,$oldImages)); //update old images
                        }
                        
                        $cur_product->update($updateInfo);
                        if ($wantedToUpdateEkPrice) {
                            app(\App\Http\Controllers\AdminMarketplaceProductsController::class)->manualRealShippingCostUpdate($cur_product, "ek_price");
                        }
                        if(count($additionalInfo) > 0) {
                            $cur_product->additionalInfo()->updateOrCreate(['product_id' => $cur_product->id], $additionalInfo ?? []);
                        }

                        if (!blank($cur_product->core_products)) {
                            app(\App\Http\Controllers\AdminMarketplaceProductsController::class)->syncUpdateWithDrmProduct($cur_product);
                        }
                        $data['updateCount']++;

                    }
    
                    if ($fromDropMatrix) {
                        $supplier = \App\DeliveryCompany::find(request()->supplier_id)->name;
                    } else {
                        $supplier = User::find(\CRUDBooster::myParentId())->name;
                    }
    
                    $url = url('/admin/marketplace_products');
                    $message_title = "{$supplier}'s "." {$data['updateCount']} product's info are updated during manual csv update. To see click the link.";
                    User::find(\App\Enums\Apps::DROPMATIX_ID)->notify( new DRMNotification($message_title, 'Manual CSV Update', $url) );
    
                    Collection::find(Session::get('update_collection_id'))->update([
                        'updated_csv' => [
                            'file_link' => Session::get('updated_csv_path'),
                            'updated_at' => Carbon::now(),
                        ],
                    ]);
    
                    $data['importSstatus'] = 'success';
                    return view('marketplace.collection.manual-import-finished', $data);
                }
            } catch (\Exception $exception) {
                return redirect(\CRUDBooster::mainpath('import-data'))->with(['message' => 'Something went wrong', 'message_type' => 'error']);
            }
        }

        public function getDeliveryCompanyName ($id)
        {
        	return \App\DeliveryCompany::find($id)->name;
        }

        public function generateCollectionStatusColumn ($id)
        {
            if ( $this->pendingCountMapArr[$id] == 0 ) {
                return $status = '<span class="btn btn-xs btn-success" style="width: 100%">Approved</span>';
            } else {
                return $status = '<span class="btn btn-xs btn-warning" style="width: 100%">'.$this->pendingCountMapArr[$id].' - Pending</span>';
            }
        }

        public function image_processing($product_all_image,$image_prefix,$image_suffix,$image_separator){
            $images = [];
            foreach($product_all_image as $image){
                if(!empty($image_prefix) && !empty($image_suffix) && !empty($image_separator)){
                    if(substr($image_prefix,-1) != '/'){
                        $image_prefix = $image_prefix.'/';
                    }
                    if(substr($image_suffix,0,1) != '.'){
                        $image_suffix = '.'.$image_suffix;
                    }
                    $image_arrs = explode($image_separator,$image);
                    foreach($image_arrs as $image_arr){
                        if(!empty($image_arr)){
                            $img_url = $image_prefix.$image_arr.$image_suffix;
                            array_push($images, $img_url);
                        }
                    }

                }else if(empty($image_prefix) && !empty($image_suffix) && !empty($image_separator)){
                    if(substr($image_suffix,0,1) != '.'){
                        $image_suffix = '.'.$image_suffix;
                    }
                    $image_arrs = explode($image_separator,$image);

                    foreach($image_arrs as $image_arr){
                        if(!empty($image_arr)){
                            $img_url = $image_arr.$image_suffix;
                            array_push($images, $img_url);
                        }
                    }
                }else if(!empty($image_prefix) && empty($image_suffix) && !empty($image_separator)){
                    if(substr($image_prefix,-1) != '/'){
                        $image_prefix = $image_prefix.'/';
                    }
                    $image_arrs = explode($image_separator,$image);

                    foreach($image_arrs as $image_arr){
                        if(!empty($image_arr)){
                            $img_url = $image_prefix.$image_arr;
                            array_push($images, $img_url);
                        }
                    }
                }else{
                    $semicolon = explode(';', $image);
                    $coma = explode(',', $image);
                    $pipe = explode('|', $image);
                    $space = explode(' ', $image);
                    if (is_array($semicolon) && count($semicolon) > 1) {
                        foreach ($semicolon as $url) array_push($images, $url);
                    } elseif (is_array($coma) && count($coma) > 1) {
                        foreach ($coma as $url) array_push($images, $url);
                    } elseif (is_array($pipe) && count($pipe) > 1) {
                        foreach ($pipe as $url) array_push($images, $url);
                    } elseif (is_array($space) && count($space) > 1) {
                        foreach ($space as $url) array_push($images, $url);
                    } else array_push($images, $image);
                }
            }
            return $images;
        }

        public function getCsvCategoryList(){
            $column_name = request()->column_name;
            if($column_name){
                if( is_relavida() || in_array(CRUDBooster::myParentId(), [2817])){
                    $mainCategories = ChannelUserCategory::where('user_id', 2694)->where('channel', 10)->where('parent',0)->orderBy('category_name')->get()->unique('category_name');
                    $parent_categories = ChannelUserCategory::with('child_category')->where('user_id', 2694)->where('channel', 10)->where('parent', '!=', 0)->orderBy('category_name')->get()->unique('category_name');
                } else {
                    $mainCategories = Category::where('is_active',1)->whereNull('parent_id')->orWhere('parent_id',0)->orderBy('name')->get();
                    $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->orderBy('name')->get(['id','name']);
                }
                $unique_categories = [];
                foreach(json_decode(Cache::store('file')->get('mp_csv_content'.'-'.CRUDBooster::myParentId()),true) as $data){
                    if(!in_array($data[$column_name],$unique_categories)){
                        $unique_categories[] = $data[$column_name];
                    }
                }
                return view('marketplace.collection.inc.import_csv_category',compact('parent_categories','unique_categories','mainCategories'));
            }

        }

        public function getSelectedIndustryTemplate(){
            $templates = request()->template;
            if($templates){
                return view('marketplace.collection.inc.industry_template',compact('templates'));
            }else{
                return '';
            }
        }

        public function postCheckUvpLowerThenEkPrice(){
            $csv_datas = json_decode(Cache::store('file')->get('mp_csv_content'.'-'.CRUDBooster::myParentId()),true);
            $rows =collect($csv_datas)->unique(request()->ean)->toArray();

            if($rows){
                if(request()->ek_price && request()->uvp){
                    $rows = json_decode(Cache::store('file')->get('mp_csv_content'.'-'.CRUDBooster::myParentId()),true);
                    
                    $html = '';
                    foreach($rows as $row){
                        if(removeCommaFromPrice($row[request()->ek_price]) > removeCommaFromPrice($row[request()->uvp])){
                            $html .= '<tr><td>'.$row[request()->ean].'</td><td>'.number_format($row[request()->ek_price],2).'</td><td>'.number_format($row[request()->uvp],2).'</td></tr>';
                        }
                    }
    
                    if($html){
                        return response()->json(['status' => 'error','html' => $html]);
                    }else{  
                        return response()->json(['status' => 'success']);
                    }
                }
            }else{
               return response()->json(['status' => 'success']); 
            }
        }
        public function uvpPrice(){
            return view('marketplace.collection.updateuvp');
        }


        //update uvp price
        public function csvUvpPriceUpdate()
        {
            if(Request::hasFile('userfile')){
                $category_map = "";
                $file = Request::file('userfile');
                $ext = $file->getClientOriginalExtension();
                Session::put('collection_name',Carbon::now().'-'.pathinfo(Request::file('userfile')->getClientOriginalName(), PATHINFO_FILENAME));
                $validator = Validator::make([
                    'extension' => $ext,
                ], [
                    'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV,txt,TXT,XML,xml',
                ]);
                if ($validator->fails()) {
                    $message = $validator->errors()->all();
                    return redirect()->back()->with(['message' => implode('<br/>', $message), 'message_type' => 'warning']);
                }
                $type = pathinfo($file, PATHINFO_EXTENSION);
                $rows = $this->csvToArray($file,$type,'auto',false);
                $validateCsv = $rows ? array_key_exists('Product Codes: EAN',$rows[0]) && array_key_exists('List Price: Highest',$rows[0]) : false;
                if(!$validateCsv){
                    CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Invalid csv Imported", "warning");
                }
                UpdateProductsUvp::dispatch($rows);
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "File Imported Successfully", "success");
            }

        }
        //Download Ean List Function
        function downloadEanlist(){
            ini_set('max_execution_time', '0');
            //$str_ids = explode (",", request()->ids);
            //dd($request->ids);
            //if($_GET['status']==1){
                $products = DB::connection('marketplace')->table('marketplace_products')->whereIn('id',request()->ids)->select('ean')->groupBy('ean')->get()->toArray();
                return response($products);
                //dd($products);
                // }else{
            //     $str_ids = explode (",", $_GET['products']);
            //     $products = DB::connection('marketplace')->table('marketplace_products')->whereIn('id',$str_ids)->select('ean')->where('ean','!=','')->groupBy('ean')->get()->toArray();
            // }
            //$products = DB::table('marketplace_products')->select('ean')->where('marketplace_product_id','!=','')->where('ean','!=','')->groupBy('ean')->get();
            $headings = array_keys((array)$products[0] ?? []);
            $chunks = array_chunk($products, 10000);
            $name = Carbon::now()->format('d-m-Y');
            $a = 0;
            foreach($chunks as $key => $value){
                $a++;
                Excel::store(new MarketplaceProductsExport($value, $headings, true), 'eans/'.$name.'/products_ean_list'.$a.'.csv');
            }
            $zip = new ZipArchive;
            $fileName = $name.'.zip';
            File::delete('product_eans/'.$fileName);
            $path = public_path().'/product_eans';
            if (!file_exists($path)) {
                //dd("not");
                File::makeDirectory($path, $mode = 0777, true, true);
            }
            if ($zip->open(public_path('product_eans/'.$fileName), ZipArchive::CREATE) === TRUE)
            {
                $files = File::files(storage_path('app/eans/'.$name));
                foreach ($files as $key => $value) {
                    $relativeNameInZipFile = basename($value);
                    $zip->addFile($value, $relativeNameInZipFile);
                }
                $zip->close();
            }
            //dd(public_path('product_eans/'.$fileName));
            File::deleteDirectory(storage_path('app/eans/'.$name));
            return response($fileName);

        }

        function downloadEanlistroot(){
            ini_set('max_execution_time', '0');

            if($_GET['status']==1){
                $products = DB::connection('marketplace')->table('marketplace_products')->select('ean')->where('ean','!=','')->groupBy('ean')->get()->toArray();
            }else{
                $str_ids = explode (",", $_GET['products']);
                $products = DB::connection('marketplace')->table('marketplace_products')->whereIn('id',$str_ids)->select('ean')->where('ean','!=','')->groupBy('ean')->get()->toArray();
            }
            //$products = DB::table('marketplace_products')->select('ean')->where('marketplace_product_id','!=','')->where('ean','!=','')->groupBy('ean')->get();
            $headings = array_keys((array)$products[0] ?? []);
            $chunks = array_chunk($products, 10000);
            $name = Carbon::now()->format('d-m-Y');
            $a = 0;
            foreach($chunks as $key => $value){
                $a++;
                Excel::store(new MarketplaceProductsExport($value, $headings, true), 'eans/'.$name.'/products_ean_list'.$a.'.csv');
            }
            $zip = new ZipArchive;
            $fileName = $name.'.zip';
            File::delete('product_eans/'.$fileName);
            $path = public_path().'/product_eans';
            if (!file_exists($path)) {
                //dd("not");
                File::makeDirectory($path, $mode = 0777, true, true);
            }
            if ($zip->open(public_path('product_eans/'.$fileName), ZipArchive::CREATE) === TRUE)
            {
                $files = File::files(storage_path('app/eans/'.$name));
                foreach ($files as $key => $value) {
                    $relativeNameInZipFile = basename($value);
                    $zip->addFile($value, $relativeNameInZipFile);
                }
                $zip->close();
            }
            File::deleteDirectory(storage_path('app/eans/'.$name));
            return response()->download(public_path('product_eans/'.$fileName));

        }

        public function finaldownload(){
            $file = $_GET['path'];
            return response()->download(public_path('product_eans/'.$file));
        }

       public function importMpReview(){
          $page_title = __('marketplace.import_product_review');
          return view('marketplace.collection.mp-review-import',compact('page_title'));
       }

       public function csvMpReviewImport(){

            if(Request::hasFile('userfile')){
                $category_map = "";
                $file = Request::file('userfile');
                $ext = $file->getClientOriginalExtension();
                Session::put('collection_name',Carbon::now().'-'.pathinfo(Request::file('userfile')->getClientOriginalName(), PATHINFO_FILENAME));
                $validator = Validator::make([
                    'extension' => $ext,
                ], [
                    'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV,txt,TXT,XML,xml',
                ]);
                if ($validator->fails()) {
                    $message = $validator->errors()->all();
                    return redirect()->back()->with(['message' => implode('<br/>', $message), 'message_type' => 'warning']);
                }
                $type = pathinfo($file, PATHINFO_EXTENSION);
                $rows = $this->csvToArray($file,$type,'auto',false);
                $validateCsv = $rows ? array_key_exists('Product Codes: EAN',$rows[0]) && array_key_exists('Reviews: Rating',$rows[0]) : false;
                if(!$validateCsv){
                    CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Invalid csv Imported", "warning");
                }
                foreach(array_chunk($rows, 1000) as $reviewRating){
                    dispatch(new MarketplaceReviewAndRating($reviewRating));
                }
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "File Imported Successfully", "success");
            }
       }

       public function postSaveImage(Req $request): JsonResponse
       {
           try {
               // Check if a file is uploaded
               if ($request->hasFile('file')) { // $request is the instance, not the facade
                   $image = $request->file('file');
                   $allowedTypes = ['jpg', 'jpeg', 'png', 'gif'];
       
                   // Validate the file type
                   $imageType = strtolower($image->getClientOriginalExtension());
                   if (in_array($imageType, $allowedTypes)) {
                       // Upload the image
                       $imageUrl = uploadImage($image, 'marketplace-products/' . CRUDBooster::myParentId());
       
                       return response()->json([
                           'success' => true,
                           'name'    => $imageUrl
                       ], 200);
                   } else {
                       // Invalid file type
                       return response()->json([
                           'success' => false,
                           'message' => 'Invalid file type. Allowed types are jpg, jpeg, png, and gif.'
                       ], 422);
                   }
               } else {
                   // No file uploaded
                   return response()->json([
                       'success' => false,
                       'message' => 'No file uploaded.'
                   ], 400);
               }
           } catch (\Exception $e) {
               // Handle exceptions
               return response()->json([
                   'success' => false,
                   'message' => 'An error occurred while uploading the image.',
                   'error'   => $e->getMessage()
               ], 500);
           }
       }
       
    }
