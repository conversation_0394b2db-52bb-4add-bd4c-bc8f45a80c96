<?php

namespace App\Http\Controllers;

use CB;
use DB;
use File;
use Route;
use Request;
use Session;
use App\User;
use \Datetime;
use Exception;
use ZipArchive;
use CRUDBooster;
use App\AppStore;
// use App\Country;
use Carbon\Carbon;
use App\DrmProduct;
use App\Enums\Apps;
use App\BillingDetail;
use App\Enums\Channel;
use App\DeliveryCompany;
use App\Mail\DRMSEndMail;
use App\Models\DrmCategory;
use Illuminate\Support\Arr;
// use Illuminate\Validation\Rule;
use Illuminate\Support\Str;
use App\MarketplaceProducts;
// use Illuminate\Validation\Rule;
use Illuminate\Http\Response;
use App\DropmatixProductBrand;
use App\Enums\PermissionStatus;
use App\Services\DRM\DRMService;
// use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use App\Models\ChannelUserCategory;
use App\Models\Marketplace\Product;
use Illuminate\Http\Request as Req;
use Illuminate\Support\Facades\Log;
// use Illuminate\Support\Facades\URL;

// use App\Models\Marketplace\Collection;
use App\Models\Marketplace\Category;
use App\Models\Marketplace\Delivery;
use App\Models\Marketplace\PreOrder;
use App\Models\Marketplace\Supplier;
// use App\Models\Marketplace\Collection;
use App\Services\Payment\PaymentTax;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;
use App\Enums\Marketplace\PriceRange;
// use Illuminate\Support\Facades\DB as FacadesDB;
// use App\Models\Marketplace\ProductSyncedHistory;
use App\Models\Marketplace\Collection;
use App\Models\Marketplace\UserAccess;
use App\Notifications\DRMNotification;
use App\Models\Marketplace\ApiCategory;
use App\Models\Marketplace\DirectOrder;
use App\Services\ChannelProductService;
use Illuminate\Support\Facades\Storage;
use App\Enums\Marketplace\FakeSuppliers;
use App\Enums\Marketplace\ProductStatus;
use App\Models\Export\PlusHDeliveryNote;

use App\Jobs\ChannelManager\AutoTransfer;
use App\Models\Marketplace\ApiCredential;
use Illuminate\Support\Facades\Validator;

// use crocodicstudio\crudbooster\helpers\CRUDBooster as HelpersCRUDBooster;
use \App\Enums\Marketplace\ShippingMethod;
use App\Services\Marketplace\PlusHService;
use App\Models\Marketplace\MpApiScaledPrice;
use App\Models\Marketplace\SupplierPlushLog;
use App\Models\MarketplaceProfitCalculation;
use App\Services\Marketplace\ProductService;
use App\Services\ProductApi\TransferProduct;
use App\Models\Marketplace\KeepaProductPrice;
use App\Services\Marketplace\NewWarehouseService;

// use crocodicstudio\crudbooster\helpers\CRUDBooster as HelpersCRUDBooster;
use App\Models\Marketplace\MarketplaceSponsor;
use App\Models\Marketplace\MpSendStockComment;
use App\Jobs\Marketplace\ProductBrandChangeJob;
use App\Jobs\MarketplaceAllProductStatusUpdate;
use App\Jobs\RemoveProductsFromBanededChannels;
use App\Models\Export\MarketplaceProductsExport;
use App\Jobs\Marketplace\MarketplaceIMHandelSync;
use App\Services\Marketplace\InternelSyncService;
use App\Jobs\Marketplace\UpdateUserCategoryAccess;
use App\Models\Marketplace\FulfilmentStockSendLog;
use App\Models\Marketplace\MarketplaceSyncedOrder;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Models\Marketplace\MarketplaceAllowedChannel;
use App\Models\Marketplace\MarketplaceParentCategory;
use App\Jobs\Marketplace\BulkMarketplaceProductDelete;
use App\Jobs\Marketplace\MarketplaceCategorySyncToDrm;
use App\Jobs\Marketplace\SendMailProductListIntoPlusH;
use App\Jobs\Marketplace\MarketplaceProductTransferToDrm;
use App\Jobs\Marketplace\SendMailProductListIntoInternel;
use App\Jobs\Marketplace\MarketplaceProductCalculationJob;
use App\Jobs\Marketplace\MarketplaceProductDiscountSyncToDrm;
use App\Models\Marketplace\ScaledPriceDiscountForDirectOrder;
use App\Jobs\Marketplace\MarketplaceProductsDirectTransferToDrmUser;
use App\Models\Marketplace\ProductSafetyGPSR;
use App\Services\Marketplace\ProductService as MarketplaceProductService;


class AdminMarketplaceProductsController extends \crocodicstudio\crudbooster\controllers\MarketplaceCBController
{
    protected $syncedProductsStock = [];
    protected $isSuperAdmin;
    protected $suppliersService;
    public $productService;
    public $channels;
    public $updated_channel;
    protected $searchableproducts;
    protected $indexBuilder = false;

    public function __construct()
    {
        $this->productService = new \App\Services\Marketplace\ProductService();
    }

    public function cbInit()
    {

        $this->isSuperAdmin = CRUDBooster::isSuperadmin();
        $this->suppliersService = new \App\Services\Marketplace\SuppliersService();
        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "50";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = false;
        $this->button_edit = false;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = false;
        $this->button_import = false;
        $this->button_export = false;

        $this->table = "marketplace_products";
        # END CONFIGURATION DO NOT REMOVE THIS LINE



        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => __("marketplace.id_and_status"), "name" => "id"];
        $this->col[] = ["label" => __("Image"), "name" => "image"];
        $this->col[] = ["label" => __("_Title"), "name" => "name"];
        $this->col[] = ["label" => __("Ean"), "name" => "ean"];
        $this->col[] = ["label" =>  !isPatrickSpecial() ? __("Ek_Price") : __("next CB"), "name" => "ek_price"];
        $this->col[] = ["label" =>  __("marketplace.Real Shipping Cost"), "name" => "real_shipping_cost"];

        if ($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
            $this->col[] = ["label" => __("marketplace.Vk Price"), "name" => "vk_price"];
            $this->col[] = ["label" => __("marketplace.Shipping_Cost"), "name" => "shipping_cost"];
        }

        //$this->col[] = ["label" => __("UVP"), "name" => "uvp"];
        $this->col[] = ["label" => __("UVP"), "name" => "uvp"];
        
        //$this->col[] = ["label" => __("NewUVP"), "name"=>"ean",  "callback_php" => '$this->CompareUVP($row->ean)'];

        if ($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
            $this->col[] = ["label" => __("IM Handel"), "name" => "im_handel"];
            $this->col[] = ["label" => __("Profit"), "name" => "vk_price"];
            $this->col[] = ["label" => __("Api Category"), "name" => "api_category_id"];
        }


        $this->col[] = ["label" => __("Category"), "name" => "category"];

        $this->col[] = ["label" => __("marketplace.Stock"), "name" => "stock"];

        // $this->col[] = ["label" => ("SA"), "name" => "internel_stock"];
        // $this->col[] = ["label"=>("TA"),"name"=>"ta"];
        $this->col[] = ["label"=>__("marketplace.Send Stock"),"name"=>"atw"];
        $this->col[] = ["label"=>__("marketplace.defect_stock"),"name"=>"defect_stock"];
        // $this->col[] = ["label" => 'Shipping method', "name" => 'shipping_method', "callback_php" => '$row->shipping_method==2?$row->id."-Fullfilment":"Dropshipping"'];
        $this->col[] = ["label" => __("Shipping method"), "name" => 'shipping_method'];

        // $this->col[] = ["label" => 'Stock Update', "name" => "internel_stock"];

        // $this->col[] = ["label" => "Status", "name" => "status"];
        // $this->col[] = ["label" => "Fulfilment Status", "name" => "status"];
        if($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
            $this->col[] = ["label" => __("Supplier"), "name"=>"id",  "callback_php" => '$this->getProductDeliveryCompany($row->id)'];
            //$this->col[] = ["label" => __("Contact Person"), "name"=>"id",  "callback_php" => '$this->getProductDeliveryCompany($row->id)'];
        }
        if ($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
            $start7Date     = Carbon::now()->subDays(7);
            $start30Date    = Carbon::now()->subDays(30);
            $start60Date    = Carbon::now()->subDays(60);
            $endDate        = Carbon::now()->addDays(1);

            $salesColumns = [
                [
                    "label" => __("marketplace.Sales 7 days"),
                    "name" => "(select sum(marketplace_product_sales_information.sales_stock) from marketplace_product_sales_information where marketplace_product_sales_information.marketplace_product_id = marketplace_products.id and created_at between '{$start7Date}' and '{$endDate}') as sales_7_days"
                ],
                [
                    "label" => __("marketplace.Sales figure 7 days"),
                    "name" => "(select CAST(SUM(marketplace_product_sales_information.sales_amount) AS DECIMAL(10, 2)) from marketplace_product_sales_information where marketplace_product_sales_information.marketplace_product_id = marketplace_products.id and created_at between '{$start7Date}' and '{$endDate}') as sales_figure_7_days","callback" => function ($row) {
                        return  $row->sales_figure_7_days ? "<i class='fa fa-euro'></i> ".$row->sales_figure_7_days: '';
                    }
                ],
                [
                    "label" => __("marketplace.Sales 30 days"),
                    "name" => "(select sum(marketplace_product_sales_information.sales_stock) from marketplace_product_sales_information where marketplace_product_sales_information.marketplace_product_id = marketplace_products.id and created_at between '{$start30Date}' and '{$endDate}') as sales_30_days"
                ],
                [
                    "label" => __("marketplace.Sales figure 30 days"),
                    "name" => "(select CAST(SUM(marketplace_product_sales_information.sales_amount) AS DECIMAL(10, 2)) from marketplace_product_sales_information where marketplace_product_sales_information.marketplace_product_id = marketplace_products.id and created_at between '{$start30Date}' and '{$endDate}') as sales_figure_30_days","callback" => function ($row) {
                        return  $row->sales_figure_30_days ? "<i class='fa fa-euro'></i> ".$row->sales_figure_30_days: '';
                    }
                ],
                [
                    "label" => __("marketplace.Sales 60 days"),
                    "name" => "(select sum(marketplace_product_sales_information.sales_stock) from marketplace_product_sales_information where marketplace_product_sales_information.marketplace_product_id = marketplace_products.id and created_at between '{$start60Date}' and '{$endDate}') as sales_60_days"
                ],
                [
                    "label" => __("marketplace.Sales figure 60 days"),
                    "name" => "(select CAST(SUM(marketplace_product_sales_information.sales_amount) AS DECIMAL(10, 2)) from marketplace_product_sales_information where marketplace_product_sales_information.marketplace_product_id = marketplace_products.id and created_at between '{$start60Date}' and '{$endDate}') as sales_figure_60_days","callback" => function ($row) {
                        return  $row->sales_figure_60_days ? "<i class='fa fa-euro'></i> ".$row->sales_figure_60_days: '';
                    }
                ]
            ];

            $this->col = array_merge($this->col, $salesColumns);
            $this->col[] = ["label" => __("total_seller"), "name" => "total_no_transferred"];
        }
        // $this->col[] = ["label" => __("Top Product"), "name" => "is_top_product"];// for task Delate "Top Angebote"
        if (CRUDBooster::isSupplier()) {
            $this->col[] = ["label" => __("marketplace.alarm_quantity"), "name" => "alarm_quantity"];
            $this->col[] = ["label" => __("FWDL"), "name" => "internel_send_date"];
            $this->col[] = ["label" => __("Visibility"), "name" => "category_id",  "callback_php" => '$this->visibilityLight($row->category_id,$row->id,$row->status)'];
        }
        $this->col[] = ["label" => __("marketplace.cubic_meter"), "name" => "cubic_meters"];
        $this->col[] = ["label" => __("marketplace.brand"), "name" => "brand", "callback_php" => '$this->getProductBrand($row->brand)'];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        // $this->form = [];
        $this->form[] = ['label' => 'Name', 'name' => 'name', 'type' => 'text', 'validation' => 'required|string|min:3|max:70', 'width' => 'col-sm-10', 'placeholder' => 'You can only enter the letter only'];
        if ($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport())
            $this->form[] = ['label' => 'Collection', 'name' => 'collection_id', 'type' => 'select2', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'datatable' => 'marketplace_collections,name'];
        else
            $this->form[] = ['label' => 'Collection', 'name' => 'collection_id', 'type' => 'select2', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'datatable' => 'marketplace_collections,name', 'datatable_where' => CRUDBooster::myParentId() . "=marketplace_collections.supplier_id"];

        // $this->form[] = ['label'=>'Parent Id','name'=>'parent_id','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'parent,id'];
        $this->form[] = ['label' => 'Ean', 'name' => 'ean', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Price', 'name' => 'price', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Description', 'name' => 'description', 'type' => 'textarea', 'validation' => 'required|string|min:5|max:5000', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Category', 'name' => 'category_id', 'type' => 'select2', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'datatable' => 'marketplace_categories,name'];
        $this->form[] = ['label' => 'Image', 'name' => 'image', 'type' => 'upload', 'validation' => 'image|max:3000', 'width' => 'col-sm-10', 'help' => 'File types support : JPG, JPEG, PNG, GIF, BMP'];
        $this->form[] = ['label' => 'Item Color', 'name' => 'item_color', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Brand', 'name' => 'brand', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];

        $this->form[] = ['label' => 'Item Number', 'name' => 'item_number', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Purchase Price', 'name' => 'purchase_price', 'type' => 'money', 'validation' => 'integer|min:0', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Vat', 'name' => 'vat', 'type' => 'money', 'validation' => 'integer|min:0', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Stock', 'name' => 'stock', 'type' => 'number', 'validation' => 'required|integer|min:0', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Item Weight', 'name' => 'item_weight', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Item Size', 'name' => 'item_size', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Note', 'name' => 'note', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Production Year', 'name' => 'production_year', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Materials', 'name' => 'materials', 'type' => 'text', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Tags', 'name' => 'tags', 'type' => 'textarea', 'validation' => 'string|min:5|max:5000', 'width' => 'col-sm-10'];
        $this->form[] = ['label' => 'Update Enabled', 'name' => 'update_enabled', 'type' => 'select', 'validation' => 'min:1|max:255', 'width' => 'col-sm-10', 'dataenum' => '1|Yes;0|No'];

        // if ($this->isSuperAdmin || \CRUDBooster::isDropMatrix()) // for task Delate "Top Angebote"
        //     $this->form[] = ['label' => 'Is Top Product', 'name' => 'is_top_product', 'type' => 'radio', 'validation' => 'integer', 'width' => 'col-sm-10', 'dataenum' => '1|Yes;0|No'];
        # END FORM DO NOT REMOVE THIS LINE

        # Start Column management
        $defaultColumnSupplier = ['id', 'image', 'name', 'ean', 'ek_price','real_shipping_cost', 'uvp', 'category', 'stock', 'internel_stock', 'ta','atw','defect_stock','shipping_method', 'alarm_quantity', 'internel_send_date', 'Visibility'];
        $fixedSelectedColumnSupplier = ['id', 'image', 'name', 'ean', 'ek_price', 'real_shipping_cost', 'uvp', 'category', 'stock', 'internel_stock','ta','atw', 'defect_stock','shipping_method'];

        // $defaultColumnNonSupplier = ['id', 'image', 'name', 'ean', 'ek_price', 'vk_price', 'shipping_cost', 'uvp', 'api_category_id', 'category', 'stock', 'internel_stock', 'ta', 'atw', 'defect_stock','shipping_method'];
        $fixedSelectedColumnNonSupplier = ['id', 'image', 'name', 'ean', 'ek_price','real_shipping_cost', 'vk_price', 'shipping_cost', 'uvp', 'im_handel', 'api_category_id', 'category', 'stock', 'internel_stock', 'ta', 'atw', 'defect_stock','shipping_method'];

        if (CRUDBooster::isSupplier()) {
            $defaultColumn = $defaultColumnSupplier;
            $fixedSelectedColumn = $fixedSelectedColumnSupplier;
        } else {
            // $defaultColumn = $defaultColumnNonSupplier;
            $defaultColumn = $fixedSelectedColumn = $fixedSelectedColumnNonSupplier;
        }

        $savedColumn = DB::table('drm_user_saved_columns')
            ->where('user_id', CRUDBooster::myId())
            ->where('table_name', $this->table)
            ->pluck('columns')
            ->first();

        if ($savedColumn) {
            $defaultColumn = array_merge($fixedSelectedColumn, json_decode($savedColumn));
        }

        $action = CRUDBOoster::mainpath('mp-column-save');
        $token = csrf_token();
        $checkboxdata = '<form id="show-hide-form-table" style="display:none;position: relative;width: 100%;background: #fff;padding: 10px 15px;margin-bottom:5px;" method="post" action="'.$action.'"><input type="hidden" name="_token" value="'.$token.' "><input type="hidden" name="table_name" value="'.$this->table.' "><h5 class="box-title">Table column:</h5>';

        foreach ($this->col as $key => $singleColumn) {
            $columnName = $singleColumn['name'];

            if (!CRUDBooster::isSupplier()) {
                if ($singleColumn['label'] === __("marketplace.Sales 7 days")) {
                    $columnName = 'sales_7_days';
                } elseif ($singleColumn['label'] === __("marketplace.Sales figure 7 days")) {
                    $columnName = 'sales_figure_7_days';
                } elseif ($singleColumn['label'] === __("marketplace.Sales 30 days")) {
                    $columnName = 'sales_30_days';
                } elseif ($singleColumn['label'] === __("marketplace.Sales figure 30 days")) {
                    $columnName = 'sales_figure_30_days';
                } elseif ($singleColumn['label'] === __("marketplace.Sales 60 days")) {
                    $columnName = 'sales_60_days';
                } elseif ($singleColumn['label'] === __("marketplace.Sales figure 60 days")) {
                    $columnName = 'sales_figure_60_days';
                }
            }

            $selected = in_array($columnName, $defaultColumn) ? 'checked' : '';
            $disabled = in_array($columnName, $fixedSelectedColumn) ? 'disabled' : '';

            if (!in_array($columnName, $defaultColumn)) {
                unset($this->col[$key]);
            }

            $checkboxdata .= '<div class="single-ct-checkbox"><input type="checkbox" id="" class="" name="saved_columns[]" value="'.$columnName.'" '.$selected.' '.$disabled.'> '.$singleColumn['label'].'</div>';
        }

        $checkboxdata .= '<div class="ct-btn-wrap"><input type="submit" class="btn btn-drm" value="Apply"></div></form>';

        # End Column management
        // if(CRUDBooster::isSupplier()){
        //     $default_column=['id', 'image', 'name', 'ean', 'ek_price','shipping_cost', 'uvp', 'category', 'stock', 'internel_stock', 'shipping_method', 'alarm_quantity', 'internel_send_date', 'Visibility'];
        //     $fixed_selected_column=['id', 'image', 'name', 'ean', 'ek_price','shipping_cost', 'uvp', 'category', 'stock', 'internel_stock','ta','atw', 'shipping_method'];
        //     $saved_colum=DB::table('drm_user_saved_columns')
        //     ->where('user_id',CRUDBooster::myParentId())
        //     ->where('table_name',$this->table)->pluck('columns')->first();
        //     if($saved_colum){
        //       $default_column = array_merge($fixed_selected_column,json_decode($saved_colum));
        //     }
        //     $action= CRUDBOOster::mainpath('mp-column-save');
        //     $token=csrf_token();
        //     $checkboxdata= '<form id="show-hide-form-table" style="display:none;position: relative;width: 100%;background: #fff;padding: 10px 15px;margin-bottom:5px;" method="post" action="'.$action.'"><input type="hidden" name="_token" value="'.$token.' "><input type="hidden" name="table_name" value="'.$this->table.' "><h5 class="box-title">Table column:</h5>';
        //     foreach($this->col as $key=>$single_column){
        //     $selected='checked';
        //     $disabled = '';
        //     if(!in_array($single_column['name'], $default_column)){
        //         unset($this->col[$key]);
        //         $selected='';
        //     }

        //     if(in_array($single_column['name'], $fixed_selected_column)){
        //         $disabled = 'disabled';
        //     }

        //     $checkboxdata.= '<div class="single-ct-checkbox"><input  type="checkbox" id="" class="" name="saved_columns[]" value="'.$single_column['name'].'" '.$selected.' '.$disabled.'> '.$single_column['label'].'</div>';
        //     }
        //     $checkboxdata.= '<div class="ct-btn-wrap"><input type="submit" class="btn btn-drm" value="Apply"></div></form>';
        // }
        /*
    | ----------------------------------------------------------------------
    | Sub Module
    | ----------------------------------------------------------------------
    | @label          = Label of action
    | @path           = Path of sub module
    | @foreign_key    = foreign key of sub table/module
    | @button_color   = Bootstrap Class (primary,success,warning,danger)
    | @button_icon    = Font Awesome Class
    | @parent_columns = Sparate with comma, e.g : name,created_at
    |
    */
        $this->sub_module = array();


        /*
    | ----------------------------------------------------------------------
    | Add More Action Button / Menu
    | ----------------------------------------------------------------------
    | @label       = Label of action
    | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
    | @icon        = Font awesome class icon. e.g : fa fa-bars
    | @color       = Default is primary. (primary, warning, succecss, info)
    | @showIf      = If condition when action show. Use field alias. e.g : [id] == 1 CRUDBooster::mainpath('stock/[id]')
    |
    */
        $this->addaction = array();

        // $this->addaction[] = [
        //     'title' => 'See Internel stock', 'name' => 'see_available_stock', 'url' => 'javascript:get_available_stock([id])', 'icon' => 'fa fa-info',
        //     'color' => 'primary', 'showIf' => '\App\Models\Marketplace\Product::isInternel([id])'
        // ];
        
        $this->addaction[] = [
            'title' => 'Product Missing Info', 'url' => 'javascript:mpProductMissingInfo([id])', 'icon' => 'fa fa-info',
            'color' => 'primary',
        ];



        // if ($this->isSuperAdmin) {
        //     // $this->addaction[] = ['label'=>'Login','url'=>CRUDBooster::mainpath('top-product/[id]'),'icon'=>'fa fa-minus','color'=>'warning','showIf' => '[is_top_product] == 1'];
        //     $this->addaction[] = ['title' => 'Remove From Top Product', 'name' => 'is_top_product', 'url' => CRUDBooster::mainpath('top-product/[id]'), 'icon' => 'fa fa-minus', 'color' => 'primary', 'showIf' => '[is_top_product] == 1'];
        //     $this->addaction[] = ['title' => 'Mark As Top Product', 'name' => 'is_top_product', 'url' => CRUDBooster::mainpath('top-product/[id]'), 'icon' => 'fa fa-plus', 'color' => 'success', 'showIf' => '[is_top_product] == 0'];
        // }
        //        if ($this->isSuperAdmin) {
        //            $this->addaction[] = ['title' => 'Send Order', 'showIf' => '[marketplace_product_id] != null', 'icon' => 'fa fa-share', 'url' => CrudBooster::mainpath('make-order/[id]')];


        // <a class="btn btn-xs btn-primary product-clone" title="Product Clone" href="javascript:void(0)" data-product-id="18"><i class="fa fa-copy"></i></a>
        //        }

        $this->addaction[] = ['title' => 'Clone Product', 'icon' => 'fa fa-copy', 'url' => 'javascript:riseCloneModal([id])'];
        $this->addaction[] = [
            'title' => __("crudbooster.action_delete_selected"),
            'icon' => 'fa fa-trash',
            'color' => 'warning',
            'url' => 'javascript:deleteMarketplaceProduct([id])',
        ];
        
        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon        = Icon from fontawesome
        | @name        = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();
        // $title = __('marketplace.confirm_shipment');
        // $this->button_selected[] = ['label' => $title , 'icon' => 'fa fa-refresh fa-spin', 'name' => 'sync_product'];

        $puls_h_title = __('marketplace.warehouse_master_data');
        $this->button_selected[] = ['label' => $puls_h_title , 'icon' => 'fa fa-refresh fa-spin', 'name' => 'sync_new_warehouse_product'];

        $puls_h_title = __('marketplace.warehouse_delivery_note');
        $this->button_selected[] = ['label' => $puls_h_title , 'icon' => 'fa fa-refresh fa-spin', 'name' => 'delivery_note_new_warehouse'];
        // $puls_h_title = 'confirm material';
        // $this->button_selected[] = ['label' => $puls_h_title , 'icon' => 'fa fa-refresh fa-spin', 'name' => 'confirm_material'];

        //  $this->button_selected[] = ['label'=>'Export Products', 'icon'=>'fa fa-download', 'name'=>'export_products'];
        // $this->button_selected[] = ['label' => 'Sync Delivery Report', 'icon' => 'fa fa-refresh', 'name' => 'sync_delivery_reports'];
        // $this->button_selected[] = ['label' => 'Make an Order', 'icon' => 'fa fa-cart-arrow-down make_an_order', 'name' => 'make_an_order'];


        /*
    | ----------------------------------------------------------------------
    | Add alert message to this module at overheader
    | ----------------------------------------------------------------------
    | @message = Text of message
    | @type    = warning,success,danger,info
    |
    */
        $this->alert        = array();
        $message = __('marketplace.please_note_that_shipments_for_our_warehouse');
        $hasWaitingSync = SupplierPlushLog::where('user_id',\CRUDBooster::myParentID())->whereDate('updated_at','>=', Carbon::now()->subdays(4))->exists();
        if(\CRUDBooster::isSupplier() && $hasWaitingSync){
            $this->alert[]=['message' => $message, 'type' => 'danger'];
        }



        /*
    | ----------------------------------------------------------------------
    | Add more button to header button
    | ----------------------------------------------------------------------
    | @label = Name of button
    | @url   = URL Target
    | @icon  = Icon from Awesome.
    |
    */
        $this->index_button = array();



        /*
    | ----------------------------------------------------------------------
    | Customize Table Row Color
    | ----------------------------------------------------------------------
    | @condition = If condition. You may use field alias. E.g : [id] == 1
    | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
    |
    */
        $this->table_row_color = array();


        /*
    | ----------------------------------------------------------------------
    | You may use this bellow array to add statistic at dashboard
    | ----------------------------------------------------------------------
    | @label, @count, @icon, @color
    |
    */
        $this->index_statistic = array();



        /*
    | ----------------------------------------------------------------------
    | Add javascript at body
    | ----------------------------------------------------------------------
    | javascript code in the variable
    | $this->script_js = "function() { ... }";
    |
    */
        $this->script_js = "
        $(document).find('.make_an_order').parent().attr('class','make_order_btn');
        $(document).on('click','.make_order_btn',function(event) {
            event.stopPropagation();
            event.preventDefault();
            swal.close();
            $('#makeOrderForm').modal('show');
        });
        $(document).on('click','.inactive',function(event) {
            let is_admin = $(this).data('status');
            if(is_admin == 1) {
                swal({ title:`Missing Filed Details!`, text: 'VK Price is Required', icon: 'warning', confirmButtonColor: '#008D4C', confirmButtonText:  'Ok', closeOnConfirm: true })
            }
        });



        function riseCloneModal (productId) {
            var hitUrl = ADMIN_PATH+'/marketplace_products/product-clone-modal';
            $.ajax({
                url: hitUrl,
                method: 'GET',
                data: { id: productId },
                success: function(response) {
                    $('.product-clone-modal-container').html(response);
                    $('#product-clone-form-modal').modal('show');
                }
            });
        }


    ";


        /*
    | ----------------------------------------------------------------------
    | Include HTML Code before index table
    | ----------------------------------------------------------------------
    | html code to display it before index table
    | $this->pre_index_html = "<p>test</p>";
    |
    */
    $this->pre_index_html = $checkboxdata;



        /*
    | ----------------------------------------------------------------------
    | Include HTML Code after index table
    | ----------------------------------------------------------------------
    | html code to display it after index table
    | $this->post_index_html = "<p>test</p>";
    |
    */




        $this->post_index_html = '
        <div class="modal fade" id="getStockModal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title changable-title">Stock Info </h4>
                    </div>
                    <div class="modal-body changable-body">
                        <table class="table table-bordered table-sm">
                            <tr>
                                <th>Product ID : </th>
                                <td class="product_id"></td>
                            </tr>
                            <tr>
                                <th>Available : </th>
                                <td class="available_free"></td>
                            </tr>
                            <tr>
                                <th>Damaged : </th>
                                <td class="damaged"></td>
                            </tr>
                            <tr>
                                <th>Reserved : </th>
                                <td class="reserved"></td>
                            </tr>
                            <tr>
                                <th>Returned : </th>
                                <td class="returned"></td>
                            </tr>
                        </table>
                            <p style="color:#ff1800;" class="stock_notify"></p>
                    </div>
                </div>
              </div>
        </div>

        <div class="modal fade" id="makeOrderForm" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title changable-title">Stock Info </h4>
                    </div>
                    <div class="modal-body changable-body">

                    </div>
                </div>
              </div>
        </div>


        <div class="modal fade" id="product-clone-form-modal" tabindex="-1" role="dialog">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <p>Clone Product</p>
                    </div>
                    <div class="modal-body changable-body product-clone-modal-container" style="position: relative; padding: 15px; overflow-y: auto; max-height: 500px;">

                    </div>
                </div>
            </div>
        </div>
    ';



        /*
    | ----------------------------------------------------------------------
    | Include Javascript File
    | ----------------------------------------------------------------------
    | URL of your javascript each array
    | $this->load_js[] = asset("myfile.js");
    |
    */
        $this->load_js = array();
        $this->load_js[] = asset('//cdnjs.cloudflare.com/ajax/libs/x-editable/1.5.0/bootstrap3-editable/js/bootstrap-editable.min.js');
        //        $this->load_js[] = asset('js/marketplace_products.js');




        /*
    | ----------------------------------------------------------------------
    | Add css style at body
    | ----------------------------------------------------------------------
    | css code in the variable
    | $this->style_css = ".style{....}";
    |
    */
        $this->style_css = NULL;



        /*
    | ----------------------------------------------------------------------
    | Include css File
    | ----------------------------------------------------------------------
    | URL of your css each array
    | $this->load_css[] = asset("myfile.css");
    |
    */
        $this->load_css = array();
        $this->load_css[] = asset('//cdnjs.cloudflare.com/ajax/libs/x-editable/1.5.0/bootstrap3-editable/css/bootstrap-editable.css');
    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {

        if ($button_name == 'sync_product') {
            //this function does not work now it was for internel warehouse
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Internel api not available for now', 'warning');
            if (count($id_selected) > 50) {
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Maximum 50 products should be allowed.", 'warning');
            }
            $products =  DB::connection('marketplace')->table('marketplace_products')
                ->whereIn('marketplace_products.id', $id_selected)->where('marketplace_products.status',1)->where('marketplace_products.stock','>',0)
                ->select("marketplace_products.id", "marketplace_products.ean", "marketplace_products.stock","marketplace_products.marketplace_product_id")
                ->leftJoin('fulfilment_stock_send_log', function($join) {
                    $join->on('marketplace_products.id', '=', 'fulfilment_stock_send_log.Product_id');
                })->whereNull('fulfilment_stock_send_log.is_left');

            $collection = collect($products->get());
            $newSend =  $collection->where('marketplace_product_id','=',null)->pluck('id')->toArray();

            $approvedSelectedProductId = $products->pluck('marketplace_products.id')->toArray();
            $mailSendProducts = $products->select('marketplace_products.id','marketplace_products.ean','marketplace_products.stock')->get();

            if(!$approvedSelectedProductId){
                $mesage = __('marketplace.Please select approved fulfillment products');
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $mesage, 'warning');
            }
            $newResponse = false;
            if(!empty($newSend)){  // Send only new product
              $res = app(InternelSyncService::class)->transferProductData($newSend);
              $newResponse = true;
            }
            // Sending stock
             $result = app(InternelSyncService::class)->transferIncomingDeliveries($approvedSelectedProductId);


            if (count($approvedSelectedProductId) > 0) {
                if($result){
                    $successDeliveryIdInfo = (array)$result['successDeliveryId'];
                    $successDeliveryId = $successDeliveryIdInfo[0];
                    $datasync['internel_delivery_id'] = $successDeliveryId;
                    $datasync['status'] = ProductStatus::WAITING_SYNC;
                    if(!$newResponse){
                        $datasync['internel_sync_status'] = \App\Enums\Marketplace\ProductStatus::WAITING_SYNC;
                        $datasync['internel_send_date'] =  Carbon::now();
                    }
                    Product::whereIn('id', $result['productsIds'])->update($datasync);

                    Product::whereIn('id', $result['productsIds'])->update([
                        'internel_delivery_id' => $successDeliveryId,
                        'status' => ProductStatus::WAITING_SYNC
                    ]);

                    Delivery::create([
                        'supplier_id'   => CRUDBooster::myParentId(),
                        'delivery_id'   => $successDeliveryId,
                        'delivery_date' => Carbon::now()->todatestring(),
                        'product_ids'   => $result['productsIds'],
                    ]);

                    foreach($mailSendProducts as $product){
                        try{
                            $sendproduct = FulfilmentStockSendLog::where('product_id',$product->id)->first();
                            if($sendproduct !=null){
                                $data['send_stock'] = ($sendproduct->send_stock + $product->stock);
                                if(($sendproduct->send_stock + $product->stock) > $sendproduct->send_stock){
                                    $data['send_more'] = (($sendproduct->send_stock + $product->stock) - $sendproduct->send_stock);
                                }
                                $sendproduct->update($data);
                            }else{
                                \App\Models\Marketplace\FulfilmentStockSendLog::create([
                                  'product_id' => $product->id,
                                  'send_stock' => $product->stock
                                ]);
                            }
                        }catch(\Exception $e){
                            Log::info('!ops Error'.$e->getMessage());
                        }
                    }
                    $pdfUrl = $this->deliveryBillCloudUploadPdf($mailSendProducts);
                    dispatch(new SendMailProductListIntoInternel($pdfUrl));

                }

                 $message = __('marketplace.your_shipment_has_been_flagged').' '.'<a href="'.$pdfUrl.'" class="btn btn-warning btn-sm" target="_blank" download>'.__('Download').'</a>';
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $message , 'info');
            } else {
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "No products synced !", 'warning');
            }
        }
        if ($button_name == 'sync_new_warehouse_product') {
            $is_Supplier = \CRUDBooster::isSupplier();
            $productsQuery = Product::with(['additionalInfo' => function ($query) use ($is_Supplier){
                if($is_Supplier){
                    $query->where('product_length', '<=', 120)
                          ->where('product_width',  '<=', 60)
                          ->where('product_height', '<=', 60)
                          ->where('marketplace_products.item_weight', '<=', 31.5);
                }}])
                ->where('atw', '>', 0)
                ->whereIn('marketplace_products.id', $id_selected)
                ->where('marketplace_products.status', ProductStatus::ACTIVE)
                ->where('marketplace_products.shipping_method', ShippingMethod::FULFILLment)
                ->where('marketplace_products.internel_sync_status', '<>', ProductStatus::WAITING_SYNC)
                ->select('marketplace_products.id', 
                         'marketplace_products.ean',
                         'marketplace_products.item_number', 
                         'marketplace_products.name',
                         'marketplace_products.stock',
                         'marketplace_products.atw',
                         'marketplace_products.item_weight');
        

            $approvedSelectedProducts = $productsQuery->get();
            $message = __('marketplace.new_warehouse_master_data_send_allowed_message');

            if (blank($approvedSelectedProducts)) {
                return $this->redirectBackWithMessage($message, 'warning');
            }

            if ($is_Supplier) {
                $approvedSelectedProducts = $approvedSelectedProducts->filter(fn($product) => $product->additionalInfo !== null);
        
                if (blank($approvedSelectedProducts)) {
                    return $this->redirectBackWithMessage($message, 'warning');
                }
            }
        
            $datasync = [
                'internel_sync_status' => ProductStatus::WAITING_SYNC,
                'internel_send_date' => Carbon::now()
            ];
            Product::whereIn('id', $approvedSelectedProducts->pluck('id'))->update($datasync);
    
            $deliveryDate = Carbon::now();
            $warehouseResponse = app(NewWarehouseService::class)->transferProductData($approvedSelectedProducts, $deliveryDate);
            $message = __('marketplace.your_shipment_has_been_flagged').' '.'<a href="'.$warehouseResponse.'" class="btn btn-warning btn-sm" target="_blank" download>'.__('Download').'</a>';
            return $this->redirectBackWithMessage($message, 'success');

        }
        

        if ($button_name == 'delivery_note_new_warehouse') {
            try {
                $products = Product::with('additionalInfo')
                    ->where('atw', '>', 0)
                    ->whereIn('id', $id_selected)
                    ->where('shipping_method', ShippingMethod::FULFILLment)
                    ->where('internel_sync_status', ProductStatus::WAITING_SYNC)
                    ->select('id', 'ean', 'item_number', 'name', 'atw', 'stock')
                    ->get();
        
                if ($products->isEmpty()) {
                    return $this->redirectBackWithMessage(
                        __('marketplace.new_warehouse_delivery_note_send_allowed_message'), 
                        'warning'
                    );
                }
        
                // $deliveryId = uniqid();
                // $deliveryDate = Carbon::now();
                $delivery_note_response = app(NewWarehouseService::class)->transferDeliveryNoteData($products, false);
                
                if ($delivery_note_response->getData()->success !== true) {
                    return $this->redirectBackWithMessage(
                        $delivery_note_response->getData()->message, 
                        'warning'
                    );
                }
        
                foreach ($products as $product) {
                    $fulfilment_log = $product->stockSendLog;
                    
                    $product->update([
                        'internel_send_date' =>  Carbon::now(),
                        'internel_sync_status' => ProductStatus::INTERNAL_SYNC_APPROVED,
                    ]);
                    
                    if ($fulfilment_log) {
                        $fulfilment_log->update(['send_stock' => $product->atw]);
                    } else {
                        \App\Models\Marketplace\FulfilmentStockSendLog::create([
                            'product_id' => $product->id,
                            'send_stock' => $product->atw,
                        ]);
                    }
                }
                
                $deliveryNotePdf = app(NewWarehouseService::class)->deliveryNotePdf($products);
                $message = __('marketplace.downloadable_delivery_note_pdf') . ' ' .
                    '<a href="' . $deliveryNotePdf->getData()->delivery_note_link . '" class="btn btn-warning btn-sm" target="_blank" download>' .
                    __('Download') . '</a>';
        
                return $this->redirectBackWithMessage(
                    __('marketplace.synced_with') . $products->count() . 
                    __('marketplace.products_successfully') . $message, 
                    'success'
                );
        
            } catch (\Exception $e) {
                \Log::error('Delivery Note Error: ' . $e->getMessage(), ['exception' => $e]);
        
                return $this->redirectBackWithMessage(
                    __('Something went wrong...'), 
                    'warning'
                );
            }
        }
        

        if($button_name == 'sync_pulsh_product'){
            $products = Product::with(['additionalInfo' => function($additionalInfo) {
                $additionalInfo->select('product_id', 'product_length', 'product_width', 'product_height', 'volume');
            },'stockSendComment'=> function($stockSendComment) {
                $stockSendComment->select('id', 'mp_product_id', 'send_stock_comment');
            },'stockSendLog'])->whereIn('marketplace_products.id', $id_selected)
            ->where('marketplace_products.status', 1)
            ->where('marketplace_products.shipping_method', ShippingMethod::FULFILLment)
            ->where('marketplace_products.stock', '>', 0)
            ->whereNull('marketplace_products.internel_delivery_id')
            ->select('marketplace_products.id', 'marketplace_products.ean','marketplace_products.item_number', 'marketplace_products.name','marketplace_products.stock','marketplace_products.item_weight','marketplace_products.ta')
            ->leftJoin('fulfilment_stock_send_log', function($join) {
                $join->on('marketplace_products.id', '=', 'fulfilment_stock_send_log.Product_id');
            })
            ->whereNull('fulfilment_stock_send_log.is_left');
            $approvedSelectedProductId = $products->pluck('id')->toArray();
            if(!$approvedSelectedProductId){
                $mesage = __('marketplace.Please select Allowed Fulfillment products or select Send stock more than 0 products or select products without waiting sync');
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $mesage, 'warning');
            }
            $deleveryId = uniqid();
            $deliveryDate = Carbon::now();
            $approvedSelectedProducts = $products->get();
            $ftpProductResponse       = app(PlusHService::class)->transferProductData($approvedSelectedProducts,$deliveryDate);
            if(!is_object($ftpProductResponse)){
                try {
                    $datasync['status'] = ProductStatus::WAITING_SYNC;
                    $datasync['internel_sync_status'] = ProductStatus::WAITING_SYNC;
                    $datasync['internel_send_date'] =  Carbon::now();
                    Product::whereIn('id',$approvedSelectedProductId)->update($datasync);
                    Delivery::create([
                        'supplier_id'   => CRUDBooster::myParentId(),
                        'delivery_id'   => $deleveryId,
                        'delivery_date' => $deliveryDate,
                        'product_ids'   => $approvedSelectedProductId,
                    ]);
                    $csvFile = $this->plusHdeliveryBillCsv($approvedSelectedProducts,$deleveryId,$deliveryDate);
                    // dispatch(new SendMailProductListIntoPlusH($csvFile));
                    $message = __('marketplace.your_shipment_has_been_flagged').' '.'<a href="'.$csvFile.'" class="btn btn-warning btn-sm" target="_blank" download>'.__('Download').'</a>';
                    $supplierPlushLog = SupplierPlushLog::updateOrCreate(['user_id'=>\CRUDBooster::myParentID()]);
                    $supplierPlushLog ->touch();
                    \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $message , 'info');

               }catch(\Exception $e){
                 Log::info('!ops Error'.$e->getMessage());
                 \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Somthing Wrong......', 'warning');
               }
            }else{
               \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $ftpProductResponse->getData()->message, 'warning');
            }
        }

        if ($button_name == 'sync_delivery_reports') {
            $result = app(InternelSyncService::class)->transferIncomingDeliveries($id_selected);


            $successDeliveryIdInfo = (array)$result['successDeliveryId'];
            $successDeliveryId = $successDeliveryIdInfo[0];

            Product::whereIn('id', $result['productsIds'])->update([
                'internel_delivery_id' => $successDeliveryId,
                'status' => ProductStatus::WAITING_SYNC
            ]);

            if ($result['addedCount'] > 0) {
                Delivery::create([
                    'supplier_id'   => CRUDBooster::myParentId(),
                    'delivery_id'   => $successDeliveryId,
                    'delivery_date' => Carbon::now()->todatestring(),
                    'product_ids'   => $result['productsIds'],
                ]);

                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Delivery ID " . $successDeliveryId . " synced with " . count($result['productsIds']) . " products successfully.", 'info');
            } else {
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Something went wrong !", 'warning');
            }
        }

        if ($button_name == 'delete') {
            $products = Product::whereIn('id', $id_selected)->select('id', 'ean', 'delivery_company_id')->get();
        
            $uniqueKeys = $products->map(function ($product) {
                return [
                    'ean' => $product->ean,
                    'delivery_company_id' => $product->delivery_company_id,
                ];
            });

            $deletableProducts = Product::select('id', 'image', 'ean')
                ->with([
                    'MpCoreDrmTransferProduct:marketplace_product_id,drm_product_id,user_id',
                    'core_products:marketplace_product_id,id,user_id',
                ])
                ->where(function ($query) use ($uniqueKeys) {
                    foreach ($uniqueKeys as $key) {
                        $query->orWhere(function ($subQuery) use ($key) {
                            $subQuery->where('ean', $key['ean'])
                                     ->where('delivery_company_id', $key['delivery_company_id']);
                        });
                    }
                })
                ->get();
        
            $deletableProducts->chunk(100)->each(function ($deletableChunk) {
                dispatch(new BulkMarketplaceProductDelete($deletableChunk));
            });
        
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "The product deletion process now starts in the background.", 'info');

        }
        
        // if ($button_name == 'delete') {
        //     $deletable_products = [];
        //     $products = Product::whereIn('id', $id_selected)->select('id', 'ean', 'delivery_company_id')->get();
        //     foreach($products as $product){
        //         $deletable_products[] = Product::select('id', 'image', 'ean')
        //         ->with([
        //             'MpCoreDrmTransferProduct:marketplace_product_id,drm_product_id,user_id',
        //             'core_products:marketplace_product_id,id,user_id',
        //         ])
        //         ->where('ean', $product->ean)
        //         ->where('delivery_company_id', $product->delivery_company_id)
        //         ->get();
        //     }


        //     collect($deletable_products)->chunk(100)->each(function ($deletable_product) {
        //         dispatch(new BulkMarketplaceProductDelete($deletable_product));
        //     });

        //     \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "The product deletion process now starts in the background.", 'info');
        // }

        if ($button_name == 'export_products') {
            $data['products'] = Product::whereIn('id', $id_selected)->get();
            $pdf_view = 'marketplace.product.export_pdf';

            $pdfStream = \PDF::loadView($pdf_view, $data)->setWarnings(false)->stream();

            //            Mail Send to . . .
            $data = [];
            $data['email_from'] = '<EMAIL>';
            $data['email_to']   = '<EMAIL>';
            $data['subject']    = 'Product Lists';

            app('drm.mailer')->getMailer(CRUDBooster::myParentId(),$data['email_from'])->send('marketplace.mail_template.product_export', $data, function ($messages) use ($data, $pdfStream) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);
                $messages->attachData($pdfStream, 'products.pdf', [
                    'mime' => 'application/pdf',
                ]);
            });

            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect(
                $_SERVER['HTTP_REFERER'],
                $res['syncedProductCount'] . "Mail Sent:: Please check your email to see product list. Thank you.",
                'info'
            );
        }

        if ($button_name == 'make_an_order') {
            dd('This feature not activated.',  $id_selected);
        }
        if($button_name == 'delivery_note_plush'){
            $products = Product::with('stockSendLog')
                                ->select('id', 'ean', 'item_number', 'name', 'ta', 'stock')
                                ->whereIn('id', $id_selected)
                                ->where('shipping_method', ShippingMethod::FULFILLment)
                                ->where('atw',0)
                                ->whereDate('internel_send_date', '<=', Carbon::now()->subDays(5))
                                ->where(function ($query) {
                                    $query->where(function ($query) {
                                        $query->where('status', ProductStatus::WAITING_SYNC)
                                            ->where('internel_sync_status', ProductStatus::WAITING_SYNC);
                                    })->orWhere(function ($query) {
                                        $query->whereNotNull('internel_delivery_id')
                                            ->where('stock','>',0);
                                    });
                                })
                                ->get();

           if($products->isEmpty()){
             $mesage = __('marketplace.Can not send this products for delivery before 5 days');
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $mesage, 'warning');
          }
          else{
            $deliveryId = uniqid();
            $deliveryDate = Carbon::now();
            $ftpDeliveryNoteResponse = app(PlusHService::class)->transferDeliveryNoteData($products,$deliveryId,$deliveryDate);
            if(!is_object($ftpDeliveryNoteResponse)){
                try{
                foreach($products as $product){
                        $fulfilment_log = $product->stockSendLog;
                        $product_data['ta'] = $product->stock;
                        $product_data['internel_delivery_id'] = $deliveryId;
                        $product_data['stock'] = 0;
                        $product_data['internel_sync_status'] = ProductStatus::INTERNAL_SYNC_APPROVED;
                        $product_data['status'] = ProductStatus::ACTIVE;
                        $product->update($product_data);
                        if($fulfilment_log !=null){
                            $data['send_stock'] = $product->ta;
                            $fulfilment_log->update($data);
                        }else{
                            \App\Models\Marketplace\FulfilmentStockSendLog::create([
                            'product_id' => $product->id,
                            'send_stock' => $product->ta
                            ]);
                        }
                }
                $deliveryDate = $deliveryDate->format('d.m.y');
                $deliveryNotePdf = app(PlusHService::class)->deliveryNotePdf($products,$deliveryId,$deliveryDate);
                $message = __('marketplace.downloadable_delivery_note_pdf').' '.'<a href="'.$deliveryNotePdf.'" class="btn btn-warning btn-sm" target="_blank" download>'.__('Download').'</a>';
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], __('marketplace.delivery_id') . $deliveryId . __('marketplace.synced_with') . count($id_selected) .__('marketplace.products_successfully').$message, 'success');
            } catch (\Exception $e) {

                        Log::info('!ops Error' . $e->getMessage());

                        \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Somthing Wrong......', 'warning');
                    }
            }else{
               \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $ftpDeliveryNoteResponse->getData()->message, 'warning');
            }
        }
        }
        if($button_name == 'confirm_material'){
            $products = Product::select('id','ta','atw')->whereIn('id',$id_selected)->where('ta','>',0)->get();
            if($products->isEmpty()){
                $mesage = __('marketplace.please_select_those_products');
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $mesage, 'warning');
            }

            foreach($products as $product){
                $product->update([
                    'atw' => $product->ta,
                    'ta' => 0
                ]);
            }
            $mesage = __('marketplace.your_confirmation_was_successfull');
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $mesage, 'success');
        }
    }
    public function deliveryBillCloudUploadPdf(object $products){
        $pdf_view = 'marketplace.product.internel.internel_send_product_pdf';
        $randomStr = substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', mt_rand(1, 5))), 1, 40);
        $fileName = 'marketplace-internel-product' . '/' . $randomStr . ".pdf";
        $pdf = \PDF::loadView($pdf_view, compact('products'))->setPaper('a4', 'vertical')->setWarnings(false);
        Storage::disk('spaces')->put($fileName, $pdf->output(), 'public');
        return Storage::disk('spaces')->url($fileName);
    }

    public function plusHdeliveryBillCsv($approvedSelectedProducts,$deleveryId,$deliveryDate){
        $fileName = 'plusH-product-delivery-note' . '/'.$deleveryId.'-'.'plush-delivery-note.csv';
        $content = app(PlusHService::class)->deliveryNoteContent($approvedSelectedProducts,$deleveryId,$deliveryDate);
        // Excel::store(new PlusHDeliveryNote($deliveryProducts), 'plus-h-delivery-note.csv');
        Storage::disk('spaces')->put($fileName,  $content, 'public');
        return Storage::disk('spaces')->url($fileName);
    }

    public function postActionSelected()
    {

        if ($_REQUEST['select_all'] == 0) {
            $this->cbLoader();
            $id_selected = Request::input('checkbox');
        } else {
            $_REQUEST['only_products'] = 1;
            $products = $this->getIndex();
            if ($products) {
                $id_selected = $products->pluck('id')->toArray();
                $skipProductIds = explode(',', $_REQUEST['remove_product_ids']);
                $id_selected = array_diff($id_selected, $skipProductIds);
            } else {
                $id_selected = null;
            }
        }
        $button_name = Request::input('button_name');

        if (!$id_selected) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans("crudbooster.alert_select_a_data"), 'warning');
        }

        $this->actionButtonSelected($id_selected, $button_name);
    }



    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
        public function hook_query_index(&$query)
    {
        //Filter product based on search and store session for not forgetting last filter
        //STATUS FILTER
        if (Request::get('status')) {
            $st = Request::get('status');

            if (Request::get('status') == 'all') {
                $query = $query;
                // Session::put('filter_status_' . CRUDBooster::myParentId(), Request::get('status'));
            } else {
                if ($st == 'pending') $col_val = ProductStatus::PENDING;
                if ($st == 'approved') $col_val = ProductStatus::ACTIVE;
                if ($st == 'rejected') $col_val = ProductStatus::INACTIVE;
                if ($st == 'incomplete') $col_val = ProductStatus::INCOMPLETE;
                if ($st == 'blocked') $col_val = ProductStatus::BLOCKED;
                if ($st == 'quality') {
                    $col_val = ProductStatus::QUALITY_DEFECT;
                }

                if ($st == 'pending') {
                    $query->where('vk_price', ">", 0)->where('status', 0)->where('is_dublicate',0);

                } else if ($st == 'deletable') {
                     $query->where('is_dublicate',0)
                        ->where(function ($query) {
                        $query->where(function ($query) {
                            $query->where('shipping_method', 1)
                                ->where('stock', 0);
                        });
                    });

                }else if ($st == 'ean') {
                    $query->where('is_dublicate', 1);

                } else if ($col_val == 3) {
                    $query->where('vk_price', "<=", 0)->where('is_dublicate',0);

                } else if ($st == 'waiting_sync') {
                    $query->where('status', ProductStatus::WAITING_SYNC)->where('internel_sync_status', ProductStatus::WAITING_SYNC);
                } else {
                    $query->where('status', $col_val)->where('is_dublicate',0);
                }
            }
            Session::put('filter_status_' . CRUDBooster::myParentId(), Request::get('status'));

        } else if (Session::has('filter_status_' . CRUDBooster::myParentId())) {
            $st = Session::get('filter_status_' . CRUDBooster::myParentId());
            if ($st != 'all') {
                if ($st == 'pending') $col_val = 0;
                if ($st == 'approved') $col_val = 1;
                if ($st == 'rejected') $col_val = 2;
                if ($st == 'incomplete') $col_val = 3;
                if ($st == 'blocked') $col_val = 4;
                if ($st == 'quality') {
                    $col_val = ProductStatus::QUALITY_DEFECT;
                }

                if ($st == 'pending') {
                    $query->where('vk_price', ">", 0)->where('status', 0)->where('is_dublicate',0);
                } else if ($st == 'deletable') {
                    $query->where('is_dublicate',0)
                        ->where(function ($query) {
                        $query->where(function ($query) {
                            $query->where('shipping_method', 1)
                                ->where('stock', 0);
                        });
                    });
                }else if ($st == 'ean') {
                    $query->where('is_dublicate', 1);

                } else if ($col_val == 3) {
                    $query->where('vk_price', "<=", 0)->where('is_dublicate',0);
                } else if ($st == 'waiting_sync') {
                    $query->where('status', ProductStatus::WAITING_SYNC)->where('internel_sync_status', ProductStatus::WAITING_SYNC);
                } else {
                    $query->where('status', $col_val)->where('is_dublicate',0);
                }
            } else {
                $query = $query;
            }
        }

        //FILTER by country
        if (Request::get('country_id')) {
            $query->where('country_id', Request::get('country_id'));
            Session::put('filter_country_' . CRUDBooster::myParentId(), Request::get('country_id'));
        } else if (Session::has('filter_country_' . CRUDBooster::myParentId())) {
            $country_id = Session::get('filter_country_' . CRUDBooster::myParentId());
            $query->where('country_id', $country_id);
        } else {
            Session::put('filter_country_' . CRUDBooster::myParentId(), 1);
            $query->where('country_id', 1);
        }

        //FILTER by Supllier
        if ($_REQUEST['supplier'] != "") {
            $deliveryCompany = \App\DeliveryCompany::find($_REQUEST['supplier']);

            if ($deliveryCompany->is_marketplace_supplier && $deliveryCompany->supplier_id) {
                $query->where('supplier_id', $deliveryCompany->supplier_id);
            } else {
                $query->where('delivery_company_id', $_REQUEST['supplier']);
            }
        }

        //        For Supplier Account
        if (\CRUDBooster::isSupplier()) {
            $mp_supplier = Supplier::where('cms_user_id', \CRUDBooster::myParentId())->first();
            if ($mp_supplier) {
                $query->where('supplier_id', $mp_supplier->parent_id);
            } else {
                $query->where('supplier_id', \CRUDBooster::myParentId());
            }
        }
        
        // if (isPatrickSpecial()) {
        //     $query = $query->whereNotNull('api_id')->orWhere('shipping_method', 2);
        // } else if (CRUDBooster::isDropMatrix()) {
        //     $query = $query->whereNull('api_id');
        // }

        // START:: Filter & search execution
        if ($_REQUEST['filter_by'] != "") {
            $filterBy = $_REQUEST['filter_by'];
            if ($filterBy == 'api_category_id') {
                return $query->where('api_id', session()->get('selected_api_category'))->where('api_category_id', $_REQUEST['api_category_id'])->get();
            }
            // else if ($filterBy == 'ean') {
            //     $query->where('status', 1)->where('is_dublicate', 1)->where('best_price', 1);
            // }
            else if ($filterBy == 'not_in_fulfillment_inventory') {
                $query->where('shipping_method', 2)->where(function ($query) {
                    return $query->where('internel_stock', 0)->where('old_internel_stock', 0)
                        ->orWhere('marketplace_product_id', null);
                });
            } else if ($filterBy == 'low_stock_product') {
                $query->where('shipping_method', 2)->where('alarm_status', 1);
            } else if ($filterBy == 'visibility_light') {
                if (request()->visibility_light == 'red') {
                    $query->where('status', '!=', 1);
                } else if (request()->visibility_light == 'green') {
                    $query->where('status', 1)->whereIn('id', function ($q) {
                        $q->select('marketplace_product_id')->from('mp_core_drm_transfer_products');
                    });
                } else if (request()->visibility_light == 'yellow') {
                    $query->where('status', 1)->whereNotIn('id', function ($q) {
                        $q->select('marketplace_product_id')->from('mp_core_drm_transfer_products');
                    });

                    //->whereRaw('id NOT IN(".$test.")')->get();
                }
            } else if ($filterBy == 'category_id') { //here use session because understanding its sub cat id or main cat id
                if (\CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
                    if (strpos($_REQUEST['category_id'], "_cat")) {
                        $categoryId = explode("main_cat_", $_REQUEST['category_id'])[1];
                        $categoryList = Category::where('parent_id', $categoryId)->where('parent_id', '!=', null)->pluck('id')->toArray();
                        Session::put('filter_category_is_main_' . CRUDBooster::myParentId(), $categoryId);
                        Session::forget('filter_category_is_sub_' . CRUDBooster::myParentId());
                        $query->whereIn($filterBy, $categoryList);
                    } else {
                        Session::put('filter_category_is_sub_' . CRUDBooster::myParentId(), $_REQUEST['category_id']);
                        Session::forget('filter_category_is_main_' . CRUDBooster::myParentId());
                        $query->where($filterBy, $_REQUEST[$filterBy]);
                    }

                    // Filter with how many product transfer to the DRM
                    $total_seller_from = (int) $_REQUEST['seller_from'];
                    $total_seller_to = (int) $_REQUEST['seller_to'];

                    if( ( !empty($total_seller_from) && !empty($total_seller_to) ) || !empty($total_seller_to)){
                        $query->whereBetween('total_no_transferred', [$total_seller_from, $total_seller_to]);
                    }
                    // Filter with how many product transfer to the DRM END
                }
            } else if($filterBy == 'delivery_country') {
                $query->join('marketplace_allowed_channels', 'marketplace_products.id', '=', 'marketplace_allowed_channels.product_id')
                    ->where('marketplace_allowed_channels.country_id', $_REQUEST[$filterBy]);
            } else {
                $query->where($filterBy, $_REQUEST[$filterBy]);
            }
        }
        if((Session::get('filter_category_is_sub_' . CRUDBooster::myParentId()) || Session::get('filter_category_is_main_' . CRUDBooster::myParentId())) && !$_REQUEST['category_id'] ){
            Session::forget('filter_category_is_main_' . CRUDBooster::myParentId());
            Session::forget('filter_category_is_sub_' . CRUDBooster::myParentId());
        }

        $searchCategory = $_REQUEST['search_by_select_category'] ?? '';
        $searchCategoryId = $_REQUEST['search_by_category_id'] ?? '';

        if ($searchCategory && !in_array($searchCategory, ['visibility_light', 'low_stock_product']) && $searchCategoryId) {
            if ($searchCategory == 'category_id' && (\CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport())) {
                if (strpos($searchCategoryId, "_cat")) {
                    $categoryId = explode("main_cat_", $searchCategoryId)[1];
                    $categoryList = Category::where('parent_id', $categoryId)->whereNotNull('parent_id')->pluck('id')->toArray();
                    Session::put('filter_category_is_main_' . CRUDBooster::myParentId(), $categoryId);
                    Session::forget('filter_category_is_sub_' . CRUDBooster::myParentId());
                    $query->whereIn($searchCategory, $categoryList);
                } else {
                    Session::put('filter_category_is_sub_' . CRUDBooster::myParentId(), $searchCategoryId);
                    Session::forget('filter_category_is_main_' . CRUDBooster::myParentId());
                    $query->where($searchCategory, session('filter_by_input'));
                }
            } else if($searchCategory == 'delivery_country') {
                $query->join('marketplace_allowed_channels', 'marketplace_products.id', '=', 'marketplace_allowed_channels.product_id')
                    ->where('marketplace_allowed_channels.country_id', session('filter_by_input'));
            }else{
                $query->where($searchCategory, session('filter_by_input'));
            }

        }

        // Build query For Search
        if ($_REQUEST['search_by_select'] != null && $_REQUEST['search_by_query']) {
            if ($_REQUEST['search_by_select'] == 'ean') {
                $ean = $_REQUEST['search_by_query'] ?? '';
                $query->where('ean', $ean)->orWhereJsonContains('additional_eans',$ean)->where(function ($query) {
                    return $query->where('is_dublicate', 1)->where('best_price', 1)
                        ->orWhere('is_dublicate', 1)->where('best_price', 0)
                        ->orWhere('is_dublicate', 0)->where('best_price', 0);
                    });
            } else {
                $search_by_query = trim($_REQUEST['search_by_query']);
                if ($_REQUEST['search_by_select'] == 'item_number') {
                    $query->where('item_number', $search_by_query);
                } else {
                    if($search_by_query == 'all'){
                        $query = $query->where($_REQUEST['search_by_select'], '!=', NULL)->where($_REQUEST['search_by_select'], '!=', '');
                    } else {
                        $query = app(ProductService::class)->buildQueryBySearch($query, $_REQUEST['search_by_select'], $search_by_query);
                    }
                }
            }
        }

        // Build query for Searching with min and max value
        if (!blank($_REQUEST['search_by_select']) && !blank($_REQUEST['search_by_query_min']) && !blank($_REQUEST['search_by_query_max'])) {
            $search_by_query_min = is_numeric($_REQUEST['search_by_query_min']) ? trim($_REQUEST['search_by_query_min']) : null;
            $search_by_query_max = is_numeric($_REQUEST['search_by_query_max']) ? trim($_REQUEST['search_by_query_max']) : null;

            if ($_REQUEST['search_by_select'] == 'profit' && !\CRUDBooster::isSupplier()) {
                $query->whereRaw("ROUND(((vk_price - ek_price) / ek_price * 100), 2) BETWEEN $search_by_query_min AND $search_by_query_max");
            } else if ($_REQUEST['search_by_select'] == 'stock'){
                $search_by_query_min = (int)$search_by_query_min;
                $search_by_query_max = (int)$search_by_query_max;
                $searchColumn = $searchCategoryId == 2 ? 'internel_stock' : 'stock';
                if($searchCategory == 'shipping_method'){
                    $query->whereBetween($searchColumn, [$search_by_query_min, $search_by_query_max]);
                } else {
                    $query->where(function ($query) use ($search_by_query_min, $search_by_query_max) {
                        $query->where('shipping_method', 1)
                              ->whereBetween('stock', [$search_by_query_min, $search_by_query_max])
                              ->orWhere(function ($query) use ($search_by_query_min, $search_by_query_max) {
                                  $query->where('shipping_method', 2)
                                        ->whereBetween('internel_stock', [$search_by_query_min, $search_by_query_max]);
                              });
                    });
                }
            } else {
                $query->whereBetween($_REQUEST['search_by_select'], [$search_by_query_min, $search_by_query_max]);
            }
        } else if(!blank($_REQUEST['search_by_select']) && (!blank($_REQUEST['search_by_query_min']) || !blank($_REQUEST['search_by_query_max']))){
            $search_by_query_min = is_numeric($_REQUEST['search_by_query_min']) ? trim($_REQUEST['search_by_query_min']) : null;
            $search_by_query_max = is_numeric($_REQUEST['search_by_query_max']) ? trim($_REQUEST['search_by_query_max']) : null;

            if ($_REQUEST['search_by_select'] == 'profit' && !\CRUDBooster::isSupplier()) {
                $operator = $search_by_query_min ? '>=' : '<=';
                $searchValue = $search_by_query_min ?: $search_by_query_max;
                $query->whereRaw("round(((vk_price - ek_price) / ek_price * 100), 2) $operator $searchValue");
                
            } else if ($_REQUEST['search_by_select'] == 'stock') {
                $searchValue = $search_by_query_min ?: $search_by_query_max;
                if ($searchCategory == 'shipping_method') {
                    $searchColumn = $searchCategoryId == 2 ? 'internel_stock' : 'stock';
                    $query->where($searchColumn, (int)$searchValue);
                } else {   
                    $query->where(function ($query) use ($searchValue) {
                        $query->where('shipping_method', 1)
                              ->where('stock', (int)$searchValue)
                              ->orWhere(function ($query) use ($searchValue) {
                                  $query->where('shipping_method', 2)
                                        ->where('internel_stock', (int)$searchValue);
                              });
                    });
                          
                }
            } else {
                $condition = $search_by_query_min ? ['>=', $search_by_query_min] : ['<=', $search_by_query_max];
                $query->where($_REQUEST['search_by_select'], $condition[0], $condition[1]);                
            }
        }
        //        Sorting by column
        if (Request::get('filter_column')) {
            $query = app(ProductService::class)->buildQueryByFilterColumnSorting($query, Request::get('filter_column'));
        }

        $query = $query;
    }

    public function hook_query_index_cp(&$query)
    {

        //Filter product based on search and store session for not forgetting last filter
        //STATUS FILTER
        if (Request::get('status')) {
            $st = Request::get('status');

            if (Request::get('status') == 'all') {
                $query = $query;
                // Session::put('filter_status_' . CRUDBooster::myParentId(), Request::get('status'));
            } else {
                if ($st == 'pending') $col_val = ProductStatus::PENDING;
                if ($st == 'approved') $col_val = ProductStatus::ACTIVE;
                if ($st == 'rejected') $col_val = ProductStatus::INACTIVE;
                if ($st == 'incomplete') $col_val = ProductStatus::INCOMPLETE;
                if ($st == 'blocked') $col_val = ProductStatus::BLOCKED;
                if ($st == 'pending') {
                    $query->where('vk_price', ">", 0)->where('status', 0);
                } else if ($st == 'deletable') {
                    $query->where('is_dublicate',0)
                       ->where(function ($query) {
                       $query->where(function ($query) {
                           $query->where('shipping_method', 1)
                               ->where('stock', 0);
                       });
                   });
                }else if ($st == 'ean') {
                    $query->where('is_dublicate', 1);
                } else if ($col_val == 3) {
                    $query->where('vk_price', "<=", 0);
                } else if ($st == 'waiting_sync') {
                    $query->where('status', ProductStatus::WAITING_SYNC)->where('internel_sync_status', ProductStatus::WAITING_SYNC);
                } else {
                    $query->where('status', $col_val);
                }
            }
            Session::put('filter_status_' . CRUDBooster::myParentId(), Request::get('status'));

        } else if (Session::has('filter_status_' . CRUDBooster::myParentId())) {
            $st = Session::get('filter_status_' . CRUDBooster::myParentId());
            if ($st != 'all') {
                if ($st == 'pending') $col_val = 0;
                if ($st == 'approved') $col_val = 1;
                if ($st == 'rejected') $col_val = 2;
                if ($st == 'incomplete') $col_val = 3;
                if ($st == 'blocked') $col_val = 4;
                if ($st == 'pending') {
                    $query->where('vk_price', ">", 0)->where('status', 0);
                } else if ($st == 'deletable') {
                    $query->where('is_dublicate',0)
                       ->where(function ($query) {
                       $query->where(function ($query) {
                           $query->where('shipping_method', 1)
                               ->where('stock', 0);
                       });
                   });
                }else if ($st == 'ean') {
                    $query->where('is_dublicate', 1);
                } else if ($col_val == 3) {
                    $query->where('vk_price', "<=", 0);
                } else if ($st == 'waiting_sync') {
                    $query->where('status', ProductStatus::WAITING_SYNC)->where('internel_sync_status', ProductStatus::WAITING_SYNC);
                } else {
                    $query->where('status', $col_val);
                }
            }
        }

        //FILTER by Supllier
        if ($_REQUEST['supplier'] != "") {
            $deliveryCompany = \App\DeliveryCompany::find($_REQUEST['supplier']);

            if ($deliveryCompany->is_marketplace_supplier && $deliveryCompany->supplier_id) {
                $query->where('supplier_id', $deliveryCompany->supplier_id);
            } else {
                $query->where('delivery_company_id', $_REQUEST['supplier']);
            }
        }

        //For Supplier Account
        if (\CRUDBooster::isSupplier()) {
            $mp_supplier = Supplier::where('cms_user_id', \CRUDBooster::myParentId())->first();
            if ($mp_supplier) {
                $query->where('supplier_id', $mp_supplier->parent_id);
            } else {
                $query->where('supplier_id', \CRUDBooster::myParentId());
            }
        }
        // START:: Filter & search execution
        if ($_REQUEST['filter_by'] != "") {
            $filterBy = $_REQUEST['filter_by'];
            if ($filterBy == 'api_category_id') {
                $query->where('api_id', session()->get('selected_api_category'))->where('api_category_id', $_REQUEST['api_category_id']);
            }else if ($filterBy == 'ean') {
                $query->where('status', 1)->where('is_dublicate', 1)->where('best_price', 1);
            } else if ($filterBy == 'not_in_fulfillment_inventory') {
                $query->where('shipping_method', 2)->where(function ($queryT) {
                    return $queryT->where('internel_stock', 0)->where('old_internel_stock', 0)
                        ->orWhere('marketplace_product_id', null);
                });
            } else if ($filterBy == 'low_stock_product') {
                $query->where('shipping_method', 2)->where('alarm_status', 1);
            } else if ($filterBy == 'visibility_light') {
                if (request()->visibility_light == 'red') {
                    $query->where('status', '!=', 1);
                } else if (request()->visibility_light == 'green') {
                    $query->where('status', 1)->whereIn('id', function ($q) {
                        $q->select('marketplace_product_id')->from('mp_core_drm_transfer_products');
                    });
                } else if (request()->visibility_light == 'yellow') {
                    $query->where('status', 1)->whereNotIn('id', function ($q) {
                        $q->select('marketplace_product_id')->from('mp_core_drm_transfer_products');
                    });

                    //->whereRaw('id NOT IN(".$test.")')->get();
                }
            } else {
                $query->where($filterBy, $_REQUEST[$filterBy]);
            }
        }

        $searchCategory = $_REQUEST['search_by_select_category'] ?? '';
        $searchCategoryId = $_REQUEST['search_by_category_id'] ?? '';

        if ($searchCategory && !in_array($searchCategory, ['visibility_light', 'low_stock_product']) && $searchCategoryId) {
            if ($searchCategory == 'category_id' && (\CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport())) {
                if (strpos($searchCategoryId, "_cat")) {
                    $categoryId = explode("main_cat_", $searchCategoryId)[1];
                    $categoryList = Category::where('parent_id', $categoryId)->whereNotNull('parent_id')->pluck('id')->toArray();
                    Session::put('filter_category_is_main_' . CRUDBooster::myParentId(), $categoryId);
                    Session::forget('filter_category_is_sub_' . CRUDBooster::myParentId());
                    $query->whereIn($searchCategory, $categoryList);
                } else {
                    Session::put('filter_category_is_sub_' . CRUDBooster::myParentId(), $searchCategoryId);
                    Session::forget('filter_category_is_main_' . CRUDBooster::myParentId());
                    $query->where($searchCategory, session('filter_by_input'));
                }
            } else if($searchCategory == 'delivery_country') {
                $query->join('marketplace_allowed_channels', 'marketplace_products.id', '=', 'marketplace_allowed_channels.product_id')
                    ->where('marketplace_allowed_channels.country_id', session('filter_by_input'));
            } else {
                $query->where($searchCategory, session('filter_by_input'));
            }
        }

        // Build query For Search
        if ($_REQUEST['search_by_select'] != null && $_REQUEST['search_by_query']) {
            if ($_REQUEST['search_by_select'] == 'ean') {
                $ean = $_REQUEST['search_by_query'] ?? '';
                $query->whereJsonContains('additional_eans',$ean)->orWhere('ean', $ean)->where(function ($queryT) {
                    return $queryT->where('is_dublicate', 1)->where('best_price', 1)
                        ->orWhere('is_dublicate', 1)->where('best_price', 0)
                        ->orWhere('is_dublicate', 0)->where('best_price', 0);
                });
            } else {
                $search_by_query = trim($_REQUEST['search_by_query']);
                if ($_REQUEST['search_by_select'] == 'item_number') {
                    $query->where('item_number', $search_by_query);
                } else {
                    if($search_by_query == 'all'){
                        $query->where($_REQUEST['search_by_select'], '!=', NULL)->where($_REQUEST['search_by_select'], '!=', '');
                    } else {
                        app(ProductService::class)->buildQueryBySearch($query, $_REQUEST['search_by_select'], $search_by_query);
                    }
                }
            }
        }

        // Build query for Searching with min and max value
        if (!blank($_REQUEST['search_by_select']) && !blank($_REQUEST['search_by_query_min']) && !blank($_REQUEST['search_by_query_max'])) {
            $search_by_query_min = is_numeric($_REQUEST['search_by_query_min']) ? trim($_REQUEST['search_by_query_min']) : null;
            $search_by_query_max = is_numeric($_REQUEST['search_by_query_max']) ? trim($_REQUEST['search_by_query_max']) : null;

            if ($_REQUEST['search_by_select'] == 'profit' && !\CRUDBooster::isSupplier()) {
                $query->whereRaw("ROUND(((vk_price - ek_price) / ek_price * 100), 2) BETWEEN $search_by_query_min AND $search_by_query_max");
            } else if ($_REQUEST['search_by_select'] == 'stock'){
                $search_by_query_min = (int)$search_by_query_min;
                $search_by_query_max = (int)$search_by_query_max;
                $searchColumn = $searchCategoryId == 2 ? 'internel_stock' : 'stock';
                if($searchCategory == 'shipping_method'){
                    $query->whereBetween($searchColumn, [$search_by_query_min, $search_by_query_max]);
                } else {
                    $query->where(function ($query) use ($search_by_query_min, $search_by_query_max) {
                        $query->where('shipping_method', 1)
                              ->whereBetween('stock', [$search_by_query_min, $search_by_query_max])
                              ->orWhere(function ($query) use ($search_by_query_min, $search_by_query_max) {
                                  $query->where('shipping_method', 2)
                                        ->whereBetween('internel_stock', [$search_by_query_min, $search_by_query_max]);
                              });
                    });
                }
            } else {
                $query->whereBetween($_REQUEST['search_by_select'], [$search_by_query_min, $search_by_query_max]);
            }
        } else if(!blank($_REQUEST['search_by_select']) && (!blank($_REQUEST['search_by_query_min']) || !blank($_REQUEST['search_by_query_max']))){
            $search_by_query_min = is_numeric($_REQUEST['search_by_query_min']) ? trim($_REQUEST['search_by_query_min']) : null;
            $search_by_query_max = is_numeric($_REQUEST['search_by_query_max']) ? trim($_REQUEST['search_by_query_max']) : null;

            if ($_REQUEST['search_by_select'] == 'profit' && !\CRUDBooster::isSupplier()) {
                $operator = $search_by_query_min ? '>=' : '<=';
                $searchValue = $search_by_query_min ?: $search_by_query_max;
                $query->whereRaw("round(((vk_price - ek_price) / ek_price * 100), 2) $operator $searchValue");
                
            } else if ($_REQUEST['search_by_select'] == 'stock') {
                $searchValue = $search_by_query_min ?: $search_by_query_max;
                if ($searchCategory == 'shipping_method') {
                    $searchColumn = $searchCategoryId == 2 ? 'internel_stock' : 'stock';
                    $query->where($searchColumn, (int)$searchValue);
                } else {   
                    $query->where(function ($query) use ($searchValue) {
                        $query->where('shipping_method', 1)
                              ->where('stock', (int)$searchValue)
                              ->orWhere(function ($query) use ($searchValue) {
                                  $query->where('shipping_method', 2)
                                        ->where('internel_stock', (int)$searchValue);
                              });
                    });
                          
                }
            } else {
                $condition = $search_by_query_min ? ['>=', $search_by_query_min] : ['<=', $search_by_query_max];
                $query->where($_REQUEST['search_by_select'], $condition[0], $condition[1]);                
            }
        }
        //        Sorting by column
        if (Request::get('filter_column')) {
            app(ProductService::class)->buildQueryByFilterColumnSorting($query, Request::get('filter_column'));
        }

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    protected $internalValue = null;
    protected $current_id = null;
    protected $ek_price = 0;
    protected $vk_price = 0;
    protected $product = null;

    public function stockUpdate($product = null)
    {
        $message = null;
        $deleted_message = null;
        if (!empty($product)) {
            $updated_time = null;
            $next_update_time = null;
            // $api_credential = ApiCredential::where('api_id',1)->first();
            // $hour = $product->api_id == 1 ?'+ 20 hours':'+ 18 hours';
            // $next_update_time = Carbon::createFromTimestamp(strtotime($api_credential->token_next_update_time . $hour))->diffForHumans();

            if ($product->api_id) {

                $mp_api_update_schedule = DB::connection('marketplace')->table('marketplace_daily_api_update_schedule')->where('api_id', $product->api_id)->first();

                if ($mp_api_update_schedule) {



                    $next_update = Carbon::now()->diffInMinutes(Carbon::parse($mp_api_update_schedule->next_update_time));


                    $next_update = Carbon::now()->addMinutes($next_update)->diffForHumans();



                    if ($next_update == null) {
                        $next_update_time = __('marketplace.stock_sync_processing');
                    } else {
                        // $next_update_time = 'After ' . $next_update . ' minutes';
                        $next_update_time = $next_update;
                    }
                }
            } else {
                $next_update_time = __('marketplace.one_hour_from_now');
            }

            $updated_time = $product->stock_updated_at;
            // For Shipping method DROPSHIPPING

            if ($product->shipping_method == ShippingMethod::DROPSHIPPING) {

                if($product->stock == 0){
                    $selected_updating_time = Carbon::parse($updated_time ?? $product->created_at);
                    $deleted_at = $selected_updating_time->addDays(60);
                    $today = Carbon::today();
                    $countdown_date = $today->diffInDays($deleted_at);

                    $deleted_message = "</br>"
                        . "<h4>" . __('marketplace.deleted_at') . ": " . __('marketplace.after') . " " . $countdown_date . " " . __('marketplace.days')."</h4>"
                        . "<h4>" . $deleted_at . "</h4>"
                        . "<span style='font-weight: 700'>" . __('marketplace.product_auto_delete_text');
                }

                if ($product->old_stock != null) {
                    $diff = $product->stock - $product->old_stock;
                    // $result = $product->stock . " - " . $product->old_stock . " = " . $diff;
                    $result = "<h4>" . __('marketplace.previous_stock') . ": " . $product->old_stock . "</h4>"
                        . "<h4>" . __('marketplace.new_stock') . ": " . $product->stock . "</h4>"
                        . "<h4>" . __('marketplace.last_update') . ": " . $updated_time . "</h4>"
                        . "<span style='font-weight: 700'>" . __('marketplace.next_update') . ": " . $next_update_time;

                    if ($diff > 0) {
                        $title = __('marketplace.stock_increased');
                        $message = '<span style="color:green;font-weight:bold;cursor:pointer" onclick="swal({html: true,title:`' . $title . '`,text:`' . $result . $deleted_message . '`})" class="glyphicon glyphicon-triangle-top"></span>';
                    } elseif ($diff < 0) {
                        $title = __('marketplace.stock_decreased');
                        $message = '<span style="color:red;font-weight:bold;cursor:pointer" onclick="swal({html: true,title:`' . $title . '`,text:`' . $result . $deleted_message .'`})" class="glyphicon glyphicon-triangle-bottom"></span>';
                    } else {
                        $title = __('drm_import.stocks_not_updated');
                        $message = '<i style="color:#6cbfd5;cursor:pointer" onclick="swal({html: true,title:`' . $title . '`,text:`'. $deleted_message .'`})" class="fa fa-info-circle" aria-hidden="true"></i>';
                    }
                } else {
                    $title = __('drm_import.stocks_not_updated');
                    $message = '<i style="color:#6cbfd5;cursor:pointer" onclick="swal({html: true,title:`' . $title . '`,text:`'. $deleted_message .'`})" class="fa fa-info-circle" aria-hidden="true"></i>';
                }
            }

            // For Shipping method FULFILLment

            if ($product->shipping_method == ShippingMethod::FULFILLment) {


                // if($product->internel_stock == 0){
                //     $selected_updating_time = Carbon::parse($updated_time ?? $product->created_at);
                //     $deleted_at = $selected_updating_time->addDays(60);
                //     $today = Carbon::today();
                //     $countdown_date = $today->diffInDays($deleted_at);

                //     $deleted_message = "</br>"
                //         . "<h4>" . __('marketplace.deleted_at') . ": " . __('marketplace.after') . " " . $countdown_date . " " . __('marketplace.days')."</h4>"
                //         . "<h4>" . $deleted_at . "</h4>"
                //         . "<span style='font-weight: 700'>" . __('marketplace.product_auto_delete_text');
                // }

                if ($product->old_internel_stock != null) {
                    $diff = $product->internel_stock - $product->old_internel_stock;
                    // $result = $product->internel_stock . " - " . $product->old_internel_stock . " = " . $diff;
                    $result = "<h4>" . __('marketplace.previous_stock') . ": " . $product->old_internel_stock . "</h4>"
                        . "<h4>" . __('marketplace.new_stock') . ": " . $product->internel_stock . "</h4>"
                        . "<h4>" . __('marketplace.last_update') . ": " . $product->internel_stock_updated_at . "</h4>";

                    // $result =  __('marketplace.previous_stock')." : " . $product->old_internel_stock .

                    //     __('marketplace.new_stock') . ": " . $product->internel_stock .

                    //     __('marketplace.last_update') . ": " . $product->internel_stock_updated_at;

                    if ($diff > 0) {
                        $title = __('marketplace.stock_increased');
                        $message = '<span style="color:green;font-weight:bold;cursor:pointer" onclick="swal({html: true,title:`' . $title . '`,text:`' . $result . $deleted_message . '`})" class="glyphicon glyphicon-triangle-top"></span>';
                    } elseif ($diff < 0) {
                        $title = __('marketplace.stock_decreased');
                        $message = '<span style="color:red;font-weight:bold;cursor:pointer" onclick="swal({html: true,title:`' . $title . '`,text:`' . $result . $deleted_message .'`})" class="glyphicon glyphicon-triangle-bottom"></span>';
                    } else {
                        $title = __('drm_import.stocks_not_updated');
                        $message = '<i style="color:#6cbfd5;cursor:pointer" onclick="swal({html: true,title:`' . $title . '`,text:`'. $deleted_message .'`})" class="fa fa-info-circle" aria-hidden="true"></i>';
                    }
                } else {
                    $title = __('drm_import.stocks_not_updated');
                    $message = '<i style="color:#6cbfd5;cursor:pointer" onclick="swal({html: true,title:`' . $title . '`,text:`'. $deleted_message .'`})" class="fa fa-info-circle" aria-hidden="true"></i>';
                }
            }
        } else {
            $message = '<i style="color:#6cbfd5;cursor:pointer" onclick="swal(`' . __('drm_import.stocks_not_updated') . '`)" class="fa fa-info-circle" aria-hidden="true"></i>';
        }

        return $message;
    }
    public function hook_row_index($column_index, &$column_value)
    {
        
        if ($column_index == 1) {
            $this->current_id = $column_value;

            $this->product = Product::with('stockSendLog')->find($this->current_id);
            if($this->product->vk_price <= 0 && $this->product->status == 1) {
                $this->product->update(['status' => ProductStatus::PENDING]);
            }

            $adminPermission = ($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport());
            if($adminPermission){
                $status_column_value  = $this->product->status;
                if( $status_column_value == 0 && $this->product->is_dublicate == 1 && $this->product->best_price == 0) $bg = 'btn-warning';
                else if ($status_column_value == 0 ) $bg = 'btn-warning';
                else if ( in_array($status_column_value,[ProductStatus::ACTIVE,ProductStatus::WAITING_SYNC])) $bg = 'btn-success';
                else if (  $this->product->vk_price <= 0 || in_array($status_column_value, [ProductStatus::INACTIVE, ProductStatus::BLOCKED, ProductStatus::QUALITY_DEFECT])) $bg = 'btn-danger';
                // else if ( $status_column_value == 2 ) $bg = 'btn-danger';
                // else if ( $status_column_value == 4 ) $bg = 'btn-danger';
                // if ( $adminPermission && $this->product->vk_price <= 0 ) $bg = 'btn-danger';

                if ( $status_column_value == 0 && $this->product->is_dublicate == 1 && $this->product->best_price == 0) $currentStatus = 'Duplicate Ean';
                else if ( $status_column_value == 0 ) $currentStatus = __('marketplace.pending');
                else if ( $status_column_value == 1 && $this->product->shipping_method  == 1 ) $currentStatus = __('marketplace.approved');
                else if ( in_array($status_column_value,[ProductStatus::ACTIVE,ProductStatus::WAITING_SYNC]) && $this->product->shipping_method  == 2 ) $currentStatus = __('marketplace.approved');

                else if ( $status_column_value == 2 ) $currentStatus = __('marketplace.rejected');
                else if ( $status_column_value == 4 ) $currentStatus = __('marketplace.blocked');
                else if ( $status_column_value == 7 ) $currentStatus = __('marketplace.defect');
                if ( $adminPermission && $this->product->vk_price <= 0 ) $currentStatus = __('marketplace.incomplete').' '.'<i class="fa fa-info"></i>';

                $classes = ($adminPermission && $this->product->vk_price != 0) ? 'btn btn-xs change-status-btn '.$bg : 'btn btn-xs inactive '.$bg;
                $link = ($adminPermission && $this->product->vk_price != 0) ? "javascript:changeStatusModal($this->current_id, $status_column_value)" : "javascript:;";
                $is_admin = $adminPermission ? true : false;

                $status_column_value = '<a href="'.$link.'"style="width: 100%" id="'.$this->current_id.'" status="'.$status_column_value.'" class="'.$classes.'" data-status="'.$is_admin.'">'.$currentStatus.'</a>';

                $column_value = '<img class="waiting-loader-'.$this->current_id.'" style="width:20px;height:20px;" src="'.($adminPermission ? asset('Marketing_assets/img/25.gif') : '#').'"/><div class="mp-product-duplicate duplicate-product-'.$this->current_id.'" data-value='.$this->current_id.' data-ean='.$this->product->ean.'></div>'.$this->current_id.' '.$status_column_value;
            }else{
                    $status_column_value  = $this->product->status;
                    if (  $status_column_value == 0 ) $bg = 'btn-warning';
                    else if ( in_array( $status_column_value,[ProductStatus::ACTIVE,ProductStatus::WAITING_SYNC])) $bg = 'btn-success';
                    else if (  in_array($status_column_value, [ProductStatus::INACTIVE, ProductStatus::BLOCKED, ProductStatus::QUALITY_DEFECT])) $bg = 'btn-danger';
                    // if (  $status_column_value == 4 ) $bg = 'btn-danger';
                    // if ( $column_value == 5 ) $bg = 'btn-info';
                    // if ( $column_value == 6 ) $bg = 'btn-success';

                    if (  $status_column_value == 0 ) $currentStatus = __('marketplace.pending');
                    else if (  $status_column_value == 1 && $this->product->shipping_method  == ShippingMethod::DROPSHIPPING ) $currentStatus = __('marketplace.approved');
                    else if ( in_array( $status_column_value,[ProductStatus::ACTIVE,ProductStatus::WAITING_SYNC]) && $this->product->shipping_method  == ShippingMethod::FULFILLment ) $currentStatus = __('marketplace.approved');
                    else if (  $status_column_value == 2 ) $currentStatus = __('marketplace.rejected');
                    else if (  $status_column_value == 4 ) $currentStatus = __('marketplace.blocked');
                    else if (  $status_column_value == 7 ) $currentStatus = __('marketplace.defect');
                    // if ( $column_value == ProductStatus::WAITING_SYNC && $this->product->internel_sync_status == ProductStatus::WAITING_SYNC ) $currentStatus = 'Waiting Sync';
                    // if ( $column_value == ProductStatus::ACTIVE && $this->product->internel_sync_status == ProductStatus::GOODS_RECEIVED) $currentStatus = 'Goods Received';

                    $classes = ($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) ? 'btn btn-xs change-status-btn '.$bg : 'btn btn-xs inactive '.$bg;
                    $link = ($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) ? "javascript:changeStatusModal($this->current_id,  $status_column_value)" : "javascript:;";


                    $column_value = $this->current_id.' '.'<a href="'.$link.'"style="width: 100%" id="'.$this->current_id.'" status="'. $status_column_value.'" class="'.$classes.'">'.$currentStatus.'</a>';

            }

        }
        // Dublicate Ean Color condition
        // if( $column_index == 3){
        //     $ean =  $column_value ?? '';
        //     $dublicate = Product::where('ean',$ean)->get();
        //     if(count( $dublicate ?? []) > 1){
        //         $column_value = '<span class="m-0 p-0" style="background-color:yellow;">'.$column_value.'</span>';
        //     }
        // }

        if ($column_index == 2) {
            $icon = $this->product->is_image_process == 1
                ? '<i class="fa fa-check-square-o" aria-hidden="true" title="Image sync" style="position: absolute;right: -7px;top: 1px;color: green;cursor: pointer;"></i>'
                : '<i class="fa fa-refresh" aria-hidden="true" title="Image not sync" style="position: absolute;right: -7px;top: 1px;color: #fd6500; cursor: pointer;"></i>';
            $column_value = '<div style="position: relative; width: 46px;">' . $column_value . '' . $icon . '</div>';
        }


        if ($column_index == 5) {
            $this->ek_price = number_format((float)$column_value,2,".","");
            if (\CRUDBooster::isSupplier()){
                $column_value = "<span class='market_p_editable' data-name='ek_price' data-pk='{$this->product->id}'>{$this->ek_price}</span><span>{$this->productService->CheckSupplierDiscountAmount($this->product)}</span></span><br/>{$this->productService->ekPriceStockInfo($this->product)}";
            } else {
                if($this->product->api_id == 4){
                    $column_value = "<span class='market_p_editable' data-name='ek_price' data-pk='{$this->product->id}'>{$this->ek_price}</span><br/>{$this->productService->newEkPriceStockInfo($this->product)}";
                } else {
                    $column_value = "<span class='market_p_editable' data-name='ek_price' data-pk='{$this->product->id}'>{$this->ek_price}</span><br/>{$this->productService->ekPriceStockInfo($this->product)}";
                }
            }
        }

        if ($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {

            if ($column_index == 7) {
                $this->vk_price = number_format((float)$column_value,2,".","");
                if($this->product->api_id == 4){
                    $column_value = "<span class='market_p_editable' data-name='vk_price' data-pk='{$this->product->id}'>{$this->vk_price}</span><span>{$this->productService->CheckSupplierDiscountAmount($this->product)}</span><br/>{$this->productService->newVkPriceStockInfo($this->product)}";
                }else{
                    $column_value = "<span class='market_p_editable' data-name='vk_price' data-pk='{$this->product->id}'>{$this->vk_price}</span><span>{$this->productService->CheckSupplierDiscountAmount($this->product)}</span><br/>{$this->productService->vkPriceStockInfo($this->product)}";
                }
            }

            if ($column_index == 9) {
                $this->uvp = number_format((float)$column_value,2);
                $column_value = "<span class='market_p_editable' data-name='uvp' data-pk='{$this->product->id}'>{$this->uvp}</span><br/>{$this->productService->uvpInfo($this->product)}";
            }

            //Profit
            if ($column_index == 11) {
                try {
                    if ($this->vk_price > 0) {
                        $column_value = round(($this->vk_price - $this->ek_price) / $this->ek_price * 100, 2);
                    } else {
                        $column_value = 0;
                    }
                } catch (\Throwable $th) {
                    $column_value = 0;
                }

                $this->ek_price = 0;
                $this->vk_price = 0;
            }

            if ($column_index == 12) {

                if (!empty($this->product->api_category_id) && $this->product->api_id) {
                    $api_cat_name = str_replace(' ', '', preg_replace('/[^A-Za-z0-9\-\(\) ]/', '', $column_value));
                    $column_value = '<p class="apiids  apid-' . $this->product->api_id . '-' . $api_cat_name . '" data-value="' . $column_value . '=>' . $this->product->api_id . '" > <i class="fa fa-spinner  fa-spin"></i></p>';
                } else {
                    $column_value = "No Category";
                }
            }

            if ($column_index == 13) {

                if(is_relavida() || in_array($this->product->supplier_id, [2817])){
                    $mainCategory = ChannelUserCategory::select('category_name', 'parent')->find($this->product->category_id);
                    $category_name = $mainCategory->category_name ?? null;
                    $parent_id = $mainCategory->parent ?? null;
                } else {
                    $mainCategory = Category::select('name', 'parent_id')->find($this->product->category_id);
                    $category_name = $mainCategory->name ?? null;
                    $parent_id = $mainCategory->parent_id ?? null;
                }
                $column_value = str_replace('"', '', $column_value,);
                $column_value = str_replace('[', '', $column_value,);
                $column_value = str_replace(']', '', $column_value,);
                $column_value = str_replace(',', ' > ', $column_value,);

                $params = $this->current_id . ',' . $this->product->category_id . ',' . $parent_id . ',' . '`' . $column_value . '`' . ',' . $this->product->status;
                $column_value = '<span class="m-0 p-0">' . $category_name . '</span>' . '<br /><i class="fa fa-edit" onclick="javascript:changeCategoryModal(' . $params . ')" style="cursor:pointer"></i>';

            }

            // if ($column_index == 10) {
            //     $images = json_decode($column_value);
            //     $column_value = '<img width="40" height="40" src="' . Arr::get($images, 0) . '" />';
            // }

            // if ($column_index == 11) {
            //     $icon = $this->product->is_image_process == 1
            //         ? '<i class="fa fa-check-square-o" aria-hidden="true" title="Image sync" style="position: absolute;right: -7px;top: 1px;color: green;cursor: pointer;"></i>'
            //         : '<i class="fa fa-refresh" aria-hidden="true" title="Image not sync" style="position: absolute;right: -7px;top: 1px;color: #fd6500; cursor: pointer;"></i>';
            //     $column_value = '<div style="position: relative; width: 46px;">' . $column_value . '' . $icon . '</div>';
            // }

            if ($column_index == 14)  {


                if($this->product->shipping_method == ShippingMethod::DROPSHIPPING) {
                    if($this->product->stock > 2 && $this->product->api_id == 2 && (\CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport())){
                        $stock = $this->product->stock + ceil(($this->product->stock * 10)/100);
                        $column_value = $this->product->stock .'/'. $stock;
                    }else{
                        $column_value = $this->product->stock;
                    }

                    // $column_value = $this->product->stock;
                    $column_value = "<span class='market_p_editable' data-name='stock' data-pk='{$this->product->id}'>{$column_value}</span><br/>{$this->stockUpdate($this->product)}";

                } elseif ( $this->product->shipping_method == ShippingMethod::FULFILLment ) {
                    if ( $this->product->internel_stock ) {
                        $column_value = $this->product->internel_stock;
                    } else {
                        $column_value = "<span class='text-danger'>0</span>";
                    }

                    $column_value = "<span style='display:flex;gap:3px;'>{$column_value}</span><br/>{$this->stockUpdate($this->product)}";
                }

            }


        //     if ($column_index == 15)  {
        //         $class = (
        //             ($this->product->shipping_method == ShippingMethod::FULFILLment && $this->product->internel_sync_status == ProductStatus::WAITING_SYNC) ||
        //             $this->product->status != ProductStatus::ACTIVE ||
        //             $this->product->atw != 0
        //         ) ? '' : 'market_p_editable';
        //         if($this->product->shipping_method == ShippingMethod::FULFILLment) {
        //             $column_value = $this->product->stock;
        //             $column_value = "<span class ='{$class}' data-name='stock' data-pk='{$this->product->id}'>{$column_value}</span><br/>";
        //         }
        //         else{
        //             $column_value = '0';
        //         }

        //     }
        //     if($column_index == 16){
        //         if($this->product->shipping_method == ShippingMethod::FULFILLment && $this->product->internel_delivery_id != null){
        //             $column_value = $this->product->ta;
        //         }
        //         else{
        //            $column_value = 0;
        //         }
        //    }
           if($column_index == 15){
                if($this->product->shipping_method == ShippingMethod::FULFILLment) {
                    if($this->product->stockSendLog){
                        if($this->product->stockSendLog->is_left != null){
                            $last_transmission = '<span class="text-danger">(-'.$this->product->stockSendLog->is_left.')</span>';
                            $column_value = ($this->product->atw ?? 0).''.$last_transmission;
                        }
                        else if($this->product->stockSendLog->recived_stock>0 && $this->product->stockSendLog->is_left == null){
                            $column_value = '<span class="text-success">' . $this->product->atw . '</span>';
                        }
                        else{
                            $column_value = $this->product->atw;
                        }
                    }
                    else{
                        $column_value = $this->product->atw;
                    }


                    }else{
                    $column_value = "0";
                }
           }
            if($column_index == 16){
                $column_value = $this->product->defect_stock;
            }
            if ($column_index == 17) {
                $fulfilment_column_value  = $this->product->status;
                if(
                    $this->product->shipping_method  == ShippingMethod::FULFILLment
                    && in_array($this->product->internel_sync_status,[0,ProductStatus::WAITING_SYNC,ProductStatus::GOODS_RECEIVED])
                    && !in_array($fulfilment_column_value, [ProductStatus::PENDING, ProductStatus::INACTIVE,ProductStatus::BLOCKED])
                ){
                    if ( $fulfilment_column_value == 0 ) $bg = 'btn-warning';
                    //else if ( $fulfilment_column_value == 1 && ($this->product->stock > 0 && $this->product->internel_stock == 0 || $this->product->stock > 0 && $this->product->internel_stock > 0)) $bg = 'btn-success';
                    else if ( $fulfilment_column_value == 2 ) $bg = 'btn-danger';
                    else if ( $fulfilment_column_value == 4 ) $bg = 'btn-danger';
                    else if ( $fulfilment_column_value == 5 ) $bg = 'btn-info';
                    else if ( $fulfilment_column_value == 1 && $this->product->internel_sync_status == ProductStatus::GOODS_RECEIVED) $bg = 'btn-success';
                    //if ( $fulfilment_column_value == ProductStatus::ACTIVE && ($this->product->stock > 0 && $this->product->internel_stock == 0 || $this->product->stock > 0 && $this->product->internel_stock > 0) && $this->product->internel_sync_status == 0 ) $currentStatus = __('marketplace.confirm_shipment');
                    if ( $fulfilment_column_value == ProductStatus::WAITING_SYNC && $this->product->internel_sync_status == ProductStatus::WAITING_SYNC ) $currentStatus = __('marketplace.waiting_sync');
                    if ( $fulfilment_column_value == ProductStatus::ACTIVE && $this->product->internel_sync_status == ProductStatus::GOODS_RECEIVED) $currentStatus = __('marketplace.goods_received');
                    if ( $fulfilment_column_value == ProductStatus::ACTIVE && $this->product->internel_sync_status == ProductStatus::GOODS_RECEIVED && $this->product->stockSendLog->is_left != null) $currentStatus = __('marketplace.waiting_for_goods_received');

                    $classes = (($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) ) ? 'btn btn-xs change-status-btn '.$bg : 'btn btn-xs inactive '.$bg;
                    $link = $this->product->internel_sync_status != 6 ? "javascript:;" : "javascript:;";
                    $fulfilment_column_value = '<a href="'.$link.'"style="width: 100%" id="'.$this->current_id.'" status="'.$fulfilment_column_value.'" class="'.$classes.'">'.$currentStatus.'</a>';
                }else{
                    $fulfilment_column_value = '';
                }

                if ($this->product->shipping_method== ShippingMethod::FULFILLment) {
                    if ($this->product->internel_delivery_id){
                        $column_value = "<b class='text-success'>".$this->product->id."-Fulfillment</b>".' '.$fulfilment_column_value;
                    } else {
                        $column_value = "<b class='text-danger'>".$this->product->id."-Fulfillment</b>".' '.$fulfilment_column_value;
                    }
                } elseif ($this->product->shipping_method== ShippingMethod::DROPSHIPPING) {
                    $column_value = "DropShipping";
                }
            }

            if($column_index == 18){
                $supplier_id = $this->product->supplier_id ?? $this->product->delivery_company_id;
                $column_value = '<a href="javascript:supplierContractInformation('.$supplier_id.')">'.$column_value.'</a>';
            }



        } else {
            if ($column_index == 8) {

                if(is_relavida() || in_array(CRUDBooster::myParentId(), [2817])){
                    $mainCategory = ChannelUserCategory::select('category_name', 'parent')->find($this->product->category_id);
                    $category_name = $mainCategory->category_name ?? null;
                    $parent_id = $mainCategory->parent ?? null;
                } else {
                    $mainCategory = Category::select('name', 'parent_id')->find($this->product->category_id);
                    $category_name = $mainCategory->name ?? null;
                    $parent_id = $mainCategory->parent_id ?? null;
                }
                $column_value = str_replace('"', '', $column_value,);
                $column_value = str_replace('[', '', $column_value,);
                $column_value = str_replace(']', '', $column_value,);
                $column_value = str_replace(',', ' > ', $column_value,);

                $params = $this->current_id . ',' . $this->product->category_id . ',' . $parent_id . ',' . '`' . $column_value . '`' . ',' . $this->product->status;
                $column_value = '<span class="m-0 p-0">' . $category_name . ' </span>' . '<br /><i class="fa fa-edit" onclick="javascript:changeCategoryModal(' . $params . ')" style="cursor:pointer"></i>';
            }

            // if ($column_index == 8) {
            //     $images = json_decode($column_value);
            //     $column_value = '<img width="40" height="40" src="' . Arr::get($images, 0) . '" />';
            // }
            if ($column_index == 9)  {
                if($this->product->shipping_method == ShippingMethod::DROPSHIPPING) {

                    $column_value = $this->product->stock;

                    $column_value = "<span class='market_p_editable stock' data-name='stock' data-pk='{$this->product->id}'>{$column_value}</span><br/>{$this->stockUpdate($this->product)}";

                } elseif ( $this->product->shipping_method == ShippingMethod::FULFILLment ) {
                    if ( $this->product->internel_stock ) {
                        $column_value = $this->product->internel_stock;
                    } else {
                        $column_value = "<span class='text-danger'>0</span>";
                    }
                    $column_value = "<span style='display:flex;gap:3px;' class='stock'>{$column_value}</span><br/>{$this->stockUpdate($this->product)}";
                }

            }
        //     if ($column_index == 10)  {
        //         $class = (
        //             ($this->product->shipping_method == ShippingMethod::FULFILLment && $this->product->internel_sync_status == ProductStatus::WAITING_SYNC) ||
        //             $this->product->status != ProductStatus::ACTIVE ||
        //             $this->product->atw != 0
        //         ) ? '' : 'market_p_editable';
        //         if($this->product->shipping_method == ShippingMethod::FULFILLment) {
        //             $column_value = $this->product->stock;
        //             $column_value = "<span class ='{$class}' data-name='stock' data-pk='{$this->product->id}'>{$column_value}</span><br/>";
        //         }
        //         else{
        //             $column_value = '0';
        //         }

        //     }
        //     if($column_index == 11){
        //         if($this->product->shipping_method == ShippingMethod::FULFILLment && $this->product->internel_delivery_id != null ){
        //             $column_value = $this->product->ta;
        //         }
        //         else{
        //            $column_value = 0;
        //         }
        //    }
           if($column_index == 10){
                if($this->product->shipping_method == ShippingMethod::FULFILLment) {
                    if($this->product->stockSendLog){
                        if($this->product->stockSendLog->is_left != null){
                            $last_transmission = '<span class="text-danger">(-'.$this->product->stockSendLog->is_left.')</span>';
                            $column_value = ($this->product->atw ?? 0).''.$last_transmission;
                        }
                        else if($this->product->stockSendLog->recived_stock>0 && $this->product->stockSendLog->is_left == null){
                            $column_value = '<span class="text-success">' . $this->product->atw . '</span>';
                        }
                        else{
                            $column_value = $this->product->atw;
                        }
                    }
                    else{
                        $column_value = $this->product->atw;
                    }


                    }else{
                    $column_value = "0";
                }
           }
            if($column_index == 11){
                $column_value = $this->product->defect_stock;
            }

            if ($column_index == 12) {
                $fulfilment_column_value  = $this->product->status;
                if(
                    $this->product->shipping_method  == ShippingMethod::FULFILLment
                    && in_array($this->product->internel_sync_status,[0,ProductStatus::WAITING_SYNC,ProductStatus::GOODS_RECEIVED])
                    && !in_array($fulfilment_column_value, [ProductStatus::PENDING, ProductStatus::INACTIVE,ProductStatus::BLOCKED])
                ){
                    if ( $fulfilment_column_value == 0 ) $bg = 'btn-warning';
                    //else if ($fulfilment_column_value == 1 && ($this->product->stock > 0 && $this->product->internel_stock == 0 || $this->product->stock > 0 && $this->product->internel_stock > 0)) $bg = 'btn-success';
                    else if ( $fulfilment_column_value == 2 ) $bg = 'btn-danger';
                    else if ( $fulfilment_column_value == 4 ) $bg = 'btn-danger';
                    else if ($fulfilment_column_value == 5 ) $bg = 'btn-info';
                    else if ($fulfilment_column_value == 1 && $this->product->internel_sync_status == ProductStatus::GOODS_RECEIVED) $bg = 'btn-success';
                    //if ( $fulfilment_column_value == ProductStatus::ACTIVE && ($this->product->stock > 0 && $this->product->internel_stock == 0 || $this->product->stock > 0 && $this->product->internel_stock > 0) && $this->product->internel_sync_status == 0 ) $currentStatus = __('marketplace.confirm_shipment');
                    if ( $fulfilment_column_value == ProductStatus::WAITING_SYNC && $this->product->internel_sync_status == ProductStatus::WAITING_SYNC ) $currentStatus = __('marketplace.waiting_sync');
                    if ( $fulfilment_column_value == ProductStatus::ACTIVE && $this->product->internel_sync_status == ProductStatus::GOODS_RECEIVED) $currentStatus = __('marketplace.goods_received');
                    if ( $fulfilment_column_value == ProductStatus::ACTIVE && $this->product->internel_sync_status == ProductStatus::GOODS_RECEIVED && $this->product->stockSendLog->is_left != null) $currentStatus = __('marketplace.waiting_for_goods_received');

                    $classes = (($this->isSuperAdmin || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) ) ? 'btn btn-xs change-status-btn '.$bg : 'btn btn-xs inactive '.$bg;
                    $link = $this->product->internel_sync_status != 6 ? "javascript:;" : "javascript:;";
                    $fulfilment_column_value = '<a href="'.$link.'"style="width: 100%" id="'.$this->current_id.'" status="'.$fulfilment_column_value.'" class="'.$classes.'">'.$currentStatus.'</a>';
                }else{
                    $fulfilment_column_value = '';
                }

                if ($this->product->shipping_method==2) {
                    if ($this->product->internel_delivery_id){
                        $column_value = "<b class='text-success shipping' data-shipping-id='{$this->product->shipping_method}'>".$this->product->id."-Fulfillment</b><br>".' '.$fulfilment_column_value;
                    } else {
                        $column_value = "<b class='text-danger shipping' data-shipping-id='{$this->product->shipping_method}'>".$this->product->id."-Fulfillment</b><br>".' '.$fulfilment_column_value;
                    }
                } elseif ($this->product->shipping_method==1) {
                    $column_value = "<b class='text-danger shipping' data-shipping-id='0'>DropShipping</b>";
                }
            }

            if ($column_index == 13)  {
                if($column_value!=''){
                    $column_value = "<span class='market_p_editable alarm' data-name='alarm_quantity' data-pk='{$this->product->id}'>{$column_value}</span><br/>";
                }else{
                    $column_value = "<span class='market_p_editable alarm' data-name='alarm_quantity' data-pk='{$this->product->id}'>-</span><br/>";
                }
            }

            if($column_index == 14 && $this->col[13]['name'] == 'internel_send_date'){

                if ( $this->product->shipping_method == ShippingMethod::FULFILLment ) {

                    if ( $this->product->internel_send_date !=null && $this->product->internel_stock > 0) {
                      $column_value = "<span class='mp-product-FWDL product-FWDL-" .$this->product->id. "' id='product-FWDL-" .$this->product->id. "' data-value='".$this->product->internel_send_date."' data-id='".$this->product->id."'></span>";
                    }else{
                        $column_value = "<span>----</span>";
                    }

                    $column_value = "<div style='display:flex;gap:8px;width:109px;'><span>{$column_value}</span>{$this->productService->fulfilmentWarehouseExpirationInfo($this->product)}</div>";
                }else{
                    $column_value = "<span>----</span>";
                }
            }
            
            // if ( $column_index == 12) {// for task Delate "Top Angebote"
            //     $column_value = ($column_value == 1) ? "checked='checked'" : '';
            //     $product_status = ($this->product->status != 1) ? "disabled" : "";
            //     $daysleft = $this->daysDifference($this->product->top_product_datetime);
            //     $daysleft = ($this->product->top_product_datetime != null && $this->product->is_top_product == 1) ? "<br/><span class='text-small text-bold' id='toppro_day_".$this->current_id."'>{$daysleft} Left</span>" : "<span class='text-bold' id='toppro_day_".$this->current_id."'></span>";
            //     $column_value = ($this->product->status != 1 || $this->product->vk_price <= 0 ) ? '<b>N/A</b>' : '<div id="topproduct_'.$this->current_id.'" data-id="'.$this->current_id.'" data-status="'.$this->product->is_top_product.'" class="onoffswitch product-onoffswitch-checkbox"> <input type="checkbox" name="onoffswitch" class="onoffswitch-checkbox" id="topproductonoff_'.$this->current_id.'" '.$column_value.' '.$product_status.'><label class="onoffswitch-label" for="topproductonoff_'.$this->current_id.'"><span class="onoffswitch-inner"></span><span class="onoffswitch-switch"></span></label></div>'.$daysleft;
            // }
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        // $images = [];
        // $images = array_merge($images, array($postdata['image']));
        // $postdata['image'] = json_encode($images);
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {

        //Your code here


    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        $item = Product::where('id', $id)->select('id', 'ean', 'delivery_company_id')->first();

        if ($item) {
            $products = Product::where('ean', $item->ean)->where('delivery_company_id', $item->delivery_company_id)
                ->with([
                    'MpCoreDrmTransferProduct:marketplace_product_id,drm_product_id,user_id',
                    'core_products:marketplace_product_id,id,user_id',
                    'additionalInfo:product_id,id'
                ])
                ->select('id', 'image')
                ->get();

            if($products->isNotEmpty()) {
                foreach ($products as $product) {
                    if(!blank($product->MpCoreDrmTransferProduct)){
                        foreach($product->MpCoreDrmTransferProduct as $MpCoreDrmTransferProduct){
                            app(\App\Services\DRMProductService::class)->destroy($MpCoreDrmTransferProduct->drm_product_id,$MpCoreDrmTransferProduct->user_id);
                        }
                    }else if(!blank($product->core_products)){
                        foreach($product->core_products as $core_product){
                            app(\App\Services\DRMProductService::class)->destroy($core_product->id,$core_product->user_id);
                        }
                    }

                    if (!empty($product->image)) {
                        foreach ($product->image as $image) {
                            $this->postDeleteSourceImage($image);
                        }
                    }
                    $product->additionalInfo ? $product->additionalInfo->delete() : '';
    
                    $product->delete();
                }
            }
        }
    }

    //Index and Detail Page Quick Editable Data Update
    public function postMarketplaceEditableSave()
    {
        try {
            $id = $_REQUEST['pk'];
            $name = $_REQUEST['name'];
            $value = $_REQUEST['value'];
            $product = Product::with('mainCategory')->find($id);

            if ($name == 'ek_price' && floatval($value) <= 0) {
                return response()->json([
                    'status' => 400,
                    'error'  => 'invalid_ek_price',
                ]);
            }

            if ($name == 'ean' && app(\App\Services\Marketplace\ProductService::class)->validateEAN($value) == false) {
                return response()->json([
                    'status' => 400,
                    'error'  => 'invalid_ean',
                ]);
            }

            // if ( $name == 'vk_price' && floatval($value) <= 0 ) {
            if ($name == 'vk_price' && removeCommaFromPrice($value) <= 0) {
                return response()->json([
                    'status' => 400,
                    'error'  => 'invalid_vk_price',
                ]);
            }

            //ek_price update range checke max up 10%, max down 70
            // if ($name == 'ek_price') {
            //     $updatedEkPrice = removeCommaFromPrice($value);
            //     $blockProduct = $this->productService->applyEkPriceViolationRule($product->id, $updatedEkPrice);

            //     if ($blockProduct) {
            //         if (\CRUDBooster::isSupplier()) {
            //             $supplier = User::find($product->supplier_id);
            //             $message_title = "Product Blocked: {$supplier->name} product has been blocked. <b>Supplier ID: {$supplier->id}</b>.<br/>To track the product plaese follow the below Link";
            //             $url = url('/admin/marketplace_products?q=' . $id);
            //             User::find(\App\Enums\Apps::DROPMATIX_ID)->notify(new DRMNotification($message_title, 'MP PRODUCT BLOCKED', $url));
            //         }
            //         $product->update(['status' => ProductStatus::BLOCKED, 'ek_price' => $updatedEkPrice]);
            //         return response()->json([
            //             'status' => 400,
            //             'error'  => 'product_blocked',
            //         ]);
            //     }
            // }


            if($name == 'internel_stock' && $product->shipping_method == ShippingMethod::FULFILLment){
                $product->update(['stock' => $value]);
            }

            if($name == 'stock'){
                $product->update([$name => $value]);
                $this->productService->stockUpdateToActiveBest($_REQUEST['pk'], '', $value);
            } else if ($name == 'ek_price' || $name == 'uvp' || $name == 'shipping_cost') {
                if (($name == 'ek_price' || $name == 'shipping_cost') && $product->is_dublicate) {
                    $this->productService->updateProductWithChipestPrice($_REQUEST['pk'], removeCommaFromPrice($value), $name);
                }
                $product->update([$name => removeCommaFromPrice($value)]);
            } else if ($name == "vk_price") {
                $product->update(['calculation_id' => null, 'vk_price' => removeCommaFromPrice($value)]);
            }else{
                $product->update([$name => $value]);
            }

            if ($name == "ek_price" || $name == "vk_price" || $name == "real_shipping_cost") {
                // $this->updateAssignCalc($id);
                $this->manualRealShippingCostUpdate($product,$name);
            }

            if ($name === "name" && $value == "") {
                $product->update(['status' => ProductStatus::QUALITY_DEFECT]);
            }

            if ($name == "stock" && $product->shipping_method == ShippingMethod::DROPSHIPPING && in_array($value, [0, 1, ''])) {
                $product->update(['status' => ProductStatus::QUALITY_DEFECT]);
            }

            $this->syncUpdateWithDrmProduct(Product::with('additionalInfo','productBrand')->find($id));

            return response()->json([
                'status' => 200,
                'id' => $id
            ]);
        } catch (\Exception $e) {

            return response()->json([
                'status' => 400,
            ]);
        }
    }

    public function getTopProduct($id)
    {
        try {
            $product = Product::find($id);
            Product::where('id', $id)->update(['is_top_product' => $product->is_top_product == PermissionStatus::YES ? PermissionStatus::NO : PermissionStatus::YES]);
            $event = $product->is_top_product == PermissionStatus::YES ? 'No' : 'Yes';
            return redirect()->back()->with(['_status' => 'success', '_msg' => 'Successfully ' . $event . 'Top Product']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to update!']);
        }
    }

    public function getVarientProduct()
    {
        $ean = Request::get('ean') ?? '';
        $product_id = Request::get('id');
        $country_id = Session::get('filter_country_' . CRUDBooster::myParentId()) ?? 1;

        $dublicate_product = Product::with('mainCategory:id,name' ,'supplier', 'deliveryCompanies')->where('ean', $ean)->where('country_id', $country_id)->orderBy('best_price', 'desc')->orderBy(DB::raw("`ek_price` + `real_shipping_cost`"), 'asc')->get();
        return response()->json([
            'data' => $dublicate_product
        ]);
    }

    //Index page

    public function getIndex()
    {
        $this->cbLoader();

        // Prevent Customer to see this page
        if (!(CRUDBooster::isSuperAdmin() || CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport() || CRUDBooster::isSupplier())) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        // Check access for sub supplier
        if (!$this->suppliersService->hasAccess('PRODUCT_OVERVIEW')) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }



        $data['action_add_data_title'] = 'Add Product';
        $data['action_show_data_title'] = 'Show Products';


        // $categories = Category::where('is_active', 1)
        // ->orderBy("name")
        // ->get()->toArray();




        $module = CRUDBooster::getCurrentModule();


        if (Request::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::connection('marketplace')->table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
            if (Request::get('foreign_key')) {
                $data['parent_field'] = Request::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($data['parent_field']) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $data['parent_field']) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        // $data['page_title'] = $module->name;
        $data['page_title'] = __("Marketplace Products");
        $data['page_description'] = trans('crudbooster.default_module_description');
        $data['date_candidate'] = $this->date_candidate;
        // $data['suppliers'] = User::where('id_cms_privileges', 4)->get();

        $limitSessionKey = 'mp_limit_for_' . \crocodicstudio\crudbooster\helpers\CRUDBooster::myParentId();

        if (Request::get('limit')) {
            $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;
            \Illuminate\Support\Facades\Session::put($limitSessionKey, $limit);
        } else if (\Illuminate\Support\Facades\Session::has($limitSessionKey)) {
            $data['limit'] = $limit = \Illuminate\Support\Facades\Session::get($limitSessionKey);
        } else {
            $data['limit'] = $limit = $this->limit;
        }

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::connection('marketplace')->table($this->table)->select(DB::connection('marketplace')->raw($this->table . "." . $this->primary_key));

        if (Request::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . Request::get('foreign_key'), Request::get('parent_id'));
        }

        // if($this->indexBuilder)
        // {
        //     $this->hook_query_index_cp($result);
        // } else {
        //     $this->hook_query_index($result);
        // }

        // $this->hook_query_index_cp($result);
        $this->hook_query_index($result);

        // if (in_array('deleted_at', $table_columns)) {
        //     $result->where($this->table . '.deleted_at', null);
        // }

        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;

        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::connection('marketplace')->raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::connection('marketplace')->raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {
                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if (Request::get('q')) {
            $result->where(function ($w) use ($columns_table, $request) {
                foreach ($columns_table as $col) {
                    if (!$col['field_with']) {
                        continue;
                    }
                    if ($col['is_subquery']) {
                        continue;
                    }
                    $w->orwhere($col['field_with'], "like", "%" . Request::get("q") . "%");
                }
            });
        }

        if (Request::get('where')) {
            foreach (Request::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }


        if($this->indexBuilder)
        {
            return $result;
        }

        // getting all_products for Bulk_action
        if ($_REQUEST['only_products'] == 1) {
            if ($_REQUEST['analysis'] == 1) {
                $all_products = $result->where('status', 1)->pluck('id')->toArray();
            } else {
                $all_products = $result->select('*')->get();
            }
            return $all_products;
        }

        $filter_is_orderby = false;


        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];

        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(Request::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (Request::get('page')) ? Request::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            if ($this->button_bulk_action) {
                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];


                if ($col['name'] == 'image') {

                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('../Marketing_assets/img/no-product-image.jpg') . "'><img width='40px' height='40px' src='" . asset('../Marketing_assets/img/no-product-image.jpg') . "' /></a>";
                    } else {
                        $value = "<a data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' class='roadtrip-product-id-" .$row->{$tablePK}. "' href='".asset('Marketing_assets/img/25.gif')."'><img class='mp-product-image product-image-" .$row->{$tablePK}. "' data-value='".$row->{$tablePK}."' style='width:20px;height:20px;' src='".asset('Marketing_assets/img/25.gif')."'/></a>";
                    }
                }

                if($col['name'] == 'total_no_transferred' && !empty($value)){
                    $value = '<span class="drm-total-transfer transferred_row_'.$row->{$tablePK}.'" data-product_id="'.$row->{$tablePK}.'"><img src="'.(asset('Marketing_assets/img/25.gif')).'" style="width:20px;height:20px;"/></span>';
                }

                if (@$col['download']) {
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action) :
                $button_action_style = $this->button_action_style;
                $button_delete = false;
                $html_content[] = "<div class='button_action mp-button-action' style='text-align:right'>" . view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field', 'button_delete'))->render() . "</div>";
            endif; //button_table_action

            foreach ($html_content as $i => $v) {
                $this->hook_row_index($i, $v);
                $html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];
        if(is_relavida() || in_array(CRUDBooster::myParentId(), [2817])){
            $parentCategory = ChannelUserCategory::where('user_id', 2694)->where('channel', 10)->where('parent',0)->orderBy('category_name')->get()->unique('category_name')->toArray();
        } else {
            $parentCategory = MarketplaceParentCategory::where('is_active', 1)->orderBy("name")->get()->toArray();
        }
        $data['suppliers'] = app(\App\Services\Marketplace\ProductService::class)->getDeliveryCompaniesForFilterInProducts();

        $data['html_contents'] = $html_contents;
        $data['parent_categories'] = $parent_categories;
        $data['categories'] = $categories;
        $data['parentCategory'] = $parentCategory;
        $data['all_channels'] = $this->channels = collect(config('channel.list'));
        $data['disable_channel'] = \App\Enums\Channel::MP_BLACKLIST;
        $data['countries'] = \App\Country::where(['is_active' => 1, 'status' => 1])->get();
        $data['selected_country'] = $data['countries']->where('id', Session::get('filter_country_' . CRUDBooster::myParentId()) ?? 1)->first();

        return view("marketplace.product.index", $data);
    }

    //Detail Page
    public function getDetail($id)
    {
        $this->cbLoader();
        $row = Product::with('stockSendComment','additionalInfo')->find($id);
        if (!CRUDBooster::isRead() && $this->global_privilege == false || $this->button_detail == false) {
            CRUDBooster::insertLog(trans("crudbooster.log_try_view", [
                'name' => $row->{$this->title_field},
                'module' => CRUDBooster::getCurrentModule()->name,
            ]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $module = CRUDBooster::getCurrentModule();
        $userId = CRUDBooster::myParentId();
        $page_menu = Route::getCurrentRoute()->getActionName();
        $page_title = trans("crudbooster.detail_data_page_title", ['module' => $module->name, 'name' => $row->{$this->title_field}]);
        $command = 'detail';
        Session::put('current_row_id', $id);
        // $previous = Product::where('id', '<', $id)->orderBy('id', 'desc')->whereNull('deleted_at')->first();
        // $next = Product::where('id', '>', $id)->orderBy('id', 'asc')->whereNull('deleted_at')->first();
        $categories = Category::all()->toArray();
        $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active', 1)->orderBy('name')->get();
        $productCategory = \App\Models\Marketplace\Category::find($row->category_id);
        $notAccessCountryList = [];

        if(\CRUDBooster::isSupplier()){
            $currentUserId = CRUDBooster::myParentId();
            $productBrands = DB::connection('marketplace')->table('marketplace_product_brand')
                            ->join('supplier_brands', 'supplier_brands.brand_id', '=', 'marketplace_product_brand.id')
                            ->where('supplier_brands.user_id',$currentUserId)
                            ->select('marketplace_product_brand.id','marketplace_product_brand.brand_name', 'supplier_brands.brand_logo')
                            ->get();
            if($row->shipping_method == 2){
                $notAccessCountryList = [2, 81, 83, 84];
            }
        }else{
            $productBrands = DB::connection('marketplace')->table('marketplace_product_brand')->get();
        }
        // if(\CRUDBooster::isDropMatrix() || \CRUDBooster::isDropmatixSupport() || \CRUDBooster::isDropmatixEinkauf()){
        //     $productBrands = \App\Models\Marketplace\ProductBrand::get();
        // }else{
        //     $productBrands = \App\Models\Marketplace\ProductBrand::where('user_id',$userId)->get();
        // }
        $productCountry = \App\Country::find($row->country_id);
        $channels = $this->channels;
        $updated_flags = MarketplaceAllowedChannel::select('country_id')->where('product_id', $id)->get();
        $all_updated_flag = array();
        foreach ($updated_flags as $updated_flag) {
            $all_updated_flag[] = $updated_flag->country_id;
        }
        $updated_flag = !empty($all_updated_flag) ? $all_updated_flag : \App\Enums\DefaultSelectedCountries::COUNTRY_IDS;
        $languageId = app('App\Services\UserService')->getProductCountry($userId);
        $countries = \App\Country::where('id', '!=', 11)->where(['is_active' => 1, 'status' => 1])->get();
        $selectedCountry = $countries->where('id', $languageId)->first();
        $all_countries = DB::connection('mysql')->table('all_country')->get();
        $shipping_companies = DB::connection('mysql')->table('drm_shipping_companies')->where('user_id',$userId)->get();
        $all_genders = Product::whereNotNull('gender')->whereNotIn('gender', ['', '0'])->distinct()->orderBy('gender')->pluck('gender')->toArray();
        $productSafetyInfo = ProductSafetyGPSR::where('product_id', $id)->orWhere('safety_brand_id', $row->brand)->first();

        return view('marketplace.product.new_details', compact('row', 'page_menu', 'page_title', 'command', 'id', 'parent_categories','productBrands','all_countries','shipping_companies','categories', 'productCategory', 'productCountry', 'countries', 'channels', 'updated_flag', 'selectedCountry', 'languageId', 'notAccessCountryList','userId','all_genders', 'productSafetyInfo'));
        // Dont turn on this comment line
        // return view('marketplace.product.details', compact('row', 'page_menu', 'page_title', 'command', 'id', 'previous', 'next', 'categories', 'productCategory', 'productCountry', 'countries', 'channels', 'updated_flag', 'selectedCountry', 'languageId'));
    }

    //Product Tag Save
    public function postProductTagSave()
    {
        $product_id = $_REQUEST['id'];
        $tags = explode(',', $_REQUEST['tags']);
        $data['tags'] = generateTags($tags);
        DB::connection('marketplace')->table('marketplace_products')->where('id', $product_id)->update($data);
        
        $drm_products =\App\Models\Marketplace\MpCoreDrmTransferProduct::where('marketplace_product_id',  $product_id)->pluck('drm_product_id')->toArray();
        if($drm_products){
            $updateableColumn['tags'] = $data['tags'];
            foreach($drm_products as $drm_product_id){
                app(\App\Services\DRMProductService::class)->update($drm_product_id, $updateableColumn);
            }
        }

    }

    public function postAddImage(Req $request)
    {
        try {
            if ($request->hasFile('file')) {
                $imagesAll = $request->file('file');
                foreach ($imagesAll as $image) {
                    $image_type = strtolower($image->getClientOriginalExtension());
                    if ($image_type == "jpg" || $image_type == "png" || $image_type == "jpeg" || $image_type == "gif") {
                        $product = app(MarketplaceProductService::class)->getById($request->product_id);

                        $image_url = uploadImage($image, 'marketplace-products/' . CRUDBooster::myParentId());
                        if ($image_url && !in_array($image_url, $product->image)) {
                            $data = $product->image;
                            $data[] = $image_url;

                            app(MarketplaceProductService::class)->update($product->id, ['image' => $data]);
                        }
                    }
                }

                return response()->json(['success' => true], 200);
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false], 422);
        }
    }

    public function drmDisableUpdate($array)
    {
        $product_id = $array['product_id'];
        unset($array['product_id']);
        $product = DB::connection('marketplace')->table('marketplace_products')->where('id', $product_id)->first();
        $update_status = json_decode($product->update_status, true);
        foreach ($array as $key => $value) {
            $update_status[$value] = 0;
            insertChangeLog($product_id, $value, 2);
        }
        $update_status = json_encode($update_status);
        DB::connection('marketplace')->table('marketplace_products')->where('id', $product_id)->update(['update_status' => $update_status]);
    }

    public function postDeleteImage(Req $request)
    {
        try {
            $product = app(MarketplaceProductService::class)->getById($request->product);

            $path = str_replace("https://drm-file.fra1.digitaloceanspaces.com/", "/", $request->image);
            if ($path && Storage::disk('spaces')->exists('/' . $path)) {
                Storage::disk('spaces')->delete('/' . $path);
            }
            $remove_img = array_diff($product->image ?? [], [$request->image]);
            app(MarketplaceProductService::class)->update($request->product, ['image' => array_values($remove_img)]);
            if($remove_img == "" || $remove_img == null){
                $product->update(['status' => ProductStatus::QUALITY_DEFECT]);
            }
            return response()->json(['success' => true], 200);
        } catch (\Exception $e) {
        }
    }

    public function postProductEdit()
    {
        $_REQUEST['ek_price']      = removeCommaFromPrice($_REQUEST['ek_price']);
        $_REQUEST['uvp']           = removeCommaFromPrice($_REQUEST['uvp']);
        $_REQUEST['shipping_cost'] = removeCommaFromPrice($_REQUEST['shipping_cost']);
        $_REQUEST['purchase_price']      = removeCommaFromPrice($_REQUEST['purchase_price']);

        $category_id = $_REQUEST['category_id'];
        $stock = $_REQUEST['send_stock'] > 0 ? $_REQUEST['send_stock'] : $_REQUEST['stock'];

        $status = $_REQUEST['status'];
        $gender = $_REQUEST['gender'];

        $vk_price = $_REQUEST['purchase_price'];
        $ek_price = $_REQUEST['ek_price'];
        $uvp = $_REQUEST['uvp'];

        $product_id = $_REQUEST['product_id'];
        $import = $_REQUEST['collection_id'];

        $title = $_REQUEST['name'];
        $desc = $_REQUEST['description'];
        $note = $_REQUEST['note'];

        $item_size = $_REQUEST['item_size'];
        $item_color = $_REQUEST['item_color'];
        $brand = $_REQUEST['brand_id'];
        $item_weight = $_REQUEST['item_weight'];
        $materials = $_REQUEST['materials'];
        $production_year = $_REQUEST['production_year'];
        $delivery_days = $_REQUEST['delivery_days'];
        $shipping_cost = $_REQUEST['shipping_cost'];
        $request_industry_template = $_REQUEST['industry_template'];
        $country_id = $_REQUEST['state_id'];
        $product_length = empty($_REQUEST['product_length']) ? 0 : $_REQUEST['product_length'];
        $product_width =  empty($_REQUEST['product_width']) ? 0 : $_REQUEST['product_width'];
        $product_height = empty($_REQUEST['product_height']) ? 0 : $_REQUEST['product_height'];
        $packing_unit = $_REQUEST['packing_unit'];
        $packaging_length =  empty($_REQUEST['packaging_length']) ? 0 : $_REQUEST['packaging_length'];
        $packaging_width =  empty($_REQUEST['packaging_width']) ? 0 : $_REQUEST['packaging_width'];
        $packaging_height =  empty($_REQUEST['packaging_height']) ? 0 : $_REQUEST['packaging_height'];
        $volume = (($product_length * $product_width * $product_height) / 1000000) ?? 0;
        $volume_gross = (($packaging_length * $packaging_width * $packaging_height) /1000000) ?? 0;
        $vat = explode("_", $_REQUEST['tax'])[1];
        $tax_type  = explode("_", $_REQUEST['tax'])[0];

        $product = Product::with('stockSendComment')->find($product_id);

        if($product->shipping_method == 1){
            $shipping_type = $_REQUEST['shipping_type'];
        }
        $industry_template = app(\App\Services\Marketplace\ProductService::class)->formIndustryTemplateDataFormat(
            $product->category_id,
            $product->country_id,
            $request_industry_template
        );
        if ($_REQUEST['delivery_days'] == 0 && $product->api_product_id == 0) {
            $delivery_days = 1;
        } else if ($_REQUEST['delivery_days'] == 0 && $product->api_product_id > 0) {
            $delivery_days = 3;
        }

         $exist_additional_eans = $product->additional_eans ?? [];
         $additional_eans = array_merge($exist_additional_eans, [$_REQUEST['new_ean']]?? []);
        // //add new industry_template_field
        // if($industry_template == null){
        //     $currentUserId = CRUDBooster::myParentId();
        //     $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        //     $lang = app('App\Services\UserService')->getProductLanguage($languageId);
        //     $industry_template_name = $_REQUEST['industry_template'];
        //     if ($industry_template_name) {
        //         $industry_template_field_list = $_REQUEST[$industry_template_name];
        //         $tmp_field_list = [];

        //         foreach ($industry_template_field_list as $field_key => $field_value) {

        //             $tmp_field_list[$field_key] = [
        //                 'de' => ($lang == 'de') ? $field_value : null,
        //                 'en' => ($lang == 'en') ? $field_value : null,
        //             ];
        //         }
        //         $industry_temp_data[$industry_template_name] = $tmp_field_list;
        //     }
        //     if($industry_temp_data){
        //         $industry_template = json_encode($industry_temp_data, true);
        //     }
        // }

        $values = array(
            'name' => $title,
            'description' => $desc,
            'vk_price' => floatval($vk_price),
            'ek_price' => floatval($ek_price),
            'uvp' => floatval($uvp),

            'stock' => $stock ?? $product->stock,
            'alarm_quantity' => $_REQUEST['min_stock'],
            // 'status' => $status,
            'gender' => $gender,
            'item_size' => $item_size,
            'item_color' => $item_color,
            'brand' => $brand,
            'item_weight' => $item_weight,
            'note' => $note,
            'materials' => $materials,
            'production_year' => $production_year,
            // 'category' => json_encode($category),
            'category_id' => $category_id,
            'country_id'  => $country_id ?? 1,
            'shipping_cost' => $shipping_cost ?? $product->shipping_cost,
            'delivery_days' => $delivery_days,
            'industry_template_data' => $industry_template,
            'additional_eans' => array_filter($additional_eans),
            'shipping_type' => $shipping_type ?? $product->shipping_type,
            'vat' => $vat,
            'tax_type' => $tax_type,
        );

        $additionalInfo = [
            'product_id'  => $product_id,
            'manufacturer' => $_REQUEST['manufacturer'],
            'manufacturer_link' => $_REQUEST['manufacturer_link'],
            'manufacturer_id' => $_REQUEST['manufacturer_id'],
            'custom_tariff_number' => $_REQUEST['tariff_number'],
            'shipping_company_id' => $_REQUEST['shipping_company_id'],
            'region' => $_REQUEST['region_id'],
            'country_of_origin' => $_REQUEST['country_id'],
            'min_stock' => $_REQUEST['min_stock'],
            'min_order' => $_REQUEST['min_order'],
            'gross_weight' => $_REQUEST['gross_weight'],

            'product_length'=> $product_length,
            'product_width' => $product_width,
            'product_height'=> $product_height,
            'volume' => $volume > 0 ? number_format($volume,6) : '',

            'packaging_length'=> $packaging_length,
            'packaging_width' => $packaging_width,
            'packaging_height'=> $packaging_height,
            'item_unit' => $_REQUEST['item_unit'],
            'shipping_company_id' => $_REQUEST['mp_shipping_method_select'],
            'packing_unit' => $packing_unit,
            'volume_gross' => $volume_gross > 0 ? number_format($volume_gross,6) : '',
            'model_number' => $_REQUEST['model_number'],
            'item_description' => $_REQUEST['item_description'],
            'item_sub_color' => $_REQUEST['item_sub_color'],
            'base_materials' => $_REQUEST['base_materials'],
            'quantity_in_ml' => $_REQUEST['quantity_in_ml'],
            'area_in_cube' => $_REQUEST['area_in_cube'],
            'area_in_square' => $_REQUEST['area_in_square'],
            'price_labeling_obligation' => $_REQUEST['price_labeling_obligation'],
            'base_price_reference' => $_REQUEST['base_price_reference'],
            'base_price' => $_REQUEST['base_price'],
            'target_group' => $_REQUEST['target_group'],
            'age_recommendation' => $_REQUEST['age_recommendation'],
            'electrical_appliance' => $_REQUEST['electrical_appliance'],
            'energy_efficiency_class' => $_REQUEST['energy_efficiency_class'],
            'energy_label' => $_REQUEST['energy_label'],
            'quantity_in_pieces' => $_REQUEST['quantity_in_pieces'],
        ];

        $productSafetyInfo = [
            'product_id'  => $product_id,
            'safety_company_name' => $_REQUEST['safety_company_name'],
            'safety_brand_id' => $_REQUEST['safety_brand_id'],
            'safety_email' => $_REQUEST['safety_email'],
            'safety_phone' => $_REQUEST['safety_phone'],
            'safety_street' => $_REQUEST['safety_street'],
            'safety_city' => $_REQUEST['safety_city'],
            'safety_zip_code' => $_REQUEST['safety_zip_code'],
            'safety_country_id' => $_REQUEST['safety_country_id'],
        ];

        if (\CRUDBooster::isSupplier()) unset($values['vk_price']);

        $updates['product_id'] = $product_id;

        if ($product->stock != $stock) {
            $updates[] = 'stock';
        }


        if ($product->status != $status) {
            $updates[] = 'status';
        }

        // if ($product->gender != $gender) {
        //     $updates[] = 'gender';
        // }


        if ($product->vk_price != floatval($vk_price)) {
            $updates[] = 'vk_price';
        }

        if ($product->uvp != floatval($uvp)) {
            $updates[] = 'uvp';
        }

        if ($product->ek_price != floatval($ek_price)) {
            $updates[] = 'ek_price';
        }

        if ($product->name != $title) {
            $updates[] = 'name';
        }

        if ($product->description != $desc) {
            $updates[] = 'description';
        }

        // if ($product->category_id != $category) {
        //     $updates[] = 'category_id';
        // }
        if ($product->category_id != $category_id) {
            $updates[] = 'category_id';
        }

        if ($product->item_color != $item_color) {
            $updates[] = 'item_color';
        }

        if ($product->item_size != $item_size) {
            $updates[] = 'item_size';
        }

        if ($product->brand != $brand) {
            $updates[] = 'brand';
        }

        if ($product->item_weight != $item_weight) {
            $updates[] = 'item_weight';
        }

        if ($product->materials != $materials) {
            $updates[] = 'materials';
        }

        if ($product->production_year != $production_year) {
            $updates[] = 'production_year';
        }

        //ek_price update range checke max up 10%, max down 70%
        $updated_ek_price = $_REQUEST['ek_price'];
        $block_product = false;

        // if (!(\CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || \CRUDBooster::hasDropmatixMpSupport())) {
        //     if ($updated_ek_price < $product->ek_price) {
        //         $price_down =  $product->ek_price - $updated_ek_price;
        //         $ek_max_down = $product->ek_price - ($product->ek_price * PriceRange::MAX_DOWN_RANGE);
        //         if ($price_down > $ek_max_down) {
        //             $block_product = true;
        //         }
        //     } else {
        //         $price_up =  $updated_ek_price - $product->ek_price;
        //         $ek_max_up = $product->ek_price * PriceRange::MAX_UP_RANGE;
        //         if ($price_up > $ek_max_up) {
        //             $block_product = true;
        //         }
        //     }
        // }

        if(in_array(strip_tags($desc), ["", null])){
            $product->update(['status' => ProductStatus::QUALITY_DEFECT]);
        }

        if (in_array(CRUDBooster::myParentId(),[2455,50131]) && ($product->delivery_days > $_REQUEST['delivery_days'])) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Delivery Day can extend but not shorten the times", "info");
        }

        if ($block_product == false && $product->status == ProductStatus::BLOCKED) {
            $values['status'] = ProductStatus::PENDING;
            $product->update($values);

            if ($_REQUEST['delivery_days'] == 0 && $product->api_product_id == 0) {
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Delivery Day Must be greater than 1 days", "info");
            }
            if ($_REQUEST['delivery_days'] == 0 && $product->api_product_id > 0) {
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Delivery Day Must be greater than 3 days", "info");
            } else {
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Product Edited Successfully", "success");
            }
        } else if ($block_product) {
            // Prevent to update ek_price
            // unset($values['ek_price']);
            $values['status'] = ProductStatus::BLOCKED;
            $product->update($values);
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Product has been blocked because of violating ek price rule.", "error");
        } else {

            $product->stockSendComment()->updateOrCreate(['mp_product_id' => $product->id ],['send_stock_comment' => request()->internal_notes]);
            $product->additionalInfo()->updateOrCreate(['product_id' => $product->id ], $additionalInfo);
            $product->productSafetyInfo()->updateOrCreate(['product_id' => $product->id ], $productSafetyInfo);
            $updateStatus = $product->update($values);
            if ($updateStatus) {
                $p = \App\Models\Marketplace\Product::with('additionalInfo','productBrand')->find($product->id);
                $this->syncUpdateWithDrmProduct($p);
            }
        }

        if ($_REQUEST['delivery_days'] == 0 && $product->api_product_id == 0) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Delivery Day Must be greater than 1 days", "info");
        }
        if ($_REQUEST['delivery_days'] == 0 && $product->api_product_id > 0) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Delivery Day Must be greater than 3 days", "info");
        } else {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Product info updated Successfully", "success");
        }
    }

    public function getEnableUpdate()
    {
        $id = $_REQUEST['id'];
        $value = $_REQUEST['val'];
        $product = Product::find($id);
        $update_status =  json_decode($product->update_status, true);
        $update_status[$value] = 1;
        $update_status = json_encode($update_status);
        $product->update_status = $update_status;
        $product->save();
        insertChangeLog($id, $value, 3);
        return redirect()->back();
    }


    public function postTopProduct(Req $request)
    {
        $product = Product::find($request->id);
        $message = "Updated Successfully";
        $days = "";

        if ($product->is_top_product == 1) {
            $product->is_top_product = 0;
        } else {
            if ($product->top_product_datetime != null && $product->top_product_datetime > date('Y-m-d H:i:s')) {
                $message = "You can't turn status ON before " . $this->daysDifference($product->top_product_datetime);
            } else {
                $product->is_top_product = 1;
                $product->top_product_datetime = Carbon::now()->addDay(30);
                $days = $this->daysDifference($product->top_product_datetime);
            }
        }

        $product->update();

        return response()->json(['status' => true, 'message' => $message, "top_status" => $product->is_top_product, 'days' => $days]);
    }

    private function daysDifference($date1)
    {
        $fdate = $date1;
        $tdate = Carbon::now()->addMinutes(1);
        $datetime1 = new \DateTime($fdate);
        $datetime2 = new \DateTime($tdate);
        $interval = $datetime1->diff($datetime2);
        $days = $interval->format('%a');

        return "{$days} Days";
    }

    public function postAddImageUrl(Req $request)
    {
        $id = $request->product_id;
        $url = $request->url;

        if (!empty($url)) {
            $product = app(MarketplaceProductService::class)->getById($id);
            $upload_url = upload_url_image($url);
            app(MarketplaceProductService::class)->update($id, ['image' => array_merge($product->image ?? [], $upload_url)]);

            return response()->json(['success' => true], 200);
        } else {
            return response()->json(['success' => false], 422);
        }
    }

    public function getStock($id)
    {
        return app(InternelSyncService::class)->retriveStockData($id);
    }

    public function getMakeOrder($productId)
    {
        $data['page_title'] =  'Order Create';
        $form = [];

        $form['Client'] = [
            'CustomerID'        => ['label' => 'Customer ID'],
            'ClientFirstName'   => ['label' => 'First Name', 'required' => 1],
            'ClientLastName'    => ['label' => 'Last Name', 'required' => 1],
            'CompanyName'       => ['label' => 'Company Name'],
        ];

        $form['ShipmentMethod'] = [
            'REGULAR', 'BY_NOON', 'SATURDAY'
        ];
        $form['PaymentMethod']  = [
            'CASH', 'TRANSFER'
        ];

        $countries = DB::table('countries')
            ->select('id', 'name', 'language_shortcode')
            ->where('is_active', 1)
            ->get();
        $form['countries'] = $countries;

        $product = Product::find($productId);

        return view('marketplace.product.order_form', compact('form', 'product', 'data'));
    }

    public function postOrderSyncSubmit()
    {
        $request = $_REQUEST;

        try {
            $response = app(InternelSyncService::class)->transferOutgoingOrders($request);

            $product_ids = [$response['product_id']];

            foreach ($response['addedOrderIds'] as $orderId) {
                MarketplaceSyncedOrder::create([
                    'supplier_id'   => CRUDBooster::myParentId(),
                    'order_date'    => $request['OrderDateTime'] ?? '',
                    'order_id'      => $request['OrderID'],
                    'product_ids'   => json_encode($request['ProductID']),
                    'status'        => 'ADDED',
                ]);
            }

            Product::where('marketplace_product_id', $request['ProductID'])
                ->update([
                    'internel_order_id' => $request['OrderID'],
                ]);

            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Order Synced Successfully.', 'info');
        } catch (Exception $e) {
        }
    }

    public function getOrders()
    {
        $this->cbLoader();

        $data['page_title'] = 'Marketplace Synced Orders';
        $orders = MarketplaceSyncedOrder::where('supplier_id', CRUDBooster::myParentId())
            ->get();

        return view('marketplace.orders.synced_orders', compact('orders', 'data'));
    }

    public function getExportOrder()
    {
    }

    public function getAllCategories()
    {
        $categories = Category::where('is_active', 1)
            ->select('name', 'id')
            ->get();

        $responseHtml = '';
        foreach ($categories as $category) {
            $responseHtml .= "<li value='" . $category->id . "'>" . $category->name . "</li>";
        }

        return $responseHtml;
    }

    public function postChangeStatus()
    {
        try {
            $country_id = Session::get('filter_country_' . CRUDBooster::myParentId()) ?? 1;
            $productIds = explode(',', request()->product_id);
            $not_approved_product = [];
            $products = Product::with('mainCategory:id,minimum_price')->where('country_id', $country_id)->whereIn('id', $productIds)->where('vk_price', '<>', 0)->get();

            if (request()->updating_status == 1) {
                $product_ids = [];
                foreach ($products as $product) {
                    $category_min_price = $product->mainCategory->minimum_price ?? 0;
                    $is_product_dublicate_live = Product::where('ean', $product->ean ?? '')
                            ->where('country_id', $country_id)
                            ->where('is_dublicate', 1)
                            ->where('best_price', 1)
                            ->first();

                    if ($is_product_dublicate_live != null && request()->updating_status == 1) {
                        array_push($not_approved_product, $product->ean);
                    } else if ($category_min_price < $product->ek_price) {
                        $product->update([
                            'status' => intval(request()->updating_status),
                        ]);
                        $product_ids[] = $product->id;
                        if ($product->is_dublicate == 1) {
                            $this->productService->bulkDuplicateApproved($product->id);
                        }
                    }
                    $this->productService->otherCountryDuplicateStatusChange([$product->ean], request()->updating_status);
                }

                $suppliers_products = $products->whereIn('id', $product_ids)->where('supplier_id', '>', 0)->groupBy('supplier_id');
                foreach ($suppliers_products as $supplier_id => $supplier_product) {

                    if (count($supplier_product) > 1) {
                        $html = '<ul>';
                        foreach ($supplier_product->pluck('ean')->toArray() as $ean) {
                            $html .= '<ol>' . $ean . '</ol>';
                        }
                        $html .= '</ul>';

                        if ($supplier_product[0]->shipping_method == 1) {
                            $slug = 'dropshipping_product_accepted';
                        } else if ($supplier_product[0]->shipping_method == 2) {
                            $slug = 'fulfilment_product_accepted';
                        }

                        $supplier_email = User::where('id', $supplier_id)->first();
                        $tags = [
                            'name' => $supplier_email->name,
                            'ean' => $html,
                        ];
                        if($supplier_email->email != '<EMAIL>'){
                            $mail_data = DRMParseMailTemplate($tags, $slug);
                            app('drm.mailer')->getMailer()->to($supplier_email->email)->send(new DRMSEndMail($mail_data)); //Send
                        }
                    }
                }
            } else {
                \App\Models\Marketplace\Product::whereIn('id', $productIds)
                    ->where('country_id', $country_id)
                    ->where('vk_price', '<>', 0)
                    ->update([
                        'status' => intval(request()->updating_status),
                    ]);

                $this->productService->revokedProductDelete($products->pluck('id')->toArray());

                foreach ($products as $product) {
                    // if ($product->is_dublicate == 1) {
                    //     $this->productService->dublicateBestOneApproved($product->id, intval(request()->updating_status));
                    // }
                    $this->productService->otherCountryDuplicateStatusChange([$product->ean], request()->updating_status);
                    // $this->productService->revokedProductDelete($product->id);
                }

                // Rejected products email send off.....

                // if (request()->updating_status == 2) {

                //     $suppliers_products = $products->where('supplier_id', '>', 0)->groupBy('supplier_id');
                //     foreach ($suppliers_products as $supplier_id => $supplier_product) {

                //         $html = '<ul>';
                //         foreach ($supplier_product->pluck('ean')->toArray() as $ean) {
                //             $html .= '<ol>' . $ean . '</ol>';
                //         }
                //         $html .= '</ul>';

                //         $slug = 'rejected_product_email';

                //         $supplier_email = User::where('id', $supplier_id)->first();
                //         $tags = [
                //             'name' => $supplier_email->name,
                //             'ean' => $html,
                //         ];
                //         if ($supplier_email->email != '<EMAIL>') {
                //             $mail_data = DRMParseMailTemplate($tags, $slug);
                //             app('drm.mailer')->getMailer()->to($supplier_email->email)->send(new DRMSEndMail($mail_data)); //Send
                //         }
                //     }
                // }

            }
            $notapproved_ean  = implode(",", $not_approved_product);
            $message = count($not_approved_product) >= 1 ? 'Status changed successfully and you ' . " can't" . ' approved this Ean ' . $notapproved_ean . ' because duplicate product find in live' : 'Status changed successfully.';
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $message, 'info');
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    public function getChangeStatus()
    {
        ini_set('max_execution_time', '0');
        try {
            $allProducts = $this->getTranferableProducts()->get();
            $productIds = $allProducts->pluck('id')->toArray();
            $skipProductIds = explode(',', request()->remove_product_ids);
            $productIds = array_diff($productIds, $skipProductIds);

            foreach(array_chunk($productIds, 500) as $approve_ids ){
                // $products = \App\Models\Marketplace\Product::with('mainCategory:id,minimum_price')->whereIn('id', $approve_ids)->where('vk_price', '<>', 0)->get();
    
                if (request()->updating_status == 1) {
                    // foreach( $products->chunk(500) as $approve_status ){
                        Log::info("Products status approve request job dispatch");
                        dispatch(new MarketplaceAllProductStatusUpdate($approve_ids));
                    // }
                    // $not_approved_product = [];
                    // dispatch(new MarketplaceAllProductStatusUpdate($products));
                    // foreach ($products as $product) {
                    //     $category_min_price = $product->mainCategory->minimum_price ?? 0;
                    //     $is_product_dublicate_live = Product::where('ean', $product->ean ?? '')->where('is_dublicate', 1)->where('best_price', 1)->first();
                    //     if ($is_product_dublicate_live != null && request()->updating_status == 1) {
                    //         array_push($not_approved_product, $product->ean);
                    //     } else if ($category_min_price < $product->ek_price) {
                    //         $product->update([
                    //             'status' => intval(request()->updating_status),
                    //         ]);
                    //         if ($product->is_dublicate == 1) {
                    //             $this->productService->bulkDuplicateApproved($product->id);
                    //         }
                    //     }
                    // }
                } else {
                    // $productIds = $products->pluck('id');
                    $country_id = Session::get('filter_country_' . CRUDBooster::myParentId()) ?? 1;
                    \App\Models\Marketplace\Product::whereIn('id', $productIds)
                        ->where('vk_price', '<>', 0)
                        ->where('country_id', $country_id)
                        ->update([
                            'status' => intval(request()->updating_status),
                        ]);
    
                    $products = \App\Models\Marketplace\Product::with('mainCategory:id,minimum_price')->whereIn('id', $approve_ids)->where('country_id', $country_id)->where('vk_price', '<>', 0)->get();
                    $this->productService->revokedProductDelete($products->pluck('id')->toArray() ?? []);
    
                    foreach ($products as $product) {
                        // if ($product->is_dublicate == 1) {
                        //     $this->productService->dublicateBestOneApproved($product->id, intval(request()->updating_status));
                        // }
                        $this->productService->otherCountryDuplicateStatusChange([$product->ean], request()->updating_status);
                        // $this->productService->revokedProductDelete($product->id);
                    }
                    // $message='Status changed successfully';
                    // \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $message, 'info');
    
                    // Rejected products email send off.....
    
                    // if (request()->updating_status == 2) {
    
                    //     $suppliers_products = $products->where('supplier_id', '>', 0)->groupBy('supplier_id');
                    //     foreach ($suppliers_products as $supplier_id => $supplier_product) {
    
                    //         $html = '<ul>';
                    //         foreach ($supplier_product->pluck('ean')->toArray() as $ean) {
                    //             $html .= '<ol>' . $ean . '</ol>';
                    //         }
                    //         $html .= '</ul>';
    
                    //         $slug = 'rejected_product_email';
    
                    //         $supplier_email = User::where('id', $supplier_id)->first();
                    //         $tags = [
                    //             'name' => $supplier_email->name,
                    //             'ean' => $html,
                    //         ];
                    //         if ($supplier_email->email != '<EMAIL>') {
                    //             $mail_data = DRMParseMailTemplate($tags, $slug);
                    //             app('drm.mailer')->getMailer()->to($supplier_email->email)->send(new DRMSEndMail($mail_data)); //Send
                    //         }
                    //     }
                    // }
    
                }
            }

            $message = 'Your selected ' .count($productIds). ' products status change on going on the background process. It may take some time to approve all products.';
                    \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $message, 'info');

            // $message = 'Status changed successfully';
            // \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $message, 'info');
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    public function postChangeCategory()
    {
        try {
            $request = $_REQUEST;
            $productIds = $request['product_id'];
            $mainCategory = $_REQUEST['post_sub_category'];
            $category = $request['category'];
            if (!$mainCategory) {
                \CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Category not select.', 'info');
            }
            $productIds = explode(',', $productIds);
            Product::whereIn('id', $productIds)->where('status', 1)->update([
                'category_id' => $mainCategory, 
                'category' => [$category] ?? []
            ]);

            $change_category = Category::where('id', $mainCategory)->select('id', 'im_handel')->first();
            $products = Product::whereIn('id', $productIds)->where('status', 1)->select('id', 'ean', 'is_dublicate', 'best_price')->get();
            $duplicate_products = $products->where('is_dublicate', 1);
            $not_duplicate_ids = $products->where('is_dublicate', '<>', 1)->pluck('id');

            //.... Change duplicate EAN category ....
            if ($duplicate_products->isNotEmpty()) {
                $this->duplicateProductCategoryUpdate($duplicate_products, $change_category);
            }

            // ... IM_HANDEL value change ...
            if ($not_duplicate_ids->isNotEmpty()) {
                dispatch(new MarketplaceIMHandelSync($not_duplicate_ids, $change_category->im_handel ?? 0.00));
            }

            // ... Category change sync with DRM ...
            $checkExistInDrm = MpCoreDrmTransferProduct::whereIn('marketplace_product_id', $productIds)->get();
            if (count($checkExistInDrm) > 0) {
                foreach ($checkExistInDrm->chunk(350) as $product) {
                    dispatch(new MarketplaceCategorySyncToDrm($product));
                }
            }

            $productIds = implode(',', $productIds);
            return view('marketplace.product.modals.product_status_change_modal', compact('productIds'));
            // \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Category changed successfully.', 'info');
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    public function getChangeCategory()
    {
        try {
            $products = $this->getTranferableProducts()->get();
            $request = $_REQUEST;
            $mainCategory = $request['get_sub_category'];
            $category = $request['category'];
            $productIds = $products->where('status', 1)->pluck('id')->toArray();
            $skipProductIds = $request['remove_product_ids'] ?? [];
            $productIds = array_diff($productIds, $skipProductIds);

            // ... Update category ...
            Product::whereIn('id', $productIds)->update([
                'category_id' => $mainCategory, 
                'category' => [$category] ?? []
            ]);

            $change_category = Category::where('id', $mainCategory)->select('id', 'im_handel')->first();
            foreach(array_chunk($productIds, 1500) as $product_ids){
                $products = Product::whereIn('id', $product_ids)->select('id', 'ean', 'is_dublicate', 'best_price')->get();
                $duplicate_products = $products->where('is_dublicate', 1);
                $not_duplicate_ids = $products->where('is_dublicate', '<>', 1)->pluck('id');
    
                //.... Change duplicate EAN category ....
                if ($duplicate_products->isNotEmpty()) {
                    $this->duplicateProductCategoryUpdate($duplicate_products, $change_category);
                }
    
                // ... IM_HANDEL value change ...
                if ($not_duplicate_ids->isNotEmpty()) {
                    dispatch(new MarketplaceIMHandelSync($not_duplicate_ids, $change_category->im_handel ?? 0.00));
                }
    
                // ... Category change sync with DRM ...
                $checkExistInDrm = MpCoreDrmTransferProduct::whereIn('marketplace_product_id', $product_ids)->get();
                if ($checkExistInDrm->isNotEmpty()) {
                    foreach ($checkExistInDrm->chunk(350) as $product) {
                        dispatch(new MarketplaceCategorySyncToDrm($product));
                    }
                }
            }

            $productIds = implode(',', $productIds);
            return view('marketplace.product.modals.product_status_change_modal', compact('productIds'));
            // \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Category changed successfully.', 'info');
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    private function duplicateProductCategoryUpdate($products, $category)
    {
        try {
            foreach ($products as $duplicate_product) {
                // $active_priority_product = $this->getActivePriorityProduct($duplicate_product->ean, 2, 'internel_stock');

                // if (!$active_priority_product) {
                //     $active_priority_product = $this->getActivePriorityProduct($duplicate_product->ean, 1, 'stock');
                // }

                // if ($active_priority_product) {
                //     $this->updateProductCategoryAndSyncIMHandel(
                //         $active_priority_product->id,
                //         $active_priority_product->ean,
                //         $active_priority_product->mainCategory->id,
                //         $active_priority_product->mainCategory->im_handel ?? 0.00
                //     );
                // }

                $this->updateProductCategoryAndSyncIMHandel(
                    $duplicate_product->id,
                    $duplicate_product->ean,
                    $category->id,
                    $category->im_handel ?? 0.00
                );
            }
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    private function getActivePriorityProduct($ean, $shippingMethod, $stockField)
    {
        return Product::with('mainCategory:id,im_handel')
            ->where('ean', $ean)
            ->whereNotIn('status', [2, 7, 5])
            ->where('shipping_method', $shippingMethod)
            ->where($stockField, '>=', 1)
            ->where('vk_price', '<>', 0)
            ->select('id', 'ean', 'category_id')
            ->orderByRaw('`ek_price` + `real_shipping_cost`')
            ->first();
    }


    private function updateProductCategoryAndSyncIMHandel($productId, $ean, $categoryId, $imHandelValue)
    {
        try {
            // Update the category for available products
            Product::where('id', '<>', $productId)->where('ean', $ean)->update(['category_id' => $categoryId]);
        
            // Fetch the updated product IDs
            $productIds = Product::where('ean', $ean)->pluck('id');
        
            // Dispatch the job with the updated IDs and IM_HANDEL value
            dispatch(new MarketplaceIMHandelSync($productIds, $imHandelValue));
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    public function postChangeStatusWhenCategoryChange()
    {
        try {
            $productIds = explode(',', request()->product_id);
            $not_approved_product = [];
            $country_id = Session::get('filter_country_' . CRUDBooster::myParentId()) ?? 1;
            $products = \App\Models\Marketplace\Product::whereIn('id', $productIds)->where('country_id', $country_id)->where('vk_price', '<>', 0)->get();

            if (request()->updating_status == 1) {
                $product_ids = [];
                foreach ($products as $product) {
                    $category_min_price = $product->mainCategory->minimum_price ?? 0;
                    $is_product_dublicate_live = Product::where('ean', $product->ean ?? '')->where('country_id', $country_id)->where('is_dublicate', 1)->where('best_price', 1)->first();
                    if ($is_product_dublicate_live != null && request()->updating_status == 1) {
                        array_push($not_approved_product, $product->ean);
                    } else if ($category_min_price < $product->ek_price) {
                        $product->update([
                            'status' => intval(request()->updating_status),
                        ]);
                        $product_ids[] = $product->id;
                        if ($product->is_dublicate == 1) {
                            $this->productService->bulkDuplicateApproved($product->id);
                        }
                    }
                    $this->productService->otherCountryDuplicateStatusChange([$product->ean], request()->updating_status);
                }

                $suppliers_products = $products->whereIn('id', $product_ids)->where('supplier_id', '>', 0)->groupBy('supplier_id');
                foreach ($suppliers_products as $supplier_id => $supplier_product) {

                    if (count($supplier_product) > 1) {
                        $html = '<ul>';
                        foreach ($supplier_product->pluck('ean')->toArray() as $ean) {
                            $html .= '<ol>' . $ean . '</ol>';
                        }
                        $html .= '</ul>';

                        if ($supplier_product[0]->shipping_method == 1) {
                            $slug = 'dropshipping_product_accepted';
                        } else if ($supplier_product[0]->shipping_method == 2) {
                            $slug = 'fulfilment_product_accepted';
                        }

                        $supplier_email = User::where('id', $supplier_id)->first();
                        $tags = [
                            'name' => $supplier_email->name,
                            'ean' => $html,
                        ];
                        if ($supplier_email->email != '<EMAIL>') {
                            $mail_data = DRMParseMailTemplate($tags, $slug);
                            app('drm.mailer')->getMailer()->to($supplier_email->email)->send(new DRMSEndMail($mail_data)); //Send
                        }
                    }
                }
            } else {
                \App\Models\Marketplace\Product::whereIn('id', $productIds)
                    ->where('vk_price', '<>', 0)
                    ->where('country_id', $country_id)
                    ->update([
                        'status' => intval(request()->updating_status),
                    ]);

                $this->productService->revokedProductDelete($products->pluck('id')->toArray());

                foreach ($products as $product) {
                    // if ($product->is_dublicate == 1) {
                    //     $this->productService->dublicateBestOneApproved($product->id, intval(request()->updating_status));
                    // }
                    $this->productService->otherCountryDuplicateStatusChange([$product->ean], request()->updating_status);
                    // $this->productService->revokedProductDelete($product->id);
                }

                // Rejected products email send off.....

                // if (request()->updating_status == 2) {

                //     $suppliers_products = $products->where('supplier_id', '>', 0)->groupBy('supplier_id');
                //     foreach ($suppliers_products as $supplier_id => $supplier_product) {

                //         $html = '<ul>';
                //         foreach ($supplier_product->pluck('ean')->toArray() as $ean) {
                //             $html .= '<ol>' . $ean . '</ol>';
                //         }
                //         $html .= '</ul>';

                //         $slug = 'rejected_product_email';

                //         $supplier_email = User::where('id', $supplier_id)->first();
                //         $tags = [
                //             'name' => $supplier_email->name,
                //             'ean' => $html,
                //         ];
                //         if ($supplier_email->email != '<EMAIL>') {
                //             $mail_data = DRMParseMailTemplate($tags, $slug);
                //             app('drm.mailer')->getMailer()->to($supplier_email->email)->send(new DRMSEndMail($mail_data)); //Send
                //         }
                //     }
                // }
            }

            $productIds = implode(',', $productIds);
            $customers = \App\User::where('id_cms_privileges', 3)->where('status','Active')->where('marketplace_access', 1)->get();
            return view('marketplace.product.modals.product_direct_transfer_modal', compact('productIds','customers'));
            // $notapproved_ean  = implode(",", $not_approved_product);
            // $message = count($not_approved_product) >= 1 ? 'Status changed successfully and you ' . " can't" . ' approved this Ean ' . $notapproved_ean . ' because duplicate product find in live' : 'Status changed successfully.';
            // \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $message, 'info');
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    public function postAllFilteredProductsTransferToAnalysis()
    {
        try {
            $product_ids = $this->getIndex();
            $user_id = CRUDBooster::myParentId();
            $res = app(TransferProduct::class)->supplierProductTransferToAnalysis($user_id, $product_ids);
            return response()->json($res);
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    public function getSendEmailSyncedProductsPDF($historyId)
    {
        dd($historyId);
    }

    /**
     * Developed By EFIT Team
     * Contribution
     */

    public function getAdd()
    {
        $this->cbLoader();

        // Prevent Customer to see this page
        if (!(CRUDBooster::isSuperAdmin() || CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport() || CRUDBooster::isSupplier())) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        // Check access for sub supplier
        if (!$this->suppliersService->hasAccess('INSERT_NEW_ARTICLES_MANUALLY')) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $page_title = __('Marketplace Products');

        // $categories =  DB::connection('marketplace')->table('marketplace_categories')->select('id', 'name')->get();
        $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active', 1)->orderBy('name')->get();

        $countries  = \App\AllCountry::get();

        $suppliers = app(\App\Services\Marketplace\ProductService::class)->getDeliveryCompanies();

        if(\CRUDBooster::isSupplier()){
            $currentUserId = CRUDBooster::myParentId();
            $productBrands = DB::connection('marketplace')->table('marketplace_product_brand')
                ->join('supplier_brands', 'supplier_brands.brand_id', '=', 'marketplace_product_brand.id')
                ->where('supplier_brands.user_id',$currentUserId)
                ->select('marketplace_product_brand.id','marketplace_product_brand.brand_name', 'supplier_brands.brand_logo')
                ->get();
        }else{
            $productBrands = DB::connection('marketplace')->table('marketplace_product_brand')->get();
        }

        // if(CRUDBooster::myId() == 2570){
        return view('marketplace.product.add_product', compact('page_title', 'collections', 'parent_categories', 'countries', 'suppliers','productBrands'));
        // }else{
        //     return view('marketplace.product.old_add_product', compact('page_title', 'collections', 'parent_categories', 'countries', 'suppliers','productBrands'));
        // }
    }


    public function postAdd(Req $request)
    {
        if (!(CRUDBooster::isSuperAdmin() || CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport() || CRUDBooster::isSupplier())) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }
        $ek_price           = removeCommaFromPrice($request->ek_price);
        $uvp                = removeCommaFromPrice($request->uvp);
        $vat                = removeCommaFromPrice($request->vat);
        $real_shipping_cost = removeCommaFromPrice($request->shipping_cost);

        Validator::make($request->all(), [
            "name"      => "required",
            "ek_price"  => "required|numeric|min:1",
            'uvp'       => 'required|numeric|min:1',
            'image'     => 'required',
            "category_id"     => 'required',
            'shipping_method' => 'required',
            'description'     => 'required',
            'vat'       => 'required|numeric'
        ]);

        $vk_price = 0;


        if(isset($request->shipping_method)){
            if($request->shipping_method == 1){
                $request->validate(['stock' => 'required','handling_time' => 'required', 'shipping_cost' => 'required']);
                $shipping_type = $request->shipping_type ?? null;
            }
            if ($request->shipping_method == 2) {
                $request->validate(['item_weight' => 'required','product_length'=>'required','product_width'=>'required','product_height'=>'required']);
                $real_shipping_cost = 5.20;
                $request->handling_time = 2;
                $request->stock = 0;
                $shipping_type = null;
            }
        }

        if (\CrudBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
            $deliveryCompanyId  = $request->supplier_id;
            $supplierId         = null;
            // $vk_price = removeCommaFromPrice($request->vk_price);
            // Validator::make($request->all(), [
            //     'vk_price'       => 'required|numeric|min:1',
            // ]);
        } else {
            $supplierId         = \CRUDBooster::myParentId();
            $deliveryCompanyId  = \App\DeliveryCompany::where('supplier_id', $supplierId)->first()->id ?? '';
        }

        // Updated Logic
        if (Product::where('delivery_company_id', $deliveryCompanyId)->where('ean', $request->ean)->exists()) {
            return redirect()->back()->withInput($request->input())->with('ean_dupplication_msg', trans('marketplace.ean_already_exists'));
        }


        // if (in_array($supplierId, FakeSuppliers::FAKE_IDS)) {
        //     $vk_price = $request->ek_price + ($request->ek_price * 0.05);
        //     $vk_price = (float)str_replace(',', '', number_format($vk_price, 2));
        // } else {
        //     $vk_price = $request->vk_price ?? 0;
        // }


        // Industry template data
        $industryTemplateData = app(\App\Services\Marketplace\ProductService::class)->formIndustryTemplateDataFormat(
            $request->category_id,
            $request->state_id,
            $request->industry_fields
        );
        // $country = DB::table('countries')->where('id', $request->state_id)->first();

        $country = \App\Country::select('tax_rate', 'reduced_tax_rate')->find($request->state_id);

        // $status = ProductStatus::PENDING;
        // if ($request->shipping_method == ShippingMethod::DROPSHIPPING  && $request->stock == 0) {
        //     $status = ProductStatus::QUALITY_DEFECT;
        // }
        
        $shipping_cost  = $real_shipping_cost * 1.10;
        $calculation    = MarketplaceProfitCalculation::find(24);
        if($shipping_cost > 35){
            $vk_price = $this->calculatePrice($ek_price, $calculation, $uvp, 35);
            $vk_price += $shipping_cost - 35;
            $shipping_cost = 35;
        }else{
            $vk_price = $this->calculatePrice($ek_price, $calculation, $uvp, $shipping_cost);
        }

        $category_im_handel = Category::where('id', $request->category_id)->first();
        $im_handel = 0;
        if(isset($category_im_handel) && $category_im_handel->im_handel > 0){
            $im_handel =  $vk_price + (($vk_price * $category_im_handel->im_handel) / 100);
        }

        $VALUES = app(MarketplaceProductService::class)->store([
            'name'      => $request->name,
            'ean'       => $request->ean,
            'ek_price'  => $ek_price,
            'uvp'       => $uvp,
            'vat'       => $vat,
            'tax_type'  => $vat == $country->reduced_tax_rate ? 2 : 1,
            'stock'     => $request->stock,
            'brand'     => $request->brand,
            'tags'      => $request->tags,
            'item_size' => $request->item_size,
            'materials' => $request->materials ?? '',
            'category'  => explode('->', $request->category), #sub category
            'vk_price'  => $vk_price,
            'image'     => $request->image,
            'gender'    => $request->gender,

            'supplier_id'           => $supplierId,
            'item_color'            => $request->item_color,
            'item_weight'           => $request->item_weight,
            'item_number'           => $request->item_number,
            'category_id'           => $request->category_id,
            'delivery_company_id'   => $deliveryCompanyId,
            'production_year'       => $request->production_year,
            'description'           => $request->description,
            'update_enabled'        => $request->update_enabled,
            'delivery_days'         => $request->handling_time,
            'shipping_method'       => $request->shipping_method,
            'real_shipping_cost'    => $real_shipping_cost,
            'shipping_cost'         => $shipping_cost,
            'country_id'            => $request->state_id ?? 1,
            'status'                => ProductStatus::PENDING,
            'industry_template_data' => $industryTemplateData,
            'alarm_quantity'        => $request->min_stock,
            'shipping_type'         => $shipping_type ?? null,
            'im_handel'             => $im_handel,
        ]);

        $product_length     = empty($request->product_length) ? 0 : $request->product_length;
        $product_width      =  empty($request->product_width) ? 0 : $request->product_width;
        $product_height     = empty($request->product_height) ? 0 : $request->product_height;
        $packing_unit       = $request->packing_unit;
        $packaging_length   =  empty($request->packaging_length) ? 0 : $request->packaging_length;
        $packaging_width    =  empty($request->packaging_width) ? 0 : $request->packaging_width;
        $packaging_height   =  empty($request->packaging_height) ? 0 : $request->packaging_height;
        $volume             = (($product_length * $product_width * $product_height) / 1000000) ?? 0;
        $volume_gross       = (($packaging_length * $packaging_width * $packaging_height) /1000000) ?? 0;

        $additionalInfo = [
            'product_id'  => $VALUES->id,
            'manufacturer' => $request->manufacturer,
            'manufacturer_link' => $request->manufacturer_link,
            'manufacturer_id' => $request->manufacturer_id,
            'custom_tariff_number' => $request->tariff_number,
            'shipping_company_id' => $request->shipping_company_id,
            'region' => $request->region_id,
            'country_of_origin' => $request->state_id,
            'min_stock' => $request->min_stock,
            'min_order' => $request->min_order,
            'gross_weight' => $request->gross_weight,

            'product_length'=> $product_length,
            'product_width' => $product_width,
            'product_height'=> $product_height,
            'volume' => $volume > 0 ? number_format($volume,6) : '',

            'packaging_length'=> $packaging_length,
            'packaging_width' => $packaging_width,
            'packaging_height'=> $packaging_height,
            'item_unit' => $request->item_unit,
            'shipping_company_id' => $request->mp_shipping_method_select,
            'packing_unit' => $packing_unit,
            'volume_gross' => $volume_gross > 0 ? number_format($volume_gross,6) : '',

            'model_number'  => $request->model_number,
            'item_description' => $request->item_description,
            'item_sub_color' => $request->item_sub_color,
            'base_materials' => $request->base_materials,
            'quantity_in_ml' => $request->quantity_in_ml,
            'area_in_square' => $request->area_in_square,
            'area_in_cube'   => $request->area_in_cube,
            'quantity_in_pieces' => $request->quantity_in_pieces,
            'price_labeling_obligation' => $request->price_labeling_obligation,
            'base_price_reference' => $request->base_price_reference,
            'target_group' => $request->target_group,
            'age_recommendation' => $request->age_recommendation,
            'electrical_appliance' => $_REQUEST['electrical_appliance'],
            'energy_efficiency_class' => $_REQUEST['energy_efficiency_class'],
            'energy_label' => $_REQUEST['energy_label'],
        ];

        app(MarketplaceProductService::class)->createAdditionalInfo($additionalInfo);

        $productSafetyInfo = [
            'product_id'  => $VALUES->id,
            'safety_company_name' => $request->safety_company_name,
            'safety_brand_id' => $request->safety_brand_id,
            'safety_email' => $request->safety_email,
            'safety_phone' => $request->safety_phone,
            'safety_street' => $request->safety_street,
            'safety_city' => $request->safety_city,
            'safety_zip_code' => $request->safety_zip_code,
            'safety_country_id' => $request->safety_country_id,
        ];
        app(MarketplaceProductService::class)->createProductSafetyInfo($productSafetyInfo);

        // Send notification to super admin
        $supplier = \App\DeliveryCompany::find($deliveryCompanyId);
        $url      = CRUDBooster::adminPath('marketplace_products');

        $message_title = 'Supplier: ' . $supplier->name . ' has imported a product.';
        User::find(71)->notify(new DRMNotification($message_title, 'PRODUCT IMPORTED', $url));
        User::find(\App\Enums\Apps::DROPMATIX_ID)->notify(new DRMNotification($message_title, 'PRODUCT IMPORTED', $url));

        \CRUDBooster::redirect(\CRUDBooster::adminPath('marketplace_products'), trans('Product Added Successfully'), 'success');
    }

    public function getEdit($id)
    {
        $this->cbLoader();
        if (!(CRUDBooster::isSuperAdmin() || CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()  || CRUDBooster::isSupplier())) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        $data['page_title'] = 'Marketplace Edit Products';
        $product            = MarketplaceProducts::find($id);
        $productCountyId    = $product->country_id;
        $countries          = \App\Country::whereNotIn('id', [2, 11])->get();

        $productCountry = $countries->filter(function ($value, $key) use ($productCountyId) {
            if ($value['id'] == $productCountyId) {
                return true;
            }
        })->first();

        $suppliers          = app(\App\Services\Marketplace\ProductService::class)->getDeliveryCompanies();
        $collections        =  Collection::select('id', 'name')->get();
        $categories         =  Category::select('id', 'name')->get();
        $stock              =  app(\App\Services\Marketplace\ProductService::class)->getProductStockInfo($product);

        return view('marketplace.product.edit_product', compact('data', 'product', 'collections', 'categories', 'countries', 'stock', 'productCountry'));
    }

    public function postEdit(Req $request, int $id)
    {
        if (!(CRUDBooster::isSuperAdmin() || CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport() || CRUDBooster::isSupplier())) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }
        // Apply validation rules
        Validator::make($request->all(), [
            "name" => "required",
            "ek_price"  => "required|numeric|min:1",
            'uvp' => 'required|required|min:1',
            'stock' => 'required',
            'image' => 'nullable|image',
            "category_id" => 'required',
        ]);

        $request->ek_price      = removeCommaFromPrice($request->ek_price);
        $request->uvp           = removeCommaFromPrice($request->uvp);
        $request->vat           = removeCommaFromPrice($request->vat);
        $request->shipping_cost = removeCommaFromPrice($request->shipping_cost);
        $request->vk_price      = removeCommaFromPrice($request->vk_price);
        $country                = \App\Country::find($request->state_id);

        $p = \App\Models\Marketplace\Product::find($id);
        $shippingMethod = $p->shipping_method;


        if($shippingMethod == ShippingMethod::DROPSHIPPING){
            Validator::make($request->all(), [
                'handling_time' => 'required',
                "shipping_cost"  => "required|numeric|min:1",
            ]);
        }

        if($shippingMethod == ShippingMethod::FULFILLment){
            $request->validate(['item_weight' => 'required']);
        }
        if (\CrudBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
            Validator::make($request->all(), [
                "vk_price"  => "required|numeric|min:1",
            ]);
        }

        if (isset($request->image)) {
            $images = [];
            foreach (array_unique($request->image) as $image) {
                $path = str_replace("https://drm-file.fra1.digitaloceanspaces.com/", "/", $image);
                if (Storage::disk('spaces')->exists('/' . $path)) {
                    $images[] = $image;
                }
            }
        }

        //ek_price update range checke max up 10%, max down 70%
        $updatedEkPrice = $request->ek_price;

        $blockProduct = false; //$this->productService->applyEkPriceViolationRule($p->id, $updatedEkPrice);

        if (\CRUDBooster::isSupplier()) {
            $request->vk_price = $p->vk_price;
        }

        if ($request->vk_price != $p->vk_price) {
            $p->update(['calculation_id' => null, 'vk_price' => $request->vk_price]);
        } elseif ($request->ek_price != $p->ek_price) {
            $this->updateAssignCalc($p->id);
        }
        $is_dublicate =  $p->is_dublicate;
        if ($is_dublicate) {
            $this->productService->updateProductWithChipestPrice($p->id, '', '', $request->ek_price, $request->shipping_cost);
        }
        if ($blockProduct) {
            $product = $this->productService->update($id, [
                'status' =>  ProductStatus::BLOCKED,
                'ek_price' => $request->ek_price,
            ]);
        } else {
            if ($request->industry_fields) {
                $industryTemplateData  = app(\App\Services\Marketplace\ProductService::class)
                    ->formIndustryTemplateDataFormat($p->category_id, $p->country_id, $request->industry_fields);
            }

            $updateAttributes = [
                'name' => $request->name,
                'ek_price' => $request->ek_price,
                'vk_price' => $request->vk_price,
                'uvp' => $request->uvp,
                'vat' => $request->vat,
                'stock' => $request->stock ?? $p->stock,
                'brand' => $request->brand,
                'item_color' => $request->item_color,
                'item_weight' => $request->item_weight,
                'item_size' => $request->item_size,
                'item_number' => $request->item_number,
                'category_id' => $request->category_id,
                'materials' => $request->materials,
                'note' => $request->note ?? '',
                'production_year' => $request->production_year,
                'tags' => $request->tags,
                'description' => $request->description,
                'category' => explode('->', $request->category),  //sub-category
                'delivery_days' => $request->handling_time,
                'shipping_cost' => $request->shipping_cost ?? $p->shipping_cost,
                'country_id' => $request->state_id ?? 1,
                'image' => (!empty($images)) ? $images : $p->image,
                'gender' => $request->gender,
                'industry_template_data' => $industryTemplateData,
            ];

            $product = $this->productService->update($id, $updateAttributes);
            $this->productService->stockUpdateToActiveBest($id, '', $request->stock ?? $p->stock);
        }

        if ($blockProduct) {
            if (\CRUDBooster::isSupplier()) {
                $supplier = User::find($p->supplier_id);
                $message_title = "Product Blocked: {$supplier->name} product has been blocked. <b>Supplier ID: {$supplier->id}</b>.<br/>To track the product plaese follow the below Link";
                $url = url('/admin/marketplace_products?q=' . $id);
                User::find(\App\Enums\Apps::DROPMATIX_ID)->notify(new DRMNotification($message_title, "MP PRODUCT BLOCKED", $url));
            }
            \CRUDBooster::redirect(\CRUDBooster::adminPath('marketplace_products'), trans('The product is blocked. Because you violate the base EK price update policy.<br/><i class="fa fa-check-circle"></i> You can increase max 10% of your base price.<br/><i class="fa fa-check-circle"></i> You can decrease max 70% of your base price.'), 'danger');
        } else {
            $this->syncUpdateWithDrmProduct($product);
            \CRUDBooster::redirect(\CRUDBooster::adminPath('marketplace_products'), trans('Product info updated Successfully'), 'success');
        }
    }

    public function syncUpdateWithDrmProduct($product)
    {
        $drmProducts         = DrmProduct::where('marketplace_product_id',$product->id)->get()->toArray();

        if($drmProducts){
        // if (Product::isExistInDrmProductList($product->id)) {
            $marketPlaceProduct = $product->toArray();
            // $drmProducts         = DrmProduct::where(['marketplace_product_id' => $product->id,])->get()->toArray();
            $updateableColumns = [];
            $product_sync_data = [];
            $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
            $lang = app('App\Services\UserService')->getProductLanguage($country_id);

            foreach ($drmProducts as $drmProduct) {
                $update_status = json_decode($drmProduct['update_status'], 1);
                foreach ($update_status as $k => $v) {
                    if ($update_status[$k] == 1 && $k != 'category') {
                        if ($marketPlaceProduct[$k] != $drmProduct[$k]) {
                            $updateableColumns[$k] = $marketPlaceProduct[$k];
                        }

                        if ($k == 'ek_price') {
                            $discount = Category::where('id', $marketPlaceProduct['category_id'])->where('is_offer_active', 1)->whereDate('start_date', '<=', date('Y-m-d'))->whereDate('end_date', '>=', date('Y-m-d'))->select('discount_percentage')->first()->discount_percentage ?? 0.0;
                            if( $marketPlaceProduct['offer_start_date'] <= now() && $marketPlaceProduct['offer_end_date'] >= now() && $marketPlaceProduct['is_offer_active'] == 1){
                                $discount += $marketPlaceProduct['discount_percentage'] ? ($marketPlaceProduct['discount_percentage'] - $marketPlaceProduct['discount_percentage']/4) : 0.0;
                            }
                            $updateableColumns['mp_category_offer'] = $discount;
                            $updateableColumns[$k] = userWiseVkPriceCalculate($marketPlaceProduct['vk_price'] ?? 0.0, $drmProduct['user_id'], false, $discount, $marketPlaceProduct['api_id'] ?? 0);
                        }

                        if ($k == 'industry_template_data') {
                            $updateableColumns[$k] = json_encode($marketPlaceProduct['industry_template_data']);
                        }

                        if ($k == 'vk_price') {
                            continue;
                        }

                        if ($k == 'uvp') {
                            $updateableColumns[$k] = userWiseUvpPriceCalculate($drmProduct['user_id'], $marketPlaceProduct['uvp'] ?? 0.0);
                        }

                        // if ($k == 'shipping_cost') {
                        //     $updateableColumns[$k] = round($marketPlaceProduct['shipping_cost'] * 1.10, 2);
                        // }

                        if ($k == 'title') {
                            $updateableColumns[$k] = [
                                array_keys($drmProduct[$k])[0] => $marketPlaceProduct['name'],
                            ];
                        }

                        if ($k == 'description') {
                            $updateableColumns[$k] = [
                                array_keys($drmProduct[$k])[0] => $marketPlaceProduct['description'],
                            ];
                        }

                        if ($k == 'stock') {
                            if ($marketPlaceProduct['shipping_method'] == 1) {
                                $updateableColumns[$k] = $marketPlaceProduct['stock'];
                            } else {
                                $updateableColumns[$k] = $marketPlaceProduct['internel_stock'];
                            }
                        }
                        if ($k == 'brand') {

                            if ($marketPlaceProduct['brand'] ) {
                                $mpBrandName = (is_numeric($marketPlaceProduct['brand']) ? $marketPlaceProduct['product_brand']['brand_name'] : $marketPlaceProduct['brand']) ?? '';
                                $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id',$drmProduct['user_id'])->first();
                                if ( $drmBrand ) {
                                    $drmBrand = $drmBrand;
                                } else {
                                    if(!empty($mpBrandName)){
                                        $drmBrand = DropmatixProductBrand::create([
                                            'brand_name' =>  $mpBrandName,
                                            'user_id' =>$drmProduct['user_id'],
                                            'brand_logo'=>($marketPlaceProduct['product_brand']['brand_logo'] ?? [])
                                        ]);
                                    }
                                }
                                $updateableColumns[$k] = $drmBrand->id;
                            }
                        }

                        if ($k == 'additional_eans') {
                            $updateableColumns[$k] = json_encode($marketPlaceProduct['additional_eans']);
                        }

                        if ($k == 'manufacturer') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['manufacturer'];
                        }

                        if ($k == 'manufacturer_link') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['manufacturer_link'];
                        }

                        if ($k == 'manufacturer_id') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['manufacturer_id'];
                        }

                        if ($k == 'custom_tariff_number') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['custom_tariff_number'];
                        }

                        if ($k == 'shipping_company_id') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['shipping_company_id'];
                        }

                        if ($k == 'region') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['region'];
                        }

                        if ($k == 'country_of_origin') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['country_of_origin'];
                        }

                        if ($k == 'min_stock') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['min_stock'];
                        }

                        if ($k == 'min_order') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['min_order'];
                        }

                        if ($k == 'gross_weight') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['gross_weight'];
                        }

                        if ($k == 'net_weight') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['net_weight'];
                        }

                        if ($k == 'product_length') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['product_length'];
                        }

                        if ($k == 'product_width') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['product_width'];
                        }

                        if ($k == 'product_height') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['product_height'];
                        }

                        if ($k == 'packaging_length') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['packaging_length'];
                        }

                        if ($k == 'packaging_width') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['packaging_width'];
                        }

                        if ($k == 'packaging_height') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['packaging_height'];
                        }

                        if ($k == 'item_unit') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['item_unit'];
                        }

                        if ($k == 'packaging_unit') {
                            $updateableColumns[$k] = $marketPlaceProduct['additional_info']['packing_unit'];
                        }

                        // * Category sync to drm by Job*
                        // if ( $k == 'category' ) {
                        //     $marketPlaceProductCategory = Category::find($marketPlaceProduct['category_id']);

                        //     if ( DrmCategory::where('category_name_'.$lang, $marketPlaceProductCategory->name)->where('user_id', $drmProduct['user_id'])->exists() ) {
                        //         $drmCategory = DrmCategory::where('category_name_'.$lang, $marketPlaceProductCategory->name)->where('user_id', $drmProduct['user_id'])->first();
                        //     } else {
                        //         $drmCategory = DrmCategory::create([
                        //             'category_name_'.$lang => $marketPlaceProductCategory->name,
                        //             'user_id'          => $drmProduct['user_id'],
                        //             'country_id'       => $country_id,
                        //         ]);
                        //     }
                        //     $updateableColumns[$k] = [$drmCategory->id];

                        //     if ( \App\Models\DRMProductCategory::where('product_id', $drmProduct['id'])->exists() ) {
                        //         \App\Models\DRMProductCategory::where('product_id', $drmProduct['id'])->first()->update([
                        //             'category_id' => $drmCategory->id,
                        //         ]);
                        //     } else {
                        //         \App\Models\DRMProductCategory::create([
                        //             'product_id'  => $drmProduct['id'],
                        //             'category_id' => $drmCategory->id,
                        //             'country_id'       => $country_id,
                        //         ]);
                        //     }
                        // }
                    } else if($update_status[$k] == 0 || $k == 'category') {
                        // Nothing updated
                    }

                    if (!isset($update_status['vat']) && !isset($update_status['tax_type'])) {
                        $updateableColumns['vat'] = $marketPlaceProduct['vat'];
                        $updateableColumns['tax_type'] = $marketPlaceProduct['tax_type'];
                    }

                    if (!isset($update_status['tags'])) {
                        $updateableColumns['tags'] = $marketPlaceProduct['tags'];
                    }

                    if(!isset($update_status['additional_eans'])){
                        $updateableColumns['additional_eans']       =    json_encode($marketPlaceProduct['additional_eans']);
                        $updateableColumns['manufacturer']          =    $marketPlaceProduct['additional_info']['manufacturer'];
                        $updateableColumns['manufacturer_link']     =    $marketPlaceProduct['additional_info']['manufacturer_link'];
                        $updateableColumns['manufacturer_id']       =    $marketPlaceProduct['additional_info']['manufacturer_id'];
                        $updateableColumns['custom_tariff_number']  =    $marketPlaceProduct['additional_info']['custom_tariff_number'];
                        $updateableColumns['shipping_company_id']   =    $marketPlaceProduct['additional_info']['shipping_company_id'];
                        $updateableColumns['region']                =    $marketPlaceProduct['additional_info']['region'];
                        $updateableColumns['country_of_origin']     =    $marketPlaceProduct['additional_info']['country_of_origin'];
                        $updateableColumns['min_stock']             =    $marketPlaceProduct['additional_info']['min_stock'];
                        $updateableColumns['min_order']             =    $marketPlaceProduct['additional_info']['min_order'];
                        $updateableColumns['gross_weight']          =    $marketPlaceProduct['additional_info']['gross_weight'];
                        $updateableColumns['net_weight']            =    $marketPlaceProduct['additional_info']['net_weight'];
                        $updateableColumns['product_length']        =    $marketPlaceProduct['additional_info']['product_length'];
                        $updateableColumns['product_width']         =    $marketPlaceProduct['additional_info']['product_width'];
                        $updateableColumns['product_height']        =    $marketPlaceProduct['additional_info']['product_height'];
                        $updateableColumns['packaging_length']      =    $marketPlaceProduct['additional_info']['packaging_length'];
                        $updateableColumns['packaging_width']       =    $marketPlaceProduct['additional_info']['packaging_width'];
                        $updateableColumns['packaging_height']      =    $marketPlaceProduct['additional_info']['packaging_height'];
                        $updateableColumns['item_unit']             =    $marketPlaceProduct['additional_info']['item_unit'];
                        $updateableColumns['packaging_unit']        =    $marketPlaceProduct['additional_info']['packing_unit'];
                    }

                    if(!isset($update_status['brand']) ){
                        if ($marketPlaceProduct['brand']) {
                            $mpBrandName = (is_numeric($marketPlaceProduct['brand']) ? $marketPlaceProduct['product_brand']['brand_name'] : $marketPlaceProduct['brand']) ?? '';
                            $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id',$drmProduct['user_id'])->first();
                            if ( $drmBrand ) {
                                $drmBrand = $drmBrand;
                            } else {
                                if(!empty($mpBrandName)){
                                    $drmBrand = DropmatixProductBrand::create([
                                        'brand_name' =>  $mpBrandName,
                                        'user_id' =>$drmProduct['user_id'],
                                        'brand_logo'=>($marketPlaceProduct['product_brand']['brand_logo'] ?? [])
                                    ]);
                                }
                            }
                            $updateableColumns[$k] = $drmBrand->id;
                        }
                    }
                }
                
                if( count($updateableColumns) > 0 ){
                    
                    app(\App\Services\DRMProductService::class)->update($drmProduct['id'], $updateableColumns, $lang);

                    if(in_array($drmProduct['user_id'], [62, 71,212,3878,2454,3675,3987])){

                        $v2Update = $updateableColumns;
                        if (isset($v2Update['title']['de'])) {
                            $v2Update['title'] = $v2Update['title']['de']; // Replace with the actual value
                        }
                    
                        if (isset($v2Update['description']['de'])) {
                            $v2Update['description'] = $v2Update['description']['de']; // Replace with the actual value
                        }
                        
                        $product_sync_data[] = [
                            'marketplace_product_id' => $product->id,
                            'user_id'  => $drmProduct['user_id'],
                            'country_id' => $product->country_id,
                            'metadata' => json_encode($v2Update),
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    
                    }
                    
                    $v2Update = [];
                    $updateableColumns = [];
                }
            }

            if(!blank($product_sync_data)) {
                DB::connection('marketplace')->table('mp_product_sync_histories')->insert($product_sync_data);
            }

            return $drmProducts;
        }
    }

    public function postSaveImage(Req $request): JsonResponse
    {
        try {
            if ($request->hasFile('file')) {
                $image = $request->file('file');
                $image_type = strtolower($image->getClientOriginalExtension());
                if ($image_type == "jpg" || $image_type == "png" || $image_type == "jpeg" || $image_type == "gif") {
                    $image_url = uploadImage($image, 'marketplace-products/' . CRUDBooster::myParentId());

                    return response()->json(['success' => true, 'name' => $image_url], 200);
                }
            }
        } catch (Exception $e) {
            return response()->json(['success' => false], 422);
        }
        return response()->json(['success' => true]);
    }

    public function postDeleteImages(Req $request): JsonResponse
    {

        $this->postDeleteSourceImage($request->file_name);
        return response()->json(['success' => true]);

        if (!$this->canAccess('edit')) {
            return response()->json(['success' => false], 401);
        }
        $lang = $this->getLanguage();
        try {
            $product = $this->productService->getById($request->product);
            $this->productService->update($request->product, ['image' => array_filter(array_diff($product->image, [$request->image]))], $lang, "manual");

            return response()->json(['success' => true]);
        } catch (Exception $ex) {
            return response()->json(['success' => false], 422);
        }
    }

    public function postUpdateImage(Req $request): JsonResponse
    {

        // if (!$this->canAccess('edit')) {
        //     return response()->json(['success' => false], 401);
        // }
        // $lang = $this->getLanguage();
        $id = $request->product_id;
        $images = $request->imagens;
        if (!is_array($images)) {
            $images = preg_replace("/\r|\n/", "", $images);
            $images = array_unique(array_values(json_decode($images, true)));
        }
        $this->productService->update($id, ['image' => array_filter($images)]);
        return response()->json(['success' => true]);
    }

    public function postDeleteSourceImage($path)
    {
        $path = str_replace("https://drm-file.fra1.digitaloceanspaces.com/", "/", $path);
        if ($path && Storage::disk('spaces')->exists('/' . $path)) {
            Storage::disk('spaces')->delete('/' . $path);
        }
    }

    public function getAcceptOrReject($productId)
    {
        $country_id = Session::get('filter_country_' . CRUDBooster::myParentId()) ?? 1;
        $product = Product::with('mainCategory:id,minimum_price')->where('country_id', $country_id)->findOrFail($productId);
        try {
            $is_product_dublicate_live = Product::where('ean', $product->ean ?? '')->where('country_id', $country_id)->where('is_dublicate', 1)->where('best_price', 1)->first();
            $is_product_dublicate = Product::where('ean', $product->ean ?? '')->where('country_id', $country_id)->get();
            if ($is_product_dublicate_live != null && $_REQUEST['updating_status'] == 1) {
                $this->productService->otherCountryDuplicateStatusChange([$product->ean],  $_REQUEST['updating_status']);
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'You Can not Live This Product Dublicate Ean another Product is Live', 'info');
            } else if ($_REQUEST['updating_status'] == 1 && count($is_product_dublicate ?? []) > 1) {
                $this->productService->dublicateBestOneApproved($product->id, $_REQUEST['updating_status']);
                $this->productService->otherCountryDuplicateStatusChange([$product->ean],  $_REQUEST['updating_status']);
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Status Changed successfully.', 'info');
            } else if ($_REQUEST['updating_status'] == 1 && ($product->mainCategory->minimum_price ?? 0) < $product->ek_price) {
                $product->update([
                    'status' => $_REQUEST['updating_status'],
                ]);
                $this->productService->otherCountryDuplicateStatusChange([$product->ean],  $_REQUEST['updating_status']);
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Status Changed successfully.', 'info');
            } else if ($_REQUEST['updating_status'] != 1) {
                $product->update([
                    'status' => $_REQUEST['updating_status'],
                ]);

                // Rejected products email send off.....

                // if ($_REQUEST['updating_status'] == 2) {
                //     $slug = 'rejected_product_email';
                //     if ($product->delivery_company_id > 0) {
                //         $delivery_company = DeliveryCompany::find($product->delivery_company_id);

                //         $tags = [
                //             'name' => $delivery_company->name,
                //             'ean' =>  $product->ean,
                //         ];
                //         if ($delivery_company->email != '<EMAIL>') {
                //              $mail_data = DRMParseMailTemplate($tags, $slug);
                //              app('drm.mailer')->getMailer()->to($delivery_company->email)->send(new DRMSEndMail($mail_data)); //Send
                //         }
                //     } else {
                //         $supplier_email = User::where('id', $product->supplier_id)->first();
                //         $tags = [
                //             'name' => $supplier_email->name,
                //             'ean' =>  $product->ean,
                //         ];
                //         if ($supplier_email->email != '<EMAIL>') {
                //             $mail_data = DRMParseMailTemplate($tags, $slug);
                //             app('drm.mailer')->getMailer()->to($supplier_email->email)->send(new DRMSEndMail($mail_data)); //Send
                //         }
                //     }
                // }

                // if ($product->is_dublicate == 1) {
                //     $this->productService->dublicateBestOneApproved($product->id, $_REQUEST['updating_status']);
                // }
                // $this->productService->revokedProductDelete($product->id);
                $this->productService->otherCountryDuplicateStatusChange([$product->ean],  $_REQUEST['updating_status']);
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Status Changed successfully.', 'info');
            } else {
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Status not Change. Ek Price mustbe be greater than category minimum price', 'error');
            }
        } catch (Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'error');
        }
    }

    public function postStoreImageUrl(Req $request): JsonResponse
    {
        try {
            $url = $request->url;

            $path = 'marketplace-products/' . CRUDBooster::myParentId();

            $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
            $randomStr = substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', mt_rand(1, 5))), 1, 40);

            if (!empty($extension)) {
                $fileName = $path . '/' . $randomStr . "." . $extension;
            } else {
                $fileName = $path . '/' . $randomStr;
            }
            $fileContent = file_get_contents($url);

            Storage::disk('spaces')->put($fileName, $fileContent, 'public');
            if (Storage::disk('spaces')->exists($fileName)) {
                $images_url = Storage::disk('spaces')->url($fileName);
            } else {
                $images_url = null;
            }

            if (!empty($images_url)) {
                return response()->json([
                    'name' => $images_url,
                    'serverFileName' => $randomStr,
                    'success' => true,
                    'message' => 'Image upload successfully!',
                ]);
            } else {
                return response()->json(['success' => false, 'message' => 'Image not uploaded!'], 422);
            }
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 422);
        }
    }

    public function getCalculations(Req $request)
    {
        $calculations = $this->getUserCalculations(CRUDBooster::myParentId());
        return view('marketplace.product.modals.assign_calculation', compact('calculations'));
    }

    public function getUserCalculations($user_id)
    {
        return MarketplaceProfitCalculation::where([
            'user_id' => $user_id,
        ])->get();
    }

    public function postCreateCalculation(Request $request)
    {
        $data['calculations'] = $this->getUserCalculations(CRUDBooster::myParentId());
        return view('marketplace.product.modals.assign_calculation', $data);
    }

    /**
     * @param Request $request
     * @return Response
     */
    public function postAddCalculation(Req $request): Response
    {
        // if(!$this->canAccess('calculation')){
        //     return new Response(["You do not have permissions !"],401);
        // }
        $user_id = CRUDBooster::myParentId();
        $validatedData = $this->validateCalculation($request);

        try {
            $calculationData = $this->createCalculation($validatedData, $user_id);
        } catch (Throwable $th) {
            return new Response([$th->getMessage()], 400);
        }
        return new Response([
            'status' => "success",
            'data'   => $calculationData['calculation'],
            'dupplicated' => $calculationData['duplicated'],
        ]);
    }

    public function createCalculation($data, $user_id)
    {
        $dynamicShippingCost = $data['dynamic_shipping_cost'] == 'on' ? 1 : 0;
        $roundScale = (float)$data['round_scale'] ?? 0;
        $existing = MarketplaceProfitCalculation::where([
            'user_id' => $user_id,
            'additional_charge' => (float)$data['additional_charge'] ?? 0,
            'profit_percent' => (float)$data['profit_percent'] ?? 0,
            'shipping_cost' => (float)$data['shipping_cost'] ?? 0,
            'round_scale' => $roundScale,
            'uvp' => $data['uvp'] ?? false,
            'dynamic_shipping_cost' => $dynamicShippingCost,
        ])->first();

        if (empty($existing)) {
            $calculation = MarketplaceProfitCalculation::create([
                'name' => $data['name'],
                'user_id' => $user_id,
                'additional_charge' => (float)$data['additional_charge'] ?? 0,
                'profit_percent' => (float)$data['profit_percent'] ?? 0,
                'shipping_cost' => (float)$data['shipping_cost'] ?? 0,
                'round_scale' => $roundScale,
                'dynamic_shipping_cost' => $dynamicShippingCost,
                'uvp' => $data['uvp'] ?? false
            ]);
            return [
                'duplicated' => false,
                'calculation' => $calculation,
            ];
        } else {
            return [
                'duplicated' => true,
                'calculation' => $existing,
            ];
        }
    }

    public function postUpdateCalculation(Req $request): Response
    {
        // if(!$this->canAccess('calculation')){
        //     return new Response(["You do not have permissions !"],401);
        // }
        $user_id = CRUDBooster::myParentId();
        $id = $request->calculation_id;

        $validatedData = $this->validateCalculation($request);
        if ($request->dynamic_shipping_cost) $validatedData['dynamic_shipping_cost'] = 1;
        else  $validatedData['dynamic_shipping_cost'] = 0;

        try {
            $calculation = $this->updateCalculation($id, $validatedData, $user_id);
        } catch (Throwable $th) {
            return new Response([$th->getMessage()], 400);
        }
        return new Response([
            'status' => "success",
            'data'   => $calculation
        ], 200);
    }


    public function updateCalculation($id, $data, $user_id)
    {
        $calculation = MarketplaceProfitCalculation::where([
            'id' => $id,
            'user_id' => $user_id,
        ])->first();

        $calculation->fill($data);
        $calculation->save();
        return $calculation;
    }

    /**
     * @param Request $request
     * @return Response
     */
    public function getCheckCalculationAssign(Req $request): Response
    {
        $user_id = CRUDBooster::myParentId();
        try {
            $count = $this->checkCalculationAssign($request->calculation_id, $user_id);
        } catch (Throwable $th) {
            return new Response([$th->getMessage()], 400);
        }
        return new Response([
            'success' => true,
            'data'    => [
                'count' => $count
            ]
        ], 200);
    }

    public function checkCalculationAssign($id, $user_id)
    {
        return MarketplaceProducts::where([
            'calculation_id' => $id
        ])->count();
    }


    /**
     * @param Request $request
     * @return Response
     */
    public function postDeleteCalculation(Req $request): Response
    {
        // if(!$this->canAccess('calculation')){
        //     return new Response(["You do not have permissions !"],401);
        // }

        $user_id = CRUDBooster::myParentId();
        $id = $request->calculation_id;
        try {
            $this->deleteCalculation($id, $user_id);
        } catch (Throwable $th) {
            return new Response([$th->getMessage()], 400);
        }
        return new Response([
            'success' => true,
        ], 200);
    }

    public function deleteCalculation($id, $user_id)
    {
        return MarketplaceProfitCalculation::where([
            'id' => $id,
            'user_id' => $user_id,
        ])->delete();
    }

    public function getCheckCalculatedProducts(Req $request): Response
    {
        // $user_id = CRUDBooster::myParentId();
        try {
            if ($request->only_products && $request->only_products == 1) {
                $products = $this->getIndex();
                $productIds = $products->pluck('id')->toArray();
                $productIds = array_diff($productIds, $request->product_ids ?? []);
                $count = $this->checkOverwriteProductCount($productIds);
            } else {
                $count = count($this->calculatedProducts($request->product_ids));
            }
        } catch (Throwable $th) {
            return new Response([$th->getMessage()], 400);
        }
        return new Response([
            'success' => true,
            'data'    => [
                'count' => $count
            ]
        ], 200);
    }

    private function checkOverwriteProductCount($productIds)
    {

        $results = 0;
        foreach (array_chunk($productIds,5000) as $chunk) {
            $results += MarketplaceProducts::select('id','vk_price')->whereIn('id', $chunk)
                        ->where(function ($q) {
                            $q->whereNotNull('vk_price')
                            ->orWhere('vk_price', '<>', 0);
                        })->count();
        }

        return $results;
    }

    public function postAssignCalculation(Req $request)
    {
        // if(!$this->canAccess('calculation')){
        //     return response()->json(['success' => false], 401);
        // }
        // $user_id = CRUDBooster::myParentId();
        $ids = [];
        if ($request->only_products && $request->only_products == 1) {
            $products = $this->getIndex();
            $ids = $products->pluck('id')->toArray();
            $ids = array_diff($ids, $request->product_ids ?? []);
        } else {
            $ids = $request->product_ids;
        }
        // $channel = $request->channel;
        $calculation = $request->calculation;

        if ((int)$request->overwrite == 0) {
            $skipped_products = $this->calculatedProducts($ids);
            $skipped_ids = $skipped_products->pluck('id')->toArray();
            $ids = array_diff($ids, $skipped_ids);
        }
        $res = $this->assignCalc($ids, $calculation);

        return response()->json(['success' => $res['message']], $res ? 200 : 422);
    }

    public function assignCalc($ids, $calculation_id)
    {
        try {
            $calculation = MarketplaceProfitCalculation::find($calculation_id);
            $total_products = count($ids);
            if($total_products > 50){
                foreach(array_chunk($ids, 250) as $product_ids){
                    // dispatch(new MarketplaceProductCalculationJob($product_ids, $calculation));

                    $channelProducts = MarketplaceProducts::whereIn('id', $product_ids)->get();

                    foreach ($channelProducts as $channelProduct) {
                        $price = $this->calculatePrice($channelProduct->ek_price, $calculation, $channelProduct->uvp, $channelProduct->shipping_cost);

                        if ($price > 0) {
                            $data = [
                                'vk_price' => $price,
                                'old_vk_price' => $channelProduct->vk_price,
                                'calculation_id' => $calculation->id,
                            ];

                            $this->update($channelProduct->id, $data);
                        }
                    }

                }

                return ['message' => 'The calculation for your selected ' .$total_products.' products is being added to the background process.'];
            }else{

                $channelProducts = MarketplaceProducts::whereIn('id', $ids)->get();

                foreach ($channelProducts as $channelProduct) {
                    $price = $this->calculatePrice($channelProduct->ek_price, $calculation, $channelProduct->uvp, $channelProduct->shipping_cost);

                    if ($price > 0) {
                        $data = [
                            'vk_price' => $price,
                            'old_vk_price' => $channelProduct->vk_price,
                            'calculation_id' => $calculation->id,
                        ];

                        $this->update($channelProduct->id, $data);
                    }
                }
                return ['message' => 'Products Calculation Added Successfully'];
            }

        } catch (\Throwable $th) {
            return false;
        }
    }

    public function updateAssignCalc($id)
    {
        try {
            $channelProduct = MarketplaceProducts::where('id', $id)->first();
            if ($channelProduct->calculation_id) {
                $calculation = MarketplaceProfitCalculation::find($channelProduct->calculation_id);
                $price = $this->calculatePrice($channelProduct->ek_price, $calculation, $channelProduct->uvp, $channelProduct->shipping_cost);
                if ($price > 0) {
                    $data = [
                        'vk_price' => $price,
                        'old_vk_price' => $channelProduct->vk_price,
                        'calculation_id' => $calculation->id,
                    ];
                    $this->update($channelProduct->id, $data);
                }
                return true;
            }
            return false;
        } catch (\Throwable $th) {
            return false;
        }
    }


    public function update($id, array $data)
    {
        $product = $this->saveProduct($data, $id);
        return $product;
    }

    private function saveProduct($data, $id = null)
    {
        // $channelProduct = MarketplaceProducts::findOrNew($id);
        // $channelProduct->fill($data);
        $channelProduct = \App\Models\Marketplace\Product::with('core_products')->where('id', $id)->first();
        
        if (!empty($data['vk_price'])) {
            $channelProduct->calculation_id = $data['calculation_id'] ?? null;
            $channelProduct->vk_price = $data['vk_price'] ?? null;
            $channelProduct->old_vk_price = $data['old_vk_price'] ?? null;
            $channelProduct->vk_price_updated_at  = \Carbon\Carbon::now();
        }

        $channelProduct->save();
        
        // Update DRM vk_price
        if(!empty($channelProduct->core_products)){
            $this->calculatedPriceSyncToDrm($channelProduct->core_products, $channelProduct->vk_price);
        }

        return $channelProduct;
    }

    public function calculatedPriceSyncToDrm($products, $price)
    {
        $mp_vk_price = $price;
        foreach($products as $product){
    
            $mp_price_markup_discount = !blank($product->mp_price_markup) ? ($product->mp_price_markup * $mp_vk_price) / 100 : 0;
            $cat_offer_discount = !blank($product->mp_category_offer) ? ($product->mp_category_offer * $mp_vk_price) / 100 : 0;
    
            $mp_new_vk_price = round($mp_vk_price + $mp_price_markup_discount - $cat_offer_discount, 2);
    
            if (round($product->ek_price, 2) != $mp_new_vk_price) {
                $updateableColumns['ek_price'] = $mp_new_vk_price;
                app(\App\Services\DRMProductService::class)->update($product->id, $updateableColumns, 'de');
            }
        }
    }

    public function calculatePrice($price, $calculation, $uvp = 0, $shipping_cost = 0)
    {
        if ($calculation->dynamic_shipping_cost && $shipping_cost > 0) {
            $calculation_shipping_cost = $shipping_cost;
        } else {
            $calculation_shipping_cost = $calculation->shipping_cost;
        }
        Log::channel('uscreen')->info($calculation_shipping_cost);
        try {
            if ($calculation->uvp) {
                $price = $uvp;
            } else {
                $price = $price + $price
                    * ($calculation->profit_percent / 100)
                    + $calculation_shipping_cost
                    + $calculation->additional_charge;

                if ($calculation->round_scale != null) {
                    $prices = explode('.', $price);
                    if ($prices[1] != 0) {
                        $price = $prices[0] + $calculation->round_scale;
                    }
                }
            }
            return (float)str_replace(',', '', number_format($price, 2));
        } catch (\Throwable $th) {
            return $price;
        }
    }

    public function calculatedProducts($ids)
    {
        return MarketplaceProducts::whereIn('id', $ids)
            ->where(function ($q) {
                $q->whereNotNull('vk_price');
                $q->orWhere('vk_price', '<>', 0);
                return $q;
            })->get();
    }

    public function getProfitDetails(Req $request): string
    {
        $product = $this->getById($request->id);
        $profit_margin = MarketplaceProfitCalculation::find($product->calculation_id);
        $shipping_cost = ($profit_margin->dynamic_shipping_cost && $product->shipping_cost > 0) ? $product->shipping_cost : $profit_margin->shipping_cost;
        if (!empty($profit_margin)) {
            $profit = (($product->vk_price - $product->ek_price) / $product->ek_price * 100);
            $calculation = $product->ek_price + $product->ek_price * (floatval($profit_margin->profit_percent) / 100);
            $calc2 = $shipping_cost + floatval($profit_margin->additional_charge) + $calculation;
            $round = ($profit_margin->round_scale) ? " + " . $profit_margin->round_scale . " = " . $product->vk_price : "";
            $round_html = $profit_margin->round_scale ? $profit_margin->round_scale : 'N/A';
            $calculation_html = $product->ek_price . " + " . $product->ek_price * (floatval($profit_margin->profit_percent) / 100) . " (" . $profit_margin->profit_percent . "%) = " .
                $calculation . " + (" . $shipping_cost . " + " . floatval($profit_margin->additional_charge) . ") = " . $calc2 . $round . "  €";
            $round_html = $profit_margin->round_scale ? $profit_margin->round_scale : 'N/A';
            $html = "
                Calculation name : " . $profit_margin->name . "
                <a href='javascript:;' onclick='
                    openUpdateCalc(" . json_encode($profit_margin) . ")
                ' style='color:#fd6500'>
                <i class='fa fa-edit'></i> Update</a></br></br>

                Profit percent : " . $profit_margin->profit_percent . "% </br></br>
                Shipping cost : " . $shipping_cost . " €</br></br>
                Additional charge : " . $profit_margin->additional_charge . " €</br></br>
                Round scale : " . $round_html . "</br></br>
                Total : " . number_format($profit, 2) . "% Profit</br></br>
                Calculation: " . $calculation_html . "</br></br>";
        } else {
            $profit = \str_replace('%', "", $product->profit);
            $profit = (float)$profit;
            $calculation_html = "(" . $product->ek_price . " * " . $profit . " / 100) = " . round(($product->ek_price * $profit / 100), 2) . "<br><br>";
            $calculation_html .= $product->ek_price . " + " . round(($product->ek_price * $profit / 100), 2) . " = " . $product->vk_price . " €";

            $price_source = $product->price_droptienda ? "Droptienda" : "Manual";
            $html = "
                Price source : <em>" . $price_source . "</em></br></br>
                Profit : " . $product->profit . "</br></br>
                Calculation: " . $calculation_html . "</br></br>
            ";
        }

        return $html;
    }

    private function validateCalculation($request): array
    {
        if ($request->uvp) {
            $validatedData = [
                'name' => "UVP",
                'uvp'  => true
            ];
        } else {
            $validatedData = $request->validate([
                'name' => ['required', 'string', 'max:255'],
                'additional_charge' => ['nullable'],
                'profit_percent' => ['required', 'numeric'],
                'shipping_cost' => ['nullable'],
                'round_scale'   => ['numeric', 'nullable'],
                'dynamic_shipping_cost' => ['nullable'],
            ]);
            $validatedData['uvp'] = false;
        }
        return $validatedData;
    }

    public function getById($id)
    {
        return MarketplaceProducts::find($id);
    }

    public function postDupplicateEans()
    {
        $ean            = request()->ean;
        $supplierId     = request()->supplier_id;

        if (\CrudBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
            $dupplicatedProducts = \App\Models\Marketplace\Product::where('ean', $ean)
                ->where('supplier_id', '<>', $supplierId)
                ->get();
            if (count($dupplicatedProducts) == 0) {
                return 'no_dupplicates';
            } else {
                return $dupplicatedProducts;
            }
        }
    }


    public function postCountStatusUnchangeableProducts()
    {
        $statusUnchangeAbleProducts = \App\Models\Marketplace\Product::whereIn('id', request()->ids)
            ->where('vk_price', 0)
            ->get();
        return response()->json($statusUnchangeAbleProducts);
    }

    public function getProductDeliveryCompany($productId)
    {
        $product = Product::find($productId);

        if ($product->delivery_company_id) {
            return \App\DeliveryCompany::find($product->delivery_company_id)->name;
        } elseif ($product->supplier_id) {
            return \App\User::where('id', $product->supplier_id)->first()->name ?? '-';
        } else {
            return '-';
        }
    }

    public function getProductBrand($brand_id)
    {
        $brand_name = \App\Models\Marketplace\ProductBrand::select('id' ,'brand_name')->find($brand_id)->brand_name ?? 'NO BRAND';     
        return "<span style='display:flex;gap:3px;'>{$brand_name}</span>";
       
    }

    public function CompareUVP($productId)
    {
        //return $productId;
        $keepaproduct = KeepaProductPrice::where('ean',$productId)->first();
        $product = Product::where('ean',$productId)->latest()->first();

         if( !empty($keepaproduct) && $keepaproduct->price!=0){
            if($keepaproduct->price > $product->uvp){
                return "<span class='market_p_editable ' data-name='uvp' data-pk='{$this->product->id}'>{$product->uvp}</span><br/><span style='color:green;font-weight:bold;cursor:pointer' class='glyphicon glyphicon-triangle-top'></span>";
            }else{
                return "<span class='market_p_editable ' data-name='uvp' data-pk='{$this->product->id}'>{$product->uvp}</span><br/><span style='color:red;font-weight:bold;cursor:pointer' class='glyphicon glyphicon-triangle-bottom'></span>";
            }
         }else{
           return $product->uvp;
         }


    }



    public function getProductCloneModal()
    {
        $productId = request()->id;

        $product   = \App\Models\Marketplace\Product::find($productId);
        $suppliers = app(\App\Services\Marketplace\ProductService::class)->getDeliveryCompaniesForFilterInProducts();
        $brands = DB::connection('marketplace')->table('marketplace_product_brand')->get();
        $categories = Category::where('is_active', 1)->orderBy('name', 'asc')->get();

        return view('marketplace.product.modals.product_clone_form_modal', compact('product', 'suppliers', 'brands', 'categories'));
    }

    public function postCloneProductSave()
    {
        $request = request();

        $productImages = explode(" ",request()->product_images);
        // ek price Validation
        if (floatval($request->ek_price) < 1) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Invalid ek price.", 'warning');
        }

        $data = $request->only([
            'name',
            'ean',
            'item_number',
            'item_size',
            'item_color',
            'brand',
            'item_weight',
            'gender',
            'delivery_days',
            'note',
            'production_year',
            'materials',
            'ek_price',
            'uvp',
            'stock',
            'description',
            'category_id',
            'atw'
        ]);
        $othersData = [];

        //Determining product delivery company and supplier id
        if (\CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
            $othersData['vk_price']             = $request->vk_price ?? 0.00;
            $othersData['delivery_company_id']  = intval($request->delivery_company_id);

            $supplier = \App\DeliveryCompany::find($othersData['delivery_company_id']);
            $othersData['supplier_id']          = ($supplier->is_marketplace_supplier && $supplier->supplier_id) ? $supplier->supplier_id : null;
        } else {
            $othersData['vk_price']             = 0.00;

            $deliveryCompany = \App\DeliveryCompany::where('supplier_id', \CRUDBooster::myParentId());

            if ($deliveryCompany->exists()) {
                $othersData['delivery_company_id'] = $deliveryCompany->first()->id;
                $othersData['supplier_id'] = \CRUDBooster::myParentId();
            }
        }

        //Determine dupplication
        $isDuplicated = \App\Models\Marketplace\Product::where('ean', $request->ean)->exists();
        if ($isDuplicated) {
            $othersData['is_dublicate'] = 1;
        }

        $othersData['name']            = $request->name;
        $othersData['shipping_method'] = $request->shipping_method;
        $othersData['shipping_cost']   = $request->shipping_cost;

        if ( $request->shipping_method == ShippingMethod::DROPSHIPPING ) {
            $othersData['delivery_days'] = $request->delivery_days;
        }

        if(!empty($productImages)){
            $othersData['image'] = upload_url_image($productImages);
        }

        $row = array_merge($data, $othersData);

        $productCreated        = \App\Models\Marketplace\Product::create($row);
        $additionalInfo = [
            'product_id'  => $productCreated->id,
            'product_length'=> $request->item_length,
            'product_width' => $request->item_width,
            'product_height'=> $request->item_height,
            'volume' => $request->item_volume > 0 ? number_format($request->item_volume,6) : '',
        ];

        $productCreated->additionalInfo()->updateOrCreate(['product_id' => $productCreated->id ],$additionalInfo);

        $supplier = \App\User::find($othersData['supplier_id']);
        $message_title = 'Supplier: ' . $supplier->name . ' has cloned a product.';

        $url      = CRUDBooster::adminPath('marketplace_products');
        User::find(71)->notify(new DRMNotification($message_title, 'PRODUCT CLONED', $url));
        User::find(\App\Enums\Apps::DROPMATIX_ID)->notify(new DRMNotification($message_title, 'PRODUCT CLONED', $url));

        if ($productCreated) {
            if($productCreated->stock > 0 && $productCreated->shipping_method == ShippingMethod::FULFILLment ){
                $productCreated->update(['status' => ProductStatus::ACTIVE]);
                $this->actionButtonSelected([$productCreated->id], 'sync_pulsh_product');
            }
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Product cloned and added successfully", 'success');
            return redirect()->back();
        }
    }


    public function getFilterByContents()
    {
        $listHtml = "";

        try {

            // For category filter
            if (request()->filter_by == 'category_id') {
                // $marketplaceCategories = \App\Models\Marketplace\Category::where('is_active', 1)
                //     ->orderBy('name', 'asc')
                //     ->get();
                $mainCategory = Session::get('filter_category_is_main_'. CRUDBooster::myParentId());
                $subCategory = Session::get('filter_category_is_sub_'. CRUDBooster::myParentId());
                $parent_categories = MarketplaceParentCategory::withCount('category')->where('is_active',1)->orderBy('name')->get(['id','name']);
                 $listHtml .="<option value=''>Select Category</option>";
                 foreach ($parent_categories as $parent_category ){
                 if($parent_category->category_count > 0){

                    if(\CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()){
                        $listHtml .= "<optgroup label='Main Category'>";
                        $mainSelected = $mainCategory == $parent_category->id ? 'selected' : '';
                        $listHtml .= "<option $mainSelected value='" .'main_cat_'.$parent_category->id . "'> $parent_category->name</option>";
                        $listHtml .= "</optgroup>";
                        $listHtml .= "<optgroup label='Sub-category ( ". $parent_category->category->count()." )'>";
                        foreach ($parent_category->category as $category) {
                            $subSelected = $subCategory == $category->id ? 'selected' : '';
                            $listHtml .= "<option $subSelected value='" . $category->id . "'> $category->name</option>";
                        }
                        $listHtml .= "</optgroup>";
                    }else{
                        $listHtml .= "<optgroup label='".$parent_category->name."'>";
                        foreach($parent_category->category as $category){
                          $s = request()->selected_value == $category->id ? 'selected' : '';
                          $listHtml .= "<option $s value='".$category->id."'> $category->name</option>";
                        }
                        $listHtml .= "</optgroup>";
                    }
                 }
                }
                // $listHtml .= "<option value=''>Select Category</option>";
                // foreach ($marketplaceCategories as $mc) {
                //     $s = request()->selected_value == $mc->id ? 'selected' : '';
                //     $listHtml .= "<option $s value='" . $mc->id . "'>" . $mc->name . "</option>";
                // }
            }

            // For Api category filter
            if (request()->filter_by == 'api_category_id') {
                $api_ids = ApiCategory::orderBy('api_category_name', 'asc')->get()->groupBy('api_id');
                $listHtml .="<option value=''>Select Api Category</option>";

                foreach ($api_ids as $key => $api_id ){
                    $listHtml .= "<optgroup label='".\App\Enums\Marketplace\ApiCategoryName::getApiNameById($key)."'>";
                    foreach($api_id as $category){
                            $s = request()->selected_value == $category->api_category_id ? 'selected' : '';
                            $listHtml .= "<option $s name='".$key."' value='".$category->api_category_id."'> $category->api_category_name</option>";
                        }
                    $listHtml .= "</optgroup>";
                }
            }

            // For Delivery company / Supplier filter
            if (request()->filter_by == 'delivery_company_id') {
                $deliveryCompanies = app(\App\Services\Marketplace\ProductService::class)
                    ->getDeliveryCompaniesForFilterInProducts();
                foreach ($deliveryCompanies as $dc) {
                    $d = request()->selected_value == $dc->id ? 'selected' : '';
                    if(empty($dc->supplier_id)){
                        $listHtml .= "<option $d value='" . $dc->id . "'>" . $dc->name . "</option>";
                    }else{
                        $billing_details = BillingDetail::select('company_name')->where('user_id', $dc->supplier_id)->first();
                        if(isset($billing_details->company_name)){
                            $listHtml .= "<option $d value='" . $dc->id . "'>" . $billing_details->company_name . "</option>";
                        }else{
                            $listHtml .= "<option $d value='" . $dc->id . "'style='background-color:red'>" . $dc->supplier_id . "</option>";
                        }
                    }
                }
            }

            if (request()->filter_by == 'shipping_method') {
                $selected = request()->selected_value;
                $listHtml .= "<option value='1' " . ($selected == 1 ? "selected" : null) . ">DropShipping</option>";
                $listHtml .= "<option value='2' " . ($selected == 2 ? "selected" : null) . ">Fulfillment</option>";
            }

            if (request()->filter_by == 'visibility_light') {
                $selected = request()->selected_value;
                $listHtml .= "<option value='red' " . ($selected == 'red' ? "selected" : null) . ">Red</option>";
                $listHtml .= "<option value='yellow' " . ($selected == 'yellow' ? "selected" : null) . ">Yellow</option>";
                $listHtml .= "<option value='green' " . ($selected == 'green' ? "selected" : null) . ">Green</option>";
            }

            if (request()->filter_by == 'delivery_country') {
                $countries = \App\Country::where(['is_active' => 1, 'status' => 1])->get();
                $listHtml .= "<option value=''>Select Country</option>";

                foreach ($countries as $country) {
                    $selected = session('filter_by_input') == $country->id ? 'selected' : '';
                    $listHtml .= "<option $selected value='" . $country->id . "'>" . $country->name . "</option>";
                }
            }
            return response()->json([
                'status'    => 'success',
                'content'   => $listHtml,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status'  => 'error',
                'content' => $e,
            ]);
        }
    }

    public function getIndustryTemplateField()
    {
        $request = request();
        $category = \App\Models\Marketplace\Category::find($request->category_id);

        if ($category->industry_template) {
            $template = \App\Enums\Marketplace\IndustryTemplates::TEMPLATES[$category->industry_template];
            return view('marketplace.product.industry_template_form', compact('template')) ?? null;
        } else {
            return response()->json([
                'success' => 0,
            ]);
        }
    }

    // For product channel save country wise
    public function postCountrySave()
    {
        try {
            if ($_REQUEST['only_products'] && $_REQUEST['only_products'] == 1) {
                $products = $this->getIndex();
                $product_ids = $products->pluck('id');
            } else {
                $product_ids = explode(',', $_REQUEST['product_id']);
            }

            $channel = $_REQUEST['channel'];
            $channels = array_map('makeInt', explode(',', $channel));
            $product_country = $_REQUEST['country'];
            $marketplaceAllowedChannel = new MarketplaceAllowedChannel;
            // $productsAllowedChannelcheck = $marketplaceAllowedChannel->where('country_id', $product_country)
            //                     ->whereIn('product_id', $product_ids)
            //                     ->get();
            // if(!empty($productsAllowedChannelcheck)){
            //     dispatch(new RemoveProductsFromBanededChannels($productsAllowedChannelcheck, $channels));
            // }

            foreach ($product_ids as $key => $product_id) {

                $allowedChannelcheck = $marketplaceAllowedChannel->where('country_id', $product_country)
                    ->where('product_id', $product_id)
                    ->first();
                if (isset($allowedChannelcheck->channel_id)) {
                    $removeChannels = array_diff($allowedChannelcheck->channel_id, $channels);

                    if ($removeChannels) {
                        dispatch(new RemoveProductsFromBanededChannels($product_id, $removeChannels));
                    }

                    if ($allowedChannelcheck->channel_id[0] != 0 && $channels[0] != 0) {
                        $marketplaceAllowedChannel->where('country_id', $product_country)
                            ->where('product_id', $product_id)
                            ->update(array('channel_id'  => $channels));
                    }
                    if ($channels[0] == 0) {
                        $marketplaceAllowedChannel->where('product_id', $product_id)->where('country_id', $product_country)
                            ->delete();
                    }
                } else if ($channels[0] != 0) {
                    $now = new DateTime();
                    $updateAttributes = [];
                    $updateAttributes[] = [
                        'product_id' => $product_id,
                        'country_id' => $product_country,
                        'channel_id' => json_encode($channels),
                        'created_at' => $now->format('Y-m-d H:i:s'),
                        'updated_at' => $now->format('Y-m-d H:i:s'),
                    ];

                    // $marketplaceAllowedChannel->insert($updateAttributes);

                    $default_channels = json_encode(array_values(array_diff(\App\Enums\Channel::ALL, array_merge(\App\Enums\Channel::MP_BLACKLIST, ))));
                    $default_country_ids = array_diff(\App\Enums\DefaultSelectedCountries::COUNTRY_IDS, [$product_country]);
                    foreach($default_country_ids as $country_id){
                        if(!$marketplaceAllowedChannel->where('product_id', $product_id)->where('country_id', $country_id)->exists()){
                            $updateAttributes[] = [
                                'product_id' => $product_id,
                                'country_id' => $country_id,
                                'channel_id' => $default_channels,
                                'created_at' => $now->format('Y-m-d H:i:s'),
                                'updated_at' => $now->format('Y-m-d H:i:s'),
                            ];
                        }
                    }
                    $marketplaceAllowedChannel->insert($updateAttributes);

                } else {
                    $now = new DateTime();
                    $channels = json_encode(array_values(array_diff(\App\Enums\Channel::ALL, array_merge(\App\Enums\Channel::MP_BLACKLIST, ))));
                    $country_ids = array_diff(\App\Enums\DefaultSelectedCountries::COUNTRY_IDS, [$product_country]);
                    $updateAttributes = [];
                    foreach($country_ids as $country_id){
                        $updateAttributes[] = [
                            'product_id' => $product_id,
                            'country_id' => $country_id,
                            'channel_id' => $channels,
                            'created_at' => $now->format('Y-m-d H:i:s'),
                            'updated_at' => $now->format('Y-m-d H:i:s'),
                        ];
                    }
                    MarketplaceAllowedChannel::insert($updateAttributes);
                }
            }
            $message = __('products have had their distribution rights updated. Note the impact on the visibility of your offers.');
            return response()->json(['success' => true, 'message' => (app()->getLocale() == 'de' ? 'Bei ' : '') . ($key + 1) . ' ' . $message]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
        }
    }

    // For send country to channel modal
    public function getSelectedCountry()
    {
        $country = request()->country;
        $product_id = request()->product_id;
        $updated_channels = MarketplaceAllowedChannel::where('country_id', $country)
            ->where('product_id', $product_id)
            ->first();
        $channel_id = $updated_channels->channel_id;
        $channels = collect(config('channel.list'));
        $disable_channel = \App\Enums\Channel::MP_BLACKLIST;
        return view('marketplace.product.product_flag', compact('channel_id', 'channels', 'disable_channel'));
    }

    public function getApiCategoryNames()
    {

        $ids = request()->apiIds;

        $products = [];
        foreach ($ids as $id) {
            $id_array =  explode("=>", $id);

            $local_products = ApiCategory::where('api_id', $id_array[1])
                ->where('api_category_id', $id_array[0])
                ->select('api_id', 'api_category_id', 'api_category_name')->first();
            if (!empty($local_products)) {
                $products[] = $local_products->toArray();
            } else {
                $products[] = [
                    "api_id" => $id_array[1],
                    "api_category_id" => $id_array[0],
                    "api_category_name" => "No Category"
                ];
            }
        }
        return response()->json(['data' => $products], 200);
    }

    public function getCheckInternelApiOrder()
    {
        $data['order_id']    = $_GET['order_id'];
        $data = json_decode(json_encode($data));
        if ($data) {

            // $result = app(InternelSyncService::class)->retriveOrderStatus($data);
            $result = app(InternelSyncService::class)->checkOrderStatusById($data);

            dd($result);
        } else {
            dd("param needed");
        }
    }

    public function visibilityLight($category_id, $product_id, $status)
    {
        $isTransferInventory = DB::connection('marketplace')->table('mp_core_drm_transfer_products')->select('drm_product_id')->where('marketplace_product_id', $product_id)->pluck('drm_product_id')->toArray();
        if ($isTransferInventory) {
            $isConnectedChannels = DB::table('channel_products')->whereIn('drm_product_id', $isTransferInventory ?? [])->exists();
            if ($isConnectedChannels) {
                return '<div class="traffic-light">
                            <input type="radio" class="traffic-light-color" style="background-color: #00FF00 !important;box-shadow: 0 0 6em #33ff33;" name="traffic-light-color" id="color-green" value="color-green" />
                       </div>';
            } else {
                return '<div class="traffic-light">
                            <input type="radio" class="traffic-light-color" style="background-color: #ffa500 !important;box-shadow: 0 0 6em #ffa500;" name="traffic-light-color" id="color-orange" value="color-orange" />
                        </div>';
            }
        } else {
            $found = false;
            $sponsors_products = MarketplaceSponsor::select('product_id')->where('supplier_id', \CRUDBooster::myParentId())->where('status',1)->pluck('product_id')->toArray();
            foreach ($sponsors_products as $sponsors_product) {
                if (array_search($product_id, $sponsors_product)) {
                    $found = true;
                }
            }
            if($found){
                return
                    "<img style=border-radius:100% height=23px width=23px border-radius=100% src='".asset('campaign_img/add_img.png')."'/>"
               ;
            } else {
                return '<div class="traffic-light">
                            <a href="'.route('marketplace::sponsor-create').'?product_id='.$product_id.'">
                                <input type="button" class="traffic-light-color" style="background-color: #ff0505 !important;box-shadow: 0 0 6em #ff3333;" name="traffic-light-color" id="color-red" value="" />
                            </a>
                        </div>';
            }
        }
    }

    public function getSearchableProduct()
    {
        $search = request()->search_key;
        $accessableCategory = DB::connection('marketplace')->table('marketplace_user_accesses')->where('user_id', request()->customer_id)->pluck('accessable_categories')->first();
        if ($search != null) {
            $searchWord = "\\b$search\b";
            $marketplaceProducts = DB::connection('marketplace')->table('marketplace_products')->select('name');
            if (\CRUDBooster::isSuperadmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
                $marketplaceProducts = $marketplaceProducts->where('status', 1)->where("name", 'REGEXP', $searchWord)->get();
            } else {
                $marketplaceProducts = $marketplaceProducts->whereIn('category_id', json_decode($accessableCategory) ?? [])->where('status', 1)->where("name", 'REGEXP', $searchWord)->get();
            }
            foreach ($marketplaceProducts as $marketplaceProduct) {
                $this->searchableproducts .= '<li>' . $marketplaceProduct->name . '</li>';
            }
        } else {
            $this->searchableproducts = '';
        }
        return $this->searchableproducts;
    }

    public function updateUserCategoryAccess($products, $Category_id)
    {
        Log::info('UpdateUserCategoryAccess Function Hit Done');
        $transfer = new MpCoreDrmTransferProduct;
        $user_access = new UserAccess;
        $trnsfer_products = $transfer->whereIn('marketplace_product_id', $products)->get();
        $user_ids = $trnsfer_products->pluck('user_id')->unique()->toArray();
        $all_user_access = $user_access->whereIn('user_id', $user_ids)
            ->whereJsonContains('accessable_categories', $Category_id)
            ->select('accessable_categories', 'user_id')
            ->get();
        if ($trnsfer_products && $all_user_access) {
            foreach ($trnsfer_products as $product) {
                $user_access = $all_user_access->where('user_id', $product->user_id)->first();
                if (!$user_access) {
                    app(\App\Services\DRMProductService::class)->destroy($product->drm_product_id, $product->user_id);
                }
            }
        }
    }

    public function getCountryWiseTax()
    {
        // $country_id = request()->country_id;
        $country = \App\Country::select('tax_rate', 'reduced_tax_rate')->find(request()->country_id);
        $country_tax = '<option value="" selected>Select Tax</option>';
        $country_tax .= '<option value="' . $country->tax_rate . '">' . $country->tax_rate . '% (Original)' . '</option>';
        $country_tax .= '<option value="' . $country->reduced_tax_rate . '">' . $country->reduced_tax_rate . '% (Reduced)' . '</option>';

        return $country_tax;
    }

    public function getProductCategoryUpdate()
    {
        $category = '';
        $category .= '<option value="">Select a category</option>';
        if(is_relavida() || in_array(CRUDBooster::myParentId(), [2817])){
            $categories = ChannelUserCategory::where('user_id', 2694)->where('channel', 10)->where('parent', request()->parent_category)->orderBy('category_name')->get();
            foreach ($categories as $categorie) {
                $category .= '<option value="' . $categorie->id . '" data-parent-id="' . $categorie->parent . '">' . $categorie->category_name . '</option>';
            }
        } else {
            $categories = Category::where('parent_id', request()->parent_category)->where('is_active', 1)->orderBy("name")->get();
            foreach ($categories as $categorie) {
                $category .= '<option value="' . $categorie->id . '" data-parent-id="' . $categorie->parent_id . '">' . $categorie->name . '</option>';
            }
        }
        return $category;
    }

    public function postChangeAlarmQuantity()
    {
        try {
            $productIds = explode(',', request()->product_id);
            $changeAlarmQty = DB::connection('marketplace')->table('marketplace_products')->whereIn('id', $productIds)
                ->update([
                    'alarm_quantity' => request()->update_qty,
                ]);
            $message = 'Alarm Quantity changed successfully';
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $message, 'info');
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    public function postTransferProductsToDrm()
    {
       try {

            $productIds = [];
            $all_products = [];
            $user_id = request()->customer_id;
            $parent_categories = array_map('intval', explode(',', request()->category_ids)) ?? [];
            $categories = Category::select('id', 'parent_id')->whereIn('parent_id', $parent_categories)->get() ?? [];
            $request_product_ids = request()->product_ids ?? [];
            $tranferable_ids = [];
            $country_id = (int)(Session::get('filter_country_' . CRUDBooster::myParentId()) ?? 1);

            if(request()->selected_all == 1){
                $all_products = $this->getTranferableProducts()->select('id', 'category_id', 'ean', 'status', 'api_id', 'country_id')->get();
                $productIds = $all_products->where('status',1)
                    ->whereIn('category_id', $categories->pluck('id')->toArray() ?? [])
                    ->filter(function ($product) {
                        return !($product->api_id == 5 && in_array($product->country_id, [8, 83]));
                    })
                    ->pluck('id')
                    ->toArray();//Product::where('status',1)->whereIn('id',$productIds)->whereIn('category_id', $categories)->select('id')->get()->pluck('id')->toArray();
                $productIds = array_diff($productIds, $request_product_ids);
            } else {
                $all_products = Product::select('id', 'ean', 'api_id', 'country_id')
                    ->where('status',1)
                    ->whereIn('id',$request_product_ids)
                    ->whereIn('category_id', $categories->pluck('id')->toArray() ?? [])
                    ->where(function ($query) {
                        $query->where('api_id', '!=', 5)
                        ->orWhereNotIn('country_id', [8, 83]);
                    })
                    ->get();
                $productIds = $all_products->pluck('id')->toArray();
            }

            if(empty($productIds)){
                return [
                    'success' => false,
                    'message' => "Products are not approved or the limit of categories already Over.",
                ];
            }
            foreach( array_chunk($productIds, 10000) as $chunk_ids ){

                if(isset(\App\Enums\V2UserAccess::USERS[$user_id])){

                    $id_exists = DB::connection('marketplace')->table('marketplace_transferred_products_in_drm')
                        ->where('user_id', $user_id)
                        ->where('country_id', $country_id)
                        ->whereIn('marketplace_product_id', $chunk_ids)
                        ->pluck('marketplace_product_id')
                        ->toArray();

                }else{
                    $id_exists = DrmProduct::select('marketplace_product_id')
                    ->where('user_id', $user_id)
                    ->whereIn('marketplace_product_id', $chunk_ids)
                    ->get()
                    ->pluck('marketplace_product_id')
                    ->toArray();
                }

                $tranferable_ids[] = array_diff($chunk_ids, $id_exists);
            }

            $tranferable_ids = array_merge(...$tranferable_ids);

            if(empty($tranferable_ids)){
                return [
                    'success' => false,
                    'message' => "Products already transfered to drm. Please select new products.",
                ];
            }

            $importProduct = app(\App\Http\Controllers\AdminDrmImportsController::class)->importProductCheck($user_id);
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;
            $transferPlan = $importProduct['plan'] ? $importProduct['plan'] : null;
            $transferPlanLimit = $importProduct['limit'] ? $importProduct['limit'] : null;

            if((($transferPlan && $transferPlan == 'Trial') && ($transferPlanLimit && $transferPlanLimit == 'Unlimited')) || ($transferPlanLimit && $transferPlanLimit == 'Unlimited')){
                $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $user_id)->first();
                $product_cats = array_diff($parent_categories, $user_access_status->accessable_parent_categories ?? []);
                $newArray = [];
                $allowedCategories = $parent_categories;
                if(!empty($allowedCategories)){
                    $all_access_category = UserAccess::where('user_id', $user_id)
                                            ->select('check_accessable_categories')
                                            ->get()->toArray();
                    if(!empty($all_access_category)){
                        $single_access_category = array_column($all_access_category[0]['check_accessable_categories'], 'accessable_categories');

                        $single_access_category_user = array_column($all_access_category[0]['check_accessable_categories'], 'set_by_admin');

                        foreach($allowedCategories as $category){
                            if(!in_array($category,$single_access_category)){
                                $newArray[] = array("accessable_categories"=>$category,"set_by_admin"=>0);
                            }else{
                                $index = array_keys($single_access_category, $category);
                                $val = $single_access_category_user[$index[0]];
                                $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>$val);
                            }
                        }
                        foreach($single_access_category as $key => $old_access){
                            if(!in_array($old_access,$allowedCategories)){
                                $val = $single_access_category_user[$key];
                                if($val == 0){
                                    $newArray[] = array("accessable_categories"=>$old_access,"set_by_admin"=>$val);
                                }
                            }
                        }
                    }else{
                        foreach($allowedCategories as $category){
                            $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>0);
                        }
                    }
                }

                if(!empty($product_cats)){
                    $new_merge = array_unique(array_merge($user_access_status->accessable_parent_categories ?? [], $product_cats));
                    $sub_category = array_unique(array_merge($user_access_status->accessable_categories ?? [], $categories->whereIn('parent_id', $product_cats)->pluck('id')->toArray() ?? []));

                }else{
                    $new_merge = $user_access_status->accessable_parent_categories;
                    $sub_category = $user_access_status->accessable_categories;
                }

                if(!empty($newArray)){
                    $attributes =  array_merge([
                        'user_id'           => $user_id,
                        'status'            => 1,
                    ],['accessable_categories' => $sub_category], ['accessable_parent_categories' => $new_merge], ['check_accessable_categories' => $newArray]);

                    $newRow = UserAccess::updateOrCreate(['user_id'=>$user_id], $attributes);
                }
                // if(count($tranferable_ids) > 150){
                    foreach( array_chunk($tranferable_ids, 500) as $tranferable_id ){
                        Log::info("MP to DRM unlimited product transfer job dispatch");
                        dispatch(new MarketplaceProductTransferToDrm($tranferable_id, $user_id, null, true, $country_id))->onQueue('mp_product_trans'); //->onQueue('keepa');
                    }
                    return [
                        'status' => true,
                        'message' => 'Your selected ' .count($tranferable_ids) . ' products transferred on going on the background process. It may take some time to transfer all products.',
                    ];
                // } else {
                //     return app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferUnlimitedProductsToDrm($tranferable_ids, $user_id);
                // }
            } else if($transferLimit && $transferLimit > 0) {
                $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $user_id)->first();
                $product_cats = array_diff($parent_categories, $user_access_status->accessable_parent_categories ?? []);
                $newArray = [];
                $allowedCategories = $parent_categories;
                if(!empty($allowedCategories)){
                    $all_access_category = UserAccess::where('user_id', $user_id)
                                                ->select('check_accessable_categories')
                                                ->get()->toArray();
                    if(!empty($all_access_category)){
                        $single_access_category = array_column($all_access_category[0]['check_accessable_categories'], 'accessable_categories');

                        $single_access_category_user = array_column($all_access_category[0]['check_accessable_categories'], 'set_by_admin');

                        foreach($allowedCategories as $category){
                            if(!in_array($category,$single_access_category)){
                                $newArray[] = array("accessable_categories"=>$category,"set_by_admin"=>0);
                            }else{
                                $index = array_keys($single_access_category, $category);
                                $val = $single_access_category_user[$index[0]];
                                $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>$val);
                            }
                        }
                        foreach($single_access_category as $key => $old_access){
                            if(!in_array($old_access,$allowedCategories)){
                            $val = $single_access_category_user[$key];
                            if($val == 0){
                                $newArray[] = array("accessable_categories"=>$old_access,"set_by_admin"=>$val);
                            }
                            }
                        }
                    }else{
                        foreach($allowedCategories as $category){
                            $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>0);
                        }
                    }
                }

                if(!empty($product_cats)){
                    $new_merge = array_unique(array_merge($user_access_status->accessable_parent_categories ?? [], $product_cats));
                    $sub_category = array_unique(array_merge($user_access_status->accessable_categories ?? [], $categories->whereIn('parent_id', $product_cats)->pluck('id')->toArray() ?? []));
                }else{
                    $new_merge = $user_access_status->accessable_parent_categories;
                    $sub_category = $user_access_status->accessable_categories;
                }

                if(!empty($newArray)){
                    $attributes =  array_merge([
                        'user_id'           => $user_id,
                        'status'            => 1,
                    ],['accessable_categories' => $sub_category], ['accessable_parent_categories' => $new_merge], ['check_accessable_categories' => $newArray]);

                    $newRow = UserAccess::updateOrCreate(['user_id'=>$user_id], $attributes);
                }

                // if(count($tranferable_ids) > 150){
                    foreach( array_chunk($tranferable_ids, 500) as $tranferable_id ){
                        Log::info("MP to DRM limited product transfer job dispatch");
                        dispatch(new MarketplaceProductTransferToDrm($tranferable_id, $user_id, null, false, $country_id))->onQueue('mp_product_trans'); //->onQueue('keepa');
                    }
                    return [
                        'status' => true,
                        'message' => 'Your selected ' .count($tranferable_ids). ' products transferred on going on the background process. It may take some time to transfer all products.',
                    ];
                // } else {
                //     return app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferTarifLimitedProductsToDrm($tranferable_ids, $user_id);
                // }

            } else {
                return response()->json([
                    'status'      => true,
                    'message'     => 'Your DRM products transfer limit exceed! Please Upgrade your tarif plan.',
                ]);
            }

        } catch (\Exception $e) {
           return response()->json([
               'status'        => 'error :: '.$e->getMessage(),
               'error_msg'     => 'Something went wrong',
           ]);
       }
    }

    public function getInternelIsLeftStock()
    {
      $stockinfo = DB::connection('marketplace')->table('fulfilment_stock_send_log')->select('is_left')->where('product_id',request()->product_id)->where('is_left','!=',null)->first();
      return response()->json([
        'is_left' => $stockinfo->is_left != null ? true : false,
        'is_left_stock'=> $stockinfo->is_left ?? 0
      ]);
    }

    public function getStoreSendItem(){

        try {
            $productIds = request()->product_ids;
            $approvedProduct =  \App\Models\Marketplace\Product::with('additionalInfo:product_id,product_length,product_width,product_height', 'stockSendComment')
                ->whereIn('id', $productIds)
                ->where('shipping_method', ShippingMethod::FULFILLment)
                ->where('status', ProductStatus::ACTIVE)
                ->where('atw', 0)
                ->select('id','ean','name','image','stock', 'atw', 'item_weight')
                ->orderBy('id','desc')
                ->get();
            
            if(!blank($approvedProduct)){
                return response()->json([
                    'status' => 200,
                    'data'=> $approvedProduct ?? [],
                    'is_supplier' => \CRUDBooster::isSupplier(),
                ]);
            }else{
                return response()->json([
                    'status' => 204,
                ]);
            }
        } catch (\Exception $e) {
            Log::info('!ops Error get store send item'.$e->getMessage());
            return $this->redirectBackWithMessage(
                __('Something went wrong...'), 
                'warning'
            );
        }
    }

    public function postSaveSendItem(){
        try {
            $request = request();
            $send_items = $request->input('atw');
            $product_ids = $request->input('product_id');
            $comments = $request->input('comment');
            $item_weights = $request->input('item_weight');
            $item_lengths = $request->input('item_length');
            $item_heights = $request->input('item_height');
            $item_widths = $request->input('item_width');

            foreach ($product_ids as $product_id) {
                $product = Product::findOrFail($product_id);

                $product->update([
                    'atw' => $send_items[$product_id],
                    'item_weight' => $item_weights[$product_id]
                ]);

                $product->stockSendComment()->updateOrCreate(
                    ['mp_product_id' => $product_id],
                    ['send_stock_comment' => $comments[$product_id]]
                );

                $product->additionalInfo()->updateOrCreate(
                    ['product_id' => $product_id],
                    [
                        'product_length' => $item_lengths[$product_id],
                        'product_height' => $item_heights[$product_id],
                        'product_width'  => $item_widths[$product_id],
                        'volume' => ($item_lengths[$product_id] ?? 0) * ($item_heights[$product_id]) * ($item_widths[$product_id]) / 1000000,
                    ]
                );
            }

            $message = 'Warehouse send item set successful';
            return $this->redirectBackWithMessage($message, 'success');

        } catch (\Exception $e) {
            Log::info('!ops Error get store send item'.$e->getMessage());
            return $this->redirectBackWithMessage(
                __('Something went wrong...'), 
                'warning'
            );
        }
    }
    
    private function redirectBackWithMessage($message, $type) {
        \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $message, $type);
    }
    

    public function getSupplierBillingAddress(){

        $user = User::find(request()->supplier_id);
        if($user){
            $update_at = $user->billing_detail->last_updated_at;
            $update_billing = false;

            return response()->json([
                'success' => true,
                'html' => view('subscription.mp-billing', compact('user'))->render(),
                'update_billing' => $update_billing,
                'next_date' => '',
                'last_updated_at' => Carbon::parse($update_at)->format('d.m.Y H:i'),
                'pdf_link' => empty($user->billing_detail->pdf_link) != false ? $user->billing_detail->pdf_link : null ,
            ]);
        }else{
            $update_billing = false;
            return response()->json([
                'success' => true,
                'update_billing' => $update_billing,
                'html' => view('subscription.mp-billing', compact('user'))->render()
            ]);
        }
    }

    public function getChangeFulfilmentPartialProduct(){
        $productIds = request()->product_ids;
        $partialProducts =  Product::select('id','internel_stock')->whereIn('id',$productIds)
        ->whereHas('stockSendLog', function ($query) {
            $query->where('is_left','!=',null);
        })->where('shipping_method',\App\Enums\Marketplace\ShippingMethod::FULFILLment)
        ->where('status',ProductStatus::ACTIVE)->where('internel_sync_status',ProductStatus::GOODS_RECEIVED)->get();
        if(!blank($partialProducts)){
            foreach($partialProducts as $partialProduct){
                try {
                    Product::where('id',$partialProduct->id)->update([
                     'internel_sync_status' => 0,
                     'stock' => 0,
                     'internel_stock_received_at' => null
                    ]);
                    FulfilmentStockSendLog::where('product_id',$partialProduct->id)->update([
                     'send_stock' => $partialProduct->internel_stock,
                     'recived_stock' => $partialProduct->internel_stock,
                     'is_left' => null
                    ]);
                } catch (\Exception $e) {
                    Log::info('!ops Error change fulfilment partial product'.$e->getMessage());
                }
            }
            return response()->json([
                'status' => 200,
                'message'=> __('marketplace.Order_complete_successful')
            ]);
        }else{
            return response()->json([
                'status' => 204,
                'message'=> __('marketplace.selected_non_partial_product')
            ]);
        }
    }

    public function getProductImage()
    {
        $ids = request()->productIds;
        $products = [];
        foreach ($ids as $id) {
            $product = Product::where('id', $id)->select('id','image')->first();
            $products[] = [
                "product_id" =>$product->id,
                "product_image" => $product->image ? $product->image[0] : asset('../Marketing_assets/img/dropmatix_logo.png'),
            ];
        }
        return response()->json(['data' => $products], 200);
    }

    public function getAddIndustryTemplateFields(){

        $selected_template_name = $_REQUEST['selected_template_name'];
        $selected_template_field_name = config("industry_template.".$selected_template_name);
        $selected_template_field_name = array_keys($selected_template_field_name);

        $input_field = '';
        foreach($selected_template_field_name as $name){

            $trans_field_name = __('industry_template.'.$name);

            $input_field .= '
                            <div class="row" style="padding-bottom: 15px;padding-top: 5px;">
                                <label class="col-sm-1 control-label">'. $trans_field_name .'</label>
                                <div class="col-sm-4">
                                <input name="' . $selected_template_name . '[' . $name . ']' . '" placeholder="' . $trans_field_name . '" class="form-control" type="text">
                                </div>
                            </div>
                         ';

        }
        return response()->json(['success' => true, 'data' => $input_field], 200);
    }

    public function postProductEanRemove(){
       try{
            $product = Product::select('id','additional_eans')->where('id',request()->product_id)->first();
            $exist_additional_eans = $product->additional_eans ?? [];
            $additional_eans = array_filter(array_diff($exist_additional_eans, [request()->remove_ean]));
            $product->update(['additional_eans' =>$additional_eans]);
            return response()->json([
                'status' => 200,
                'message'=> 'Ean remove success'
            ]);
       }catch(\Exception $e){
            return response()->json([
                'status' => 204,
                'message'=> 'Ean remove success'
            ]);
       }
    }
    public function postChangeShippingMethodType(Req $request)
    {
        try {
            $ids = [];
            $truncate_ids = [];
            if ($request->only_products && $request->only_products == 1) {
                $products = $this->getIndex();
                $ids = $products->pluck('id')->toArray();
                $ids = array_diff($ids, $request->product_ids ?? []);
            } else {
                $ids = $request->product_ids;
            }

            if($request->change_shipping_method_type == ShippingMethod::DROPSHIPPING){
                // $truncate_ids = \App\Models\Marketplace\Product::where('shipping_method', ShippingMethod::FULFILLment)->where('internel_stock', '>', 0)
                //                 ->orWhere('internel_sync_status', ProductStatus::WAITING_SYNC)->pluck('id')->toArray();
                $truncate_ids = \App\Models\Marketplace\Product::where('shipping_method', ShippingMethod::FULFILLment)
                    ->whereIn('id', $ids)
                    ->where('internel_stock', '>', 0)
                    ->orWhere(function($query) use ($ids){
                        $query->whereIn('id', $ids)
                        ->where('shipping_method', ShippingMethod::FULFILLment)
                        ->where('internel_sync_status', ProductStatus::WAITING_SYNC);
                    })
                    ->orWhere(function($query) use ($ids){
                        $query->whereIn('id', $ids)
                        ->whereHas('stockSendLog', function ($query) {
                            $query->where('is_left','!=',null);
                        })
                        ->where('shipping_method', ShippingMethod::FULFILLment)
                        ->where('internel_sync_status', ProductStatus::GOODS_RECEIVED);
                    })
                    ->pluck('id')->toArray();
                $ids = array_diff($ids, $truncate_ids ?? []);

                $selected_products = \App\Models\Marketplace\Product::select('id','ean','name','image','status','internel_stock','internel_sync_status')
                ->whereIn('id', $truncate_ids ?? [])
                ->with('stockSendLog:is_left')
                // ->where('status', 1)
                ->orderBy('id','desc')
                ->get();
            }

            if(count($ids) > 0){
                if($request->change_shipping_method_type == ShippingMethod::FULFILLment){
                    $update_products = \App\Models\Marketplace\Product::whereIn('id', $ids)->where('status', 1)->where('shipping_method', '!=', $request->change_shipping_method_type)->update(
                        [
                            'shipping_method' => $request->change_shipping_method_type,
                            'internel_sync_status' => 0,
                            'stock' => 0,
                        ]
                    );
                } else {
                    $update_products = \App\Models\Marketplace\Product::whereIn('id', $ids)->where('status', 1)->where('shipping_method', '!=', $request->change_shipping_method_type)->update(
                        [
                            'shipping_method' => $request->change_shipping_method_type,
                        ]
                    );
                }
                $drm_products =\App\Models\Marketplace\MpCoreDrmTransferProduct::whereIn('marketplace_product_id',  $ids)->pluck('drm_product_id')->toArray();
                if($drm_products){
                    $updateableColumn['marketplace_shipping_method'] = $request->change_shipping_method_type;
                    foreach($drm_products as $drm_product_id){
                        app(\App\Services\DRMProductService::class)->update($drm_product_id, $updateableColumn);
                    }
                }

                if(!blank($selected_products)){
                    return response()->json([
                        'status' => true,
                        'data'=> $selected_products ?? [],
                    ]);
                }

                if($update_products > 0){
                    return response()->json([
                        'success' => true,
                        'message' => $request->change_shipping_method_type == ShippingMethod::FULFILLment ? 'Successfully! changed products Dropship To Fulfillment.' : 'Successfully! changed products Fulfillment To Dropship.',
                    ]);
                } else {
                    return response()->json([
                        'success' => true,
                        'message' => 'Wrong! action taken, selected products not changed.',
                    ]);
                }

            } else {

                if(!blank($selected_products)){
                    return response()->json([
                        'status' => true,
                        'data'=> $selected_products ?? [],
                    ]);
                }

                return response()->json([
                    'success' => false,
                    'message' => $request->change_shipping_method_type == ShippingMethod::FULFILLment ? 'No products! changed Dropship To Fulfillment.'
                                : 'Change of dropshipping status is only possible when there are no more products available in stock".
                                Please fully stock out this product first or wait until it is fully sold out.',
                ]);
            }

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
        }
    }

    public function  postTransferToAnalysis(Req $request){

        $user_id = CRUDBooster::myParentId();
        $ids = [];
        if ($request->selected_all && $request->only_products == 1) {

            $res = [];
            Log::channel('command')->info('Line 5000');
            $this->indexBuilder = true;

            Log::channel('command')->info('Line 5004');
            $this->getIndex()
            ->select('id')
            ->orderBy('id')
            ->chunk(1000, function($items) use ($user_id, &$res) {
                Log::channel('command')->info('Line 5004x');
                $res = app(TransferProduct::class)->mpProductTransferToAnalysis($user_id, $items->pluck('id')->toArray());
                Log::channel('command')->info('Line 5004xx');
            });
            Log::channel('command')->info('Line 5005');

            return $res;






            // Log::channel('command')->info('Line 5005');
            // $ids = $products->pluck('id')->toArray();
            // Log::channel('command')->info('Line 5007');
            // $ids = array_diff($ids, $request->product_ids ?? []);
            // Log::channel('command')->info('Line 5009');
        } else {
            $ids = is_array($request->product_ids) ? $request->product_ids : [$request->product_ids];
            $res = app(TransferProduct::class)->mpProductTransferToAnalysis($user_id, $ids);
        }

        // $productChunks = array_chunk($ids, 1000);

        // foreach($productChunks as $chunks){
        //     $res = app(TransferProduct::class)->mpProductTransferToAnalysis($user_id, $chunks);
        // }

        return response()->json($res);
    }

    public function  postCsvDownloadSend(Req $request){

        $user_id = CRUDBooster::myParentId();
        $ids = [];
        if($request->status==0){
            //dd("kkk");
            if ($request->selected_all==1 ) {
                $res = [];
                Log::channel('command')->info('Line 5000');
                $this->indexBuilder = true;

               $products = $this->getIndex()
                ->pluck('ean')->toArray();

                //dd((array)$products[0]);
                //code to csv zip download
                //$headings = array_keys((array)$products[0] ?? []);
                $headings = ['ean'] ?? [];
                $chunks = array_chunk($products, 10000);
                $name = Carbon::now()->format('d-m-Y');
                $a = 0;
                foreach($chunks as $key => $value){
                    $a++;
                    Excel::store(new MarketplaceProductsExport($value, $headings, true), 'eans/'.$name.'/products_ean_list'.$a.'.csv');
                }
                $zip = new ZipArchive;
                $fileName = $name.'.zip';
                File::delete('product_eans/'.$fileName);
                $path = public_path().'/product_eans';
                if (!file_exists($path)) {
                    //dd("not");
                    File::makeDirectory($path, $mode = 0777, true, true);
                }
                if ($zip->open(public_path('product_eans/'.$fileName), ZipArchive::CREATE) === TRUE)
                {
                    $files = File::files(storage_path('app/eans/'.$name));
                    foreach ($files as $key => $value) {
                        $relativeNameInZipFile = basename($value);
                        $zip->addFile($value, $relativeNameInZipFile);
                    }
                    $zip->close();
                }
                File::deleteDirectory(storage_path('app/eans/'.$name));

                return response($fileName);

            } else {
                //dd("here");
                $ids = $request->product_ids;

                $products = DB::connection('marketplace')->table('marketplace_products')->whereIn('id',$ids)->pluck('ean')->toArray();
                // $products = $this->getIndex()
                // ->pluck('ean')->toArray();

                //code to csv zip download
                //$headings = array_keys((array)$products[0] ?? []);
                $headings = ['ean'] ?? [];
                $chunks = array_chunk($products, 10000);
                $name = Carbon::now()->format('d-m-Y');
                $a = 0;
                foreach($chunks as $key => $value){
                    $a++;
                    Excel::store(new MarketplaceProductsExport($value, $headings, true), 'eans/'.$name.'/products_ean_list'.$a.'.csv');
                }
                $zip = new ZipArchive;
                $fileName = $name.'.zip';
                File::delete('product_eans/'.$fileName);
                $path = public_path().'/product_eans';
                if (!file_exists($path)) {
                    //dd("not");
                    File::makeDirectory($path, $mode = 0777, true, true);
                }
                if ($zip->open(public_path('product_eans/'.$fileName), ZipArchive::CREATE) === TRUE)
                {
                    $files = File::files(storage_path('app/eans/'.$name));
                    foreach ($files as $key => $value) {
                        $relativeNameInZipFile = basename($value);
                        $zip->addFile($value, $relativeNameInZipFile);
                    }
                    $zip->close();
                }
                File::deleteDirectory(storage_path('app/eans/'.$name));

                return response($fileName);

            }
        //    // dd($request->all(),$ids);
        //     return response()->json($ids);
        }

    }
       //Download Ean List Function
    // function downloadEanlist($ids){
    //     ini_set('max_execution_time', '0');

    //     foreach($chunks2 as $key=>$val){
    //         dd($val);
    //         $products = DB::connection('marketplace')->table('marketplace_products')->whereIn('id',$val)->select('ean')->get()->toArray();

    //     }
    //     dd($products);
    //     //$products = DB::connection('marketplace')->table('marketplace_products')->whereIn('id',$ids)->select('ean')->get()->toArray();

    //     //     $this->indexBuilder = true;

    //     //    // Log::channel('command')->info('Line 5004');
    //     //    $products = $this->getIndex()
    //     //     ->select('ean')->get()->toArray();

    //     //$products = DB::table('marketplace_products')->select('ean')->where('marketplace_product_id','!=','')->where('ean','!=','')->groupBy('ean')->get();
    //     $headings = array_keys((array)$products[0] ?? []);
    //     $chunks = array_chunk($products, 10000);
    //     $name = Carbon::now()->format('d-m-Y');
    //     $a = 0;
    //     foreach($chunks as $key => $value){
    //         $a++;
    //         Excel::store(new MarketplaceProductsExport($value, $headings, true), 'eans/'.$name.'/products_ean_list'.$a.'.csv');
    //     }
    //     $zip = new ZipArchive;
    //     $fileName = $name.'.zip';
    //     File::delete('product_eans/'.$fileName);
    //     $path = public_path().'/product_eans';
    //     if (!file_exists($path)) {
    //         //dd("not");
    //         File::makeDirectory($path, $mode = 0777, true, true);
    //     }
    //     if ($zip->open(public_path('product_eans/'.$fileName), ZipArchive::CREATE) === TRUE)
    //     {
    //         $files = File::files(storage_path('app/eans/'.$name));
    //         foreach ($files as $key => $value) {
    //             $relativeNameInZipFile = basename($value);
    //             $zip->addFile($value, $relativeNameInZipFile);
    //         }
    //         $zip->close();
    //     }
    //     File::deleteDirectory(storage_path('app/eans/'.$name));
    //     return true;
    //     return response()->download(public_path('product_eans/'.$fileName));

    // }

    public function getDropshipToFulfillmentProducts(Req $request){
        try {
            $ids = [];
            $changable_ids = [];
            if ($request->only_products && $request->only_products == 1) {
                $products = $this->getIndex();
                $ids = $products->pluck('id')->toArray();
                $ids = array_diff($ids, $request->product_ids ?? []);
            } else {
                $ids = $request->product_ids;
            }
            if($request->change_shipping_method_type == ShippingMethod::FULFILLment){
                $ids = \App\Models\Marketplace\Product::whereIn('id', $ids)->where('shipping_method', '!=', $request->change_shipping_method_type)->pluck('id')->toArray();
                $selected_products = \App\Models\Marketplace\Product::select('id','ean','name','image','stock','item_size','item_size_mesure','item_weight','item_weight_mesure')
                    ->whereIn('id',$ids)
                    ->where('status', 1)
                    ->with('stockSendComment')
                    ->orderBy('id','desc')
                    ->get();
            } else {
                $truncate_ids = \App\Models\Marketplace\Product::where('shipping_method', ShippingMethod::FULFILLment)
                    ->whereIn('id', $ids)
                    ->where('internel_stock', '>', 0)
                    ->orWhere(function($query) use ($ids){
                        $query->whereIn('id', $ids)
                        ->where('shipping_method', ShippingMethod::FULFILLment)
                        ->where('internel_sync_status', ProductStatus::WAITING_SYNC);
                    })
                    ->orWhere(function($query) use ($ids){
                        $query->whereIn('id', $ids)
                        ->whereHas('stockSendLog', function ($query) {
                            $query->where('is_left','!=',null);
                        })
                        ->where('shipping_method', ShippingMethod::FULFILLment)
                        ->where('internel_sync_status', ProductStatus::GOODS_RECEIVED);
                    })
                    ->pluck('id')->toArray();

                $selected_products = \App\Models\Marketplace\Product::select('id','ean','name','image','status','internel_stock','internel_sync_status')
                    ->whereIn('id', $truncate_ids)
                    ->with('stockSendLog:is_left')
                    ->where('status', 1)
                    ->orderBy('id','desc')
                    ->get();

                $changable_ids = array_diff($ids, $truncate_ids ?? []);
            }



            if(!blank($selected_products)){
                return response()->json([
                    'status' => 200,
                    'data'=> $selected_products ?? [],
                    'changable_ids' => $changable_ids,
                ]);
            }else{
                return response()->json([
                    'status' => 200,
                    'data'=> null,
                ]);
            }
        } catch (\Exception $e) {
            Log::info('!ops Error get store send item'.$e->getMessage());
        }
    }

    public function postSaveDropshipToFulfillmentProducts(){
        $request = request()->all();
        $product_ids = $request['product_ids'];
        $stock = $request['item_stock'];
        $item_size = $request['item_size'];
        $item_weight = $request['item_weight'];
        $item_comment = $request['comments'];
        $item_size_unit = $request['measurement_size'];
        $item_weight_unit = $request['measurement_weight'];

        if($product_ids){
            foreach($product_ids as $product_id){
                \App\Models\Marketplace\Product::where('id', $product_id)->update([
                    'stock' => $stock[$product_id],
                    'item_size' => $item_size[$product_id],
                    'item_size_mesure' => $item_size_unit[$product_id],
                    'item_weight' => $item_weight[$product_id],
                    'item_weight_mesure' => $item_weight_unit[$product_id],
                    'shipping_method' => 2,
                    'shipping_cost' => 5.20,
                    'internel_sync_status' => 0,
                ]);
                MpSendStockComment::updateOrCreate(['mp_product_id' => $product_id], [
                    'send_stock_comment' => $item_comment[$product_id],
                ]);
            }
            //$this->actionButtonSelected($product_ids, 'sync_product');
             \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Successfully! changed products Dropship To Fulfillment.', 'success');
        } else {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'No Products Changed Dropship To Fulfillment.', 'error');
        }
    }

    public function postSaveFulfillmentToDropshipProducts(){
        $request = request()->all();
        $product_ids = explode(',', $request['product_ids']);

        if($product_ids){
                \App\Models\Marketplace\Product::whereIn('id', $product_ids)->where('shipping_method', '!=', ShippingMethod::DROPSHIPPING)->update([
                    'stock' => 0,
                    'shipping_method' => ShippingMethod::DROPSHIPPING,
                    'internel_sync_status' => 0,
                ]);

            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Successfully! changed products Fulfillment to Dropship.', 'success');
        } else {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'No Products Changed Fulfillment to Dropship.', 'error');
        }
    }

    public function postMpColumnSave() {
        $fixed_selected_column = ['id', 'image', 'name', 'ean', 'ek_price','shipping_cost', 'uvp', 'category', 'stock', 'internel_stock', 'shipping_method'];
        $selected_column = array_values(Request::input('saved_columns') ?? []);
        $selected_column = json_encode(array_diff($selected_column ?? [],$fixed_selected_column));
        $user_id = CRUDBooster::myId();
        $table = Request::input('table_name');
        DB::table('drm_user_saved_columns')->updateOrInsert(
            ['user_id' => $user_id, 'table_name' => $table],
            ['columns' => $selected_column]
        );
        CRUDBooster::redirectBack(trans("crudbooster.alert_update_data_success"), 'success');
    }

    function getTranferableProducts(){

        $query = DB::connection('marketplace')->table('marketplace_products');

        //STATUS FILTER
        if (Request::get('status')) {
            $st = Request::get('status');

            if (Request::get('status') == 'all') {
                $query = $query;
                // Session::put('filter_status_' . CRUDBooster::myParentId(), Request::get('status'));
            } else {
                if ($st == 'pending') $col_val = ProductStatus::PENDING;
                if ($st == 'approved') $col_val = ProductStatus::ACTIVE;
                if ($st == 'rejected') $col_val = ProductStatus::INACTIVE;
                if ($st == 'incomplete') $col_val = ProductStatus::INCOMPLETE;
                if ($st == 'blocked') $col_val = ProductStatus::BLOCKED;
                if ($st == 'pending') {
                    $query->where('vk_price', ">", 0)->where('status', 0)->where('is_dublicate', 0);
                }else if ($st == 'deletable') {
                    $query->where('is_dublicate',0)
                        ->where(function ($query) {
                        $query->where(function ($query) {
                            $query->where('shipping_method', 1)
                                ->where('stock', 0);
                        });
                    });
                }else if ($st == 'ean') {
                    $query->where('is_dublicate', 1);

                } else if ($col_val == 3) {
                    $query->where('vk_price', "<=", 0)->where('is_dublicate', 0);
                } else if ($st == 'waiting_sync') {
                    $query->where('status', ProductStatus::WAITING_SYNC)->where('internel_sync_status', ProductStatus::WAITING_SYNC);
                } else {
                    $query->where('status', $col_val)->where('is_dublicate', 0);
                }
            }
            Session::put('filter_status_' . CRUDBooster::myParentId(), Request::get('status'));

        } else if (Session::has('filter_status_' . CRUDBooster::myParentId())) {
            $st = Session::get('filter_status_' . CRUDBooster::myParentId());
            if ($st != 'all') {
                if ($st == 'pending') $col_val = 0;
                if ($st == 'approved') $col_val = 1;
                if ($st == 'rejected') $col_val = 2;
                if ($st == 'incomplete') $col_val = 3;
                if ($st == 'blocked') $col_val = 4;
                if ($st == 'pending') {
                    $query->where('vk_price', ">", 0)->where('status', 0)->where('is_dublicate', 0);
                }else if ($st == 'deletable') {
                    $query->where('is_dublicate',0)
                        ->where(function ($query) {
                        $query->where(function ($query) {
                            $query->where('shipping_method', 1)
                                ->where('stock', 0);
                        });
                    });
                }else if ($st == 'ean') {
                    $query->where('is_dublicate', 1);

                } else if ($col_val == 3) {
                    $query->where('vk_price', "<=", 0)->where('is_dublicate', 0);
                } else if ($st == 'waiting_sync') {
                    $query->where('status', ProductStatus::WAITING_SYNC)->where('internel_sync_status', ProductStatus::WAITING_SYNC);
                } else {
                    $query->where('status', $col_val)->where('is_dublicate', 0);
                }
            } else {
                $query = $query;
            }
        }

        //FILTER by country
        if (Request::get('country_id')) {
            $query->where('country_id', Request::get('country_id'));
            Session::put('filter_country_' . CRUDBooster::myParentId(), Request::get('country_id'));
        } else if (Session::has('filter_country_' . CRUDBooster::myParentId())) {
            $country_id = Session::get('filter_country_' . CRUDBooster::myParentId());
            $query->where('country_id', $country_id);
        } else {
            Session::put('filter_country_' . CRUDBooster::myParentId(), 1);
            $query->where('country_id', 1);
        }

        //FILTER by Supllier
        if ($_REQUEST['supplier'] != "") {
            $deliveryCompany = \App\DeliveryCompany::find($_REQUEST['supplier']);

            if ($deliveryCompany->is_marketplace_supplier && $deliveryCompany->supplier_id) {
                $query->where('supplier_id', $deliveryCompany->supplier_id);
            } else {
                $query->where('delivery_company_id', $_REQUEST['supplier']);
            }
        }

        //For Supplier Account
        if (\CRUDBooster::isSupplier()) {
            $mp_supplier = Supplier::where('cms_user_id', \CRUDBooster::myParentId())->first();
            if ($mp_supplier) {
                $query->where('supplier_id', $mp_supplier->parent_id);
            } else {
                $query->where('supplier_id', \CRUDBooster::myParentId());
            }
        }

        // if (isPatrickSpecial()) {
        //     $query = $query->whereNotNull('api_id')->orWhere('shipping_method', 2);
        // } else if (CRUDBooster::isDropMatrix()) {
        //     $query = $query->whereNull('api_id');
        // }

        // START:: Filter & search execution
        if ($_REQUEST['filter_by'] != "") {
            $filterBy = $_REQUEST['filter_by'];
            if ($filterBy == 'api_category_id') {
                return $query->where('api_id', session()->get('selected_api_category'))->where('api_category_id', $_REQUEST['api_category_id'])->get();
            }
            // else if ($filterBy == 'ean') {
            //     $query->where('status', 1)->where('is_dublicate', 1)->where('best_price', 1);
            // }
            else if ($filterBy == 'not_in_fulfillment_inventory') {
                $query->where('shipping_method', 2)->where(function ($query) {
                    return $query->where('internel_stock', 0)->where('old_internel_stock', 0)
                        ->orWhere('marketplace_product_id', null);
                });
            } else if ($filterBy == 'low_stock_product') {
                $query->where('shipping_method', 2)->where('alarm_status', 1);
            } else if ($filterBy == 'visibility_light') {
                if (request()->visibility_light == 'red') {
                    $query->where('status', '!=', 1);
                } else if (request()->visibility_light == 'green') {
                    $query->where('status', 1)->whereIn('id', function ($q) {
                        $q->select('marketplace_product_id')->from('mp_core_drm_transfer_products');
                    });
                } else if (request()->visibility_light == 'yellow') {
                    $query->where('status', 1)->whereNotIn('id', function ($q) {
                        $q->select('marketplace_product_id')->from('mp_core_drm_transfer_products');
                    });

                    //->whereRaw('id NOT IN(".$test.")')->get();
                }
            } else if ($filterBy == 'category_id') { //here use session because understanding its sub cat id or main cat id
                if (\CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()) {
                    if (strpos($_REQUEST['category_id'], "_cat")) {
                        $categoryId = explode("main_cat_", $_REQUEST['category_id'])[1];
                        $categoryList = Category::where('parent_id', $categoryId)->where('parent_id', '!=', null)->pluck('id')->toArray();
                        Session::put('filter_category_is_main_' . CRUDBooster::myParentId(), $categoryId);
                        Session::forget('filter_category_is_sub_' . CRUDBooster::myParentId());
                        $query->whereIn($filterBy, $categoryList);
                    } else {
                        Session::put('filter_category_is_sub_' . CRUDBooster::myParentId(), $_REQUEST['category_id']);
                        Session::forget('filter_category_is_main_' . CRUDBooster::myParentId());
                        $query->where($filterBy, $_REQUEST[$filterBy]);
                    }
                }
            } else {
                $query->where($filterBy, $_REQUEST[$filterBy]);
            }
        }

        $searchCategory = $_REQUEST['search_by_select_category'] ?? '';
        $searchCategoryId = $_REQUEST['search_by_category_id'] ?? '';

        if ($searchCategory && !in_array($searchCategory, ['visibility_light', 'low_stock_product']) && $searchCategoryId) {
            if ($searchCategory == 'category_id' && (\CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport())) {
                if (strpos($searchCategoryId, "_cat")) {
                    $categoryId = explode("main_cat_", $searchCategoryId)[1];
                    $categoryList = Category::where('parent_id', $categoryId)->whereNotNull('parent_id')->pluck('id')->toArray();
                    Session::put('filter_category_is_main_' . CRUDBooster::myParentId(), $categoryId);
                    Session::forget('filter_category_is_sub_' . CRUDBooster::myParentId());
                    $query->whereIn($searchCategory, $categoryList);
                } else {
                    Session::put('filter_category_is_sub_' . CRUDBooster::myParentId(), $searchCategoryId);
                    Session::forget('filter_category_is_main_' . CRUDBooster::myParentId());
                    $query->where($searchCategory, session('filter_by_input'));
                }
            } else {
                $query->where($searchCategory, session('filter_by_input'));
            }
        }

        // Build query For Search
        if ($_REQUEST['search_by_select'] != null && $_REQUEST['search_by_query']) {
            if ($_REQUEST['search_by_select'] == 'ean') {
                $ean = $_REQUEST['search_by_query'] ?? '';
                $query->whereJsonContains('additional_eans',$ean)->orWhere('ean', $ean)->where(function ($query) {
                    return $query->where('is_dublicate', 1)->where('best_price', 1)
                        ->orWhere('is_dublicate', 1)->where('best_price', 0)
                        ->orWhere('is_dublicate', 0)->where('best_price', 0);
                });
            } else {
                $search_by_query = trim($_REQUEST['search_by_query']);
                if ($_REQUEST['search_by_select'] == 'item_number') {
                    $query->where('item_number', $search_by_query);
                } else {
                    if($search_by_query == 'all'){
                        $query = $query->where($_REQUEST['search_by_select'], '!=', NULL)->where($_REQUEST['search_by_select'], '!=', '');
                    } else {
                        $query = app(ProductService::class)->buildQueryBySearch($query, $_REQUEST['search_by_select'], $search_by_query);
                    }
                }
            }
        }

        // Build query for Searching with min and max value
        if (!blank($_REQUEST['search_by_select']) && !blank($_REQUEST['search_by_query_min']) && !blank($_REQUEST['search_by_query_max'])) {
            $search_by_query_min = is_numeric($_REQUEST['search_by_query_min']) ? trim($_REQUEST['search_by_query_min']) : null;
            $search_by_query_max = is_numeric($_REQUEST['search_by_query_max']) ? trim($_REQUEST['search_by_query_max']) : null;

            if ($_REQUEST['search_by_select'] == 'profit' && !\CRUDBooster::isSupplier()) {
                $query->whereRaw("ROUND(((vk_price - ek_price) / ek_price * 100), 2) BETWEEN $search_by_query_min AND $search_by_query_max");
            } else if ($_REQUEST['search_by_select'] == 'stock'){
                $search_by_query_min = (int)$search_by_query_min;
                $search_by_query_max = (int)$search_by_query_max;
                $searchColumn = $searchCategoryId == 2 ? 'internel_stock' : 'stock';
                if($searchCategory == 'shipping_method'){
                    $query->whereBetween($searchColumn, [$search_by_query_min, $search_by_query_max]);
                } else {
                    $query->where(function ($query) use ($search_by_query_min, $search_by_query_max) {
                        $query->where('shipping_method', 1)
                              ->whereBetween('stock', [$search_by_query_min, $search_by_query_max])
                              ->orWhere(function ($query) use ($search_by_query_min, $search_by_query_max) {
                                  $query->where('shipping_method', 2)
                                        ->whereBetween('internel_stock', [$search_by_query_min, $search_by_query_max]);
                              });
                    });
                }
            } else {
                $query->whereBetween($_REQUEST['search_by_select'], [$search_by_query_min, $search_by_query_max]);
            }
        } else if(!blank($_REQUEST['search_by_select']) && (!blank($_REQUEST['search_by_query_min']) || !blank($_REQUEST['search_by_query_max']))){
            $search_by_query_min = is_numeric($_REQUEST['search_by_query_min']) ? trim($_REQUEST['search_by_query_min']) : null;
            $search_by_query_max = is_numeric($_REQUEST['search_by_query_max']) ? trim($_REQUEST['search_by_query_max']) : null;

            if ($_REQUEST['search_by_select'] == 'profit' && !\CRUDBooster::isSupplier()) {
                $operator = $search_by_query_min ? '>=' : '<=';
                $searchValue = $search_by_query_min ?: $search_by_query_max;
                $query->whereRaw("round(((vk_price - ek_price) / ek_price * 100), 2) $operator $searchValue");
                
            } else if ($_REQUEST['search_by_select'] == 'stock') {
                $searchValue = $search_by_query_min ?: $search_by_query_max;
                if ($searchCategory == 'shipping_method') {
                    $searchColumn = $searchCategoryId == 2 ? 'internel_stock' : 'stock';
                    $query->where($searchColumn, (int)$searchValue);
                } else {   
                    $query->where(function ($query) use ($searchValue) {
                        $query->where('shipping_method', 1)
                              ->where('stock', (int)$searchValue)
                              ->orWhere(function ($query) use ($searchValue) {
                                  $query->where('shipping_method', 2)
                                        ->where('internel_stock', (int)$searchValue);
                              });
                    });
                          
                }
            } else {
                $condition = $search_by_query_min ? ['>=', $search_by_query_min] : ['<=', $search_by_query_max];
                $query->where($_REQUEST['search_by_select'], $condition[0], $condition[1]);                
            }
        }

        // Sorting by column
        if (Request::get('filter_column')) {
            $query = app(ProductService::class)->buildQueryByFilterColumnSorting($query, Request::get('filter_column'));
        }
        
        return $query;

    }
    // Old
    // function checkCategoryDrmTransfer()
    // {

    //     $productIds = [];
    //     $all_products = [];
    //     $categories = [];
    //     if(request()->selected_all == 1){
    //         $all_products = $this->getTranferableProducts()->get();
    //         $productIds = array_diff($all_products->pluck('id')->toArray(), request()->product_ids ?? []);
    //         $categories = array_unique($all_products->pluck('category_id')->toArray());//Product::whereIn('id', $productIds)->select(DB::raw('DISTINCT category_id'))->pluck('category_id')->toArray();
    //     } else {
    //         $productIds = request()->product_ids;
    //         $categories = array_unique(Product::whereIn('id', $productIds)->select(DB::raw('DISTINCT category_id'))->pluck('category_id')->toArray());
    //     }


    //     $userId = request()->customer_id;
    //     $user = User::where('id', $userId)->first();
    //     $totalAllowedCategories = $user->category_quantity ?? 10;

    //     $userAccessStatus = \App\Models\Marketplace\UserAccess::where('user_id', $userId)->first(['accessable_categories'])->accessable_categories ?? [];
    //     $productCategories = array_diff($categories, $userAccessStatus);

    //     $totalCategories = count($userAccessStatus) + count($productCategories);
    //     if ($totalCategories > $totalAllowedCategories) {
    //         if ($totalAllowedCategories <= count($userAccessStatus)) {
    //             return response()->json([
    //                 'status' => 204,
    //                 'category_ids' => $userAccessStatus,
    //                 'message'=> 'Category Limit Exceeds of the user .'
    //             ]);
    //         } else {
    //             $counter =  $totalAllowedCategories - count($userAccessStatus);
    //             $newCategories = array_slice(array_unique($productCategories), 0, $counter);
    //             return response()->json([
    //                 'status' => 204,
    //                 'category_ids' => array_merge($newCategories, $userAccessStatus),
    //                 'message'=> 'Category Limit Exceeds of the user .'
    //             ]);
    //         }
    //     } else {
    //         return response()->json([
    //             'status' => 200,
    //             'category_ids' => array_unique($categories),
    //             'message'=> 'Okay'
    //         ]);
    //     }
    // }

    function checkCategoryDrmTransfer()
    {
        // if(request()->customer_id == 212){
            $productIds = [];
            $all_products = [];
            $categoryIds = [];
            if(request()->selected_all == 1){
                $all_products = $this->getTranferableProducts()->get();
                $productIds = array_diff($all_products->pluck('id')->toArray(), request()->product_ids ?? []);
                $categoryIds = array_unique($all_products->pluck('category_id')->toArray());//Product::whereIn('id', $productIds)->select(DB::raw('DISTINCT category_id'))->pluck('category_id')->toArray();
            } else {
                $productIds = request()->product_ids;
                $categoryIds = array_unique(Product::whereIn('id', $productIds)->select(DB::raw('DISTINCT category_id'))->pluck('category_id')->toArray());
            }


            $userId = request()->customer_id;
            // $user = User::where('id', $userId)->first();
            $totalAllowedCategories = app('App\Http\Controllers\AdminMarketplaceCategoriesController')->userMpCategoryLimit($userId);
            $requestParentCategories = array_unique(Category::whereIn('id', $categoryIds)->pluck('parent_id')->toArray()) ?? [];

            $accessableParentCategories = \App\Models\Marketplace\UserAccess::where('user_id', $userId)->first('accessable_parent_categories')->accessable_parent_categories ?? [];
            $productCategories = array_diff($requestParentCategories, $accessableParentCategories);
            $totalCategories = count($accessableParentCategories) + count($productCategories);

            if ($totalCategories > $totalAllowedCategories) {
                if ($totalAllowedCategories <= count($accessableParentCategories)) {
                    return response()->json([
                        'status' => 204,
                        'category_ids' => implode(',', $accessableParentCategories),
                        'message'=> 'Category Limit Exceeds of the user .'
                    ]);
                } else {
                    $counter =  $totalAllowedCategories - count($accessableParentCategories);
                    $newCategories = array_slice($productCategories, 0, $counter);
                    return response()->json([
                        'status' => 204,
                        'category_ids' => implode(',', array_merge($newCategories, $accessableParentCategories)),
                        'message'=> 'Category Limit Exceeds of the user .'
                    ]);
                }
            } else {
                return response()->json([
                    'status' => 200,
                    'category_ids' => implode(',',array_unique($requestParentCategories)),
                    'message'=> 'Okay'
                ]);
            }
        // }
    }

    function postCheckCategoryLimitWhenCategoryChange()
    {
        $productIds = [];
        $all_products = [];
        $categoryIds = [];

        $productIds = explode(',', request()->product_ids);
        $categoryIds = array_unique(Product::whereIn('id', $productIds)->select(DB::raw('DISTINCT category_id'))->pluck('category_id')->toArray());

        $userId = request()->customer_id;
        // $user = User::where('id', $userId)->first();
        $totalAllowedCategories = app('App\Http\Controllers\AdminMarketplaceCategoriesController')->userMpCategoryLimit($userId);
        $requestParentCategories = array_unique(Category::whereIn('id', $categoryIds)->pluck('parent_id')->toArray()) ?? [];

        $accessableParentCategories = \App\Models\Marketplace\UserAccess::where('user_id', $userId)->first('accessable_parent_categories')->accessable_parent_categories ?? [];
        $productCategories = array_diff($requestParentCategories, $accessableParentCategories);
        $totalCategories = count($accessableParentCategories) + count($productCategories);

        if ($totalCategories > $totalAllowedCategories) {
            if ($totalAllowedCategories <= count($accessableParentCategories)) {
                return response()->json([
                    'status' => 204,
                    'category_ids' => implode(',', $accessableParentCategories),
                    'message'=> 'Category Limit Exceeds of the user .'
                ]);
            } else {
                $counter =  $totalAllowedCategories - count($accessableParentCategories);
                $newCategories = array_slice($productCategories, 0, $counter);
                return response()->json([
                    'status' => 204,
                    'category_ids' => implode(',', array_merge($newCategories, $accessableParentCategories)),
                    'message'=> 'Category Limit Exceeds of the user .'
                ]);
            }
        } else {
            return response()->json([
                'status' => 200,
                'category_ids' => implode(',',array_unique($requestParentCategories)),
                'message'=> 'Okay'
            ]);
        }
    }

    public function postTransferProductsToDrmWhenCategoryChange()
    {
       try {
            $productIds = [];
            $all_products = [];
            $user_id = request()->customer_id;
            $parent_categories = array_map('intval', explode(',', request()->category_ids)) ?? [];
            $categories = Category::select('id', 'parent_id')->whereIn('parent_id', $parent_categories)->get() ?? [];
            $request_product_ids = explode(',', request()->product_ids) ?? [];
            $country_id = (int)(Session::get('filter_country_' . CRUDBooster::myParentId()) ?? 1);

            $all_products = Product::select('id', 'ean', 'api_id', 'country_id')
                ->where('status',1)
                ->whereIn('id',$request_product_ids)
                ->whereIn('category_id', $categories->pluck('id')->toArray() ?? [])
                ->where(function ($query) {
                    $query->where('api_id', '!=', 5)
                          ->orWhereNotIn('country_id', [8, 83]);
                })
                ->get();
            $productIds = $all_products->pluck('id')->toArray();

            if(empty($productIds)){
                return [
                    'success' => false,
                    'message' => "Products are not approved or the limit of categories already Over.",
                ];
            }

            foreach( array_chunk($productIds, 10000) as $chunk_ids ){

                if(isset(\App\Enums\V2UserAccess::USERS[$user_id])){

                    $id_exists = DB::connection('marketplace')->table('marketplace_transferred_products_in_drm')
                        ->where('user_id', $user_id)
                        ->where('country_id', $country_id)
                        ->whereIn('marketplace_product_id', $chunk_ids)
                        ->pluck('marketplace_product_id')
                        ->toArray();
                }else{
                    $id_exists = DrmProduct::select('marketplace_product_id')
                    ->where('user_id', $user_id)
                    ->whereIn('marketplace_product_id', $chunk_ids)
                    ->get()
                    ->pluck('marketplace_product_id')
                    ->toArray();
                }

                $tranferable_ids[] = array_diff($chunk_ids, $id_exists);
            }

            $tranferable_ids = array_merge(...$tranferable_ids);

            if(empty($tranferable_ids)){
                return [
                    'success' => false,
                    'message' => "Products already transfered to drm. Please select new products.",
                ];
            }

            $importProduct = app(\App\Http\Controllers\AdminDrmImportsController::class)->importProductCheck($user_id);
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;
            $transferPlan = $importProduct['plan'] ? $importProduct['plan'] : null;
            $transferPlanLimit = $importProduct['limit'] ? $importProduct['limit'] : null;

            if((($transferPlan && $transferPlan == 'Trial') && ($transferPlanLimit && $transferPlanLimit == 'Unlimited')) || ($transferPlanLimit && $transferPlanLimit == 'Unlimited')){
                $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $user_id)->first();
                $product_cats = array_diff($parent_categories, $user_access_status->accessable_parent_categories ?? []);
                $newArray = [];
                $allowedCategories = $parent_categories;
                if(!empty($allowedCategories)){
                    $all_access_category = UserAccess::where('user_id', $user_id)
                                                ->select('check_accessable_categories')
                                                ->get()->toArray();
                    if(!empty($all_access_category)){
                        $single_access_category = array_column($all_access_category[0]['check_accessable_categories'], 'accessable_categories');

                        $single_access_category_user = array_column($all_access_category[0]['check_accessable_categories'], 'set_by_admin');

                        foreach($allowedCategories as $category){
                            if(!in_array($category,$single_access_category)){
                                $newArray[] = array("accessable_categories"=>$category,"set_by_admin"=>0);
                            }else{
                                $index = array_keys($single_access_category, $category);
                                $val = $single_access_category_user[$index[0]];
                                $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>$val);
                            }
                        }
                        foreach($single_access_category as $key => $old_access){
                            if(!in_array($old_access,$allowedCategories)){
                            $val = $single_access_category_user[$key];
                            if($val == 0){
                                $newArray[] = array("accessable_categories"=>$old_access,"set_by_admin"=>$val);
                            }
                            }
                        }
                    }else{
                        foreach($allowedCategories as $category){
                            $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>0);
                        }
                    }
                }

                if(!empty($product_cats)){
                    $new_merge = array_unique(array_merge($user_access_status->accessable_parent_categories ?? [], $product_cats));
                    $sub_category = array_unique(array_merge($user_access_status->accessable_categories ?? [], $categories->whereIn('parent_id', $product_cats)->pluck('id')->toArray() ?? []));

                }else{
                    $new_merge = $user_access_status->accessable_parent_categories;
                    $sub_category = $user_access_status->accessable_categories;
                }

                if(!empty($newArray)){
                    $attributes =  array_merge([
                        'user_id'           => $user_id,
                        'status'            => 1,
                    ],['accessable_categories' => $sub_category], ['accessable_parent_categories' => $new_merge], ['check_accessable_categories' => $newArray]);

                    $newRow = UserAccess::updateOrCreate(['user_id'=>$user_id], $attributes);
                }
                // if(count($tranferable_ids) > 150){
                    foreach( array_chunk($tranferable_ids, 500) as $tranferable_id ){
                        Log::info("MP to DRM unlimited product transfer job dispatch");
                        dispatch(new MarketplaceProductTransferToDrm($tranferable_id, $user_id, null, true, $country_id))->onQueue('mp_product_trans'); //->onQueue('keepa');
                    }
                    return [
                        'status' => true,
                        'message' => 'Your selected ' .count($tranferable_ids) . ' products transferred on going on the background process. It may take some time to transfer all products.',
                    ];
                // } else {
                //     return app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferUnlimitedProductsToDrm($tranferable_ids, $user_id);
                // }
            } else if($transferLimit && $transferLimit > 0) {
                $user_access_status = \App\Models\Marketplace\UserAccess::where('user_id', $user_id)->first();
                $product_cats = array_diff($parent_categories, $user_access_status->accessable_parent_categories ?? []);
                $newArray = [];
                $allowedCategories = $parent_categories;
                if(!empty($allowedCategories)){
                    $all_access_category = UserAccess::where('user_id', $user_id)
                                                ->select('check_accessable_categories')
                                                ->get()->toArray();
                    if(!empty($all_access_category)){
                        $single_access_category = array_column($all_access_category[0]['check_accessable_categories'], 'accessable_categories');

                        $single_access_category_user = array_column($all_access_category[0]['check_accessable_categories'], 'set_by_admin');

                        foreach($allowedCategories as $category){
                            if(!in_array($category,$single_access_category)){
                                $newArray[] = array("accessable_categories"=>$category,"set_by_admin"=>0);
                            }else{
                                $index = array_keys($single_access_category, $category);
                                $val = $single_access_category_user[$index[0]];
                                $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>$val);
                            }
                        }
                        foreach($single_access_category as $key => $old_access){
                            if(!in_array($old_access,$allowedCategories)){
                            $val = $single_access_category_user[$key];
                            if($val == 0){
                                $newArray[] = array("accessable_categories"=>$old_access,"set_by_admin"=>$val);
                            }
                            }
                        }
                    }else{
                        foreach($allowedCategories as $category){
                            $newArray[] =  array("accessable_categories"=>$category,"set_by_admin"=>0);
                        }
                    }
                }

                if(!empty($product_cats)){
                    $new_merge = array_unique(array_merge($user_access_status->accessable_parent_categories ?? [], $product_cats));
                    $sub_category = array_unique(array_merge($user_access_status->accessable_categories ?? [], $categories->whereIn('parent_id', $product_cats)->pluck('id')->toArray() ?? []));
                }else{
                    $new_merge = $user_access_status->accessable_parent_categories;
                    $sub_category = $user_access_status->accessable_categories;
                }

                if(!empty($newArray)){
                    $attributes =  array_merge([
                        'user_id'           => $user_id,
                        'status'            => 1,
                    ],['accessable_categories' => $sub_category], ['accessable_parent_categories' => $new_merge], ['check_accessable_categories' => $newArray]);

                    $newRow = UserAccess::updateOrCreate(['user_id'=>$user_id], $attributes);
                }

                // if(count($tranferable_ids) > 150){
                    foreach( array_chunk($tranferable_ids, 500) as $tranferable_id ){
                        Log::info("MP to DRM limited product transfer job dispatch");
                        dispatch(new MarketplaceProductTransferToDrm($tranferable_id, $user_id, null, false, $country_id))->onQueue('mp_product_trans'); //->onQueue('keepa');
                    }
                    return [
                        'status' => true,
                        'message' => 'Your selected ' .count($tranferable_ids). ' products transferred on going on the background process. It may take some time to transfer all products.',
                    ];
                // } else {
                //     return app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferTarifLimitedProductsToDrm($tranferable_ids, $user_id);
                // }

            } else {
               return response()->json([
                   'status'      => true,
                   'message'     => 'Your DRM products transfer limit exceed! Please Upgrade your tarif plan.',
               ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status'        => 'error :: '.$e->getMessage(),
                'error_msg'     => 'Something went wrong',
            ]);
        }
    }

    public function postCancelInternelDelivery(){

        $InternelResponse = [];
        $cancelProduct =  \App\Models\Marketplace\Product::select('id','ean','internel_delivery_id')->whereIn('id', request()->product_ids)
                          ->where('status',ProductStatus::WAITING_SYNC)->where('shipping_method',ShippingMethod::FULFILLment);
        $cancelProductsinfo = $cancelProduct->get()->toArray();
        $internelDeliveryResponses = app(InternelSyncService::class)->cancelInternelDelivery($cancelProduct->pluck('id')->toArray());
        if(!empty( $internelDeliveryResponses)){
            $internelDeliveryResponseInfos = array_map(function ($productInfo,$internelResponse) {
                return $productInfo + $internelResponse;
            }, $cancelProductsinfo,$internelDeliveryResponses);
            $html = '';
            foreach ($internelDeliveryResponseInfos as $internelDeliveryResponse) {
                $html .= '<tr>';
                $html .= '<td align="center">' .$internelDeliveryResponse['id'] . '</td>';
                $html .= '<td align="center">' . $internelDeliveryResponse['ean'] . '</td>';
                $html .= '<td align="center">' . $internelDeliveryResponse['ErrorMsg'] . '</td>';
                $html .= '</tr>';
            }
        }
        return response()->json([
            'status' => 200,
            'data'=> $html ?? [],
        ]);
    }

    public function cartForm()
    {
        $id = $_REQUEST['id'];
        $result = Product::with('mainCategory')->where('id',$id)->first();
        $user = User::find(CRUDBooster::myParentId());
        $quantity = $_REQUEST['qty'];
        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }

        $product_discount = 0.0;
        if($result->mainCategory->start_date <= now() && $result->mainCategory->end_date >= now() && $result->mainCategory->is_offer_active){
            $product_discount = $result->mainCategory->discount_percentage;
        }
        if($result->offer_start_date <= now() && $result->offer_end_date >= now() && $result->is_offer_active){
            $product_discount += $result->discount_percentage ? ($result->discount_percentage - $result->discount_percentage/4) : 0.0;
        }

        $qunatity_discount_amount = 0.0;
        if($result->scaled_price_id){
            $vk_price = round(userWiseVkPriceCalculate($result->vk_price ?? 0.00, CRUDBooster::myParentId(), false, $product_discount, $result->api_id) ?? 0.00, 2);
            $qunatity_discount_amount = QuantityWiseMarketplaceScaledPriceDiscount($quantity, $vk_price, $result->scaled_price_id);
        } else if($result->api_id == 4){
            $vk_price = round(userWiseVkPriceCalculate($result->vk_price ?? 0.00, CRUDBooster::myParentId(), false, $product_discount, $result->api_id) ?? 0.00, 2);
            $qunatity_discount_amount = QuantityWiseApiScaledPriceDiscount($result, $quantity, $vk_price, $product_discount);
        }

        $is_upadat_form = false;

        $hasPaypal = app(DRMService::class)->paypalCheck(User::DROPMATIX_ACCOUNT_ID);

        $discountedShipping = 0.00;
        $shipping_cost = $result->shipping_cost;
        $isEnterpriceOrTrialUser = isEnterpriceOrTrialUser($user->id) ? true : false;
        if ($isEnterpriceOrTrialUser) {
            $shipping_cost = $result->real_shipping_cost == 0 ? $result->real_shipping_cost : $shipping_cost;
        }
        //     $discountedShipping = ($quantity >= 2 && $shipping_cost > 0 ? ($shipping_cost * $quantity) - ($shipping_cost + ($quantity -1) * 1.5) : 0.0) ;
        // } else if(professionalOrHigher($user->id)){
        //     $discountedShipping = ($shipping_cost * $quantity) - ($shipping_cost + ($quantity -1) * 2.5);
        // }
        // $discountedShipping = $discountedShipping > 0 ? $discountedShipping : 0.00;
        $countries  = DB::table('tax_rates')
            ->selectRAW("id, country as name, UPPER(country_code) as country_shortcut, charge")
            ->get();

        $paymentTax = new PaymentTax($user->id, 2455);
        $vatNumber = $paymentTax->vatNumber() ?? false;
        $taxRate = $paymentTax->taxRate();

        return view('marketplace.product.product_purchase_form', compact('result', 'plans', 'user', 'quantity', 'term', 'is_upadat_form','qunatity_discount_amount','product_discount', 'hasPaypal', 'vatNumber', 'taxRate', 'discountedShipping', 'shipping_cost'));

    }

    public function preCartForm()
    {
        $id = $_REQUEST['id'];
        $result = Product::where('id',$id)->first();
        $user = User::find(CRUDBooster::myParentId());
        $quantity = $_REQUEST['qty'];
        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }

        $product_discount = 0.0;
        if($result->mainCategory->start_date <= now() && $result->mainCategory->end_date >= now() && $result->mainCategory->is_offer_active){
            $product_discount = $result->mainCategory->discount_percentage;
        }
        if($result->offer_start_date <= now() && $result->offer_end_date >= now() && $result->is_offer_active){
            $product_discount += $result->discount_percentage ? ($result->discount_percentage - $result->discount_percentage/4) : 0.0;
        }

        $qunatity_discount_amount = 0.0;
        if($result->scaled_price_id){
            $vk_price = round(userWiseVkPriceCalculate($result->vk_price ?? 0.00, CRUDBooster::myParentId(), false, $product_discount, $result->api_id) ?? 0.00, 2);
            $qunatity_discount_amount = QuantityWiseMarketplaceScaledPriceDiscount($quantity, $vk_price, $result->scaled_price_id);
        } else if($result->api_id == 4){
            $vk_price = round(userWiseVkPriceCalculate($result->vk_price ?? 0.00, CRUDBooster::myParentId(), false, $product_discount, $result->api_id) ?? 0.00, 2);
            $qunatity_discount_amount = QuantityWiseApiScaledPriceDiscount($result, $quantity, $vk_price, $product_discount);
        }

        $is_upadat_form = false;

        $hasPaypal = app(DRMService::class)->paypalCheck(User::DROPMATIX_ACCOUNT_ID);

        $discountedShipping = 0.00;
        $shipping_cost = $result->shipping_cost;
        $isEnterpriceOrTrialUser = isEnterpriceOrTrialUser($user->id) ? true : false;
        if ($isEnterpriceOrTrialUser) {
            $shipping_cost = $result->real_shipping_cost == 0 ? $result->real_shipping_cost : $shipping_cost;
        }
        //     $discountedShipping = $quantity >= 2 && $shipping_cost > 0 ? (($shipping_cost * $quantity) - ($shipping_cost + ($quantity -1) * 1.5)) : 0.0;
        // } else if(professionalOrHigher($user->id)){
        //     $discountedShipping = ($shipping_cost * $quantity) - ($shipping_cost + ($quantity -1) * 2.5);
        // }
        // $discountedShipping = $discountedShipping > 0 ? $discountedShipping : 0.00;

        $countries  = DB::table('tax_rates')
                ->selectRAW("id, country as name, UPPER(country_code) as country_shortcut, charge")
                ->get();

        $paymentTax = new PaymentTax($user->id, 2455);
        $vatNumber = $paymentTax->vatNumber() ?? false;
        $taxRate = $paymentTax->taxRate();

        return view('marketplace.product.product_pre_purchase_form', compact('result', 'plans', 'user', 'quantity', 'term', 'is_upadat_form', 'qunatity_discount_amount','product_discount', 'hasPaypal', 'vatNumber', 'taxRate', 'discountedShipping', 'shipping_cost'));

    }



    //save direct order
    function saveDirectOrder(Request $req){

        $order = new DirectOrder();
        $order->product_id = request()->product_id;
        $order->user_id = CRUDBooster::myParentId();
        $order->quantity = request()->quantity;
        $order->unit_price = request()->unit_price;
        $order->unit_shipping = request()->unit_shipping;
        $order->total_shipping = request()->total_shipping;
        $order->shipping_discount = request()->shipping_discount;
        $order->total = request()->price;
        $order->save();

        return response()->json($order);

    }

      //save direct order
      function savePreOrder(Request $req){

        $order = new PreOrder();
        $order->product_id = request()->product_id;
        $order->user_id = CRUDBooster::myParentId();
        $order->quantity = request()->quantity;
        $order->unit_price = request()->unit_price;
        $order->unit_shipping = request()->unit_shipping;
        $order->total_shipping = request()->total_shipping;
        $order->shipping_discount = request()->shipping_discount;
        $order->total = request()->price;
        $order->save();

        return response()->json($order);

    }

    public function getIsProductDuplicate()
    {
        $products = request()->products;
        $duplicateProducts = [];
        foreach ($products as  $product) {
              $duplicateProducts[] = [
                 $formate = "'".$product['product_id']. "'".','."'".$product['product_ean']. "'",
                "product_id" =>$product['product_id'],
                "duplicate_menu" => have_dublicate($product['product_ean']) ? '<span  onclick="showDublicateProduct('.$formate.')"><i style="display:inline;cursor:pointer;color:orange" class="fa fa-bars"></i></span>' : '',
              ];
        }
        return response()->json(['data' => $duplicateProducts], 200);
    }

    public function getAssignDiscountProducts()
    {
        try {
            if($_REQUEST['only_products'] == 1){
                $products = $this->getIndex();
                $productIds = $products->pluck('id')->toArray();
                $skipProductIds = $_REQUEST['product_ids'] ? explode(',', $_REQUEST['product_ids']) : [];
                $productIds = array_diff($productIds, $skipProductIds);
            } else {
                $productIds = $_REQUEST['product_ids'] ? explode(',', $_REQUEST['product_ids']) : [];
            }
            if($productIds){
                Product::whereIn('id', $productIds)->update([
                    'is_offer_active' => $_REQUEST['is_offer_active'],
                    'discount_percentage' => $_REQUEST['discount_percentage'],
                    'offer_start_date' => $_REQUEST['start_date'],
                    'offer_end_date' => $_REQUEST['end_date'],
                ]);
            } else {
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Please select at least one product.', 'info');
            }

            $checkExistInDrm = MpCoreDrmTransferProduct::whereIn('marketplace_product_id', $productIds)->get();
            if (count($checkExistInDrm) > 0) {
                dispatch(new MarketplaceProductDiscountSyncToDrm($checkExistInDrm));
            }

            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Discount added successfully.', 'info');
        } catch (\Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.', 'alert');
        }
    }

    public function postBrandChangeBulk()
    {

        $productIds          = [];
        $all_products        = [];
        $selected_brand_id   = request()->selected_brand_id;
        $selected_brand_name = request()->selected_brand_name;
        $selected_brand_logo = request()->selected_brand_logo;
        if(request()->selected_all == 1){
            $all_products = $this->getTranferableProducts()->get();
            $productIds = array_diff($all_products->pluck('id')->toArray(), request()->product_ids ?? []);
        } else {
            $productIds = request()->product_ids;
        }
        if(count($productIds) > 500){
            foreach(array_chunk($productIds,500) as $productId){
                ProductBrandChangeJob::dispatch($productId,$selected_brand_id,$selected_brand_name,$selected_brand_logo)->onQueue('keepa');
            }
            return response()->json(['status' => 'success', 'message' => 'The product brand update process now starts in the background. It may take a few minutes to complete.']);
        } else {
            app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->ProductBrandUpdate($productIds,$selected_brand_id,$selected_brand_name,$selected_brand_logo);
            return response()->json(['status' => 'success', 'message' => 'The product brand updated successfully.']);
        }
    }

    public function getScaledPriceDiscounts()
    {
        $scaled_price_discounts = $this->getUserScaledPriceDiscount(CRUDBooster::myParentId());
        return view('marketplace.product.modals.assign_scaled_price_discount', compact('scaled_price_discounts'));
    }

    public function getUserScaledPriceDiscount($user_id)
    {
        return ScaledPriceDiscountForDirectOrder::where([
            'user_id' => $user_id,
        ])->get();
    }

    public function postAddScaledPriceDiscount(Req $request){

        if(count($request['discount']) > 0 && count($request['quantity']) > 0){
            $quantity_wise_discount = [];
            foreach(array_filter($request['quantity']) as $key => $quantiy){
                $quantity_wise_discount[$quantiy] = $request['discount'][$key];
            }
            ScaledPriceDiscountForDirectOrder::create([
                'name' => $request['name'],
                'user_id' => CRUDBooster::myParentId(),
                'quantity_wise_discount' => $quantity_wise_discount,
            ]);

            return response()->json(['message' => 'Successfully added a scaled price discount.']);
        } else {
            return response()->json(['message' => 'Please set at leat one quntity wise discount.']);
        }
    }

    public function postAssignedScaledPriceDiscount(Req $request){
        try{
            $ids = [];
            if ($request->only_products && $request->only_products == 1) {
                $products = $this->getIndex();
                $ids = $products->pluck('id')->toArray();
                $ids = array_diff($ids, $request->product_ids ?? []);
            } else {
                $ids = $request->product_ids;
            }
            MarketplaceProducts::whereIn('id', $ids)->update(['scaled_price_id' => $request->scaled_price_id]);
            return ['message' => 'Scaled price assign on selected products successfully.'];

        } catch (\Exception $e){
            return ['message' => "Something went wrong !"];
        }
    }

    public function getOpenUpdateScaledPriceDiscount(Req $request){
        try{
            if($request->scaled_price_id){
                $update_discount = ScaledPriceDiscountForDirectOrder::where([
                    'id' => $request->scaled_price_id,
                ])->first();

                return view('marketplace.product.modals.update_scaled_price_discount', compact('update_discount'));
            }
        } catch (\Exception $e){
            return ['message' => "Something went wrong !"];
        }
    }

    public function postUpdateScaledPriceDiscount(Req $request)
    {
        try {
            if(count($request['discount']) > 0 && count($request['quantity']) > 0){
                $quantity_wise_discount = [];
                foreach(array_filter($request['quantity']) as $key => $quantiy){
                    $quantity_wise_discount[$quantiy] = $request['discount'][$key];
                }
                ScaledPriceDiscountForDirectOrder::where('id',$request['scaled_price_id'])->update([
                    'name' => $request['name'],
                    'user_id' => CRUDBooster::myParentId(),
                    'quantity_wise_discount' => $quantity_wise_discount,
                ]);
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Successfully update selected scaled price discount.', 'success');
            }
        } catch (\Throwable $th) {
            return false;
        }
    }

    public function postDeleteScaledPriceDiscount(Req $request)
    {
        try {
            if($request->scaled_price_id){
                ScaledPriceDiscountForDirectOrder::where([
                    'id' => $request->scaled_price_id,
                    'user_id' => CRUDBooster::myParentId(),
                ])->delete();

                return response()->json(['message' => 'Successfully remove selected scaled price.']);
            }
        } catch (Throwable $th) {
            return response()->json(['message' => 'Something went wrong!.']);
        }
    }

    public function getScaledPriceDiscountList(Req $request){
        
        $original_price = $request->vk_price;
        $scaled_price_calculation = [];

        if($request->scaled_price_id){

            $scaled_price_discount_list = ScaledPriceDiscountForDirectOrder::where([
                'id' => $request->scaled_price_id,
            ])->first();

            if ($scaled_price_discount_list && $scaled_price_discount_list->quantity_wise_discount) {
                foreach($scaled_price_discount_list->quantity_wise_discount as $key => $scaled_price_discount){

                    $discount_amount = ($original_price * $scaled_price_discount) / 100;
                    $scaled_price_calculation[$key] = [
                        'discount' => $scaled_price_discount,
                        'discounted_price' => round($original_price - $discount_amount, 2),
                    ];
                    $discount_amount = 0.0;
                }
            } 
        }

        if( blank($scaled_price_calculation) && $request->item_number){
            $scal_prices = MpApiScaledPrice::where([
                'item_number' => $request->item_number,
            ])->get();
            
            foreach ($scal_prices as $scal_price) {

                $calculated_price = userWiseVkPriceCalculate($scal_price->price * 1.10, CRUDBooster::myParentId(), false, $request->discount ?? 0);
                $discount_percentage = (($original_price - $calculated_price) / $original_price) * 50;

                $new_discount_price = $calculated_price + ($calculated_price * $discount_percentage / 100);
                $final_discount_percentage = (($original_price - $new_discount_price) / $original_price) * 100;
    
                $scaled_price_calculation[$scal_price->stock] = [
                    'discount' => number_format($final_discount_percentage, 2),
                    'discounted_price' => $new_discount_price
                ];
            }
        }

        return view('marketplace.product.modals.list_of_scaled_price_discount', compact('scaled_price_calculation', 'original_price'));
    }
    
    public function getShowPlushConfirmationProductsOverview(Req $request){

        if($request->product_ids){
            $products = Product::whereIn('id',$request->product_ids)->where('status', 1)
                ->where('atw', '>', 0)
                ->where('shipping_method', ShippingMethod::FULFILLment)
                ->select('name','ean','image', 'atw')
                ->get()
                ->toArray();

            return view('marketplace.product.modals.plush_overview', compact('products'));
        }

    }

    public function getApplyProductCubicMeter(Req $request){
        $productIds = [];
        $all_products = [];
        if (request()->selected_all == 1) {
            $all_products = $this->getTranferableProducts()->get();
            $productIds = array_diff($all_products->pluck('id')->toArray(), request()->product_ids ?? []);
        } else {
            $productIds = request()->product_ids;
        }
        $marketplaceProducts = Product::has('additionalInfo')->with('additionalInfo:product_id,product_length,product_width,product_height')
            ->whereIn('id', $productIds)->where('status', ProductStatus::ACTIVE)->where('internel_stock','>',0)
            ->where('shipping_method', ShippingMethod::FULFILLment)->select('id', 'internel_stock')->get();
        if(!blank($marketplaceProducts)){
            foreach($marketplaceProducts as $product){
                 $calculateCubicMeter = (($product->additionalInfo->product_length ?? 0) * ($product->additionalInfo->product_width ?? 0) * ($product->additionalInfo->product_height ?? 0))/1000000;
                 $totalCalculateCubicMeter = $calculateCubicMeter * $product->internel_stock;
                 $product->update(['cubic_meters'=> $totalCalculateCubicMeter]);
            }
            return response()->json(['status' => 200,'message' => __('marketplace.cubic_meter_apply_success')]);
        }else{
            return response()->json(['status'=>204,'message' => __('marketplace.select_fulfillment_products')]);
        }
    }

    public function getCalculateProductCubicMeter(){
        $productIds = [];
        $all_products = [];
        if (request()->selected_all == 1) {
            $all_products = $this->getTranferableProducts()->get();
            $productIds = array_diff($all_products->pluck('id')->toArray(), request()->product_ids ?? []);
        } else {
            $productIds = request()->product_ids;
        }
        $calculateProducts = Product::whereIn('id', $productIds)
        ->where('status', ProductStatus::ACTIVE)
        ->where('cubic_meters', '>', 0)
        ->where('shipping_method', ShippingMethod::FULFILLment);

        $productCount = $calculateProducts->count();  // Get the count of products
        $totalCubicMeters = $calculateProducts->sum('cubic_meters');  // Get the sum of cubic meters
        if ($calculateProducts != 0) {
            return response()->json([
                'status' => 200,
                'message' => 'Total selected products : '.count($productIds).'/ '.$productCount.'.Total Cubic Meter = '. $totalCubicMeters.''
                ]);
        } else {
            return response()->json(['status' => 204, 'message' => __('marketplace.select_calculate_fulfillment_products')]);
        }
    }

    public function getTransferredProductDetail()
    {
        $ids = request()->productIds;
        $products = [];
        foreach ($ids as $id) {
            $transfer_detail = $this->productService->transferredProductDetail($id);
            $products[] = [
                "product_id" => $id,
                "product_detail" => $transfer_detail,
            ];
        }
        return response()->json(['data' => $products], 200);
    }

    public function getProductMissingInfo()
    {
        $ids = request()->productIds;
        $product = Product::find($ids);
        
        $fields = ['name' => 'mp_Name', 'description' => 'mp_Description', 'image' => 'mp_Image', 
               'vk_price' => 'VK Price', 'category_id' => 'Category', 'brand' => 'mp_Brand', 'shipping_cost' => 'mp_Shipping Cost', 'uvp' => 'mp_Uvp', 'vat' => 'mp_Vat', 'delivery_days' => 'mp_handling_time'];

        if($product->shipping_method == 2){
            $fields['internel_stock'] = 'mp_Stock';
        } else {
            $fields['stock'] = 'mp_Stock';
        }
        
        $html = '';
        $html .= "<li class='list-group-item d-flex justify-content-between align-items-center'></li>";
        foreach ($fields as $field => $label) {
            if($field == 'vk_price' || $field == 'stock' || $field == 'internel_stock' || $field == 'uvp' || $field == 'vat' || $field == 'category_id' || $field == 'brand' || $field == 'shipping_cost' || $field == 'delivery_days'){
                $iconClass = !blank($product->{$field}) && $product->{$field} > 0 ? 'fa-check' : 'fa-times';
                $color = !blank($product->{$field}) && $product->{$field} > 0 ? 'green' : 'red';
            } else {
                $iconClass = !blank($product->{$field}) ? 'fa-check' : 'fa-times';
                $color = !blank($product->{$field}) ? 'green' : 'red';
            }

            $html .= "<li class='list-group-item d-flex justify-content-between align-items-center'>" . __($label) . "<i class='fa $iconClass pull-right' style='color:$color'></i></li>";
        }

        return response()->json(['html' => $html], 200);
    }

    public function newVkPriceCalculation($product)
    {
        if(isset($product->calculation_id) && $product->calculation_id > 0){
            $calculation = MarketplaceProfitCalculation::find($product->calculation_id);
        }else{
            $calculation = MarketplaceProfitCalculation::find(24);
        }

        return $this->calculatePrice($product->ek_price, $calculation, $product->uvp, $product->shipping_cost);
    }

    public function manualRealShippingCostUpdate($product, $column_name)
    {
        $vk_price = $column_name == "vk_price" ? $product->vk_price : $this->newVkPriceCalculation($product);

        $real_shipping_cost = $product->real_shipping_cost * 1.10;
        if ($real_shipping_cost > 35 && $column_name != "vk_price") {
            $vk_price += removeCommaFromPrice($real_shipping_cost) - 35;
            $shipping_cost = 35;
        } else {
            $shipping_cost = $real_shipping_cost;
        }

        $im_handel = $product->mainCategory->im_handel > 0
            ? $vk_price + (($vk_price * $product->mainCategory->im_handel) / 100)
            : ($product->im_handel ?? 0);

        $product->update([
            'shipping_cost' => $shipping_cost,
            'vk_price' => $vk_price,
            'im_handel' => $im_handel,
        ]);

        return true;
    }

    public function getEkPriceInfo(){

        $productIds = request()->product_id ?? '';
        
        $message = '';
        $title = __('drm_import.stocks_not_updated');
        if(!blank($productIds)){
            $product = Product::where('id',$productIds)
                                ->where('api_id',4)
                                ->select(
                                    'ek_price',
                                    'ek_price_updated_at',
                                    'old_ek_price',
                                    'api_product_id',
                                    'item_number'
                                    )
                                ->first();
            
            if ($product->ek_price != null && $product->ek_price_updated_at) {
                $diff = $product->ek_price - $product->old_ek_price;
                
                $message = "<h4> Previous Ek Price : ".$product->old_ek_price."</h4>"
                            . "<h4> New Ek Price : ".$product->ek_price."</h4>"
                            . "<h4> Last update : ".$product->ek_price_updated_at."</h4>";

                if ($diff > 0) {
                    $title = "Ek Price Increased";
                } elseif ($diff < 0) {
                    $title = "Ek Price Decreased";
                } else{
                    $title = __('drm_import.stocks_not_updated');
                }
            }

            $api_scal_price = DB::connection('marketplace')->table('mp_api_scaled_prices')
                            ->where('api_id',4)
                            ->where('item_number', $product->item_number)
                            ->get();
                            
            if($api_scal_price->count() > 0){
                $scal_html = '';
                foreach($api_scal_price as $scal_price){

                    $discount_percentage = (($product->ek_price - $scal_price->price) / $product->ek_price) * 100;
                    $discount_percentage = number_format($discount_percentage, 2);

                    $scal_html .= "<tr><td>$scal_price->stock</td><td>$discount_percentage %</td><td>$product->ek_price</td><td>$scal_price->price</td></tr>";
                }

                $message .= "</br><span class='text-black'><strong>Quantity Wise Discount Price List</strong></span><table class='table text-center'><thead><tr><th>Quantity</th><th>Discount</th><th>Original Price</th><th>Discounted Price</th></tr></thead><tbody>$scal_html</tbody></table>";
            }
        }
        
        return response()->json(['title'=> $title, 'message' => $message,],200);
    }

    public function getVkPriceInfo(){

        $productIds = request()->product_id ?? '';
        
        $message = '';
        $title = __('drm_import.stocks_not_updated');
        if(!blank($productIds)){
            $product = Product::where('id',$productIds)
                                ->where('api_id',4)
                                ->select(
                                    'vk_price',
                                    'vk_price_updated_at',
                                    'old_ek_price',
                                    'api_product_id',
                                    'item_number'
                                    )
                                ->first();
            
            if ($product->vk_price != null && $product->vk_price_updated_at) {
                $diff = $product->vk_price - $product->old_ek_price;
                
                $message = "<h4> Previous Vk Price : ".$product->old_ek_price."</h4>"
                            . "<h4> New Vk Price : ".$product->vk_price."</h4>"
                            . "<h4> Last update : ".$product->vk_price_updated_at."</h4>";

                if ($diff > 0) {
                    $title = "Vk Price Increased";
                } elseif ($diff < 0) {
                    $title = "Vk Price Decreased";
                } else{
                    $title = __('drm_import.stocks_not_updated');
                }
            }

            $api_scal_price = DB::connection('marketplace')->table('mp_api_scaled_prices')
                            ->where('api_id',4)
                            ->where('item_number', $product->item_number)
                            ->get();
                            
            if($api_scal_price->count() > 0){
                $scal_html = '';
                foreach($api_scal_price as $scal_price){
                    $api_vk = $scal_price->price * 1.10;
                    $discount_percentage = (($product->vk_price - $api_vk) / $product->vk_price) * 50;
                    $new_discount_price  = $api_vk + ($api_vk * $discount_percentage / 100);
                    $final_discount_percentage = number_format((($product->vk_price - $new_discount_price) / $product->vk_price) * 100, 2);

                    $scal_html .= "<tr><td>$scal_price->stock</td><td>$final_discount_percentage %</td><td>$product->vk_price</td><td>".number_format($new_discount_price, 2)."</td></tr>";
                }

                $message .= "</br><span class='text-black'><strong>Quantity Wise Discount Price List</strong></span><table class='table text-center'><thead><tr><th>Quantity</th><th>Discount</th><th>Original Price</th><th>Discounted Price</th></tr></thead><tbody>$scal_html</tbody></table>";
            }
        }
        
        return response()->json(['title'=> $title, 'message' => $message,],200);
    }

    public function getCheckDuplicateProductDelete()
    {
        try{
            $this->hook_after_delete(request()->product_id);
            return true;
        } catch (Exception $e){
            return false;
        }
    }

}
