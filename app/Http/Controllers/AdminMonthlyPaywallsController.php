<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use App\User;
	use App\NewOrder;
	use App\MonthlyPaywall;
	use Illuminate\Support\Facades\Mail;
	use App\Mail\DRMSEndMail;
	use App\Jobs\MonthlyPaywallJob;
	use App\Jobs\MonthlyPaywallExpiredJob;
	use App\Notifications\DRMNotification;

	class AdminMonthlyPaywallsController extends \crocodicstudio\crudbooster\controllers\CBController {
	    public function cbInit() {
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

	    	# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->table 			   = "monthly_paywalls";
			$this->title_field         = "id";
			$this->limit               = 20;
			$this->orderby             = "id,desc";
			$this->show_numbering      = FALSE;
			$this->global_privilege    = FALSE;
			$this->button_table_action = true;
			$this->button_action_style = "icon";
			$this->button_add          = FALSE;
			$this->button_delete       = FALSE;
			$this->button_edit         = FALSE;
			$this->button_detail       = FALSE;
			$this->button_show         = FALSE;
			$this->button_filter       = FALSE;
			$this->button_export       = FALSE;
			$this->button_import       = FALSE;
			$this->button_bulk_action  = FALSE;
			$this->sidebar_mode		   = "normal"; //normal,mini,collapse,collapse-mini
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
	        $this->col = [];
			$this->col[] = array("label"=>"ID","name"=>"id");
			$this->col[] = array("label"=>"Customer","name"=>"user_id","join"=>"cms_users,name");
			$this->col[] = array("label"=>"Invoice Number","name"=>"order_id","join"=>"new_orders,invoice_number");
			$this->col[] = array("label"=>"Amount","name"=>"order_id","join"=>"new_orders,total");
			$this->col[] = array("label"=>"Start date","name"=>"start_at");
			$this->col[] = array("label"=>"Paid at","name"=>"paid_at" );
			$this->col[] = array("label"=>"Status","name"=>"status" );
			$this->col[] = array("label"=>"Mail Send Count","name"=>"mail_send_count" );
			$this->col[] = array("label"=>"Payment Id","name"=>"payment_id");
			// $this->col[] = array("label"=>"Data","name"=>"data");
			$this->col[] = array("label"=>"Collect Total","name"=>"total" );

			# END COLUMNS DO NOT REMOVE THIS LINE
			# START FORM DO NOT REMOVE THIS LINE
		$this->form = [];
		// $this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
		// $this->form[] = ["label"=>"Generated At","name"=>"generated_at","type"=>"datetime","required"=>TRUE,"validation"=>"required|date_format:Y-m-d H:i:s"];
		// $this->form[] = ["label"=>"Paat","name"=>"paid_at","type"=>"datetime","required"=>TRUE,"validation"=>"required|date_format:Y-m-d H:i:s"];
		// $this->form[] = ["label"=>"Status","name"=>"status","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Mail Send Count","name"=>"mail_send_count","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
		// $this->form[] = ["label"=>"Payment Id","name"=>"payment_id","type"=>"select2","required"=>TRUE,"validation"=>"required|min:1|max:255","datatable"=>"payment,id"];
		// $this->form[] = ["label"=>"Total","name"=>"total","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Sub Total","name"=>"sub_total","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Discount","name"=>"discount","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Data","name"=>"data","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
		// $this->form[] = ["label"=>"Response","name"=>"response","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];

			# END FORM DO NOT REMOVE THIS LINE

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();

	        $this->addaction[] = ['title'=>'Send', 'label' => 'Send', 'url'=>CRUDBooster::mainpath('send/[id]'), 'confirmation_text' => 'Are you sure to send!', "icon" => 'fa fa-envelope', 'confirmation' => true, 'color' => 'info', 'showIf'=>"(strtolower([status]) != 'paid')"];

	        // $this->addaction[] = ['title'=>'Send', 'label' => 'Send', 'url'=>CRUDBooster::mainpath('send-queue/[id]'), 'confirmation_text' => 'Are you sure to send using queue!', "icon" => 'fa fa-envelope', 'confirmation' => true, 'color' => 'warning', 'showIf'=>"(strtolower([status]) != 'paid')"];
	        $this->addaction[] = ['title'=>'Mark as Paid', 'label' => 'Mark as Paid', 'url'=>CRUDBooster::mainpath('paid/[id]'), 'confirmation_text' => 'Are you sure to mark this paywall as Paid!', "icon" => 'fa fa-money', 'confirmation' => true, 'color' => 'danger', 'showIf'=>"(strtolower([status]) != 'paid')"];

	        if(CRUDBooster::isSuperadmin()){
	        	$this->addaction[] = ['title'=>'Payment', 'label' => 'Payment', 'url'=> CRUDBooster::adminPath('paywall-payment/[id]'), "icon" => 'fa fa-money', 'color' => 'info'];
	        }
	        


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	$amount_col = 3;
	    	$status_col = 6;
	    	$amount_collect_col = 9;

	    	if($column_index == $amount_col){
	    		$column_value = number_format((float)$column_value, 2, ',', '.').formatCurrency('eur');
	    	}	    	

	    	if($column_index == $amount_collect_col){
	    		$column_value = number_format((float)$column_value, 2, ',', '.').formatCurrency('eur');
	    	}	    		    	

	    	if($column_index == $status_col){
	    		$color = in_array(strtolower($column_value), ['succeeded', 'paid'])? '#398439' : '#f39c12';
	    		$color = in_array(strtolower($column_value), ['over_due'])? '#ff0000' : $color;
	    		$color = in_array(strtolower($column_value), ['processing'])? '#00c0ef' : $color;
	    		$column_value = '<label class="btn btn-xs" style="background-color:'.$color.';color:#fff;">'.ucfirst(str_replace('_', ' ', $column_value)).'</label>';
	    	}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)

	    public function getSend($id){
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

	    	try{
	    		$paywall = MonthlyPaywall::find($id);
	    		if(is_null($paywall)){
	    			throw new \Exception('Invalid action!');	
	    		}
	    		if($this->sendMonthlyPaywall($id)){
	    			CRUDBooster::redirect(CRUDBooster::mainpath(), 'Send success!', 'success');
	    		}else{
	    			CRUDBooster::redirect(CRUDBooster::mainpath(), 'Send failed!', 'error');
	    		}
	    	}catch(\Exception $e){
	    		CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error');
	    	}
	    }	    

	    public function getSendQueue($id){
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

	    	try{
	    		$paywall = MonthlyPaywall::find($id);
	    		if(is_null($paywall)){
	    			throw new \Exception('Invalid action!');	
	    		}
	    		MonthlyPaywallJob::dispatch($id);
	    		CRUDBooster::redirect(CRUDBooster::mainpath(), 'Send processing on Queue!', 'info');
	    	}catch(\Exception $e){
	    		CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error');
	    	}
	    }

	    //Send monthly paywall
	    public function sendMonthlyPaywall($id){
        	//DB::beginTransaction();
			try{

		    	$paywall = MonthlyPaywall::has('order')->whereNull('paid_at')->find($id);
			    if(is_null($paywall)) throw new \Exception('Invalid monthly paywall!');
			    DB::table('new_orders')->where('id', $paywall->order_id)->update(['status' => 'no_money_received']);
			    
				
				$data = [];
				$invoice_data = [];
				$order = NewOrder::find($paywall->order_id);
			    // $invoice_data['page_title'] = 'Paywall Invoice';
			    // $invoice_data['order'] = $order;
			    // $invoice_data['product_list'] = json_decode($order->cart);
			    // $invoice_data['customer'] = (object)$order->customer;
			    // $invoice_data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $order->cms_user_id)->first();

			    // $pdf_view = 'admin.invoice.charge_inv';
			    // $pdf_stream = \PDF::loadView($pdf_view, $invoice_data)->setWarnings(false)->stream();

			    $url = route('user-paywall-payment', $id); //CRUDBooster::adminPath('monthly_paywalls/payment/'.$id);
			    $due_date = config('global.paywall_due_date');

			    //Send Email
		    	$username = ($paywall->user)? $paywall->user->name : '';
		    	$max_due_date = ($paywall->start_at)? $paywall->start_at->addDays($due_date)->format('Y-m-d') : '7 days';
				$tags = [
				    'user' =>  $username,
				    'amount' =>  number_format((float)$order->total, 2, ',', '.').' EUR',
				    'date' =>  $paywall->paywall_date,
				    'max_due_date' => $max_due_date,
				    'pay_url' => $url,// $this->get_tiny_pay_url($url),
				];

				$slug = 'monthly_paywall'; //Page slug
				$mail_data = DRMParseMailTemplate($tags, $slug); //Generated html
				$user_email = $paywall->user->email;
				if(filter_var($user_email, FILTER_VALIDATE_EMAIL)){
					app('drm.mailer')->getMailer()->to($user_email)->send(new DRMSEndMail($mail_data)); //Send
				}else{
					throw new \Exception('Invalid user email. User ID:'.$paywall->user_id);	
				}


				// $data['email_to'] = 'serakib@gmail'; //$invoice_data['customer']->email;
			 //    $data['email_from'] = $invoice_data['setting']->email;
			 //    $data['subject'] = $mail_data['subject'];

				// app('drm.mailer')->getMailer()->send('admin.new_order.email_invoice_template', $mail_data, function($messages) use ($data, $pdf_stream){
	   //  	        $messages->from($data['email_from']);
	   //  	        $messages->to($data['email_to']);
	   //  	        $messages->subject($data['subject']);
	   //  			$messages->attachData($pdf_stream, 'invoice.pdf', [
	   //  	            'mime' => 'application/pdf',
	   //  	        ]);
	   //  	    });



				$paywall->increment('mail_send_count');
				//DB::commit(); //All ok
				return true;
			} catch (\Exception $e) {
			    //DB::rollBack();
			    User::find(71)->notify(new DRMNotification('Paywall Send customer invoice error '.$e->getMessage().' Line:'.$e->getLine(), '', '#'));

			    if($paywall){
				    $error_log = $paywall->error_log?? [];
				    $error_log[] = ['message' => $e->getMessage(), 'time' => date('Y-m-d H:i:s')];
				    $paywall->update(['error_log' => $error_log]);			    	
			    }

			    return false;
			}
	    }

	    //generate tiny url using tiny url
		private function get_tiny_pay_url($url)  {
			$ch = curl_init();
			$timeout = 5;
			curl_setopt($ch,CURLOPT_URL,'http://tinyurl.com/api-create.php?url='.$url);
			curl_setopt($ch,CURLOPT_RETURNTRANSFER,1);
			curl_setopt($ch,CURLOPT_CONNECTTIMEOUT,$timeout);
			$data = curl_exec($ch);
			curl_close($ch);
			return $data;
		}

		//paywall stripe payment
		public function paywallPaymentSCA($purchase_data){
			//DB::beginTransaction();
			try{

				$paywall = MonthlyPaywall::find($purchase_data['paywall_id']);
				if(is_null($paywall)) throw new \Exception("Invalid paywall charge!");

				$discount = $purchase_data['discount']?? 0;
                $total = $purchase_data['total']?? 0;
                $sub_total = $purchase_data['sub_total']?? 0;

                $update_data = [];
                $update_data['payment_id'] = $purchase_data['id'];
                $update_data['total'] = $total;
                $update_data['sub_total'] = $sub_total;
                $update_data['discount'] = $discount;
                $update_data['paid_at'] = now();
                $update_data['status'] = 'paid';

                $update_data = array_filter($update_data);
                $paywall->update($update_data);

                $payment_intend_id = $purchase_data['intend_id'] ?? null;

                DB::table('new_orders')->where('id', $paywall->order_id)->update(['status' => 'paid', 'intend_id' => $payment_intend_id]);

                $order = NewOrder::find($paywall->order_id);
                if($order){
                	updateOrderHistory($order, 'inkasso', 'Order status change to paid successfully!');
                }

				//DB::commit();    // Commiting  ==> There is no problem whatsoever
            	return ['success' => true, 'message' => 'Paywall charge payment successfully!'];
			}catch(\Exception $e){
				//DB::rollBack();   // rollbacking  ==> Something went wrong
            	return ['success' => false, 'message' => $e->getMessage()];
			}
		}

		//payment notification to user
		public function getSendPaywallAction(){
			try{
				//Get last 7 days paywall
				$due_date = config('global.paywall_due_date');
				$date = \Carbon\Carbon::today()->subDays($due_date);

				$due_paywalls = MonthlyPaywall::whereNull('paid_at')->whereNotNull('start_at')->where('start_at','>=',$date)->whereNotIn('status', ['processing', 'over_due'])->pluck('id')->toArray();
				$expired_paywalls = MonthlyPaywall::whereNull('paid_at')->whereNotNull('start_at')->where('start_at','<',$date)->whereNotIn('status', ['processing', 'over_due'])->pluck('id')->toArray();

				if($due_paywalls){
					foreach ($due_paywalls as $due_paywall) {
						MonthlyPaywallJob::dispatch($due_paywall); //Dispatch due paywall
					}
				}

				if($expired_paywalls){
					MonthlyPaywallExpiredJob::dispatch($expired_paywalls); //Dispatch expired paywall data
				}

			}catch(\Exception $e){
				User::find(71)->notify(new DRMNotification('Due paywall dispatch Error: '.$e->getMessage().' Line:'.$e->getLine(), '', '#'));
			}
		}

		public function getPaywallExpiredAction($paywall_ids){
			if($paywall_ids){
				DB::table('monthly_paywalls')->whereIn('id', $paywall_ids)->update(['status' => 'over_due']);
				$id_string = implode(',', $paywall_ids);
				User::find(71)->notify(new DRMNotification('Paywall Expired Ids: '.$id_string, '', CRUDBooster::adminPath('monthly_paywalls')));

				//block user
				// $block_user_ids = DB::table('monthly_paywalls')->whereIn('id', $paywall_ids)->select('user_id')->pluck('user_id')->toArray();
				// DB::table('cms_users')->whereIn('id', $block_user_ids)->update(['status' => 'Deactivate']);
			}
		}

		public function getPaid($id){
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

	    	//DB::beginTransaction();
	    	try{
	    		$paywall = MonthlyPaywall::has('order')->whereNull('paid_at')->find($id);
	    		if(is_null($paywall)){
	    			throw new \Exception('Invalid action!');	
	    		}

	    		$total = $paywall->order->total;

	    		$update_data = [];
                $update_data['payment_id'] = 'mnl_'.$id.'_'.date('YmdHis');
                $update_data['total'] = $total;
                $update_data['sub_total'] = $total;
                $update_data['paid_at'] = now();
                $update_data['status'] = 'paid';
                $update_data = array_filter($update_data);

                DB::table('new_orders')->where('id', $paywall->order_id)->update(['status' => 'paid']);

	    		if( $paywall->update($update_data) ){
	    			//DB::commit();
	    			CRUDBooster::redirect(CRUDBooster::mainpath(), 'Manual paid success!', 'success');
	    		}else{
	    			CRUDBooster::redirect(CRUDBooster::mainpath(), 'Manual paid failed!', 'error');
	    		}
	    	}catch(\Exception $e){
	    		//DB::rollBack(); 
	    		CRUDBooster::redirect(CRUDBooster::mainpath(), $e->getMessage(), 'error');
	    	}
		}
	}