<?php namespace App\Http\Controllers;

use App\DropfunnelCustomerTag;
use App\DropfunnelTag;
use App\Models\ChannelProduct;
use App\NewOrder;
use App\Notifications\DRMNotification;
use App\Traits\InvoiceNumber;
use App\User;
use Carbon\Carbon;
use DateTime;
use DateInterval;
use Illuminate\Http\Request as LaravelRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Exception;
use Session;
use Request;
use CRUDBooster;
use Cache;

use \Illuminate\Support\Facades\Request as req;
use crocodicstudio\crudbooster\helpers\CB;
class AdminOfferFormController extends \crocodicstudio\crudbooster\controllers\CBController
{
    use InvoiceNumber;
    private $embed_url = "";

    public function cbInit()
    {
//        if(!in_array(CRUDBooster::myParentId(),[98,2494])){
//             CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'You do not have access permission', 'danger');
//        }

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = false;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = true;
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = false;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "offer_form";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
//			$this->col[] = ["label"=>"User Id","name"=>"user_id","join"=>"cms_users,id"];
        $this->col[] = ["label" => __("Title"), "name" => "title", "width" => "75%"];
        $this->col[] = ["label" => __("Action"), "name" => "id"];
//			$this->col[] = ["label"=>"Form Design","name"=>"form_design"];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
//			$this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'cms_users,id'];
        $this->form[] = ['label' => 'Title', 'name' => 'title', 'type' => 'textarea', 'validation' => 'required|string|min:5|max:5000', 'width' => 'col-sm-10'];
//			$this->form[] = ['label'=>'Form Design','name'=>'form_design','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ['label'=>'User Id','name'=>'user_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'user,id'];
        //$this->form[] = ['label'=>'Form Design','name'=>'form_design','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();
        // $this->addaction[] = ["label" => "Embed code", "url" => "javascript:showOfferIframe([id])", "icon" => "fa fa-pencil", "color" => "primary"];
        // $this->addaction[] = ["label" => "Preview", "url" => "javascript:showOfferPreview([id])", "icon" => "fa fa-eye", "color" => "success"];


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = "function showOfferIframe(id) {

            swal({
                title: 'Loading...',
                imageUrl: window.ASSET_URL+ 'images/loading.gif',
                showConfirmButton: false,
                allowOutsideClick: false,
                confirm: true,
                showLoaderOnConfirm: true
            });

            $.ajax({
                method: 'POST',
                data: {id},
                url: '" . $this->embed_url . "offer_form/embed-link',
                success: function(response) {
                    swal.close();
                    if(response.success) {
                        $('#oplnEmbedFrame').html(response.html).modal('show');
                    } else {
                        swal('Hi There', 'opln embed error', 'warning');
                    }
                }
            })
        }

        function showOfferPreview(id) {
            $.ajax({
                    method: 'POST',
                    data: {id},
                    url: '" . $this->embed_url . "offer_form/offer-preview',
                    success: function(response) {
                        swal.close();
                        if(response.success) {
                            $('#offerPreview').html(response.html).modal('show');
                        } else {
                            swal('Hi There', 'opln embed error', 'warning');
                        }
                    }
                });
        }
        ";


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = "<div class='modal' id='oplnEmbedFrame'></div> <div class='modal' id='offerPreview'></div>";


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        $query->where('user_id', '=', CRUDBooster::myParentId());

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        if ($column_index == 2) {

            $id = $column_value;
            $edit_path = CRUDBooster::mainpath('edit/'.$id);
            $delete_path = CRUDBooster::mainpath('delete/'.$id);

            $onclick = 'swal({
                title: "Are you sure ?",
                text: "You will not be able to recover this record data!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#ff0000",
                confirmButtonText: "Yes!",
                cancelButtonText: "No",
                closeOnConfirm: false,
                showLoaderOnConfirm: true
                },
                function(){
                    location.href = "'.$delete_path.'";
                })';

            $column_value = "<div class='button_action' style='text-align:left'>
                                <a href='javascript:void(0)' class='btn btn-xs btn-primary' title='Embed code' onclick='showOfferIframe($id)'> <i class='fa fa-code' aria-hidden='true'></i> ".__('Embed Code')."</a>
                                <a href='javascript:void(0)' class='btn btn-xs btn-success' title='Preview' onclick='showOfferPreview($id)'> <i class='fa fa-eye'></i> ".__('Preview')."</a>
                                <a href=$edit_path class='btn btn-xs btn-info btn-edit' title='Edit Data'> <i class='fa fa-pencil'></i> </a>
                                <a href='javascript:void(0)' class='btn btn-xs btn-warning btn-delete' title='Delete' onclick='$onclick'><i class='fa fa-trash'></i>
                                </a>
                            </div>";

        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        DB::table('offer_card')->where('offer_id',$id)->delete();

    }


    //By the way, you can still create your own method in here... :)

    public function getIndex()
    {
        $this->cbLoader();

        $module = CRUDBooster::getCurrentModule();

        if (!CRUDBooster::isView() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        if (req::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::table(req::get('parent_table'))->where($parentTablePK, req::get('parent_id'))->first();
            if (req::get('foreign_key')) {
                $data['parent_field'] = req::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($parent_field) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $parent_field) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        $data['page_title'] = __('Offer Form');
        $data['action_add_data_title'] = __('Create New Quote Form');
        $data['date_candidate'] = $this->date_candidate;
        $data['limit'] = $limit = (req::get('limit')) ? req::get('limit') : $this->limit;

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::table($this->table)->select(DB::raw($this->table . "." . $this->primary_key));

        if (req::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . req::get('foreign_key'), req::get('parent_id'));
        }

        $this->hook_query_index($result);

        if (in_array('deleted_at', $table_columns)) {
            $result->where($this->table . '.deleted_at', null);
        }

        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;
        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {

                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if (req::get('q')) {
            $result->where(function ($w) use ($columns_table, $req) {
                foreach ($columns_table as $col) {
                    if (!$col['field_with']) {
                        continue;
                    }
                    if ($col['is_subquery']) {
                        continue;
                    }
                    $w->orwhere($col['field_with'], "like", "%" . req::get("q") . "%");
                }
            });
        }

        if (req::get('where')) {
            foreach (req::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }

        $filter_is_orderby = false;
        if (req::get('filter_column')) {

            $filter_column = req::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {
                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];

                if ($sorting != '') {
                    if ($key) {
                        $result->orderby($key, $sorting);
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];

        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(req::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (req::get('page')) ? req::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            if ($this->button_bulk_action) {

                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];

                if (isset($col['image'])) {
                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('vendor/crudbooster/avatar.jpg') . "'><img width='40px' height='40px' src='" . asset('vendor/crudbooster/avatar.jpg') . "'/></a>";
                    } else {
                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='" . $pic . "'><img width='40px' height='40px' src='" . $pic . "'/></a>";
                    }
                }

                if (@$col['download']) {
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = \Illuminate\Support\Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action):

                $button_action_style = $this->button_action_style;
                $html_content[] = "<div class='button_action' style='text-align:right'>" . view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render() . "</div>";

            endif;//button_table_action

            foreach ($html_content as $i => $v) {
                $this->hook_row_index($i, $v);
                $html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

        $data['html_contents'] = $html_contents;

        return view("crudbooster::default.index", $data);
    }

    public function getAdd()
    {
        $data = [];
        $data['page_title'] = __('Add Offer Form');
        $data['slug'] = $name = Str::random(20);
        $data['product_exists'] = ChannelProduct::where('user_id', '=', CRUDBooster::myParentId())
            ->where('channel', '=', 10)    //10 for DT shop
            ->exists();
        return view('admin.drm_email_marketings.offer_form.add', $data);
    }

    public function postSaveOffer()
    {
        $validator = Validator::make($_REQUEST, [
            'offer_name' => 'required',
            'iframe_backgrund_color' => 'required',
        ]);

        if ($validator->fails())
        {
            return response()->json(['errors'=>$validator->errors()->all()]);
        }

        $product_exists = DB::table('offer_card')->where('slug', $_REQUEST['slug'])->exists();
        if(!$product_exists){
            return response()->json(['success' => false, 'message' => 'You do not add product to card!' ]);
        }

        try {
            $offer_id = DB::table('offer_form')->insertGetId(
                [
                    'user_id' => CRUDBooster::myParentId(),
                    'title' => $_REQUEST['offer_name'],
                    'form_design' => $_REQUEST['html_design'],
                    'bg_color' => $_REQUEST['iframe_backgrund_color'],
                    'transparent' => $_REQUEST['transparent'],
                    'tag_id' => $_REQUEST['tag_select'],
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]
            );

            DB::table('offer_card')->where('slug', $_REQUEST['slug'])->update(['offer_id' => $offer_id]);
            DB::table('offer_card')->where('offer_id','<','1')->delete();

            return response()->json(['success' => true, 'message' => 'Offer save successfully!','offer_id' => $offer_id]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something went wrong!']);
        }
    }

    public function postUpdateOffer()
    {
        $validator = Validator::make($_REQUEST, [
            'offer_name' => 'required',
            'iframe_backgrund_color' => 'required',
        ]);

        if ($validator->fails())
        {
            return response()->json(['errors'=>$validator->errors()->all()]);
        }

        try {
            DB::table('offer_form')->where('id', $_REQUEST['offer_id'])->update(
                [
                    'user_id' => CRUDBooster::myParentId(),
                    'title' => $_REQUEST['offer_name'],
                    'form_design' => $_REQUEST['html_design'],
                    'bg_color' => $_REQUEST['iframe_backgrund_color'],
                    'transparent' => $_REQUEST['transparent'],
                    'tag_id' => $_REQUEST['tag_select'],
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]
            );

//                DB::table('offer_card')->where('slug', $_REQUEST['slug'])->update(['offer_id' => $_REQUEST['offer_id']]);

            return response()->json(['success' => true, 'message' => 'Offer save successfully!','offer_id' => $_REQUEST['offer_id']]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something went wrong!']);
        }
    }

    public function getEdit($id)
    {
        $data = [];
        $data['page_title'] = __('Edit Offer Form');
        $data['offers'] = DB::table('offer_form')->where('id', $id)->first();
        $data['slug'] = DB::table('offer_card')->where('offer_id', $id)->value('slug');
        $data['offer_id'] = $id;
        $data['product_exists'] = ChannelProduct::where('user_id', '=', CRUDBooster::myParentId())->where('channel', '=', 10)->exists();
        $data['existingTag'] = (!empty($data['offers']) && $data['offers']->tag_id) ? DropfunnelTag::where('id', $data['offers']->tag_id)->select('tag', 'id')->first() : null;
        return view('admin.drm_email_marketings.offer_form.edit', $data);
    }

    public function getDetail($id)
    {
        $data = [];
        $data['page_title'] = 'Offer Detail';
        $data['offers'] = DB::table('offer_form')->where('user_id', CRUDBooster::myParentId())->where('id', $id)->first();
        return view('admin.drm_email_marketings.offer_form.detail', $data);
    }


    public function postSaveCard()
    {

        $validator = Validator::make($_REQUEST, [
            'card_title' => 'required',
            'card_price' => 'required|min:0|numeric',
            'card_ean' => 'nullable|min:8|max:13',
        ]);
        if ($validator->fails())
        {
            return response()->json(['errors'=>$validator->errors()->all()]);
        }

        try {
            $cards = DB::table('offer_card')->where('slug', $_REQUEST['slug'])->first();
            $card_array = json_decode($cards->cards, true);
            $data = [];
            $card['id'] = $_REQUEST['id'];
            $card['title'] = $_REQUEST['card_title'];
            $card['price'] = $_REQUEST['card_price'];
            $card['ean'] = $_REQUEST['card_ean'];
            $card['description'] = $_REQUEST['card_description'];
            $card['price_type'] = $_REQUEST['price_type'];


            DB::table('offer_card')->updateOrInsert(
                ['card_id' => $_REQUEST['id']],
                [
                    'slug' => $_REQUEST['slug'],
                    'offer_id' => $_REQUEST['offer_id'],
                    'cards' => json_encode($card),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ]);

            return response()->json(['success' => true, 'message' => 'Card save successfully!']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something went wrong!']);
        }
    }

    public function postDeleteCard(){
        try{
           $card_id =  $_REQUEST['id'];
            DB::table('offer_card')->where('card_id', $card_id)->delete();
            return response()->json(['success' => true, 'message' => 'Card Deleted Successfully!']);
        }catch (Exception $e){
            dd($e);
            return response()->json(['success' => false, 'message' => 'Something went wrong!']);
        }
    }

    public function postEmbedLink()
    {

        try {
            $request = $_REQUEST;
            $id = $request["id"];
            $offer = DB::table("offer_form")->find($id);
            if ($offer) {
                $data = [];
                $data["url"] = url("drm-offer-iframe/" . $offer->id);
                $html = view("admin.drm_email_marketings.offer_form.embed", $data)->render();
                return response()->json([
                    "success" => true,
                    "html" => $html
                ]);
            } else {
                throw new Exception("Invalid action.");
            }

        } catch (Exception $e) {
            return response()->json([
                "success" => false,
                "message" => $e->getMessage()
            ]);
        }
    }
    public function postOfferPreview()
    {

        try {
            $request = $_REQUEST;
            $id = $request["id"];
            $offer = DB::table("offer_form")->find($id);
            if ($offer) {
                $data = [];
                $data["url"] = url("drm-offer-iframe/" . $offer->id);
                $data["offer"] = $offer;
                $html = view("admin.drm_email_marketings.offer_form.preview", $data)->render();
                return response()->json([
                    "success" => true,
                    "html" => $html
                ]);
            } else {
                throw new Exception("Invalid action.");
            }

        } catch (Exception $e) {
            return response()->json([
                "success" => false,
                "message" => $e->getMessage()
            ]);
        }
    }

    // Iframe
    public function offerIFrame($id)
    {
        $data = [];
        $data["offer"] = DB::table("offer_form")->where("id", $id)->first();
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();
        return view("admin.drm_email_marketings.offer_form.iframe", $data);
    }

    public function postOrderOffer()
    {
        try{
//            Cache::put( 'request', $_REQUEST);
            return response()->json(["success" => true,"message" => ' ' ]);
        }catch (Exception $exception){
            return response()->json(["success" => false,"message" => 'Something went wrong!' ]);
        }

    }

    public function offerContactForm(){
        $data = [];
        $data['page_title'] = 'Offer Contact Form';
        return view("admin.drm_email_marketings.offer_form.contact_form", $data);
    }

    public function saveOfferContact(LaravelRequest $request){
        $_REQUEST = $request->toArray();

        Validator::make($_REQUEST, [
            'email' => 'required|email|unique:new_customers'
        ])->validate();

        try{

            /* Customer create  */
            $card_ids = explode(',',$_REQUEST['ids']['0']);
            $offer = DB::table("offer_form")->where("id", $_REQUEST['offer_id'])->first();
            $cards_info = DB::table("offer_card")->whereIn('card_id', $card_ids)->select('card_id','cards')->get();
            $customer_info = [
                'full_name' => $_REQUEST['name'],
                'email' => $_REQUEST['email'],
                'phone' => $_REQUEST['phone'],
                'country' => $_REQUEST['country'],
                'address' => $_REQUEST['address'],
                'insert_type' => '12',
                'user_id' => $offer->user_id,
            ];

            $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);
            $tag_name = DropfunnelTag::select('tag')->find($offer->tag_id);
            DropfunnelCustomerTag::insertTag($tag_name->tag, $offer->user_id, $customer_id, 19);

            /* ------------------ insert order ----------------- */


            $customer_info = [
                'customer_full_name' => $_REQUEST['name'],
                'company_name' => $_REQUEST['customer_company_name'],
                'city' => $_REQUEST['customer_city'],
                'zip_code' => $_REQUEST['customer_zip_code'],
                'state' => $_REQUEST['customer_state'],
                'country' => $_REQUEST['country'],
                'address' => $_REQUEST['address'],
                'vat_number' => $_REQUEST['vat_number'],
                // 'insert_type' => 6,

                // shipping
                'street_shipping' => $_REQUEST['shipping_street'],
                'city_shipping' => $_REQUEST['shipping_city'],
                'state_shipping' => $_REQUEST['shipping_state'],
                'zipcode_shipping' => $_REQUEST['shipping_zip_code'],
                'country_shipping' => $_REQUEST['shipping_country'],
            ];

            //billing
            if (!empty($_REQUEST['is_same_address'])) {
                $customer_info['street_billing'] = $_REQUEST['shipping_street'];
                $customer_info['city_billing'] = $_REQUEST['shipping_city'];
                $customer_info['state_billing'] = $_REQUEST['shipping_state'];
                $customer_info['zipcode_billing'] = $_REQUEST['shipping_zip_code'];
                $customer_info['country_billing'] = $_REQUEST['shipping_country'];
            } else {
                $customer_info['street_billing'] = $_REQUEST['billing_street'];
                $customer_info['city_billing'] = $_REQUEST['billing_city'];
                $customer_info['state_billing'] = $_REQUEST['billing_state'];
                $customer_info['zipcode_billing'] = $_REQUEST['billing_zip_code'];
                $customer_info['country_billing'] = $_REQUEST['billing_country'];
            }

            $_REQUEST['insert_type'] = 6;
            $_REQUEST['user_id'] = $offer->user_id;

            // date seconds fixing
            $date1 = new DateTime($_REQUEST['order_date']);
            $date1->add(new DateInterval('PT' . date("H\Hi\Ms\S")));
            $_REQUEST['order_date'] = $date1->format('Y-m-d H:i:s');
            $_REQUEST['status'] = 'offer_sent';

            //customer info
            $_REQUEST['customer_info'] = customerInfoJson($customer_info);

            //billing
            $_REQUEST['billing'] = billingInfoJson($customer_info);

            $_REQUEST['is_offer'] = true;

            $_REQUEST['currency'] = 'EUR';

            //shipping
            $_REQUEST['shipping'] = shippingInfoJson($customer_info);

            if (isset($_REQUEST['is_same_address'])) {
                $customer_info['is_same_address'] = true;
                $_REQUEST['billing'] = $_REQUEST['shipping'];
            }

            app('App\Http\Controllers\AdminDrmAllCustomersController')->update_customer($customer_info, $customer_id);

            $tax_rate = 0;
            $carts = [];
            $total = 0;
            foreach ($cards_info as  $product) {
                $cart_item = [];
                $card_info = json_decode($product->cards);
                if($card_info->price_type != 'Monthly'){
                    $cart_item['id'] = $product->card_id;
                    $cart_item['product_name'] = $card_info->title;
                    $cart_item['description'] = $card_info->description;
                    $cart_item['qty'] = 1;
                    $cart_item['rate'] = $card_info->price;
                    $cart_item['tax'] = 0;
                    $cart_item['image'] = null;
                    $cart_item['product_discount'] =  0;
                    $cart_item['ean'] =  $card_info->ean;
                    $cart_item['amount'] = $card_info->price;
                    $carts[] = $cart_item;
                    $total += $card_info->price;
//                $this->updateInvoiceProduct($cart_item);
                }
            }
            $_REQUEST['cart'] = json_encode($carts);
            $_REQUEST['tax_rate'] = $tax_rate;
            $_REQUEST['total_amount'] = $total;
            $_REQUEST['drm_customer_id'] = $customer_id;
            $order = $this->insert_order($_REQUEST);
            $data['content'] = $offer->thank_you_page;
            if ($order == null || $order == [] || $order->id == null) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Something went wrong!'), 'error');
            }

            if(!empty($order->offer_number)){
                session()->flash('order_heighlight', $order->id);
                return view("admin.drm_email_marketings.offer_form.view_thank_you_page", $data);
            }else{
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Failed to create offer', 'error');
            }
        }catch (Expection $e){
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Something went wrong!', 'error');
        }
    }

    public function thankYouPage($offer_id){
        $data = [];
        $data['page_title'] = __('Offer Thank You Page');
        $data['offer_data'] = DB::table("offer_form")->where("id", $offer_id)->select('*')->first();
        $data["footer_bg"] = ($data['offer_data']->footer_bg) ? $data['offer_data']->footer_bg : '#0c5460';
        $data["sub_total_color"] = ($data['offer_data']->sub_total_color) ? $data['offer_data']->sub_total_color : '#5f9ea0';
        $data["countries"] = DB::table("countries")->select('id','name')->get();
        $data["offer_id"] = $offer_id;
        return view("admin.drm_email_marketings.offer_form.thank_you_page", $data);
    }

    public function postUpdateOfferThankYouPage()
    {
        try {
            DB::table('offer_form')
                ->where('id', $_REQUEST['offer_id'])
                ->update([
                    'thank_you_page' => $_REQUEST['html'],
                    'footer_bg' => $_REQUEST['footer_bg'],
                    'sub_total_color' => $_REQUEST['sub_total_color'],
                    'form' => $_REQUEST['form'],
                    'form_title' => $_REQUEST['form_title'],
                    'back_button' => $_REQUEST['back_button'],
                    'submit_button' => $_REQUEST['submit_button'],
                ]);
//            CRUDBooster::redirect(CRUDBooster::adminPath('offer_form'), 'Offer Successfully Store', 'success');
            return response()->json(['success' => true, 'redirect_url' => CRUDBooster::adminPath('offer_form') , 'message' => 'Offer Successfully Store']);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => 'Something Went Wrong!']);
        }

    }

    public function offerEmailValidation(LaravelRequest $request){
        $rules = [
            'email' => 'required|email|unique:new_customers',
        ];
        $validator = Validator::make($_REQUEST, $rules);


        if ($validator->fails()) {
            return response()->json(['success' => true, 'message' => $validator->messages()]);
        }

        return response()->json(['success' => false, 'message' => ' ']);
    }



    public function droptiendaOfferCreate(LaravelRequest $request){
//        $rules = [
//            'product_info.ean' =>  'required',
//        ];
//        $validator = Validator::make($request->all(), $rules);
//
//        if ($validator->fails()) {
//            return response()->json(['success' => false,'message' => $validator->messages()]);
//        }

        try {
            $password = $request->header('userPassToken');
            $token = $request->header('userToken');

            $_REQUEST = $request->toArray();

            $shop = \App\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();
            $user_id = $_REQUEST['user_id'] = $shop->user_id;


            /* Customer create  */
            //  dd($_REQUEST['uvp_request']);
//            if($_REQUEST['uvp_request'] != 1){
//                $shop = \App\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();
//                if (empty($shop)) {
//                    return response()->json(['success' => false, 'message' => 'Shop does not exists!',
//                    ]);
//                }
//
//                $user_id = $shop->user_id;
//                $product_price = DB::table('channel_products')->where('user_id', $user_id)->where('ean', $_REQUEST['product_info']['ean'])->value('uvp');
//                if ($product_price <= 0) {
//                    DB::table('temp_offer_infos')->insert([
//                        'user_id' => $user_id,
//                        'ean' => $_REQUEST['product_info']['ean'],
//                        'order_info' => json_encode($_REQUEST),
//                        'created_at' => now(),
//                        'updated_at' => now()
//                    ]);
//                    User::find($user_id)->notify(new DRMNotification('The customer is ' . $_REQUEST['name'] . ', Email : ' . $_REQUEST['email'].', Product EAN : '.$_REQUEST['product_info']['ean'], 'OFFER_MODULE', url('/') . '/admin/drm_products?search_by_field_column=ean&q='.$_REQUEST['product_info']['ean']));
//
//                    return response()->json([
//                        'success' => true,
//                        'message' => 'Offer created successfully!',
//                    ]);
//                }
//            }else{
//                $user_id = $_REQUEST['user_id'];
                $product_price = DB::table('channel_products')->where('user_id', $user_id)->where('ean', $_REQUEST['product_info']['ean'])->value('uvp');
//            }

//            $customer_info = [
//                'full_name' => $_REQUEST['name'],
//                'email' => $_REQUEST['email'],
//                'phone' => $_REQUEST['phone'],
//                'country' => $_REQUEST['country'],
//                'address' => $_REQUEST['address'],
//                'insert_type' => '12',
//                'user_id' => $user_id,
//            ];
//            $customer_id = DB::table('new_customers')->insertGetId($customer_info);

            $customer_info = [
                "customer_full_name" => $_REQUEST['name'],
                "company_name" => '',
                "currency" => 'EUR',
                'email' => $_REQUEST['email'],
                'address' => $_REQUEST['address'],
                'country' => $_REQUEST['country'],
                'default_language' => 'DE',
                'zip_code' => $_REQUEST['zip_code'],
                'state' => $_REQUEST['state'],
                'insert_type' => '12',
                'status' => 1,
                'source' => '10',
                'type' => 5,
                'user_id' => $user_id,
            ];

            //Add customer account
            $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);

//            $tag_name = DropfunnelTag::select('tag')->find($offer->tag_id);
//            DropfunnelCustomerTag::insertTag($tag_name->tag, $offer->user_id, $customer_id, 19);

            /* ------------------ insert order ----------------- */

            $customer_info = [
                'customer_full_name' => $_REQUEST['name'],
                'company_name' => $_REQUEST['customer_company_name'],
                'city' => $_REQUEST['customer_city'],
                'zip_code' => $_REQUEST['zip_code'],
                'state' => $_REQUEST['state'],
                'country' => $_REQUEST['country'],
                'address' => $_REQUEST['address'],
                'vat_number' => $_REQUEST['vat_number'],
                // 'insert_type' => 6,

                // shipping
                'street_shipping' => $_REQUEST['shipping_street'],
                'city_shipping' => $_REQUEST['shipping_city'],
                'state_shipping' => $_REQUEST['shipping_state'],
                'zipcode_shipping' => $_REQUEST['shipping_zip_code'],
                'country_shipping' => $_REQUEST['shipping_country'],
            ];

            //billing
            if (!empty($_REQUEST['is_same_address'])) {
                $customer_info['street_billing'] = $_REQUEST['shipping_street'];
                $customer_info['city_billing'] = $_REQUEST['shipping_city'];
                $customer_info['state_billing'] = $_REQUEST['shipping_state'];
                $customer_info['zipcode_billing'] = $_REQUEST['shipping_zip_code'];
                $customer_info['country_billing'] = $_REQUEST['shipping_country'];
            } else {
                $customer_info['street_billing'] = $_REQUEST['billing_street'];
                $customer_info['city_billing'] = $_REQUEST['billing_city'];
                $customer_info['state_billing'] = $_REQUEST['billing_state'];
                $customer_info['zipcode_billing'] = $_REQUEST['billing_zip_code'];
                $customer_info['country_billing'] = $_REQUEST['billing_country'];
            }

            $_REQUEST['insert_type'] = 6;
            $_REQUEST['user_id'] = $user_id;

            // date seconds fixing
//            $date1 = new DateTime($_REQUEST['order_date']);
//            $date1->add(new DateInterval('PT' . date("H\Hi\Ms\S")));
//            $_REQUEST['order_date'] = $date1->format('Y-m-d H:i:s');
            $_REQUEST['order_date'] = Carbon::now()->format('Y-m-d');
            $_REQUEST['status'] = 'offer_sent';
            //customer info
            $_REQUEST['customer_info'] = customerInfoJson($customer_info);

            //billing
            $_REQUEST['billing'] = billingInfoJson($customer_info);

            $_REQUEST['is_offer'] = true;

            $_REQUEST['currency'] = 'EUR';

            //shipping
            $_REQUEST['shipping'] = shippingInfoJson($customer_info);

            if (isset($_REQUEST['is_same_address'])) {
                $customer_info['is_same_address'] = true;
                $_REQUEST['billing'] = $_REQUEST['shipping'];
            }

            app('App\Http\Controllers\AdminDrmAllCustomersController')->update_customer($customer_info, $customer_id);
            $cart_item = [];
            $amount = $product_price * $_REQUEST['product_info']['qty'];
            $cart_item['id'] = 1;
            $cart_item['product_name'] = $_REQUEST['product_info']['product_name'];
            $cart_item['description'] = $_REQUEST['product_info']['product_name'];
            $cart_item['qty'] = $_REQUEST['product_info']['qty'];;
            $cart_item['rate'] = $product_price;
            $cart_item['tax'] = $_REQUEST['tax_rate'];
            $cart_item['image'] = null;
            $cart_item['product_discount'] = 0;
            $cart_item['ean'] = $_REQUEST['product_info']['ean'];
            $cart_item['amount'] = $amount;

            $_REQUEST['cart'] = json_encode([$cart_item]);
            $_REQUEST['offer_options'] = $_REQUEST['offer_options'];
            if (in_array($user_id, [2494, 212, 2591]) || isLocal()){
                $net_amount = ($amount / (100 + $_REQUEST['tax_rate']) * 100);
                $_REQUEST['discount'] = 0;
                $_REQUEST['tax_rate'] = $_REQUEST['tax_rate'];
                $_REQUEST['total_tax'] = $amount - $net_amount;
                $_REQUEST['total_amount'] = $amount;
                $_REQUEST['sub_total'] = $amount;
            }else{
                $_REQUEST['tax_rate'] = 0;
                $_REQUEST['total_amount'] = $amount;
                $_REQUEST['sub_total'] = $amount;
            }
            $_REQUEST['drm_customer_id'] = $customer_id;

            $order = $this->insert_order($_REQUEST);
            if(!empty($order->offer_number)){
                $headline = 'The customer is ' . $_REQUEST['name'] . ', Email : ' . $_REQUEST['email'].', Product EAN : '.$_REQUEST['product_info']['ean'];
                $detail_url = url('/') . '/admin/drm_products?search_by_field_column=ean&q='.$_REQUEST['product_info']['ean'];
                User::find($user_id)->notify(new DRMNotification($headline, 'OFFER_MODULE', $detail_url));

                $this->storeDtContactInformation($_REQUEST, $user_id, $customer_id);

                return response()->json([
                    'success' => true,
                    'message' =>'Offer create successfully',
                ]);
            }else{
                return response()->json([
                    'success' => false,
                    'message' => 'Something went wrong!',
                ]);
            }
        }catch (\Exception $e){
            return response()->json([
            'success' => false,
            'message' => 'Something went wrong!',
          ]);
        }
    }

    private function storeDtContactInformation($req, $user_id, $customer_id) {
        $info = [
            [
                'name' => 'first_name',
                'label' => __('First name'),
                'value' => $req['first_name'] ?? $req['name'],
            ],
            [
                'name' => 'last_name',
                'label' => __('Last name'),
                'value' => $req['last_name'] ?? '',
            ],
            [
                'name' => 'email',
                'label' => __('Email'),
                'value' => $req['email'],
            ],
            [
                'name' => 'phone',
                'label' => __('Phone'),
                'value' => $req['phone'],
            ],
            [
                'name' => 'street',
                'label' => __('Street'),
                'value' => $req['street'],
            ],
            [
                'name' => 'state',
                'label' => __('State'),
                'value' => $req['state'],
            ],
            [
                'name' => 'zip_code',
                'label' => __('Zip code'),
                'value' => $req['zip_code'],
            ],
            [
                'name' => 'city',
                'label' => __('City'),
                'value' => $req['address'],
            ],
            [
                'name' => 'country',
                'label' => __('Country'),
                'value' => $req['country'],
            ],
            [
                'name' => 'product_name',
                'label' => __('Product'),
                'value' => $req['product_info']['product_name'],
            ],
            [
                'name' => 'ean',
                'label' => 'EAN',
                'value' => $req['product_info']['ean'],
            ],
            [
                'name' => 'message',
                'label' => __('Message'),
                'value' => $req['message'] ?? '',
            ],
        ];

        \App\ContactFormMessagesHistory::create([
            'user_id' => $user_id,
            'customer_id' => $customer_id,
            'contact_form_id' => 0,
            'extra_fields' => json_encode($info),
        ]);
    }

    public function insert_order($order_info)
    {
        //DB::beginTransaction();
        try {
            $is_normal_invoice = true;

            $row['cms_user_id'] = $check['cms_user_id'] = $order_info['user_id'];
            $row['order_date'] = $check['order_date'] = date('Y-m-d H:i:s', strtotime($order_info['order_date']));
            $row['insert_type'] = $check['insert_type'] = $order_info['insert_type'];
            $row['shop_id'] = $check['shop_id'] = $order_info['shop_id'];

            /* --------------- invoice number --------------------- */
            // $inv1 = DB::table('new_orders')->where('cms_user_id', $check['cms_user_id'])->where('invoice_number', '!=', -1)->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
            // $inv2 = DB::table('drm_invoice_setting')->where('cms_user_id', $check['cms_user_id'])->first()->start_invoice_number;

            $invoice_number = null; //self::generateInvoiceNumber($check['cms_user_id']); //($inv1 > $inv2) ? $inv1 : $inv2;
            $offer_number = null;
            $offer_note = false;

            /* -------------- future invoice ------------------- */
            $status = $order_info['status'] ?? "nicht_bezahlt";
            if (isset($order_info['invoice_date']) && $order_info['invoice_date']) {
                $now = new DateTime();
                $due = new DateTime($order_info['invoice_date']);

                if ($due > $now) {
                    $invoice_number = -1;
                    $is_normal_invoice = false;
                }
            }

            if($is_normal_invoice){
                //Offer number
                if(!empty($order_info['is_offer'])) {
                    $offer_number = self::generateOfferNumber($check['cms_user_id']);
                    $offer_note = true;
                }else{
                    $invoice_number = self::generateInvoiceNumber($check['cms_user_id']);
                }
            }


            /* ------------------ insert order ----------------- */
            $row['invoice_number'] = $invoice_number;
            $row['invoice_date'] = $order_info['invoice_date'] ? date('Y-m-d H:i:s', strtotime($order_info['invoice_date'])) : null;

            $status = drmOrderLabelByGroupId($status);

            $row['total'] = removeCommaPrice($order_info['total_amount']);
            $row['sub_total'] = removeCommaPrice($order_info['sub_total']);
            $row['total_tax'] = removeCommaPrice($order_info['total_tax']);

            $row['drm_customer_id'] = $order_info['drm_customer_id'];

            $orderCustomer = DB::table('new_customers')->where('id', '=', $order_info['drm_customer_id'])->select('tax_number', 'vat_number')->first();
            $row['vat_number'] = $orderCustomer->vat_number;
            $row['tax_number'] = $orderCustomer->tax_number;

            $row['discount'] = removeCommaPrice($order_info['discount']);
            $row['discount_type'] = $order_info['discount_type'];
            $row['adjustment'] = $order_info['adjustment'];
            $row['payment_type'] = $order_info['payment_type'];
            $row['currency'] = $order_info['currency'];
            $row['shipping_cost'] = $order_info['shipping_cost'];
            $row['customer_info'] = $order_info['customer_info'];
            $row['billing'] = $order_info['billing'];
            $row['shipping'] = $order_info['shipping'];
            $row['client_note'] = $order_info['client_note'];
            $row['status'] = $status;
            $row['cms_client'] = $order_info['cms_client'];
            $row['cart'] = $order_info['cart'];
            // if(in_array($order_info['user_id'],[2494,212,2693])){
                $row['offer_options'] = $order_info['offer_options'];
            // }
            $row['char_status'] = $order_info['char_status'] ?? 0;

            $row['tax_exclude'] = $order_info['tax_exclude'] ?? 0;
            $row['tax_rate']    = $order_info['tax_rate'];

            $row['feature_type'] = null;
            $row['feature_id'] = null;

            $row['offer_number'] = $offer_number;
            $row['offer_remainder'] = !empty($order_info['offer_remainder'])? $order_info['offer_remainder'] : null;

            if (isset($order_info['feature_id'])) {
                $row['feature_type'] = $order_info['feature_type'];
                $row['feature_id'] = $order_info['feature_id'];
            }

            $row['inv_pattern'] = $offer_note ? null : self::generateInvoiceString($invoice_number, $check['cms_user_id']);


            if(isset($order_info['dropmatix_sub_total']))
            {
              $row['dropmatix_sub_total'] = $order_info['dropmatix_sub_total'];
              $row['dropmatix_total_tax'] = $order_info['dropmatix_total_tax'];
              $row['dropmatix_discount'] = $order_info['dropmatix_discount'];
              $row['dropmatix_shipping_cost'] = $order_info['dropmatix_shipping_cost'];
              $row['dropmatix_tax_rate'] = $order_info['dropmatix_tax_rate'];
            } else {
                $dropmatixData = app(\App\Services\Order\Store\UpdateDropmatix::class)->get($row);
                if(!empty($dropmatixData))
                {
                   $row = array_merge($row, $dropmatixData);
                }
            }

            // STORE_ORDER_ON_DATABASE
            $order = NewOrder::create($row);
            $this->convertNonEurToEur($order);

            //Log History
            try {
                drmOrderFirstHistory($order);
            } catch (Exception $eee) {
            }

            //Auto email

            // if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 2592])){

                $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

                $channel_offer_auto_mail = DB::table('drm_offer_mail')
                ->where(['cms_user_id' => $order->cms_user_id])
                ->first()->auto_mail;

                if($is_normal_invoice && $channel_offer_auto_mail){

                    // if( $channel_offer_auto_mail->auto_mail ){
                        app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
                    // }

                }else if($is_normal_invoice && DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'])->whereNull('channel')->first()->auto_mail){
                    app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
                }

            // }else{
            // if ($is_normal_invoice && DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'])->first()->auto_mail) {
            //     app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
            // }
            // }

            app('App\Http\Controllers\AdminDrmAllCustomersController')->insertCustomerTag($order->id);

            //DB::commit();
            return $order;
        } catch (Exception $e) {
            //DB::rollBack();
            return null;
        }
    }
    private function convertNonEurToEur(NewOrder $order)
    {
        try {
            $access_key = 'd130eab6685454f33867262346fcd27c';

            $base = 'EUR';
            $date = Carbon::parse($order->order_date)->format('Y-m-d');
            $currency = strtoupper($order->currency);
            $total = $order->total;

            $currency_rate = 0;
            $eur_total = 0;

            if ($currency === 'EUR') {
                $currency_rate = 1;
                $eur_total = $order->total;
            } else {
                // Initialize CURL:
                $ch = curl_init('http://data.fixer.io/api/' . $date . '?access_key=' . $access_key . '&base=' . $base);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                // Store the data:
                $json = curl_exec($ch);
                curl_close($ch);

                // Decode JSON response:
                $exchangeRates = json_decode($json, true);

                if ($exchangeRates['success'] && $exchangeRates['rates']) {
                    $rates = $exchangeRates['rates'];
                    if (isset($rates[$currency])) {
                        $currency_rate = removeCommaPrice($rates[$currency]);
                        $eur_total = ($total / $currency_rate);
                    } else {
                        throw new Exception($currency . " not found on fixer API.", 2);
                    }
                } else {
                    throw new Exception("API connection problem.", 1);
                }
            }

            //Update calculated currency value
            // STORE_ORDER_STATUS_ON_DATABASE
            if ($order->update(['currency_rate' => $currency_rate, 'eur_total' => $eur_total])) {
                return true;
            } else {
                throw new Exception("Currency convert update failed.", 3);
            }

        } catch (Exception $e) {
            $err_message = "Currency Conversion Failed for Order ID: " . $order->id . " Currency: " . $order->currency . ". Error: " . $e->getMessage();
            User::find(71)->notify(new DRMNotification($err_message)); //Notify developer about error
            return false;
        }
    }

    //Search products
    public function searchProducts()
    {
        $term = $_REQUEST['term'];
        $result = [];
//        ->orWhere('meta->title', 'LIKE', '%' . $term . '%')
        if (isset($term)) {
            $result = ChannelProduct::where('ean', 'LIKE', '%' . $term . '%')
                ->where('user_id', '=', CRUDBooster::myParentId())
//                ->where('channel', '=', 10)    //10 for DT shop
                ->take(15)
                ->select('id', 'ean as value')
                ->get()->toArray();

            if(!empty($result)){
                return $result;
            }else{
                $result = ChannelProduct::Where('title->de', 'LIKE', '%' . $term . '%')
                    ->where('user_id', '=', CRUDBooster::myParentId())
//                    ->where('channel', '=', 10)    //10 for DT shop
                    ->take(15)
                    ->select('id', 'title->de as value')
                    ->get()->toArray();
//                dd($result);
            }
//            if (!empty($result)) return $result;
        }
        return $result;
    }

    public function postChannelProduct(){
        try {
            $result = ChannelProduct::where('id', '=', $_REQUEST['product_id'] )
                ->where('user_id', '=', CRUDBooster::myParentId())
                ->where('channel', '=', 10)    //10 for DT shop
                ->select('id','title','ean','description','images','vk_price')
                ->first();

            return response()->json(['success' => true,'data' => $result]);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'data' => 'Data not found!']);
        }
    }




}
