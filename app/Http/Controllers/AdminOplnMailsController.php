<?php

namespace App\Http\Controllers;

use App\AppointmentType;
use App\NewCustomer;
use Carbon\Carbon;
use crocodicstudio\crudbooster\controllers\CBController;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use App\DropfunnelCustomerTag;
use App\DropfunnelTag;
use App\OplnMail;
use Illuminate\Database\QueryException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Session;

use \Illuminate\Support\Facades\Request as req;
use crocodicstudio\crudbooster\helpers\CB;


class AdminOplnMailsController extends CBController
{
    private $embed_url = "";

    private $colors = [
        "#4e6b1d", "#ff793e", "#dfdfdf", "#97db2d", "#49FA8E",
        "#ff9013", "#f0b849", "#ff5d19", "#9bd424", "#a831fb",
        "#ec7f4e", "#d3e33f", "#ff8700", "#f4ee2f", "#ff7c15",
        "#74ea87", "#d1dc48", "#ffac00", "#ff983e", "#fbe90a",
        "#7e32b4", "#D959FA", "#440c6f", "#480050", "#ffb100",
        "#cdea02", "#79c3ea", "#4bbcf6", "#2f4ba1", "#354458",
    ];

    private $icons = [
        "send","share",
        "paper-plane-o","share-square","share-square-o",
    ];

    private $btnColors = [
        ["black", "rgb(0, 0, 0)"],
        ["green", "rgb(0, 128, 0)"],
        ["silver", "rgb(192, 192, 192)"],
        ["lime", "rgb(0, 255, 0)"],
        ["gray", "rgb(128, 0, 128)"],
        ["olive", "rgb(128, 128, 0)"],
        ["white", "rgb(255, 255, 255)"],
        ["yellow", "rgb(255, 255, 0)"],
        ["maroon", "rgb(128, 0, 0)"],
        ["navy", "rgb(0, 0, 128)"],
        ["red", "rgb(255, 0, 0)"],
        ["blue", "rgb(0, 0, 255)"],
        ["purple", "rgb(128, 0, 128)"],
        ["teal", "rgb(0, 128, 128)"],
        ["fuchsia", "rgb(255, 0, 255)"],
        ["aqua", "rgb(0, 255, 255)"],
    ];

    private $tags = [
        "manually",
        "drop_shipping_upselling",
    ];

    public function cbInit()
    {
//        if (!CRUDBooster::isSuperadmin()) {
//            $app_id = config('global.email_marketing_app_id');
//            if (!DrmUserHasPurchasedApp(CRUDBooster::myParentId(), $app_id)) {
//                CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
//            }
//        }


        $this->embed_url = CRUDBooster::adminPath("opln_mails/embed-link");
        $this->script_url = CRUDBooster::adminPath("opln_mails/script-link");

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "content";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = false;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = true;
        $this->button_edit = true;
        $this->button_delete = true;
        $this->button_detail = false;
        $this->button_show = false;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "opln_mails";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => __("ID"), "name" => "id"];
        if (CRUDBooster::isSuperAdmin()) {
            $this->col[] = ["label" => __("User Name"), "name" => "user_id", "join" => "cms_users,name"];
        }
        $this->col[] = ["label" => __("Title"), "name" => "title"];
        $this->col[] = ["label" => __("Thumbnail"), "name" => "thumbnail"];
        $this->col[] = ["label" => __("Slug"), "name" => "slug", "visible" => false];
        $this->col[] = ["label" => __("Action"), "name" => "id", "width" => "15%"];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ["label"=>"User Id","name"=>"user_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"user,id"];
        //$this->form[] = ["label"=>"Tag","name"=>"tag","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Fav Color","name"=>"fav_color","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Width","name"=>"width","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
        //$this->form[] = ["label"=>"Swoosh","name"=>"swoosh","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Footer Note","name"=>"footer_note","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Footer Text","name"=>"footer_text","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Fields","name"=>"fields","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Content","name"=>"content","type"=>"textarea","required"=>TRUE,"validation"=>"required|string|min:5|max:5000"];
        //$this->form[] = ["label"=>"Slug","name"=>"slug","type"=>"text","required"=>TRUE,"validation"=>"required|min:1|max:255"];
        //$this->form[] = ["label"=>"Title","name"=>"title","type"=>"text","required"=>TRUE,"validation"=>"required|string|min:3|max:70","placeholder"=>"You can only enter the letter only"];
        //$this->form[] = ["label"=>"Start Value","name"=>"start_value","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
         $this->addaction = array();
        $this->addaction[] = ["label" => __("Embed Code"), "url" => "javascript:showOplnIframe([id])", "icon" => "fa fa-code", "color" => "primary"];
       


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = "function showOplnIframe(id) {

            swal({
                title: 'Loading...',
                imageUrl: window.ASSET_URL+ 'images/loading.gif',
                showConfirmButton: false,
                allowOutsideClick: false,
                confirm: true,
                showLoaderOnConfirm: true
            });

            $.ajax({
                method: 'POST',
                data: {id},
                url: '" . $this->embed_url . "',
                success: function(response) {
                    swal.close();
                    if(response.success) {
                        $('#oplnEmbedFrame').html(response.html).modal('show');
                    } else {
                        swal('Hi There', 'opln embed error', 'warning');
                    }
                }
            })
        }
        
        function showOplnScript(id) {

            swal({
                title: 'Loading...',
                imageUrl: window.ASSET_URL+ 'images/loading.gif',
                showConfirmButton: false,
                allowOutsideClick: false,
                confirm: true,
                showLoaderOnConfirm: true
            });

            $.ajax({
                method: 'POST',
                data: {id},
                url: '" . $this->script_url . "',
                success: function(response) {
                    swal.close();
                    if(response.success) {
                        $('#oplnEmbedFrame').html(response.html).modal('show');
                    } else {
                        swal('Hi There', 'opln embed error', 'warning');
                    }
                }
            })
        }
        
        ";


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = "<div class='modal' id='oplnEmbedFrame'></div> <div class='modal' id='oplnPreview'></div>";


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }

    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        if (!CRUDBooster::isSuperAdmin()) {
            $query->where('user_id', CRUDBooster::myParentId());
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, & $column_value)
    {
        if (CRUDBooster::isSuperadmin() ? $column_index == 4 : $column_index == 3) {

            $column_value = '<img src="'.$column_value.'" height="80" width="120"';
        }

        if (CRUDBooster::isSuperadmin() ? $column_index == 5 : $column_index == 4) {

            $id = $column_value;
            $edit_path = CRUDBooster::mainpath('edit/'.$id);
            $delete_path = CRUDBooster::mainpath('delete/'.$id);

            $onclick = 'swal({
                title: "Are you sure ?",
                text: "You will not be able to recover this record data!",
                type: "warning",
                showCancelButton: true,
                confirmButtonColor: "#ff0000",
                confirmButtonText: "Yes!",
                cancelButtonText: "No",
                closeOnConfirm: false,
                showLoaderOnConfirm: true
                },
                function(){
                    location.href = "'.$delete_path.'";
                })';

           
                $column_value = "<div class='button_action' style='text-align:left'>
                <a href='javascript:void(0)' class='btn btn-xs btn-primary' title='Embed Code' onclick='showOplnIframe($id)'> <i class='fa fa-code' aria-hidden='true'></i>  ".__('Embed Code')."</a>
                <a href='javascript:void(0)' class='btn btn-xs btn-primary' title='Embed Code' onclick='showOplnScript($id)'> <i class='fa fa-code' aria-hidden='true'></i>  ".__('Script Code ')."</a>
                <a href=$edit_path class='btn btn-xs btn-success btn-edit' title='Edit Data'> <i class='fa fa-pencil'></i> </a>
                <a href='javascript:void(0)' class='btn btn-xs btn-warning btn-delete' title='Delete' onclick='$onclick'><i class='fa fa-trash'></i>
                </a>
                </div>";
           
            

        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here
    }

    public function getIndex()
    {
        $this->cbLoader();

        $module = CRUDBooster::getCurrentModule();

        if ((!CRUDBooster::isView() && $this->global_privilege == false) && !hasAllRightsPermission()) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        if (req::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::table(req::get('parent_table'))->where($parentTablePK, req::get('parent_id'))->first();
            if (req::get('foreign_key')) {
                $data['parent_field'] = req::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($parent_field) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $parent_field) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        $data['page_title'] = __('OptIn Box');
        $data['action_add_data_title'] = __('Add Data');
        $data['date_candidate'] = $this->date_candidate;
        $data['limit'] = $limit = (req::get('limit')) ? req::get('limit') : $this->limit;

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::table($this->table)->select(DB::raw($this->table . "." . $this->primary_key));

        if (req::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . req::get('foreign_key'), req::get('parent_id'));
        }

        $this->hook_query_index($result);

        if (in_array('deleted_at', $table_columns)) {
            $result->where($this->table . '.deleted_at', null);
        }

        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;
        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {

                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if (req::get('q')) {
            $result->where(function ($w) use ($columns_table, $req) {
                foreach ($columns_table as $col) {
                    if (!$col['field_with']) {
                        continue;
                    }
                    if ($col['is_subquery']) {
                        continue;
                    }
                    $w->orwhere($col['field_with'], "like", "%" . req::get("q") . "%");
                }
            });
        }

        if (req::get('where')) {
            foreach (req::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }

        $filter_is_orderby = false;
        if (req::get('filter_column')) {

            $filter_column = req::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {
                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];

                if ($sorting != '') {
                    if ($key) {
                        $result->orderby($key, $sorting);
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];

        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(req::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (req::get('page')) ? req::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            if ($this->button_bulk_action) {

                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];

                if (isset($col['image'])) {
                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('vendor/crudbooster/avatar.jpg') . "'><img width='40px' height='40px' src='" . asset('vendor/crudbooster/avatar.jpg') . "'/></a>";
                    } else {
                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='" . $pic . "'><img width='40px' height='40px' src='" . $pic . "'/></a>";
                    }
                }

                if (@$col['download']) {
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = \Illuminate\Support\Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action):

                $button_action_style = $this->button_action_style;
                $html_content[] = "<div class='button_action' style='text-align:right'>" . view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render() . "</div>";

            endif;//button_table_action

            foreach ($html_content as $i => $v) {
                $this->hook_row_index($i, $v);
                $html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        //if list is empty send to add page
        if(!CRUDBooster::isSubUser() || CRUDBooster::isSubUser() && (sub_account_can('create_lead_forms', 120) || sub_account_can('all_modules', 122))){
            if(empty($html_contents)){
                return $this->getAdd();
             }
        }

      

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

        $data['html_contents'] = $html_contents;
        
        return view("crudbooster::default.index", $data);
    }

    // By the way, you can still create your own method in here... :)
    public function getAdd()
    {
        $user_id = CRUDBooster::myParentId();
        $data["colors"] = $this->colors;
        $data["buttonColors"] = $this->btnColors;
        $data["icons"] = $this->icons;
        $data["tags"] = $this->tags;
          $data["page_data"] = DB::table('optin_pages')
              ->where('user_id', $user_id)
              ->orderBy('id', 'desc')
              ->value('id');
          $path = public_path() . "/optin_templates.json";
          if(isLocal()){
           $path = public_path() . "/optin_templates_test.json";
          }


        $data["templates"] = json_decode(file_get_contents($path), true);
        $data['slug'] = $name = Str::random(20);
        $data['types'] = AppointmentType::where('user_id', $user_id)
            ->select('id', 'slug', 'name', 'link', 'link_status')
            ->get()
            ->each(function ($type) {
                $type->link = url('/appointment/booking') . '/' . $type->slug . '?' . $type->link;
            });
        if(!CRUDBooster::isSubUser() || CRUDBooster::isSubUser() && (sub_account_can('create_lead_forms', 120) || sub_account_can('all_modules', 122))){
            return view("admin.drm_dynamic_mail_system.page_builder", $data);
        }else{
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
    }
    public function postAddSave()
    {
        $request = $_REQUEST;
        try {
            Validator::make($request, [
                "title" => "required",
                "tag" => "required",
               "fields" => "required|array|min:1",
               "content" => "required"
            ])->validate();
        } catch (ValidationException $e) {
            return response()->json($e->errors(), 201);
        }
        try {
           $pushData = [];
           $slug = uniqid();
           array_push($pushData, $request["fields"]);
            $optln = OplnMail::create([
                "user_id" => CRUDBooster::myParentId(),
                "tag" => $request["tag"],
                "title" => $request["title"],
                "fav_color" => $request["custom_color"],
                "width" => $request["width"],
                "swoosh" => $request["swoosh_button"],
                "footer_note" => $request["editor_data"],
                "footer_text" => $request["footer_text"],
                "start_value" => $request["start_value"],
                "thank_you_html" => $request["thankYouContent"],
                "fields" => json_encode($pushData),
                "content" => "<input type='hidden' name='slug' value='${slug}'/>" . $request["content"],
                "slug" => $slug,
                "created_at" => Carbon::now(),
                "updated_at" => Carbon::now(),
            ]);
            // if(isLocal() && in_array(CRUDBooster::myParentId(),[212,98])){
            //   DB::table('optin_pages')->where('slug', $request["slug"])->update(['user_id' => CRUDBooster::myParentId(), 'parent_slug' => $slug]);
            //   DB::table('optin_pages')->whereNull('user_id')->delete();
            // }

            $this->insertNewTag($request["tag"]);

            return response()->json(['success' => true, 'slug' => $optln->slug]);

        } catch (QueryException $exception) {
            return response()->json(false, 500);
        }
    }


    public function getEdit($id)
    {
        $data["id"] = $id;
        $data["colors"] = $this->colors;
        $data["buttonColors"] = $this->btnColors;
        $data["tags"] = $this->tags;
        $data["icons"] = $this->icons;
        $user_id = CRUDBooster::myParentId();
        $data["existing"] = DB::table("opln_mails")->where("user_id", $user_id)->find($id);

        if ($data["existing"]) {
          $data["design"] = DB::table('optin_pages')->where('parent_slug',$data["existing"]->slug)->select('slug','html','css')->first();
          $data["slug"] = ($data["design"]->slug) ? $data["design"]->slug : null;
          $path = public_path() . "/optin_templates.json";
          if(isLocal()){
            $path = public_path() . "/optin_templates_test.json";
          }
          $data["templates"] = json_decode(file_get_contents($path), true);
          $data['width'] = $data["existing"]->width;
          $data['height'] = $data["existing"]->height;
          $data['types']  = AppointmentType::where('user_id', $user_id)
              ->select('id','slug','name','link','link_status')
              ->get()
              ->each(function ($type) {
              $type->link = url('/appointment/booking').'/'.$type->slug.'?'.$type->link;
          });
            return view("admin.drm_dynamic_mail_system.page_builder_edit", $data);
        } else {
            CRUDBooster::redirectBack("Invalid action!", "error");
            return "";
        }
    }

    public function postEditSave($id)
    {
        $request = $_REQUEST;
        try {
            Validator::make($request, [
                "title" => "required",
                "tag" => "required",
                "fields" => "required|array|min:1",
                "content" => "required",
                "slug" => "required"
            ])->validate();
        } catch (ValidationException $e) {
            return response()->json(false, 200);
        }
        try {
            $pushData = [];
            array_push($pushData, $request["fields"]);

            $optln = OplnMail::find($id);
            $data = ["tag" => $request["tag"], "title" => $request["title"], "fav_color" => $request["custom_color"], "width" => $request["width"], "swoosh" => $request["swoosh_button"], "footer_note" => $request["editor_data"], "footer_text" => $request["footer_text"], "fields" => json_encode($pushData), "content" => $request["content"], "start_value" => $request["start_value"], "updated_at" => Carbon::now()];
            if (!empty($request["thankYouContent"])) {
                $data["thank_you_html"] = $request["thankYouContent"];
            }
            $optln->update($data);

            $this->insertNewTag($request["tag"]);

            return response()->json(['success' => true, 'slug' => $optln->slug]);
        } catch (QueryException $exception) {
            return response()->json(false, 500);
        }
    }

    public function getDelete($id){
       try {
           $opln = OplnMail::where('id', $id)->where('user_id', CRUDBooster::myParentId())->first();
           if ($opln && $opln->delete()) {
                DB::table('optin_pages')->where('parent_slug',$opln->slug)->delete();
               CRUDBooster::redirect(CRUDBooster::adminPath('opln_mails'), 'OptIn  deleted', 'success');
           } else {
               throw new Exception('Invalid action!');
           }
       } catch (Exception $exception) {
           CRUDBooster::redirect(CRUDBooster::adminPath('opln_mails'), 'Failed to Delete', 'danger');
       }
     }


    // Iframe
    public function emailIFrame($slug)
    {
        $data = [];
        $data["full"] = DB::table("opln_mails")->where("slug", $slug)->first();

        $user_id = DB::table("opln_mails")->where("slug", $slug)->value('user_id');


            $result = NewCustomer::where('user_id', $user_id)->doesntHave('orders')->count('id');
            $editor_content = DB::table('optin_pages')->where('user_id',$user_id)->where('parent_slug',$slug)->select('html','css')->first();
            $content = $editor_content->html;
            $data["html"] =  $editor_content->html;
            $total_leads = DB::table('new_customers')->where('user_id',$user_id)->sum('id');
            $pattern = '/<span class="ktchicklet">(\d*)/'; // ("){1}(>){1} // \d*)\W
            $replacement = '<span class="ktchicklet">' . $result;
            $data["html"] =  preg_replace($pattern, $replacement, $content);
          $data["css"] = $editor_content->css;
          return view("admin.drm_dynamic_mail_system.page_builder_iframe", $data);
        // return view("admin.drm_dynamic_mail_system.iframe", $data);
    }
    // Iframe post data
    public function postIframeData()
    {
        $request = $_REQUEST;

        try {
            $optlnForm = $request['opln'];
            $validator = Validator::make($optlnForm, [
                'email' => 'required|email',
                // 'name' => 'required|max:100',
            ]);

            if($validator->fails())
            {
                throw new \Exception(trim($validator->errors()->first()));
            }
            
            if($optlnForm['name'])
            {
                $formName = $optlnForm['name'];
                $hasSpecialChars = preg_match('/[#$%^&*()+=\-\[\]\';,.\/{}|":<>?~\\\\]/', $formName);
                $hasUrl = preg_match('/(http|ftp|mailto)/', $formName);

                if($hasUrl > 0 || $hasSpecialChars > 0) throw new \Exception('Invalid name format.');                
            }

            // Validate Email and phone on API
            $email = $request["opln"]["email"];
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);
            if(isset($request["opln"]["phone"]) && !empty($request["opln"]["phone"]))
            {
                $phone = $request["opln"]["phone"];
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
            }

        }catch(\Exception $e)
        {
            return view('optln-invalid', ['message' => $e->getMessage()]);
        }

        if ($request['slug']) {
            $opln = DB::table("opln_mails")->where("slug", $request["slug"])->first();
            if ($opln) {
                if ($request["opln"]["email"]) {
                    $slug = uniqid();
                    $oldCustomer = DB::table("new_customers")->where("email", $request["opln"]["email"])
                        ->where("user_id", $opln->user_id)->first();
                    if (empty($oldCustomer)) { // create this customer
                        $createBody = [];
                        if ($request["opln"]["email"]) $createBody["email"] = $request["opln"]["email"];
                        if ($request["opln"]["name"]) $createBody["full_name"] = $request["opln"]["name"];
                        if ($request["opln"]["phone"]) $createBody["phone"] = $request["opln"]["phone"];
                        if ($request["opln"]["company"]) $createBody["company_name"] = $request["opln"]["company"];
                        $createBody["user_id"] = $opln->user_id;
                        $createBody["slug"] = $slug;
                        $createBody["status"] = 0;
                        $createBody["source"] = 52;
                        $createBody["default_language"] = 'de';
                        $createBody["created_at"] = Carbon::now();
                        $createBody["updated_at"] = Carbon::now();
                        $createBody['insert_type'] = 10;
                        $customerId = DB::table("new_customers")->insertGetId($createBody);

                        DB::table("opln_mails")->where("slug", $request["slug"])->update([
                            "start_value" => $opln->start_value + 1,
                            "updated_at" => Carbon::now(),
                        ]);
                        // send verification email
                        if($customerId){
                            $data = [
                                'user_id' => $opln->user_id,
                                'slug' => $slug,
                                'customer_name' => $request["opln"]["name"],
                                'to_email' => $request["opln"]["email"]
                            ];
                            $this->sendVerificationEmail($data);
                        }

                    } else { // update new customer
                        $updateBody = [];
                        if ($request["opln"]["email"]) $updateBody["email"] = $request["opln"]["email"];
                        if ($request["opln"]["name"]) $updateBody["full_name"] = $request["opln"]["name"];
                        if ($request["opln"]["phone"]) $updateBody["phone"] = $request["opln"]["phone"];
                        if ($request["opln"]["company"]) $createBody["company_name"] = $request["opln"]["company"];
                        if (count($updateBody) > 0) $updateBody["updated_at"] = Carbon::now();
                        DB::table("new_customers")->where("email", $request["opln"]["email"])
                            ->where("user_id", $opln->user_id)->update($updateBody);
                        $customerId = $oldCustomer->id;
                        // app("App\Http\Controllers\AdminDrmAllCustomersController")->updateOrderCustomerInfo($customerId);

                        if(empty($oldCustomer->email_verification))
                        {
                            $data = [
                                'user_id' => $opln->user_id,
                                'slug' => $slug,
                                'customer_name' => $request["opln"]["name"],
                                'to_email' => $request["opln"]["email"]
                            ];
                            $this->sendVerificationEmail($data);
                        }
                    }
                    $customer_info = $createBody ?? $updateBody;

                    //insert tag
                    if (empty($request["opln"]["new_tag"])) {
                        $this->insertCustomerTag(trim($opln->tag), $opln->user_id, $customerId);
                    }else{
                        foreach ($request["opln"]["new_tag"] as $key => $value){
                            $this->insertCustomerTag(trim($value), $opln->user_id, $customerId);
                        }
                    }

                    if(!empty($request['type'])){
                        return response()->json([
                            'success' => true,
                            'html' => $opln->thank_you_html,
                            'message' => 'Store successfully!',
                        ]);
                    }


                   if($opln->link_switch == 1 && $opln->calendar_link && $customerId){
                       $customer_info['redirect_url']  = url('api/thank-you').'/'.$opln->slug;
                       return redirect()->to($opln->calendar_link.'&customer='.json_encode($customer_info));
                   }

                    return redirect()->route('thank-you', $opln->slug);
                }
            }
        }

//        return "<script>alert('There are no fields to generate a report');window.location.href='${$_SERVER["HTTP_REFERER"]}';</script>";
        return redirect()->away($_SERVER["HTTP_REFERER"]);
    }

    public function sendVerificationEmail($data)
    {
        try {
            $parsedData = DRMParseCustomerVerificationEmailTemplate($data);
            app('drm.mailer')->getMailer($data['user_id'],$parsedData['senderEmail'])->send('admin.new_order.email_invoice_template', ['body' => $parsedData['body']], function ($messages) use ($parsedData) {
                // $messages->from($parsedData['senderEmail']);
                $messages->to($parsedData['toEmail']);
                $messages->subject($parsedData['subject']);
                if (!empty($parsedData['bcc'])) {
                    $messages->bcc($parsedData['bcc']);
                }
            });
            return true;
        } catch (Exception $exception) {
            return false;
        }
    }

    protected function insertCustomerTag($tag_name, $user_id, $customer_id){
        try {
            DropfunnelCustomerTag::insertTag($tag_name, $user_id, $customer_id, 5);
        } catch (\Exception $ev) {
        }
    }

    // Generate embed link
    public function postEmbedLink()
    {
        try {
            $request = $_REQUEST;
            $id = $request["id"];

            $opln = DB::table("opln_mails")->find($id, ["slug"]);
            if ($opln) {
                $data = [];
                $data["url"] = url("drm-email-iframe/" . $opln->slug);
                $html = view("admin.drm_dynamic_mail_system.embed", $data)->render();
                return response()->json([
                    "success" => true,
                    "html" => $html
                ]);
            } else {
                throw new Exception("Invalid action.");
            }

        } catch (Exception $e) {
            return response()->json([
                "success" => false,
                "message" => $e->getMessage()
            ]);
        }

    }

    // Generate script link
    public function postScriptLink()
    {
        try {
            $request = $_REQUEST;
            $id = $request["id"];

            $opln = DB::table("opln_mails")->find($id, ['id',"slug",'popup_setting']);
            if ($opln) {
                $data = [];
                $data['id'] = $id;
                $data["url"] = url("popmodal.js?id=" . $opln->id);
                $obj = $opln->popup_setting;
                $data["setting"] = json_decode($opln->popup_setting,true);
                //dd($data["setting"]);
                $html = view("admin.drm_dynamic_mail_system.scriptform", $data)->render();
                return response()->json([
                    "success" => true,
                    "html" => $html
                ]);
            } else {
                throw new Exception("Invalid action.");
            }

        } catch (Exception $e) {
            return response()->json([
                "success" => false,
                "message" => $e->getMessage()
            ]);
        }

    }

     // Generate script link using slug
     public function postScriptLinkSlug()
     {
         try {
             $request = $_REQUEST;
             $slug = $request["slug"];
            //dd($slug);
             $opln = DB::table("optin_pages")->where('slug','TPgjTNbTYcAPyQGAbRKJ')->select('id',"slug",'popup_setting')->first();
  
             if ($opln) {
                 $data = [];
                 $data['id'] = $id ?? 0;
                 $data["url"] = url("popmodal.js?id=" . $opln->id);
                 $obj = $opln->popup_setting;
                 $data["setting"] = json_decode($opln->popup_setting,true);
                 //dd($data["setting"]);
                 $html = view("admin.drm_dynamic_mail_system.scriptform", $data)->render();
                 return response()->json([
                     "success" => true,
                     "html" => $html
                 ]);
             } else {
                 throw new Exception("Invalid action.");
             }
 
         } catch (Exception $e) {
             return response()->json([
                 "success" => false,
                 "message" => $e->getMessage()
             ]);
         }
 
     }

    public function zapierSecretKey()
    {
        $userId = CRUDBooster::myParentId();

        if (!CRUDBooster::isSuperadmin()) {
            $app_id = config('global.zapier_app_id');
            if (!DrmUserHasPurchasedApp($userId, $app_id)) {
                CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
            }
        }

        if (\request()->ajax()) {
            try {

                $name = Str::random(20);
                $sec = Str::random(50);

                DB::table('oauth_clients')->updateOrInsert([
                    "user_id" => $userId,
                ], [
                    "user_id" => $userId, "name" => $name, "secret" => $sec,
                    "personal_access_client" => "0", "password_client" => "1",
                    "revoked" => "0",
                    "created_at" => now(),
                    "updated_at" => now()
                ]);
                $data['name'] = $name;
                $data['secret'] = $sec;

                return response()->json($data);
            } catch (Exception $exception) {
                $data['name'] = '';
                $data['secret'] = '';
                return response()->json($data);
            }
        }

        $data = [];
        $data['page_title'] = 'Zapier';

        $fullData = DB::table('oauth_clients')->where("user_id", $userId)->first();
        if (!empty($fullData)) {
            $data['name'] = $fullData->name;
            $data['secret'] = $fullData->secret;
        }

        return view('admin.drm.zapier_token', $data);
    }

    public function showThankYou($slug)
    {
        $thankYouContent = DB::table('opln_mails')->where('slug', $slug)->first();
        $thankYou = null;
        if (!empty($thankYouContent)) {
            $thankYou = $thankYouContent->thank_you_html;
        }
        return view('admin.drm_dynamic_mail_system.thank_you', compact('thankYou'));
    }

    public function customerCountUsingTag(Request $request)
    {
        $tag_id = DB::table('dropfunnel_tags')->where('user_id', CRUDBooster::myParentId())->where(DB::raw('LOWER(`tag`)'), strtolower($request->tag))->value('id');
        $customerCount = 0;
        if (!empty($tag_id)) {
            $customerCount = DB::table('dropfunnel_customer_tags')->where('tag_id', $tag_id)->whereNull('deleted_at')->count();
        }
        return response()->json(['success' => true, 'count' => $customerCount]);
    }

    public function emmadedCodePage($slug)
    {
        $data = [];
        $data['page_title'] = 'Embaded Code For Opln Mail';
        $embadedPage = DB::table('opln_mails')->where('slug', $slug)->first();
        $content = $url = null;
        if (!empty($embadedPage)) {
            $url = url("drm-email-iframe/" . $embadedPage->slug);
            $content = '<iframe src="' . $url . '" width="800" height="600" style="border:none !important;"></iframe>';
        }
        $data['content'] = $content;
        $data['url'] = $url;
        return view('admin.drm_dynamic_mail_system._iframe_view', $data);
    }

    private function insertNewTag($tag)
    {
        $authId = CRUDBooster::myParentId();
        $isTagExists = DropfunnelTag::where('user_id', $authId)
            ->where(DB::raw('LOWER(`tag`)'), strtolower($tag))
            ->exists();

        if ($isTagExists) {
            return false;
        }

        $newTag = new DropfunnelTag();
        $newTag->tag = $tag;
        $newTag->user_id = $authId;
        $newTag->save();

        return true;
    }




    public function pageBuilder()
    {
        $data['page_title'] = 'OptIn Box';
        $data["colors"] = $this->colors;
        $data["buttonColors"] = $this->btnColors;
        $data["icons"] = $this->icons;
        $data["page_data"] = DB::table('optin_pages')
            ->where('user_id', CRUDBooster::myParentId())
            ->orderBy('id', 'desc')
            ->value('id');
        // $da = json_decode(Storage::disk('public')->get('optin_templates.json'));
        $path = public_path() . "/optin_templates.json";
        $page = "admin.drm_dynamic_mail_system.page_builder";
        if(isLocal()){
         $path = public_path() . "/optin_templates_test.json";
         $page = "admin.drm_dynamic_mail_system.page_builder_test";
        }
        $data["templates"] = json_decode(file_get_contents($path), true);
        $data['slug'] = $name = Str::random(20);
        $data["tags"] = $this->tags;
        return view($page, $data);
    }

    public function savePageDesign(Request $request)
    {
        try {
            DB::table('optin_pages')->updateOrInsert(
                ['slug' => $request->slug],
                [
                    'assets' => $request['gjs-assets'],
                    'components' => $request['gjs-components'],
                    'html' => $request['gjs-html'],
                    'css' => $request['gjs-css'],
                    'styles' => $request['gjs-styles']
                ]);
            return response()->json(['message' => 'Insert Successfullly!']);

        } catch (\Exception  $e) {
            return response()->json(['message' => 'Something went wrong']);
        }
    }

    public function loadPageDesign($slug)
    {

        $template = DB::table('optin_pages')
            ->where('slug', $slug)
            ->first();

        $data = [];
        $data["page_id"] = $template->id;
        $data["assets"] = $template->assets;
        // $data["components"] = $template->components;
        $data["html"] = $template->html;
        $data["css"] = $template->css;
        $data["styles"] = $template->styles;
        return $data;

    }

    public function saveTemplate(Request $request)
    {

        try {
            Validator::make($request->all(), [
                "title" => "required",
                // "content" => "exists:optin_pages,id,slug,".$request->slug,
            ])->validate();
        } catch (ValidationException $e) {
            return response()->json($e->errors(), 201);
        }
        try {
            $slug = uniqid();
            $text = DB::table('optin_pages')->where('slug',$request["slug"])->select('html')->first();
            $popup = DB::table('optin_pages')->where('slug',$request["slug"])->select('popup_setting')->first() ?? '';
            $pattern = '/type="hidden"/i';
            $content = preg_replace($pattern, 'type="hidden" name="slug" value="'.$slug.'"', $text->html);
            $form_pattern = '/method="post"/i';
            $content = preg_replace($form_pattern, 'method="post" class="'.$slug.'"', $content);
            $optln = OplnMail::create([
                "user_id" => CRUDBooster::myParentId(),
                "tag" => $request["tag"] ?? 'Leads',
                "title" => $request["title"],
                "thank_you_html" => $request["thank_you_page"],
                "fields" => "null",
                "content" => $content,
                "slug" => $slug,
                "height" => $request['height'],
                "width" => $request['width'],
                "calendar_id" => $request["calendar_id"],
                "calendar_link" => $request["calendar_link"],
                "link_switch" => $request["link_switch"],
                "created_at" => Carbon::now(),
                "updated_at" => Carbon::now(),
                "popup_setting"=>$popup ? $popup->popup_setting : '',
            ]);
              DB::table('optin_pages')->where('slug', $request["slug"])->update(['user_id' => CRUDBooster::myParentId(), 'parent_slug' => $slug ,'html' => $content]);
              DB::table('optin_pages')->whereNull('user_id')->delete();
            if($request["tag"]){
                $this->insertNewTag($request["tag"]);
            }


//            $url = 'https://drm.software/drm-email-iframe/622f2f608e9ff';
//            $image = ($this->urlToImage($url)) ? $this->urlToImage($url,800, 1250) : null;
            $url = 'https://drm.software/drm-email-iframe/'.$optln->slug;
            $image = ($this->urlToImage($url)) ? $this->urlToImage($url,$request['height'],$request['width']) : null;

            DB::table('opln_mails')->where('slug',$slug)->update(['thumbnail' => $image]);

            return response()->json(['success' => true,'template_id' => $optln->id, 'slug' => $optln->slug]);

        } catch (QueryException $exception) {

            return response()->json($exception, 500);
        }
    }
    public function updateTemplate(Request $request){

        try {
            Validator::make($request->all(), [
                "title" => "required",
                "slug" => "required"
            ])->validate();
        } catch (ValidationException $e) {
            return response()->json(false, 200);
        }

        try {
           $optln = OplnMail::find($request["id"]);
//         $url = 'https://drm.software/drm-email-iframe/61878bce1b96a';
//         $image = ($this->urlToImage($url)) ? $this->urlToImage($url,800, 1250) : null;
            $url = 'https://drm.software/drm-email-iframe/'.$optln->slug;
            $image = ($this->urlToImage($url)) ? $this->urlToImage($url,$request['height'],$request['width']) : null;

            $text = DB::table('optin_pages')->where('parent_slug',$optln->slug)->select('html','slug')->first();
            $content = str_replace('<input type="hidden">', '<input type="hidden" name="slug" value="'.$optln->slug.'">', $text->html);
            $data = [
                "tag" => $request["tag"] ?? 'Leads',
                "thumbnail" => $image,
                "title" => $request["title"],
                "height" => $request["height"],
                "width" => $request["width"],
                "calendar_id" => $request["calendar_id"],
                "calendar_link" => $request["calendar_link"],
                "link_switch" => $request["link_switch"],
                "updated_at" => Carbon::now()
            ];
            if (!empty($request["thank_you_page"])) {
                $data["thank_you_html"] = $request["thank_you_page"];
            }
            $pattern = '/type="hidden"/i';
            $content = preg_replace($pattern, 'type="hidden" name="slug" value="'.$optln->slug.'"', $text->html);
            $form_pattern = '/method="post"/i';
            $content = preg_replace($form_pattern, 'method="post" class="'.$optln->slug.'"', $content);
            $optln->update($data);
            DB::table('optin_pages')->where('slug', $text->slug)->update(['html'=> $content]);
            DB::table('optin_pages')->whereNull('user_id')->delete();
            if($request["tag"]){
                $this->insertNewTag($request["tag"]);
            }

            return response()->json(['success' => true, 'slug' => $optln->slug]);
        } catch (QueryException $exception) {
            return response()->json(false, 500);
        }
    }
      public function templateSource(Request $request){
        if($request->source){
              Session::put('source',$request->source);
              $url =  url('/admin/template-preview');
              $iframe =  '<iframe id="iframe_div" style="height:936px;" src="'.$url.'" title="description"></iframe>';
              return response()->json(['success' => true, 'iframe' => $iframe]);
        }else{
            return response()->json(['success' => false, 'message' => 'Template source not found!']);
        }
      }
    public function templatePreview(){
      $data['source'] = Session::get('source');
      $data["colors"] = $this->colors;
        $data["icons"] = $this->icons;
      return view("admin.drm_dynamic_mail_system.preview", $data);
    }
    public function templateForm(Request $request){
      if($request->template_id){
            // $template = DB::table('opln_template')->where('id',$request->template_id)->value('source');
            $path = public_path() . "/optin_templates.json";
            if(isLocal()){
                $path = public_path() . "/optin_templates_test.json";
            }
            $template = json_decode(file_get_contents($path), true);
            $key = array_search($request->template_id, array_column($template, 'id'));
            Session::put('source',$template[$key]['source']);

          $content = $template[$key]['source'];
          $pattern = '/<label for="DSGVOCheckbox" id="DSGVOCheckboxL" style="margin-left: 17px;">(\d*)/'; // ("){1}(>){1} // \d*)\W
          $replacement = '<label for="DSGVOCheckbox" id="DSGVOCheckboxL" style="margin-left: 17px;"> '.__("optin footer message");

          $content = preg_replace($pattern, $replacement, $content);
            $url =  url('/admin/template-preview');
            $iframe =  '<iframe id="iframe_div" style="height:612px;width:100%;" src="'.$url.'" title="description"></iframe>';
            $iframe  = $content;
            return response()->json([
                'success' => true,
                'iframe' => $iframe,
                'height' => $template[$key]['height'],
                'width' => $template[$key]['width']
            ]);
      }else{
          return response()->json(['success' => false, 'message' => 'Template source not found!']);
      }
    }

    public function urlToImage($target_url, $height = 800 , $width = 1000)
    {
        try {
            $url = 'https://drm.software/api/url-to-image-convert';
            $client = new \GuzzleHttp\Client();
            $response = $client->request('POST', $url, [
                'form_params' => [
                    'target_url' => $target_url,
                    'width' => $width,
                    'height' => $height,
                    'token' => 'xvamc4s3wesses4f'
                ]
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new \Exception('Connection problem!');
            }

            return $response->getBody()->getContents();

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'url' => null,
                'message' => $e->getMessage()
            ]);
        }
    }

    function popupSetting(){
        $req = request();
        $settings = [
            'active_status'=> $req->active_status ?? 0,
            'on_load' => $req->load ?? 0,
            'load_time' => $req->load_time ?? 0,
            'on_leave'=> $req->leave ?? 0, 
        ];
        $update = DB::table('opln_mails')
                ->where('id',$req->optin_id)
                ->update([
                'popup_setting'=>json_encode($settings),
                ]);
        
        return response()->json([
            "success" => true,
            "message" => 'Setting Updated Succesfully'
        ]);

    }

    function popupSettingSlug(){
        $req = request();
        $settings = [
            'active_status'=> $req->active_status ?? 0,
            'on_load' => $req->load ?? 0,
            'load_time' => $req->load_time ?? 0,
            'on_leave'=> $req->leave ?? 0, 
        ];
        $update = DB::table('optin_pages')
                ->where('slug',$req->optin_slug)
                ->update([
                'popup_setting'=>json_encode($settings),
                ]);
        
        return response()->json([
            "success" => true,
            "message" => 'Setting Updated Succesfully'
        ]);

    }
    function checkSignature(){
        $req = request();
        $data = [];
        if(!empty($req->placeholders)){
            foreach($req->placeholders as $placeholder){
                $number = preg_replace('/[^0-9]/', '', $placeholder);
                $signature = DB::table('drop_funnel_signatures')->find($number);
                $text = array(
                    "placeholder"=>$placeholder,
                    "text"=>$signature->signature
                );

                $data[] = $text;
               
            }
        }
       return $data;
    }
}
