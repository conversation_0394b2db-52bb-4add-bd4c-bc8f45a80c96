<?php namespace App\Http\Controllers;

use App\Country;
use App\DrmProduct;
use App\DropfunnelCustomerTag;
use App\Enums\Apps;
use App\offerCard;
use App\OfferDeal;
use App\DealHistory;
use App\NewCustomer;
use App\NewOrder;
use App\Traits\Offer;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use DB;
use Google\Service\AdExchangeBuyerII\Deal;
use http\Env\Response;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\CustomerPipelineRemainder;
use App\DealPlan;
use App\Models\UserTags;
use App\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\JsonResponse;

class AdminPipelineController extends Controller
{
    use Offer;
    public function index()
    {
        redirectToV2('/orders/pipeline');

        $user_id              = CRUDBooster::myParentId();
        $is_super_or_key_user = isSuperOrKeyUser();
        $filter_by = $_GET['filter_by'] ?? '';

        $version = isset($_GET['version']) ? (int) $_GET['version'] : 1;

        $saved_version = \App\Option::where([
            'option_key' => 'pipeline_version',
            'option_group' => 'pipeline_version',
            'user_id' => $user_id,
        ])->value('option_value');

        if (isset($_GET['version']) || !empty($saved_version)) {
            if ($version != $saved_version) {
                \App\Option::updateOrCreate([
                    'option_key' => 'pipeline_version',
                    'option_group' => 'pipeline_version',
                    'user_id' => $user_id,
                ],
                [
                    'option_value' => $version,
                ]);
            }
        }

        $deals_query = OfferDeal::with(['offer' => function($offer) {
            return $offer->with('customer:id,full_name,company_name')
                    ->select('id', 'drm_customer_id', 'total', 'cart', 'status');
        }]);

        $deals_query->join('cms_users', function ($join_users) {
            $join_users->on('offer_deal.user_id', '=', 'cms_users.id');
        });

        $deals_query->leftJoin('appointments', function ($join) {
            $join->on('offer_deal.appointment_id', '=', 'appointments.id');
        });

        $deals_query->where('offer_deal.version', $version);

        // filters
        if (!empty($_GET['filter_by'])) {
            if ($filter_by == 'manual' || isAutoDeal($filter_by)) {
                $deals_query->where('offer_deal.source', $filter_by);
            }
            else if ($filter_by == 'lost') {
                $deals_query->withTrashed()->whereNotNull('deleted_at');
            } 
        }

        // search deal 
        if (isset($_GET['search_q']) && !empty($_GET['search_q'])) {
            $deals_query->where('offer_deal.title', 'LIKE', '%' . $_GET['search_q'] . '%');
        }

        $deals_query->where('offer_deal.user_id', $user_id)
            ->orWhere(function($query) use ($user_id, $filter_by) {
                if (CRUDBooster::isKeyAccount()) { // key account has access to see registration leads
                    $query->where('offer_deal.source', 'registration')->whereNotNull('appointment_id');
                }
                else if ($user_id == 2455 && in_array($filter_by, ['', 'calendar'])) { // show daily account calendar leads to drm account
                    $query->where([
                        'offer_deal.user_id' => 98,
                        'offer_deal.source'  => 'calendar',
                    ]);
                }
            });

        $deals       = $deals_query->select('offer_deal.*', 'appointment', 'cms_users.photo', 'cms_users.name as creator_name')->orderBy('offer_deal.id', 'desc')->get();
        $deal_titles = $deals->pluck('title', 'id')->toArray();
        $deals       = $deals->groupBy('stage_id');

        $data['page_title'] = 'Sales pipeline';
        $data['stages'] = $data['stage_body'] = $this->stages('all', $version);
        foreach ($deals as $key => $deal){
            if (isset($data['stages'][$key])) {
                $data['stage_body'][$key] = $deal;
            } else {
                // $data['stage_body'][999999] = $deal;
            }
        }

        $data['max_stage'] = $this->maxAvailableStage($user_id);

        $data['deal_titles'] = $deal_titles;
        $data['is_super_or_key_user'] = $is_super_or_key_user;

        $data['tariff_plans'] = $is_super_or_key_user ? $this->getTariffPlans() : [];
        $data['deal_plans']   = $is_super_or_key_user ? $this->getDealPlans() : [];
        $data['version'] = $version;

        return view('admin.sales_pipeline.index', $data);
    }

    public function deal($id)
    {

        redirectToV2('/orders/pipeline');

        $data = [];
        $user_id = autoDealOwnerId($id);
        $is_super_or_key_user = isSuperOrKeyUser();

        $deal = OfferDeal::withTrashed()->where([
            'id'      => $id,
            'user_id' => $user_id,
        ])->first();
        if(empty($deal)) abort(404);

        if ($deal->user_id != $user_id) {
            $abort_details = true;
            if ((CRUDBooster::isKeyAccount() && ($deal->source == 'registration'))) {
                $abort_details = false;
            } 
            else if ($user_id == 2455 && $deal->user_id == 98) {
                $abort_details = false;
            }

            if ($abort_details) abort(404);
        }

        $tagesHistory = DealHistory::where('deal_id', $deal->id)
        ->whereNotNull('stage')
        ->select('id', 'stage', 'created_at')
        ->get()
        ->groupBy('stage')
        ->map(function($item) use ($deal) {
            return $item->sortByDesc('id')->first()->created_at->format('Y-m-d H:i:s');
        })->toArray();

        $offer = NewOrder::with('customer:id,full_name,company_name')->where('id', $deal->offer_id)->select('id', 'drm_customer_id', 'total', 'cart', 'client_note', 'intro_note', 'offer_remainder', 'pdf_link', 'status')->first();
        $data['offer'] = $offer ?? (object) [];
        $data['customer'] = $offer->customer ?? NewCustomer::find($deal->drm_customer_id);
        $data['deal_info'] = $deal;
        $data['stages'] = $this->stages('all', $deal->version);
        $data['tags'] = !empty($deal->tags) && is_array($deal->tags) ? $deal->tags : [];

        $data['products'] = collect(json_decode($deal->offer->cart, true) ?? [])
        ->map(function($product) use ($deal) {
            $tax_rate = (float)$product['tax'];
            $amount = (float)$product['amount'];
            $totalTax = ($amount * $tax_rate) / 100;
            $product['amount'] = round(($amount + $totalTax), 2);
            $product['total'] = round($deal->offer->total, 2);
            return $product;
        })
        ->toArray();

        $data['stage_history'] = $tagesHistory;
        $data['has_offer_email'] = DB::table('drm_offer_mail')->where('cms_user_id', $user_id)->exists();
        $data['page_title'] = 'Sales pipeline';

        $data['is_super_or_key_user'] = $is_super_or_key_user;

        $data['tariff_plans'] = $is_super_or_key_user ? $this->getTariffPlans() : [];
        $data['deal_plans']   = $is_super_or_key_user ? $this->getDealPlans() : [];

        return view('admin.sales_pipeline.deal', $data);
    }


    public function dealHistory($id)
    {
        try {
            $user_id = autoDealOwnerId($id);
            $data = OfferDeal::withTrashed()->where([
                'user_id'=> $user_id,
                'id' => $id
            ])->value('id');
            if(empty($id)) throw new Exception('Empty data!');

            $history = DealHistory::where('deal_id', $id)->select('created_at', 'message')->orderBy('id', 'desc')->get();
            return view('admin.sales_pipeline.partials.history', compact('history'));

        } catch(\Exception $e) {
            return '';
        }
    }

    //Save covers
    public function saveCovers(Request $request, $id)
    {

        $user_id = autoDealOwnerId($id);
        try {

            $offer_id = OfferDeal::withTrashed()->where(['id'=> $id, 'user_id' => $user_id])->value('offer_id');
            if(empty($offer_id)) throw new Exception('Invalid access!');


            $payload = $request->only(['client_note', 'intro_note']);
            $url = null;

            if ($request->hasFile('pdf')) {
                $document = $request->file('pdf');
                $image_url = uploadImage($document, 'pipe_line/ducument-note/' . $user_id);
                $payload['pdf_link'] = $image_url;
                $url = $image_url;
            }

            if(NewOrder::where('id', $offer_id)->update($payload))
            {
                return response()->json(['success' => true, 'message'=>'Saved successfully!', 'url' => $url], 200);
            } else {
                throw new Exception('Nothing changed!');
            }

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }


    public function saveTags(Request $request, $id)
    {
        $user_id = autoDealOwnerId($id);
        try {

            $offer_id = OfferDeal::withTrashed()->where(['id'=> $id, 'user_id' => $user_id])->value('offer_id');
            if(empty($offer_id)) throw new Exception('Invalid access!');

            $offer = NewOrder::where('id', $offer_id)->select('drm_customer_id', 'cms_user_id')->first();
            if(empty($offer)) throw new Exception('Invalid access!');

            $tags = [];
            $manual_tags = $request->input('tags', []);
            foreach ($manual_tags as $manual_tag) {
                $tag = trim($manual_tag);
                if ($tag) {
                    //insert tag
                    try {
                        $res = DropfunnelCustomerTag::insertTag($tag, $user_id, $offer->drm_customer_id, 27, $offer_id);

                        if(isset($res['tag_id']) && $res['tag_id'])
                        {
                            $tags[] = ['label' => $tag, 'value' => $res['tag_id']];
                        }
                    } catch (\Exception $ev) {}
                }
            }
            OfferDeal::withTrashed()->where('id', $id)->update(['tags' => $tags]);
            return response()->json(['success' => true, 'message'=>'Saved successfully!'], 200);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }

    public function updoadDocument(Request $request){
       
        $user_id = autoDealOwnerId($request->deal_id);
        try {



            $deal = OfferDeal::withTrashed()->where(['id'=> $request->deal_id, 'user_id' => $user_id])->first();
            if(empty($deal)) throw new Exception('Invalid access!');

            $offer = NewOrder::where('id', $deal->offer_id)->first();
            if(empty($offer)) throw new Exception('Invalid access!');


            if ($request->hasFile('document')) {
                $document = $request->file('document');
                
                $image_url = uploadImage($document, 'pipe_line/ducument/' . $user_id);
              
                $deal->update([
                    'document' => $image_url
                ]);

                $this->insertHistory($request->deal_id, "Document uploaded: ". $image_url);

                    return response()->json(['success' => true,'message'=>'Document upload successfully!', 'url' => $image_url], 200);
                }
        } catch (\Exception $e) {
            return response()->json(['success' => false], 422);
        }
        return response()->json(['success' => false,'message'=>'Document upload failed!'], 400);
    }

    public function saveDeal($reqData = []){
        $_REQUEST     = !empty($reqData) ? $reqData : $_REQUEST;
        $deal_source  = $_REQUEST['source'] ?? 'manual';

        $auto_deal    = ($deal_source != 'manual') ? true : false;
        $deal_user_id = $auto_deal ? $_REQUEST['user_id'] : CRUDBooster::myParentId();

        Validator::make($_REQUEST, [
            'Email' => 'required|email',
            'title' => 'required|string',
            'deal_amount' => 'required',
            'product_name.*' => 'required',
            'price.*' => 'required',
            'qty.*' => 'required',
        ])->validate();

        $carts = [];
        foreach($_REQUEST['product_name'] ?? [] as $k => $productName)
        {
            $cart = [
                "id" => $k + 1,
                "product_name" => $_REQUEST['product_name'][$k],
                "description" => "",
                "qty" => removeCommaFromPrice($_REQUEST['qty'][$k]),
                "rate" => removeCommaFromPrice($_REQUEST['rate'][$k]),
                "tax" => removeCommaFromPrice($_REQUEST['tax'][$k]),
                "product_discount" => 0,
                "amount" => removeCommaFromPrice($_REQUEST['amount'][$k]),
            ];
            $carts[] = $cart;
        }

        $stage      = (int)$_REQUEST['stage_id'] ?? 1;
        $send_email = (int)$stage === 4;  
        if ($auto_deal) {
            $send_email = false;
        }

        $payload = [
            "email"  => $_REQUEST['Email'],
            'full_name' => $_REQUEST['contact_name'],
            'company_name' => $_REQUEST['organization'],
            'phone' => $_REQUEST['phone'] ?? null,
            'offer_remainder' => $_REQUEST['offer_remainder'] ?? null,
            'carts' => $carts,
            'send_email' => $send_email,
            'auto_deal'    => $auto_deal,
            'deal_user_id' => $deal_user_id,
            'deal_amount' => $_REQUEST['deal_amount'] ?? 0,
        ];

        if($stage < 4)
        {
            $payload['status'] = 'offer_created';
        }

        $resOffer = app('App\Http\Controllers\AdminDrmAllOrdersController')->createPipelineOffer($payload);
        $offer = $resOffer['offer'];
        $_REQUEST['drm_customer_id'] = $resOffer['drm_customer_id'];

        $deal_id = $this->insertDeal($_REQUEST, $offer->id ?? 0);
        if($deal_id){
            $this->insertTag($deal_id, $stage);
       
            if (isAutoDeal($deal_source)) {
                $msg = '';

                if ($deal_source == 'registration') {
                    $msg = (($deal_user_id == 2439) ? 'DT' : 'DRM') . ' user registration deal added automatically.';
                } else if ($deal_source == 'calendar') {
                    $msg = 'Calendar deal added as ' . CRUDBooster::me()->email . ' set an appointment over calendar.';
                } else if ($deal_source == 'contact') {
                    $msg = 'Contact deal added.';
                } else if ($deal_source == 'zapier') {
                    $msg = 'Zapier deal added.';
                }
            
                if (!empty($msg)) {
                    $this->insertHistory($deal_id, $msg);
                }
            }
        } else {
            return response()->json([
                'success' => false,
                'data' => [],
                'message' => __('Failed to create Deal!')
            ], 400);
        }

        $_REQUEST['deal_id'] = $deal_id;
        $_REQUEST['url'] = CRUDBooster::adminPath('sales-pipeline/show-deals').'/'.$deal_id;
        $_REQUEST['deal_amount_html'] = number_format((float)($offer->total ?? 0),2,',','.');
        $_REQUEST['deal_amount'] = (float)$offer->total ?? 0;

        $this->insertHistory($deal_id, '<p>The offer deal Invoice created Successfully</p>');

        if (request()->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $_REQUEST,
                'message' => __('Deal created successfully!')
            ]);
        }
    }

    public function updateDealStage($reqData = []){

        try {
            $_REQUEST = !empty($reqData) ? $reqData : $_REQUEST;

            $deal_id = $_REQUEST['deal_id'];
            $user_id = $_REQUEST['deal_user_id'] ?? CRUDBooster::myParentId();

            $deal = OfferDeal::withTrashed()->where([
                'id' => $deal_id,
                'user_id' => $user_id,
            ])
            ->first();

            if(empty($deal) || empty($deal->id)) throw new \Exception('Invalid access!');
            $isForwardMove = $deal->stage_id < $_REQUEST['stage_id'];
            
            // deal update
            $updateParams = [
                'stage_id' => $_REQUEST['stage_id'],
            ];
            if (isset($_REQUEST['user_remove_time'])) {
                $updateParams['situation'] = 'account_deleted';
            }
            $deal->update($updateParams);

            $this->insertTag($_REQUEST['deal_id'], $_REQUEST['stage_id']);
            $this->GenerateMessagAndInsertHistory($deal_id, $_REQUEST['stage_id'], $_REQUEST['old_stage_id']);

            $stage = (int)$_REQUEST['stage_id'];
            if ($deal->source == 'manual') {
            if($isForwardMove && in_array($stage, [4, 5])) {   
                // Stage 5
                if($stage === 4)
                {
                    // Deal customer
                    $customer_id = DB::table('new_orders')->where('id', $deal->offer_id)->value('drm_customer_id');
                    if($customer_id && !(CustomerPipelineRemainder::where('customer_id', $customer_id)->exists()))
                    {
                        CustomerPipelineRemainder::updateOrCreate(['customer_id' => $customer_id]);
                        DB::table('new_customers')->where('id', $customer_id)->update(['deal_mail_sent' => now()]);
                    }
                }

                if($stage === 4 && DB::table('drm_offer_mail')->where('cms_user_id', $user_id)->where('auto_mail', 1)->exists())
                {      
                    app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($deal->offer_id);

                } else if($stage === 5 && DB::table('drm_offer_remainder_mail')->where('cms_user_id', $user_id)->where('auto_mail', 1)->exists()) {

                    app('App\Http\Controllers\AdminDrmAllOrdersController')->offerRemainderEmail($deal->offer_id);
                }
            }
            }

            $message = 'Deal moved to '. $this->stages($_REQUEST['stage_id']) .'.';

            if (isset($_REQUEST['log_message'])) {
                $this->insertHistory($deal_id, 'Deal moved to ' . $this->stages($_REQUEST['stage_id']) . ' ' . $_REQUEST['log_message']);
            }
            
            if (request()->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => $message
                ]);
            }
        }catch(\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }


    public function resendOffer($id)
    {
        if (app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($deal->offer_id))
        {
            return response()->json([
                'success' => true,
                'message' => "Offer send successfully!"
            ]); 
        }

        return response()->json([
            'success' => false,
            'message' => 'Offer send failed!'
        ], 400);
    }


    public function updateDealTitle($id)
    {
        $title = request()->title;

        $user_id = CRUDBooster::myParentId();
        
        $conditionArr = [ 'id' => $id ];
        if (!isSuperOrKeyUser()) {
            $conditionArr['user_id'] = $user_id;
        }

        $deal = OfferDeal::withTrashed()->where($conditionArr)->first();

        if(empty($deal) || $deal->title == $title)
        {
            return response()->json([
                'success' => false,
                'message' => 'Nothing changes!',
            ], 400);
        }

        if($deal->update(['title' => $title]))
        {
            $this->insertHistory($id, "Deal title changed: ". $title);
            return response()->json([
                'success' => true,
                'message' => __('Deal title updated successfully!'),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Nothing changes!',
        ], 400);
    }

    public function updateDealPrice($id)
    {
        $price = request()->price;

        $deal = OfferDeal::withTrashed()->where('id', $id)->first();
        if(empty($deal)) {
            return response()->json([
                'success' => false,
                'message' => __('Deal not found!'),
            ], 400);
        }

        $order_data = NewOrder::where('id', $deal->offer_id)->first();
        if(empty($order_data) || $order_data->total == $price) {
            return response()->json([
                'success' => false,
                'message' => __('Nothing changes!'),
            ], 400);
        }

        $price_format = number_format($price, 2, ',' , '.');

        // price update time, change deal+cart product title
        $deal_title = $deal->title;
        $title_arr  = explode(' - ', $deal->title);
        
        $total_title_portions = count($title_arr);
        if ($total_title_portions >= 2) {
            $price_index             = ($total_title_portions == 2) ? 1 : $total_title_portions-1;
            $title_arr[$price_index] = $price_format . ' EUR';
            $deal_title              = implode(' - ', $title_arr);

            $deal->update(['title' => $deal_title]);
        }

        $carts     = json_decode($order_data->cart);
        $cart_data = collect($carts)->map(function ($item) use ($price, $deal_title) {
            $item->product_name = $deal_title;
            $item->rate         = $price;
            $item->amount       = $price;
            
            return $item;
        })
        ->toJson();

        $updateOrderContent = [
            'total'               => $price,
            'eur_total'           => $price,
            'sub_total'           => $price,
            'dropmatix_sub_total' => $price,
            'cart'                => $cart_data,
        ];

        if($order_data->update($updateOrderContent)) {
            $this->insertHistory($id, "Deal price changed: ". $price);

            return response()->json([
                'success'      => true,
                'message'      => __('Price changed successfully!'),
                'deal_title'   => $deal_title,
                'price_format' => $price_format,
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => __('Nothing changes!'),
        ], 400);
    }

    public function updateDealProduct($id)
    {
        $deal = OfferDeal::withTrashed()->where('id', $id)->first();
        if (empty($deal)) {
            return response()->json([
                'success' => false,
                'message' => __('Deal not found!'),
            ], 400);
        }

        $order_data = NewOrder::where('id', $deal->offer_id)->first();
        if(empty($order_data)) {
            return response()->json([
                'success' => false,
                'message' => __('Order not found!'),
            ], 400);
        }

        $plan_title = request()->plan_title;
        $plan_price = request()->plan_price;

        if ($deal->source == 'manual') {
            $product_title = ((CRUDBooster::myParentId() == 2439) ? 'DT ' : 'DRM ') . $plan_title . ' Tarif';
        } else {
            $deal_title_arr = explode(' - ', $deal->title);
            $user_name      = $deal_title_arr[0];
            $product_title  = $user_name . ' - ' . $plan_title;
        }
  
        $carts     = json_decode($order_data->cart);
        $cart_data = collect($carts)->take(1)->map(function ($item) use ($plan_price, $product_title) {
            $item->product_name = $product_title;
            $item->rate         = $plan_price;
            $item->amount       = $plan_price;
            $item->qty          = 1;

            return $item;
        })
        ->toJson();

        $updateOrderContent = [
            'total'               => $plan_price,
            'eur_total'           => $plan_price,
            'sub_total'           => $plan_price,
            'dropmatix_sub_total' => $plan_price,
            'cart'                => $cart_data,
        ];

        if($order_data->update($updateOrderContent)) {
            $this->insertHistory($id, "Deal product changed");

            if ($deal->source != 'manual') {
                $deal->update(['title' => $product_title]);
            }

            return response()->json([
                'success'       => true,
                'message'       => __('Deal product changed successfully!'),
                'product_title' => $product_title,
                'price_format'  => number_format($plan_price, 2, ',' , '.'),
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => __('Nothing changes!'),
        ], 400);
    }


    public function saveDealNote(){

        $user_id = CRUDBooster::myParentId();

        $conditionArr = [ 'id' => $_REQUEST['deal_id'] ];
        if (!isSuperOrKeyUser()) {
            $conditionArr['user_id'] = $user_id;
        }
        
        OfferDeal::withTrashed()->where($conditionArr)->update([
            'note' => $_REQUEST['note'],
        ]);

        return response()->json([
            'success' => true,
            'message' => __('Deal note updated successfully!')
        ]);
    }

    // single won/loss, bulk won/loss, purchased, assigned
    public function updateDealStatus($reqData = [])
    {
        $_REQUEST      = !empty($reqData) ? $reqData : $_REQUEST;
        $action_status = strtolower($_REQUEST['status']);

        $deal_id = $_REQUEST['deal_id'];
        $deal    = OfferDeal::withTrashed()->find($deal_id);

        if ($action_status == 'offer_won_open') {
            $action_status = 'offer_created';
            $this->insertHistory($deal_id, "Deal status updated from Won to Open.");
        }
        else if ($action_status == 'offer_lost') {
            $this->updateLostOpenStatus($deal_id, $deal);

            return response()->json([
                'success' => true,
                'message' => __('Deal status updated successfully!'),
            ]);
        }

        $purchased_or_assigned = $_REQUEST['purchased_or_assigned'] ?? null;
        $generate_invoice      = $_REQUEST['generate_invoice'] ?? false;
        $send_invoice          = $_REQUEST['send_invoice'] ?? false;

        if (in_array($purchased_or_assigned, ['purchased', 'assigned'])) {
            $deal->update([
                'situation' => $purchased_or_assigned,
                'plan_id'   => $_REQUEST['plan_id'] ?? null,
            ]);
        }

        // user purchases: may have a discount or up/low plan selected
        if (isset($_REQUEST['row']) && !empty($_REQUEST['row'])) {
            $row                = $_REQUEST['row'];
            $plan_total         = $row['total'];
            $plan_sub_total     = $row['sub_total']; //
            $plan_discount      = $row['discount'] ?? null;
            $plan_discount_type = $row['discount_type'] ?? null; //
            $plan_tax_rate      = $row['tax_rate'] ?? null;
            $plan_tax_version   = $row['tax_version'] ?? null;
            $plan_total_tax     = $row['total_tax'] ?? null;

            $is_dt_user         = $_REQUEST['is_dt_user'] ?? is_dt_user();
            if ($plan_total != $is_dt_user ? 39 : 179) {
                // purchase or admin assign time, change deal title
                $deal_title     = $deal->title;
                $title_arr      = explode(' - ', $deal->title);
                $title_arr[count($title_arr)-1] = $plan_total . ' EUR';
                $deal_title = implode(' - ', $title_arr);

                if (isset($_REQUEST['plan_id'])) {
                    $plan_name = DB::table('import_plans')->where('id', $_REQUEST['plan_id'])->value('plan');
                    if (!empty($plan_name)) {
                        $deal_title = str_replace("Starter", ucfirst(strtolower($plan_name)), $deal_title);
                    }
                }

                $deal->update(['title' => $deal_title]);

                $offer_order = NewOrder::find($deal->offer_id);

                $carts     = json_decode($offer_order->cart);
                $cart_data = collect($carts)->map(function ($item) use ($deal_title, $plan_total, $plan_tax_rate) {
                    $item->product_name   = $deal_title;
                    $item->rate   = $plan_total;
                    $item->amount = $plan_total;
                    $item->tax    = $plan_tax_rate;
                    return $item;
                })
                ->toJson();

                $offer_order->update([
                    'total'               => $plan_total, 
                    'eur_total'           => $plan_total, // 
                    'sub_total'           => $plan_sub_total, 
                    'dropmatix_sub_total' => $plan_sub_total,
                    'cart'                => $cart_data, // 
                    'discount'            => $plan_discount, 
                    'dropmatix_discount'  => $plan_discount,
                    'discount_type'       => $plan_discount_type, // 
                    'total_tax'           => $plan_total_tax, 
                    'dropmatix_total_tax' => $plan_total_tax, // 
                    'tax_rate'            => $plan_tax_rate, 
                    'dropmatix_tax_rate'  => $plan_tax_rate,
                    'tax_version'         => $plan_tax_version,
                ]);
            }
        }

        $redirect = false;

        $misc_params = [
            'purchased_or_assigned' => $purchased_or_assigned,
            'generate_invoice'      => $generate_invoice,
            'send_invoice'          => $send_invoice,
        ];

        $response = \App\Helper\OrderHelper::defaultStatusAction($deal->offer, $action_status, [], $deal, $misc_params);

        if ($response['success']) {
            $this->insertHistory($deal_id, $response['message']);

            if ($action_status == 'offer_accepted') { // won
                if ($deal->trashed()) {
                    $deal->restore();
                }

                if ($purchased_or_assigned == 'purchased') {
                    $this->insertHistory($deal_id, 'User purchased import plan!');
                }

                if ($generate_invoice) {
                    $this->insertHistory($deal_id, '<p>The offer deal won Invoice created Successfully.</p>');
                }
            }
            else if($action_status == 'offer_rejected') { // lost
                $deal->forceDelete();
                DealHistory::where('deal_id', $deal_id)->delete();

                $redirect = true;
            }
        } else {
            return response()->json([
                'success' => false,
                'message' => $response['message'] ?? 'Something went wrong',
            ], 400);
        }

        return response()->json([
            'success' => true,
            'message' => __('Deal status updated successfully!'),
            'redirect' => $redirect,
        ]);
    }

    public function updateLostOpenStatus($deal_id, $deal) {
        if ($deal->trashed()) {
            $deal->restore();
            $this->insertHistory($deal_id, "Deal status updated from Lost to Open.");
        } else {
            $deal->delete(); // soft delete
            $this->insertHistory($deal_id, "Deal status updated to Lost.");
        }
    }


    //Remove tags on status change
    public function removeOfferDealTags($offer_id)
    {
        $deal_id = OfferDeal::withTrashed()->where('offer_id', $offer_id)->value('id');
        if (empty($deal_id)) return;
        $this->removeDealTags($deal_id);
    }

    //Remove deal tags
    private function removeDealTags($deal_id)
    {
        if (empty($deal_id)) return;

        DB::table('dropfunnel_customer_tags')->where('insert_type', 27)->where('group_id', $deal_id)->delete();
        OfferDeal::withTrashed()->where('id', $deal_id)->update(['tags' => null]);
    }


    public function getAllDeals()
    {
        redirectToV2('/orders/pipeline');

        $user_id = CRUDBooster::myParentId();
        $deals = OfferDeal::where('user_id', $user_id)->get()->groupBy('stage_id');
        $data['stages'] = $data['stage_body'] = $this->stages();

        foreach ($deals as $key => $deal) {
            $data['stage_body'][$key] = $deal;
        }

        $html = view("admin.sales_pipeline.canvas", $data)->render();

        return response()->json([
            'success' => true,
            'html' => $html,
        ]);
    }


    public function getCustomerInfo($email)
    {
        $user_id = CRUDBooster::myParentId();
        $customer = NewCustomer::where([
            "user_id" => $user_id,
            "email" => $email
        ])
            ->select('id', 'user_id', 'full_name', 'company_name', 'phone', 'email')
            ->first();

        if ($customer) {
            $customer->remainder = CustomerPipelineRemainder::where('customer_id', $customer->id)->exists() ? 'yes' : 'no';
            return Response()->json([
                'success' => true,
                'data' => $customer
            ]);
        }
        return Response()->json([
            'success' => false,
            'data' => []
        ]);
    }

    public function productSeach()
    {
        $request = $_REQUEST;
        $result = [];
        if (isset($request['value'])) {
            try {
                $products = [];


                $search_val = $request['value'];
                $channel = intval($request['channel']);
                $price_column = $request['price_column'];

                //Price column
                if($price_column && $price_column === 'vk')
                {
                    $price_column = 'vk_price';
                }


                $lang = $request['lang'] ?? 'de';
                $user_id = CRUDBooster::myParentId();
                $country_id = $this->getCountryIdByLang($lang);

                $title_col = 'title->' . $lang;
                $description_col = 'description->' . $lang;

                $search_val = trim($search_val);


                $result = DrmProduct::with('connected_products:drm_product_id,vk_price,uvp,channel')
                    ->whereNull('deleted_at')
                    ->where('user_id', $user_id)
                    ->where('country_id', $country_id)
                    ->where('ean', 'LIKE', '%' . $search_val . '%')
                    ->orWhere($title_col, 'LIKE', '%' . $search_val . '%');

                if($channel) {
                    $result->whereHas('connected_products', function($r) use ($channel) {
                        $r->where('channel', '=', $channel);
                    });
                }

                $result = $result->select($title_col . ' as product_title', 'id', 'ean', 'vk_price', $description_col . ' as product_description', 'ean', 'id')
                    ->limit(25)
                    ->get()
                    ->map(function ($product) use ($lang, $channel, $price_column) {

                        $price_column = empty($price_column)? 'vk_price' : $price_column;
                        $rate = $channel? $product->connected_products->firstWhere('channel', $channel)->{$price_column} : $product->vk_avg;

                        return [
                            'text' => $product->product_title,
                            'id' => $lang . '_' . $product->id,
                            'description' => Str::words(preg_replace('/\s+/', ' ', trim(strip_tags($product->product_description))), 10, ' ...'),
                            'rate' => $product->vk_price,
                            'ean' => $product->ean,
                        ];
                    });

                //Channel rate filter
                if($channel) {
                    $result = $result->filter(function($product) {
                        return $product['rate'] > 0;
                    });
                }

            } catch (Exception $e) {
                return response()->json([]);
            }
        }
        return response()->json($result);
    }
    public function getCountryIdByLang($lang){
        return Country::where(['is_active' => 1, 'status' => 1])->where('language_shortcode', $lang)->value('id');
    }



    //Mail send action
    public function addMailSendLog($orderId, $message)
    {
        $deal = OfferDeal::where('offer_id', $orderId)->first();
        if(empty($deal) || empty($deal->id)) return;

        $stage = $deal->stage_id;
        if($stage < 4)
        {
            $deal->update([
                'stage_id' => 4,
            ]);
            $this->insertTag($deal->id, 4);
            $this->GenerateMessagAndInsertHistory($deal->id, 4, $stage);
        }

        $this->insertHistory($deal->id, $message);
    }

    public function addRemainderMailSendLog($orderId, $message)
    {
        $deal = OfferDeal::where('offer_id', $orderId)->first();
        if(empty($deal) || empty($deal->id)) return;

        $stage = $deal->stage_id;
        if($stage < 5)
        {
            $deal->update([
                'stage_id' => 5,
            ]);
            $this->insertTag($deal->id, 5);
            $this->GenerateMessagAndInsertHistory($deal->id, 5, $stage);
        }

        $this->insertHistory($deal->id, $message);
    }

    // Max available stage
    private function maxAvailableStage($user_id)
    {
        $offerMail = DB::table('drm_offer_mail')->where('cms_user_id', $user_id)->where('auto_mail', 1)->exists();
        $offer_remainderMail = DB::table('drm_offer_remainder_mail')->where('cms_user_id', $user_id)->where('auto_mail', 1)->exists();

        if($offerMail && $offer_remainderMail) return 6;

        if($offerMail) return 4;

        return 3;
    }

    public function registrationTimeDealCreation($user = null) {
        $user_tags   = UserTags::where('user_id', $user->id)->pluck('tag')->toArray();
        $is_dt_user  = in_array('dt_user', $user_tags) ? true : false;

        $customer_id = NewCustomer::where([
                'email'      => $user->email,
                'user_id'    => $is_dt_user ? 2439 : 2455,
                // 'cc_user_id' => $user->id, // customer may created by appointment edit type where customer isn't system user
            ])
            ->value('id');

        // check user already has calendar/contact deal. if not than create, else ignore
        $deal_exists = OfferDeal::join('new_orders', 'offer_deal.offer_id', '=', 'new_orders.id')
            ->where([
                'offer_deal.drm_customer_id' => $customer_id,
            ])
            ->whereIn('source', ['calendar', 'contact', 'zapier'])
            ->exists();

        if (!$deal_exists) { // if already not created deal via appointment type edit 
            $account_type = $is_dt_user ? 'DT' : 'DRM';
            $reg_price    = $is_dt_user ? 39 : 179; // price

            $dealPayload  = [
                'source'       => 'registration',
                'product_name' => $account_type . ' ' . __('Registration'),
                'deal_title'   => $user->name . ' - ' . __("Starter") . ' - ' . $reg_price . ' EUR',
                'deal_price'   => $reg_price, 
                'stage_id'     => 1,
                'email_addr'   => $user->email,
                'contact_name' => $user->name,
                'user_id'      => $is_dt_user ? 2439 : 2455,
            ];

            $this->autoTypeDealCreation($dealPayload);
        } else { 
            // update cc_user_id=sys_user_id to new_customers table, cms_client=sys_user_id to new_orders table
        }
    }

    // cms user free appointment time: add appointment id to the deal, and stage may change
    public function addAppointmentToDeal($new_appointment_id = 0) {
        $user        = CRUDBooster::me();
        $customer_id = NewCustomer::where([
                'email'      => $user->email,
                'user_id'    => is_dt_user() ? 2439 : 2455,
                'cc_user_id' => $user->id,
            ])
            ->value('id');

        $deal = OfferDeal::join('new_orders', 'offer_deal.offer_id', '=', 'new_orders.id')
            ->where([
                'source'          => 'registration',
                'drm_customer_id' => $customer_id,
                ['stage_id', '<=', 3],
            ])
            ->whereNull('appointment_id')
            ->select('offer_deal.id', 'offer_deal.user_id', 'stage_id')
            ->first();
        
        if (!empty($deal)) {
            $updatableDealData['appointment_id'] = $new_appointment_id;
            $deal->update($updatableDealData);

            // purchase time stage may be already updated to 3
            if ($deal->stage_id < 3) {
                $dealStagePayload = [
                    'deal_id'          => $deal->id,
                    'stage_id'         => 3, // Meeting Scheduled
                    'old_stage_id'     => $deal->stage_id,
                    'deal_user_id'     => $deal->user_id,
                    'free_appointment' => 1,
                    'log_message'      => 'as user takes free appointment.',
                ];

                $this->updateDealStage($dealStagePayload);
            }
        }
    }

    public function purchaseTimeDealUpdate($user_id = null, $row = [], $admin_assigned = false, $is_dt_user = null, $plan_id = null) {
        $user       = User::where('id', $user_id)->first();
        $is_dt_user = !is_null($is_dt_user) ? $is_dt_user : is_dt_user();

        $deal = OfferDeal::
            join('new_orders', 'offer_deal.offer_id', '=', 'new_orders.id')
            ->join('new_customers', 'new_orders.drm_customer_id', '=', 'new_customers.id')
            ->where([
                'offer_deal.source'  => 'registration',
                'new_orders.status'  => 'offer_created',
                'email'              => $user->email,
                'offer_deal.user_id' => $is_dt_user ? 2439 : 2455,
                'cc_user_id'         => $user->id,
            ])
            ->select('offer_deal.id', 'offer_deal.user_id', 'stage_id', 'offer_id')
            ->first();

        if (!empty($deal)) {
            // already purchased order may exist. test case: update plan 
            $deal_order = NewOrder::where([
                    'cms_user_id'  => $deal->user_id,
                    // 'cms_client'   => $user->id,
                    'order_id_api' => 'doff' . $deal->offer_id . 'indrmo',
                ])
                ->exists();

            if (!$deal_order) { // if not purchased order
                // stage may already scheduled through free appointment
                if ($deal->stage_id < 3) { 
                    $dealStagePayload = [
                        'deal_id'       => $deal->id,
                        'stage_id'      => 3, // Meeting Scheduled
                        'old_stage_id'  => $deal->stage_id,
                        'deal_user_id'  => $deal->user_id,
                        'purchased_or_assigned' => $admin_assigned ? 'assigned' : 'purchased',
                        'log_message'   => $admin_assigned ? 'as admin assigned import plan.' : 'as user purchased import plan.',
                    ];
                    $this->updateDealStage($dealStagePayload);
                }
        
                // update deal status
                $payload = [
                    'deal_id' 		        => $deal->id,
                    'status'  		        => 'offer_accepted',
                    'row'                   => $row,
                    'is_dt_user'            => $is_dt_user,
                    'admin_assigned'        => $admin_assigned,
                    'generate_invoice'      => false,
                    'send_invoice'          => false,
                    'purchased_or_assigned' => $admin_assigned ? 'assigned' : 'purchased',
                    'plan_id' => $plan_id,
                ];
                $this->updateDealStatus($payload); // purchase time or admin tariff assign time
            }
        }
    }

    // auto deal creation
    public function autoTypeDealCreation($payload = []) {
        $email_addr   = $payload['email_addr'] ?? '';
        $contact_name = $payload['contact_name'] ?? '';
        $customer_id  = $payload['customer_id'] ?? null;
        $user_id      = $payload['user_id'] ?? null;
        $appointment_id = $payload['appointment_id'] ?? null;
        $contact_history_id = $payload['contact_history_id'] ?? null;
        $pipeline_version = $payload['pipeline_version'] ?? 1;

        $product_name = $payload['product_name'];
        $deal_title   = $payload['deal_title'];
        $deal_price   = $payload['deal_price'];

        $deal_source  = $payload['source'];
        $stage_id     = $payload['stage_id'] ?? 1;
        $expect_date  = Carbon::now()->format('Y-m-d');

        $deal_data = [
            "Email"           => $email_addr,
            "customer_id"     => $customer_id,
            "user_id"         => $user_id,
            "contact_name"    => $contact_name,
            "organization"    => "",
            "title"           => $deal_title,
            "stage_id"        => $stage_id, 
            "expect_date"     => $expect_date,
            "offer_remainder" => "",
            "phone"           => "",
            "end"             => "Home",
            "deal_amount"     => $deal_price,
            "product_name"    => [ $product_name ],
            "rate"            => [ $deal_price ],
            "qty"             => [ 1 ],
            "tax"             => [ 0 ],
            "amount"          => [ $deal_price ],
            "source"          => $deal_source,
            "appointment_id"  => $appointment_id,
            "contact_history_id" => $contact_history_id,
            "pipeline_version" => $pipeline_version,
        ];
       
        $this->saveDeal($deal_data);
    }

    public function bulkUpdateStatus(Request $req)
    {
        $selected_status  = $req->selectedStatus;
        $deal_ids         = $req->selectedItems;
        $generate_invoice = $req->generate_invoice;
        $send_invoice     = $req->send_invoice;

        if (!empty($deal_ids) && in_array($selected_status, ['offer_accepted', 'offer_lost', 'offer_rejected'])) {
            foreach ($deal_ids as $index => $deal_id) {

                if ($selected_status == 'offer_lost') {
                    $deal = OfferDeal::withTrashed()->find($deal_id);
                    $this->updateLostOpenStatus($deal_id, $deal);
                } 
                else {
                    if ($generate_invoice && ($selected_status == 'offer_accepted')) {
                        $generate_invoice = $send_invoice = $this->dealCustomerHasAccount($deal_id) ? true : false;
                    } 

                    $payload = [
                        'deal_id'          => $deal_id,
                        'status'           => $selected_status,
                        'generate_invoice' => $generate_invoice,
                        'send_invoice'     => $send_invoice,
                    ];
                    $this->updateDealStatus($payload); // bulk update time
                }
            }
        }

        return response()->json([
            'success' => true,
            'message' => __('Deals updated successfully!')
        ]);
    }

    // after meeting date passed, next day move appointment deals to 4th column(Offer Submitted) 
    public function updateStageAfterMeetingPassed() {
        $appointment_deals = OfferDeal::join('appointments', 'offer_deal.appointment_id', '=', 'appointments.id')
            ->join('new_orders', 'offer_deal.offer_id', '=', 'new_orders.id')
            ->whereIn('offer_deal.source', ['registration'])
            ->whereNotNull('appointment_id')
            ->where([
                'stage_id'           => 3,
                'new_orders.status'  => 'offer_created',
            ])
            ->select('offer_deal.id', 'offer_deal.user_id', 'stage_id', 'appointment_id', 'appointment')
            ->get();

        if ($appointment_deals->isNotEmpty()) {
            $today_date = Carbon::now()->toDateString();

            foreach ($appointment_deals as $index => $deal_data) {
                if (!empty($deal_data->appointment)) {
                    $appointment_data = json_decode($deal_data->appointment, true)[0];

                    if ($today_date > $appointment_data['date']) {
                        $dealStagePayload = [
                            'deal_id'             => $deal_data->id,
                            'stage_id'            => 4, // Offer Submitted
                            'old_stage_id'        => $deal_data->stage_id,
                            'deal_user_id'        => $deal_data->user_id,
                            'meeting_date_passed' => 1,
                            'log_message'         => 'as Meeting date passed.',
                        ];
                        $this->updateDealStage($dealStagePayload);
                    }
                }
            }
        }
    }

    public function notRenewedUsersDealUpdate() {
        $expired_drm_users = DB::table('purchase_import_plans')
            ->where('end_date', '<', Carbon::now()->toDateString())
            ->pluck('cms_user_id')
            ->toArray();

        $expired_dt_users = DB::table('dt_tariff_purchases')
            ->where('end_date', '<', Carbon::now()->toDateTimeString())
            ->pluck('user_id')
            ->toArray();
        
        $expired_users = array_merge($expired_drm_users, $expired_dt_users);
        
        $deals = OfferDeal::withTrashed()
            ->join('new_orders', 'offer_deal.offer_id', '=', 'new_orders.id')
            ->join('new_customers', 'new_orders.drm_customer_id', '=', 'new_customers.id')
            ->join('cms_users', 'new_customers.cc_user_id', '=', 'cms_users.id')
            ->where([
                'offer_deal.source'  => 'registration',
            ])
            ->whereIn('new_customers.cc_user_id', $expired_users)
            ->select('offer_deal.id')
            ->get(); 

        foreach ($deals as $index => $deal) {
            OfferDeal::withTrashed()->where('id', $deal->id)->update(['situation' => 'not_renewed']);
        }
    }

    public function addDealPlan(Request $request): JsonResponse
    {
        try {
            $message = __('Something went wrong! Please try again.');
            $data    = [];

            Validator::make($_REQUEST, [
                'title' => 'required',
                'price' => 'required',
            ])->validate();

            $deal_plan             = new DealPlan();
            $deal_plan->title      = $request->title;
            $deal_plan->price      = $request->price;
            $deal_plan->owner_id   = is_dt_user() ? 2439 : 2455;
            $deal_plan->creator_id = CRUDBooster::myParentId();
            $deal_plan->save();
        
            if (!empty($deal_plan->id)) {
                $message = __('Deal plan added successfully.');
            }

            return response()->json([
                'message' => $message,
                'data'    => $data,
            ], 200);
        } catch(\Exception $ex) {
            return response()->json([
                'success' => false,
                'message' => $ex->getMessage()
            ], 422);
        }
    }

    // check deal customer has cms account. If not, then invoice will not be created
    public function dealCustomerHasAccount($deal_id = null) {
        $customer_has_acc = OfferDeal::withTrashed()
            ->join('new_orders', 'offer_deal.offer_id', '=', 'new_orders.id')
            ->join('new_customers', 'new_orders.drm_customer_id', '=', 'new_customers.id')
            ->join('cms_users', 'new_customers.cc_user_id', '=', 'cms_users.id')
            ->where([
                'offer_deal.id' => $deal_id,
            ])
            ->exists();

        return $customer_has_acc;
    }

    public function getTariffPlans() {
        $plan_ids = !is_dt_user() ? [24, 25, 26, 27] : [28, 29, 30, 31];
        
        $tariff_plans = DB::table('import_plans')
            ->whereIn('id', $plan_ids)
            ->pluck('plan', 'amount')
            ->toArray();

        return $tariff_plans;
    }

    public function getDealPlans() {
        $owner_id = is_dt_user() ? 2439 : 2455;

        $deal_plans = DealPlan::where('owner_id', $owner_id)
            ->select('title', 'price')
            ->get();
        
        return $deal_plans;
    }

    public function saveColumn(): JsonResponse
    {
        $message = '';
        $data = [];

        $request = $_REQUEST;

        Validator::make($request, [
            'title' => 'required|min:2',
        ], [
            'title' => __("Column name is required."),
        ])->validate();

        $column_id = $request['id'] ?? 0;
        $user_id = CRUDBooster::myParentId();
    
        try {
            if ($column_id > 0) {
                DB::table('offer_deal_stages')
                    ->where([
                        'id' => $column_id,
                        'user_id' => $user_id
                    ])
                    ->update([
                        'name' => $request['title'],
                        'updated_at' => now(),
                    ]);

                $data['saved'] = true;
                $message  = __("Column name updated successfully!");
            } else {
                $position = DB::table('offer_deal_stages')
                    ->where([
                        'user_id' => $user_id
                    ])
                    ->orderBy('sort', 'desc')
                    ->value('sort');

                $column_id = DB::table('offer_deal_stages')
                    ->insertGetId([
                        'user_id' => $user_id,
                        'name' => $request['title'],
                        'sort' => $position + 1,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);

                $data['saved'] = true;
                $message  = __("Column created successfully!");
            }

        } catch (\Exception $ex) {
            $message  = __("Save failed! Please try again.");
        }

        return response()->json([
            'success' => true,
            'data'    => $data,
            'message' => $message,
        ], 200);
    }

    public function deleteColumn($id): JsonResponse
    {
        $message = '';
        $data = [];
    
        try {
            if ($id > 0) {
                DB::table('offer_deal_stages')
                    ->where([
                        'id' => $id,
                        'user_id' => CRUDBooster::myParentId(),
                    ])
                    ->delete();

                $data['deleted'] = true;
                $message = __("Column deleted successfully!");
            }
        } catch (\Exception $ex) {
            $message = __("Delete process failed! Please try again.");
        }

        return response()->json([
            'success' => true,
            'data'    => $data,
            'message' => $message,
        ], 200);
    }

    public function updateColumnPosition(): JsonResponse
    {
        $message = '';
        $data = [];
    
        try {
            $column_positions = $_REQUEST['column_positions'];

            if (!empty($column_positions)) {
                foreach ($column_positions as $index => $column) {
                    DB::table('offer_deal_stages')
                        ->where([
                            'id' => $column['id'],
                            'user_id' => CRUDBooster::myParentId(),
                        ])
                        ->update([
                            'sort' => $column['position']
                        ]);

                    $data['updated'] = true;
                    $message = __("Section position updated successfully!");
                }
            }
        } catch (\Exception $ex) {
            $message = __("Failed! Please try again.");
        }

        return response()->json([
            'success' => true,
            'data'    => $data,
            'message' => $message,
        ], 200);
    }

}
