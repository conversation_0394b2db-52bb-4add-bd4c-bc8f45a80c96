<?php namespace App\Http\Controllers;

	use Session;
	use Illuminate\Http\Request;
	use DB;
	use CRUDBooster;
	use App\AppStore;
	use App\Comment;
	use App\User;
	use App\NewOrder;
	use Illuminate\Support\Facades\Validator;
	use Illuminate\Support\Facades\Log;
    use App\Notifications\DRMNotification;

// use App\Mail\AppPurchaseConfirmation;
// use App\Mail\AppTrialConfirmation;

use App\Mail\DRMSEndMail;


use Illuminate\Support\Facades\Mail;
use ServiceKey;
use Illuminate\Support\Facades\Http;
use GuzzleHttp\Client;
use App\AgbLogs;
use App\Jobs\TrendImport;
use App\Models\AppStore\PurchasedApp;
use App\Option;
use App\Models\AppStore\AssignedApp;
use App\Services\Payment\Service\AppStore as Apps;
use App\Services\DRM\DRMService;

class AdminPurchaseAppsController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function cbInit()
    {

        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = false;
        $this->button_edit = false;
        $this->button_delete = true;
        $this->button_detail = true;
        $this->button_show = true;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "purchase_apps";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];
        $this->col[] = ["label" => "App Name", "name" => "app_id", "join" => "app_stores,menu_name"];
        $this->col[] = ["label" => "Type", "name" => "app_id", "join" => "app_stores,type"];
        $this->col[] = ["label" => "User Name", "name" => "cms_user_id", "join" => "cms_users,name"];
        $this->col[] = ["label" => "Plan Type", "name" => "type"];
        $this->col[] = ["label" => "Price", "name" => "price"];
        $this->col[] = ["label" => "Status", "name" => "status"];
        $this->col[] = ["label" => "Subscription Date Start", "name" => "subscription_date_start"];
        $this->col[] = ["label" => "Subscription Date End", "name" => "subscription_date_end"];
        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE

        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM

        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key 	  = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color 	   = Default is primary. (primary, warning, succecss, info)
        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon 	   = Icon from fontawesome
        | @name 	   = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code before index table
        | ----------------------------------------------------------------------
        | html code to display it before index table
        | $this->pre_index_html = "<p>test</p>";
        |
        */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {

        // if($column_index == 6){
        //    if($column_value == 1){
        //    	 $column_value ='<p">Active</p>';
        //     }else{
        //         $column_value ='<p>Un Active</p>';
        //     }
        // }

        // if($column_index == 8){
        // 		if($column_value >= date('Y-m-d')){
        //    	     $column_value ='<p">Active</p>';
        //     }else{
        //         $column_value ='<p>Un Active</p>';
        //     }
        //    }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */
    public function hook_before_add(&$postdata)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }


    //By the way, you can still create your own method in here... :)

    public function getAdd()
    {
        //Create an Auth
        if (!CRUDBooster::isCreate() && $this->global_privilege == FALSE || $this->button_add == FALSE) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $data = [];
        $data['page_title'] = 'Create App Store';

        //Please use cbView method instead view method from laravel
        $this->cbView('app_store.create_app_store', $data);
    }


    public function appList(Request $request, $id = null)
    {
        // $this->CheckAppPurchase('DRM-Projekt');
        $user = User::find(CRUDBooster::myParentId());

        if (!CRUDBooster::myParentId()) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $assigns = DB::table('app_assigns')->where('user_id', $user->id)
        ->where(function($amnu){
            $amnu->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
        })
        ->pluck('app_id')->toArray();
        $subs = DB::table('purchase_apps')
            ->where('cms_user_id', $user->id)
            // ->where('type', '!=', 'Free Trail')
            ->where(function ($q) {
                $q->where('subscription_date_end', '>', \Carbon\Carbon::today())
                    ->orwhere('subscription_life_time', 1);
            })->select('app_id', 'type', 'subscription_date_end')
            ->get()->toArray();

            $trials = collect($subs)->filter(function($trial){
                return ($trial->type == 'Free Trail');
            })->map(function($item){

                $remaining = \Carbon\Carbon::now()->diffInSeconds($item->subscription_date_end, false);
                $remaining = $remaining > 0 ? $remaining : 0;

                //Trial
                $trial_data = [
                    'app_id' => $item->app_id,
                    'remaining' => $remaining
                ];

                //Remaining
                if($remaining) return $trial_data;

            })->pluck('remaining', 'app_id')->toArray();

            $using = collect($subs)->filter(function($trial){
                return ($trial->type != 'Free Trail');
            })->pluck('app_id')->merge($assigns)->unique()->toArray();


            //->pluck('app_id')->toArray();
        $query = AppStore::with('categories');

         // Leftjoin('app_categories', 'app_categories.id', '=', 'app_stores.app_category_id')
         //    ->select('app_stores.*');

        if(!CRUDBooster::isSuperadmin()){
            $query->where('draft', 0);
        }


        if(checkTariffEligibility(CRUDBooster::myParentId()))
        {
            $intervalApps = [config('global.interval_app_id'), config('global.csv_interval_app_id')];
            $intervalApps = array_filter($intervalApps);

            $query->whereNotIn('id', $intervalApps);
        }


        if (!empty($using)) {
            $query->whereNotIn('app_stores.id', $using);
        }

        if (!empty($request->search)) {

            $search = $request->search;

            $query->where(function($q) use ($search) {
                $q->where('app_stores.menu_name', 'LIKE', '%' . $search . '%');

                $searchValues = preg_split('/\s+/', $search, -1, PREG_SPLIT_NO_EMPTY);
                foreach ($searchValues as $value) {
                    $q->orWhere('app_stores.menu_name', 'LIKE', '%' . $value . '%');
                    if (is_numeric($value)) {
                        $q->orWhereBetween('app_stores.fixed_price', [(floor($value)), (float)(floor($value) + 0.9999)]);
                    }
                }
            });
        }

        $flat_rate_deny = !CRUDBooster::hasMarketplaceAccess();
        if( $flat_rate_deny )
        {
            $query->whereDoesntHave('categories', function($q) {
                $q->where('app_categories.id', 13);
            });
        }


        if ($id != null) {
            // $query->where('app_stores.app_category_id', $id);
            $query->whereHas('categories', function($q) use ($id) {
                $q->where('app_categories.id', $id);
            });
        }



        if (!empty($request->created_at)) {
            $query->where('app_stores.created_at', '<=', $request->created_at . ' 00:00:00');
        }
        if (!empty($request->filter)) {
            $search_str = $request->filter;

            if ($search_str == 'price_max') {
                $query->orderby('app_stores.fixed_price', 'desc');
            } elseif ($search_str == 'price_min') {
                $query->orderby('app_stores.fixed_price', 'asc');
            } elseif ($search_str == 'desc') {
                $query->orderby('app_stores.created_at', 'desc');
            } else {
                $query->orderby('app_stores.created_at', 'asc');
            }
        }
        $results = $query->orderBy('feature_product', 'asc')->paginate(30);
        $categories = DB::table('app_categories')->get();
        $premium_flat_rate_status = DB::table('premium_flat_rate_users')->where('user_id', $user->id)->first();

        $assignid_total_app = count($assigns);
        $app_store_total_app = AppStore::where('type', 'app')->select('id')->count();
        $flat_rate_banner_show = true;

        if($assignid_total_app == $app_store_total_app){
            $flat_rate_banner_show = false;
        }

        return view('app_store.new_app_store', compact('results', 'categories', 'user', 'premium_flat_rate_status', 'trials', 'flat_rate_banner_show'));
    }
    public function appListTest(Request $request, $id = null)
    {
        // $this->CheckAppPurchase('DRM-Projekt');
        $user = User::find(CRUDBooster::myParentId());

        if (!CRUDBooster::myParentId()) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }
        $subs = DB::table('purchase_apps')
            ->where('cms_user_id', CRUDBooster::myParentId())
            ->where('type', '!=', 'Free Trail')
            ->where(function ($q) {
                $q->where('subscription_date_end', '>', date('Y-m-d'))
                    ->orwhere('subscription_life_time', 1);
            })
            ->pluck('app_id')->toArray();
        $query = AppStore::Leftjoin('app_categories', 'app_categories.id', '=', 'app_stores.app_category_id')
            ->select('app_stores.*');
        if (!empty($subs)) {
            $query->whereNotIn('app_stores.id', $subs);
        }

        if (!empty($request->search)) {
            $query->where('app_stores.menu_name', 'LIKE', '%' . $request->search . '%');
            $searchValues = preg_split('/\s+/', $request->search, -1, PREG_SPLIT_NO_EMPTY);
            foreach ($searchValues as $value) {

                $query->orWhere('app_stores.menu_name', 'LIKE', '%' . $value . '%')
                    ->orWhere('app_stores.description', 'LIKE', '%' . $value . '%');
                if (is_numeric($value)) {
                    $query->orWhereBetween('app_stores.fixed_price', [(floor($value)), (float)(floor($value) + 0.9999)]);
                }
            }
        }

        if ($id != null) {
            $query->where('app_stores.app_category_id', $id);
        }

        if (!empty($request->created_at)) {
            $query->where('app_stores.created_at', '<=', $request->created_at . ' 00:00:00');
        }
        if (!empty($request->filter)) {
            $search_str = $request->filter;

            if ($search_str == 'price_max') {
                $query->orderby('app_stores.fixed_price', 'desc');
            } elseif ($search_str == 'price_min') {
                $query->orderby('app_stores.fixed_price', 'asc');
            } elseif ($search_str == 'desc') {
                $query->orderby('app_stores.created_at', 'desc');
            } else {
                $query->orderby('app_stores.created_at', 'asc');
            }
        }
        $results = $query->orderBy('feature_product', 'asc')->paginate(15);
        $categories = DB::table('app_categories')->get();
        $premium_flat_rate_status = DB::table('premium_flat_rate_users')->where('user_id', CRUDBooster::myParentId())->first();

        return view('app_store.app_list', compact('results', 'categories', 'user', 'premium_flat_rate_status'));
    }
    public function appstore()
    {
        return view('app_store.app_store_tem');
    }
    public function Purchased()
    {

        if (!CRUDBooster::myParentId()) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

        $user = User::find(CRUDBooster::myParentId());

        $results = DB::table('purchase_apps')
            ->join('app_stores', 'app_stores.id', '=', 'purchase_apps.app_id')
            ->where('purchase_apps.type', '!=', 'Free Trail')
            ->where('purchase_apps.cms_user_id', CRUDBooster::myParentId())
            ->where(function ($query) {
                $query->where('purchase_apps.subscription_date_end', '>', date('Y-m-d'))
                    ->orwhere('purchase_apps.subscription_life_time', 1);
            })
            ->select('app_stores.menu_name', 'app_stores.icon', 'purchase_apps.*')
            ->paginate(15);
        $categories = DB::table('app_categories')->get();
        return view('app_store.purchase_app_list', compact('results', 'categories', 'user'));
    }

    public function appView()
    {

        $id = $_REQUEST['id'];
        $result = AppStore::select('app_stores.*')
            ->where('app_stores.id', $id)
            ->first();

        $plans = DB::table('app_store_plans as asp')
            ->join('subscription_plans as sp', 'sp.id', '=', 'asp.subscription_plans_id')
            ->where('asp.app_stores_id', $id)
            ->select('sp.*')
            ->get();
        $download = '';
        if (isset($_REQUEST['app_id'])) {
            $download = 'yes';
        }
        $avg_rating = round(DB::table('ratings')->where('cms_modul_id', $id)->where('rating', '>', 0)->groupBy('cms_modul_id')->avg('rating'), 1);
        $single_user_rating = DB::table('ratings')->where(['cms_modul_id' => $id, 'cms_user_id' => \CRUDBooster::myParentId()])->select('rating')->first();
        $user_rating = $single_user_rating->rating ?? 0;

        $user_tested = DB::table('purchase_apps')->where(['app_id' => $id, 'cms_user_id' => \CRUDBooster::myParentId()])->select('id')->first();
        $user_tested_assign = DB::table('app_assigns')->where(['app_id' => $id, 'user_id' => \CRUDBooster::myParentId()])->select('id')->first();
        $project_app = ($id == 13) ? \App\Helper\AppStore::ShowDrmProjectMenu($id) : false;
        $is_tested = ($user_tested || $user_tested_assign || $project_app) ? true : false;

        return view('app_store.view', compact('result', 'plans', 'download', 'avg_rating', 'user_rating', 'is_tested'));

    }


    public function appForm()
    {
        $id = $_REQUEST['id'];

        if(checkTariffEligibility(CRUDBooster::myParentId()) && in_array($id, [config('global.interval_app_id'), config('global.csv_interval_app_id')]))
        {
            abort(404);
        }

        $result = AppStore::Leftjoin('purchase_apps', 'purchase_apps.app_id', '=', 'app_stores.id')
            ->select('app_stores.*', 'purchase_apps.is_free_trail as is_free_trail')
            ->where('app_stores.id', $id)
            ->first();

        $is_trial = DB::table('purchase_apps')->where([
            'app_id' => $id,
            'cms_user_id' => CRUDBooster::myParentId(),
            'is_free_trail' => 1
        ])->first();

        $plans = DB::table('app_store_plans as asp')
            ->join('subscription_plans as sp', 'sp.id', '=', 'asp.subscription_plans_id')
            ->where('asp.app_stores_id', $id)
            ->orderBy('sp.price', 'asc')
            ->select('sp.*')
            ->get();
        $user = User::find(CRUDBooster::myParentId());

        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }


        // if ( app()->environment('development') ) { //stripeTest
            $is_upadat_form = false;
        
            $owner_user_id = ($id === 59) ? User::DROPTIENDA_ACCOUNT_ID : User::DROPMATIX_ACCOUNT_ID;

            $hasPaypal = app(DRMService::class)->paypalCheck($owner_user_id);
            
            return view('app_store.form_v1', compact('result', 'plans', 'user', 'is_trial', 'term', 'is_upadat_form', 'hasPaypal'));
        // } else {
        //     return view('app_store.form', compact('result', 'plans', 'user', 'is_trial', 'term'));
        // }


    }
    public function topUpForm()
    {

        $plans = [
            5 => 50,
            10 =>100,
            25 => 250,
            50 => 500,
            75 => 750,
            100 => 1000,
            150 => 1500,
            300 => 3000,
            500 => 5000,
        ];
        $user = User::find(CRUDBooster::myParentId());

        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }
        $is_upadat_form = false;

        $hasPaypal = app(DRMService::class)->paypalCheck(User::DROPMATIX_ACCOUNT_ID);
        return response()->json([
            'success' => true,
            'html' => view('admin.tariff.partials.topup-modal', compact('plans', 'user', 'term'))->render(),
        ]);
    }

    public function premiumFlatRateForm(){

        $user = User::find(CRUDBooster::myParentId());

        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }

        $is_upadat_form = false;
        //dd($user, $term, $is_upadat_form);
        return view('app_store.premium_flat_rate_modal', compact('user', 'term', 'is_upadat_form'));
    }

    public function updateForm()
    {
        $id = $_REQUEST['id'];

        if(checkTariffEligibility(CRUDBooster::myParentId()) && in_array($id, [config('global.interval_app_id'), config('global.csv_interval_app_id')]))
        {
            abort(404);
        }

        $result = AppStore::join('purchase_apps', 'purchase_apps.app_id', '=', 'app_stores.id')
            ->select('app_stores.*', 'purchase_apps.id as  purchase_id', 'purchase_apps.plan_id')
            ->where('app_stores.id', $id)
            ->where('purchase_apps.cms_user_id', CRUDBooster::myParentId())
            ->first();

        $plans = DB::table('app_store_plans as asp')
            ->join('subscription_plans as sp', 'sp.id', '=', 'asp.subscription_plans_id')
            ->where('asp.app_stores_id', $id)
            ->select('sp.*')
            ->orderBy('sp.price', 'asc')
            ->get();
        $user = User::find(CRUDBooster::myParentId());

        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }


        // if ( app()->environment('development') ) { //stripeTest
            $is_upadat_form = true;

            $owner_user_id = ($id === 59) ? User::DROPTIENDA_ACCOUNT_ID : User::DROPMATIX_ACCOUNT_ID;

            $hasPaypal = app(DRMService::class)->paypalCheck($owner_user_id);

            return view('app_store.form_v1', compact('result', 'plans', 'user', 'is_trial', 'term', 'is_upadat_form', 'hasPaypal'));
        // } else {
        //     return view('app_store.update_plan', compact('result', 'plans', 'user', 'term'));
        // }


    }

    public function getComment()
    {
        return Comment::where('cms_modul_id', $_REQUEST['app_id'])
            ->with('user', 'replycomment', 'replycomment.user')
            ->orderby('comments.id', 'desc')
            ->get();

    }


    public function commentStore()
    {

        // return request();
        DB::table('comments')
            ->insert([
                'comment' => request()->comment,
                'cms_modul_id' => request()->cms_modul_id,
                'cms_user_id' => CRUDBooster::myParentId(),
                'status' => 0
            ]);

        return response(['msg', 'Message Inserted']);

    }

    public function postRatingView()
    {
        try {
            $id = request()->cms_modul_id;
            $user_rate = DB::table('ratings')->where(['cms_modul_id' => $id, 'cms_user_id' => CRUDBooster::myParentId()])->select('rating')->first();
            $user_rating = ($user_rate) ? $user_rate->rating : 0;

            $rating = round(DB::table('ratings')->where('cms_modul_id', $id)->where('rating', '>', 0)->groupBy('cms_modul_id')->avg('rating'), 1);
            return response()->json([
                'success' => true,
                'user_rating' => $user_rating,
                'avg' => $rating,
                'html' => view('app_store.rating_view', compact('user_rating', 'id'))->render()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid action!',
            ]);
        }
    }

    public function ratingStore()
    {
        try {
            DB::table('ratings')
                ->updateOrInsert([
                    'cms_modul_id' => request()->cms_modul_id,
                    'cms_user_id' => CRUDBooster::myParentId()
                ], [
                    'rating' => request()->rating,
                    'status' => 0
                ]);
            $rating = round(DB::table('ratings')->where('cms_modul_id', request()->cms_modul_id)->where('rating', '>', 0)->groupBy('cms_modul_id')->avg('rating'), 1);
            return response()->json([
                'success' => true,
                'message' => 'Rating success!',
                'avg' => $rating
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Rating failed!',
            ]);
        }
    }


    public function reply_comment_modal()
    {

        $result = Comment::where('id', $_REQUEST['id'])
            ->first();
        return view('app_store.reply_modal', compact('result'));
    }

    public function replyCommentStore()
    {

        DB::table('reply_comments')
            ->insert([
                'reply_comment' => request()->reply_comment,
                'comment_id' => request()->comment_id,
                'cms_user_id' => CRUDBooster::myParentId()
            ]);

        return response(['msg', 'Message Inserted']);

    }

    // public function check_app_purchase()
    // {
    //     $results = DB::table('purchase_apps')
    //         ->select('purchase_apps.*')
    //         ->where('purchase_apps.cms_user_id', CRUDBooster::myParentId())
    //         ->where(function ($query) {
    //             $query->where('purchase_apps.subscription_date_end', '>=', date('Y-m-d'))
    //                 ->orwhere('purchase_apps.subscription_life_time', 1);
    //         })->get();

    //     if (count($results) > 0) {
    //         return view('app_store.check_app_purchase_modal', compact('results'));
    //     } else {
    //         DB::table('cms_users')->where('id', CRUDBooster::myParentId())->update(['status' => null]);

    //         $me = CRUDBooster::me();
    //         CRUDBooster::insertLog(trans("crudbooster.log_logout", ['email' => $me->email]), '', 'logout');

    //         Session::flush();
    //         setcookie('user_id', session('admin_id'), time() - 1000);
    //         setcookie('user_name', session('admin_name'), time() - 1000);

    //         return response(['message' => 'logout']);
    //     }

    // }

    public function check_app_purchase()
    {
        
        $results = DB::table('purchase_apps')
            ->join('app_stores','app_stores.id','purchase_apps.app_id')
            ->select('purchase_apps.*','app_stores.app_name')
            ->where('purchase_apps.cms_user_id', CRUDBooster::myParentId())
            ->where(function ($query) {
                $query->where('purchase_apps.subscription_date_end', '>=', date('Y-m-d'))
                    ->orwhere('purchase_apps.subscription_life_time', 1);
            })->get();
            

        if (count($results) > 0) {
            return view('app_store.check_app_purchase_modal', compact('results'));
        } else {
            return view('app_store.confirm_final_delete_modal');
        }

    }

    public function confirm_delete_modal(){
        return view('app_store.confirm_final_delete_modal');
    }


    //check invoice before delete
    public function checkDuePaymentInvoice(){

        $user_id = CRUDBooster::myParentId();

         $unpaidOrders = DB::table('new_orders')
            ->whereNull('deleted_at')
            ->whereIn('status', ['inkasso','mahnung','nicht_bezahlt'])
            ->where('cms_client', $user_id)
            ->whereNotNull('marketplace_order_ref')
            ->where('marketplace_paid_status', '<>', 1)
            ->where('cms_user_id', 2455)
            ->select('id', 'eur_total', 'insert_type', 'order_history', 'marketplace_order_ref', 'invoice_number')
            ->orderBy('id', 'ASC')
            ->get()
            ->map(function($item) {
				$history = @json_decode($item->order_history, true) ?? [];
				$time = @collect($history)->reverse()->firstWhere('status', 'inkasso')['time'];

            	return [
            		'id' => $item->id,
            		'description' => $description,
            		'time' => $time,
            		'type' => $item->insert_type,
            		'eur_total' => $item->eur_total,
            		'marketplace_order_ref' => $item->marketplace_order_ref,
            		'invoice_number' => $item->invoice_number,
            	];
            })
            ->toArray();

        //$unpaidOrders = DB::table('new_orders')->where('cms_user_id',CRUDBooster::myParentId())->where('marketplace_paid_status',0)->get();
        if(count($unpaidOrders)>0){
            return view('app_store.check_due_purchase_invoice',compact('unpaidOrders'));
        }else{
            return response(['message' => 'app_check']);
        }
       

    }

    public function account_close()
    {
        app('App\Services\DeactiveUser\UserRemove')->remove(CRUDBooster::myId());
        DB::table('cms_users')->where('id', CRUDBooster::myId())->update(['status' => null]);
        $me = CRUDBooster::me();
        CRUDBooster::insertLog(trans("crudbooster.log_logout", ['email' => $me->email]), '', 'logout');
        $logoutURL = drm_login_url($me);

        //Delete login info to drop campus
        // $user_mac = getMacAddress();
        // $user_ip = getIpAddress();
        // resolve(\App\Services\DropCampus\DropmatixCampus::class)->deleteDrmLoginDetail($me->email, $user_mac, $user_ip);
        //Detele login info to drop campus END

        Session::flush();
        setcookie('user_id', session('admin_id'), time() - 1000);
        setcookie('user_name', session('admin_name'), time() - 1000);

        // Logout
        authUserLogout();

        return response()->json([
            'success'=>true,
            'message'=> 'Logged Out Successfully'
        ]);

        // return redirect($logoutURL)->with('message', trans("crudbooster.message_after_logout"));
    }

    public function appBuy(Request $request)
    {
        $productName = DB::table('app_stores')->where('id', $request->app)->select('menu_name')->first();
        $freescout = false;
        $user = User::with('billing_detail')->find(CRUDBooster::myParentId());
        if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

        \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
        $token = $_POST['stripeToken']; // Token ID

        //DB::beginTransaction();
        try {

            $app = DB::table('app_stores')->find(request()->app);
            if ($app->id == config('global.inbox_app_id')) {
                $this->installInbox();
                $freescout = true;
            }

            //Coupon
            $coupon = null;
            if (isset($_POST['coupon_id']) && $_POST['coupon_id']) {
                $coupon = \Stripe\Coupon::retrieve($_POST['coupon_id'], []);
            }

            $discount = 0;
            $total = $request->fixed_price;
            $sub_price = $total;
            $coupon_valid = false;

            if ($coupon) {
                if ($coupon->valid) {
                    $coupon_valid = true;
                    $discount = ($coupon->amount_off) ? ($coupon->amount_off / 100) : (($sub_price * $coupon->percent_off) / 100);
                    $discount = ($discount > $sub_price) ? $sub_price : $discount;
                    $total = $total - $discount;
                } else {
                    throw new \Exception("Invalid Coupon code. Please try again!");
                }
            }


            $charge = \Stripe\Charge::create([
                'amount' => $total * 100,
                'currency' => 'eur',
                'description' => $_POST['app_name'] . ' Purchase from Apps Store',
                'source' => $token,
            ]);

            if ($charge['status'] == "succeeded") {  // if success
                DB::table('purchase_apps')->insert([
                    'price' => $total,
                    'app_id' => request()->app,
                    'type' => 'fixed price',
                    'stripe_subscription_id' => $charge['id'],
                    'cms_user_id' => CRUDBooster::myParentId(),
                    'status' => 'active',
                    'subscription_life_time' => 1
                ]);

                //Update single pay coupon
                if ($coupon_valid) {
                    DB::table('coupons')->where('coupon_id', $coupon->id)->increment('single_pay');
                }

                //sendmail
                // $postdata=[
                // 	'app_name'=>$_POST['app_name'],
                // 	'price'=>$_POST['fixed_price'],
                // 	'subject'=>'Order confirmation of your DRM extension',
                // 	'freescout' => $freescout
                // ];
                // app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new AppPurchaseConfirmation($postdata));
                $tags = [
                    'app_name' => $_POST['app_name'],
                    'app_price' => $total,
                    'subscription_interval' => 0,
                    'period_start' => 0,
                    'period_end' => 0,

                    'INTERVAL' => false,
                    'FIXED' => true,
                    'FREESCOUT' => $freescout,
                ];
                $slug = 'app_purchase_confirmation';
                $lang = getUserSavedLang($user->billing_detail->email);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));

                // jahidulhasanzahid
                // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myParentId())
                // ->join('countries','countries.id','=','billing_details.country_id')
                // ->first();


                // $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";

                $taxShow = config('global.tax_for_invoice');
                $price = $request->fixed_price . '00';
                $total_tax = ($total * $taxShow) / 100;
                $order_info = [
                    'user_id' => 98,
                    'cms_client' => CRUDBooster::myParentId(),
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => round(($total), 2),
                    'sub_total' => round($sub_price, 2),
                    'discount' => round($discount, 2),
                    'discount_type' => 'fixed',
                    'total_tax' => 0,
                    'payment_type' => "Stripe Card",
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => 3,
                    'shop_id' => 8,
                    'order_id_api' => $charge['id'],
                ];


                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', $productName->menu_name);
                $cart_item['description'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'App Store Purchase Complete. App Name is "' . $productName->menu_name . '".Purchase Date ' . date('Y-m-d H:i:s'));
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($price, 2);
                $cart_item['tax'] = $taxShow;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($price, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);
                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info);

                // jahidulhasanzahid

            }
            //DB::commit();    // Commiting  ==> There is no problem whatsoever
            return response(['status' => true, 'message' => 'App purchase success!']);
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong

            return response(['status' => false, 'message' => $e->getMessage()]);
        }

    }


    public function testpayment(Request $request)
    {

        $productName = DB::table('app_stores')->where('id', $request->app)->select('menu_name')->first();

        $freescout = false;
        $user = User::with('billing_detail')->find(CRUDBooster::myParentId());
        if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

        \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
        $database_plan = DB::table('subscription_plans')->find($_POST['plan_id']);

        // Add customer to stripe
        $customers = \Stripe\Customer::all(['email' => $user->email]);
        $cust = $customers->jsonSerialize();

        //DB::beginTransaction();
        try {

            $app = DB::table('app_stores')->find(request()->app);
            if ($app->id == config('global.inbox_app_id')) {
                $this->installInbox();
                $freescout = true;
            }

            if (!empty($cust['data'])) {
                $custId = $cust['data'][0]['id'];
            } else {

                $customer = \Stripe\Customer::create(array(
                    'email' => $user->email,
                    'source' => $_POST['stripeToken'],
                ));
                $custId = $customer->id;
            }
            // Convert price to cents
            $priceCents = ($database_plan->price * 100);

            // Create a plan
            $plans = \Stripe\Plan::all(["amount" => $priceCents,
                "currency" => 'eur',
                "interval" => $database_plan->interval]);
            $plan = $plans->jsonSerialize();
            if (!empty($plan['data'])) {
                $planId = $plan['data'][0]['id'];
            } else {
                $plan = \Stripe\Plan::create(array(
                    "product" => [
                        "name" => $database_plan->name
                    ],
                    "amount" => $priceCents,
                    "currency" => 'eur',
                    "interval" => $database_plan->interval,
                    "interval_count" => 1
                ));
                $planId = $plan->id;
            }


            $subsctiption_data = [
                'customer' => $custId,
                'items' => [['plan' => $planId]]
            ];

            //Coupon
            $coupon = null;
            if (isset($_POST['coupon_id']) && $_POST['coupon_id']) {
                $coupon = \Stripe\Coupon::retrieve($_POST['coupon_id'], []);
            }

            $discount = 0;
            $total = $database_plan->price;
            $sub_price = $total;

            if ($coupon) {
                if ($coupon->valid) {
                    $subsctiption_data['coupon'] = $coupon->id;
                    $discount = ($coupon->amount_off) ? ($coupon->amount_off / 100) : (($sub_price * $coupon->percent_off) / 100);
                    $discount = ($discount > $sub_price) ? $sub_price : $discount;
                    $total = $total - $discount;
                } else {
                    throw new \Exception("Invalid Coupon code. Please try again!");
                }
            }

            $subscription = \Stripe\Subscription::create($subsctiption_data);

            // if($plan){
            //     // Creates a new subscription
            //        $subscription = \Stripe\Subscription::create(array(
            //            "customer" => $custId,
            //            "items" => array(
            //                array(
            //                    "plan" => $planId,
            //                ),
            //            ),
            //        ));
            // }

            $subsData = $subscription->jsonSerialize();
            $subscrID = $subsData['id'];
            $custID = $subsData['customer'];
            $planID = $subsData['plan']['id'];
            $planAmount = ($subsData['plan']['amount'] / 100);
            $planCurrency = $subsData['plan']['currency'];
            $planinterval = $subsData['plan']['interval'];
            $planIntervalCount = $subsData['plan']['interval_count'];
            $created = date("Y-m-d H:i:s", $subsData['created']);
            $current_period_start = date("Y-m-d", $subsData['current_period_start']);
            $current_period_end = date("Y-m-d", $subsData['current_period_end']);
            $status = $subsData['status'];

            DB::table('purchase_apps')->updateOrInsert([
                'app_id' => request()->app, 'cms_user_id' => CRUDBooster::myParentId()],
                [
                    'price' => $total,
                    'app_id' => request()->app,
                    'plan_id' => request()->plan_id,
                    'stripe_plan_id' => $planID,
                    'stripe_customer_id' => $custID,
                    'payer_email' => $user->email,
                    'type' => $planinterval,
                    'stripe_subscription_id' => $subscrID,
                    'status' => $status,
                    'subscription_date_start' => $current_period_start,
                    'subscription_date_end' => $current_period_end
                ]);

            //Send order
            $taxShow = config('global.tax_for_invoice');
            $price = $database_plan->price;
            $order_info = [
                'user_id' => 98,
                'cms_client' => CRUDBooster::myParentId(),
                'order_date' => date('Y-m-d H:i:s'),
                'total' => round(($total), 2),
                'sub_total' => round($sub_price, 2),
                'discount' => round($discount, 2),
                'discount_type' => 'fixed',
                'total_tax' => 0,
                'payment_type' => "Stripe Card",
                'status' => "paid",
                'currency' => "EUR",
                'adjustment' => 0,
                'insert_type' => 3,
                'shop_id' => 8,
                'order_id_api' => $subscrID,
            ];

            $carts = [];
            $cart_item = [];
            $cart_item['id'] = 1;
            $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', $app->menu_name);
            $cart_item['description'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'App Store Purchase Complete. App Name is "' . $app->menu_name . '".Purchase Date ' . date('Y-m-d H:i:s'));
            $cart_item['qty'] = 1;
            $cart_item['rate'] = round($price, 2);
            $cart_item['tax'] = $taxShow;
            $cart_item['product_discount'] = 0;
            $cart_item['amount'] = round($price, 2);
            $carts[] = $cart_item;
            $order_info['cart'] = json_encode($carts);
            app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info);

            $tags = [
                'app_name' => $app->menu_name,
                'app_price' => $total,
                'subscription_interval' => 0,
                'period_start' => 0,
                'period_end' => 0,

                'INTERVAL' => false,
                'FIXED' => true,
                'FREESCOUT' => $freescout,
            ];
            $slug = 'app_purchase_confirmation';
            $lang = getUserSavedLang($user->billing_detail->email);
            $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
            app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));

            //DB::commit();    // Commiting  ==> There is no problem whatsoever
            return response(['status' => true, 'message' => 'App purchase success!']);
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong

            return response(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    //Mailbox installetion on sca purchase
    public function installInboxSCA($user_id = null)
    {
        return true;
        try {
            $user_id = $user_id ?? CRUDBooster::myParentId();
            $cmsuserInfo = DB::table('cms_users')->where('id', $user_id)->first();
            $database_name = str_replace(' ', '_', $cmsuserInfo->name);
            $db_name_lower = strtolower($database_name);
            $db_name = preg_replace("/[^A-Za-z0-9\-\']/", '', $db_name_lower);
            $apiRequest['id'] = $cmsuserInfo->id;
            $apiRequest['db_host'] = '************';
            $apiRequest['db_name'] = $db_name;
            $apiRequest['db_user'] = 'forge';
            $apiRequest['db_password'] = 'EVlesfB2hRbs5VE3657S';
            $apiRequest['db_port'] = '3306';
            $apiRequest['name'] = $cmsuserInfo->name;
            $apiRequest['email'] = $cmsuserInfo->email;
            $apiRequest['password'] = $cmsuserInfo->password;

            $url = "https://drm.network/api/install-freescout";
            $client = new Client();

            $response = $client->post($url, ['form_params' => $apiRequest]);
            json_decode($response->getBody()->getContents(), TRUE);
            return true;

        } catch (\Exception $e) {
            return false;
        }
    }

    public function installInbox($user_id = null)
    {
        try {
            $user_id = $user_id ?? CRUDBooster::myParentId();
            $cmsuserInfo = DB::table('cms_users')->where('id', $user_id)->first();
            $database_name = str_replace(' ', '_', $cmsuserInfo->name);
            $db_name_lower = strtolower($database_name);
            $db_name = preg_replace("/[^A-Za-z0-9\-\']/", '', $db_name_lower);
            $apiRequest['id'] = $cmsuserInfo->id;
            $apiRequest['db_host'] = '************';
            $apiRequest['db_name'] = $db_name;
            $apiRequest['db_user'] = 'forge';
            $apiRequest['db_password'] = 'EVlesfB2hRbs5VE3657S';
            $apiRequest['db_port'] = '3306';
            $apiRequest['name'] = $cmsuserInfo->name;
            $apiRequest['email'] = $cmsuserInfo->email;
            $apiRequest['password'] = $cmsuserInfo->password;

            $url = "https://drm.network/api/install-freescout";
            $client = new Client();

            $response = $client->post($url, ['form_params' => $apiRequest]);
            return json_decode($response->getBody()->getContents(), TRUE);

        } catch (\Exception $e) {
            CRUDBooster::redirect(CRUDBooster::adminPath(), 'Inbox install failed!');
        }
    }

    public function freeTrial()
    {
        $freescout = false;
        //DB::beginTransaction();
        try {
            $user = User::find(CRUDBooster::myParentId());

            if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

            $app = DB::table('app_stores')->find(request()->app);
            if ($app->id == config('global.inbox_app_id')) {
                $this->installInbox();
                $freescout = true;
            }

            DB::table('purchase_apps')->updateOrInsert([
                'app_id' => request()->app, 'cms_user_id' => CRUDBooster::myParentId()
            ],
            [
                'price' => 0.00,
                // 'app_id'=>request()->app,
                'type' => 'Free Trail',
                // 'cms_user_id'=>CRUDBooster::myParentId(),
                'status' => 'active',
                'is_free_trail' => 1,
                'subscription_date_start' => date('Y-m-d'),
                'subscription_date_end' => $this->subscription('free')
            ]);

            // Agb Logfiles
            // if(isLocal()){
                $AgbLog = new AgbLogs;
                $AgbLog->user_id = CRUDBooster::myParentId();

                $user_info = DB::table('cms_users')->where('id',CRUDBooster::myParentId())->first();

                $AgbLog->message = $user_info->name.' App Trial';

                $app_info = [];
                $app_info['agb'] = config('agb.app_buy');
                $app_info['app_info'] =[ 'app_id' => request()->app, 'app_name' => request()->app_name, 'app_type' => 'Free Trial for 14 Days' ];

                $AgbLog->agb = json_encode($app_info);
                $AgbLog->ip_address = getIpAddress();
                // dd($AgbLog->agb);

                $AgbLog->save();
            // }
            // End Agb Logfiles

            // jahidulhasanzahid
            // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myParentId())
            // ->join('countries','countries.id','=','billing_details.country_id')
            // ->first();
            // $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";
            $taxShow = config('global.tax_for_invoice');
            $price = 0.00;
            $total_tax = ($price * $taxShow) / 100;
            $order_info = [
                'user_id' => 2455,  //STRIPE_CLIENT
                'cms_client' => CRUDBooster::myParentId(),
                'order_date' => date('Y-m-d H:i:s'),
                'total' => round(($price), 2),
                'sub_total' => round($price - $total_tax, 2),
                'total_tax' => round($total_tax, 2),
                'payment_type' => "Free Trial",
                'status' => "paid",
                'currency' => "EUR",
                'adjustment' => 0,
                'insert_type' => 3,
                'shop_id' => 8,
                'order_id_api' => "trial_app_".$app->id."_".CRUDBooster::myParentId().'_'.date('YmdHis'),
            ];


            $carts = [];
            $cart_item = [];
            $cart_item['id'] = 1;
            $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', request()->app_name);
            $cart_item['description'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'App Store Free Trial Complete. App Name is "' . request()->app_name . '".Purchase Date ' . date('Y-m-d H:i:s') . ' Trails Interval ' . date('Y-m-d') . ' to ' . $this->subscription('free'));
            $cart_item['qty'] = 1;
            $cart_item['rate'] = round($price, 2);
            $cart_item['tax'] = $taxShow;
            $cart_item['product_discount'] = 0;
            $cart_item['amount'] = round($price, 2);
            $carts[] = $cart_item;
            $order_info['cart'] = json_encode($carts);

            // app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info); // trial inv not needeed
            // jahidulhasanzahid


            $tags = [
                'app_name' => request()->app_name,
                'app_price' => 0,
                'subscription_interval' => 0,
                'period_start' => 0,
                'period_end' => 0,

                'INTERVAL' => false,
                'FIXED' => false,
                'FREESCOUT' => $freescout,
            ];
            $slug = 'app_purchase_confirmation';
            $lang = getUserSavedLang($user->billing_detail->email);
            $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
            app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));


            //  		$postdata=[
            // 	'app_name'=>request()->app_name,
            // 	'subject'=>'Order Trial confirmation of '.request()->app_name,
            // 	'freescout' => $freescout
            // ];
            // app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new AppTrialConfirmation($postdata));


            //DB::commit();

            //purchase done...

            $isTrendApp = DB::table('app_store_categories')->where([
                'app_id' => $app->id,
                'category_id' => 13
            ])->first();
            if($isTrendApp){
                $plans = config('global.trend_importer_app_plans');
                $trial_plan_id = 42;
                $purchased = PurchasedApp::with('trend_categories:keepa_cat_id,drm_app_id')
                            ->where('app_id', $app->id)->first();
                $package_limit = $plans[$trial_plan_id];
                $keepa_cat_id = $purchased->trend_categories['keepa_cat_id'];

                TrendImport::dispatch(CRUDBooster::myParentId(), $keepa_cat_id, $package_limit);
            }


            if($app->id == config('global.automagic_product_update')){
                #reset app uses
                Option::updateOrCreate(
                    [
                        'option_key' => 'automagic_product_uses',
                        'option_group' => 'automagic_product_update',
                        'user_id' => CRUDBooster::myId(),
                    ],
                    [
                        'option_key' => 'automagic_product_uses',
                        'option_group' => 'automagic_product_update',
                        'user_id' => CRUDBooster::myId(),
                        'option_value' => 0,
                    ]
                );

                #add uses limit
                Option::updateOrCreate(
                    [
                        'option_key' => 'automagic_product_limit',
                        'option_group' => 'automagic_product_update',
                        'user_id' => CRUDBooster::myId(),
                    ],
                    [
                        'option_key' => 'automagic_product_limit',
                        'option_group' => 'automagic_product_update',
                        'user_id' => CRUDBooster::myId(),
                        'option_value' => 15,
                    ]
                );
            }

            if (in_array($app->id, [42, 45])) {
                updateUserAppsDate($user->id, $app->id, date('Y-m-d'), $this->subscription('free'));
            }

            return response()->json([
                'success' => true,
                'message' => request()->app_name . '  App Get For Free Trail !'
            ]);
        } catch (\Exception $e) {
            //DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //Active flat rate app
    public function getActiveAllApps(){

        try{
            $user_id = CRUDBooster::myParentId();
            $apps = DB::table('app_stores')->where('status', 1)
            ->where('app_category_id', '<>', 12)
            ->whereNull('fixed_price')->get();

            $success_count = $failed_count = 0;

            //Freescout
            $is_failed_mailbox = false;

            //Carts
            $carts = [];

            //Email Tags
            $tags = [];
            $tags['app_name'] = 'Premium flat rate';
            $tags['app_price'] = 0;
            $tags['subscription_interval'] = '2 years';
            $tags['period_start'] = now()->format('Y-m-d');
            $tags['period_end'] = now()->addMonths(24)->format('Y-m-d');
            $tags['INTERVAL'] = true;
            $tags['FIXED'] = false;
            $tags['FREESCOUT'] = false;

            //Accept term
            $user = User::with('billing_detail')->find($user_id);
            if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

            foreach ($apps as $app) {

                $app_id = $app->id;

                $activation_data = [];
                $activation_data['app_id'] = $app_id;
                $activation_data['type'] = 'fixed';
                $activation_data['id'] = $intend_id = 'ptr_drm_'.$app->id.'_u'.$user_id.'_'.date('Ym');
                $activation_data['user_id'] = $user_id;

                $activation_data['discount'] = 0; //Price
                $activation_data['total'] =  0;
                $activation_data['sub_total'] = 0;

                //Subscription data
                $plan = DB::table('app_store_plans as asp')
                ->join('subscription_plans as sp', 'sp.id', '=', 'asp.subscription_plans_id')
                ->where('asp.app_stores_id', $app_id)
                ->orderBy('sp.price', 'desc')
                ->select('sp.*')
                ->first();

                if($plan){
                    $activation_data['type'] = 'plan';
                    $activation_data['plan_id'] = $plan->id;
                    $activation_data['stripe_plan_id'];
                    $activation_data['stripe_customer_id'];
                    $activation_data['interval_type'] = $plan->interval;
                    $activation_data['subscription_id'];
                    $activation_data['status'] = 'Active';
                    $activation_data['period_start'] = now();
                    $activation_data['period_end'] = now()->addMonths(24);
                }

                $return_data = app('App\Http\Controllers\AdminPurchaseAppsController')->flatRateAppActive($activation_data);

                if(is_array($return_data) && count($return_data)){
                    if($return_data['success']){

                        //add to cart
                        $carts[] = $return_data['cart'];

                        //Install mailbox
                        if ($app_id == config('global.inbox_app_id')) {
                            if($this->installInboxSCA($user_id)){
                                $is_failed_mailbox = false;
                            }else{
                                $is_failed_mailbox = true;
                            }

                            $tags['FREESCOUT'] = true;
                        }

                        $success_count++;
                    }else{
                        $failed_count++;
                    }
                }
            }
            //Loop end

            $success_msg = ($success_count)? $success_count.' app active' : '';
            $failed_msg = ($failed_count)? $failed_count.' app activation failed' : '';


            //Update user flat rates
            if($success_count > 0){
                DB::table('premium_flat_rate_users')->updateOrInsert([
                    'user_id' => CRUDBooster::myParentId()],
                    ['subscription_date_start' => now(),
                    'subscription_date_end' => now()->addMonths(24),
                    'flat_rate_status' => 'true',
                    ]
                );
            }

            //Step 2 - Create invoice
            $order_info = [
                'user_id' => 2455,
                'cms_client' => $user->id,
                'order_date' => date('Y-m-d H:i:s'),
                'total' => round(0, 2),
                'sub_total' => round(0, 2),
                'discount' => round(0, 2),
                'discount_type' => 'fixed',
                'total_tax' => 0,
                'payment_type' => "Stripe Card",
                'status' => "paid",
                'currency' => "EUR",
                'adjustment' => 0,
                'insert_type' => 3,
                'shop_id' => 8,
                'order_id_api' => $intend_id,
            ];
            $order_info['cart'] = json_encode($carts);

            //create invoice
            app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $user->id);

            try{
                // //Step 3 - Send Email to customer
                $slug = 'app_purchase_confirmation';
                $lang = getUserSavedLang($user->billing_detail->email);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));
            }catch(\Exception $ee){}


            $mail_box_message = ($is_failed_mailbox)? ' But Mailbox installation failed, please contact DRM support.' : null;

            return response()->json([
                'success' => true,
                'message' => $success_msg.$mail_box_message.' '.$failed_msg
            ]);

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    //Flat rate Active app
    public function flatRateAppActive($purchase_data)
    {
        //DB::beginTransaction();
        try {

            $purchase_type = $purchase_data['type'];
            $app = DB::table('app_stores')->find($purchase_data['app_id']);
            if( is_null($app) || !in_array($purchase_type, ['plan', 'fixed']) ) throw new \Exception('Invalid action!');

            $intend_id = $purchase_data['id'];
            $user = User::with('billing_detail')->find($purchase_data['user_id']);


            //Step 1 - Active service
            $purchare_db = [];
            $purchare_db['price'] = $total;



            //Cancel old subscription
            $old = DB::table('purchase_apps')->where(['cms_user_id' => $user->id, 'app_id' => $app->id])->whereNotNull('stripe_subscription_id')->first();
            //Cancel old subscription
            if($old !=null){
                resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2455', '', $old->stripe_subscription_id);
            }

            if( $purchase_type  == 'plan' ){
                $purchare_db['plan_id']                 = $purchase_data['plan_id'];
                $purchare_db['stripe_plan_id']          = $purchase_data['stripe_plan_id'];
                $purchare_db['stripe_customer_id']      = $purchase_data['stripe_customer_id'];
                $purchare_db['payer_email']             = $user->email;
                $purchare_db['type']                    = $purchase_data['interval_type'];
                $purchare_db['stripe_subscription_id']  = $purchase_data['subscription_id'];
                $purchare_db['status']                  = 'active';
                $purchare_db['subscription_date_start'] = $purchase_data['period_start'];
                $purchare_db['subscription_date_end']   = $purchase_data['period_end'];
            }else{
                $purchare_db['type'] = 'fixed price';
                $purchare_db['stripe_subscription_id'] = $intend_id;
                $purchare_db['status'] = 'active';
                $purchare_db['subscription_life_time'] = 1;
            }

            //Insert app purchase data
            $filter_purchase_data = array_filter($purchare_db);
            if($filter_purchase_data){
                DB::table('purchase_apps')->updateOrInsert([
                    'app_id' => $app->id, 'cms_user_id' => $user->id],
                    $filter_purchase_data
                );
            }

            // Agb Logfiles
            try{
                $AgbLog = new AgbLogs;
                $AgbLog->user_id = $purchase_data['user_id'];
                $AgbLog->message = $user->name.' App Purchsed';

                $app_info = [];
                $app_info['agb'] = config('agb.premium_flat_rate');

                $app_details['app_id'] = $purchase_data['app_id'];
                $app_details['app_name'] = $app->menu_name;
                if($purchase_data['type'] == 'plan'){
                    $app_details['app_plan_id'] = $purchase_data['plan_id'];
                }else if($purchase_data['type'] == 'fixed'){
                    $app_details['app_type'] = "Fixed";
                }
                $app_info['app_info'] = $app_details;

                $AgbLog->agb = json_encode($app_info);
                $AgbLog->ip_address = getIpAddress();
                $AgbLog->save();
            }catch(\Exception $exc){}

            // End Agb Logfiles

            //Step 2 - Create cart item
            $taxShow = config('global.tax_for_invoice');
            $cart_item = [];
            $cart_item['id'] = $app->id;
            $cart_item['product_name'] = $app->menu_name;
            $cart_item['description'] = 'App Store Purchase. App Name is "' . $app->menu_name . '".Purchase Date ' . date('Y-m-d H:i:s');
            $cart_item['qty'] = 1;
            $cart_item['rate'] = round(0, 2);
            $cart_item['tax'] = $taxShow;
            $cart_item['product_discount'] = 0;
            $cart_item['amount'] = round(0, 2);

            if (in_array($app->id, [42, 45])) {
                updateUserAppsDate($user->id, $app->id, $purchase_data['period_start'], $purchase_data['period_end']);
            }

            //DB::commit();
            return [
                'success' => true,
                'cart' => $cart_item
            ];
        } catch (\Exception $e) {
            //DB::rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }


    //cancel calcellation
    public function postCancelCancellationTariff(){
        try{
            if(DB::table('premium_flat_rate_users')->where('user_id', CRUDBooster::myParentId())->update(['flat_rate_status' => 'true'])){
                return response()->json([
                    'success' => true,
                    'message' => 'Cancel successfully!'
                ]);
            }else{
                throw new \Exception('cancel failed!');
            }
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Cancel failed!'
            ]);
        }
    }


    /*===============================================================
    ====================== Topup stripe amount for dropmatix ========
    ================================================================*/
    public function topupAmountSCA($purchase_data)
    {
        try {

            $user_id = $purchase_data['user_id'];
            $intend_id = $purchase_data['intend_id'];

            if(DB::table('topups')->where('intend_id', $intend_id)->exists()) throw new \Exception('Already purchased!');
            if(NewOrder::where(['order_id_api' => $intend_id, 'cms_user_id' => $user_id, 'shop_id' => 8])->exists()) throw new \Exception('Already purchased!'); //STRIPE_CLIENT


            $user = User::with('billing_detail')->find($user_id);
            if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);
            $is_tax_free = $user->billing_detail->country_id == 83;

            $total_tax = $purchase_data['total_tax'];
            $vat_number = $purchase_data['vat_number'] ?? null;
            $has_vat_number = !empty($vat_number) || $is_tax_free;
            $tax_rate = $has_vat_number ? 0 : 21;
            $tax_version = 1;

            DB::table('topups')->insert([
                'intend_id' => $intend_id,
                'total' => $purchase_data['total'],
                'total_tax' => $purchase_data['total_tax'],
                'sub_total' => $purchase_data['sub_total'],
                'user_id' => $user_id,
                'plan_id' => $purchase_data['plan_id'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            $current_balance = DB::table('drm_tariff_balance')
            ->where('user_id', $user_id)->value('balance') ?? 0;

            DB::table('drm_tariff_balance')->updateOrInsert([
                'user_id' => $user_id,
            ],
            [
                'balance' => $current_balance + $purchase_data['sub_total']
            ]);

            $discount = $purchase_data['discount']?? 0;
            $total = $purchase_data['total']?? 0;
            $sub_total = $purchase_data['sub_total']?? 0;

            //Step 2 - Create invoice
            $order_info = [
                'user_id' => 2455,  //STRIPE_CLIENT
                'cms_client' => $user->id,
                'order_date' => date('Y-m-d H:i:s'),
                'total' => round(($total), 2),
                'sub_total' => round($sub_total, 2),
                'discount' => round(0, 2),
                'discount_type' => 'fixed',
                'total_tax' => $total_tax,
                'tax_rate' => $tax_rate,
                'payment_type' => "Stripe Card",
                'status' => "paid",
                'currency' => "EUR",
                'adjustment' => 0,
                'insert_type' => \App\Enums\InsertType::APP,
                'shop_id' => 8,
                'order_id_api' => $intend_id,
                'intend_id' => $intend_id,
                'vat_number' => $vat_number,
                'tax_version' => $tax_version,
            ];

            $carts = [];
            $cart_item = [];
            $cart_item['id'] = 1;
            $cart_item['product_name'] = 'Topup balance';
            $cart_item['description'] = 'Purchase Date ' . date('Y-m-d H:i:s');
            $cart_item['qty'] = 1;
            $cart_item['rate'] = round($sub_total, 2);
            $cart_item['tax'] = $tax_rate;
            $cart_item['product_discount'] = 0;
            $cart_item['amount'] = round($sub_total, 2);
            $carts[] = $cart_item;
            $order_info['cart'] = json_encode($carts);
            app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $user->id);

            try{
                $tags = [
                    'app_name' => 'Topup balance',
                    'app_price' => $total,
                    'subscription_interval' => 0,
                    'period_start' => 0,
                    'period_end' => 0,

                    'INTERVAL' => false,
                    'FIXED' => true,
                    'FREESCOUT' => false,
                ];
                $slug = 'app_purchase_confirmation';
                $lang = getUserSavedLang($user->billing_detail->email);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                $emails = json_decode($user->billing_detail->billing_emails ,true) ?? [ $user->billing_detail->email ];
                foreach ($emails as $key => $email){
                    app('drm.mailer')->getMailer()->to($email)->send(new DRMSEndMail($mail_data));
                }
            }catch (\Exception $exception){

            }

            return ['success' => true, 'message' => 'Topup success!'];
        }catch(\Exception $e)  {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }



    /*===============================================================
    ====================== Stripe Purchase App SCA payment===========
    ================================================================*/

    //Purchase apps SCA
    public function appBuySCA($purchase_data)
    {
        //DB::beginTransaction();
        try {
            $user_id = $purchase_data['user_id'];
            $app_id = $purchase_data['app_id'];
            $plan_id = $purchase_data['plan_id'];

            $payment_intend_id = $purchase_data['intend_id'] ?? null;


            $purchase_type = $purchase_data['type'];
            $app = DB::table('app_stores')->find($purchase_data['app_id']);
            $cms_user_id = 2455;
            $stripe_key = 'stripe_key_2455';
            if(config('global.automatic_feed_app_id') == $app->id){
                $cms_user_id = 2439;
                $stripe_key = 'stripe_key_2439';
            }
            if( is_null($app) || !in_array($purchase_type, ['plan', 'fixed']) ) throw new \Exception('Invalid action!');

            $intend_id = $purchase_data['id'];
            if(NewOrder::where(['order_id_api' => $intend_id, 'cms_user_id' => $cms_user_id, 'shop_id' => 8])->exists()) throw new \Exception('Already purchased!'); //STRIPE_CLIENT

            $freescout = false;
            $is_failed_mailbox = false;

            $user = User::with('billing_detail')->find($purchase_data['user_id']);
            if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);
            $is_tax_free = $user->billing_detail->country_id == 83;

            if ($app->id == config('global.inbox_app_id')) {
                if($this->installInboxSCA()){
                    $is_failed_mailbox = false;
                }else{
                    $is_failed_mailbox = true;
                }
                $freescout = true;
            }

            //Increment single pay coupon usages
            if (isset($purchase_data['coupon']) && $purchase_data['coupon']) {
                DB::table('coupons')->where('coupon_id', $purchase_data['coupon'])->increment('single_pay');
            }

            if($app->id == config('global.automagic_product_update')){
                $automagic_product_update_plan = config('global.automagic_product_update_plans');

                #reset app uses
                Option::updateOrCreate(
                    [
                        'option_key' => 'automagic_product_uses',
                        'option_group' => 'automagic_product_update',
                        'user_id' => CRUDBooster::myId(),
                    ],
                    [
                        'option_key' => 'automagic_product_uses',
                        'option_group' => 'automagic_product_update',
                        'user_id' => CRUDBooster::myId(),
                        'option_value' => 0,
                    ]
                );
                #add uses limit
                Option::updateOrCreate(
                    [
                        'option_key' => 'automagic_product_limit',
                        'option_group' => 'automagic_product_update',
                        'user_id' => CRUDBooster::myId(),
                    ],
                    [
                        'option_key' => 'automagic_product_limit',
                        'option_group' => 'automagic_product_update',
                        'user_id' => CRUDBooster::myId(),
                        'option_value' => $automagic_product_update_plan[$plan_id],
                    ]
                );
            }

            $discount = $purchase_data['discount']?? 0;
            $total = $purchase_data['total']?? 0;
            $sub_total = $purchase_data['sub_total']?? 0;

            $tax_rate = 21;
            $total_tax = 0;
            $has_vat_number = false;
            $vat_number = null;
            $tax_version = null;

            if( isset($purchase_data['main_amount']) ) {
                $sub_total = $purchase_data['main_amount'];
                $tax_version = 1;
                $vat_number = $purchase_data['vat_number'] ?? null;
                $has_vat_number = !empty($vat_number) || $is_tax_free;
                $tax_rate = $has_vat_number ? 0 : 21;
                $total_tax = $purchase_data['total_tax'];
            }

            //Step 1 - Active service
            $purchare_db = [];
            $purchare_db['price'] = $total;

            //Email Tags
            $tags = [];
            $tags['app_name'] = $app->menu_name;
            $tags['app_price'] = $total;
            $tags['subscription_interval'] = 0;
            $tags['period_start'] = 0;
            $tags['period_end'] = 0;
            $tags['INTERVAL'] = false;
            $tags['FIXED'] = true;
            $tags['FREESCOUT'] = $freescout;



            $old = DB::table('purchase_apps')->where(['cms_user_id' => $user->id, 'app_id' => $app->id])->whereNotNull('stripe_subscription_id')->first();
            //Cancel old subscription
            if($old !=null){
                resolve(\App\Services\Stripe\Latest\CancelSubscription::class)($stripe_key, '', $old->stripe_subscription_id);
            }


            if( $purchase_type  == 'plan' ){
                $purchare_db['plan_id']                 = $plan_id;
                $purchare_db['stripe_plan_id']          = $purchase_data['stripe_plan_id'];
                $purchare_db['stripe_customer_id']      = $purchase_data['stripe_customer_id'];
                $purchare_db['payer_email']             = $user->email;
                $purchare_db['type']                    = $purchase_data['interval_type'];
                $purchare_db['stripe_subscription_id']  = $purchase_data['subscription_id'];
                $purchare_db['status']                  = 'active';
                $purchare_db['subscription_date_start'] = $purchase_data['period_start'];
                $purchare_db['subscription_date_end']   = $purchase_data['period_end'];

                $tags['subscription_interval'] = $purchase_data['interval_type'];
                $tags['period_start'] = $purchase_data['period_start'];
                $tags['period_end'] = $purchase_data['period_end'];
                $tags['INTERVAL'] = true;

            }else{
                $purchare_db['type'] = 'fixed price';
                $purchare_db['stripe_subscription_id'] = $intend_id;
                $purchare_db['status'] = 'active';
                $purchare_db['subscription_life_time'] = 1;

            }

            //Insert app purchase data
            $filter_purchase_data = array_filter($purchare_db);
            if($filter_purchase_data){
                $filter_purchase_data['is_free_trail'] = null;
                DB::table('purchase_apps')->updateOrInsert([
                    'app_id' => $app->id, 'cms_user_id' => $user->id],
                    $filter_purchase_data
                );
            }

            //purchase done...

            $isTrendApp = DB::table('app_store_categories')->where([
                'app_id' => $app->id,
                'category_id' => 13
            ])->first();
            if($isTrendApp){
                $plans = config('global.trend_importer_app_plans');
                $purchased = PurchasedApp::with('trend_categories:keepa_cat_id,drm_app_id')
                            ->where('app_id', $app_id)->first();
                $package_limit = $plans[$plan_id];
                $keepa_cat_id = $purchased->trend_categories['keepa_cat_id'];

                TrendImport::dispatch($user_id, $keepa_cat_id, $package_limit);
            }

            // Agb Logfiles
            // if(isLocal()){
                // dd($purchase_data);
                $AgbLog = new AgbLogs;

                $AgbLog->user_id = $purchase_data['user_id'];
                $AgbLog->message = $user->name.' App Purchsed';

                $app_info = [];
                $app_info['agb'] = config('agb.app_buy');

                $app_details['app_id'] = $purchase_data['app_id'];
                $app_details['app_name'] = $app->menu_name;
                if($purchase_data['type'] == 'plan'){
                    $app_details['app_plan_id'] = $plan_id;
                }else if($purchase_data['type'] == 'fixed'){
                    $app_details['app_type'] = "Fixed";
                }
                $app_info['app_info'] = $app_details;

                $AgbLog->agb = json_encode($app_info);
                $AgbLog->ip_address = getIpAddress();
                $AgbLog->save();
            // }
            // End Agb Logfiles

            //Step 2 - Create invoice
            $order_info = [
                'user_id' => $cms_user_id,  //STRIPE_CLIENT
                'cms_client' => $user->id,
                'order_date' => date('Y-m-d H:i:s'),
                'total' => round(($total), 2),
                'sub_total' => round($sub_total, 2),
                'discount' => round($discount, 2),
                'discount_type' => 'fixed',
                'total_tax' => $total_tax,
                'tax_rate' => $tax_rate,
                'payment_type' => "Stripe Card",
                'status' => "paid",
                'currency' => "EUR",
                'adjustment' => 0,
                'insert_type' => \App\Enums\InsertType::APP,
                'shop_id' => 8,
                'order_id_api' => $intend_id,
                'intend_id' => $payment_intend_id,
                'vat_number' => $vat_number,
                'tax_version' => $tax_version,
            ];

            $carts = [];
            $cart_item = [];
            $cart_item['id'] = 1;
            $cart_item['product_name'] = $app->menu_name;
            $cart_item['description'] = 'App Store Purchase Complete. App Name is "' . $app->menu_name . '".Purchase Date ' . date('Y-m-d H:i:s');
            $cart_item['qty'] = 1;
            $cart_item['rate'] = round($sub_total, 2);
            $cart_item['tax'] = $tax_rate;
            $cart_item['product_discount'] = 0;
            $cart_item['amount'] = round($sub_total, 2);
            $carts[] = $cart_item;
            $order_info['cart'] = json_encode($carts);
            app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $user->id);


            try{
                // //Step 3 - Send Email to customer
                $slug = 'app_purchase_confirmation';

                $lang = getUserSavedLang($user->billing_detail->email);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);

                // if(isLocal() || in_array($user->id, [212, 2592])){
                    $emails = json_decode($user->billing_detail->billing_emails, true) ?? [ $user->billing_detail->email ];
                    foreach ($emails as $key => $email){
                        app('drm.mailer')->getMailer()->to($email)->send(new DRMSEndMail($mail_data));
                    }
                // }else{
                // app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data));
                // }
            }catch(\Exception $ee){

            }


            $mail_box_message = ($is_failed_mailbox)? ' But Mailbox installation failed, please contact DRM support.' : '';
           //DB::commit();    // Commiting  ==> There is no problem whatsoever

            if (in_array($app->id, [42, 45])) {
                updateUserAppsDate($user->id, $app->id, $purchase_data['period_start'], $purchase_data['period_end']);
            }

            return ['success' => true, 'message' => 'App purchase success!'.$mail_box_message];
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }




    public function stripeFailedPaymentInvoice($data)
    {

        // if (app()->environment('production')) {
        $data = json_decode(request()->getContent(), true);
        Log::channel('uscreen')->info($data);

        if ($data['type'] == 'invoice.payment_succeeded') {

            $sub_id = $data['data'];
            $price = ($data['data']['object']['lines']['data'][0]['amount'] / 100);
            $startDate = date("Y-m-d", $data['data']['object']['lines']['data'][0]['period']['start']);
            $endDate = date("Y-m-d", $data['data']['object']['lines']['data'][0]['period']['end']);

            $inv_id = $data['data']['object']['id'];

            $app = DB::table('purchase_apps')
                ->join('app_stores', 'app_stores.id', '=', 'purchase_apps.app_id')
                ->where('stripe_subscription_id', $sub_id)
                ->select('purchase_apps.type', 'purchase_apps.cms_user_id', 'app_stores.menu_name')
                ->first();

            $import = DB::table('purchase_import_plans')
                ->join('import_plans', 'import_plans.id', '=', 'purchase_import_plans.import_plan_id')
                ->where('purchase_import_plans.stripe_subscription_id', $sub_id)
                ->select('purchase_import_plans.product_amount_import', 'import_plans.*')
                ->first();

            if ($app) {
                DB::table('purchase_apps')->where('stripe_subscription_id', $sub_id)
                    ->update([
                        'subscription_date_start' => $startDate,
                        'subscription_date_end' => $endDate,
                    ]);


                $tags = [
                    'app_name' => $app->menu_name,
                    'app_price' => $price,
                    'subscription_interval' => ucfirst($app->type),
                    'period_start' => $startDate,
                    'period_end' => $endDate,

                    'INTERVAL' => true,
                    'FIXED' => false,
                    'FREESCOUT' => false,
                ];

                // jahidulhasanzahid
                $result = DB::table('purchase_apps')
                    ->join('cms_users', 'purchase_apps.cms_user_id', '=', 'cms_users.id')
                    ->join('billing_details', 'purchase_apps.cms_user_id', '=', 'billing_details.user_id')
                    ->join('app_stores', 'purchase_apps.app_id', '=', 'app_stores.id')
                    ->where('purchase_apps.stripe_subscription_id', '=', $sub_id)
                    ->select('purchase_apps.*', 'billing_details.*', 'cms_users.name', 'app_stores.menu_name')
                    ->first();
                $appPrice = $result->price;
                $endDate = $result->subscription_date_end;
                $startDate = $result->subscription_date_start;
                $menuName = $result->menu_name;
                $companyName = $result->company_name;
                $billingAddress = $result->address;
                $billingZip = $result->zip;
                $billingCity = $result->city;
                $billingPhone = $result->phone;
                $userEmail = $result->email;
                $userName = $result->name;
                $countryID = $result->country_id;
                // $country = DB::table('countries')->where('id','=',$countryID)
                // ->select('name')
                // ->first();
                // $billingCountry = $country->name;
                // $detailsInformationForBilling = "<b>Company Name: $companyName</b></br><p>Address: $billingAddress,$billingCity,$billingZip,$billingCountry</p><p>Contact Information:</p><p>E-mail: $userEmail</p><p>Phone: $billingPhone</p>";
                $price = $appPrice;
                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($price * $taxShow) / 100;
                $order_info = [
                    'user_id' => 98,
                    'cms_client' => $result->cms_user_id,
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => round(($price), 2),
                    'sub_total' => round($price - $total_tax, 2),
                    'total_tax' => round($total_tax, 2),
                    'payment_type' => "Stripe Card",
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => 3,
                    'shop_id' => 8,
                    'order_id_api' => $sub_id,
                ];


                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', $menuName);
                $cart_item['description'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'App Purchase Complete from App Store. App Name is "' . $menuName . '".Subscription Start from ' . $startDate . ' to ' . $endDate . '.Payment by Stripe.');
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($price, 2);
                $cart_item['tax'] = $taxShow;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($price, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);

                // $order_exist = NewOrder::where(['order_id_api' => $sub_id, 'cms_client' => $result->cms_user_id, 'shop_id' => 8, 'cms_user_id' => 98])->whereYear('created_at', '=', date('Y'))->whereMonth('created_at', '=', date('m'))->select('id')->first();
                // if ($order_exist && $order_exist->id) return;

                if(NewOrder::where('order_id_api', $inv_id)->exists()) return 'App subscription Inv already Exist';

                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $result->cms_user_id);
                // jahidulhasanzahid

                $slug = 'app_purchase_confirmation';
                $lang = getUserSavedLang($userEmail);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                app('drm.mailer')->getMailer()->to($userEmail)->send(new DRMSEndMail($mail_data));

            } else if ($import) {
                DB::table('purchase_import_plans')->where('stripe_subscription_id', $sub_id)->update([
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'product_amount_import' => $import->product_amount
                ]);
                //$import->product_amount_import +

                $tags = [
                    'app_name' => 'Import Plan ' . $import->plan,
                    'app_price' => $price,
                    'subscription_interval' => ucfirst($import->interval),
                    'period_start' => $startDate,
                    'period_end' => $endDate,

                    'INTERVAL' => true,
                    'FIXED' => false,
                    'FREESCOUT' => false,
                ];

                // jahidulhasanzahid
                $importResult = DB::table('purchase_import_plans')
                    ->join('cms_users', 'purchase_import_plans.cms_user_id', '=', 'cms_users.id')
                    ->join('billing_details', 'purchase_import_plans.cms_user_id', '=', 'billing_details.user_id')
                    ->join('import_plans', 'purchase_import_plans.import_plan_id', '=', 'import_plans.id')
                    ->where('purchase_import_plans.stripe_subscription_id', '=', $sub_id)
                    ->select('purchase_import_plans.*', 'billing_details.*', 'cms_users.name', 'import_plans.plan', 'import_plans.interval')
                    ->first();
                $appPrice = $importResult->price;
                $endDate = $importResult->end_date;
                $startDate = $importResult->start_date;
                $menuName = $importResult->plan;
                $companyName = $importResult->company_name;
                $billingAddress = $importResult->address;
                $billingZip = $importResult->zip;
                $billingCity = $importResult->city;
                $billingPhone = $importResult->phone;
                $userEmail = $importResult->email;
                $userName = $importResult->name;
                $countryID = $importResult->country_id;
                // $country = DB::table('countries')->where('id','=',$countryID)
                // ->select('name')
                // ->first();
                // $billingCountry = $country->name;
                // $detailsInformationForBilling = "<b>Company Name: $companyName</b></br><p>Address: $billingAddress,$billingCity,$billingZip,$billingCountry</p><p>Contact Information:</p><p>E-mail: $userEmail</p><p>Phone: $billingPhone</p>";
                $price = $appPrice;
                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($price * $taxShow) / 100;
                $order_info = [
                    'user_id' => 98,
                    'cms_client' => $importResult->cms_user_id,
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => round($price, 2),
                    'sub_total' => round($price, 2),
                    'total_tax' => round($total_tax, 2),
                    'payment_type' => "Stripe Card",
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => 3,
                    'shop_id' => 8,
                    'order_id_api' => $sub_id,
                ];

                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', $menuName);
                $cart_item['description'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'Product Import Plan Purchase Complete. App Name is "' . $menuName . '".Subscription Start from ' . $startDate . ' to ' . $endDate . '.Payment by Stripe.');
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($price, 2);
                $cart_item['tax'] = $taxShow;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($price, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);

                // $order_exist = NewOrder::where(['order_id_api' => $sub_id, 'cms_client' => $importResult->cms_user_id, 'shop_id' => 8, 'cms_user_id' => 98])->whereYear('created_at', '=', date('Y'))->whereMonth('created_at', '=', date('m'))->select('id')->first();
                // if ($order_exist && $order_exist->id) return;

                if(NewOrder::where('order_id_api', $inv_id)->exists()) return 'Import Inv already Exist';

                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $importResult->cms_user_id);
                // jahidulhasanzahid

                $slug = 'app_purchase_confirmation';
                $lang = getUserSavedLang($userEmail);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                app('drm.mailer')->getMailer()->to($userEmail)->send(new DRMSEndMail($mail_data));
                //email send end

            }
        }

        var_dump($data['data']['object']['lines']['data']);
        // }
    }


    public function appUpdate()
    {

        $user = User::with('billing_detail')->find(CRUDBooster::myParentId());
        \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
        $database_plan = DB::table('subscription_plans')->find($_POST['plan_id']);
        $priceCents = ($database_plan->price * 100);

        $purchase = DB::table('purchase_apps')->where('id', $_POST['id'])->whereNotNull('stripe_subscription_id')->first();

        $customers = \Stripe\Customer::all(['email' => $user->email]);
        $cust = $customers->jsonSerialize();
        //DB::beginTransaction();
        try {
            // customer create
            if (!empty($cust['data'])) {
                $custId = $cust['data'][0]['id'];
            } else {

                $customer = \Stripe\Customer::create(array(
                    'email' => $user->email,
                    'source' => $_POST['stripeToken'],
                ));
                $custId = $customer->id;
            }
            // plan create

            $plans = \Stripe\Plan::all(["amount" => $priceCents,
                "currency" => 'eur',
                "interval" => $database_plan->interval]);
            $plan = $plans->jsonSerialize();
            if (!empty($plan['data'])) {
                $planId = $plan['data'][0]['id'];
            } else {
                $plan = \Stripe\Plan::create(array(
                    "product" => [
                        "name" => $database_plan->name
                    ],
                    "amount" => $priceCents,
                    "currency" => 'eur',
                    "interval" => $database_plan->interval,
                    "interval_count" => 1
                ));
                $planId = $plan->id;
            }
            // cancel subscription
            if ($purchase != null) {
                resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2455', '', $purchase->stripe_subscription_id);
            }


            $subsctiption_data = [
                'customer' => $custId,
                'items' => [['plan' => $planId]]
            ];

            //Coupon
            $coupon = null;
            if (isset($_POST['coupon_id']) && $_POST['coupon_id']) {
                $coupon = \Stripe\Coupon::retrieve($_POST['coupon_id'], []);
            }

            $discount = 0;
            $total = $database_plan->price;
            $sub_price = $total;

            if ($coupon) {
                if ($coupon->valid) {
                    $subsctiption_data['coupon'] = $coupon->id;
                    $discount = ($coupon->amount_off) ? ($coupon->amount_off / 100) : (($sub_price * $coupon->percent_off) / 100);
                    $discount = ($discount > $sub_price) ? $sub_price : $discount;
                    $total = $total - $discount;
                } else {
                    throw new \Exception("Invalid Coupon code. Please try again!");
                }
            }

            $subscription = \Stripe\Subscription::create($subsctiption_data);

            // $data = \Stripe\Subscription::create(array(
            //          "customer" => $custId,
            //          "items" => array(
            //              array(
            //                  "plan" => $planId,
            //              ),
            //          ),
            //      ));

            $subsData = $subscription->jsonSerialize();
            $subscrID = $subsData['id'];
            $custID = $subsData['customer'];
            $planID = $subsData['plan']['id'];
            $planAmount = ($subsData['plan']['amount'] / 100);
            $planCurrency = $subsData['plan']['currency'];
            $planinterval = $subsData['plan']['interval'];
            $planIntervalCount = $subsData['plan']['interval_count'];
            $created = date("Y-m-d H:i:s", $subsData['created']);
            $current_period_start = date("Y-m-d", $subsData['current_period_start']);
            $current_period_end = date("Y-m-d", $subsData['current_period_end']);
            $status = $subsData['status'];

            DB::table('purchase_apps')->where('id', $_POST['id'])->update([
                'price' => $total,
                'stripe_subscription_id' => $subscrID,
                'plan_id' => request()->plan_id,
                'stripe_plan_id' => $planID,
                'type' => $planinterval,
                'status' => $status,
                'subscription_date_start' => $current_period_start,
                'subscription_date_end' => $current_period_end
            ]);
            //DB::commit();    // Commiting  ==> There is no problem whatsoever

            return response(['status' => true, 'message' => 'Update App Plan success!']);
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong

            return response(['status' => false, 'message' => $e->getMessage()]);
        }

    }


    public function subscription($type)
    {

        $time = strtotime(date('Y-m-d'));

        return date("Y-m-d", strtotime("+14 days", $time));
    }


    public function cancelSubscription($id)
    {
 
        $data = DB::table('purchase_apps')
            ->join('app_stores', 'app_stores.id', '=', 'purchase_apps.app_id')
            ->select('purchase_apps.subscription_date_end', 'purchase_apps.cms_user_id', 'purchase_apps.type', 'app_stores.menu_name', 'purchase_apps.stripe_subscription_id')
            ->where('purchase_apps.id', $id)
            ->first();
        $user = User::with('billing_detail')->find($data->cms_user_id);

        //DB::beginTransaction();
        try {

            $stripe_key = (int)$id === 59 ? 'stripe_key_2439' : 'stripe_key_2455';

            if($data && $data->stripe_subscription_id)
            {
                resolve(\App\Services\Stripe\Latest\CancelSubscription::class)($stripe_key, '', $data->stripe_subscription_id);
            }

            DB::table('purchase_apps')->where('id', $id)->update(['is_renew_cancel' => 1]);

            $tags = [
                'app_name' => $data->menu_name,
                'subscription_interval' => ucfirst($data->type),
                'period_end' => $data->subscription_date_end,
                'period_start' => date('Y-m-d'),
            ];

            $slug = 'subscription_cancel'; //Page slug
            $lang = getUserSavedLang($user->billing_detail->email);
            $mail_data = DRMParseMailTemplate($tags, $slug, $lang); //Generated html
            app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data)); //Send

            //DB::commit();    // Commiting  ==> There is no problem whatsoever

            return response(['status' => true, 'message' => 'Subscription Cancel Success!']);
        } catch (\Exception $e) {
            return response(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    // Premium Flat Rate Cancel
    public function flatRateCancel()
    {
        $data = DB::table('purchase_apps')
            ->join('app_stores', 'app_stores.id', '=', 'purchase_apps.app_id')
            ->select('purchase_apps.id', 'purchase_apps.subscription_date_end', 'purchase_apps.cms_user_id', 'purchase_apps.type', 'app_stores.menu_name')
            ->where('purchase_apps.cms_user_id', CRUDBooster::myParentId())
            ->whereNotNull('purchase_apps.subscription_date_end')
            ->get();
            //dd($data);
        $user = User::with('billing_detail')->find(CRUDBooster::myParentId());
        //dd($user);

        foreach($data as $value){

            $subscription_interval = ( $value->type ) ? '24 Months' : ' No Interval' ;

            //dd($subscription_interval);

            ////DB::beginTransaction();


                DB::table('purchase_apps')->where('id', $value->id)->update(['is_renew_cancel' => 1]);

                $tags = [
                    'app_name' => $value->menu_name,
                    'subscription_interval' => $subscription_interval,
                    'period_end' => $value->subscription_date_end,
                    'period_start' => date('Y-m-d'),
                ];
                //dd($tags, $user->billing_detail->email);

                try{
                    $slug = 'subscription_cancel'; //Page slug
                    $lang = getUserSavedLang($user->billing_detail->email);
                    $mail_data = DRMParseMailTemplate($tags, $slug, $lang); //Generated html
                    app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data)); //Send
                }catch(\Exception $ee){

                }


        }
//dd(DB::table('premium_flat_rate_users')->where('user_id', CRUDBooster::myParentId())->get());
        DB::table('premium_flat_rate_users')->where('user_id', CRUDBooster::myParentId())->update(['flat_rate_status' => 'false']);

        return response(['status' => true, 'message' => 'Premium Flat Rate Renew Cancel Success!']);

    }


    public function insertDailyOrder()
    {
        return 'Not used now!';
        //  	try{
        // $last_item = DB::table('order_sync_reports')->where('api_type', 'daily_insert')->orderBy('id', 'DESC')->first();
        // $last =  ($last_item->timestamp)? $last_item->timestamp : 0;

        // $timestamp =  \Carbon\Carbon::now()->unix();
        // return $this->insertDayliOrderData($last, $timestamp);
        //  	} catch (\Exception $e) {
        //       return response(['status'=> false,'message'=>$e->getMessage()]);
        //   }
    }


    // new webhook event

    public function webhookInvoice()
    {

        // if (app()->environment('production')) {
        $data = json_decode(request()->getContent(), true);
        Log::channel('uscreen')->info($data);

        if ($data['type'] == 'invoice.payment_succeeded') {

            $sub_id = $data['data']['object']['lines']['data'][0]['subscription'];
            $price = ($data['data']['object']['lines']['data'][0]['amount'] / 100);
            $startDate = date("Y-m-d", $data['data']['object']['lines']['data'][0]['period']['start']);
            $endDate = date("Y-m-d", $data['data']['object']['lines']['data'][0]['period']['end']);

            $inv_id = $data['data']['object']['id'];

            $existsOnNew = DB::table('stripe_payments')
            ->where('object_type', 'invoice')
            ->where('object_id', $inv_id)
            ->where('version', 1)
            ->exists();
            if($existsOnNew)
            {
                echo "Version 1 request";
                return;
            }

            $app = DB::table('purchase_apps')
                ->join('app_stores', 'app_stores.id', '=', 'purchase_apps.app_id')
                ->where('stripe_subscription_id', $sub_id)
                ->select('purchase_apps.type', 'purchase_apps.cms_user_id', 'app_stores.menu_name')
                ->first();

            $import = DB::table('purchase_import_plans')
                ->join('import_plans', 'import_plans.id', '=', 'purchase_import_plans.import_plan_id')
                ->where('purchase_import_plans.stripe_subscription_id', $sub_id)
                ->select('purchase_import_plans.product_amount_import', 'import_plans.*')
                ->first();

            if ($app) {
                DB::table('purchase_apps')->where('stripe_subscription_id', $sub_id)
                    ->update([
                        'subscription_date_start' => $startDate,
                        'subscription_date_end' => $endDate,
                    ]);


                $tags = [
                    'app_name' => $app->menu_name,
                    'app_price' => $price,
                    'subscription_interval' => ucfirst($app->type),
                    'period_start' => $startDate,
                    'period_end' => $endDate,

                    'INTERVAL' => true,
                    'FIXED' => false,
                    'FREESCOUT' => false,
                ];

                // jahidulhasanzahid
                $result = DB::table('purchase_apps')
                    ->join('cms_users', 'purchase_apps.cms_user_id', '=', 'cms_users.id')
                    ->join('billing_details', 'purchase_apps.cms_user_id', '=', 'billing_details.user_id')
                    ->join('app_stores', 'purchase_apps.app_id', '=', 'app_stores.id')
                    ->where('purchase_apps.stripe_subscription_id', '=', $sub_id)
                    ->select('purchase_apps.*', 'billing_details.*', 'cms_users.name', 'app_stores.menu_name')
                    ->first();
                $appPrice = $result->price;
                $endDate = $result->subscription_date_end;
                $startDate = $result->subscription_date_start;
                $menuName = $result->menu_name;
                $companyName = $result->company_name;
                $billingAddress = $result->address;
                $billingZip = $result->zip;
                $billingCity = $result->city;
                $billingPhone = $result->phone;
                $userEmail = $result->email;
                $userName = $result->name;
                $countryID = $result->country_id;
                // $country = DB::table('countries')->where('id','=',$countryID)
                // ->select('name')
                // ->first();
                // $billingCountry = $country->name;
                // $detailsInformationForBilling = "<b>Company Name: $companyName</b></br><p>Address: $billingAddress,$billingCity,$billingZip,$billingCountry</p><p>Contact Information:</p><p>E-mail: $userEmail</p><p>Phone: $billingPhone</p>";
                $price = $appPrice;
                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($price * $taxShow) / 100;
                $order_info = [
                    'user_id' => 2455,  //STRIPE_CLIENT
                    'cms_client' => $result->cms_user_id,
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => round(($price), 2),
                    'sub_total' => round($price - $total_tax, 2),
                    'total_tax' => round($total_tax, 2),
                    'payment_type' => "Stripe Card",
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => 3,
                    'shop_id' => 8,
                    'order_id_api' => $inv_id,
                ];


                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', $menuName);
                $cart_item['description'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'App Purchase Complete from App Store. App Name is "' . $menuName . '".Subscription Start from ' . $startDate . ' to ' . $endDate . '.Payment by Stripe.');
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($price, 2);
                $cart_item['tax'] = $taxShow;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($price, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);

                // $order_exist = NewOrder::where(['order_id_api' => $sub_id, 'cms_client' => $result->cms_user_id, 'shop_id' => 8, 'cms_user_id' => 98])->whereYear('created_at', '=', date('Y'))->whereMonth('created_at', '=', date('m'))->select('id')->first();
                // if ($order_exist && $order_exist->id) return;

                if(NewOrder::where('order_id_api', $inv_id)->exists()){
                    DB::table('new_orders')->where('order_id_api', $inv_id)->update(['status' => 'paid']);
                   return 'App subscription Inv already Exist';
                }

                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $result->cms_user_id);
                // jahidulhasanzahid

                $slug = 'app_purchase_confirmation';
                $lang = getUserSavedLang($userEmail);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                app('drm.mailer')->getMailer()->to($userEmail)->send(new DRMSEndMail($mail_data));

            } else if ($import) {
                DB::table('purchase_import_plans')->where('stripe_subscription_id', $sub_id)->update([
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                    'product_amount_import' => $import->product_amount
                ]);
                //$import->product_amount_import +

                $tags = [
                    'app_name' => 'Import Plan ' . $import->plan,
                    'app_price' => $price,
                    'subscription_interval' => ucfirst($import->interval),
                    'period_start' => $startDate,
                    'period_end' => $endDate,

                    'INTERVAL' => true,
                    'FIXED' => false,
                    'FREESCOUT' => false,
                ];

                // jahidulhasanzahid
                $importResult = DB::table('purchase_import_plans')
                    ->join('cms_users', 'purchase_import_plans.cms_user_id', '=', 'cms_users.id')
                    ->join('billing_details', 'purchase_import_plans.cms_user_id', '=', 'billing_details.user_id')
                    ->join('import_plans', 'purchase_import_plans.import_plan_id', '=', 'import_plans.id')
                    ->where('purchase_import_plans.stripe_subscription_id', '=', $sub_id)
                    ->select('purchase_import_plans.*', 'billing_details.*', 'cms_users.name', 'import_plans.plan', 'import_plans.interval')
                    ->first();
                $appPrice = $importResult->price;
                $endDate = $importResult->end_date;
                $startDate = $importResult->start_date;
                $menuName = $importResult->plan;
                $companyName = $importResult->company_name;
                $billingAddress = $importResult->address;
                $billingZip = $importResult->zip;
                $billingCity = $importResult->city;
                $billingPhone = $importResult->phone;
                $userEmail = $importResult->email;
                $userName = $importResult->name;
                $countryID = $importResult->country_id;

                DB::table('import_plan_get_discounts')->where('user_id', $importResult->cms_user_id)->where('status', 1)->update(['exp_date' => $endDate]);

                // $country = DB::table('countries')->where('id','=',$countryID)
                // ->select('name')
                // ->first();
                // $billingCountry = $country->name;
                // $detailsInformationForBilling = "<b>Company Name: $companyName</b></br><p>Address: $billingAddress,$billingCity,$billingZip,$billingCountry</p><p>Contact Information:</p><p>E-mail: $userEmail</p><p>Phone: $billingPhone</p>";
                $price = $appPrice;
                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($price * $taxShow) / 100;
                $order_info = [
                    'user_id' => 2455,  //STRIPE_CLIENT
                    'cms_client' => $importResult->cms_user_id,
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => round($price, 2),
                    'sub_total' => round($price, 2),
                    'total_tax' => round($total_tax, 2),
                    'payment_type' => "Stripe Card",
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => 3,
                    'shop_id' => 8,
                    'order_id_api' => $inv_id,
                ];

                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', $menuName);
                $cart_item['description'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'Product Import Plan Purchase Complete. App Name is "' . $menuName . '".Subscription Start from ' . $startDate . ' to ' . $endDate . '.Payment by Stripe.');
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($price, 2);
                $cart_item['tax'] = $taxShow;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($price, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);

                // $order_exist = NewOrder::where(['order_id_api' => $sub_id, 'cms_client' => $importResult->cms_user_id, 'shop_id' => 8, 'cms_user_id' => 98])->whereYear('created_at', '=', date('Y'))->whereMonth('created_at', '=', date('m'))->select('id')->first();
                // if ($order_exist && $order_exist->id) return;

                if(NewOrder::where('order_id_api', $inv_id)->exists()){
                    DB::table('new_orders')->where('order_id_api', $inv_id)->update(['status' => 'paid']);
                   return 'App subscription Inv already Exist';
                }

                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $importResult->cms_user_id);
                // jahidulhasanzahid

                $slug = 'app_purchase_confirmation';
                $lang = getUserSavedLang($userEmail);
                $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                app('drm.mailer')->getMailer()->to($userEmail)->send(new DRMSEndMail($mail_data));
                //email send end

            }
        }

        var_dump($data['data']['object']['lines']['data']);
        // }
    }


    //NOT_USED
    public function testIframe()
    {

        $data = DB::table('chronosale')->where('id', 3)->first();

        $shop_info = \App\Shop::where('id', $data->shop_id)->first();

        $table = "drm_translation_" . $data->language_code;
        $trans_category = 'category_name_' . $data->language_code;

        //Updated feed
        $productIds = explode(',', $data->product_id);
        $productId = array_map('intval', array_map('trim', $productIds));

        $table = "drm_translation_" . $data->language_code;
        $trans_category = 'category_name_' . $data->language_code;

        $products = DB::table($table)
            ->join('drm_products', 'drm_products.id', '=', $table . '.product_id')
            ->whereNull('drm_products.deleted_at')
            ->select('drm_products.*', 'drm_products.description as descriptionProduct', 'drm_products.name as product_category_nameroduct', 'drm_products.id as product_id', $table . '.description', $table . '.title as name')
            ->whereIn('drm_products.id', $productId)->get();

        dd($table, $products, $shop_info);

        // $data['url'] = 'https://drm.team';
        //   	$this->cbView('admin.mail_index_page',$data);
    }

    //NOT_USED
    public function testInterval()
    {
        $user_id = CRUDBooster::myParentId();
        $app_id = config('global.csv_interval_app_id');

        $user_has_plan = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('plan_id')->first();
        $user_has_assign = \DB::table('app_assigns')->where(['app_id' => $app_id, 'user_id' => $user_id])->select('plan_id')->first();
        $purchase_plan = ($user_has_plan) ? $user_has_plan->plan_id : 0;
        $assign_plan = ($user_has_assign) ? $user_has_assign->plan_id : 0;
        $fast_plan = ($purchase_plan > $assign_plan) ? $purchase_plan : $assign_plan;

        $user_has_trial = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'status' => 'active', 'is_free_trail' => 1, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->first();
        $plan = ($fast_plan) ? $fast_plan : (($user_has_trial) ? config('global.interval_platinum_plan') : null);
        $data = [
            'user_id' => $user_id,
            'app_id' => $app_id,
            'purchased' => $user_has_plan,
            'assign' => $user_has_assign,
            'trial' => $user_has_trial,
            'interval' => app_user_interval(app_user_plan_id($user_id, $app_id)),
        ];

        $app_id = config('global.interval_app_id');

        $user_has_plan = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('plan_id')->first();
        $user_has_assign = \DB::table('app_assigns')->where(['app_id' => $app_id, 'user_id' => $user_id])->select('plan_id')->first();
        $purchase_plan = ($user_has_plan) ? $user_has_plan->plan_id : 0;
        $assign_plan = ($user_has_assign) ? $user_has_assign->plan_id : 0;
        $fast_plan = ($purchase_plan > $assign_plan) ? $purchase_plan : $assign_plan;

        $user_has_trial = \DB::table('purchase_apps')->where(['app_id' => $app_id, 'status' => 'active', 'is_free_trail' => 1, 'cms_user_id' => $user_id])->whereDate('purchase_apps.subscription_date_end', '>=', \Carbon\Carbon::now())->select('cms_user_id')->first();
        $plan = ($fast_plan) ? $fast_plan : (($user_has_trial) ? config('global.interval_platinum_plan') : null);

        $data2 = [
            'user_id' => $user_id,
            'app_id' => $app_id,
            'purchased' => $user_has_plan,
            'assign' => $user_has_assign,
            'trial' => $user_has_trial,
            'interval' => app_user_interval(app_user_plan_id($user_id, $app_id)),
        ];


        dd($data, $data2);
        return ($user_has_trial) ? config('global.interval_platinum_plan') : $plan;


    }

    public function appTypeFlatRateCheck(){
        try {
            $app_id = request()->id;

            $plan = DB::table('app_store_plans as asp')
                    ->join('subscription_plans as sp', 'sp.id', '=', 'asp.subscription_plans_id')
                    ->where('asp.app_stores_id', $app_id)
                    ->orderBy('sp.price', 'desc')
                    ->select('sp.*')
                    ->first();

            $user_id = CRUDBooster::myParentId();

            $flat_rate_active = DB::table('premium_flat_rate_users')
                        ->where('user_id', $user_id)
                        ->first();

            $user = User::with('billing_detail')->find($user_id);
            // dd($plan, $flat_rate_active, $user);

            $purchare_db = [];
            // dd(checkUserPurchasedApp($app_id, $user_id));
            if($plan && $flat_rate_active && !checkUserPurchasedApp($app_id, $user_id)){
                $purchare_db['plan_id']                 = $plan->id;
                $purchare_db['stripe_plan_id'];
                $purchare_db['stripe_customer_id'];
                $purchare_db['payer_email']             = $user->email;
                $purchare_db['type']                    = $plan->interval;
                $purchare_db['stripe_subscription_id'];
                $purchare_db['status']                  = 'active';
                $purchare_db['subscription_date_start'] = $flat_rate_active->subscription_date_start;
                $purchare_db['subscription_date_end']   = $flat_rate_active->subscription_date_end;
            }

            //Insert app purchase data
            $filter_purchase_data = array_filter($purchare_db);
            if($filter_purchase_data){
                DB::table('purchase_apps')->updateOrInsert([
                    'app_id' => $app_id, 'cms_user_id' => $user_id
                ],
                    $filter_purchase_data
                );
            }

            if ($plan && $flat_rate_active) {
                return response()->json([
                    'success' => true,
                    'message' => 'subscription_app'
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'message' => 'buy_app'
                ]);
            }

            if (in_array($app_id, [42, 45])) {
                updateUserAppsDate($user_id, $app_id, $flat_rate_active->subscription_date_start, $flat_rate_active->subscription_date_end);
            }

        } catch (\Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }

    }

}
