<?php namespace App\Http\Controllers;

use App\Enums\Apps;
use App\Http\Controllers\tariffController;
use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use App\User;
	use ServiceKey;
	// use App\Mail\AppPurchaseConfirmation;
	use App\Mail\DRMSEndMail;
	use Illuminate\Support\Facades\Mail;
	use App\Notifications\DRMNotification;
	use Carbon\Carbon;
	use App\Services\DropCampus\DropmatixCampus;
	use App\Services\DropCampus\DroptiendaCampus;
use App\Services\Tariff\Credit\RefillCredit;

	class AdminPurchaseImportPlansController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "id";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = false;
			$this->button_edit = false;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "purchase_import_plans";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Import Plan","name"=>"import_plan_id","join"=>"import_plans,plan"];
			$this->col[] = ["label"=>"Customer","name"=>"cms_user_id","join"=>"cms_users,name"];
			$this->col[] = ["label"=>"Product Amount Import","name"=>"product_amount_import"];
			$this->col[] = ["label"=>"Start Date","name"=>"start_date"];
			$this->col[] = ["label"=>"End Date","name"=>"end_date"];
			$this->col[] = ["label"=>"Status","name"=>"status"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			// $this->form = [];
			// $this->form[] = ['label'=>'Import Plan Id','name'=>'import_plan_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'import_plan,id'];
			// $this->form[] = ['label'=>'Start Date','name'=>'start_date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'End Date','name'=>'end_date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Status','name'=>'status','type'=>'number','validation'=>'required|integer|min:0','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			//$this->form[] = ["label"=>"Import Plan Id","name"=>"import_plan_id","type"=>"select2","required"=>TRUE,"validation"=>"required|integer|min:0","datatable"=>"import_plan,id"];
			//$this->form[] = ["label"=>"Start Date","name"=>"start_date","type"=>"date","required"=>TRUE,"validation"=>"required|date"];
			//$this->form[] = ["label"=>"End Date","name"=>"end_date","type"=>"date","required"=>TRUE,"validation"=>"required|date"];
			//$this->form[] = ["label"=>"Status","name"=>"status","type"=>"number","required"=>TRUE,"validation"=>"required|integer|min:0"];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)

	    public function getImportPayment(){
	    	$plans=DB::table('import_plans')->where('status',1)->get();

	    	$user = User::find(CRUDBooster::myId());

	        //User term
	        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
	        $term = ($privacy)? $privacy->page_content : '';
	        $user_data = '<div id="customer_data_term"></div>';
	        if($user->billing_detail){
	            $billing = $user->billing_detail;
	            $user_data = '<div id="customer_data_term">'.$billing->company_name.'<br>'.$billing->address.'<br>'.$billing->zip.' '.$billing->city.'<br>'.$billing->country->name.'</div>';
	        }
	        if (strpos($term, '{customer}') !== false) {
	            $term = str_replace('{customer}', $user_data, $term);
	        }
	    	return view('app_store.import_payment',compact('plans', 'term'));
	    }

	    public function importPayment(){
	    	$old=DB::table('purchase_import_plans')->where('cms_user_id',CRUDBooster::myId())->first();
	    	$user = User::with('billing_detail')->find(CRUDBooster::myId());
			if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

		    \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
			$database_plan=DB::table('import_plans')->find($_POST['plan_id']);
	    	//craete  method
			//DB::beginTransaction();
    	try{

	    	//stripe  customer
	    	$customers = \Stripe\Customer::all(['email' => $user->email]);
		    $cust=$customers->jsonSerialize();
	    	if(!empty($cust['data'])){
		    	$custId=$cust['data'][0]['id'];
			}else{

			    $customer = \Stripe\Customer::create(array(
			        'email' => $user->email,
			        'source'  => $_POST['stripeToken'],
			    ));
			    $custId=$customer->id;
			}
	    	//stripe  customer
	    	$priceCents = ($database_plan->amount*100);
	    	//stripe  plan
	    	$plans = \Stripe\Plan::all(["amount" => $priceCents,
	        "currency" => 'eur',
	        "interval" => $database_plan->interval]);
		    $plan=$plans->jsonSerialize();
		    if(!empty($plan['data'])) {
		    	$planId=$plan['data'][0]['id'];
		    }else{
		    	$plan = \Stripe\Plan::create(array(
		        "product" => [
		            "name" => $database_plan->plan
		        ],
		        "amount" => $priceCents,
		        "currency" => 'eur',
		        "interval" => $database_plan->interval,
		        "interval_count" => 1
		      ));
		    	$planId=$plan->id;
		    }

		    //Cancel  subscription
	    	if($old !=null){
	    		resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2455', '', $old->stripe_subscription_id);
	    	}
	    	//stripe  plan

	    	$subsctiption_data = [
			    'customer' => $custId,
			    'items' => [['plan' => $planId]]
			];

    		//Coupon
    		$coupon = null;
    		if( isset($_POST['coupon_id']) && $_POST['coupon_id'] ){
    			$coupon = \Stripe\Coupon::retrieve($_POST['coupon_id'], []);
    		}

			$discount = 0;
			$total = $database_plan->amount;
			$sub_price = $total;

			if($coupon){
				if($coupon->valid){
					$subsctiption_data['coupon'] = $coupon->id;
					$discount = ($coupon->amount_off)? ($coupon->amount_off / 100) : (($sub_price * $coupon->percent_off) / 100);
					$discount = ($discount > $sub_price)? $sub_price : $discount;
					$total = $total - $discount;
				}else{
					throw new \Exception("Invalid Coupon code. Please try again!");
				}
			}

			$subscription = \Stripe\Subscription::create($subsctiption_data);


	    	// $subscription = \Stripe\Subscription::create(array(
      //           "customer" => $custId,
      //           'coupon' => 'free-period',
      //           "items" => array(
      //               array(
      //                   "plan" => $planId,
      //               ),
      //           ),
      //       ));

        $subsData = $subscription->jsonSerialize();
	    	$current_period_start = date("Y-m-d", $subsData['current_period_start']);
	      $current_period_end = date("Y-m-d", $subsData['current_period_end']);

	    	DB::table('purchase_import_plans')->updateOrInsert(['cms_user_id'=>CRUDBooster::myId()],
	          [
	          'price'=> round($total, 2),
	          'import_plan_id'=>request()->plan_id,
	          'stripe_plan_id'=>$subsData['plan']['id'],
	          'stripe_customer_id'=>$subsData['customer'],
	          'payer_email'=>$user->email,
	          'stripe_subscription_id'=> $subsData['id'],
	          'type'=>$subsData['plan']['interval'],
	          'status'=>1,
	          'start_date'=>$current_period_start,
	          'end_date'=>$current_period_end,
						'product_amount_import' => $database_plan->product_amount,
	        ]);

			// DB::table('app_trials')->where('user_id',CRUDBooster::myId())->where('app_id',0)->update(['trial_days'=>0]);

	    	//Insert order to daily account
			$taxShow = config('global.tax_for_invoice');
			$price = $database_plan->amount; //$request->fixed_price.'00';
			// $total_tax = ($price * $taxShow) /100;
			$order_info = [
				'user_id' => 98,
            	'cms_client'  => CRUDBooster::myId(),
				'order_date'    => date('Y-m-d H:i:s'),
				'total' => round($total,2),
				'sub_total' => round($sub_price,2),
				'discount' => round($discount, 2),
				'discount_type' => 'fixed',
				'total_tax' => 0,
				'payment_type'  => "Stripe Card",
				'status'    => "paid",
				'currency'  => "EUR",
				'adjustment'    => 0,
				'insert_type'   => 3,
				'shop_id'       => 8,
				'order_id_api'  => $subsData['id'],
			];

			$carts = [];
			$cart_item = [];
			$cart_item['id'] = 1;
			$cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', $database_plan->plan);
			$cart_item['description'] = $this->planDescription($database_plan);
			$cart_item['qty'] = 1;
			$cart_item['rate'] = round($price,2);
			$cart_item['tax'] = $taxShow;
			$cart_item['product_discount'] = 0;
			$cart_item['amount'] = round($price,2);
			$carts[] = $cart_item;
			$order_info['cart'] = json_encode($carts);
	        app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info);

			 //DB::commit();    // Commiting  ==> There is no problem whatsoever
             session()->put('url',null);
		    return response(['status'=>true, 'message'=> __('Import Plan Purchase success!'),'url'=>$_POST['url']]);
		    } catch (\Exception $e) {
		        //DB::rollBack();   // rollbacking  ==> Something went wrong

		        return response(['status'=> false,'message'=>$e->getMessage()]);
		    }



	  }



	  //Import payment SCA
	  public function importPaymentSCA($purchase_data){

		//DB::beginTransaction();
    	try{

    		$intend_id = $purchase_data['id'];
            if(DB::table('new_orders')->where(['order_id_api' => $intend_id, 'cms_user_id' => 2455, 'shop_id' => 8])->exists()) throw new \Exception('Already Exist!');  //STRIPE_CLIENT

	    	$user = User::with('billing_detail')->find($purchase_data['user_id']);
	    	if(is_null($user)) throw new \Exception('User not Exist!');

	    	if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

			$database_plan = DB::table('import_plans')->find($purchase_data['plan_id']);

			$import_plan_discount = 0;
			$existing_discount = null;

			DB::table('manual_import_tarrif')->where(['user_id' => $purchase_data['user_id']])->delete();

			//Find profit share data
			if($purchase_data['profit_share_id']){
				$existing_discount = DB::table('import_plan_get_discounts')->where('id', '=', $purchase_data['profit_share_id'])
									->select('get_discount', 'id')
									->first();
			}else{
				$existing_discount = DB::table('import_plan_get_discounts')->where('user_id', $user->id)->where('status' , 1)
									->where('end_date', '>=', \Carbon\Carbon::now()->toDateTimeString())
									->select('get_discount', 'id')
									->first();
			}

			//If profit share active, set exp, sub_id, status set to 1
			if(!empty($existing_discount)){
				$import_plan_discount = $existing_discount->get_discount ?? 0;
				DB::table('import_plan_get_discounts')->where('id', $existing_discount->id)->update([
					'exp_date' => $purchase_data['period_end'],
					'sub_id' => $purchase_data['subscription_id'],
					'status' => 1
				]);
			}else{
				//Cancel profit share
				DB::table('import_plan_get_discounts')->where('user_id', $user->id)->where('status' , 1)->update(['status' => 0]);
			}

	    	$old = DB::table('purchase_import_plans')->where('cms_user_id', $user->id)->first();

			$onboarding_voucher = null;
            //Increment single pay coupon usages
            if (isset($purchase_data['coupon']) && $purchase_data['coupon']) {
                DB::table('coupons')->where('coupon_id', $purchase_data['coupon'])->increment('single_pay');
				app('\App\Services\CouponService')->insertOnboardingCouponUsageLog($user->id);

				// isLocal() || 
				if (checkTariffEligibility($user->id)) {
					$onboarding_voucher = app('\App\Services\CouponService')->getValidUserVoucherByCouponId($user->id, $purchase_data['coupon']);
					if (!empty($onboarding_voucher)) {
						// $import_plan_discount = 35; // onboarding 35% discount
						app('\App\Services\CouponService')->hideOnboardingProgressBar($onboarding_voucher->coupon_code, $onboarding_voucher->voucher_id, $user->id);
					}
				}
            }

		    //Cancel old subscription
	    	if($old !=null){
	    		resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2455', '', $old->stripe_subscription_id);
	    	}

	    	$purchare_db = [];
    	    $purchare_db['price']                 	= $purchase_data['total'];
    	    $purchare_db['import_plan_id']         	= $purchase_data['plan_id'];
            $purchare_db['stripe_plan_id']          = $purchase_data['stripe_plan_id'];
            $purchare_db['stripe_customer_id']      = $purchase_data['stripe_customer_id'];
            $purchare_db['payer_email']             = $user->email;
            $purchare_db['type']                    = $purchase_data['interval_type'];
            $purchare_db['stripe_subscription_id']  = $purchase_data['subscription_id'];
            $purchare_db['status']                  = 1;
            $purchare_db['start_date'] 				= $purchase_data['period_start'];
            $purchare_db['end_date']   				= $purchase_data['period_end'];
            $purchare_db['product_amount_import']   = $database_plan->product_amount;
            $purchare_db['import_plan_percentage_discount']   = $import_plan_discount;

			if(!is_dt_user() && checkTariffEligibility($user->id)){
				$purchare_db['total_channel']   = $database_plan->no_of_online_channels;
			}

			$previous_purchase = DB::table('purchase_import_plans')->where('cms_user_id', $user->id)->exists();

	    	DB::table('purchase_import_plans')->updateOrInsert( ['cms_user_id'=> $user->id], $purchare_db);

			$session_var = 'has_deluxe_or_higher_' . $user->id;
			if (session()->has($session_var)) {
				session()->forget($session_var);
			}

            $creditRefill = new RefillCredit;

			// Referrer Bonus Credit
			if(!$previous_purchase && $user->referrer_id && checkTariffEligibility($user->referrer_id)){
				$bonus_credit = (int) (($purchare_db['price'] ?? 0) / 2);
				$tariffController = new tariffController();
				$status = \App\Enums\CreditType::CREDIT_ADD;

				// app('App\Http\Controllers\tariffController')->CreditUpdate($user->referrer_id, $bonus_credit, 'credit_add');
				// app('App\Http\Controllers\tariffController')->drmUserCreditAdd($user->referrer_id, $bonus_credit, 'User Refferal Bonus', 5, $status);

                $creditRefill->refillBonusCredit($user->referrer_id, (float) $bonus_credit, \App\Services\Tariff\Credit\CreditType::USER_REFERRAL_BONUS);
			}

			// Credit/Token insert to log and credit table
			// in_array($user->id, [212, 210, 2592]) || 
			if(checkTariffEligibility($user->id) && !empty($database_plan->credit)){
				$credit   = $database_plan->credit;

				$message = 'Import Plan Purchase Credit';
				$type = \App\Enums\CreditType::PLAN_PURCHASE;
				$status = \App\Enums\CreditType::CREDIT_ADD;

				// app('App\Http\Controllers\tariffController')->drmUserCreditAdd($user->id, $credit, $message, $type, $status);
				// app('App\Http\Controllers\tariffController')->CreditUpdate($user->id, $credit, 'credit_add');

                $creditRefill->resetTariffCredit($user->id, (float) $credit, \App\Services\Tariff\Credit\CreditType::IMPORT_PLAN_PURCHASE_CREDIT);
			}

			// Marketplace Category Access Update
			// in_array($user->id, [212, 210, 2592]) || 
			if( checkTariffEligibility($user->id) && !empty($database_plan->mp_category)){
				DB::table('cms_users')->where(['id' => $purchase_data['user_id']])->update(['category_quantity' => $database_plan->mp_category]);
			}
			
			$lock_status = 'call_from_purchase_import_plan';
			app('App\Http\Controllers\AdminDrmAllCustomersController')->lockUnlockCustomer($user->id,$lock_status);

			\DRM::refreshImportPlanSession();

	    	// $discount = $purchase_data['discount']?? 0;
      		// $total = $purchase_data['total']?? 0;
      		// $sub_total = $purchase_data['sub_total']?? 0;

            //Price calculation
            // $sub_total = $database_plan->plan;
            $sub_total = $database_plan->amount;
            $total = $purchase_data['total']?? 0;
            $discount = $sub_total - $total;

      $payment_intend_id = $purchase_data['intend_id'] ?? null;
	    	//Insert order to daily account
			$taxShow = config('global.tax_for_invoice');
			$price = $database_plan->amount; //$request->fixed_price.'00';
			$order_info = [
				'user_id' => 2455, //STRIPE_CLIENT
            	'cms_client'  => $user->id,
				'order_date'    => date('Y-m-d H:i:s'),
				'total' => round($total,2),
				'sub_total' => round($sub_total,2),
				'discount' => round($discount, 2),
				'discount_type' => empty($onboarding_voucher) ? 'fixed' : 'percentage', 
				'total_tax' => 0,
				'payment_type'  => "Stripe Card",
				'status'    => "paid",
				'currency'  => "EUR",
				'adjustment'    => 0,
				'insert_type'   => \App\Enums\InsertType::IMPORT,
				'shop_id'       => 8,
				'order_id_api'  => $intend_id,
				'intend_id' => $payment_intend_id,
			];

			$carts = [];
			$cart_item = [];
			$cart_item['id'] = 1;
			$cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', $database_plan->plan);
			$cart_item['description'] = $this->planDescription($database_plan);
			$cart_item['qty'] = 1;
			$cart_item['rate'] = round($price,2);
			$cart_item['tax'] = $taxShow;
			$cart_item['product_discount'] = 0;
			$cart_item['amount'] = round($price, 2);
			$carts[] = $cart_item;
			$order_info['cart'] = json_encode($carts);
	        app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $user->id);

			$user_tags = DB::table('user_tags')->where('user_id', $user->id)->pluck('tag')->toArray();

			// if(in_array('drm_user', $user_tags)){
			// 	$drmCampusService = new DropmatixCampus();

			// 	if($purchare_db['import_plan_id'] > 24){
			// 		$drmCampusService->createDropmatixCampusUser($user);
			// 	}else{
			// 		$drmCampusService->deactivateDropmatixCampusUser($user);
			// 	}
			// }

			if(in_array('dt_user', $user_tags)){
				$drmCampusService = new DroptiendaCampus();

				if($purchare_db['import_plan_id'] > 24){
					$drmCampusService->createDroptiendaCampusUser($user);
				}else{
					$drmCampusService->deactivateDroptiendaCampusUser($user);
				}
			}

			 //DB::commit();// Commiting  ==> There is no problem whatsoever

		    return ['success'=> true, 'message'=> __('Import Plan Purchase success!'), 'url'=> CRUDBooster::adminPath('drm_imports/import') ];
	    }catch (\Exception $e) {

	        //DB::rollBack();// rollbacking  ==> Something went wrong
	        return ['success'=> false, 'message'=>$e->getMessage()];
	    }
	}









	  public function ImportSubscriptionCancel($id)
	  {
		$user_id = CRUDBooster::myId();

        $is_dt_new_user = is_dt_user() && checkTariffEligibility($user_id);

        if (!$is_dt_new_user) {
            $import_plan = \DB::table('purchase_import_plans')
                            ->join('import_plans','import_plans.id','=','purchase_import_plans.import_plan_id')
                            ->where('purchase_import_plans.cms_user_id', $user_id)
                            ->select('purchase_import_plans.*','import_plans.amount','import_plans.plan','import_plans.interval', 'import_plans.credit as total_credit')
                            ->first();
        } else {
            $import_plan = \DB::table('dt_tariff_purchases')
                            ->join('import_plans','import_plans.id','=','dt_tariff_purchases.plan_id')
                            ->where('dt_tariff_purchases.user_id', $user_id)
                            ->select('dt_tariff_purchases.*','dt_tariff_purchases.total as price','import_plans.amount','import_plans.plan','import_plans.interval')
                            ->first();
        }

		$has_payment_contract = false;
		if (!empty($import_plan->contract_end_at) && ($import_plan->contract_end_at >= \Carbon\Carbon::now())) { 
			$has_payment_contract = true;
		}

		if ($has_payment_contract) {
	        return response(['status'=> false, 'message' => __('Subscription can only be canceled after 3 months of subscription date.')]);
		}

	  	\Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
    	$data=DB::table('purchase_import_plans as pip')
    			->join('import_plans','import_plans.id','=','pip.import_plan_id')
    			->where('pip.id',$id)
    			->first();
    	$user=User::with('billing_detail')->find($data->cms_user_id);
    	//DB::beginTransaction();
    	try{

    		if($data && $data->stripe_subscription_id)
    		{
    			resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2455', '', $data->stripe_subscription_id);
    		}

    		DB::table('purchase_import_plans')->where('id',$id)->update(['is_renew_cancel'=>1]);
			
    		$tags = [
			    'app_name' =>  $data->plan,
			    'subscription_interval' =>  ucfirst($data->interval),
			    'period_end' =>  $data->end_date,
			    'period_start' =>  date('Y-m-d'),
			];

			$slug = 'subscription_cancel'; //Page slug
            $lang = getUserSavedLang($user->billing_detail->email);
			$mail_data = DRMParseMailTemplate($tags, $slug, $lang); //Generated html

			if(!isLocal()){
				app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new DRMSEndMail($mail_data)); //Send
			}

    		//DB::commit();    // Commiting  ==> There is no problem whatsoever

	    return response(['status'=>true, 'message'=>'Subscription Cancel Success!']);
	    } catch (\Exception $e) {
	        //DB::rollBack();   // rollbacking  ==> Something went wrong

	        return response(['status'=> false,'message'=>$e->getMessage()]);
	    }
	}


	private function planDescription($plan)
	{
		if($plan->id === 25) return 'Tarifa Dropmatix mensual "Deluxe" con 1.500 artículos, 25 categorías, 5.000 consultas y 3 trabajos';

		if($plan->id === 24) return 'Tarifa mensual de Dropmatix "Starter" con 500 artículos, 10 categorías y 1 lugar de trabajo';

		if($plan->id === 26) return 'Tarifa Dropmatix mensual "Professional" con 10.000 artículos, 50 categorías, 10.000 consultas, 5 estaciones de trabajo incluyendo uso de almacén + gestión de devoluciones';

		if($plan->id === 27) return 'Tarifa mensual de Dropmatix "Enterprise" con un número ilimitado de artículos, categorías y consultas, 15 estaciones de trabajo que incluyen uso de almacén + gestión de devoluciones';

		return iconv('UTF-8', 'ASCII//TRANSLIT','Import Plan Purchase. Plan Name is "'.$plan->plan.'". Purchase Date '.date('Y-m-d H:i:s'));
	}
}
