<?php

namespace App\Http\Controllers;

use App\Services\UserService;
use CB;
use Auth;
use phpseclib\Net\SFTP;
use Schema;
use Request;
use Spatie\PdfToText\Pdf;
use Storage;
use App\Shop;
use App\User;
use DateTime;
use Exception;
use Carbon\Carbon;
use App\DrmProduct;
use App\Enums\Apps;
use App\ChannelColor;
use App\Enums\Channel;
use League\Csv\Writer;
use App\Helper\AppStore;
use App\Helper\GambioApi;
use App\Helper\LengowApi;
use App\Helper\ShopifyApi;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use App\Traits\ProjectShare;
use App\DropfunnelCustomerTag;
use App\Models\ChannelProduct;
use App\Models\EtsyCredential;
use \DTS\eBaySDK\Trading\Enums;
use \DTS\eBaySDK\Trading\Types;
use Drm\EtsyClient\Server\Etsy;
use Illuminate\Validation\Rule;
use League\Flysystem\Filesystem;
use League\Flysystem\Adapter\Ftp;
use Illuminate\Support\Facades\DB;
use App\Services\Shop\SprinterShop;
use Illuminate\Support\Facades\Http;
use App\Services\Shop\MediamarktShop;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;
use \Hkonnet\LaravelEbay\EbayServices;
use App\Notifications\DRMNotification;
use App\Services\Shop\TiendanimalShop;
use App\Services\Shop\ConradShop;
use App\Services\Shop\FressnapfShop;
use App\Services\Shop\VolknerShop;
use App\Services\Shop\ManorShop;
use App\Services\Shop\XxxlutzShop;
use App\Services\Shop\PerfumesClubShop;
use App\Services\Shop\Home24Shop;
use App\Services\Shop\AlltricksShop;
use App\Services\Shop\ClubeFashionShop;
use App\Services\Shop\ZooplusShop;
use App\Services\Shop\PssShop;
use App\Services\Shop\BigbangShop;
use App\Services\Shop\BricodepotShop;
use App\Services\Shop\HornbachShop;
use App\Services\Shop\PlanetahuertoShop;
use App\Services\Shop\CarrefourShop;
use App\Services\ChannelProductService;
use Illuminate\Support\Facades\Session;
use App\Services\Modules\Export\Colizey;
use App\Services\Shop\ColizeyValidation;
use App\Jobs\ChannelManager\AutoTransfer;
use Illuminate\Support\Facades\Validator;
use App\Services\Modules\Export\Ebay\Ebay;
use App\Services\Modules\Export\Droptienda;
use Illuminate\Http\Request as LaravelRequest;

use App\Services\Modules\Export\Kaufland\Kaufland;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Services\Modules\Export\Ebay\Api as EbayApi;
use App\Services\Modules\Export\Kaufland\Api as KauflandApi;
use App\Services\Shop\MiraklConnectShop;

class AdminShopSettingController extends \crocodicstudio\crudbooster\controllers\CBController
{
    use ProjectShare;

    private const ETSY_SCOPE = 'email_r listings_r listings_w listings_d transactions_r transactions_w address_r address_w profile_w profile_r billing_r favorites_rw shops_rw';
    public $channelName;

    private $droptiendaDaily = 2439;
    private $is_dt_daily_full_list = false;

    public function cbInit()
    {
        if (isset($_GET['lang']) && $_GET['lang']) {
            $this->lang = $_GET['lang'];
        } elseif (isset($_COOKIE['languageShortcode'])) {
            $this->lang = $_COOKIE['languageShortcode'];
        } else {
            $this->lang = "de";
        }
        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "20";
        $this->orderby = "id,desc";
        $this->global_privilege = false;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_addmore = false;
        $this->button_action_style = "button_icon";
        $this->button_add = false;

        if (isset($_REQUEST['dailydroptienda']) && (CRUDBooster::myId() == $this->droptiendaDaily || CRUDBooster::isDroptiendaSupport())) {
            $this->is_dt_daily_full_list = true;
        }

        if (isset($_GET['id']) && $_GET['id'] == 4) {
            $this->button_edit = false;
        } else {
            $this->button_edit = true;
        }

        # new added
        $shop_types = drm_shop_channels();

        $shop_name = isset($shop_types[$_GET['id']]) ? $shop_types[$_GET['id']] : null;
        $shop_enum = ($shop_name) ? $_GET['id'] . '|' . $shop_name : null;
        $my_shop_name = $shop_name . ' - ' . CRUDBooster::myName();

        // $this->button_delete = CRUDBooster::myId() == $this->droptiendaDaily || !$this->is_dt_daily_full_list;
        $this->button_delete = false;

        $this->button_detail = true;
        $this->button_show = false;
        $this->button_filter = false;
        $this->button_import = false;
        $this->button_export = false;
        $this->table = "shops";

        $this->col = [];
        $this->col[] = ["label" => "ID", "name" => "id"];
        if (CRUDBooster::isSuperadmin() || $this->is_dt_daily_full_list) {
            $this->col[] = ["label" => __("User name"), "name" => "user_id", "join"=>"cms_users,name", 'type' => 'userName'];
        }

        $this->col[] = ["label" => "Shop Name", "name" => "shop_name"];
        $this->col[] = ["label" => "Channel", "name" => "channel", "type" => "channelName"];

        $this->col[] = ["label" => "Shop URL", "name" => "url", 'type' => 'shopUrl'];


        if (!(CRUDBooster::isSuperadmin() || $this->is_dt_daily_full_list)) {
            $this->col[] = ["label" => "Products", "name" => "channel", 'type' => 'productCount'];
        }

        $this->col[] = ["label" => "Version", "name" => "shop_version"];

        $this->col[] = ["label" => "Kategorie", "name" => "category", 'type' => 'category'];

        $this->col[] = ["label" => __("Legal texts"), "name" => "protected_shop", 'type' => 'protected_shop'];

        if(!$this->is_dt_daily_full_list){
            $this->col[] = ["label" => "Auto transfer", "name" => "auto_transfer", 'type' => 'auto_transfer'];
        }
        # START FORM DO NOT REMOVE THIS LINE
        $this->form = [];
        $this->form[] = ['label' => 'Channel', 'name' => 'channel', 'id' => 'channel', 'type' => 'select', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'dataenum' => $shop_enum, 'help' => 'Gambio Version 3.15.2', 'value' => $_GET['id'], 'type' => 'hidden'];
        $this->form[] = ['label' => 'Shop Name', 'name' => 'shop_name', 'type' => 'text', 'validation' => 'required', 'width' => 'col-sm-10', 'placeholder' => 'Please Enter Shop Name', 'value' => $my_shop_name];

        if ($_GET['id'] == 6) {
            $shop_url_hint = 'Please enter valid shop URL : https://xxx.myshopify.com/';
        } else {
            $shop_url_hint = 'Please enter valid shop URL : https://example.com/';
        }

        $this->form[] = ['label' => 'Shop URL', 'name' => 'url', 'type' => 'text', 'validation' => 'required', 'width' => 'col-sm-10', 'placeholder' => $shop_url_hint];
        $this->form[] = ['label' => 'Username / Access Token', 'name' => 'username', 'type' => 'text', 'width' => 'col-sm-10', 'placeholder' => 'Please enter a username or access token'];
        $this->form[] = ['label' => 'Password / Secret', 'name' => 'password', 'type' => 'text', 'width' => 'col-sm-10', 'placeholder' => 'Please enter a user password or secret'];
        $this->form[] = ['label' => 'HNR', 'name' => 'hnr', 'placeholder' => 'Please enter HNR number','type' => 'text' , 'validation' => 'required'];
        $this->form[] = ['label' => 'Seller Company ID', 'name' => 'seller_company_id', 'placeholder' => 'Please enter Seller Company ID','type' => 'text' , 'validation' => 'required'];

        $this->form[] = ['label' => 'User Id', 'name' => 'user_id', 'type' => 'hidden'];
        $this->form[] = ['label' => ' ', 'name' => 'is_history', 'type' => 'text'];
        $this->form[] = ['label' => 'Language', 'name' => 'lang', 'type' => 'hidden', 'value' => $_GET['lang']];
        // $this->form[] = ['label'=>'Access Token','name'=>'token','type'=>'text','width'=>'col-sm-10','placeholder'=>'Please enter a valid access token'];
        // $this->form[] = ['label'=>'Secret','name'=>'secret','type'=>'text','width'=>'col-sm-10','placeholder'=>'Please enter a valid secret'];
        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */

        // select('" . $_GET['id'] . "');
        // if('" . $_GET['id'] . "' == 7){
        //   setWooCommerce();
        // }else if( ('" . $_GET['id'] . "' == 8) || ('" . $_GET['id'] . "' == 9 ) ){
        //     Chrono24();
        // }else if('" . $_GET['id'] . "' == 5){
        //     amazonShop();
        // }else {
        //     setDefault();
        // }


        $channel_delete_url = CRUDBooster::mainpath('remove-shop/') . $_GET['id'];
        $i_am_sure = __('I AM SURE');
        $are_you_sure = __('Are you sure you want to delete this shop?');
        $please_write = __('Please write');
        $confirmation_info_one = __('in the text field to initiate the deletion process.');
        $confirmation_info_two = __('The data will be irrevocably deleted after confirmation.');
        $confirmation_text_doesnt_match = __('Confirmation text doesnt match! Please type ');
        $btn_confirm = __('Confirm');
        $btn_cancel = __('Cancel');
        $select_shop = __('Please Select Shop');

        $this->script_js = "
        $(function() {

            $(document).ready(function(){
                let shop_type = $('#form-group-shop_name').siblings('input[name*=\'channel\']').val();

                let searchParams = new URLSearchParams(window.location.search)
                let history = searchParams.get('is_history')
                //$('#is_history').val(history);
                $('#is_history').attr('value',history);
                $('#is_history').attr('type','hidden');

                if(shop_type != 16 || shop_type != 21){
                    $('#form-group-hnr').hide();
                    $('#hnr').prop('required',false);
                }

                if(shop_type != 40){
                    $('#form-group-seller_company_id').hide();
                    $('#seller_company_id').prop('required',false);
                }
                select(shop_type);
                if(shop_type == 7){
                    setWooCommerce();
                }else if( (shop_type == 8) || (shop_type == 9 ) ){
                    Chrono24();
                }else if(shop_type == 5){
                    amazonShop();
                }else if(shop_type == 12){
                    ottoShop();
                }else if(shop_type == 14){
                    Check24();
                }else if(shop_type == 15){
                    Decathlon();
                }else if(shop_type == 16 || shop_type == 21){
                    TradeByte();
                }else if(shop_type == 17){
                    Sprinter();
                }else if(shop_type == 18){
                    Tiendanimal();
                }else if(shop_type == 19){
                    MediaMarkt();
                }else if(shop_type == 20){
                    Colizey();
                }else if(shop_type == 22){
                    Decathlon();
                }else if(shop_type == 23){
                    Decathlon();
                }else if(shop_type == 24){
                    blank();
                } else if(shop_type == 25){
                    Decathlon();
                } else if(shop_type == 26){
                    Decathlon();
                } else if(shop_type == 27){
                    Decathlon();
                } else if(shop_type == 28){
                    Decathlon();
                } else if(shop_type == 29){
                    Decathlon();
                } else if(shop_type == 30){
                    Decathlon();
                } else if(shop_type == 32){
                    Decathlon();
                } else if(shop_type == 33){
                    Decathlon();
                } else if(shop_type == 34){
                    Decathlon();
                } else if(shop_type == 35){
                    Decathlon();
                } else if(shop_type == 36){
                    Decathlon();
                } else if(shop_type == 37){
                    Decathlon();
                } else if(shop_type == 38){
                    Decathlon();
                } else if(shop_type == 39){
                    Decathlon();
                } else if(shop_type == 40){
                    MiraklConnect();
                } else {
                    setDefault();
                }

            });

            $('#channel').on('change',function(){
                select(this.value);
                if(this.value == 7){
                    setWooCommerce();
                }else if(this.value == 12){
                    ottoShop();
                }else if( (this.value == 8) || (this.value == 9) ){
                    Chrono24();
                }else if(shop_type == 14){
                    Check24();
                }else if(shop_type == 15){
                    Decathlon();
                }else if(shop_type == 16 || shop_type == 21){
                    TradeByte();
                }else {
                    setDefault();
                }
            });
        });

        function setWooCommerce(){
            $('#form-group-user label').html('Public Key');
            $('#form-group-password label').html('Private Key / Secret Key');

            $('#form-group-user input').attr('placeholder','Please enter the Public Key');
            $('#form-group-password input').attr('placeholder','Please enter the Secret Key');
        }

        function setDefault(){
            $('#form-group-user label').html('Username / Access Token');
            $('#form-group-password label').html('Password / Secret');

            $('#form-group-url').show();
            $('#form-group-user').show();
            $('#form-group-password').show();

            $('#form-group-user input').attr('placeholder','Please enter a username or access token');
            $('#form-group-password input').attr('placeholder','Please enter a user password or secret');
        }
        function select(value){
            if(value == '1'){
                $('.help-block').show();
            }else{
                $('.help-block').hide();
            }
        }

        function Chrono24(){
            var url = $('input#url').val();
            url = (url && url.length)? url : '#';
            $('input#url').val(url);
            $('#form-group-url').hide();
            $('#form-group-user').hide();
            $('#form-group-password').hide();
        }


        function TradeByte(){
            var url = $('input#url').val();
            url = (url && url.length)? url : '#';
            $('input#url').val(url);
            $('#form-group-url').hide();
            $('#form-group-user').hide();
            $('#form-group-userpas').hide();
            $('#form-group-hnr').show();
            $('#hnr').prop('required',true);
        }

        function MiraklConnect(){
            $('#form-group-url').hide();
            $('#form-group-url #url').val('#');
            $('#form-group-url #url').prop('required', false);

            $('#form-group-seller_company_id').show();
            $('#form-group-seller_company_id #seller_company_id').prop('required', true);
        }

        function Decathlon(){
            $('#form-group-url').hide();
            $('#form-group-url #url').val('#');
            $('#form-group-url #url').prop('required', false);
            $('#form-group-url').hide();

            $('#form-group-user label').html(`Shop Token <span class='text-danger' title='This field is required'>*</span>`);
            $('#form-group-user #user').attr('placeholder', 'Your shop token');
            $('#form-group-user #user').prop('required', true);

            $('#form-group-password').hide();
        }
        function Sprinter(){
            $('#form-group-url').hide();
            $('#form-group-url #url').val('#');
            $('#form-group-url #url').prop('required', false);
            $('#form-group-url').hide();

            $('#form-group-user label').html(`Shop Token <span class='text-danger' title='This field is required'>*</span>`);
            $('#form-group-user #user').attr('placeholder', 'Your shop token');
            $('#form-group-user #user').prop('required', true);

            $('#form-group-password').hide();
        }
        function Tiendanimal(){
            $('#form-group-url').hide();
            $('#form-group-url #url').val('#');
            $('#form-group-url #url').prop('required', false);
            $('#form-group-url').hide();

            $('#form-group-user label').html(`Shop Token <span class='text-danger' title='This field is required'>*</span>`);
            $('#form-group-user #user').attr('placeholder', 'Your shop token');
            $('#form-group-user #user').prop('required', true);

            $('#form-group-password').hide();
        }
        function MediaMarkt(){
            $('#form-group-url').hide();
            $('#form-group-url #url').val('#');
            $('#form-group-url #url').prop('required', false);
            $('#form-group-url').hide();

            $('#form-group-user label').html(`Shop Token <span class='text-danger' title='This field is required'>*</span>`);
            $('#form-group-user #user').attr('placeholder', 'Your shop token');
            $('#form-group-user #user').prop('required', true);

            $('#form-group-password').hide();
        }
        function Colizey(){
            $('#form-group-password label').html(`Api Key <span class='text-danger' title='This field is required'>*</span>`);
            $('#form-group-password #password').attr('placeholder','Please insert your key here');

            $('#form-group-username').hide();
        }

        function Check24(){
            $('#form-group-url').hide();
            $('#form-group-url #url').val('#');
            $('#form-group-url #url').prop('required', false);

            $('#form-group-user label').html(`Username <span class='text-danger' title='This field is required'>*</span>`);
            $('#form-group-user #user').attr('placeholder', 'Your API access username');
            $('#form-group-user #user').prop('required', true);

            $('#form-group-password label').html(`Password <span class='text-danger' title='This field is required'>*</span>`);
            $('#form-group-password #password').attr('placeholder', 'Your API access password');
            $('#form-group-password #password').prop('required', true);
        }

        function amazonShop(){
            $('#form-group-url').hide();
            $('#form-group-url #url').val('#');
            $('#form-group-url #url').prop('required', false);

            $('#form-group-username').hide();
            $('#form-group-password').hide();

            // $('#form-group-user label').html(`Seller Id <span class='text-danger' title='This field is required'>*</span>`);
            // $('#form-group-user #user').attr('placeholder', 'Enter your Seller Id here');
            // $('#form-group-user #user').prop('required', true);

            // $('#form-group-password label').html(`MWSAuthToken <span class='text-danger' title='This field is required'>*</span>`);
            // $('#form-group-password #password').attr('placeholder', 'Enter your MWSAuthToken here');
            // $('#form-group-password #password').prop('required', true);
        }

        function ottoShop(){
            $('#form-group-url').show();
            $('#form-group-url #url').val('#');
            $('#form-group-url label').html('Shop URL');
            $('#form-group-url #url').prop('required', false);

            $('#form-group-user label').html(`Otto username <span class='text-danger' title='This field is required'>*</span>`);
            $('#form-group-user #user').attr('placeholder', 'Enter your username here');
            $('#form-group-user #user').prop('required', true);

            $('#form-group-password label').html(`Password <span class='text-danger' title='This field is required'>*</span>`);
            $('#form-group-password #password').attr('placeholder', 'Enter your password here');
            $('#form-group-password #password').prop('required', true);
        }

        function blank() {
            $('#form-group-url #url').prop('required', false);
            $('#form-group-url').hide();
            $('#form-group-username').hide();
            $('#form-group-password').hide();
        }

        $(document).on('change', '.update_shop_type_category', function(e){
            const category = $(this).val();
            const old_val = $(this).data('val');
            const id = $(this).data('id');
            $(this).val(old_val);
            swal({
                title: 'Hello!',
                text: 'Are you sure to change category',
                type: 'info',
                showCancelButton: true,
                confirmButtonColor: '#00a65a',
                confirmButtonText: 'Yes',
                cancelButtonText: 'No',
                closeOnConfirm: false
            }, function () {
                swal({
                    title: 'Loading...',
                    imageUrl: window.ASSET_URL+ 'images/loading.gif',
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    confirm: true,
                    showLoaderOnConfirm: true,
                    confirmButtonColor: 'DD6B55',
                })
                $.ajax({
                    url:  window.ADMIN_PATH + '/shop_setting/assign-shop-type-category/'+id,
                    type:'POST',
                    data: {
                        category,
                    },
                    success:function(response){
                        if (response.success) {
                            swal('Success!', response.message, 'success');
                            $(this).val(category);
                            $(this).attr('data-val', category);
                            window.location.reload()
                        }else{
                            swal('Error!', response.message, 'error');
                        }
                    },
                    error: function (response) {
                        swal('Error!', 'Something went wrong!', 'error');
                    }
                });
            });
        })

        function getShopIds()
        {
            let shop_ids = [];
            $('#table_dashboard .checkbox:checked').each(function (i) {
                shop_ids[i] = $(this).val();
            });
            return shop_ids;
        }

        $(document).on('click', '#delete-channel, #kanal-loschen', function(e){
            e.preventDefault();

            let shop_ids = getShopIds();

            if(shop_ids.length == 0){
                return swal('Info', '{$select_shop}!!', 'info');
            }

            const iAmSure = '{$i_am_sure}';

            swal({
                title: 'Warning !',
                text: '{$are_you_sure} <br><br> {$please_write} <strong>'+ iAmSure + '</strong> ' + ' {$confirmation_info_one} {$confirmation_info_two}',
                type: 'input',
                inputPlaceholder: iAmSure,
                html: true,
                showCancelButton: true,
                confirmButtonColor: 'green',
                confirmButtonText: '{$btn_confirm}',
                cancelButtonText: '{$btn_cancel}',
                closeOnConfirm: false,
                closeOnCancel: true,
                showConfirmButton: true,
                showLoaderOnConfirm: true
            }, function(inputText) {
                if (inputText === false) return false;

                if (inputText != iAmSure) {
                    swal.showInputError('{$confirmation_text_doesnt_match}' + iAmSure);
                    return false;
                }

                $.ajax({
                    url: window.ADMIN_PATH + '/shop_setting/all-remove-shop',
                    type: 'GET',
                    data: {
                        shop_ids:shop_ids
                    },
                    success:function(response){
                        window.location.reload();
                    },
                    error: function (error) {
                        console.log(error);
                    }
                });

            });
        })

        ";


        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ['label'=>'Shop URL','name'=>'url','type'=>'text','validation'=>'required|url','width'=>'col-sm-10','placeholder'=>'Please enter a valid shop URL'];
        //$this->form[] = ['label'=>'Username','name'=>'user','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        //$this->form[] = ['label'=>'Password','name'=>'pass','type'=>'text','validation'=>'required|min:6|max:32','width'=>'col-sm-10'];
        # OLD END FORM

        /*
        | ----------------------------------------------------------------------
        | Sub Module
        | ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key    = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
        |
        */
        $this->sub_module = array();


        /*
                | ----------------------------------------------------------------------
                | Add More Action Button / Menu
                | ----------------------------------------------------------------------
                | @label       = Label of action
                | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
                | @icon        = Font awesome class icon. e.g : fa fa-bars
                | @color       = Default is primary. (primary, warning, succecss, info)
                | @showIf      = If condition when action show. Use field alias. e.g : [id] == 1
                |
                */
        $this->addaction = array();

        $ebay_template = AppStore::ActiveFeature('eBay-Template');
        if ($ebay_template) {
            $this->addaction[] = ['label' => 'eBay Template', 'url' => CRUDBooster::mainpath('ebay-template').'/[id]', 'icon' => 'fa fa-gear', 'color' => 'success', 'showIf' => '[channel] == 4'];
        }

        if(CRUDBooster::isSuperadmin()){
            $this->addaction[] = ['label' => '', 'url' => CRUDBooster::mainpath('lock-dt-shop/[id]/[user_id]'), 'icon' => 'fa fa-unlock-alt', 'title' => 'Lock Shop', 'color' => 'danger', 'showIf' => '[is_locked_dt] == 0'];
            $this->addaction[] = ['label' => '', 'url' => CRUDBooster::mainpath('unlock-dt-shop/[id]/[user_id]'), 'icon' => 'fa fa-lock', 'title' => 'Unlock Shop', 'color' => 'info', 'showIf' => '[is_locked_dt] != 0'];
        }


        if($this->is_dt_daily_full_list && CRUDBooster::isDroptiendaSupport())
        {
            $this->addaction[] = ['label' => '', 'url' => CRUDBooster::adminPath('users/loginas-user/[user_id]'), 'icon' => 'fa fa-sign-in', 'title' => 'Login', 'color' => 'info'];
        }

        $this->addaction[] = ['label' => __('Location Settings'), 'url' => CRUDBooster::mainpath('ebay-inventory-locations').'/[id]', 'icon' => 'fa fa-gear', 'color' => 'warning', 'showIf' => '[channel] == 4'];
        $this->addaction[] = ['label' => __('Location Settings'), 'url' => CRUDBooster::mainpath('kaufland-inventory-warehouses').'/[id]', 'icon' => 'fa fa-gear', 'color' => 'warning', 'showIf' => '[channel] == 13'];

        $this->addaction[] = ['label' => 'Update Shop', 'url' => CRUDBooster::mainpath('update-shop/[channel]/[id]'), 'icon' => 'fa fa-refresh', 'color' => 'warning', 'showIf' => '[channel] == 4'];

        $this->addaction[] = ['label' => 'Sync Category', 'url' => CRUDBooster::mainpath('sync-category/[id]'), 'icon' => 'fa fa-refresh', 'color' => 'info', 'showIf' => '[channel] == 1'];

        // Remove server
        if($this->hasServerDeleteAccess())
        {
            $this->addaction[] = ['title'=>'Remove server','url'=>CRUDBooster::mainpath('remove-server/[id]'),'color'=>'danger', 'confirmation_confirmButtonText' => 'Yes', 'confirmation_btn_show' => true, 'confirmation_text' => 'Are you sure to remove server?', 'icon' => 'fa fa-power-off', 'showIf'=>'[channel] == 10 && !empty([server_id])', 'confirmation' => true];

            //   $this->addaction[] = ['title' => 'Delete','url' => CRUDBooster::mainpath('remove-shop/[id]'), 'confirmation_title' => __('crudbooster.delete_title_confirm'), 'confirmation_text' => __('crudbooster.delete_description_confirm'), 'color'=>'danger', 'icon' => 'fa fa-trash', 'confirmation' => true, 'showIf' => '[channel] == 10'];
        }

        // if (CRUDBooster::myId() == $this->droptiendaDaily || !$this->is_dt_daily_full_list) {
        //   $this->addaction[] = ['title' => 'Delete','url' => CRUDBooster::mainpath('remove-shop/[id]'), 'confirmation_title' => __('crudbooster.delete_title_confirm'), 'confirmation_text' => __('crudbooster.delete_description_confirm'), 'color'=>'danger', 'icon' => 'fa fa-trash', 'confirmation' => true, 'showIf' => '[channel] != 10'];
        // }

        /*
                | ----------------------------------------------------------------------
                | Add More Button Selected
                | ----------------------------------------------------------------------
                | @label       = Label of action
                | @icon        = Icon from fontawesome
                | @name        = Name of button
                | Then about the action, you should code at actionButtonSelected method
                |
                */
        $this->button_selected = array();

        if (CRUDBooster::isSuperadmin() || $this->is_dt_daily_full_list)
        {
            $this->button_selected[] = ['label' => 'Export (CSV)', 'icon' => 'fa fa-arrow-down', 'name' => 'export_csv'];
            $this->button_selected[] = ['label' => 'Export (XLS)', 'icon' => 'fa fa-arrow-down', 'name' => 'export_xls'];
        }


        /*
                | ----------------------------------------------------------------------
                | Add alert message to this module at overheader
                | ----------------------------------------------------------------------
                | @message = Text of message
                | @type    = warning,success,danger,info
                |
                */
        $this->alert = array();


        /*
                | ----------------------------------------------------------------------
                | Add more button to header button
                | ----------------------------------------------------------------------
                | @label = Name of button
                | @url   = URL Target
                | @icon  = Icon from Awesome.
                |
                */
        $this->index_button = array();

        $shop_info = Shop::find($_GET['id'] ?? 0);

        $show_delete_btn = false;
        if ($this->hasServerDeleteAccess() && $shop_info->channel == 10) {
            $show_delete_btn = true;
        }

        if ((CRUDBooster::myId() == $this->droptiendaDaily || !$this->is_dt_daily_full_list) && ($shop_info->channel != 10)) {
            $show_delete_btn = true;
        }

        if ($show_delete_btn) {
            $this->index_button[] = ['label' => __('Delete Channel'), 'url' => '#' , "icon" => "fa fa-trash", 'color' => 'danger'];
        }

        /*
                | ----------------------------------------------------------------------
                | Customize Table Row Color
                | ----------------------------------------------------------------------
                | @condition = If condition. You may use field alias. E.g : [id] == 1
                | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
                |
                */
        $this->table_row_color = array();


        /*
                | ----------------------------------------------------------------------
                | You may use this bellow array to add statistic at dashboard
                | ----------------------------------------------------------------------
                | @label, @count, @icon, @color
                |
                */
        $this->index_statistic = array();


        /*
                | ----------------------------------------------------------------------
                | Include HTML Code before index table
                | ----------------------------------------------------------------------
                | html code to display it before index table
                | $this->pre_index_html = "<p>test</p>";
                |
                */
        $this->pre_index_html = '
            <style type="text/css">
                #content_section .pull-left>.selected-action{
                    /* display: none!important; */
                }
                .sweet-alert button.cancel {
                    background-color: red !important;
                }
            </style>
        ';


        /*
                | ----------------------------------------------------------------------
                | Include HTML Code after index table
                | ----------------------------------------------------------------------
                | html code to display it after index table
                | $this->post_index_html = "<p>test</p>";
                |
                */
        $this->post_index_html = null;


        /*
                | ----------------------------------------------------------------------
                | Include Javascript File
                | ----------------------------------------------------------------------
                | URL of your javascript each array
                | $this->load_js[] = asset("myfile.js");
                |
                */
        $this->load_js = array();


        /*
                | ----------------------------------------------------------------------
                | Add css style at body
                | ----------------------------------------------------------------------
                | css code in the variable
                | $this->style_css = ".style{....}";
                |
                */
        $this->style_css = "
                    #form-group-user{
                        display: none;
                            }
                            #form-group-password{
                                display: none;
                        }
                        #form-group-token{
                        display: none;
                            }
                            #form-group-secret{
                                display: none;
                        }
                        #form-group-url{
                        display: none;
                        }
                        .help-block{
                        display: none;
                        }
                    }
                    a#btn_add_new_data{
                        display:none !important;
                    }
                ";


        /*
                | ----------------------------------------------------------------------
                | Include css File
                | ----------------------------------------------------------------------
                | URL of your css each array
                | $this->load_css[] = asset("myfile.css");
                |
                */
        $this->load_css = array();
    }

    // Server delete access
    private function hasServerDeleteAccess()
    {
        return $this->is_dt_daily_full_list || CRUDBooster::myId() == $this->droptiendaDaily || CRUDBooster::isDroptiendaSupport() || CRUDBooster::isSuperAdmin();
    }

    public function getLockDtShop($id,$user_id)
    {
        Shop::where('id', $id)->update(['is_locked_dt' => 1]);
        $shop = Shop::where([
            'user_id' => $user_id,
            'channel' => Channel::DROPTIENDA
        ])->first();
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->getPrefix($shop->url).'api/v1/shop-control',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array(
                'isLocked' => "true"
            ),
            CURLOPT_HTTPHEADER => array(
                'userToken: '.$shop->username,
                'userPassToken: '.$shop->password
            ),
        ));
        $response = json_decode(curl_exec($curl),true);
        curl_close($curl);


        CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Shop is Locked!", "success");
    }

    // Server remove
    public function getRemoveServer($id)
    {
        if(!$this->hasServerDeleteAccess()) abort(404);

        try {
            $shop = Shop::find($id);
            app(\App\Services\DeactiveUser\UserRemove::class)->removeDtServer($shop);

        } catch (\Exception $e) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $e->getMessage(), "error");
        }

        CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Droptienda server removed successfully!", "success");
    }

    public function getUnlockDtShop($id,$user_id)
    {
        Shop::where('id', $id)->update(['is_locked_dt' => 0]);
        $shop = Shop::where([
            'user_id' => $user_id,
            'channel' => Channel::DROPTIENDA
        ])->first();
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->getPrefix($shop->url).'api/v1/shop-control',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array(
                'isLocked' => "false"
            ),
            CURLOPT_HTTPHEADER => array(
                'userToken: '.$shop->username,
                'userPassToken: '.$shop->password
            ),
        ));
        $response = json_decode(curl_exec($curl),true);
        curl_close($curl);
        CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Shop is Unlocked!", "success");
    }


    //shop category
    private function shopTypeCategories()
    {
        return [
            0 => 'Test store',
            1 => 'Real customer',
            2 => 'Template sample',
        ];
    }

    //Shop category name
    private function shopTypeCategoryName($id)
    {
        $category_name = DB::table('shop_category')->where('id', $id)->value('category_name');
        return $category_name ?? '';
    }

    //Generate shopTypeCategoryOption
    private function shopTypeCategoryOption($id, $selected)
    {
        if (!CRUDBooster::isSuperadmin() && CRUDBooster::myParentId() != 2439) return $this->shopTypeCategoryName($selected);

        $categories = DB::table('shop_category')->get();

        $options = '';
        foreach ($categories as $cat) {

            $option_selected = $cat->id == $selected ? 'selected' : '';
            $options .= '<option value="' . $cat->id . '" ' . $option_selected . '>' . $cat->category_name . '</option>';
        }

        return '<select class="update_shop_type_category form-control" data-val="' . $selected . '" data-id="' . $id . '" >' . $options . '</select>';
    }

    // Remove tag on category update
    private function manageTagOnCategoryUpdate($userId, $categoryId)
    {
        if (empty($categoryId) || $categoryId != 8) return;
        $customerId = DB::table('new_customers')->where('cc_user_id', $userId)->where('user_id', 2455)->value('id');
        if(empty($customerId)) return;

        // 31046 : test phase active
        DB::table('dropfunnel_customer_tags')->where('customer_id', $customerId)->where('tag_id', 31046)->delete();
    }

    public function postAssignShopTypeCategory($id)
    {
        try {

            if (!CRUDBooster::isSuperadmin() && CRUDBooster::myParentId() != 2439) {
                throw new \Exception('Invalid access!');
            }

            if (!isset($_REQUEST['category'])) {
                throw new \Exception('Invalid access!');
            }

            $category = $_REQUEST['category'];
            $category_item = DB::table('shop_category')->where('id', $category)->first();
            $shop = Shop::where('id', '=', $id)->first();

            if ($category_item) {
                $updated = Shop::where('id', '=', $id)->update(['category' => $category]);
                if ($updated) {
                    $this->manageTagOnCategoryUpdate($shop->user_id, $shop->category);

                    if($shop->channel == 10)
                    {
                        $this->manageDtShopCharge($shop, $category);
                    }

                    return response()->json([
                        'success' => true,
                        'message' => 'Category update successfully!',
                    ]);
                }
            }

            throw new \Exception('Nothing changed!');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }


    // Manage DT shop charge
    private function manageDtShopCharge(Shop $shop, $category)
    {
        app(\App\Services\Droptienda\DtShopCharge::class)->categoryAction($shop, $category);
    }


    //Index
    public function getIndex()
    {
        $this->cbLoader();
        $user_id = CRUDBooster::myParentId();
        $module = CRUDBooster::getCurrentModule();

        if (!CRUDBooster::isDroptiendaSupport() && !CRUDBooster::isView() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

        if (Request::get('parent_table')) {
            $parentTablePK = CB::pk(g('parent_table'));
            $data['parent_table'] = DB::table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
            if (Request::get('foreign_key')) {
                $data['parent_field'] = Request::get('foreign_key');
            } else {
                $data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
            }

            if ($parent_field) {
                foreach ($this->columns_table as $i => $col) {
                    if ($col['name'] == $parent_field) {
                        unset($this->columns_table[$i]);
                    }
                }
            }
        }

        $data['table'] = $this->table;
        $data['table_pk'] = CB::pk($this->table);
        $data['page_title'] = $module->name;
        $data['page_description'] = trans('crudbooster.default_module_description');
        $data['date_candidate'] = $this->date_candidate;
        $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

        $tablePK = $data['table_pk'];
        $table_columns = CB::getTableColumns($this->table);
        $result = DB::table($this->table)->select(DB::raw($this->table . "." . $this->primary_key));

        if (Request::get('parent_id')) {
            $table_parent = $this->table;
            $table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
            $result->where($table_parent . '.' . Request::get('foreign_key'), Request::get('parent_id'));
        }

        $result->leftjoin('server_statuses', 'shops.id', '=', 'server_statuses.shop_id')
            ->addSelect('server_statuses.id as server_id');

        $result->addselect('shops.created_at');

        $this->hook_query_index($result);

        if (in_array('deleted_at', $table_columns)) {
            $result->where($this->table . '.deleted_at', null);
        }

        if ($this->is_dt_daily_full_list && in_array('url', $table_columns) && !CRUDBooster::myId() == 2945 ) {
            $result->where($this->table . '.url', 'LIKE', '%.eu');
        }
        $alias = [];
        $join_alias_count = 0;
        $join_table_temp = [];
        $table = $this->table;
        $columns_table = $this->columns_table;
        foreach ($columns_table as $index => $coltab) {

            $join = @$coltab['join'];
            $join_where = @$coltab['join_where'];
            $join_id = @$coltab['join_id'];
            $field = @$coltab['name'];
            $join_table_temp[] = $table;

            if (!$field) {
                continue;
            }

            if (strpos($field, ' as ') !== false) {
                $field = substr($field, strpos($field, ' as ') + 4);
                $field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
                $result->addselect(DB::raw($coltab['name']));
                $columns_table[$index]['type_data'] = 'varchar';
                $columns_table[$index]['field'] = $field;
                $columns_table[$index]['field_raw'] = $field;
                $columns_table[$index]['field_with'] = $field_with;
                $columns_table[$index]['is_subquery'] = true;
                continue;
            }

            if (strpos($field, '.') !== false) {
                $result->addselect($field);
            } else {
                $result->addselect($table . '.' . $field);
            }

            $field_array = explode('.', $field);

            if (isset($field_array[1])) {
                $field = $field_array[1];
                $table = $field_array[0];
            } else {
                $table = $this->table;
            }

            if ($join) {

                $join_exp = explode(',', $join);

                $join_table = $join_exp[0];
                $joinTablePK = CB::pk($join_table);
                $join_column = $join_exp[1];
                $join_alias = str_replace(".", "_", $join_table);

                if (in_array($join_table, $join_table_temp)) {
                    $join_alias_count += 1;
                    $join_alias = $join_table . $join_alias_count;
                }
                $join_table_temp[] = $join_table;

                $result->leftjoin($join_table . ' as ' . $join_alias, $join_alias . (($join_id) ? '.' . $join_id : '.' . $joinTablePK), '=', DB::raw($table . '.' . $field . (($join_where) ? ' AND ' . $join_where . ' ' : '')));
                $result->addselect($join_alias . '.' . $join_column . ' as ' . $join_alias . '_' . $join_column);

                $join_table_columns = CRUDBooster::getTableColumns($join_table);
                if ($join_table_columns) {
                    foreach ($join_table_columns as $jtc) {
                        $result->addselect($join_alias . '.' . $jtc . ' as ' . $join_alias . '_' . $jtc);
                    }
                }

                $alias[] = $join_alias;
                $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
                $columns_table[$index]['field'] = $join_alias . '_' . $join_column;
                $columns_table[$index]['field_with'] = $join_alias . '.' . $join_column;
                $columns_table[$index]['field_raw'] = $join_column;

                @$join_table1 = $join_exp[2];
                @$joinTable1PK = CB::pk($join_table1);
                @$join_column1 = $join_exp[3];
                @$join_alias1 = $join_table1;

                if ($join_table1 && $join_column1) {

                    if (in_array($join_table1, $join_table_temp)) {
                        $join_alias_count += 1;
                        $join_alias1 = $join_table1 . $join_alias_count;
                    }

                    $join_table_temp[] = $join_table1;

                    $result->leftjoin($join_table1 . ' as ' . $join_alias1, $join_alias1 . '.' . $joinTable1PK, '=', $join_alias . '.' . $join_column);
                    $result->addselect($join_alias1 . '.' . $join_column1 . ' as ' . $join_column1 . '_' . $join_alias1);
                    $alias[] = $join_alias1;
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
                    $columns_table[$index]['field'] = $join_column1 . '_' . $join_alias1;
                    $columns_table[$index]['field_with'] = $join_alias1 . '.' . $join_column1;
                    $columns_table[$index]['field_raw'] = $join_column1;
                }
            } else {

                if (isset($field_array[1])) {
                    $result->addselect($table . '.' . $field . ' as ' . $table . '_' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $table . '_' . $field;
                    $columns_table[$index]['field_raw'] = $table . '.' . $field;
                } else {
                    $result->addselect($table . '.' . $field);
                    $columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
                    $columns_table[$index]['field'] = $field;
                    $columns_table[$index]['field_raw'] = $field;
                }

                $columns_table[$index]['field_with'] = $table . '.' . $field;
            }
        }

        if ( !(CRUDBooster::isSuperadmin() || $this->is_dt_daily_full_list || !empty($_REQUEST['search_by'])) && Request::get('q')) {
            $result->where(function ($w) use ($columns_table, $request) {
                foreach ($columns_table as $col) {
                    if (!$col['field_with']) {
                        continue;
                    }
                    if ($col['is_subquery']) {
                        continue;
                    }
                    $w->orwhere($col['field_with'], "like", "%" . Request::get("q") . "%");
                }
            });
        }

        if (Request::get('where')) {
            foreach (Request::get('where') as $k => $v) {
                $result->where($table . '.' . $k, $v);
            }
        }

        //Additional columns

        if($this->is_dt_daily_full_list)
        {
            $result->addSelect(['turnover' => \App\NewOrder::selectRaw('sum(eur_total) as total')
                ->whereColumn('new_orders.cms_client', 'shops.user_id')
                ->where(function($q) {
                    $q->where(function($qq) {
                        $qq->where('new_orders.insert_type', 4)
                            ->where('new_orders.cart', 'LIKE', '%Paywall%')
                            ->whereIntegerInRaw('new_orders.cms_user_id', [98, 2455]);
                    })->orWhere(function($qq) {
                        $qq->where('new_orders.insert_type', 6)
                            ->where('new_orders.cms_user_id', '=', 2439);
                    });
                })
                ->where('new_orders.status', 'paid')
                ->groupBy('new_orders.cms_client')
            ]);

        }else {

            $result->addSelect(['turnover' => \App\NewOrder::selectRaw('sum(eur_total) as total')
                ->whereColumn('new_orders.shop_id', 'shops.id')
                ->where('new_orders.test_order', '!=', 1)
                ->where('new_orders.invoice_number', '>', 0)
                ->groupBy('shop_id')
            ]);
        }

        $result->addSelect('shops.channel as channel');
        $result->addSelect('shops.is_suspended_dt as is_suspended_dt');
        $result->addSelect('shops.is_locked_dt as is_locked_dt');

        $filter_is_orderby = false;
        if (Request::get('filter_column')) {

            $filter_column = Request::get('filter_column');
            $result->where(function ($w) use ($filter_column, $fc) {
                foreach ($filter_column as $key => $fc) {

                    $value = @$fc['value'];
                    $type = @$fc['type'];

                    if ($type == 'empty') {
                        $w->whereNull($key)->orWhere($key, '');
                        continue;
                    }

                    if ($value == '' || $type == '') {
                        continue;
                    }

                    if ($type == 'between') {
                        continue;
                    }

                    switch ($type) {
                        default:
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'like':
                        case 'not like':
                            $value = '%' . $value . '%';
                            if ($key && $type && $value) {
                                $w->where($key, $type, $value);
                            }
                            break;
                        case 'in':
                        case 'not in':
                            if ($value) {
                                $value = explode(',', $value);
                                if ($key && $value) {
                                    $w->whereIn($key, $value);
                                }
                            }
                            break;
                    }
                }
            });

            foreach ($filter_column as $key => $fc) {
                $value = @$fc['value'];
                $type = @$fc['type'];
                $sorting = @$fc['sorting'];


                if($key == 'turnover') {
                    $result->orderby('turnover', $sorting);
                    continue;
                }

                if ($sorting != '') {
                    if ($key) {
                        $result->orderby($key, $sorting);
                        $filter_is_orderby = true;
                    }
                }

                if ($type == 'between') {
                    if ($key && $value) {
                        $result->whereBetween($key, $value);
                    }
                } else {
                    continue;
                }
            }
        }

        if ($filter_is_orderby == true) {
            $data['result'] = $result->paginate($limit);
        } else {
            if ($this->orderby) {
                if (is_array($this->orderby)) {
                    foreach ($this->orderby as $k => $v) {
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                            $k = explode(".", $k)[1];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                } else {
                    $this->orderby = explode(";", $this->orderby);
                    foreach ($this->orderby as $o) {
                        $o = explode(",", $o);
                        $k = $o[0];
                        $v = $o[1];
                        if (strpos($k, '.') !== false) {
                            $orderby_table = explode(".", $k)[0];
                        } else {
                            $orderby_table = $this->table;
                        }
                        $result->orderby($orderby_table . '.' . $k, $v);
                    }
                }
                $data['result'] = $result->paginate($limit);
            } else {
                $data['result'] = $result->orderby($this->table . '.' . $this->primary_key, 'desc')->paginate($limit);
            }
        }

        $columns_table[] = [
            "label" => __("Turnover"),
            "name" => "turnover",
            "type" => "currency",
            "field" => "turnover",
            "field_raw" => "turnover",
            "field_with" => "turnover",
        ];
        if (in_array(CRUDBooster::myParentId(), [71, 2439])) {
            $columns_table[] = [
                "label" => __("User status"),
                "name" => "status",
                "field" => "status",
                "type" => "status",
                "field_raw" => "status",
                "field_with" => "status",
            ];
        }

        // Daily full list
        if($this->is_dt_daily_full_list || CRUDBooster::isSuperadmin())
        {
            $columns_table[] = array("label" => "Created At", "name" => "created_at", "field_with" =>  "created_at", 'width' => '100', 'sorting' => true);
        }

        $data['columns'] = $columns_table;

        if ($this->index_return) {
            return $data;
        }

        //LISTING INDEX HTML
        $addaction = $this->data['addaction'];


        $protected_shop_modules = config('protectedShops.modules', []);
        $protected_shop_modules = array_keys($protected_shop_modules);


        if ($this->sub_module) {
            foreach ($this->sub_module as $s) {
                $table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
                $addaction[] = [
                    'label' => $s['label'],
                    'icon' => $s['button_icon'],
                    'url' => CRUDBooster::adminPath($s['path']) . '?return_url=' . urlencode(Request::fullUrl()) . '&parent_table=' . $table_parent . '&parent_columns=' . $s['parent_columns'] . '&parent_columns_alias=' . $s['parent_columns_alias'] . '&parent_id=[' . (!isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']) . ']&foreign_key=' . $s['foreign_key'] . '&label=' . urlencode($s['label']),
                    'color' => $s['button_color'],
                    'showIf' => $s['showIf'],
                ];
            }
        }

        $mainpath = CRUDBooster::mainpath();
        $orig_mainpath = $this->data['mainpath'];
        $title_field = $this->title_field;
        $html_contents = [];
        $page = (Request::get('page')) ? Request::get('page') : 1;
        $number = ($page - 1) * $limit + 1;
        foreach ($data['result'] as $row) {
            $html_content = [];

            if ($this->button_bulk_action) {

                $html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='" . $row->{$tablePK} . "'/>";
            }

            if ($this->show_numbering) {
                $html_content[] = $number . '. ';
                $number++;
            }

            foreach ($columns_table as $col) {
                if ($col['visible'] === false) {
                    continue;
                }

                $value = @$row->{$col['field']};
                $title = @$row->{$this->title_field};
                $label = $col['label'];

                if (isset($col['image'])) {
                    if ($value == '') {
                        $value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='" . asset('vendor/crudbooster/avatar.jpg') . "'><img width='40px' height='40px' src='" . asset('vendor/crudbooster/avatar.jpg') . "'/></a>";
                    } else {
                        $pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
                        $value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='" . $pic . "'><img width='40px' height='40px' src='" . $pic . "'/></a>";
                    }
                }

                if (@$col['download']) {
                    $url = (strpos($value, 'http://') !== false) ? $value : asset($value) . '?download=1';
                    if ($value) {
                        $value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
                    } else {
                        $value = " - ";
                    }
                }

                if ($col['str_limit']) {
                    $value = trim(strip_tags($value));
                    $value = Str::limit($value, $col['str_limit']);
                }

                if ($col['nl2br']) {
                    $value = nl2br($value);
                }

                if ($col['callback_php']) {
                    foreach ($row as $k => $v) {
                        $col['callback_php'] = str_replace("[" . $k . "]", $v, $col['callback_php']);
                    }
                    @eval("\$value = " . $col['callback_php'] . ";");
                }

                //New method for callback
                if (isset($col['callback'])) {
                    $value = call_user_func($col['callback'], $row);
                }

                $datavalue = @unserialize($value);
                if ($datavalue !== false) {
                    if ($datavalue) {
                        $prevalue = [];
                        foreach ($datavalue as $d) {
                            if ($d['label']) {
                                $prevalue[] = $d['label'];
                            }
                        }
                        if ($prevalue && count($prevalue)) {
                            $value = implode(", ", $prevalue);
                        }
                    }
                }

                if ($col['type'] == 'currency') {
                    $value = number_format((float)$value, 2, ',', '.') . formatCurrency('EUR');
                }

                if ($col['type'] == 'status') {
                    $user = DB::table('shops')->where('id', $row->id)->select('user_id', 'url')->first();

                    $userInfo = User::where('id', $user->user_id)->select('status')->first();
                    if ($userInfo) {
                        if (!empty($userInfo->status)) {
                            $value = '<span class="badge badge-pill badge-success status-success">Active</span>';
                        } else {
                            $value = '<span class="badge badge-secondary status-default">Inactive</span>';
                        }
                    } else {
                        $value = '<span class="badge badge-danger status-danger">Account Not Exits</span>';
                    }

                    $isUrl = preg_match("/^((?:https?\:\/\/|www\.)(?:[-a-z0-9]+\.)*[-a-z0-9]+.*)$/i", $user->url);
                    if (empty($user->url) || !$isUrl) {
                        $value .= ' <span class="badge badge-danger status-danger">URL Not Exits</span>';
                    }

                }


                if ($col['type'] == 'category') {
                    $value = $this->shopTypeCategoryOption($row->id, $value);
                }

                if($col['type'] == 'dt_category') {
                    $value = '...';
                }

                if ($col['type'] == 'protected_shop' && (in_array($row->channel, $protected_shop_modules) || in_array($row->channel, \App\Enums\Channel::MIRAKL_CHANNELS) )) {

                    $legal_data = drmIsJSON($row->protected_shop) ? json_decode($row->protected_shop, true) : [];
                    if (isset($legal_data["documents"]) && !empty($legal_data["documents"])) {

                        $list_doc_li = '';
                        foreach ($legal_data["documents"] as $key => $documents) {
                            foreach ($documents as $doc_type => $document) {
                                $doc_name = "$key ($doc_type)";
                                $list_doc_li .= '<li><a href="' . $document . '" target="_blank">
                                <i class="fa fa-download" style="color:green;"></i> ' . $doc_name . '</a>
                                </li>';
                            }

                            $list_doc_li .= '<li role="separator" class="divider"></li>';
                        }

                        $editHtml = '
                        <div
                            data-id="' . $row->{$tablePK} . '"
                            data-lang="' . $this->lang . '"
                            style="margin-left:2px;"
                            class="
                            btn btn-xs btn-default
                            off
                            legal_tax_toggle
                          ">
                          '.__('Edit').'
                        </div>
                        ';

                        $value = '<div class="btn-group">
                          <button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-download" style="color:green;"></i>
                          </button>
                          <ul class="dropdown-menu">' . $list_doc_li . '</ul>
                        </div>'.$editHtml;

                    } else {
                        $value = '
                        <div
                            data-id="' . $row->{$tablePK} . '"
                            data-lang="' . $this->lang . '"
                            class="
                            btn btn-xs btn-default
                            off
                            legal_tax_toggle
                          ">
                          '.__('Deactive').'
                        </div>
                        ';
                    }
                } elseif ($col['type'] == 'auto_transfer') {
                    $checked = $value ? "checked" : "";
                    if (professionalOrHigher()) {
                        $value = '<label data-id="' . $row->{$tablePK} . '" class="switch auto_transfer_toggle"><input id="auto_transfer_switch" type="checkbox" ' . $checked . '><span class="slider round"></span></label>';
                    } else {
                        $value = 'N/A';
                    }
                }

                if($col['type'] =='is_locked_dt' && in_array($row->channel, $protected_shop_modules) ) {

                    if($row->is_locked_dt == 0){
                        $value = '
                        <div data-id="'.$row->id.'" class="btn btn-xs btn-default off dt_shop_lock">
                            Unlocked
                        </div>
                        ';
                    }else{
                        $value = '
                        <div data-id="'.$row->id.'" class="btn btn-xs btn-default on dt_shop_lock">
                            Locked
                        </div>
                        ';
                    }


                }

                if($col['name'] === 'created_at') {
                    $value = date('Y-m-d H:i:s', strtotime($row->created_at));
                }


                if($col['type'] == 'shopUrl') {
                    if(filter_var($row->url, FILTER_VALIDATE_URL)) {
                        $value = '<a target="_blank" href="'.$row->url.'">'.$row->url.'</a>';
                    }
                }

                if($col['type'] == 'userName') {
                    $value = '<a target="_blank" href="'.url('/admin/users/detail/'.$row->user_id).'">'.$row->cms_users_name.'</a>';
                }
                if($col['type'] == 'channelName') {
                    $channels = config('channel.list');
                    $value = collect($channels)->where('type',$row->channel)->first()['name'] ?? "";
                }
                if($col['type'] == 'productCount') {
                    $value = \App\Models\ChannelProduct::where([
                        'user_id' => CRUDBooster::myParentId(),
                        'channel' => (int)$row->channel,
                        'shop_id' => $row->id,
                        'is_connected' => 1
                    ])->count();
                }
                $html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action):

                $button_action_style = $this->button_action_style;
                $html_content[] = "<div class='button_action' style='text-align:right'>" . view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render() . "</div>";

            endif;//button_table_action

            foreach ($html_content as $i => $v) {
                $this->hook_row_index($i, $v);
                $html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];
        $versions_query = DB::table('shops')
            ->select('shop_version')
            ->distinct()
            ->whereNotNull('shop_version')
            ->orderBy('shop_version');

        if (!$this->is_dt_daily_full_list) {
            $user_id = CRUDBooster::myId();
            $versions_query->where('shops.id', $_REQUEST['id'])->where("user_id", $user_id);
        } else {
            $versions_query->where('shops.channel', $_REQUEST['id'])
                ->join('cms_users as u', 'shops.user_id', '=', 'u.id')
                // ->whereNotNull('u.status')
                ->where($this->table . '.url', 'LIKE', '%.eu%');
        }

        $data['versions'] = $versions_query->get();
        $data['html_contents'] = $html_contents;
        $data['categories'] = DB::table('shop_category')->get();

        return view("admin.shop.index", $data);
    }


    /*
        | ----------------------------------------------------------------------
        | Hook for button selected
        | ----------------------------------------------------------------------
        | @id_selected = the id selected
        | @button_name = the name of button
        |
        */


    public function postActionSelected()
    {
        $this->cbLoader();
        $id_selected = Request::input('checkbox');
        $button_name = Request::input('button_name');

        if (! $id_selected) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans("crudbooster.alert_select_a_data"), 'warning');
        }

        if ($button_name == 'delete') {
            if (! CRUDBooster::isDelete()) {
                CRUDBooster::insertLog(trans("crudbooster.log_try_delete_selected", ['module' => CRUDBooster::getCurrentModule()->name]));
                CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
            }

            $this->hook_before_delete($id_selected);
            $tablePK = CB::pk($this->table);
            if (CRUDBooster::isColumnExists($this->table, 'deleted_at')) {

                DB::table($this->table)->whereIn($tablePK, $id_selected)->update(['deleted_at' => date('Y-m-d H:i:s')]);
            } else {
                DB::table($this->table)->whereIn($tablePK, $id_selected)->delete();
            }
            CRUDBooster::insertLog(trans("crudbooster.log_delete", ['name' => implode(',', $id_selected), 'module' => CRUDBooster::getCurrentModule()->name]));

            $this->hook_after_delete($id_selected);

            $message = trans("crudbooster.alert_delete_selected_success");

            return redirect()->back()->with(['message_type' => 'success', 'message' => $message]);
        }

        $action = str_replace(['-', '_'], ' ', $button_name);
        $action = ucwords($action);
        $type = 'success';
        $message = trans("crudbooster.alert_action", ['action' => $action]);

        try {
            if ($button_name === 'export_csv')
            {
                return resolve(\App\Services\Shop\Export::class)->run($id_selected, 'csv');
            } elseif ($button_name === 'export_xls') {
                return resolve(\App\Services\Shop\Export::class)->run($id_selected, 'xlsx');
            }
        } catch(\Exception $e) {
            $message = ! empty($this->alert['message']) ? $this->alert['message'] : 'Error';
            $type = ! empty($this->alert['type']) ? $this->alert['type'] : 'danger';
        }

        if ($this->actionButtonSelected($id_selected, $button_name) === false) {
            $message = ! empty($this->alert['message']) ? $this->alert['message'] : 'Error';
            $type = ! empty($this->alert['type']) ? $this->alert['type'] : 'danger';
        }

        return redirect()->back()->with(['message_type' => $type, 'message' => $message]);
    }



    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here
        if ($button_name === 'url_approve')
        {
            self::ApproveURL($id_selected);
        }
    }


    /*
        | ----------------------------------------------------------------------
        | Hook for manipulate query of index result
        | ----------------------------------------------------------------------
        | @query = current sql query
        |
        */
    public function hook_query_index(&$query)
    {
        //Your code here
        //dd($_REQUEST);
        setcookie('ShopSettingShopType', $_REQUEST['id'], time() + 60 * 60 * 24 * 365);
        setcookie('ShopSettingShopLang', $_REQUEST['lang'], time() + 60 * 60 * 24 * 365);

        if (!(CRUDBooster::isSuperadmin() || $this->is_dt_daily_full_list)) {
            $user_id = CRUDBooster::myId();
            $query->where('shops.id', $_REQUEST['id'])->where("user_id", $user_id);
        } else {
            $query->where('shops.channel', $_REQUEST['id'])
                ->join('cms_users as u', 'shops.user_id', '=', 'u.id');
            // ->whereNotNull('u.status');

            if(in_array($_REQUEST['search_by'], ['name', 'email'])) {
                $col = $_REQUEST['search_by'];
                $val = $_REQUEST['q'];
                $query->where('cms_users.'.$col, 'LIKE', '%'.$val.'%');
            }

            // $query->where(function($item){
            //     $item->where('url', 'not like', "%localhost%")
            //     ->where('url', 'not like', "%127.0%")
            //     ->where('url', 'not like', "%droptienda.rocks%");
            // });
        }
        if($_REQUEST['search_by'] === 'url') {
            $val = $_REQUEST['q'];
            $query->where('shops.url', 'LIKE', '%'.$val.'%');
        }


        if ($_REQUEST['version']) {
            $query->Where('shop_version', trim($_REQUEST['version']));
        }

        if ($_REQUEST['category']) {
            $query->Where('category', trim($_REQUEST['category']));
        }

        if($_REQUEST['dt_category']) return;


        if ($_REQUEST['lang']) {
            $query->where($this->table.'.lang', $_REQUEST['lang']);
        } else {
            $query->where($this->table.'.lang', 'de');
        }


        // dd($query->get());
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for manipulate row of index table html
        | ----------------------------------------------------------------------
        |
        */
    public function hook_row_index($column_index, &$column_value)
    {

    }

    //Shop turnover
    public function shopTurnover($id)
    {

        $total = \App\NewOrder::where('shop_id', '=', $id)
            ->select('eur_total')
            ->where('test_order', '!=', 1)
            ->where('invoice_number', '>', 0)
            ->where('credit_number', 0)
            ->sum("eur_total");

        return number_format((float)$total, 2, ',', '.') . formatCurrency('EUR');
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for manipulate data input before add data is execute
        | ----------------------------------------------------------------------
        | @arr
        |
        */
    public function hook_before_add(&$postdata)
    {

        $postdata['password'] = $_POST['password'];
        $postdata['url'] = $postdata['channel'] == Channel::GALAXUS? sanitize_ftp_url($postdata['url']): parse_shop_url($postdata['url']);
        $postdata["user_id"] = CRUDBooster::myParentId();
        $postdata["channel"] = (int) $postdata["channel"];

        if ($postdata['channel'] == Channel::ETSY) {
            try {
                Session::put('shop_data', $postdata);
                $server = new \Drm\EtsyClient\Server\Etsy([
                    'identifier' => $postdata['username'],
                    'secret' => $postdata['password'],
                    'scope' => self::ETSY_SCOPE,
                    'callback_uri' => route('etsy.callback', [null])
                ]);
                // Retrieve temporary credentials
                $temporaryCredentials = $server->getTemporaryCredentials();
                Session::put('etsy_temporary_credentials', serialize($temporaryCredentials));
                CRUDBooster::redirect($server->getAuthorizationUrl($temporaryCredentials), '')->send();

                return redirect($server->getAuthorizationUrl($temporaryCredentials))->send();
            } catch (\Exception $e) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), "error")->send();
            }
        } elseif ($postdata['channel'] == Channel::EBAY) {
            // For eBay, redirect to OAuth flow for new shop creation
            try {
                // Create state parameter for new shop creation
                $state = base64_encode(json_encode([
                    'user_id' => $postdata["user_id"],
                    'action' => 'create',
                    'timestamp' => time(),
                    'shop_data' => $postdata // Store shop data for after OAuth
                ]));
                
                $authUrl = 'https://auth.ebay.com/oauth2/authorize?' . http_build_query([
                    'client_id' => 'EXPERTIS-DRM-PRD-f193e18a1-6a51f647',
                    'response_type' => 'code',
                    'redirect_uri' => 'EXPERTISEROCKS_-EXPERTIS-DRM-PR-xszybym',
                    'scope' => 'https://api.ebay.com/oauth/api_scope https://api.ebay.com/oauth/api_scope/sell.marketing.readonly https://api.ebay.com/oauth/api_scope/sell.marketing https://api.ebay.com/oauth/api_scope/sell.inventory.readonly https://api.ebay.com/oauth/api_scope/sell.inventory https://api.ebay.com/oauth/api_scope/sell.account.readonly https://api.ebay.com/oauth/api_scope/sell.account https://api.ebay.com/oauth/api_scope/sell.fulfillment.readonly https://api.ebay.com/oauth/api_scope/sell.fulfillment https://api.ebay.com/oauth/api_scope/sell.analytics.readonly https://api.ebay.com/oauth/api_scope/sell.finances https://api.ebay.com/oauth/api_scope/sell.payment.dispute https://api.ebay.com/oauth/api_scope/commerce.identity.readonly',
                    'state' => $state
                ]);
                
                CRUDBooster::redirect($authUrl, '')->send();
            } catch (\Exception $e) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), "error")->send();
            }
        }elseif($postdata['channel'] == Channel::COLIZEY){
            $exists = DB::table('shops')
                ->where('user_id',$postdata["user_id"])
                ->where('shop_name',$postdata["shop_name"])
                ->where('password',$postdata["password"])
                ->where('lang',$postdata["lang"])
                ->exists();
            if($exists) CRUDBooster::redirect(Request::server('HTTP_REFERER'), "Shop Already Exists!!", "error")->send();
        }
        self::shopValidation((object)$postdata);
    }




    public function postSaveDocument($shopId)
    {
        request()->validate([
            'file.*' => 'required|mimes:pdf|max:5120'
        ]);
        $userId = CRUDBooster::myParentId();

        $shop = Shop::where([
            'user_id' => $userId,
            'id' => $shopId
        ])->first();

        try {
            if (!$shop) throw new \Exception('Access denied',403);

            if(request()->hasFile('file'))
            {
                $documents = [];
                foreach(request()->file('file') as $file)
                {
                    $ext = $file->getClientOriginalExtension();
                    $name = $file->getClientOriginalName();
                    $name = basename($name);

                    $path = 'legal-text/' . 'id_' .$userId. '_'.time();

                    $pdf = $this->storeFile(file_get_contents($file),$path.'.'.$ext);

                    $htmlData = $this->extractPdfText($file);

                    $htmlUrl = $this->storeFile($htmlData,$path.'.html');

                    $documents[$name] = [
                        'pdf' => $pdf,
                        'html' => $htmlUrl
                    ];
                }

                $shop->protected_shop = [
                    'id' => '',
                    'documents' => $documents,
                ];
                $shop->save();

                CRUDBooster::redirectBack(__('Saved successfully'), 'success');
            }

            throw new \Exception('Nothing changed');

        } catch (\Exception $e) {
            CRUDBooster::redirectBack($e->getMessage(), 'error');
        }
    }

    private function storeFile($data,$path): string
    {
        Storage::disk('spaces')->put($path, $data, 'public');
        return Storage::disk('spaces')->url($path);
    }


    public function extractPdfText($file): string
    {
        try {
            $directory = 'temp-pdf';
            if (!is_dir($directory)) {
                mkdir($directory, 0777, true);
            }

            $rand = Str::random();
            $localPath = "$directory/$rand.pdf";

            $pdfContent = file_get_contents($file);
            if ($pdfContent === false) {
                throw new Exception('Failed to fetch PDF content.');
            }
            file_put_contents($localPath, $pdfContent);

            $text = Pdf::getText($localPath);
            unlink($localPath);

            return $text;
        } catch (Exception $e) {
            return '';
        }
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for execute command after add public static function called
        | ----------------------------------------------------------------------
        | @id = last insert id
        |
        */
    public function hook_after_add($id)
    {
        $shop = Shop::find($id);
        $this->tagInsertToCustomer($shop->user_id, drm_shop_type_name($shop->channel), 8);

        //Clear account activity step
        \App\Services\CheckListProgress\Checklist::cache_key_clear(5, $shop->user_id);

        // Agb Logfile
        create_agb_log(
            $shop->user_id,
            [
                'agb' => config('agb.channel_add'),
                'shop_info' => ['shop_name' => $shop->shop_name, 'shop_type' => $shop->channel, 'shop_url' => $shop->url,],
            ], [],
            $shop->shop_name . ' Shop Added Successfully!'
        );
        // End Agb Logfile


        $is_exists = DB::table('new_order_sync_histories')->where(['shop_id' => $shop->id, 'drm_user_id' => $shop->user_id])->exists();
        if (!$is_exists && $shop->is_history == 0) {   // no previous order history
            DB::table('new_order_sync_histories')->insert(
                [
                    'shop_id' => $shop->id,
                    'drm_user_id' => $shop->user_id,
                    'last_date' => Carbon::now(),
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);
        }

        $update_color = [
            'gambio_id' => $id,
            'color' => "#ffffff"
        ];
        ChannelColor::updateOrCreate(
            $update_color
        );


        app('App\Services\UserService')->addDefaultCalculation($shop->user_id);
        if($shop->channel == Channel::DROPTIENDA){
            app('App\Services\UniversalExportService')->addDefaultFeed($shop->user_id);
        }

        session()->flash('confetti_success', 'Thank you!');
        return redirect('admin/shop_setting?id=' . $shop->id . '&lang=' . $shop->lang)->send();
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for manipulate data input before update data is execute
        | ----------------------------------------------------------------------
        | @postdata = input post data
        | @id       = current id
        |
        */
    public function hook_before_edit(&$postdata, $id)
    {
        $shop = Shop::find($id);
        $postdata['password'] = $_POST['password'];
        $postdata['url'] = Str::start(str_replace('http://', 'https://', Str::finish($postdata['url'], '/')), 'https://');
        self::shopValidation((object)$postdata);
        $postdata['status'] = 1;
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for execute command after edit public static function called
        | ----------------------------------------------------------------------
        | @id       = current id
        |
        */
    public function hook_after_edit($id)
    {
        $user_id = CRUDBooster::myId();
        $shop = Shop::where('id', $id)->where('user_id', $user_id)->first();

        if ($shop && $shop->channel == 11) {
            try {
                return redirect($this->storeEtsyCredentials($shop))->send();
            } catch (\Exception $e) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), $e->getMessage(), "error")->send();
            }
        }

        session()->flash('confetti_success', 'Thank you!');
        return redirect('admin/shop_setting?id=' . $id . '&lang=' . $_COOKIE['ShopSettingShopLang'])->send();
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for execute command before delete public static function called
        | ----------------------------------------------------------------------
        | @id       = current id
        |
        */
    public function hook_before_delete($id)
    {
        $shop = Shop::where('id', $id);

        $admin = CRUDBooster::isSuperAdmin() || (CRUDBooster::myId() == $this->droptiendaDaily);
        if (!$admin) {
            $shop->where('user_id', CRUDBooster::myId());
        }
        $shop = $shop->first();
        if (empty($shop)) {
            CRUDBooster::redirectBack('Access denied!');
        }

        try {
            // Remove DT instance
            if($shop->channel == 10 && $admin)
            {
                // Remove server
                try {
                    app(\App\Services\DeactiveUser\UserRemove::class)->removeDtServer($shop);
                } catch(\Exception $ex) {}
            }elseif($shop->channel == Channel::COLIZEY){
                // Unpublish products from Colizey in chunks
                ChannelProduct::where(['channel' => $shop->channel, 'shop_id' => $shop->id, 'is_connected' => true])
                    ->pluck('item_number')
                    ->chunk(100, function ($itemNumbers) use ($shop) {
                        // Convert item numbers to an array
                        $skus = $itemNumbers->toArray();

                        // Unpublish products using the chunk of SKUs
                        app(Colizey::class)->unpublishProductsUsingSkus($skus, $shop->password);
                    });
                // Delete the ChannelProduct records
                ChannelProduct::where(['channel' => $shop->channel, 'shop_id' => $shop->id])->delete();
            }

            app(ChannelProductService::class)->deleteChannelProducts($shop->id, CRUDBooster::myParentId());
            app(ChannelProductService::class)->deleteChannelCategories($shop->id, CRUDBooster::myParentId());

            app(Droptienda::class)->disconnectShop($shop->toArray());

            $this->tagInsertToCustomer($shop->user_id, drm_shop_type_name($shop->channel) . ' cancels', 8);

            //Clear account activity step
            \App\Services\CheckListProgress\Checklist::cache_key_clear(5, $shop->user_id);

            // Agb Logfile

            create_agb_log(
                $shop->user_id,
                [
                    'agb' => config('agb.channel_add'),
                    'shop_info' => ['shop_name' => $shop->shop_name, 'shop_type' => $shop->channel, 'shop_url' => $shop->url,],
                ], [],
                $shop->shop_name . ' Shop Deleted!'
            );

        } catch (\Exception $e) {
            CRUDBooster::redirectBack($e->getMessage());
        }


        // End Agb Logfile
    }

    /*
        | ----------------------------------------------------------------------
        | Hook for execute command after delete public static function called
        | ----------------------------------------------------------------------
        | @id       = current id
        |
        */
    public function hook_after_delete($id)
    {

    }

    public function etsyCallback($shopId = null)
    {
        try {
            $oauthToken = request()->get('oauth_token');
            $oauthVerifier = request()->get('oauth_verifier');

            if ($shopId == null && Session::has('shop_data')) {
                $shopData = Session::get('shop_data');
                $shopId = Shop::insertGetId($shopData);
                EtsyCredential::updateOrCreate(
                    ['shop_id' => $shopId],
                    [
                        'consumer_key' => $shopData['username'],
                        'consumer_secret' => $shopData['password'],
                        'temporary_credentials' => Session::get('etsy_temporary_credentials'),
                    ]
                );
            }
            $shop = Shop::where('id', $shopId)->where('user_id', CRUDBooster::myId())->first();

            //Clear account activity step
            \App\Services\CheckListProgress\Checklist::cache_key_clear(5, $shop->user_id);

            $etsyCredential = \App\Models\EtsyCredential::where('shop_id', $shopId)->first();

            $etsyClientService = new \Drm\EtsyClient\EtsyService([
                'consumer_key' => $etsyCredential->consumer_key,
                'consumer_secret' => $etsyCredential->consumer_secret,
                'temporary_credentials' => $etsyCredential->temporary_credentials,
                'scope' => self::ETSY_SCOPE
            ]);

            $tokenCredentials = $etsyClientService->approve($oauthToken, $oauthVerifier);

            $etsyCredential->oauth_token = $oauthToken;
            $etsyCredential->oauth_verifier = $oauthVerifier;
            $etsyCredential->access_token = $tokenCredentials->getIdentifier();
            $etsyCredential->access_token_secret = $tokenCredentials->getSecret();
            $etsyCredential->status = 1;
            $etsyCredential->save();

            Session::forget('shop_data');
            Session::forget('etsy_temporary_credentials');

            $response = app(EtsyController::class)->findUserShops($shopId);
            if (!empty($response['results'][0]['shop_id'])) {
                $etsyCredential->etsy_shop_id = $response['results'][0]['shop_id'];
                $etsyCredential->save();
            }

            $this->hook_after_add($shopId);
        } catch (Exception $e) {
            return redirect('admin/shop_setting?id=' . $shop->channel . '&lang=' . $shop->lang)->send();
        }
    }

    private function storeEtsyCredentials($shop)
    {
        $server = new Etsy([
            'identifier' => $shop->username,
            'secret' => $shop->password,
            'scope' => self::ETSY_SCOPE,
            'callback_uri' => route('etsy.callback', [$shop->id])
        ]);

        // Retrieve temporary credentials
        $temporaryCredentials = $server->getTemporaryCredentials();

        $etsyCredentials = EtsyCredential::where('shop_id', $shop->id)->firstOrNew();
        $etsyCredentials->shop_id = $shop->id;
        $etsyCredentials->consumer_key = $shop->username;
        $etsyCredentials->consumer_secret = $shop->password;
        $etsyCredentials->temporary_credentials = serialize($temporaryCredentials);
        $etsyCredentials->status = 0;
        $etsyCredentials->save();

        return $server->getAuthorizationUrl($temporaryCredentials);
    }

    public function storeOttoShop(LaravelRequest $request)
    {
        $url = 'https://api.otto.market/oauth2/token';

        $client_id = config('channel.otto_client_id');
        $client_secret = config('channel.otto_client_secret');

        $code = $request->code;

        $data = http_build_query([
            'grant_type' => 'authorization_code',
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'code' => $code
        ]);

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        $response_data = json_decode($response, true);
        if(isset($response_data['refresh_token'])){
            $accessToken = $response_data['access_token'];

            $installationId = $this->getOttoInstallationId($accessToken);

            $shop_id = Shop::create(
                [
                    'user_id' => CRUDBooster::myParentId(),
                    'shop_name' => CRUDBooster::myName() . ' ' . 'OTTO Shop',
                    'channel' => Channel::OTTO,
                    'url' => '#',
                    'username' => $installationId,
                    'status' => 1
                ]
            )->id;
            $this->hook_after_add($shop_id);


             $this->hook_after_add($shop_id);
        }else{
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), "Authorization failed!", 'error');
        }
    }


    private function getOttoInstallationId($token)
    {
        $appId = config('channel.otto_app_id');

        $url = 'https://api.otto.market/v1/apps/'.$appId.'/installation';

        $header = [
            'Content-Type: application/x-www-form-urlencoded',
            'Authorization: Bearer '.$token
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);

        $response = curl_exec($ch);
        curl_close($ch);

        $response_data = json_decode($response, true);
        return $response_data['installationId'];
    }

    private function getOttoAppToken()
    {
        $client_id = config('channel.otto_client_id');
        $client_secret = config('channel.otto_client_secret');

        $url = 'https://api.otto.market/oauth2/token';

        $data = http_build_query([
            'grant_type' => 'client_credentials',
            'client_id' => $client_id,
            'client_secret' => $client_secret,
            'scope' => 'developer'
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        $response_data = json_decode($response, true);

        return $response_data['access_token'];
    }
    public function getOttoInstallationAccess($installation_id)
    {
        $appId = config('channel.otto_app_id');
        $appToken = $this->getOttoAppToken();

        $url = 'https://api.otto.market/v1/apps/'.$appId.'/installations/'.$installation_id.'/accessToken';
        $data = http_build_query([
            'scope' => 'orders'
        ]);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded',
            'Authorization: Bearer '.$appToken
        ]);

        $response = curl_exec($ch);
        curl_close($ch);

        $response_data = json_decode($response, true);

        return $response_data['access_token'];
    }

    public function storeEbayShop()
    {
        $user_id = CRUDBooster::myParentId();
        $code = $_GET['code'] ?? null;
        $state = $_GET['state'] ?? null;

        if (!$user_id) {
            CRUDBooster::redirect(CRUDBooster::adminPath('login'), "Please Log In");
        }

        if (!$code) {
            CRUDBooster::redirect(CRUDBooster::mainPath(), "Authorization code missing", "error");
        }

        // Parse state parameter
        $stateData = null;
        $isUpdate = false;
        $isCreate = false;
        $shop_id = null;
        $shopData = null;

        if ($state) {
            try {
                $stateData = json_decode(base64_decode($state), true);
                
                // Validate state data
                if ($stateData && 
                    isset($stateData['user_id'], $stateData['action'], $stateData['timestamp']) &&
                    $stateData['user_id'] == $user_id &&
                    (time() - $stateData['timestamp']) < 3600) { // State valid for 1 hour
                    
                    if ($stateData['action'] === 'update') {
                        $isUpdate = true;
                        $shop_id = $stateData['shop_id'];
                        
                        // Verify shop exists and belongs to user
                        $shopExists = Shop::where([
                            'id' => $shop_id,
                            'user_id' => $user_id,
                            'channel' => Channel::EBAY
                        ])->exists();
                        
                        if (!$shopExists) {
                            CRUDBooster::redirect(CRUDBooster::mainPath(), "Shop not found or access denied", "error");
                        }
                    } elseif ($stateData['action'] === 'create') {
                        $isCreate = true;
                        $shopData = $stateData['shop_data'] ?? null;
                    }
                }
            } catch (\Exception $e) {
                // Invalid state, treat as new shop creation without form data
                $isUpdate = false;
                $isCreate = false;
            }
        }

        $api = new EbayApi();
        $tokens = $api->getTokensByCode($code);
        
        if (!$tokens) {
            CRUDBooster::redirect(CRUDBooster::mainPath(), "Failed to get eBay tokens", "error");
        }

        if ($isUpdate && $shop_id) {
            // Update existing shop
            Shop::where(['id' => $shop_id, 'user_id' => $user_id, 'channel' => Channel::EBAY])->update([
                'username' => $tokens['refresh_token'],
                'password' => $tokens['access_token'],
                'status' => 1,
                'updated_at' => now()
            ]);
            
            CRUDBooster::redirect(CRUDBooster::mainPath(), "eBay shop reconnected successfully!", "success");
        } else {
            // Create new shop
            $shopName = 'Ebay Shop';
            $lang = 'de';
            
            // Use shop data from state if available (from form submission)
            if ($isCreate && $shopData) {
                $shopName = $shopData['shop_name'] ?? $shopName;
                $lang = $shopData['lang'] ?? $lang;
            } else {
                // Fallback for direct OAuth access
                $shopName = CRUDBooster::myName() . ' ' . $shopName;
            }
            
            $shop_id = Shop::create([
                'user_id' => $user_id,
                'shop_name' => $shopName,
                'channel' => Channel::EBAY,
                'url' => '#',
                'username' => $tokens['refresh_token'],
                'password' => $tokens['access_token'],
                'status' => 1,
                'lang' => $lang
            ])->id;

            $this->hook_after_add($shop_id);
        }
    }

    /*  All Shop Validation */
    public static function shopValidation($data, $notify = NULL)
    {
        if($data->channel == Channel::LIMANGO || $data->channel == Channel::TRADEBYTE)
        {
            $user = $data->username;
            $pass = $data->password;
            $auth = 'Basic ' . base64_encode("$user:$pass");

            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://rest.trade-server.net/'.$data->hnr.'/orders/',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => array(
                    'Authorization: '.$auth,
                ),
            ));

            $content = curl_exec($curl);
            $responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            if($responseCode >= 200 && $responseCode < 300) return true;
            $xml = !$content ? simplexml_load_string($content, 'SimpleXMLElement') : null;
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), $xml ? (string)$xml->state : 'Invalid credentials');

        } elseif ($data->channel == Channel::GAMBIO) {
            $api_path = "api.php/v2/";
            $user = $data->username;
            $pass = $data->password;
            $auth = base64_encode("$user:$pass");

            $url = $data->url . $api_path . "shop_information";
            $headers = array("authorization: Basic " . $auth, "content-type: application/json");

            $response = GambioApi::doCulrRequest($url, $headers, '', 'GET');

            $check = (object)json_decode($response, true);
            if ($notify && $check->code === 401) {
                $message_title = 'Gambio Shop Setting ' . $check->message;
                User::find($data->user_id)->notify(new DRMNotification($message_title, 'SHOP_VALIDATION', url('admin/shop_setting?id='.Channel::GAMBIO)));
                Shop::where([
                    'user_id' => $data->user_id,
                    'id' => $data->id
                ])->update([
                    'status' => false,
                    'updated_at' => now()
                ]);
            }

            elseif ($check->code === 401) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Gambio Shop ' . $check->message, 'warning');
            } elseif (empty(json_decode($response))) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), "Gambio Shop This shop doesn't exist yet!", 'warning');
            }

        } elseif ($data->channel == Channel::LENGOW) {
            $response = new LengowApi($data->username, $data->password);
            if ($notify && empty($response->token) && empty($response->account_id)) {
                $message_title = 'Lengow Shop Setting Wrong Access Token or Secret';
                User::find($data->user_id)->notify(new DRMNotification($message_title, 'SHOP_VALIDATION', url('admin/shop_setting?id='.Channel::LENGOW)));

                Shop::where([
                    'user_id' => $data->user_id,
                    'id' => $data->id
                ])->update([
                    'status' => false,
                    'updated_at' => now()
                ]);

            } elseif (empty($response->token) && empty($response->account_id)) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Wrong Access Token or Secret'), 'warning');
            }
        } elseif ($data->channel == Channel::YATEGO) {
            $order_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $data->username . "&passwd=" . $data->password;

            $response = Http::get($order_url);

            if ($notify && empty($response->body()) || $response->body() == "Sie haben keine Berechtigung diese Seite aufzurufen.") {
                // if (!empty($hasPermission)) {
                $message_title = 'Yatego Shop Setting Invalid credential or your yatego shop setting not complete or password must not contain "&"';
                // if (isHookRemainOnSidebar('SHOP_VALIDATION') && isLocal()) {
                //     User::find($data->user_id)->notify(new DRMTelegramNotification($message_title, 'SHOP_VALIDATION', url('admin/shop_setting?id=3')));
                // }else{
                User::find($data->user_id)->notify(new DRMNotification($message_title, 'SHOP_VALIDATION', url('admin/shop_setting?id='.Channel::YATEGO)));
                // }
                // }
                Shop::where([
                    'user_id' => $data->user_id,
                    'id' => $data->id
                ])->update([
                    'status' => false,
                    'updated_at' => now()
                ]);

            } elseif (empty($response->body())) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Invalid credential or your yatego shop setting not complete or password must not contain "&"'), 'warning');
            } elseif (!empty($response->body()) && $response->body() == "Sie haben keine Berechtigung diese Seite aufzurufen.") {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Invalid credential or your yatego shop setting not complete or password must not contain "&"'), 'warning');
            }
        } elseif ($data->channel == Channel::EBAY) {
            return true;
        } elseif ($data->channel == Channel::AMAZON && false) {
            $client = new \App\Services\Amazon\AZClient([
                'Marketplace_Id' => 'A1PA6795UKMFR9',
                'Seller_Id' => $data->username, // Username
                'Access_Key_ID' => '********************',
                'Secret_Access_Key' => 'oEIgkGejJYPU9NeTZs3CVacxqNuML2kx1P14ODnw',
                'MWSAuthToken' => $data->password //Password
            ]);


            if(!$client->validateCredentials()){
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Invalid credentials !'), 'error');
            }
        } elseif ($data->channel == Channel::SHOPIFY) {
            $response = ShopifyApi::shopInfo($data);
            if ($notify && $response['errors']) {
                // $hasPermission = DB::table('notification_trigger')
                //     ->where('hook', 'SHOP_VALIDATION_ERROR')->where('status', 0)->first();
                // if (!empty($hasPermission)) {
                $message_title = 'Shopify Shop Setting ' . $response['errors'];
                // if (isHookRemainOnSidebar('SHOP_VALIDATION_ERROR') && isLocal()) {
                //     User::find($data->user_id)->notify(new DRMTelegramNotification($message_title, 'SHOP_VALIDATION_ERROR', url('admin/shop_setting?id=6')));
                // }else{
                User::find($data->user_id)->notify(new DRMNotification($message_title, 'SHOP_VALIDATION_ERROR', url('admin/shop_setting?id='.Channel::SHOPIFY)));
                // }
                // }
                Shop::where([
                    'user_id' => $data->user_id,
                    'id' => $data->id
                ])->update([
                    'status' => false,
                    'updated_at' => now()
                ]);

            } elseif ($response['errors']) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), $response['errors'], 'warning');
            }
            else{
                $checkPost = ShopifyApi::checkPost($data);
                if(!$checkPost){
                    CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('shopify_post_not_allowed_error'));
                }
            }
        } elseif ($data->channel == Channel::WOOCOMMERCE) {
            return true;
        } elseif ($data->channel == Channel::OTTO) { // otto shop
            $response = self::validateOttoShop($data);

            if ($response['success'] != true) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), $response['message'], );
            }
        } elseif ($data->channel == Channel::DROPTIENDA) { //Droptienda channel varify
            $response = self::validateDroptiendaShop($data);
            if ($response['success'] != true) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), $response['message'], );
            }
        } elseif ($data->channel == Channel::CHECK24) { //Check24 channel varify
            $response = self::validateCheck24Shop($data);
            if ($response['success'] != true) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), $response['message'], );
            }
        } elseif ($data->channel == 15) { //Decthlon channel varify
            $response = self::validateDecathlonShop($data);
            if ($response['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $response['message'], 'warning');
            }
        }elseif ($data->channel == Channel::SPRINTER) { //Sprinter channel varify
            $response = new SprinterShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }

        }elseif ($data->channel == Channel::TIENDANIMAL) { //Tiendanimal channel varify
            $response = new TiendanimalShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        }elseif ($data->channel == Channel::MEDIAMARKT) { //Meadia Markt channel varify
            $response = new MediamarktShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        }elseif ($data->channel == Channel::COLIZEY) { // Colizey channel varify

            $response = new ColizeyValidation();
            $result = $response->validateThisShop($data);

            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        }elseif ($data->channel == Channel::CONRAD) { //Conrad channel varify
            $response = new ConradShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        }elseif ($data->channel == Channel::FRESSNAPF) { //Fressnapf channel varify
            $response = new FressnapfShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::VOLKNER) { // Volkner channel varify
            $response = new VolknerShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::MANOR) { // Manor channel varify
            $response = new ManorShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::XXXLUTZ) { // XXXLutz channel varify
            $response = new XxxlutzShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::PERFUMES_CLUB) { // Perfume's Club channel varify
            $response = new PerfumesClubShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::HOME24) { // Home24 channel varify
            $response = new Home24Shop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::ALLTRICKS) { // Alltricks channel varify
            $response = new AlltricksShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        }

        elseif ($data->channel == Channel::GALAXUS) {
            $result = self::validateGalaxusShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        }
        elseif ($data->channel == Channel::CLUBE_FASHION) { // Clube Fashion channel varify
            $response = new ClubeFashionShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        }
        elseif ($data->channel == Channel::ZOOPLUS) { // Zooplus channel varify
            $response = new ZooplusShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        }
        elseif ($data->channel == Channel::PSS) { // Pss channel varify
            $response = new PssShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::BIGBANG) { // Bigbang channel varify
            $response = new BigbangShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::BRICODEPOT) { // Bricodepot channel varify
            $response = new BricodepotShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::HORNBACH) { // Hornbach channel varify
            $response = new HornbachShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::PLANETAHUERTO) { // Planetahuerto channel varify
            $response = new PlanetahuertoShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::CARREFOUR) { // Carrefour channel varify
            $response = new CarrefourShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        } elseif ($data->channel == Channel::MIRAKL_CONNECT) { // Mirakl Connect channel validation
            $response = new MiraklConnectShop();
            $result = $response->validateThisShop($data);
            if ($result['success'] != true) {
                return CRUDBooster::redirect(Request::server('HTTP_REFERER'), $result['message'], 'warning');
            }
        }

        return true;
    }


    //By the way, you can still create your own method in here... :)


    public function ApproveURL($id)
    {
        $urls = Shop::get();
        foreach ($urls as $key => $value) {
            Shop::update(['status' => "0"]);
        }
        foreach ($id as $ids) {
            Shop::where('id', $ids)->update(['status' => "1"]);
        }
    }

    public $stat = [];

    //Add user data - ColuSale
    public function postAddColuSale()
    {
        //DB::beginTransaction();
        try {

            $lang = request()->lang ?? 'de';
            $user_id = CRUDBooster::myId();
            Shop::updateOrInsert([
                'user_id' => $user_id,
                'channel' => 8,
                'lang' => $lang
            ], [
                'shop_name' => 'ColuSale',
                'username' => 'ColuSale',
                'url' => '#'
            ]);

            $shop = Shop::where([
                'user_id' => $user_id,
                'channel' => 8,
                'lang' => $lang
            ])->select('id')->first();
            if (is_null($shop)) throw new \Exception('ColuSale added failed!');

            $update_color = [
                'gambio_id' => $shop->id,
                'color' => "#ffffff"
            ];

            ChannelColor::updateOrCreate(
                $update_color
            );
            //DB::commit();

            //Clear account activity step
            \App\Services\CheckListProgress\Checklist::cache_key_clear(5, $user_id);

            return response()->json([
                'success' => true,
                'message' => 'ColuSale added sussessfully!'
            ]);
        } catch (\Exception $e) {
            //DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'ColuSale added failed!',
                'error' => $e->getMessage()
            ]);
        }
    }

    public function getAdd()
    {
        $this->cbLoader();
        if (!CRUDBooster::isCreate() && $this->global_privilege == false) {
            CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
        }

//        if($_GET['id'] == Channel::BLANK){
//            $user_id = CRUDBooster::myParentId();
//            $shop = $this->addBlankShop($user_id);
//            $this->hook_after_add($shop->id);
//        }

        $page_title = trans("crudbooster.add_data_page_title", ['module' => CRUDBooster::getCurrentModule()->name]);
        $page_menu = Route::getCurrentRoute()->getActionName();
        $command = 'add';

        return view('crudbooster::default.form', compact('page_title', 'page_menu', 'command'));
    }

    private function addBlankShop($user_id)
    {
        $countryId = app(UserService::class)->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($countryId);

        return Shop::updateOrCreate([
            'user_id' => $user_id,
            'channel' => Channel::BLANK,
            'lang' => $lang,
            'country_id' => $countryId
        ], [
            'shop_name' => 'Blank',
            'username' => 'blank',
            'url' => '#'
        ]);
    }


    public function getSyncShopProduct($id)
    {
        $shop = Shop::where('id', $id)->first();

        if ($shop->channel == 1) {

            self::getSyncGambioProduct($shop);
        } else if ($shop->channel == 2 || $shop->channel == 3) {

            CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'This Shop Product Sync Not Possible', 'warning');
        } else if ($shop->channel == 4) {

            self::getSyncEbayProduct($shop);
        } else if ($shop->channel == 6) {

            self::getSyncShopifyProduct($shop);
        }

    }

    public function getSyncGambioProduct($shop, $page = 1)
    {
        if ($shop) {
            $user = $shop->username;
            $pass = $shop->password;
            $shopId = $shop->id;
            $user_id = $shop->user_id;
            $base_url = $shop->url;
            $api_path = "api.php/v2/";
        } else {
            dd("You have not configured any shop yet");
        }
        if ($page == 1) {
            DB::table('gambio_products')->where('shop_id', $shop->id)->delete();
        }
        $auth = base64_encode("$user:$pass");

        $per_page = 2500;
        $url = $base_url . $api_path . "products/?page=$page&per_page=$per_page";

        $headers = array("Authorization: Basic " . $auth);
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");

        $response = curl_exec($ch);
        $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $errNo = curl_errno($ch);
        $errStr = curl_error($ch);
        curl_close($ch);

        $allProducts = json_decode($response);
        //dd($allProducts);
        //dd(count((array)$allProducts));

        $this->bulkProductInsertGambio($allProducts, $shop);

        if (count((array)$allProducts) != 0 && $responseCode != 401) {
            $page++;
            $this->getSyncGambioProduct($shop, $page);
        }

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), "All gambio shop product has been synced !", "success");

    }


    public function bulkProductInsertGambio($allProducts, $shop)
    {
        $shopId = $shop->id;
        $user_id = $shop->user_id;

        if (count((array)$allProducts) > 0) {
            $info_collection = [];

            foreach ($allProducts as $key => $value) {

                $info_collection[] = array(
                    'user_id' => $user_id,
                    'shop_id' => $shopId,
                    'product_id' => $value->id,
                    'product_model' => $value->productModel,
                    'ean' => $value->ean,
                );

            }

            DB::table('gambio_products')->insert($info_collection);

        }

    }


    public function getSyncEbayProduct($shop)
    {
        $ebay_service = new EbayServices();
        $service = $ebay_service->createTrading();

        $request = new Types\GetMyeBaySellingRequestType();

        $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
        $request->RequesterCredentials->eBayAuthToken = $shop->password;

        $request->ActiveList = new Types\ItemListCustomizationType();
        $request->ActiveList->Include = true;
        $request->ActiveList->Pagination = new Types\PaginationType();
        $request->ActiveList->Pagination->EntriesPerPage = 10;
        $request->ActiveList->Sort = Enums\ItemSortTypeCodeType::C_CURRENT_PRICE_DESCENDING;
        $pageNum = 1;

        do {
            $request->ActiveList->Pagination->PageNumber = $pageNum;

            $response = $service->getMyeBaySelling($request);
            if (isset($response->Errors)) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Connection Problem Or Item Not Found', 'warning');
            }

            if ($response->Ack !== 'Failure' && isset($response->ActiveList)) {
                foreach ($response->ActiveList->ItemArray->Item as $item) {
                    $ebay_service = new EbayServices();
                    $service = $ebay_service->createShopping();
                    $request = new \DTS\eBaySDK\Shopping\Types\GetSingleItemRequestType();
                    $request->ItemID = $item->ItemID;
                    $request->IncludeSelector = 'ItemSpecifics';
                    $responsea = $service->getSingleItem($request);
                    $lista = $responsea->Item->ItemSpecifics->NameValueList;
                    foreach ($lista as $value) {
                        if ($value->Name == 'EAN') {

                            DB::table('ebay_products')->updateOrInsert([
                                'ean' => $value->Value[0],
                                'shop_id' => $shop->id,
                            ], [
                                'user_id' => $shop->user_id,
                                'shop_id' => $shop->id,
                                'item_id' => $item->ItemID,
                                'ean' => $value->Value[0],
                                'created_at' => now()
                            ]);

                        }
                    }
                }
            }

            $pageNum += 1;
        } while (isset($response->ActiveList) && $pageNum <= $response->ActiveList->PaginationResult->TotalNumberOfPages);

        CRUDBooster::redirect(Request::server('HTTP_REFERER'), "All product has been synced !", "success");

    }


    public function getSyncShopifyProduct($shop)
    {
        $url = $shop->url . '/admin/api/2020-04/products.json';
        $client = new \GuzzleHttp\Client();
        $response = $client->request('GET', $url, [
            'auth' => [$shop->username, $shop->password]
        ]);

        if ($response->getStatusCode() !== 200) {
            CRUDBooster::redirect(Request::server('HTTP_REFERER'), 'Connection Problem Or Data Not Found', 'warning');
        }

        $data = $response->getBody()->getContents();

        $all_orders = (json_decode($data));
        foreach ($all_orders->products as $value) {
            if (!empty($value->variants[0]->barcode)) {
                DB::table('shopify_products')->updateOrInsert([
                    'ean' => $value->variants[0]->barcode,
                    'shop_id' => $shop->id,
                ], [
                    'user_id' => $shop->user_id,
                    'shop_id' => $shop->id,
                    'product_id' => $value->id,
                    'ean' => $value->variants[0]->barcode,
                    'created_at' => now()
                ]);
            }

        }
        CRUDBooster::redirect(Request::server('HTTP_REFERER'), "All product has been synced !", "success");
    }

    public function getSyncCategory($id)
    {
        $shop = Shop::where(['id' => $id, 'channel' => 1])->first();
        $Categoriescontroller = new AdminCategoriesController();
        $Categoriescontroller->getGambioCategory($shop);
    }


    public function syncGambioorder($shop, $page = 1, &$count = 0)
    {
        try {
            if ($shop) {
                $user = $shop->username;
                $pass = $shop->password;
                $shopId = $shop->id;
                $base_url = $shop->url;
                $api_path = "api.php/v2/";
            } else {
                echo "You have not configured any shop yet!";
                throw new \Exception("You have not configured any shop yet!");
            }

            $auth = base64_encode("$user:$pass");

            $per_page = 50;
            $url = $base_url . $api_path . "orders/?page=$page&per_page=$per_page";

            $headers = array("Authorization: Basic " . $auth);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");

            $response = curl_exec($ch);
            $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $errNo = curl_errno($ch);
            $errStr = curl_error($ch);
            curl_close($ch);
            // dd($responseCode,$errNo,$errStr);
            echo "Shop id : $shop->id code: $responseCode No: $errNo $errStr \n";

            if ($responseCode != 200) {
                throw new \Exception("Shop connection problem!");
            }

            $allOrder = json_decode($response);
            $cartsInfo = [];
            // dd($allOrder);

            foreach ((array)$allOrder as $indexv => $value) {
                $url = $base_url . $api_path . "orders/$value->id/items?page=$page&per_page=$per_page";

                $headers = array("Authorization: Basic " . $auth);

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
                curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");


                $response = curl_exec($ch);
                $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $errNo = curl_errno($ch);
                $errStr = curl_error($ch);
                curl_close($ch);

                $cartsInfo[] = json_decode($response);
            }

            $this->bulkOrderInsertGambio($allOrder, $shop, $cartsInfo);

            if (count((array)$allOrder) != 0) {
                $page++;
                $count += count((array)$allOrder);
                $this->syncGambioorder($shop, $page, $count);
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
                [
                    'status' => '1',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count
                ]);

        } catch (\Exception $e) {
            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage()), 'error');
            }
            if ($shop) {
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                    [
                        'status' => '2',
                        'end_time' => Carbon::now()->unix(),
                        'item' => $count,
                        'report' => 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage(),
                    ]);
                echo 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage() . "\n";
                $shop_user = User::find($shop->user_id);
                if ($shop_user) {
                    // $hasPermission = DB::table('notification_trigger')
                    // ->where('hook', 'SHOP_VALIDATION_ERROR')->where('status', 0)->first();
                    // if (!empty($hasPermission)) {
                    $message_title = 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage();
                    // if (isHookRemainOnSidebar('SHOP_VALIDATION_ERROR') && isLocal()) {
                    //     $shop_user->notify(new DRMTelegramNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }else{
                    $shop_user->notify(new DRMNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }
                    // }
                }
            } else {
                echo $e->getMessage() . "\n";
            }
        }
    }

    public function bulkOrderInsertGambio($allOrder, $shop, $cartsInfo)
    {
        echo "here :" . date('H:m:s') . "\n";
        // dd($allOrder,$cartsInfo);

        $shopId = $shop->id;
        $shop_name = $shop->shop_name;
        $user_id = CRUDBooster::myId();

        $sync_date = (DB::table('drm_order_sync')->where([
            'shop_id' => $shop->id,
            'drm_user_id' => $shop->user_id
        ])->first())->last_date;

        $l_date = $sync_date;
        if (count((array)$allOrder) > 0) {
            // dd($allOrder);

            foreach ($allOrder as $key => $value) {
                $customer_id = null;
                $order_info = [];

                $new = new DateTime($value->purchaseDate);
                $old = new DateTime($sync_date);

                if ($sync_date != null) {
                    if ($old >= $new) {
                        continue;
                    }
                }
                $l_date = $value->purchaseDate;

                $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->id)->first();
                if ($exist_shop_id) continue;

                // ---------- customer insert -------------

                list($total_sum, $currency) = explode(" ", $value->totalSum);

                $country = $value->deliveryAddress->country ?? $value->billingAddress->country ?? $value->deliveryAddress->countryIsoCode ?? $value->billingAddress->countryIsoCode;

                $customer_info = $order_info = null;

                $customer_info = [
                    "customer_full_name" => $value->customerName ?? $value->deliveryAddress->firstName . ' ' . $value->deliveryAddress->lastName,
                    "company_name" => $value->deliveryAddress->company ?? $value->billingAddress->company,
                    "currency" => $currency,
                    'email' => $value->customerEmail,
                    'address' => $value->deliveryAddress->additionalAddressInfo ?? $value->billingAddress->additionalAddressInfo,
                    'country' => $country,
                    'default_language' => $value->deliveryAddress->countryIsoCode ?? $value->billingAddress->countryIsoCode,
                    'zip_code' => $value->deliveryAddress->postcode ?? $value->billingAddress->postcode,
                    'state' => $value->deliveryAddress->state ?? $value->billingAddress->state,
                    'insert_type' => 'API',

                    //shipping
                    'street_shipping' => $value->deliveryAddress->street . ' ' . $value->deliveryAddress->houseNumber,
                    'city_shipping' => $value->deliveryAddress->city,
                    'state_shipping' => $value->deliveryAddress->state,
                    'zipcode_shipping' => $value->deliveryAddress->postcode,
                    'country_shipping' => $value->deliveryAddress->country ?? $value->billingAddress->countryIsoCode,

                    //billing
                    'street_billing' => $value->billingAddress->street . ' ' . $value->billingAddress->houseNumber,
                    'city_billing' => $value->billingAddress->city,
                    'state_billing' => $value->billingAddress->state,
                    'zipcode_billing' => $value->billingAddress->postcode,
                    'country_billing' => $value->billingAddress->country ?? $value->billingAddress->countryIsoCode,

                    'user_id' => $shop->user_id,
                ];

                $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
                if ($exist_customer_id) continue;

                $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);


                $order_info['drm_customer_id'] = $customer_id;
                $order_info['user_id'] = $shop->user_id;

                // $date=date_create("2013-03-15");
                $order_info['order_date'] = $value->purchaseDate;
                $order_info['insert_type'] = "API";
                $order_info['total'] = $total_sum;
                $order_info['shop_id'] = $shop->id;
                $order_info['order_id_api'] = $value->id;
                // $order_info['shipping'] =

                $order_info['sub_total'] = $total_sum;
                $order_info['discount'] = 0;
                $order_info['discount_type'] = "fixed";
                $order_info['adjustment'] = 0;
                $order_info['payment_type'] = $value->shippingType->module;
                $order_info['currency'] = $currency;

                $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>' . $customer_info['company_name'] . '<br>' . $customer_info['address'] . '<br>' . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>' . $customer_info['state'] . '<br>' . $customer_info['country'];


                $bill['b_street'] = $customer_info['street_billing'] . '<br>';
                $bill['b_postcode'] = $customer_info['city_billing'];
                $bill['b_state'] = $customer_info['state_billing'] . '<br>';
                $bill['b_city'] = $customer_info['zipcode_billing'] . '<br>';
                $bill['b_country'] = $customer_info['country_billing'];

                $order_info['billing'] = $bill['b_street'] . $bill['b_state'] . $bill['b_postcode'] . ' ' . $bill['b_city'] . $bill['b_country'];

                $ship['d_street'] = $customer_info['street_shipping'] . '<br>';
                $ship['d_state'] = $customer_info['city_shipping'] . '<br>';
                $ship['d_postcode'] = $customer_info['zipcode_shipping'];
                $ship['d_city'] = $customer_info['city_shipping'] . '<br>';
                $ship['d_country'] = $customer_info['country_shipping'];

                $order_info['shipping'] = $ship['d_street'] . $ship['d_state'] . $ship['d_postcode'] . ' ' . $ship['d_city'] . $ship['d_country'];

                $order_info['status'] = $value->statusName;
                $order_info['cart'] = '[{"cart":' . json_encode($cartsInfo[$key]) . "}]";

                foreach ((array)$cartsInfo[$key] as $item) {

                    $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->name);
                    $order_info['description'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->model);
                    $order_info['qty'][] = $item->quantity;
                    $order_info['rate'][] = $item->price;
                    $order_info['unit'][] = $item->quantityUnitName;
                    $order_info['tax'][] = $item->tax;
                    $order_info['product_discount'][] = $item->discount ?? 0;
                    $order_info['amount'][] = $item->finalPrice;
                }

                // ----------------------- Order Insert ---------------------
                app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);
            }/* foreach end */

            DB::table('drm_order_sync')->updateOrInsert([
                'shop_id' => $shop->id,
                'drm_user_id' => $shop->user_id
            ], [
                'last_date' => $l_date
            ]);

            // dd($od);
        }
    }

    public function syncLengowOrder($shop, $page = 1, &$count = 0)
    {
        try {
            if (!$shop) {
                echo "You have not configured any Lengow shop yet!";
                throw new \Exception("You have not configured any Lengow shop yet!");
            }

            print_r("1. Shop id: $shop->id " . "\n");

            $access_token = $shop->username;
            $secret = $shop->password;
            $lengow = new LengowApi($access_token, $secret);

            if ($lengow->token == "") {
                throw new \Exception("$shop->shop_name shop token problem!");
            }

            $all = $lengow->getOrder($page);

            if ($all) {
                $allorder = $all->results;

                print_r("Shop id: $shop->id " . "\n");
                $this->bulkOrderInsertLengow($allorder, $shop);

                if ($all->next) {
                    $page++;
                    $count += count($allorder);
                    $this->syncLengowOrder($shop, $page, $count);
                }
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
                [
                    'status' => '1',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count
                ]);

        } catch (\Exception $e) {
            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage()), 'error');
            }
            if ($shop) {
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                    [
                        'status' => '2',
                        'end_time' => Carbon::now()->unix(),
                        'item' => $count,
                        'report' => 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage(),
                    ]);
                echo 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage() . "\n";
                $shop_user = User::find($shop->user_id);
                if ($shop_user) {
                    // $hasPermission = DB::table('notification_trigger')
                    // ->where('hook', 'SHOP_VALIDATION_ERROR')->where('status', 0)->first();
                    // if (!empty($hasPermission)) {
                    $message_title = 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage();
                    // if (isHookRemainOnSidebar('SHOP_VALIDATION_ERROR') && isLocal()) {
                    //     $shop_user->notify(new DRMTelegramNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }else{
                    $shop_user->notify(new DRMNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }
                    // }
                }
            } else {
                echo $e->getMessage() . "\n";
            }
        }
    }

    public function bulkOrderInsertLengow($allorder, $shop)
    {

        print_r("Shop id: $shop->id Count: " . count($allorder) . "\n");
        // dd($allorder[0],$allorder[1]);
        // dd($shop);

        $shopId = $shop->id;
        $shop_name = $shop->shop_name;
        $user_id = $shop->user_id;
        $base_url = $shop->url;

        $sync_date = (DB::table('drm_order_sync')->where([
            'shop_id' => $shop->id,
            'drm_user_id' => $shop->user_id
        ])->first())->last_date;

        // $sync_count = 0;


        $l_date = $sync_date;
        foreach ((object)$allorder as $key => $value) {

            // $this->stat['order'] ++;

            $name = $value->billing_address->full_name . $value->billing_address->first_name . $value->billing_address->last_name . $value->packages[0]->delivery->city;

            if (strtolower(substr($name, 0, 3)) == 'xxx') {
                // $this->stat['details'][] =$name;

                continue;
            }

            $new = new DateTime($value->marketplace_order_date);
            $old = new DateTime($sync_date);

            if ($sync_date != null) {
                if ($old >= $new) {
                    continue;
                }
            }

            $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->marketplace_order_id)->first();
            if ($exist_shop_id) continue;


            $l_date = $value->marketplace_order_date;

            $customer_info = $order_info = null;

            $customer_info = [
                'customer_full_name' => $value->billing_address->full_name ?? $value->billing_address->first_name . " " . $value->billing_address->last_name,
                'company_name' => $value->packages[0]->delivery->company ?? $value->billing_address->company,
                'email' => $value->billing_address->email,
                'city' => $value->packages[0]->delivery->city ?? $value->billing_address->city,
                'zip_code' => $value->packages[0]->delivery->zipcode ?? $value->billing_address->zipcode,
                'state' => $value->packages[0]->delivery->state_region ?? $value->billing_address->state_region,
                'country' => $value->packages[0]->delivery->common_country_iso_a2 ?? $value->billing_address->common_country_iso_a2,
                'phone' => $value->packages[0]->delivery->phone_mobile ?? $value->billing_address->phone_mobile,
                // 'website' => ,
                'currency' => $value->original_currency->iso_a3,
                //  'default_language' => ,
                'address' => $value->contact_address ?? $value->billing_address->full_address ?? $value->packages[0]->delivery->full_address,
                'insert_type' => "API",
                'user_id' => $shop->user_id,
                //  'vat_number' => ,

                // shipping
                'street_shipping' => $value->packages[0]->delivery->first_line,
                'city_shipping' => $value->packages[0]->delivery->city,
                'state_shipping' => $value->packages[0]->delivery->state_region,
                'zipcode_shipping' => $value->packages[0]->delivery->zipcode,
                'country_shipping' => $value->packages[0]->delivery->common_country_iso_a2,

                //billing
                'street_billing' => $value->billing_address->first_line,
                'city_billing' => $value->billing_address->city,
                'state_billing' => $value->billing_address->state_region,
                'zipcode_billing' => $value->billing_address->zipcode,
                'country_billing' => $value->billing_address->common_country_iso_a2,

            ];

            if (isset($value->billing_address)) {
                $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
                if ($exist_customer_id) continue;
                // insert customer
                $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);
            }
            // dd($customer_id);


            // ----------------------- order ----------------------------

            $order_info['user_id'] = $shop->user_id;
            $order_info['drm_customer_id'] = $customer_id;
            // $date=date_create("2013-03-15");
            $order_info['order_date'] = $value->marketplace_order_date;
            $order_info['insert_type'] = "API";
            $order_info['total'] = $value->total_order;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $value->marketplace_order_id;

            $order_info['sub_total'] = $value->original_total_order;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $value->payments[0]->type;
            $order_info['currency'] = $value->original_currency->iso_a3;
            $order_info['shipping_cost'] = $value->shipping;

            $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>' . $customer_info['company_name'] . '<br>' . $customer_info['address'] . '<br>' . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>' . $customer_info['state'] . '<br>' . $customer_info['country'];


            $bill['b_street'] = $value->billing_address->first_line . '<br>';
            // $bill['b_house_number'] = "N/A";
            $bill['b_postcode'] = $value->billing_address->zipcode;
            $bill['b_state'] = $value->billing_address->state_region . '<br>';
            $bill['b_city'] = $value->billing_address->city . '<br>';
            $bill['b_country'] = $value->billing_address->common_country_iso_a2;

            $order_info['billing'] = $bill['b_street'] . $bill['b_state'] . $bill['b_postcode'] . ' ' . $bill['b_city'] . $bill['b_country'];

            $ship['d_street'] = $value->packages[0]->delivery->first_line . '<br>';
            $ship['d_state'] = $value->packages[0]->delivery->state_region . '<br>';
            $ship['d_postcode'] = $value->packages[0]->delivery->zipcode;
            $ship['d_city'] = $value->packages[0]->delivery->city . '<br>';
            $ship['d_country'] = $value->packages[0]->delivery->common_country_iso_a2;

            $order_info['shipping'] = $ship['d_street'] . $ship['d_state'] . $ship['d_postcode'] . ' ' . $ship['d_city'] . $ship['d_country'];

            // $order_info['client_note'];
            $order_info['status'] = $value->lengow_status;
            $order_info['cart'] = json_encode($value->packages);

            foreach ((array)$value->packages[0]->cart as $j => $item) {
                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->title);
                $order_info['description'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->category);
                $order_info['qty'][] = $item->quantity;
                $order_info['rate'][] = $item->amount;
                $order_info['tax'][] = $item->tax;
                $order_info['image'][] = $item->url_image;
                $order_info['product_discount'][] = $item->discount ?? 0;
                $order_info['amount'][] = $item->amount;
            }


            // ----------------------- Order Insert ---------------------
            app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);
        }/* end foreach */

        DB::table('drm_order_sync')->updateOrInsert([
            'shop_id' => $shop->id,
            'drm_user_id' => $shop->user_id
        ], [
            'last_date' => $l_date
        ]);

        // dd($od);

    }


    function syncYategoOrder($shop)
    {
        $count = 0;
        try {
            if (!$shop) {
                throw new \Exception("You have not configured any Yatego shop yet!");
            }
            echo "at yatego shop id $shop->id\n";


            $order_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_order";
            // $csv = array_map('str_getcsv', file($url));
            // dd(file($url));

            $order_content = @file_get_contents($order_url);
            if (!$order_content) {
                throw new \Exception("Can not access url or no order found!");
            }
            // dd($content);

            $putted_orders = @file_put_contents(storage_path() . "/tempOrder.csv", $order_content);
            if (!$putted_orders) {
                echo "Content can not be putted to file " . storage_path() . "/tempOrder.csv";
                throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrder.csv");
            }
            // dd(realpath('storage/tempOrder.csv'));
            // dd($csv);
            $l_date = null;

            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
            $reader->setInputEncoding('UTF-8');
            $reader->setDelimiter(';');
            $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrder.csv"));

            // dd($spreadsheet->getActiveSheet()->toArray());
            $order_arr = $spreadsheet->getActiveSheet()->toArray();
            $order_columns = $order_arr[0];
            unset($order_arr[0]);
            // dd($order_arr);

            if (count($order_arr)) {

                $order_id_from = $order_arr[1][1];
                $order_id_to = $order_arr[count($order_arr)][1];
                // dd($order_id_from,$order_id_to);

                $order_product_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_products&von=" . $order_id_from . "&bis=" . $order_id_to . "&varids=1";

                $product_content = @file_get_contents($order_product_url);
                // dd($content);

                if (!$product_content) {
                    throw new \Exception('Can not access Product url. Please contact admin. ');
                }

                $putted = @file_put_contents(storage_path() . "/tempOrderProduct.csv", $product_content);
                if (!$putted) {
                    echo "Content can not be putted to file " . storage_path() . "/tempOrderProduct.csv";
                    throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderProduct.csv");
                }

                // dd(realpath('storage/tempOrder.csv'));
                // dd($csv);

                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');
                $reader->setDelimiter(';');
                $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderProduct.csv"));

                // dd($spreadsheet->getActiveSheet()->toArray());
                $product_arr = $spreadsheet->getActiveSheet()->toArray();
                // dd($product_arr);
                $product_columns = $product_arr[0];
                unset($product_arr[0]);
                // dd($product_columns, $product_arr);
                $product_arr_new = [];
                foreach ($product_arr as $item) {
                    $product = @array_combine($product_columns, $item);

                    if (!$product) {
                        throw new \Exception('Error in product details. Please contact admin.');
                    }

                    $product_arr_new[] = $product;
                }

                // $product_arr_new[] = $product;

                $product_collection = collect($product_arr_new);

                // dd($product_collection);

                foreach ($order_arr as $item) {

                    $order = @array_combine($order_columns, $item);
                    // $order_arr = @array_combine($columns,$product_arr[2]);

                    if ($order) {
                        // dd();
                        $products = $product_collection->where('Bestellnummer', $order['Bestellnummer'])->toArray();

                        if (count($products)) {
                            $l_date = $this->orderInsertYatego($shop, $order, $products);
                            $count += count($products);
                        }

                    } else {
                        throw new \Exception('Shop Setting Changed. Please contact admin.');
                    }
                }

            } else {
                if (CRUDBooster::myId()) {
                    CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('No order found. '), 'error');
                }
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
                [
                    'status' => '1',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count
                ]);

        } catch (\Exception $e) {
            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage()), 'error');
            }
            if ($shop) {
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                    [
                        'status' => '2',
                        'end_time' => Carbon::now()->unix(),
                        'item' => $count,
                        'report' => 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage(),
                    ]);
                echo 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage() . "\n";
                $shop_user = User::find($shop->user_id);
                if ($shop_user) {
                    // $hasPermission = DB::table('notification_trigger')
                    // ->where('hook', 'SHOP_VALIDATION_ERROR')->where('status', 0)->first();
                    // if (!empty($hasPermission)) {
                    $message_title = 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage();
                    // if (isHookRemainOnSidebar('SHOP_VALIDATION_ERROR') && isLocal()) {
                    //     $shop_user->notify(new DRMTelegramNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }else{
                    $shop_user->notify(new DRMNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }
                    // }
                }
            } else {
                echo $e->getMessage() . "\n";
            }
        }
        //  dd($shop);

    }

    public function orderInsertYatego($shop, $order, $products)
    {
        echo "inserting at yatego\n";

        $sync_date = (DB::table('drm_order_sync')->where([
            'shop_id' => $shop->id,
            'drm_user_id' => CRUDBooster::myId() ?? $shop->user_id
        ])->first())->last_date;

        // customer
        $new = new DateTime($order['Bestelldatum']);
        $old = new DateTime($sync_date);

        if ($sync_date != null && $old >= $new) {
            return;
        }

        $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $order['Order_ID'])->first();
        if ($exist_shop_id) return;

        $l_date = $order['Bestelldatum'];

        $customer_info = $order_info = null;

        $customer_info = [
            'customer_full_name' => $order["R_Vorname"] . " " . $order["R_Nachname"],
            'company_name' => $order["R_Firma"],
            'email' => $order["E-Mail-Adresse"],
            'city' => $order["R_Stadt"],
            'zip_code' => $order["R_PLZ"],
            // 'state' =>  $order["R_Stadt"] ,
            'country' => $order["R_Land"],
            'phone' => $order["R_Telefon"],
            // 'website' => ,
            // 'currency' => ,
            //  'default_language' => ,
            'insert_type' => "API",
            'user_id' => $shop->user_id,
            //  'vat_number' => ,

            // shipping
            'street_shipping' => $order["L_Strasse"],
            'city_shipping' => $order["L_Stadt"],
            // 'state_shipping' => ,
            'zipcode_shipping' => $order["L_PLZ"],
            'country_shipping' => $order["L_Land"],

            //billing
            'street_billing' => $order["R_Strasse"],
            'city_billing' => $order["R_Stadt"],
            // 'state_billing' =>  ,
            'zipcode_billing' => $order["R_PLZ"],
            'country_billing' => $order["R_Land"],

        ];

        $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
        if ($exist_customer_id) return;
        $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);
        // -----------------------

        // order
        $order_info['drm_customer_id'] = $customer_id;
        $order_info['user_id'] = $shop->user_id;
        // $date=date_create("2013-03-15");
        $order_info['order_date'] = $order["Bestelldatum"];
        $order_info['insert_type'] = "API";
        $order_info['total'] = $order['Gesamtumsatz'];
        $order_info['shop_id'] = $shop->id;
        $order_info['order_id_api'] = $order['Order_ID'];

        $order_info['sub_total'] = $order['Gesamtumsatz'];
        $order_info['discount'] = $order['Bestellwertrabatt'];
        $order_info['discount_type'] = "fixed";
        $order_info['adjustment'] = 0;
        $order_info['payment_type'] = $order['Zahlart'];
        // $order_info['currency'] = $order['zzzz'];

        $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>'
            . $customer_info['company_name'] . '<br>'
            . $customer_info['address'] . '<br>'
            . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>'
            . $customer_info['state'] . '<br>'
            . $customer_info['country'];

        $order_info['billing'] = $customer_info['street_billing'] . '<br>'
            . $customer_info['zipcode_billing'] . ' ' . $customer_info['city_billing'] . '<br>'
            . $customer_info['country_billing'];

        $order_info['shipping'] = $customer_info['street_shipping'] . '<br>'
            . $customer_info['zipcode_shipping'] . ' ' . $customer_info['city_billing'] . '<br>'
            . $customer_info['country_shipping'];

        // $order_info['client_note'];
        // $order_info['status'] =$order['zzzz'];

        // dd($customer_info,$order_info);

        foreach ((object)$products as $item) {

            // dd($item);
            $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item['Produktname']);
            $order_info['description'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item['Artikelnummer']);
            $order_info['qty'][] = $item['Anzahl'];
            $order_info['rate'][] = $item['Einzelpreis'];
            $order_info['tax'][] = $item['Steuer'];
            $order_info['product_discount'][] = $item['Mengenrabatt'] ?? 0;
            $order_info['amount'][] = $item['Gesamtpreis'];
        }

        // order add
        app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);

        if ($l_date) {
            DB::table('drm_order_sync')->updateOrInsert([
                'shop_id' => $shop->id,
                'drm_user_id' => CRUDBooster::myId() ?? $shop->user_id
            ], [
                'last_date' => $l_date
            ]);
        }
    }


    /* Ebay Orders  */

    public function syncEbayOrder($shop)
    {
        $count = 0;
        try {
            if ($shop) {
                $ebay_service = new EbayServices();
                $service = $ebay_service->createTrading();
                $request = new Types\GetOrdersRequestType();
                $request->RequesterCredentials = new Types\CustomSecurityHeaderType();
                $request->RequesterCredentials->eBayAuthToken = $shop->password;
                $request->CreateTimeFrom = \DateTime::createFromFormat('Y-m-d H:i:s', '2019-02-01 18:33:00');
                $request->CreateTimeTo = \DateTime::createFromFormat('Y-m-d H:i:s', now());
                $request->OrderStatus = 'All';
                $response = $service->getOrders($request);

                if (json_decode($response, true)['Ack'] == 'Success') {
                    $response = (object)(json_decode($response, true)['OrderArray']);
                    $all_orders = $response->Order;

                    if ($all_orders) {
                        $count += count($all_orders);
                        $this->bulkOrderInsertEbay($shop, $all_orders);
                    } else {
                        if (CRUDBooster::myId()) {
                            CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Order Empty!'), 'error');
                        }
                    }
                } else {
                    throw new \Exception('Authentication error!');
                }
            } else {
                throw new \Exception('You have not configured any Ebay shop!');
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
                [
                    'status' => '1',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count
                ]);

        } catch (\Exception $e) {
            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage()), 'error');
            }
            if ($shop) {
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                    [
                        'status' => '2',
                        'end_time' => Carbon::now()->unix(),
                        'item' => $count,
                        'report' => 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage()
                    ]);
                echo 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage() . "\n";
                $shop_user = User::find($shop->user_id);
                if ($shop_user) {
                    // $hasPermission = DB::table('notification_trigger')
                    // ->where('hook', 'SHOP_VALIDATION_ERROR')->where('status', 0)->first();
                    // if (!empty($hasPermission)) {
                    $message_title = 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage();
                    // if (isHookRemainOnSidebar('SHOP_VALIDATION_ERROR') && isLocal()) {
                    //     $shop_user->notify(new DRMTelegramNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }else{
                    $shop_user->notify(new DRMNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }
                    // }
                }
            } else {
                echo $e->getMessage() . "\n";
            }
        }

    }

    public function bulkOrderInsertEbay($shop, $all_orders)
    {

        $last_sync_date = (DB::table('drm_order_sync')->where(['shop_id' => $shop->id, 'drm_user_id' => $shop->user_id])->first())->last_date;

        $last_date = $last_sync_date;

        foreach ($all_orders as $order) {
            $new = new DateTime($order['CreatedTime']);
            $old = new DateTime($last_sync_date);

            if ($last_sync_date != null && $old >= $new) {
                continue;
            }

            $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $order['OrderID'])->first();
            if ($exist_shop_id) continue;

            $customer_info = $order_info = null;

            $customer_info = [
                'customer_full_name' => $order['TransactionArray']['Transaction'][0]['Buyer']['UserFirstName'] . ' ' . $order['TransactionArray']['Transaction'][0]['Buyer']['UserLastName'],
                'company_name' => '',
                'email' => $order['TransactionArray']['Transaction'][0]['Buyer']['Email'],
                'city' => $order['ShippingAddress']['CityName'],
                'zip_code' => $order['ShippingAddress']['PostalCode'],
                'state' => $order['ShippingAddress']['StateOrProvince'],
                'country' => $order['ShippingAddress']['CountryName'],
                'phone' => $order['ShippingAddress']['Phone'],

                'currency' => $order['AmountPaid']['currencyID'],
                'address' => '',
                'insert_type' => "API",
                'user_id' => $shop->user_id,

                // shipping
                'street_shipping' => $order['ShippingAddress']['Street1'] ?? $order['ShippingAddress']['Street2'],
                'city_shipping' => $order['ShippingAddress']['CityName'],
                'state_shipping' => $order['ShippingAddress']['StateOrProvince'],
                'zipcode_shipping' => $order['ShippingAddress']['PostalCode'],
                'country_shipping' => $order['ShippingAddress']['CountryName'],

                //billing
                'street_billing' => $order['ShippingAddress']['Street1'] ?? $order['ShippingAddress']['Street2'],
                'city_billing' => $order['ShippingAddress']['CityName'],
                'state_billing' => $order['ShippingAddress']['StateOrProvince'],
                'zipcode_billing' => $order['ShippingAddress']['PostalCode'],
                'country_billing' => $order['ShippingAddress']['CountryName'],
            ];

            if (isset($order['TransactionArray']['Transaction'][0]['Buyer'])) {
                $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
                if ($exist_customer_id) continue;
                $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);
            }

            // ----------------------- order ----------------------------


            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $order['OrderID'];
            $order_info['order_date'] = $order['CreatedTime'];
            $last_date = $order_info['order_date'];
            $order_info['insert_type'] = "API";
            $order_info['total'] = $order['Total']['value'];
            $order_info['sub_total'] = $order['Subtotal']['value'];
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $order['CheckoutStatus']['PaymentMethod'];
            $order_info['currency'] = $order['Total']['currencyID'];

            $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>'
                . $customer_info['company_name'] . '<br>'
                . $customer_info['address'] . '<br>'
                . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>'
                . $customer_info['state'] . '<br>'
                . $customer_info['country'];

            $order_info['billing'] = $customer_info['street_billing'] . '<br>'
                . $customer_info['zipcode_billing'] . ' ' . $customer_info['city_billing'] . '<br>'
                . $customer_info['country_billing'];

            $order_info['shipping'] = $customer_info['street_shipping'] . '<br>'
                . $customer_info['zipcode_shipping'] . ' ' . $customer_info['city_billing'] . '<br>'
                . $customer_info['country_shipping'];

            $order_info['status'] = $order['OrderStatus'];

            foreach ($order['TransactionArray']['Transaction'] as $item) {
                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item['Item']['Title']);
                $order_info['description'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item['Item']['Title']);
                $order_info['qty'][] = $item['QuantityPurchased'];
                $order_info['rate'][] = $item['TransactionPrice']['value'];
                $order_info['tax'][] = $item['Taxes']['TotalTaxAmount']['value'];
                $order_info['product_discount'][] = 0;
                $order_info['amount'][] = $item['QuantityPurchased'] * $item['TransactionPrice']['value'];
            }

            app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);

        }

        if ($last_date) {
            DB::table('drm_order_sync')->updateOrInsert(['shop_id' => $shop->id, 'drm_user_id' => $shop->user_id], [
                'last_date' => $last_date
            ]);
        }

    }


    // Shopify Orders
    public function syncShopifyOrder($shop)
    {
        $count = 0;
        try {
            if ($shop) {
                echo "at Shopify shop id $shop->id \n";
                $url = $shop->url . 'admin/api/2020-01/orders.json?status=any&fulfillment_status=any';
                // $url = $shop_details->url . 'admin/api/2020-01/draft_orders.json';
                $client = new \GuzzleHttp\Client();
                $response = $client->request('GET', $url, [
                    'auth' => [$shop->username, $shop->password]
                ]);

                if ($response->getStatusCode() !== 200) {
                    throw new \Exception('Connection problem!');
                }

                $data = $response->getBody()->getContents();
                $all_orders = (json_decode($data))->orders;

                if ($all_orders) {
                    $count += count((array)$all_orders);
                    $this->bulkorderInsertShopify($shop, $all_orders);
                }
            } else {
                throw new \Exception('You have not configured shop!');
            }

            DB::table('api_order_sync_reports')->updateOrInsert([
                'shop_id' => $shop->id,
            ],
                [
                    'status' => '1',
                    'end_time' => Carbon::now()->unix(),
                    'item' => $count
                ]);

        } catch (\Exception $e) {

            if (CRUDBooster::myId()) {
                CRUDBooster::redirect(Request::server('HTTP_REFERER'), trans('Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage()), 'error');
            }
            if ($shop) {
                DB::table('api_order_sync_reports')->updateOrInsert([
                    'shop_id' => $shop->id,
                ],
                    [
                        'status' => '2',
                        'end_time' => Carbon::now()->unix(),
                        'item' => $count,
                        'report' => 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage()
                    ]);
                echo 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage() . "\n";
                $shop_user = User::find($shop->user_id);
                if ($shop_user) {
                    // $hasPermission = DB::table('notification_trigger')
                    // ->where('hook', 'SHOP_VALIDATION_ERROR')->where('status', 0)->first();
                    // if (!empty($hasPermission)) {
                    $message_title = 'Shop: ' . $shop->shop_name . ' - Error: ' . $e->getMessage();
                    // if (isHookRemainOnSidebar('SHOP_VALIDATION_ERROR') && isLocal()) {
                    //     $shop_user->notify(new DRMTelegramNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }else{
                    $shop_user->notify(new DRMNotification($message_title, 'SHOP_VALIDATION_ERROR', '#'));
                    // }
                    // }
                }
            } else {
                echo $e->getMessage() . "\n";
            }
        }
    }

    public function bulkorderInsertShopify($shop, $all_orders)
    {
        echo "inserting at Shopify\n";
        $sync_date = (DB::table('drm_order_sync')->where([
            'shop_id' => $shop->id,
            'drm_user_id' => $shop->user_id
        ])->first())->last_date;

        $l_date = $sync_date;
        // dd($all_orders);
        foreach ((object)$all_orders as $value) {
            // dd($value);
            // dd($value->billing_address->city);

            $exist_shop_id = DB::table('drm_orders_new')->where('cms_user_id', $shop->user_id)->where('order_id_api', $value->id)->first();
            if ($exist_shop_id) continue;

            $new = new DateTime($value->created_at);
            $old = new DateTime($sync_date);

            if ($sync_date != null) {
                if ($old >= $new) {
                    continue;
                }
            }

            $customer_info = $order_info = null;
            $customer_info = [
                'customer_full_name' => $value->customer->default_address->name,
                'company_name' => $value->customer->default_address->company,
                'email' => $value->customer->email,
                'city' => $value->customer->default_address->city,
                'zip_code' => $value->customer->default_address->zip,
                'state' => $value->customer->default_address->province,
                'country' => $value->customer->default_address->country_name,
                'phone' => $value->customer->default_address->phone,
                // 'website' => ,
                'currency' => $value->customer->currency,
                //  'default_language' => ,
                'address' => $value->customer->default_address->address1,
                'insert_type' => "API",
                'user_id' => $shop->user_id,
                //  'vat_number' => ,

                // shipping
                'street_shipping' => $value->shipping_address->address1,
                'city_shipping' => $value->shipping_address->city,
                'state_shipping' => $value->shipping_address->province,
                'zipcode_shipping' => $value->shipping_address->zip,
                'country_shipping' => $value->shipping_address->country,

                //billing
                'street_billing' => $value->billing_address->address1,
                'city_billing' => $value->billing_address->city,
                'state_billing' => $value->billing_address->province,
                'zipcode_billing' => $value->billing_address->zip,
                'country_billing' => $value->billing_address->country,

            ];


            if (isset($value->customer)) {
                $exist_customer_id = DB::table('drm_customers')->where('user_id', $shop->user_id)->where('email', $customer_info['email'])->first();
                if ($exist_customer_id) continue;
                // insert customer
                $customer_id = app('App\Http\Controllers\AdminDrmCustomersController')->add_customer($customer_info);
            }
            // dd($customer_id);


            // ----------------------- order ----------------------------


            $order_info['user_id'] = $shop->user_id;

            $order_info['drm_customer_id'] = $customer_id;
            // $date=date_create("2013-03-15");
            $order_info['order_date'] = $value->created_at;

            $l_date = $order_info['order_date'];

            $order_info['insert_type'] = "API";
            $order_info['total'] = $value->total_price;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $value->id;

            $order_info['sub_total'] = $value->subtotal_price;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $value->gateway;
            $order_info['currency'] = $value->currency;

            $order_info['customer_info'] = $customer_info['customer_full_name'] . '<br>'
                . $customer_info['company_name'] . '<br>'
                . $customer_info['address'] . '<br>'
                . $customer_info['zip_code'] . ' ' . $customer_info['city'] . '<br>'
                . $customer_info['state'] . '<br>'
                . $customer_info['country'];

            $order_info['billing'] = $customer_info['street_billing'] . '<br>'
                . $customer_info['zipcode_billing'] . ' ' . $customer_info['city_billing'] . '<br>'
                . $customer_info['country_billing'];

            $order_info['shipping'] = $customer_info['street_shipping'] . '<br>'
                . $customer_info['zipcode_shipping'] . ' ' . $customer_info['city_billing'] . '<br>'
                . $customer_info['country_shipping'];

            // $order_info['client_note'];
            $order_info['status'] = $value->financial_status;
            $order_info['cart'] = json_encode($value->line_items);

            foreach ((object)$value->line_items as $item) {

                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->name);
                $order_info['description'][] = iconv('UTF-8', 'ASCII//TRANSLIT', $item->title);
                $order_info['qty'][] = $item->quantity;
                $order_info['rate'][] = $item->price;
                $order_info['tax'][] = $item->tax;
                $order_info['product_discount'][] = $item->total_discount ?? 0;
                $order_info['amount'][] = $item->quantity * $item->price;
            }


            // ----------------------- Order Insert ---------------------
            app('App\Http\Controllers\AdminDrmOrdersController')->add_order($order_info);
        }

        if ($l_date) {
            DB::table('drm_order_sync')->updateOrInsert([
                'shop_id' => $shop->id,
                'drm_user_id' => $shop->user_id
            ], [
                'last_date' => $l_date
            ]);
        }

    }

    public function getProduct()
    {


        $curl = curl_init();
        $auth = base64_encode('<EMAIL>:admin123');

        curl_setopt_array($curl, array(

            CURLOPT_URL => "https://sisdemo.club/gambio/shop/api.php/v2/products/",

            CURLOPT_RETURNTRANSFER => true,

            CURLOPT_ENCODING => "",

            CURLOPT_MAXREDIRS => 10,

            CURLOPT_TIMEOUT => 30,

            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,

            CURLOPT_CUSTOMREQUEST => "GET",

            CURLOPT_HTTPHEADER => array(

                "authorization: Basic " . $auth,

                "content-type: application/json"

            ),

        ));

        $response = curl_exec($curl);

        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {

            echo "cURL Error #:" . $err;
        } else {

            $data_p = json_decode($response);
            //  dd($data_p);
            $cnt = 0;
            $data = [];
            $data['page_title'] = 'Gambio Products';
            foreach ($data_p as $key => $value) {
                // code...
                //var_dump($value->id);
                $datap[$cnt]['id'] = $value->id;
                $datap[$cnt]['name'] = $value->name;
                $datap[$cnt]['productModel'] = $value->productModel;
                $datap[$cnt]['price'] = $value->price;
                $datap[$cnt]['price'] = $value->price;
                $cnt++;
            }
            //dd($datap);


            $data['result'] = $datap;   //DB::table('gorilla_products')->orderby('id','desc')->paginate(10);
            //dd($data);

            if (!CRUDBooster::isView()) CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
            $this->cbView('gambio_product', $data);
        }
    }

    public function postSaveCategory()
    {
        try {
            DB::table('shop_category')->updateOrInsert(
                ['id' => $_REQUEST['id']],
                [
                    'user_id' => CRUDBooster::myParentId(),
                    'category_name' => $_REQUEST['name'],
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            return response()->json([
                'success' => true,
                'message' => 'Category save successfully!'
            ]);
        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong!'
            ]);
        }
    }

    public function getAllCategory()
    {
        $shop_category = DB::table('shop_category')->get();
        $html = ' ';
        $count = 1;
        if ($shop_category) {
            foreach ($shop_category as $cat) {
                $html .= '<tr>
                        <th scope="row">' . $count++ . '</th>
                        <td>' . $cat->category_name . '</td>
                        <td>
                            <a class="btn btn-xs btn-success category_edit" title="Edit Data"  data-name = "' . $cat->category_name . '"  data-id = "' . $cat->id . '"><i class="fa fa-pencil"></i></a>
                           ';
                if ($cat->id > 3) {
                    $html .= ' <a class="btn btn-xs btn-warning category_delete" data-id = "' . $cat->id . '" title = "Delete" ><i class="fa fa-trash" ></i ></a >';
                }
                $html .= '</td>
                    </tr>';
            }
        }
        return $html;
    }

    public function getDeleteCategory()
    {
//        DB::table('shob_category')->where('id',$_REQUEST['id'])->delete();
        try {
            $shop_category = DB::table('shop_category')->where('id', $_REQUEST['id'])->delete();
            if ($shop_category) {
                return response()->json(['success' => true, 'message' => 'Category delete successfully!']);
            } else {
                return response()->json(['success' => false, 'message' => 'Something went wrong!']);
            }
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'message' => 'Something went wrong!']);
        }
    }


    public function getexport($id)
    {
        $shop = DB::table('gambio_products')->select('ean')->where('shop_id', $id)->get();
        //dd($shop);

        $file = new \SplTempFileObject();
        $file->setFlags(\SplFileObject::READ_CSV);
        $csv = Writer::createFromFileObject($file);

        $header = array("item_number", "Description", "Partner Name", "Rate", "Stock", "Tax.php", "Unit", "EK Price", "EAN", "Group Name");
        $csv->insertOne($header);

        foreach ($shop as $key => $value) {
            $p_data = [];
            $product = DB::table('drm_products')->select('item_number', 'drm_import_id', 'description', 'stock', 'vat', 'ek_price', 'vk_price', 'ean', 'category')->where('ean', $value->ean)->first();
            $vats = json_decode($product->vat);
            $Vat_all = '';
            foreach ((object)$vats as $key => $vat) {
                $country = DB::table('countries')->where('id', $key)->first()->name;
                $Vat_all .= $country . " : " . $vat;
            }
            $p_data[] = $product->item_number;
            $p_data[] = strip_tags($product->description);
            $d_company_id = DB::table('drm_imports')->select('delivery_company_id')->where('id', $product->drm_import_id)->first();
            $d_company = DB::table('delivery_companies')->select('name')->where('id', $d_company_id->delivery_company_id)->first();
            $p_data[] = $d_company->name;
            $p_data[] = $product->vk_price;
            $p_data[] = $product->stock;
            $p_data[] = $Vat_all;
            $p_data[] = $product->unit;
            $p_data[] = $product->vk_price;
            $p_data[] = $product->ean;
            $p_data[] = $product->category;
            //dd($p_data);
            $csv->insertOne($p_data);
        }

        $csv->output('export.csv');
        die;
    }


    public function getCategory()
    {

        $shop = Shop::where('user_id', CRUDBooster::myId())->first();
        if ($shop) {
            $user = $shop->username;
            $pass = $shop->password;
            $base_url = $shop->url;
            $api_path = "api.php/v2/";
        } else {
            dd("You have not configured any shop yet");
        }
        $url = $base_url . $api_path . "categories";
        $auth = base64_encode("$user:$pass");
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_HTTPHEADER => array(
                "authorization: Basic " . $auth,
                "content-type: application/json"
            ),

        ));
        $response = curl_exec($curl);

        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {

            echo "cURL Error #:" . $err;
        } else {

            $categories = json_decode($response);
            $data = [];
            $data['page_title'] = 'Gambio Categories';
            $data['result'] = $categories;
            $this->cbView('admin.gambio.categories', $data);
        }
    }

    public function getEbayInventoryLocations($id)
    {
        $data['page_title'] = 'eBay location settings';
        $data['shop_id'] = $id;
        try {
            $shop = Shop::where([
                'user_id' => CRUDBooster::myId(),
                'channel' => Channel::EBAY,
                'id' => $id
            ])->first();

            if ($shop) {
                $ebay = new EbayApi($shop->username);
                $res = $ebay->getInventoryLocations();
            }
            $data['locations'] = [];
            if (isset($res['locations'])) {
                foreach ($res['locations'] as $location) {
                    $data['locations'][] = [
                        'name' => $location['name'],
                        'address1' => $location['location']['address']['addressLine1'],
                        'address2' => $location['location']['address']['addressLine2'],
                        'city' => $location['location']['address']['city'],
                        'state' => $location['location']['address']['stateOrProvince'],
                        'country' => $location['location']['address']['country'],
                        'postcode' => $location['location']['address']['postalCode'],
                        'key' => $location['merchantLocationKey']
                    ];
                }
            }
        } catch (\Exception $e) {
            CRUDBooster::redirect(CRUDBooster::mainpath(), trans('crudbooster.denied_access'));
        }

        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();
        return view('admin.drm_export.ebay_inventory_locations', $data);
    }

    public function postEbayInventoryLocations(LaravelRequest $request)
    {
        $shop = Shop::where([
            'user_id' => CRUDBooster::myId(),
            'channel' => Channel::EBAY,
            'id' => $request->shop_id
        ])->first();

        if ($shop) {
            $ebay = new Ebay();
            $res = $ebay->setupInventoryLocation($request->all(), $shop->username);
        }
        return redirect()->back();
    }

    public function getDeleteEbayLoactionKey(LaravelRequest $request)
    {
        $shop = Shop::where([
            'user_id' => CRUDBooster::myId(),
            'channel' => Channel::EBAY,
            'id'      => $request->shop_id
        ])->first();

        if ($shop) {
            $ebay = new EbayApi($shop->username);
            $ebay->deleteLocationKey($request->key);
        }
        return redirect()->back();
    }

    public function getKauflandInventoryWarehouses($id)
    {
        try {
            $shop = Shop::where([
                'user_id' => CRUDBooster::myId(),
                'channel' => 13,
                'id' => $id
            ])->first();

            if ($shop) {
                $kaufland = new KauflandApi($shop);
                $allwarehouse = $kaufland->getInventoryWarehouses();
            }
            if (isset($allwarehouse)) {
                $data['warehouses'] = $allwarehouse['data'] ?? [];
            }
        } catch (\Exception $e) {
            CRUDBooster::redirect(CRUDBooster::mainpath(), trans($allwarehouse['message'] . '' . 'go back Shop setting'));
        }
        return view('admin.drm_export.kaufland_inventory_warehouses', $data);
    }

    public function postKauflandInventoryWarehouse()
    {
        $shop = Shop::where(['user_id' => CRUDBooster::myId(), 'channel' => 13])->first();
        if ($shop) {
            $kaufland = new Kaufland();
            $res = $kaufland->setupInventoryWarehouse($_REQUEST, $shop);
        }
        return redirect()->back();
    }

    public function getDeleteKauflandWarehouse($id_warehouse)
    {
        $shop = Shop::where(['user_id' => CRUDBooster::myId(), 'channel' => 13])->first();
        if ($shop) {
            $kaufland = new KauflandApi($shop);
            $res = $kaufland->deleteWarehouse($id_warehouse);
        }
        return redirect()->back();
    }


    public function getEbayTemplate($shop_id)
    {
        $user_id = CRUDBooster::myParentId();
        $data['shop_id'] = $shop_id;
        $shop = Shop::where([
            'id' => $shop_id,
            'user_id' => $user_id,
            'channel' => Channel::EBAY
        ])->exists();
        if(!$shop){
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans('crudbooster.denied_access'));
        }
        $data['page_title'] = "eBay template";
        $data['template'] = DB::table('ebay_template')->where([
            'user_id' => CRUDBooster::myParentId(),
            'shop_id' => $shop_id
        ])->first();

        $data['product_tags'] = [
            '#NAME#',
            '#EAN#',
            '#ITEM_NUMBER#',
            '#ITEM_WEIGHT#',
            '#ITEM_SIZE#',
            '#ITEM_COLOR#',
            '#BRAND#',
            '#MATERIALS#',
            '#PRODUCTION_YEAR#',
            '#DESCRIPTION#',
            '#GENDER#',
            '#STOCK#',
            '#CATEGORY#',
            '#NOTE#',
            '#TAGS#',
            '#IMAGE_1#'
        ];
        return view('admin.drm_export.ebay_template', $data);
    }

    public function postEbayTemplate(LaravelRequest $request)
    {
        $template = $request->template;
        DB::table('ebay_template')->updateOrInsert(
            ['user_id' => CRUDBooster::myId(),'shop_id' => $request->shop_id],
            [
                'template' => $template
            ]
        );
        return redirect()->back();
    }

    public function postEbayTemplatePreview()
    {
        return $_REQUEST['template'];
    }

    public function getAllowEbayCategories()
    {
        if (CRUDBooster::isSuperadmin() || CRUDBooster::isDropmatixSupport()) {
            $categories = Cache::store('file')->remember('allow_ebay_categories', 05.0, function () {
                return DB::table('channel_categories')->where('channel', 4)->get();
            });

            $categories = collect($categories);

            if (isset($_REQUEST['filter'])) {
                if ($_REQUEST['filter'] == "allowed") {
                    $categories = $categories->where('status', 1);
                } elseif ($_REQUEST['filter'] == "not_allowed") {
                    $categories = $categories->where('status', 0);
                }
            }

            $data['categories'] = $categories;
            return view('admin.drm_export.ebay_category_management', $data);
        } else {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }
    }

    public function getOttoCategories()
    {
        if (CRUDBooster::isSuperadmin() || CRUDBooster::isDropmatixSupport()) {
            $categories = Cache::store('file')->remember('otto_categories', 05.0, function () {
                return DB::table('channel_categories')->where('channel', 12)->get();
            });

            $categories = collect($categories);

            if (isset($_REQUEST['filter'])) {
                if ($_REQUEST['filter'] == "allowed") {
                    $categories = $categories->where('status', 1);
                } elseif ($_REQUEST['filter'] == "not_allowed") {
                    $categories = $categories->where('status', 0);
                }
            }

            $data['categories'] = $categories;
            return view('admin.drm_export.otto_category_management', $data);
        } else {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }
    }

    public function postEbayCategoriesAllow()
    {
        if (CRUDBooster::isSuperadmin() || CRUDBooster::isDropmatixSupport()) {
            if ($_REQUEST['action'] == "Allow") {
                $status = 1;
            } else {
                $status = 0;
            }

            $category_id = $_REQUEST["selected_categories"];
            DB::table("channel_categories")->where('channel', 4)->whereIn("category_id", $category_id)->update(["status" => $status]);
            return redirect()->back();
        } else {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }
    }


    public function postOttoCategoriesAllow()
    {
        if (CRUDBooster::isSuperadmin() || CRUDBooster::isDropmatixSupport()) {
            if ($_REQUEST['action'] == "Allow") {
                $status = 1;
            } else {
                $status = 0;
            }

            $category_id = $_REQUEST["selected_categories"];
            DB::table("channel_categories")->where('channel', 12)->whereIn("category_id", $category_id)->update(["status" => $status]);
            return redirect()->back();
        } else {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }
    }

    public function getAllowKauflandCategories()
    {
        if (CRUDBooster::isSuperadmin()) {

            $categories = DB::table('channel_categories')->where('channel', 13);

            if (isset($_REQUEST['filter'])) {
                if ($_REQUEST['filter'] == "allowed") {
                    $categories = $categories->where('status', 1);
                } elseif ($_REQUEST['filter'] == "not_allowed") {
                    $categories = $categories->where('status', 0);
                }
            }

            $search = $_REQUEST['search'];
            if ($search != "") {
                $categories = $categories->where(function ($query) use ($search) {
                    $query->where('category_name', 'like', '%' . $search . '%')
                        ->orWhere('category_id', 'like', '%' . $search . '%');
                })
                    ->paginate(20);
                $categories->appends(['q' => $search]);
            } else {
                $categories = $categories->paginate(20);
            }


            return view('admin.drm_export.kaufland_category_management', compact('categories'));
        } else {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }

    }

    public function postKauflandCategoriesAllow()
    {
        if (CRUDBooster::isSuperadmin()) {
            if ($_REQUEST['action'] == "Allow") {
                $status = 1;
            } else {
                $status = 0;
            }

            $category_id = $_REQUEST["selected_categories"];
            DB::table("channel_categories")->whereIn("category_id", $category_id)->update(["status" => $status]);
            return redirect()->back();
        } else {
            CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        }
    }

    //Doptienda activation
    public function droptiendaActivation()
    {
        try {

            $errors = [];

            $request = $_REQUEST;
            $validator = Validator::make($request, [
                'password' => 'required',
                'url' => 'required',
                'email' => ['required', Rule::exists('cms_users')->where(function ($query) use ($request) {
                    return $query->where(['status' => 'Active']);
                })],
            ]);

            if ($validator->fails()) {
                $errors = $validator->errors();
                throw new \Exception('Your DRM credentials is Invalid! Please try with correct data.');
            }

            $email = $request['email'];
            $password = $request['password'];
            $shop_url = $request['url'];

            $client = new \GuzzleHttp\Client();
            $response = $client->request('POST', config('app.api_host').'/oauth2/token', [
                'form_params' => [
                    'grant_type' => 'password',
                    'client_id' => '7',
                    'client_secret' => 'JH43WGCz0uoClBnR5EEqwsvXiKeazq7Eq3gUkJRn',
                    'username' => $email,
                    'password' => $password,
                ],
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new \Exception('Token renerate failed!');
            }

            $dataRaw = $response->getBody()->getContents();
            $raw_data = json_decode($dataRaw, true);

            if ($raw_data) {
                $data = $raw_data['data'];
                $token = $data['access_token'];
                $id = $data['id'];

                $user = DB::table('cms_users')->where('id', $id)->where('status', 'Active')->select('id', 'name')->first();
                if (empty($user)) {
                    throw new \Exception('Your DRM account is Invalid or Blocked!');
                }

                $user_token = Str::random(40);
                $userpasToken = (string)Str::uuid();
                $user_token = 'drm' . $user_token;
                $userpasToken = 'drm' . $userpasToken;

                $shop_name = 'Droptienda - ' . $user->name;

                $shop = Shop::where('user_id', $id)->where('channel', 10)->first();
                if ($shop) {
                    $shop->update(['username' => $user_token, 'password' => $userpasToken]);
                } else {
                    Shop::updateOrCreate([
                        'user_id' => $id,
                        'channel' => 10,
                    ], [
                        'shop_name' => $shop_name,
                        'lang' => 'de',
                        'url' => $shop_url,
                        'username' => $user_token,
                        'password' => $userpasToken,
                    ]);
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Connect with DRM Droptienda channel successfully!',
                    'userToken' => $user_token,
                    'userPassToken' => $userpasToken,
                ]);
            }

            throw new \Exception('Sorry, Connect with DRM Droptienda channel failed! Please try again!');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'errors' => $errors,
            ]);
        }
    }

    //Check24 shop validation
    private static function validateCheck24Shop($data)
    {
        try {
            $adapter = new Ftp([
                'host' => 'partnerftp.shopping.check24.de', // required
                'root' => '/', // required
                'username' => $data->username, // required
                'password' => $data->password, // required
                'port' => 44021,
                'ssl' => false,
                'timeout' => 90,
                'utf8' => false,
                'passive' => true,
                'transferMode' => FTP_BINARY,
                'systemType' => null, // 'windows' or 'unix'
                'ignorePassiveAddress' => null, // true or false
                'timestampsOnUnixListingsEnabled' => false, // true or false
                'recurseManually' => false // true
            ]);
            $filesystem = new Filesystem($adapter);

            $dir = '/outbound';
            $allPaths = $filesystem->listContents($dir);

            return [
                'success' => true,
                'message' => 'Check24 credentials verification success',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    private static function validateGalaxusShop($data)
    {
        $sftp = new SFTP($data->url);
        if (!$sftp->login($data->username, $data->password)) {
            return [
                'success' => true,
                'message' => 'Galaxus credentials verification failed. '.implode(',', $sftp->getErrors()),
            ];
        }
        return [
            'success' => true,
            'message' => 'Galaxus credentials verification success',
        ];
    }

    //Decathlon shop validation
    private static function validateDecathlonShop($data)
    {
        try {
            $client = new \GuzzleHttp\Client();
            $url = 'https://marketplace-decathlon-eu.mirakl.net/api/account';
            $params = [
                'headers' => [
                    'Authorization' => $data->username,
                    'Accept' => 'application/json'
                ],
            ];

            $client->request('GET', $url, $params);

            return [
                'success' => true,
                'message' => 'Decathlon credentials verification success',
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    //Droptienda shop validation
    private static function validateDroptiendaShop($data)
    {
        try {
            $user = $data->username;
            $password = $data->password;
            $url = rtrim($data->url, '/') . '/api/v1/validate-droptienda-shop?userToken=' . $user . '&userPassToken=' . $password;

            $client = new \GuzzleHttp\Client();
            $response = $client->request('GET', $url);

            if ($response->getStatusCode() !== 200) {
                throw new \Exception('Token generate failed!');
            }

            $dataRaw = $response->getBody()->getContents();
            $raw_data = json_decode($dataRaw, true);

            $success = $raw_data['success'] ?? false;
            $message = $raw_data['message'] ?? 'DT verification failed!';

            if ($success == true) {
                return [
                    'success' => true,
                    'message' => $message,
                ];
            }

            throw new \Exception($message);

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    //Otto shop validation
    private static function validateOttoShop($data)
    {
        try {
            $user = $data->username;
            $password = $data->password;
            $client = new \GuzzleHttp\Client();
            $response = $client->request('POST', 'https://api.otto.market/v1/token', [
                'form_params' => [
                    'username' => $user,
                    'password' => $password,
                    'grant_type' => 'password',
                    'client_id' => 'token-otto-api',
                ],
            ]);

            if ($response->getStatusCode() !== 200) {
                throw new \Exception('Token generate failed!');
            }

            $dataRaw = $response->getBody()->getContents();
            $raw_data = json_decode($dataRaw, true);

            $success = (bool)$raw_data['access_token'];
            $message = 'Otto credentials verification ' . (($success) ? 'success' : 'failed') . '!';

            if ($success) {
                return [
                    'success' => true,
                    'message' => $message,
                ];
            }

            throw new \Exception($message);

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Otto credentials verification failed!',
            ];
        }
    }

    public function shopVersionUpdate(LaravelRequest $request)
    {
        $rules = [
            'version_name' => 'required',
        ];
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return response()->json(['success' => false, 'message' => $validator->messages()]);
        }

        try {
            $password = $request->header('userPassToken');
            $token = $request->header('userToken');

            $shop = Shop::where('username', $token)->where('password', $password)->first();
            if ($shop) {
                Shop::where('user_id', $shop->user_id)->update([
                    'shop_version' => $request->version_name
                ]);

                $customerId = app('App\Http\Controllers\AdminDrmAllCustomersController')->userBillingToCustomerProfile($shop->user_id, 2439);

                DropfunnelCustomerTag::insertTag('DT-shop-version-' . $request->version_name, 2439, $customerId, 20);  // 2439 droptiendaDaily user_id ,

                return ['success' => true, 'message' => 'Version updated successfully!'];
            } else {
                return ['success' => false, 'message' => 'Shop does not exists!'];
            }
        } catch (Exception $exception) {
            return ['success' => false, 'message' => 'Something went wrong!',];
        }
    }

    public function customerTagInsert(LaravelRequest $request)
    {
        $data = $request->only(['tag_name', 'user_id', 'customer_id', 'insert_type']);
        try {
            DropfunnelCustomerTag::insertTag($data['tag_name'], $data['user_id'], $data['customer_id'], $data['insert_type']);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                "message" => $exception,
            ]);
        }
    }

//    public function getAutoTransferUsage(LaravelRequest $request)
//    {
//        $user_id = CRUDBooster::myParentId();
//
//        $currentChannel = Shop::where([
//            'id' => $request->shop_id,
//            'user_id' => $user_id
//        ])->value('shop_name');
//
//        $usage = Shop::where([
//            'user_id' => $user_id,
//            'auto_transfer' => true
//        ])->pluck('shop_name')->toArray();
//
//        $plan = app('App\Services\AppStoreService')->getAppPlan(Apps::AUTO_TRANSFER, $user_id);
//        $limit = app('App\Services\AppStoreService')->getPlanLimit(Apps::AUTO_TRANSFER, $plan);
//
//        return response([
//            'status' => true,
//            'data' => [
//                'usage' => count($usage),
//                'limit' => $limit,
//                'activeChannels' => $usage,
//                'currentChannel' => $currentChannel
//            ]
//        ]);
//    }

    public function setAutoTransfer(LaravelRequest $request)
    {
        $user_id = CRUDBooster::myParentId();
        $trigger = (int)$request->trigger;

        $shop = Shop::where([
            'user_id' => $user_id,
            'id' => $request->shopId
        ])->first();

        switch ($trigger) {
            case 0:
                $shop->auto_transfer = false;
                $shop->save();
                break;
            case 1:
                if (professionalOrHigher()) {
                    $shop->auto_transfer = true;
                    $shop->save();
                    $this->transferOldProducts($shop);
                }
                break;
        }

        return response(['success' => true ]);
    }

    public function transferOldProducts($shop)
    {
        $ids = DrmProduct::where([
            'user_id' => $shop->user_id
        ])->whereNotIn('id',ChannelProduct::select('drm_product_id')->where([
            'shop_id' => $shop->id,
            'user_id' => $shop->user_id
        ]))->pluck('id')->toArray();
        foreach (array_chunk($ids,500) as $items) {
            AutoTransfer::dispatch($items,$shop->user_id,$shop->lang ?? 'de',[$shop->id]);
        }
    }

    private function getPrefix($url): string
    {
        return trim( $url, '/').'/';
    }

    public function getUpdateShop($channel,$shop_id)
    {
        switch ($channel){
            case Channel::EBAY:
                // Create a secure state parameter that includes shop_id and user_id for verification
                $state = base64_encode(json_encode([
                    'shop_id' => $shop_id,
                    'user_id' => CRUDBooster::myParentId(),
                    'action' => 'update',
                    'timestamp' => time()
                ]));
                
                $authUrl = 'https://auth.ebay.com/oauth2/authorize?' . http_build_query([
                    'client_id' => 'EXPERTIS-DRM-PRD-f193e18a1-6a51f647',
                    'response_type' => 'code',
                    'redirect_uri' => 'EXPERTISEROCKS_-EXPERTIS-DRM-PR-xszybym',
                    'scope' => 'https://api.ebay.com/oauth/api_scope https://api.ebay.com/oauth/api_scope/sell.marketing.readonly https://api.ebay.com/oauth/api_scope/sell.marketing https://api.ebay.com/oauth/api_scope/sell.inventory.readonly https://api.ebay.com/oauth/api_scope/sell.inventory https://api.ebay.com/oauth/api_scope/sell.account.readonly https://api.ebay.com/oauth/api_scope/sell.account https://api.ebay.com/oauth/api_scope/sell.fulfillment.readonly https://api.ebay.com/oauth/api_scope/sell.fulfillment https://api.ebay.com/oauth/api_scope/sell.analytics.readonly https://api.ebay.com/oauth/api_scope/sell.finances https://api.ebay.com/oauth/api_scope/sell.payment.dispute https://api.ebay.com/oauth/api_scope/commerce.identity.readonly',
                    'state' => $state
                ]);
                
                return redirect($authUrl);
            default:
                return redirect()->back();
        }
    }

    public function getAllRemoveShop()
    {
        $allShopId = $_GET['shop_ids'];
        if(!empty($allShopId)){
            foreach($allShopId as $shopId){
                $this->getRemoveShop($shopId);
            }
        }
    }

    public function getRemoveShop($shop_id)
    {
        $this->hook_before_delete($shop_id);

        $shop = Shop::find($shop_id);
        try {
            create_agb_log(
                $shop->user_id,
                [
                    'shop_info' => ['shop_name' => $shop->shop_name, 'shop_type' => $shop->channel, 'shop_url' => $shop->url,],
                ], [],
                'Shop ' . $shop->shop_name . ' is deleted by user.',
            );
        } catch (\Exception $th) {}

        Shop::where([
            'id' => $shop_id
        ])->delete();

        if(request()->ajax()){
            return;
        }

        return redirect()->back()->with(['message' => __('crudbooster.alert_delete_data_success'), 'message_type' => 'success']);
    }
}
