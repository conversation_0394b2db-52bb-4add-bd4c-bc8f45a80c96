<?php namespace App\Http\Controllers;
	use CB;
	use App\Mail\DRMSEndMail;
    use App\Models\DrmModule;
    use App\Models\SubUserPermission;
    use App\User;
    use Illuminate\Support\Facades\Mail;
    use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use Illuminate\Support\Facades\Cache;
	use Route;

	class AdminSubAccountController extends \crocodicstudio\crudbooster\controllers\CBController {

	    public function cbInit() {

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "name";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = true;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = false;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "cms_users";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
            $this->col[] = array("label"=>"Id","name"=>"id");
            $this->col[] = array("label"=>"Name","name"=>"name");
            $this->col[] = array("label"=>"Email","name"=>"email");
            $this->col[] = array("label"=>"Privilege","name"=>"id_cms_privileges","join"=>"cms_privileges,name");
            $this->col[] = array("label"=>"Photo","name"=>"photo","image"=>1);
            $this->col[] = array("label"=>"Status","name"=>"status");
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
            $this->form[] = array("label"=>__("crudbooster.PROFILE_NAME"),"name"=>"name",'required'=>true,'validation'=>'required|alpha_spaces|min:3');
            $this->form[] = array("label"=>__("crudbooster.PROFILE_EMAIL"),"name"=>"email",'required'=>true,'type'=>'email','validation'=>'required|email|unique:cms_users,email,'.CRUDBooster::getCurrentId());
            $this->form[] = array("label"=>__("crudbooster.PROFILE_IMAGE"),"name"=>"photo","type"=>"upload","help"=>__("crudbooster.PROFILE_IMAGE_RESOLUTION"),'validation'=>'image|max:1000','resize_width'=>90,'resize_height'=>90);
//            $this->form[] = array("label"=>"Privilege","name"=>"id_cms_privileges","type"=>"select","datatable"=>"cms_privileges,name",'required'=>true);
            $this->form[] = array("label"=>__("crudbooster.PROFILE_PASSWORD"),"name"=>"password","type"=>"password",'validation'=>'nullable',"help"=>__("crudbooster.PROFILE_PASSWORD_RECOMENDATION") );
            $this->form[] = array("label"=>__("crudbooster.PROFILE_PASSWORD_CONFIRM"),"name"=>"password_confirmation","type"=>"password",'validation'=>'nullable|same:password',"help"=>__("crudbooster.PROFILE_PASSWORD_CONFIRM_RECOMENDATION") );
			# END FORM DO NOT REMOVE THIS LINE

			# OLD START FORM
			//$this->form = [];
			# OLD END FORM

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();
            $this->addaction[] = ['label'=>__('Permission'),'url'=>CRUDBooster::mainpath('user-permission/[id]'),'icon'=>'fa fa-setting','color'=>'warning'];

			$this->addaction[] = ['label'=>__('Project permission'),'url'=>CRUDBooster::mainpath('user-project-permission/[id]'),'icon'=>'fa fa-setting','color'=>'warning'];

	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();
	        if(!CRUDBooster::isKeyAccount()){
            	$this->index_button[] = ['label' => 'Key-Account', 'url' => "javascript:keyAccountManagerModal()", 'color' => 'warning', "icon" => "fa fa-tasks"];
        	}



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here
            $query->whereNotNull('parent_id')
            ->where('parent_id', CRUDBooster::myId())
            ->whereIntegerNotInRaw('id_cms_privileges', [9, 10, 11]);
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
            $postdata['id_cms_privileges'] = CRUDBooster::myPrivilegeId();
            $postdata['parent_id'] = CRUDBooster::myId();
            $postdata['email_verified_at'] = now();
            $postdata['status']='Active';

            // if($postdata['id_cms_privileges'] == 3){
                // $tags = [
                //     'user_name' => $postdata['name'],
                //     'user_email' => $postdata['email'],
                //     'password_confirmation' => $postdata['password_confirmation']
                // ];
                // $slug = 'welcome_email';
                // $lang = getUserSavedLang($postdata['email']);
                // $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
                // app('drm.mailer')->getMailer()->to($postdata['email'])->send(new DRMSEndMail($mail_data));

            // }
            unset($postdata['password_confirmation']);
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        DB::table('takeappointment')->updateOrInsert(
	            ['user_id' => $id],
	            ['payment_date_for' => 1, 'payment_date_remaining' => 1]
	        );
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
            unset($postdata['password_confirmation']);
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }



	    //By the way, you can still create your own method in here... :)

        public function getUserPermission($id)
        {
            $user = User::find($id);
            if(empty($user) || empty($user->parent_id)) {
                CRUDBooster::redirect(CRUDBooster::mainPath(), 'Invalid sub account.', 'warning');
            }

			$page_title = __("Sub Account Page");

            $modules = DrmModule::with('features')
				->whereIn('path', ['drm_all_orders', 'drm_products', 'upcoming_invoices','drm_projects','dropfunnels_menu','pricepanther_menu', 'all_rights'])
				->get();

			if ($modules->isNotEmpty()) {
				$modules->prepend($modules->pop());
			}
            
			$permissions = SubUserPermission::query()
                ->select('sub_user_permissions.feature_id', 'module_features.module_id')
                ->join('module_features', 'module_features.id', '=', 'sub_user_permissions.feature_id')
                ->where('cms_user_id', $user->id)
                ->where('enable', true)
                ->get()
                ->groupBy('module_id');

            $permissions = $permissions->map(function ($items) {
                return $items->pluck('feature_id')->toArray();
            })->toArray();
			
			$mp_module = DrmModule::with('features')->where('path','marketplace_products')->orderBy('id')->get();

            return view('marketplace.supplier.permission', compact('page_title', 'user', 'modules', 'permissions', 'mp_module'));
        }

		public function getUserProjectPermission($id)
        {
			$data = [];
			$data['user'] = User::find($id);
            if(empty($data['user']) || empty($data['user']->parent_id)) {
                CRUDBooster::redirect(CRUDBooster::mainPath(), 'Invalid sub account.', 'warning');
            }

		
			$page_title = __("Sub Account Page");

            $modules = DrmModule::with('features')->whereIn('path', ['drm_all_orders', 'drm_products', 'upcoming_invoices','drm_projects'])->get();
            // $permissions = SubUserPermission::query()
            //     ->select('sub_user_permissions.feature_id', 'module_features.module_id')
            //     ->join('module_features', 'module_features.id', '=', 'sub_user_permissions.feature_id')
            //     ->where('cms_user_id', $user->id)
            //     ->where('enable', true)
            //     ->get()
            //     ->groupBy('module_id');

			$data['permissions'] = DB::table('sub_user_project_permissions')->where('user_id',$id)->where('project_id',null)->get();
			//dd($data['permissions']);

			$data['groups'] = DB::table('drm_project_groups')->where('user_id', CRUDBooster::myParentId())->get();
			$data['projects'] = DB::table('drm_project_members')->Join('drm_projects', 'drm_projects.id', '=', 'drm_project_members.drm_project_id')
			->where('drm_project_members.cms_user_id', CRUDBooster::myId())
			->orderBy('drm_projects.sort')
			->get();
			$data['permissonMenus'] = DB::table('drm_project_access_menus')->get();	
	
            // $permissions = $permissions->map(function ($items) {
            //     return $items->pluck('feature_id')->toArray();
            // })->toArray() ;

            return view('marketplace.supplier.projectpermission', $data);
        }

        public function postSavePermission()
        {
            $request = request();
            $userId = $request->input('user_id');

			if (isset($request->feature_permission) && in_array(53, $request->feature_permission)) { // all rights > feature_id=53 
				SubUserPermission::updateOrCreate(
					[
						'cms_user_id' => $userId, 
						'feature_id' => 53
					], 
					[
						'enable' => true
					]
				);
			} else {
				SubUserPermission::where('cms_user_id', $userId)->update(['enable' => false]);

				if (isset($request->feature_permission)) {
					foreach ($request->feature_permission as $featureId) {
						SubUserPermission::updateOrCreate(
							[
								'cms_user_id' => $userId, 
								'feature_id' => $featureId
							], 
							[
								'enable' => true
							]
						);
					}
				}
			}

            CRUDBooster::redirect(CRUDBooster::mainPath(), 'Success', 'success');
        }
		
		public function postSaveProjectPermission()
        {
            $request = request();
            $userId = $request->input('user_id');
			
			DB::table('sub_user_project_permissions')->where('user_id',$userId)->where('project_id',null)->delete();
            
			if(!empty($request->project_permission)){
				foreach ($request->project_permission as $key => $projectId) {
					DB::table('sub_user_project_permissions')->updateOrInsert(['user_id'=>$userId,'group_id'=>$projectId],
					[
						'group_id'=>$projectId,
						'user_id' => $userId,
						'permission_menus' => json_encode($request[''.$projectId.'_menu_permission']) ?? [],
					]);
				}
			}

            CRUDBooster::redirect(CRUDBooster::mainPath(), 'Success', 'success');
        }

        //Index page
        public function getIndex()
        {
			// if(isLocal() || in_array(CRUDBooster::myParentId(), [212, 210, 2592, 179])){
				return redirect(url('admin/tariff?tab=sub_account'));
			// }

        	$this->cbLoader();

        	$module = CRUDBooster::getCurrentModule();

        	if (! CRUDBooster::isView() && $this->global_privilege == false) {
        		CRUDBooster::insertLog(trans('crudbooster.log_try_view', ['module' => $module->name]));
        		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
        	}

        	$data['page_title'] = __("Sub Account Page");
        	$data['page_description'] = trans('crudbooster.default_module_description');


        	if(CRUDBooster::isKeyAccount()){

        		$key_users_id = \DRM::keyAccountUsersId(CRUDBooster::myId());
        		$data['key_users'] = DB::table('cms_users')
        					->whereNotNull('status')
        					->whereNotNull('email_verified_at')
        					->whereIntegerInRaw('id', $key_users_id)
        					->select('name', 'email', 'id', 'photo')
        					->get();
        		return view('sub_user.key_users', $data);
        	}

        	if (Request::get('parent_table')) {
        		$parentTablePK = CB::pk(g('parent_table'));
        		$data['parent_table'] = DB::table(Request::get('parent_table'))->where($parentTablePK, Request::get('parent_id'))->first();
        		if (Request::get('foreign_key')) {
        			$data['parent_field'] = Request::get('foreign_key');
        		} else {
        			$data['parent_field'] = CB::getTableForeignKey(g('parent_table'), $this->table);
        		}

        		if ($parent_field) {
        			foreach ($this->columns_table as $i => $col) {
        				if ($col['name'] == $parent_field) {
        					unset($this->columns_table[$i]);
        				}
        			}
        		}
        	}

        	$data['table'] = $this->table;
        	$data['table_pk'] = CB::pk($this->table);
        	$data['date_candidate'] = $this->date_candidate;
        	$data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : $this->limit;

        	$tablePK = $data['table_pk'];
        	$table_columns = CB::getTableColumns($this->table);
        	$result = DB::table($this->table)->select(DB::raw($this->table.".".$this->primary_key));

        	if (Request::get('parent_id')) {
        		$table_parent = $this->table;
        		$table_parent = CRUDBooster::parseSqlTable($table_parent)['table'];
        		$result->where($table_parent.'.'.Request::get('foreign_key'), Request::get('parent_id'));
        	}

        	$this->hook_query_index($result);

        	if (in_array('deleted_at', $table_columns)) {
        		$result->where($this->table.'.deleted_at', null);
        	}

        	$alias = [];
        	$join_alias_count = 0;
        	$join_table_temp = [];
        	$table = $this->table;
        	$columns_table = $this->columns_table;
        	foreach ($columns_table as $index => $coltab) {

        		$join = @$coltab['join'];
        		$join_where = @$coltab['join_where'];
        		$join_id = @$coltab['join_id'];
        		$field = @$coltab['name'];
        		$join_table_temp[] = $table;

        		if (! $field) {
        			continue;
        		}

        		if (strpos($field, ' as ') !== false) {
        			$field = substr($field, strpos($field, ' as ') + 4);
        			$field_with = (array_key_exists('join', $coltab)) ? str_replace(",", ".", $coltab['join']) : $field;
        			$result->addselect(DB::raw($coltab['name']));
        			$columns_table[$index]['type_data'] = 'varchar';
        			$columns_table[$index]['field'] = $field;
        			$columns_table[$index]['field_raw'] = $field;
        			$columns_table[$index]['field_with'] = $field_with;
        			$columns_table[$index]['is_subquery'] = true;
        			continue;
        		}

        		if (strpos($field, '.') !== false) {
        			$result->addselect($field);
        		} else {
        			$result->addselect($table.'.'.$field);
        		}

        		$field_array = explode('.', $field);

        		if (isset($field_array[1])) {
        			$field = $field_array[1];
        			$table = $field_array[0];
        		} else {
        			$table = $this->table;
        		}

        		if ($join) {

        			$join_exp = explode(',', $join);

        			$join_table = $join_exp[0];
        			$joinTablePK = CB::pk($join_table);
        			$join_column = $join_exp[1];
        			$join_alias = str_replace(".", "_", $join_table);

        			if (in_array($join_table, $join_table_temp)) {
        				$join_alias_count += 1;
        				$join_alias = $join_table.$join_alias_count;
        			}
        			$join_table_temp[] = $join_table;

        			$result->leftjoin($join_table.' as '.$join_alias, $join_alias.(($join_id) ? '.'.$join_id : '.'.$joinTablePK), '=', DB::raw($table.'.'.$field.(($join_where) ? ' AND '.$join_where.' ' : '')));
        			$result->addselect($join_alias.'.'.$join_column.' as '.$join_alias.'_'.$join_column);

        			$join_table_columns = CRUDBooster::getTableColumns($join_table);
        			if ($join_table_columns) {
        				foreach ($join_table_columns as $jtc) {
        					$result->addselect($join_alias.'.'.$jtc.' as '.$join_alias.'_'.$jtc);
        				}
        			}

        			$alias[] = $join_alias;
        			$columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table, $join_column);
        			$columns_table[$index]['field'] = $join_alias.'_'.$join_column;
        			$columns_table[$index]['field_with'] = $join_alias.'.'.$join_column;
        			$columns_table[$index]['field_raw'] = $join_column;

        			@$join_table1 = $join_exp[2];
        			@$joinTable1PK = CB::pk($join_table1);
        			@$join_column1 = $join_exp[3];
        			@$join_alias1 = $join_table1;

        			if ($join_table1 && $join_column1) {

        				if (in_array($join_table1, $join_table_temp)) {
        					$join_alias_count += 1;
        					$join_alias1 = $join_table1.$join_alias_count;
        				}

        				$join_table_temp[] = $join_table1;

        				$result->leftjoin($join_table1.' as '.$join_alias1, $join_alias1.'.'.$joinTable1PK, '=', $join_alias.'.'.$join_column);
        				$result->addselect($join_alias1.'.'.$join_column1.' as '.$join_column1.'_'.$join_alias1);
        				$alias[] = $join_alias1;
        				$columns_table[$index]['type_data'] = CRUDBooster::getFieldType($join_table1, $join_column1);
        				$columns_table[$index]['field'] = $join_column1.'_'.$join_alias1;
        				$columns_table[$index]['field_with'] = $join_alias1.'.'.$join_column1;
        				$columns_table[$index]['field_raw'] = $join_column1;
        			}
        		} else {

        			if(isset($field_array[1])) {
        				$result->addselect($table.'.'.$field.' as '.$table.'_'.$field);
        				$columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
        				$columns_table[$index]['field'] = $table.'_'.$field;
        				$columns_table[$index]['field_raw'] = $table.'.'.$field;
        			}else{
        				$result->addselect($table.'.'.$field);
        				$columns_table[$index]['type_data'] = CRUDBooster::getFieldType($table, $field);
        				$columns_table[$index]['field'] = $field;
        				$columns_table[$index]['field_raw'] = $field;
        			}

        			$columns_table[$index]['field_with'] = $table.'.'.$field;
        		}
        	}

        	if (Request::get('q')) {
        		$result->where(function ($w) use ($columns_table, $request) {
        			foreach ($columns_table as $col) {
        				if (! $col['field_with']) {
        					continue;
        				}
        				if ($col['is_subquery']) {
        					continue;
        				}
        				$w->orwhere($col['field_with'], "like", "%".Request::get("q")."%");
        			}
        		});
        	}

        	if (Request::get('where')) {
        		foreach (Request::get('where') as $k => $v) {
        			$result->where($table.'.'.$k, $v);
        		}
        	}

        	$filter_is_orderby = false;
        	if (Request::get('filter_column')) {

        		$filter_column = Request::get('filter_column');
        		$result->where(function ($w) use ($filter_column, $fc) {
        			foreach ($filter_column as $key => $fc) {

        				$value = @$fc['value'];
        				$type = @$fc['type'];

        				if ($type == 'empty') {
        					$w->whereNull($key)->orWhere($key, '');
        					continue;
        				}

        				if ($value == '' || $type == '') {
        					continue;
        				}

        				if ($type == 'between') {
        					continue;
        				}

        				switch ($type) {
        					default:
        					if ($key && $type && $value) {
        						$w->where($key, $type, $value);
        					}
        					break;
        					case 'like':
        					case 'not like':
        					$value = '%'.$value.'%';
        					if ($key && $type && $value) {
        						$w->where($key, $type, $value);
        					}
        					break;
        					case 'in':
        					case 'not in':
        					if ($value) {
        						$value = explode(',', $value);
        						if ($key && $value) {
        							$w->whereIn($key, $value);
        						}
        					}
        					break;
        				}
        			}
        		});

        		foreach ($filter_column as $key => $fc) {
        			$value = @$fc['value'];
        			$type = @$fc['type'];
        			$sorting = @$fc['sorting'];

        			if ($sorting != '') {
        				if ($key) {
        					$result->orderby($key, $sorting);
        					$filter_is_orderby = true;
        				}
        			}

        			if ($type == 'between') {
        				if ($key && $value) {
        					$result->whereBetween($key, $value);
        				}
        			} else {
        				continue;
        			}
        		}
        	}

        	if ($filter_is_orderby == true) {
        		$data['result'] = $result->paginate($limit);
        	} else {
        		if ($this->orderby) {
        			if (is_array($this->orderby)) {
        				foreach ($this->orderby as $k => $v) {
        					if (strpos($k, '.') !== false) {
        						$orderby_table = explode(".", $k)[0];
        						$k = explode(".", $k)[1];
        					} else {
        						$orderby_table = $this->table;
        					}
        					$result->orderby($orderby_table.'.'.$k, $v);
        				}
        			} else {
        				$this->orderby = explode(";", $this->orderby);
        				foreach ($this->orderby as $o) {
        					$o = explode(",", $o);
        					$k = $o[0];
        					$v = $o[1];
        					if (strpos($k, '.') !== false) {
        						$orderby_table = explode(".", $k)[0];
        					} else {
        						$orderby_table = $this->table;
        					}
        					$result->orderby($orderby_table.'.'.$k, $v);
        				}
        			}
        			$data['result'] = $result->paginate($limit);
        		} else {
        			$data['result'] = $result->orderby($this->table.'.'.$this->primary_key, 'desc')->paginate($limit);
        		}
        	}

        	$data['columns'] = $columns_table;

        	if ($this->index_return) {
        		return $data;
        	}

        //LISTING INDEX HTML
        	$addaction = $this->data['addaction'];

        	if ($this->sub_module) {
        		foreach ($this->sub_module as $s) {
        			$table_parent = CRUDBooster::parseSqlTable($this->table)['table'];
        			$addaction[] = [
        				'label' => $s['label'],
        				'icon' => $s['button_icon'],
        				'url' => CRUDBooster::adminPath($s['path']).'?return_url='.urlencode(Request::fullUrl()).'&parent_table='.$table_parent.'&parent_columns='.$s['parent_columns'].'&parent_columns_alias='.$s['parent_columns_alias'].'&parent_id=['.(! isset($s['custom_parent_id']) ? "id" : $s['custom_parent_id']).']&foreign_key='.$s['foreign_key'].'&label='.urlencode($s['label']),
        				'color' => $s['button_color'],
        				'showIf' => $s['showIf'],
        			];
        		}
        	}

        	$mainpath = CRUDBooster::mainpath();
        	$orig_mainpath = $this->data['mainpath'];
        	$title_field = $this->title_field;
        	$html_contents = [];
        	$page = (Request::get('page')) ? Request::get('page') : 1;
        	$number = ($page - 1) * $limit + 1;
        	foreach ($data['result'] as $row) {
        		$html_content = [];

        		if ($this->button_bulk_action) {

        			$html_content[] = "<input type='checkbox' class='checkbox' name='checkbox[]' value='".$row->{$tablePK}."'/>";
        		}

        		if ($this->show_numbering) {
        			$html_content[] = $number.'. ';
        			$number++;
        		}

        		foreach ($columns_table as $col) {
        			if ($col['visible'] === false) {
        				continue;
        			}

        			$value = @$row->{$col['field']};
        			$title = @$row->{$this->title_field};
        			$label = $col['label'];

        			if (isset($col['image'])) {
        				if ($value == '') {
        					$value = "<a  data-lightbox='roadtrip' rel='group_{{$table}}' title='$label: $title' href='".asset('vendor/crudbooster/avatar.jpg')."'><img width='40px' height='40px' src='".asset('vendor/crudbooster/avatar.jpg')."'/></a>";
        				} else {
        					$pic = (strpos($value, 'http://') !== false) ? $value : asset($value);
        					$value = "<a data-lightbox='roadtrip'  rel='group_{{$table}}' title='$label: $title' href='".$pic."'><img width='40px' height='40px' src='".$pic."'/></a>";
        				}
        			}

        			if (@$col['download']) {
        				$url = (strpos($value, 'http://') !== false) ? $value : asset($value).'?download=1';
        				if ($value) {
        					$value = "<a class='btn btn-xs btn-primary' href='$url' target='_blank' title='Download File'><i class='fa fa-download'></i> Download</a>";
        				} else {
        					$value = " - ";
        				}
        			}

        			if ($col['str_limit']) {
        				$value = trim(strip_tags($value));
        				$value = Str::limit($value, $col['str_limit']);
        			}

        			if ($col['nl2br']) {
        				$value = nl2br($value);
        			}

        			if ($col['callback_php']) {
        				foreach ($row as $k => $v) {
        					$col['callback_php'] = str_replace("[".$k."]", $v, $col['callback_php']);
        				}
        				@eval("\$value = ".$col['callback_php'].";");
        			}

                //New method for callback
        			if (isset($col['callback'])) {
        				$value = call_user_func($col['callback'], $row);
        			}

        			$datavalue = @unserialize($value);
        			if ($datavalue !== false) {
        				if ($datavalue) {
        					$prevalue = [];
        					foreach ($datavalue as $d) {
        						if ($d['label']) {
        							$prevalue[] = $d['label'];
        						}
        					}
        					if ($prevalue && count($prevalue)) {
        						$value = implode(", ", $prevalue);
        					}
        				}
        			}

        			$html_content[] = $value;
            } //end foreach columns_table

            if ($this->button_table_action):

            	$button_action_style = $this->button_action_style;
            	$html_content[] = "<div class='button_action' style='text-align:right'>".view('crudbooster::components.action', compact('addaction', 'row', 'button_action_style', 'parent_field'))->render()."</div>";

            endif;//button_table_action

            foreach ($html_content as $i => $v) {
            	$this->hook_row_index($i, $v);
            	$html_content[$i] = $v;
            }

            $html_contents[] = $html_content;
        } //end foreach data[result]

        $html_contents = ['html' => $html_contents, 'data' => $data['result']];

        $data['html_contents'] = $html_contents;

        return view("sub_user.index", $data);
    }

	public function getAdd()
	{
		$this->cbLoader();
		if ( !CRUDBooster::isCreate() && $this->global_privilege == false ) {
			CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
			CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
		}

		$page_title = __("Sub Account Add Page");
		$page_menu = Route::getCurrentRoute()->getActionName();
		$command = 'add';

		return view('crudbooster::default.form', compact('page_title', 'page_menu', 'command'));
	}

    public function getKeyAccountManager(){
		$user_id = CRUDBooster::myId();
		$managers = $this->userManagerData($user_id);
    	return view('sub_user.manager.modal', compact('managers'));
    }


    public function postKeyManagers(){
    	$user_id = CRUDBooster::myId();
		$managers = $this->userManagerData($user_id);
		return view('sub_user.manager.table', compact('managers'));
    }


    public function getSearchManager()
    {
    	$value = $_REQUEST['value'];
    	$result = DB::table('cms_users')
				->where('id_cms_privileges', '=', 8)
				->whereNotNull('status')
				->whereNotNull('email_verified_at')
				->where(function($q) use($value){
					return $q->where('name', 'LIKE', '%'.$value.'%')->orWhere('email', 'LIKE', '%'.$value.'%');
				})
				->select('name', 'email', 'id')
				->take(10)
				->get()
				->map(function($item){
					return [
						'text' => $item->name .' - '.$item->email,
						'id' => $item->id
					];
				})
				->toArray();

    	return response()->json($result);

    }


    private function userManagerData($user_id)
    {

    	$cahe_key = 'user_manager_data_'.$user_id;
		return Cache::rememberForever($cahe_key, function() use ($user_id) {

	    	$managers = [];
	    	$managers_data = DB::table('user_manager')->where('user_id', '=', $user_id)->select('manager_id', 'status', 'priority')->get()->keyBy('manager_id');

	    	if(!empty($managers_data)){
	    		$managers_id = collect($managers_data)->pluck('manager_id')->unique()->toArray();
	    		$managers = DB::table('cms_users')->whereIntegerInRaw('id', $managers_id)->select('name', 'id', 'email')->get()->map(function($item) use($managers_data) {

	    			$status = $managers_data[$item->id]->status;
	    			$priority = $managers_data[$item->id]->priority;

	    			$sort_val = 10;

	    			if($status === 1){
	    				$sort_val = 11;
	    			}

	    			if($priority === 1){
	    				$sort_val += 1;
	    			}

	    			return (object) [
	    				'name' => $item->name,
	    				'id' => $item->id,
	    				'email' => $item->email,
	    				'status' => $status,
	    				'priority' => $priority,
	    				'sort_val' => $sort_val,
	    			];
	    		})
	    		->sortByDesc('sort_val');
	    	}

	    	return $managers;
		});
    }



    //User appointment Id
    public function userAppointmentManagerId($user_id)
    {

    	$manager_id = $this->defaultBookingManager();

    	$data = $this->userManagerData($user_id);
    	$manager = collect($data)->firstWhere('status', '=', 1);
    	if(!empty($manager)){
    		$manager_id = $manager->id;
    	}

    	return $manager_id;
    }


    //Calendar default manager
    public function defaultBookingManager()
    {
		$isDt = \Session::has('is_dt_user') && \Session::get('is_dt_user');
        if($isDt) return 2439; // DT account for DT default manager

        return 2944; //Partick sub account manager as default manager
    }



    //User Manager ID
    public function userHasManagerId($user_id)
    {
    	$data = $this->userManagerData($user_id);
    	$manager = collect($data)->firstWhere('status', '=', 1);
    	if(!empty($manager)){
    		return true;
    	}

    	return false;
    }

        //Mentor active
        public function isMentorActive($user_id, $mentot_type)
        {
            $column_name = ($mentot_type == "manager") ? 'manager_switch' : 'switch';
            $mentor = DB::table('takeappointment')->where('user_id', '=', $user_id)->where($column_name, 1)->exists();
            return ($mentor) ?  true :  false;
        }


    //Post add manager
    public function postAddManager(){

    	try{
    		$manager_id = $_REQUEST['manager_id'];
    		$user_id = CRUDBooster::myId();

    		if(empty($manager_id)) throw new \Exception('Plesae select key-account!');

    		$manager = DB::table('cms_users')->where('id', '=', $manager_id)->select('status', 'id_cms_privileges', 'email_verified_at')->first();
    		if(empty($manager)) throw new \Exception('Key-Account not exists!');
    		if(empty($manager->status) || empty($manager->email_verified_at) || ($manager->id_cms_privileges != 8) ) throw new \Exception('Invalid Key-Account!');

    		$exists = DB::table('user_manager')->where('user_id', '=', $user_id)->where('manager_id', '=', $manager_id)->exists();
    		if($exists) throw new \Exception('Already exists!');

    		$inserted = DB::table('user_manager')->insert([
    			'user_id' => $user_id,
    			'manager_id' => $manager_id,
    			'created_at' => now(),
    			'updated_at' => now()
    		]);

    		if($inserted){
    			$cahe_key = 'key_manager_id_'.$manager_id;
    			Cache::forget($cahe_key);
    			Cache::forget('key_manager_all_id_'.$manager_id);
    			Cache::forget('user_manager_data_'.$user_id);

    			$managers = $this->userManagerData($user_id);
    			$html = view('sub_user.manager.table', compact('managers'))->render();

    			return response()->json([
    				'success' => true,
    				'message' => 'Key-Account added successfully!',
    				'html'	=> $html
    			], 201);
    		}

    		throw new \Exception('Key-Account added failed!');

    	}catch(\Exception $e){
    		return response()->json([
    			'success' => false,
    			'message' => $e->getMessage()
    		], 402);
    	}
    }

    //Update manager status
    public function postUpdateManagerStatus(){

    	try{
    		$manager_id = $_REQUEST['manager_id'];
    		$status = (int)$_REQUEST['status'];
    		$user_id = CRUDBooster::myId();
    		if(empty($manager_id)) throw new \Exception('Invalid action!');

    		$exists = DB::table('user_manager')->where('user_id', '=', $user_id)->where('manager_id', '=', $manager_id)->exists();
    		if(!$exists) throw new \Exception('Key-Account not exists!');

    		$success = false;
    		$message = '';

    		if($status === 3){
    			DB::table('user_manager')->where('user_id', '=', $user_id)->where('priority', '=', 1)->update(['priority' => 0]);
    			$top_priority = DB::table('user_manager')->where('user_id', '=', $user_id)->where('manager_id', '=', $manager_id)->update(['priority' => 1]);
    			if($top_priority){
    				$success = true;
    				$message = 'Top priority changed successfully!';
    			}else{
    				$message = 'Top priority changed failed!';
    			}
    		}elseif($status === 2){
    			$deleted = DB::table('user_manager')->where('user_id', '=', $user_id)->where('manager_id', '=', $manager_id)->delete();
    			if($deleted){
    				$success = true;
    				$message = 'Key-Account deleted successfully!';
    			}else{
    				$message = 'Key-Account deleted failed!';
    			}
    		}elseif($status === 1){

    			$active_manager = [];
    			$active_manager['status'] = 1;

    			if(!DB::table('user_manager')->where('user_id', '=', $user_id)->where('priority', '=', 1)->exists()){
    				$active_manager['priority'] = 1;
    			}

    			$activate = DB::table('user_manager')->where('user_id', '=', $user_id)->where('manager_id', '=', $manager_id)->update($active_manager);

    			if($activate){
    				$success = true;
    				$message = 'Key-Account activate successfully!';
    			}else{
    				$message = 'Key-Account activate failed!';
    			}
    		}else{
    			$inactive = DB::table('user_manager')->where('user_id', '=', $user_id)->where('manager_id', '=', $manager_id)->update(['status' => 0, 'priority' => 0]);
    			if($inactive){
    				$success = true;
    				$message = 'Key-Account deactive successfully!';
    			}else{
    				$message = 'Key-Account deactive failed!';
    			}
    		}


    		if($success){
    			$cahe_key = 'key_manager_id_'.$manager_id;
    			Cache::forget($cahe_key);
    			Cache::forget('key_manager_all_id_'.$manager_id);
    			Cache::forget('user_manager_data_'.$user_id);

    			$managers = $this->userManagerData($user_id);
    			$html = view('sub_user.manager.table', compact('managers'))->render();

    			return response()->json([
    				'success' => true,
    				'message' => $message,
    				'html'	=> $html
    			], 200);
    		}

    		throw new \Exception($message);

    	}catch(\Exception $e){
    		return response()->json([
    			'success' => false,
    			'message' => $e->getMessage()
    		], 402);
    	}
    }


}
