<?php namespace App\Http\Controllers;

	use Session;
	use DB;
	use CRUDBooster;
	use Illuminate\Support\Facades\Storage;
	use File;
	use ZipArchive;
	use Maatwebsite\Excel\Facades\Excel;
	use App\Http\Controllers\AdminDrmImportsController;
	use Illuminate\Support\Facades\Cache;
	use App\UpcomingInvoice;
	use App\NewOrder;
	use App\User;
	use Carbon\Carbon;
	use Illuminate\Support\Str;
	use SoapBox\Formatter\Formatter;
	use Illuminate\Support\Facades\Validator;
	use PhpOffice\PhpSpreadsheet\IOFactory;
	use Request;
	use Illuminate\Support\Facades\Route;
	use Illuminate\Support\LazyCollection;
	use Toastr;
	use AppStore;
	use App\Jobs\AccountingArchiveJob;
	use App\Exports\InvoicesExport;
	use App\Traits\AccountingLoggable;

	class AdminUpcomingInvoicesBasicController extends \crocodicstudio\crudbooster\controllers\CBController {

		use AccountingLoggable;

		/**
		 * Get the CMS user ID based on special conditions
		 * 
		 * @return int
		 */
		protected function getCmsUserId()
		{
			return isPatrickSpecial() ? User::LAYAN_ACCOUNT_ID : \CRUDBooster::myParentId();
		}
		
		/**
		 * Static version of getCmsUserId for use in static methods
		 * 
		 * @return int
		 */
		protected static function getCmsUserIdStatic()
		{
			return isPatrickSpecial() ? User::LAYAN_ACCOUNT_ID : \CRUDBooster::myParentId();
		}

	    public function cbInit() {
	    	if(CRUDBooster::isSubUser() && !isPatrickSpecial()){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));}

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "invoice_number";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = true;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = true;
			$this->button_export = false;
			$this->table = "upcoming_invoices";
			$this->table_statement = "statements";

			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"Supplier","name"=>"delivery_company_id","join"=>"delivery_companies,name"];
			$this->col[] = ["label"=>"Invoice Number","name"=>"invoice_number"];
			$this->col[] = ["label"=>"Date","name"=>"date"];
			$this->col[] = ["label"=>"Due Date","name"=>"due_date"];
			$this->col[] = ["label"=>"Category","name"=>"category"];
			$this->col[] = ["label"=>"Status","name"=>"status"];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			$this->form[] = ['label'=>'Supplier','name'=>'delivery_company_id','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'delivery_companies,name'];
			$this->form[] = ['label'=>'Invoice Number','name'=>'invoice_number','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Date','name'=>'date','type'=>'datetime','validation'=>'required|date_format:Y-m-d','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Due Date','name'=>'due_date','type'=>'datetime','validation'=>'date_format:Y-m-d','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Type Of Issue','name'=>'type_of_issue','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Category','name'=>'category','type'=>'text','validation'=>'min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Status','name'=>'status','type'=>'text','validation'=>'min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Amount','name'=>'amount','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Total Amount','name'=>'total_amount','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Tax.php','name'=>'tax','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			$this->form[] = ['label'=>'Description','name'=>'description','type'=>'textarea','validation'=>'required|string|min:5|max:5000','width'=>'col-sm-10'];
			# END FORM DO NOT REMOVE THIS LINE

			/*
	        | ----------------------------------------------------------------------
	        | Sub Module
	        | ----------------------------------------------------------------------
			| @label          = Label of action
			| @path           = Path of sub module
			| @foreign_key 	  = foreign key of sub table/module
			| @button_color   = Bootstrap Class (primary,success,warning,danger)
			| @button_icon    = Font Awesome Class
			| @parent_columns = Sparate with comma, e.g : name,created_at
	        |
	        */
	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	//Your code here
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
			$postdata['user_id']=CRUDBooster::myParentId();
			Validator::make($_REQUEST, [
				'status' => 'required',
				'delivery_company_id' => 'required',
				'invoice_number' => 'required',
				'date' => 'required',
				'due_date' => 'nullable|required_if:status,unpaid',
				'tax' => 'required',
				'amount' => 'required',
				'file.*' => 'required|mimes:jpeg,bmp,png,gif,svg,pdf',
				'description' => 'nullable|string|min:5|max:5000',
			],[
				'delivery_company_id.required' => 'Supplier required',
			]
			)->validate();
			if($postdata['tax']){
				$postdata['tax']= removeCommaPrice($postdata['tax']);
			}
			if($postdata['amount']){
				$postdata['amount']=removeCommaPrice($postdata['amount']);
			}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {

	    	$user_id = CRUDBooster::myParentId();
        	$folder = 'finance/files/' . $user_id;

			$allowedfileExtension=['pdf','jpg','png','jpeg','PDF','JPG','PNG','JPEG'];
			if(is_array(request()->file)){
				foreach(request()->file as $file){
					if(!in_array($file->getClientOriginalExtension(),$allowedfileExtension)){
						continue;
					}
					$rand=Str::random(40);
					$extension = $file->getClientOriginalExtension();
        			$path = $file->storeAs($folder, $rand.'.'.$extension, ['visibility'=>'public', 'disk'=>'dropmatix']);
					$file_path = Storage::disk('dropmatix')->url($path);
					DB::table('upcoming_invoice_documents')->insert([
						"upcoming_invoice_id"=>$id,
						"file"=>$file_path
					]);
				}
			}


			$this->insertChangeLog($id, "Invoice added.");
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
			$postdata['user_id']=CRUDBooster::myParentId();
			Validator::make($_REQUEST, [
				'status' => 'required',
				'delivery_company_id' => 'required',
				'invoice_number' => 'required',
				'date' => 'required',
				'due_date' => 'nullable|required_if:status,unpaid',
				'tax' => 'required',
				'amount' => 'required',
				'file.*' => 'nullable|mimes:jpeg,bmp,png,gif,svg,pdf',
				'description' => 'nullable|string|min:5|max:5000',
			],[
				'delivery_company_id.required' => 'Supplier required',
			]
			)->validate();
			if($postdata['tax']){
				$postdata['tax']= removeCommaPrice($postdata['tax']);
			}
			if($postdata['amount']){
				$postdata['amount']=removeCommaPrice($postdata['amount']);
			}
		}
		public function getDeleteFile($id){
			$data= DB::table('upcoming_invoice_documents')->where('id',$id)->delete();
		}

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {

	    	$user_id = CRUDBooster::myParentId();
        	$folder = 'finance/files/' . $user_id;

			if(request()->delete_f_lists && is_array(request()->delete_f_lists) ){
				foreach (request()->delete_f_lists as $file_id) {
					DB::table('upcoming_invoice_documents')->where('id', $file_id)->where('upcoming_invoice_id', $id)->delete();
				}
			}
	        $allowedfileExtension=['pdf','jpg','png','jpeg','PDF','JPG','PNG','JPEG'];
			if(is_array(request()->file)){
				foreach(request()->file as $file){
					if(!in_array($file->getClientOriginalExtension(),$allowedfileExtension)){
						continue;
					}
					$rand=Str::random(40);
					$extension = $file->getClientOriginalExtension();
					$path = $file->storeAs($folder, $rand.'.'.$extension, ['visibility'=>'public', 'disk'=>'dropmatix']);
					$file_path = Storage::disk('dropmatix')->url($path);
					DB::table('upcoming_invoice_documents')->insert([
						"upcoming_invoice_id"=>$id,
						"file"=>$file_path
					]);
				}
			}
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {

			DB::table("upcoming_invoice_documents")->where('upcoming_invoice_id',$id)->delete();
			\App\AccountingInvoiceLog::where('invoice_id', $id)->delete();
		}


		public function getIndex()
		{

			redirectToV2('/accounting');
			
			// $basic_check  = AppStore::CheckAppPurchaseBoolean('12');

			// if(!$basic_check[0]){
			// 	CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
			// }else{
			// 	$app_details = $basic_check[1];
			// }

			if(CRUDBooster::isSubUser() && !isPatrickSpecial()){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));}

			$cms_user_id = self::getCmsUserId();
			$agreementProgress = [];
            $mpAgreement = new \App\Services\DRM\MpAgreement(\CRUDBooster::myParentId());
			if($mpAgreement->isActive())
            {
                $agreementProgress = [
                    'used' => $mpAgreement->getConsumedAmount(),
                    'progress' => $mpAgreement->getProgress(),
                    'max' => $mpAgreement->getMaxAmount(),
                    'due_days' => $mpAgreement->getDueDays(),
                ];
            }

			$q=request()->q;
			$from=request()->from;
			$to=request()->to;
			$todate=$to;
			// $to=Carbon\Carbon::parse($to)->addDay(1);
			$category=request()->category;
			$supplier=request()->supplier;
			$amount = 0;

			$upcomming_amount = 0;
			$currency_data = [];


			$page="all";

			if(request()->p!=null){
			  $page=request()->p;
			}
			$due_invoices=UpcomingInvoice::where('status','unpaid')->where('user_id',$cms_user_id)->whereDate('due_date','<',date('Y-m-d'))->orderBy("date","desc");

			if($q!=null){
			  $due_invoices->where('invoice_number','like', $q);
			}

			if($from!=null ){
			  if($to==null){
				$due_invoices->whereBetween('date',[$from,now()]);
			  }else{
				$due_invoices->whereBetween('date',[$from,$to]);
			  }
			}

			if($category!=null){
			  $due_invoices->where("category",$category);
			}

			if($supplier!=null){
			  $due_invoices->where("delivery_company_id",$supplier);
			}

			$upcomming_amount = $due_invoices->sum('amount');
			$due_invoices=$due_invoices->get();
			switch($page){


            case "ongoing":
				$orders = NewOrder::where('cms_user_id', $cms_user_id)->where('status', '!=', 'Canceled')
				->where('credit_number', '=', 0)
				->whereNull('credit_ref')
                ->where('invoice_number', '!=', -1)->orderByRaw('CAST(invoice_number AS SIGNED) desc')
                ->orderByRaw('CAST(order_date AS datetime) desc');

				if (isPatrickSpecial()) {
					$orders->whereIn('cms_client', User::PATRICK_SPECIAL_USERS);
				}

					if($q!=null){
						$orders->where('invoice_number','like', $q);
					}


					if($from!=null ){
						if($to==null){

							$orders->whereBetween('order_date',[$from,now()]);
						}else{

							$orders->whereBetween('order_date',[$from,$to]);
						}
					}

					if($supplier || $category){
						$orders->where('id', '<', 0);
					}

					$currency_data = orderGoupDataToCurrency($orders);
					$orders = $orders->get();
                	break;

				case "due":

						$orders = NewOrder::where('cms_client', $cms_user_id)
						->where('client_locked', '<>', 1)
                        ->where('credit_number','=',0)
                        ->whereIn('status', ['mahnung', 'inkasso'])
		                ->orderByRaw('CAST(credit_number AS SIGNED) desc')
		                ->orderByRaw('CAST(order_date AS datetime) desc');

						if($q!=null) {
							$orders->where('invoice_number', 'like', $q);
						}
						if($from!=null ) {
							if($to==null)
							{
								$orders->whereBetween('order_date', [$from, now()]);
							} else {
								$orders->whereBetween('order_date', [$from, $to]);
							}
						}
						if($category!=null){
							$orders->where('id', '<', 0);
						}
						if($supplier!=null){
							$orders->where('id', '<', 0);
						}

						$upcomming_amount += $orders->sum('total');
			            $due_orders = $orders->get();
				  		$invoices = $due_invoices;
				  		$orders = $due_orders;

				 	break;

				case "all_incoming":

					$invoices=UpcomingInvoice::where('user_id',$cms_user_id)->orderBy("date","desc");
					$orders = NewOrder::where('cms_client', $cms_user_id)
					->where('client_locked', '<>', 1)
	                ->where('credit_number','=',0)
	                ->orderByRaw('CAST(credit_number AS SIGNED) desc')
	                ->orderByRaw('CAST(order_date AS datetime) desc');
	                // ->where('cms_user_id', '=', 2455);
	                // ->where('insert_type', '=', 8);

					if($q!=null){
					$invoices->where('invoice_number','like', $q);
					$orders->where('invoice_number', 'like', $q);
					}

					if($from!=null )
					{
					if($to==null)
					{
						$invoices->whereBetween('date',[$from,now()]);
						$orders->whereBetween('order_date', [$from, now()]);
					}
					else
					{
						$invoices->whereBetween('date',[$from,$to]);
						$orders->whereBetween('order_date', [$from, $to]);
					}
					}

					if($category!=null){
					$invoices->where("category",$category);
					$orders->where('id', '<', 0);
					}
					if($supplier!=null){
					$invoices->where("delivery_company_id",$supplier);
					$orders->where('id', '<', 0);
					}

					$upcomming_amount = $invoices->sum('amount');
					$currency_data = orderGoupDataToCurrency($orders);
					$invoices=$invoices->get();
					$orders = $orders->get();

					break;
				default:
					$invoices=UpcomingInvoice::where('user_id',$cms_user_id)->orderBy("date","desc");

					$orders = NewOrder::where('invoice_number', '!=', -1)
					->where('client_locked', '<>', 1)
					->where(function($subq) use($cms_user_id){
		                $subq->where('cms_user_id', $cms_user_id)
		                ->orWhere(function ($query) use($cms_user_id) {
		                    $query->where('cms_client', $cms_user_id);
		                    // ->where('credit_number','=',0)
		                    // ->where('cms_user_id', '=', 2455)
		                    // ->where('insert_type', '=', 8);
		                });
		            })
					->orderByRaw('CAST(invoice_number AS SIGNED) desc')
					->orderByRaw('CAST(order_date AS datetime) desc');

					if (isPatrickSpecial()) {
						$orders->whereIn('cms_client', User::PATRICK_SPECIAL_USERS);
					}

					if($q!=null){
						$orders->where('invoice_number','like', $q);
						$invoices->where('invoice_number','like', $q);
					}

					// if($quarter!=null ){
					// 	$invoices->whereBetween('date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);
					// 	$orders->whereBetween('order_date',[date('Y').'-'.$firstMonth.'-01',date('Y').'-'.$lastMonth.'-'.$lastDayOfMonth]);

					// }else{
						if($from!=null ){
							if($to==null){

								$orders->whereBetween('order_date',[$from,now()]);
								$invoices->whereBetween('date',[$from,now()]);
							}else{

								$orders->whereBetween('order_date',[$from,$to]);
								$invoices->whereBetween('date',[$from,$to]);
							}
						}
					// }

					if($category!=null){$invoices->where("category",$category);}

					if($supplier!=null){$invoices->where("delivery_company_id",$supplier);}
					if($supplier || $category){
						$orders->where('id', '<', 0);
					}

					$currency_data = orderGoupDataToCurrency($orders);
					$upcomming_amount = $invoices->sum('amount');

					$orders = $orders->get();
					$invoices = $invoices->get();

					$orCount=0; $invCount=0;
            	break;
			}

			if(isset($currency_data['EUR'])){
				$currency_data['EUR'] = ($currency_data['EUR'] + $upcomming_amount);
			}else{
				$currency_data['EUR'] = $upcomming_amount;
			}

			$pro_check = AppStore::CheckAppPurchaseBoolean('36');
	        $pro_active = $pro_check[0] ?? false;

			if(isset($_GET['p']) && $_GET['p'] == 'income_vs_expenses'){
				$page_title = ' ';
			}else{
				$page_title = __('Incoming Invoices Basic');
			}

			$orCount=0; $invCount=0;

			$hide_title_and_icon = true;

			$mp_admin_id =  isPatrickSpecial() ? 4219 : 2455;
			$agreement_orders_due_tomorrow = NewOrder::join('mp_payment_agreements', 'mp_payment_agreements.user_id', '=', 'new_orders.cms_client')
				->where('new_orders.insert_type', 8)
				->where('new_orders.test_order', '<>', 1)
				->where('new_orders.invoice_number', '>', 0)
				->whereNull('new_orders.remainder_date')
				->whereNull('new_orders.credit_ref')
				->whereNotNull('new_orders.mp_payment_agreement_at')
				->where('new_orders.marketplace_paid_status', '<>', 1)
				->where('new_orders.cms_user_id', '=', $mp_admin_id)
				->where('new_orders.cms_client', '=', $cms_user_id)
				->whereNotNull('new_orders.marketplace_order_ref')
				->whereRaw('DATEDIFF(NOW(), new_orders.created_at) = mp_payment_agreements.due_days')
				->select('new_orders.id', 'new_orders.invoice_number', 'new_orders.eur_total')
				->orderBy('id', 'desc')
				->get();

			$mp_prepayment_due_orders = NewOrder::where('cms_user_id', '=', $mp_admin_id)
				->where('cms_client', $cms_user_id)
				->where('test_order', '<>', 1)
				->where('invoice_number', '>', 0)
				->whereNotNull('remainder_date')
				->whereNull('credit_ref')
				->whereNull('mp_payment_agreement_at')
				->whereNotNull('marketplace_order_ref')
				->where('marketplace_paid_status', '<>', 1)
				->whereDate('remainder_date', '<', now())
				->select('id', 'invoice_number', 'eur_total')
				->orderBy('id', 'desc')
				->get();
			
			$has_proforma_invoices = $orders->whereNotNull('proforma_number')->count();

			$sale_progress = $this->userSaleProgressData();

			return view("admin.accounting.basic.index",compact('q','from','to','today','category','supplier','due_invoices', 'due_orders', 'page','invoices', 'orders', 'orCount', 'invCount', 'amount', 'currency_data', 'pro_active', 'page_title', 
				'agreementProgress', 'hide_title_and_icon', 'agreement_orders_due_tomorrow', 'mp_prepayment_due_orders', 'has_proforma_invoices', 'sale_progress'));
			//'app_details',
		}

		private function userSaleProgressData() {
			$sale_amount = calculateUserSalesAmount();
			$sell_progress = $this->calculateUserSellProgress($sale_amount);
			
			$progress_data = [
				'min' => $sell_progress['min'],
				'max' => $sell_progress['max'],
				'sale' => ($sale_amount > 0) ? $sale_amount : 0,
				'progress' => $sell_progress['progress'],
				'award_won_lists' => $sell_progress['award_won_lists'],
			];

			return $progress_data;
		}
	
		private function calculateUserSellProgress($sale_amount) {
			$min_amount = $progress = 0;
			$target = 10000;
			$award_won_lists = [];
			
			if ($sale_amount > 0) {
				$sales_arr = [
					10000   => '10K',
					100000  => '100K',
					250000  => '250K',
					500000  => '500K',
				];
				
				foreach ($sales_arr as $key => $short_name) {
					if ($sale_amount < $key) {
						$target = $key;
						$progress = ($sale_amount / $target) * 100;
						// $progress = (($sale_amount - $min_amount) / ($key - $min_amount)) * 100;
						
						break;
					}
	
					$min_amount = $key;
					$award_won_lists[$key] = $short_name;

					if ($sale_amount >= 500000) {
						$progress = 100;
						$target = 500000;
					}
				}
			}
	
			return [
				'min' => $min_amount,
				'max' => $target,
				'progress' => number_format($progress, 1),
				'award_won_lists' => $award_won_lists,
			];
		}

		public function getDownload()
		{
			// $basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			// if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }

			if(CRUDBooster::isSubUser() && !isPatrickSpecial()){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));}

			$cms_user_id = self::getCmsUserId();
			$user = User::find($cms_user_id);
			$q=request()->q;
			$from=request()->from;
			$to=request()->to;
			// $to=\Carbon\Carbon::parse($to)->addDay(1);
			$category=request()->category;
			$supplier=request()->supplier;
			$page=request()->p;

			if($page==null)
			{
				$page="all";
			}
			if($page=="all_incoming" || $page=="due")
			{
				$invoices=UpcomingInvoice::where('user_id',$cms_user_id);
				$orders = NewOrder::where('cms_client', $cms_user_id)
                ->where('credit_number','=',0)
                ->orderByRaw('CAST(credit_number AS SIGNED) desc')
                ->orderByRaw('CAST(order_date AS datetime) desc')
                ->where('client_locked', '<>', 1);
                // ->where('cms_user_id', '=', 2455)
                // ->where('insert_type', '=', 8);

				if($page=="due")
				{
					$invoices->where('status','unpaid')->whereDate('due_date','<',date('Y-m-d'));
					
					$orders = NewOrder::where('cms_client', $cms_user_id)
                        ->where('credit_number','=',0)
                        ->where('client_locked', '<>', 1)
                        ->whereIn('status', ['mahnung', 'inkasso'])
		                ->orderByRaw('CAST(credit_number AS SIGNED) desc')
		                ->orderByRaw('CAST(order_date AS datetime) desc');
				}
				if($q!=null)
				{
					$invoices->where('invoice_number','like', $q);
					$orders->where('invoice_number', 'like', $q);
				}

				if($from!=null )
				{
					if($to==null)
					{
					  $invoices->whereBetween('date',[$from,now()]);
					  $orders->whereBetween('order_date', [$from, now()]);
					}
					else
					{
					  $invoices->whereBetween('date',[$from,$to]);
					  $orders->whereBetween('order_date', [$from, $to]);
					}
				}

				if($category!=null)
				{
					$invoices->where("category",$category);
					$orders->where('id', '<', 0);
				}

				if($supplier!=null)
				{
					$invoices->where("delivery_company_id",$supplier);
					$orders->where('id', '<', 0);
				}

				$invoices=$invoices->pluck('id')->toArray();
				$orders = $orders->where('is_locked', '<>', 1)->pluck('id')->toArray();

				if(count($invoices) || count($orders)){
					AccountingArchiveJob::dispatch($user, ['order' => $orders, 'invoice' => $invoices ])->onQueue('file-archive');
					return redirect()->back()->with(['message' => 'Archive processing! After completing the process, we sent you notification!', 'message_type' => 'success']);
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to archive.', 'message_type' => 'warning']);
				}
			}
			elseif($page=='ongoing')
			{
				$orders = NewOrder::where('cms_user_id', $cms_user_id)->where('status', '!=', 'Canceled')
				->where('credit_number', '=', 0)
				->whereNull('credit_ref')
                ->where('invoice_number', '!=', -1)->orderByRaw('CAST(invoice_number AS SIGNED) desc')
                ->orderByRaw('CAST(order_date AS datetime) desc');

				if (isPatrickSpecial()) {
					$orders->whereIn('cms_client', User::PATRICK_SPECIAL_USERS);
				}

				if($q!=null)
				{
					$orders->where('invoice_number','like', $q);
				}

				if($from!=null )
				{
					if($to==null)
					{
						$orders->whereBetween('order_date',[$from,now()]);
					}
					else
					{
						$orders->whereBetween('order_date',[$from,$to]);
					}
				}

				if($supplier || $category){
					$orders->where('id', '<', 0);
				}

				$orders = ($supplier || $category)? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->where('is_locked', '<>', 1)->pluck('id')->toArray();
				if(count($orders)){
					AccountingArchiveJob::dispatch($user, ['order' => $orders, 'invoice' => [] ])->onQueue('file-archive');
					return redirect()->back()->with(['message' => 'Archive processing! After completing the process, we sent you notification!', 'message_type' => 'success']);
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to archive.', 'message_type' => 'warning']);
				}
			}
			elseif($page=="all")
			{
				$invoices=UpcomingInvoice::where('user_id',$cms_user_id);
				$orders = NewOrder::where('invoice_number', '!=', -1)
				->where(function($subq) use ($cms_user_id){
	                $subq->where('cms_user_id', $cms_user_id)
	                ->orWhere(function ($query) use ($cms_user_id){
	                    $query->where('cms_client', $cms_user_id)
	                    // ->where('credit_number','=',0)
	                    ->where('client_locked', '<>', 1);
	                    // ->where('cms_user_id', '=', 2455)
	                    // ->where('insert_type', '=', 8);
	                });
	            })
				->orderBy("id","desc");

				if (isPatrickSpecial()) {
					$orders->whereIn('cms_client', User::PATRICK_SPECIAL_USERS);
				}

				if($q!=null)
				{
					$orders->where('invoice_number','like', $q);
					$invoices->where('invoice_number','like', $q);
				}

				if($from!=null )
				{
					if($to==null)
					{
						$invoices->whereBetween('date',[$from,now()]);
						$orders->whereBetween('order_date',[$from,now()]);
					}
					else
					{
						$orders->whereBetween('order_date',[$from,$to]);
						$invoices->whereBetween('date',[$from,$to]);
					}
				}

				if($category!=null)
				{
					$invoices->where("category",$category);
				}

				if($supplier!=null)
				{
					$invoices->where("delivery_company_id",$supplier);
				}

				$invoices=$invoices->pluck('id')->toArray();

				$orders = ($supplier || $category)? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->where('is_locked', '<>', 1)->pluck('id')->toArray();
				if(count($orders) || count($invoices) ){
					AccountingArchiveJob::dispatch($user, ['order' => $orders, 'invoice' => $invoices ])->onQueue('file-archive');
					return redirect()->back()->with(['message' => 'Archive processing! After completing the process, we sent you notification!', 'message_type' => 'success']);
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to archive.', 'message_type' => 'warning']);
				}
			}

			// return response()->download(public_path('invoice_files/'.$fileName))->deleteFileAfterSend(true);
		}


		public function postAjaxOcr()
		{
			$file =request()->data;
			$data=[
				'apikey'=>'e6d34f8c7512bccfc900fe3e556a3202',
                      'input'=>'base64',
                      'file'=>$file,
                      'filename'=>'ocr.pdf',
                      'outputformat'=>'txt',
			];
			$data=json_encode($data);
			$ch = curl_init('https://api.convertio.co/convert');
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);;


			curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
			$output = curl_exec($ch);
			$info = curl_getinfo($ch);
			curl_close($ch);
			$res=json_decode($output);

			if(!empty($res->data->id)){
				$fileData=file_get_contents("https://api.convertio.co/convert/".$res->data->id."/dl/base64");
				return $fileData;
			}

			abort(400);
		}


		private function generate_invoice_pdf($order_id)
        {
        	$pdf_path = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_invoice_pdf($order_id, true);
        	return $pdf_path;
		}

		public function getAdd()
		{
			redirectToV2('/accounting');

			// $basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			// if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }

			if(CRUDBooster::isSubUser() && !isPatrickSpecial()){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));}

			$data['page_title'] = __('Incoming Invoices Basic');

			return view("admin.accounting.basic.upcomming_invoice", $data);
		}

		public function getEdit($id)
		{
			// $basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			// if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }

			if(CRUDBooster::isSubUser() && !isPatrickSpecial()){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));}

			$invoice= UpcomingInvoice::where('id',$id)->first();
			return view("admin.accounting.basic.upcomming_invoice_edit",compact("invoice"));
		}

		public function getDetail($id)
		{
			redirectToV2('/accounting');

			// $basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			// if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }

			if(CRUDBooster::isSubUser() && !isPatrickSpecial()){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));}

			$invoice=UpcomingInvoice::with('invoice_category')->where('id',$id)->first();
			return view("admin.accounting.basic.upcomming_invoice_detail",compact('invoice'));
		}

		public function getExcel()
		{
			// $basic_check  = AppStore::CheckAppPurchaseBoolean('12');
			// if(!$basic_check[0]){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access")); }

			if(CRUDBooster::isSubUser() && !isPatrickSpecial()){ CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));}
			
			$user_id = self::getCmsUserId();
			$user = User::find($user_id);
			$q=request()->q;
			$from=request()->from;
			$to=request()->to;
			// $to=\Carbon\Carbon::parse($to)->addDay(1);
			$category=request()->category;
			$supplier=request()->supplier;
			$page=request()->p;

			if($page==null)
			{
				$page="all";
			}
			if($page=="all_incoming" || $page=="due")
			{
				$orders = NewOrder::where('cms_client', $user_id)
	            ->where('credit_number','=',0)
	            ->orderByRaw('CAST(credit_number AS SIGNED) desc')
	            ->orderByRaw('CAST(order_date AS datetime) desc')
	            ->where('client_locked', '<>', 1);
	            // ->where('cms_user_id', '=', 2455)
	            // ->where('insert_type', '=', 8);

				$invoices=UpcomingInvoice::where('user_id', $user_id);
				if($page=="due")
				{
					$invoices->where('status','unpaid')->whereDate('due_date','<',date('Y-m-d'));
					$orders->where('id', '<', 0);
				}
				if($q!=null)
				{
					$invoices->where('invoice_number','like', $q);
					$orders->where('invoice_number', 'like',  $q);
				}

				if($from!=null )
				{
					if($to==null)
					{
					  $invoices->whereBetween('date',[$from,now()]);
					  $orders->whereBetween('order_date', [$from, now()]);
					}
					else
					{
					  $invoices->whereBetween('date',[$from,$to]);
					  $orders->whereBetween('order_date', [$from, $to]);
					}
				}

				if($category!=null)
				{
					$invoices->where("category",$category);
					$orders->where('id', '<', 0);
				}

				if($supplier!=null)
				{
					$invoices->where("delivery_company_id",$supplier);
					$orders->where('id', '<', 0);
				}

				$invoices=$invoices->pluck('id')->toArray();
				$orders = $orders->where('is_locked', '<>', 1)->pluck('id')->toArray();

				if(count($invoices) || count($orders)){
					return $this->invoiceExcel( $user, ['order' => $orders, 'invoice' => $invoices ] );
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to download.', 'message_type' => 'warning']);
				}
			}
			elseif($page=='ongoing')
			{
				$orders=NewOrder::where('cms_user_id', $user_id)->where('status', '!=', 'Canceled')->where('invoice_number', '!=', -1)->orderBy("id","desc");

				if (isPatrickSpecial()) {
					$orders->whereIn('cms_client', User::PATRICK_SPECIAL_USERS);
				}

				if($q!=null)
				{
					$orders->where('invoice_number','like', $q);
				}

				if($from!=null )
				{
					if($to==null)
					{
						$orders->whereBetween('order_date',[$from,now()]);
					}
					else
					{
						$orders->whereBetween('order_date',[$from,$to]);
					}
				}

				$orders = ($supplier || $category)? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->where('is_locked', '<>', 1)->pluck('id')->toArray();
				if(count($orders)){
					return $this->invoiceExcel( $user, ['order' => $orders, 'invoice' => $invoices ] );
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to download.', 'message_type' => 'warning']);
				}
			}
			elseif($page=="all")
			{
				$invoices=UpcomingInvoice::where('user_id',$user_id);
				$orders = NewOrder::where('status', '!=', 'Canceled')
				->where('invoice_number', '!=', -1)
				->where('client_locked', '<>', 1)
				->where(function($subq) use ($user_id) {
	                $subq->where('cms_user_id', $user_id)
	                ->orWhere(function ($query) use ($user_id) {
	                    $query->where('cms_client', $user_id)
	                    ->where('client_locked', '<>', 1)
	                    ->where('credit_number','=',0);
	                    // ->where('cms_user_id', '=', 2455)
	                    // ->where('insert_type', '=', 8);
	                });
	            })
				->orderBy("id","desc");

				if (isPatrickSpecial()) {
					$orders->whereIn('cms_client', User::PATRICK_SPECIAL_USERS);
				}

				if($q!=null)
				{
					$orders->where('invoice_number','like', $q);
					$invoices->where('invoice_number','like', $q);
				}

				if($from!=null )
				{
					if($to==null)
					{
						$invoices->whereBetween('date',[$from,now()]);
						$orders->whereBetween('order_date',[$from,now()]);
					}
					else
					{
						$orders->whereBetween('order_date',[$from,$to]);
						$invoices->whereBetween('date',[$from,$to]);
					}
				}

				if($category!=null)
				{
					$invoices->where("category",$category);
				}

				if($supplier!=null)
				{
					$invoices->where("delivery_company_id",$supplier);
				}

				$invoices=$invoices->pluck('id')->toArray();

				$orders = ($supplier || $category)? $orders->where('id', '<', 0)->pluck('id')->toArray() : $orders->where('is_locked', '<>', 1)->pluck('id')->toArray();
				if(count($orders) || count($invoices) ){
					return $this->invoiceExcel( $user, ['order' => $orders, 'invoice' => $invoices ] );
				}else{
					return redirect()->back()->with(['message' => 'Sorry! No files to download.', 'message_type' => 'warning']);
				}
			}
		}

        private function invoiceExcel($user, $data = []){

			$user_id = $this->getCmsUserId();

			if(Excel::store(new InvoicesExport($data), 'upcomming/invoices_'.$user_id.'.xlsx')){
			    header('Content-type: application/xlsx');
			    header('Content-Disposition: attachment; filename="invoices.xlsx"');
			    readfile(storage_path('app/upcomming/invoices_'.$user_id.'.xlsx'));
			}
        }

		public function downloadSalesAward($award_amount)
		{
			try {
				$award_amount = abs($award_amount);

				$sales_amount = calculateUserSalesAmount();
				if (!empty($sales_amount) && ($sales_amount >= 10000) && $award_amount >= 10000) {
					$award_name = abs($award_amount / 1000) . 'k';
					$user_full_name = auth()->user()->name . ' ' . auth()->user()->last_name;

					$img = \Intervention\Image\Facades\Image::make('images/sales-awards/'. $award_amount .'.jpg');

					$img->text($user_full_name, 325, 615, function($font) {
						$font->file(public_path('/certificate/fonts/OpenSans-Bold.ttf'));
						$font->size(24);
						// $font->color('#FF0000');
						$font->align('center');
						$font->valign('top');
					});

					$headers = [
						'Content-Type' => 'image/png',
						'Content-Disposition' => 'attachment; filename=' . 'sales_award_' . $award_name . '.png',
					];

					return response()->stream(function() use ($img) {
						echo $img->encode('png');
					}, 200, $headers);
				} else {
					die('Your sales amount is not sufficient for sales award!');
				}

			} catch(\Exception $ex) {
				
			}
		}
	}
