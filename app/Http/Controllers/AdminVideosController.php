<?php namespace App\Http\Controllers;

	use Session;
	use Request;
	use DB;
	use CRUDBooster;
	use App\Video;
	use Illuminate\Support\Facades\Cache;
	use Illuminate\Support\Facades\Validator;
  	use Illuminate\Support\Facades\Route;
	use Illuminate\Validation\Rule;

	class AdminVideosController extends \crocodicstudio\crudbooster\controllers\CBController {

		protected $types = [];

	    public function cbInit() {
	    	//Without fabian or superadmin deny access
	    	if( (CRUDBooster::myId() != 98) && (!CRUDBooster::isSuperadmin()) ){
	    		CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
	    	}

	    	$this->types = config('help_video.types');

			# START CONFIGURATION DO NOT REMOVE THIS LINE
			$this->title_field = "name";
			$this->limit = "20";
			$this->orderby = "id,desc";
			$this->global_privilege = false;
			$this->button_table_action = true;
			$this->button_bulk_action = true;
			$this->button_action_style = "button_icon";
			$this->button_add = true;
			$this->button_edit = true;
			$this->button_delete = false;
			$this->button_detail = true;
			$this->button_show = true;
			$this->button_filter = true;
			$this->button_import = false;
			$this->button_export = false;
			$this->table = "videos";
			# END CONFIGURATION DO NOT REMOVE THIS LINE

			# START COLUMNS DO NOT REMOVE THIS LINE
			$this->col = [];
			$this->col[] = ["label"=>"ID","name"=>"id"];
			$this->col[] = ["label"=>"Name","name"=>"name"];
			$this->col[] = ["label"=>"Type","name"=>"type"];
			$this->col[] = ["label"=>"Option","name"=>"typeable_id"];
			$this->col[] = ["label"=>"Request Condition","name"=>"request_condition"];
			$this->col[] = ["label"=>"Url","name"=>"url"];
			$this->col[] = ["label"=>"Active","name"=>"active", "visible" => false];
			# END COLUMNS DO NOT REMOVE THIS LINE

			# START FORM DO NOT REMOVE THIS LINE
			$this->form = [];
			// $this->form[] = ['label'=>'Name','name'=>'name','type'=>'text','validation'=>'required|string|min:3|max:70','width'=>'col-sm-10','placeholder'=>'You can only enter the letter only'];
			// $this->form[] = ['label'=>'Type','name'=>'type','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Typeable Id','name'=>'typeable_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'typeable,id'];
			// $this->form[] = ['label'=>'Active','name'=>'active','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
			// $this->form[] = ['label'=>'Request Condition','name'=>'request_condition','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];

			// $this->form[] = ['label'=>'English','name'=>'english','type'=>'text','validation'=>'required|url','width'=>'col-sm-10','placeholder'=>'Please enter a valid english URL'];
			//
			// $this->form[] = ['label'=>'German','name'=>'german','type'=>'text','validation'=>'required|url','width'=>'col-sm-10','placeholder'=>'Please enter a valid german URL'];
			//
			// $this->form[] = ['label'=>'Spanish','name'=>'spanish','type'=>'text','validation'=>'required|url','width'=>'col-sm-10','placeholder'=>'Please enter a valid spanish URL'];
			# END FORM DO NOT REMOVE THIS LINE


	        $this->sub_module = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Action Button / Menu
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
	        | @icon        = Font awesome class icon. e.g : fa fa-bars
	        | @color 	   = Default is primary. (primary, warning, succecss, info)
	        | @showIf 	   = If condition when action show. Use field alias. e.g : [id] == 1
	        |
	        */
	        $this->addaction = array();
	        $this->addaction[] = ['label'=>'Set Active','url'=>CRUDBooster::mainpath('set-status/1/[id]'),'icon'=>'fa fa-check','color'=>'success','showIf'=>"[active] != 1"];
			$this->addaction[] = ['label'=>'Set Inactive','url'=>CRUDBooster::mainpath('set-status/0/[id]'),'icon'=>'fa fa-ban','color'=>'warning','showIf'=>"[active] == 1", 'confirmation' => true];


	        /*
	        | ----------------------------------------------------------------------
	        | Add More Button Selected
	        | ----------------------------------------------------------------------
	        | @label       = Label of action
	        | @icon 	   = Icon from fontawesome
	        | @name 	   = Name of button
	        | Then about the action, you should code at actionButtonSelected method
	        |
	        */
	        $this->button_selected = array();


	        /*
	        | ----------------------------------------------------------------------
	        | Add alert message to this module at overheader
	        | ----------------------------------------------------------------------
	        | @message = Text of message
	        | @type    = warning,success,danger,info
	        |
	        */
	        $this->alert        = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add more button to header button
	        | ----------------------------------------------------------------------
	        | @label = Name of button
	        | @url   = URL Target
	        | @icon  = Icon from Awesome.
	        |
	        */
	        $this->index_button = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Customize Table Row Color
	        | ----------------------------------------------------------------------
	        | @condition = If condition. You may use field alias. E.g : [id] == 1
	        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
	        |
	        */
	        $this->table_row_color = array();


	        /*
	        | ----------------------------------------------------------------------
	        | You may use this bellow array to add statistic at dashboard
	        | ----------------------------------------------------------------------
	        | @label, @count, @icon, @color
	        |
	        */
	        $this->index_statistic = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add javascript at body
	        | ----------------------------------------------------------------------
	        | javascript code in the variable
	        | $this->script_js = "function() { ... }";
	        |
	        */
	        $this->script_js = NULL;


            /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code before index table
	        | ----------------------------------------------------------------------
	        | html code to display it before index table
	        | $this->pre_index_html = "<p>test</p>";
	        |
	        */
	        $this->pre_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include HTML Code after index table
	        | ----------------------------------------------------------------------
	        | html code to display it after index table
	        | $this->post_index_html = "<p>test</p>";
	        |
	        */
	        $this->post_index_html = null;



	        /*
	        | ----------------------------------------------------------------------
	        | Include Javascript File
	        | ----------------------------------------------------------------------
	        | URL of your javascript each array
	        | $this->load_js[] = asset("myfile.js");
	        |
	        */
	        $this->load_js = array();



	        /*
	        | ----------------------------------------------------------------------
	        | Add css style at body
	        | ----------------------------------------------------------------------
	        | css code in the variable
	        | $this->style_css = ".style{....}";
	        |
	        */
	        $this->style_css = NULL;



	        /*
	        | ----------------------------------------------------------------------
	        | Include css File
	        | ----------------------------------------------------------------------
	        | URL of your css each array
	        | $this->load_css[] = asset("myfile.css");
	        |
	        */
	        $this->load_css = array();


	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for button selected
	    | ----------------------------------------------------------------------
	    | @id_selected = the id selected
	    | @button_name = the name of button
	    |
	    */
	    public function actionButtonSelected($id_selected,$button_name) {
	        //Your code here

	    }


	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate query of index result
	    | ----------------------------------------------------------------------
	    | @query = current sql query
	    |
	    */
	    public function hook_query_index(&$query) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate row of index table html
	    | ----------------------------------------------------------------------
	    |
	    */
	    public function hook_row_index($column_index,&$column_value) {
	    	$type_col = 3;
	    	$typable_col = 4;
	    	// $active_col = 7;

	    	if($type_col == $column_index){
	    		$type_id = $column_value;
	    		$this->type_data = $type_data = ($this->types)? $this->types[$type_id] : [];
	    		$column_value = (isset($type_data['name']))? $type_data['name'] : '';
	    	}

	    	if($typable_col == $column_index){
	    		$typable_id = $column_value;
	    		$type_data = $this->type_data?? [];
	    		$column_value = (is_array($type_data) && isset($type_data['data']))? $type_data['data'][$typable_id] : '';
	    	}

	    	// if($active_col == $column_index){
	    	// 	$color_class =  'danger';
	    	// 	$active = 'Inactive';

	    	// 	if($column_value == 1){
	    	// 		$color_class = 'success';
	    	// 		$active = 'Active';
	    	// 	}
	    	// 	$column_value = '<label class="text-'.$color_class.'">'.$active.'</label>';
	    	// }
	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before add data is execute
	    | ----------------------------------------------------------------------
	    | @arr
	    |
	    */
	    public function hook_before_add(&$postdata) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after add public static function called
	    | ----------------------------------------------------------------------
	    | @id = last insert id
	    |
	    */
	    public function hook_after_add($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for manipulate data input before update data is execute
	    | ----------------------------------------------------------------------
	    | @postdata = input post data
	    | @id       = current id
	    |
	    */
	    public function hook_before_edit(&$postdata,$id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after edit public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_edit($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command before delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_before_delete($id) {
	        //Your code here

	    }

	    /*
	    | ----------------------------------------------------------------------
	    | Hook for execute command after delete public static function called
	    | ----------------------------------------------------------------------
	    | @id       = current id
	    |
	    */
	    public function hook_after_delete($id) {
	        //Your code here

	    }

	    public function getSetStatus($status, $id) {
	    	// if(!in_array($status, [1, 0])){
	    	// 	return CRUDBooster::redirect(CRUDBooster::adminPath(), 'Invalid status!');
	    	// }
		   	DB::table('videos')->where('id',$id)->update(['active' => $status]);
		   	Cache::forget('drm_help_videos');
		   //This will redirect back and gives a message
		   CRUDBooster::redirect($_SERVER['HTTP_REFERER'],"The status has been updated !","info");
		}



	    //By the way, you can still create your own method in here... :)
			public function getAdd()
     {
         $this->cbLoader();
         if (! CRUDBooster::isCreate() && $this->global_privilege == false || $this->button_add == false) {
             CRUDBooster::insertLog(trans('crudbooster.log_try_add', ['module' => CRUDBooster::getCurrentModule()->name]));
             CRUDBooster::redirect(CRUDBooster::adminPath(), trans("crudbooster.denied_access"));
         }

         $page_title = trans("crudbooster.add_data_page_title", ['module' => CRUDBooster::getCurrentModule()->name]);
         $page_menu = Route::getCurrentRoute()->getActionName();
         $command = 'add';
				 $languages=config('help_video.languages');
				 $types=config('help_video.types');
         return view('admin.help_video.add', compact('languages', 'types', 'page_title', 'page_menu', 'command'));
     }

		 public function postAddSave()
		 {
			 $request =$_REQUEST;
			 $validator = Validator::make($request, [
					 'name' => 'required',
					 'url' => 'required',
					 'lang' => 'nullable|array',
					 'type' => 'required',
					 'typeable_id' => ['nullable', 'required_unless:type,1,0', Rule::unique('videos')->where(function ($query) use($request){
					     return $query->where(['type' => $request['type'], 'typeable_id' => $request['typeable_id']]);
					 })],
					 'request_condition' => 'nullable|required_if:type,1|unique:videos,request_condition',
			 ],[
				 'typeable_id.unique' => $this->typeInfo($request['type'], $request['typeable_id']) .' video already exist!',
			 ]);

			 if ($validator->fails()) {
					 return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
			 }

			  $video = new Video;
				$video->name = $request['name'];
				$video->request_condition=  $request['request_condition'];
				$video->url =  $request['url'];
				$video->lang = $request['lang'];
				$video->type =  $request['type'];
				$video->typeable_id = $request['typeable_id'];
				$video->save();

				Cache::forget('drm_help_videos');
			  CRUDBooster::redirect(CRUDBooster::adminPath('videos'), trans('Help video added'), 'success');
		 }
		 public function getEdit($id)
		 {
			 	$this->cbLoader();
		 		$row = DB::table($this->table)->where($this->primary_key, $id)->first();

			 	if (! CRUDBooster::isRead() && $this->global_privilege == false || $this->button_edit == false) {
			 			CRUDBooster::insertLog(trans("crudbooster.log_try_edit", [
			 					'name' => $row->{$this->title_field},
			 					'module' => CRUDBooster::getCurrentModule()->name,
			 			]));
			 			CRUDBooster::redirect(CRUDBooster::adminPath(), trans('crudbooster.denied_access'));
			 	}

			 	$page_menu = Route::getCurrentRoute()->getActionName();
			 	$page_title = trans("crudbooster.edit_data_page_title", ['module' => CRUDBooster::getCurrentModule()->name, 'name' => $row->{$this->title_field}]);
			 	$command = 'edit';
	      $data = Video::find($id);
				$languages = config('help_video.languages');

				$types=config('help_video.types');
			 	return view('admin.help_video.edit', compact('languages', 'types', 'data','id', 'row', 'page_menu', 'page_title', 'command'));
		 }
		 public function postEditSave($id)
     {
			 $request = $_REQUEST;
			 $validator = Validator::make($request, [
					 'name' => 'required',
					 'url' => 'required',
					 'lang' => 'nullable|array',
					 'type' => 'required',
					 'typeable_id' => ['nullable', 'required_unless:type,1,0', Rule::unique('videos')->where(function ($query) use($request, $id){
					 		return $query->where(['type' => $request['type'], 'typeable_id' => $request['typeable_id']])->whereNotNull('typeable_id')->where('id', '!=', $id);
					 })],
					 'request_condition' => 'nullable|required_if:type,1|unique:videos,request_condition,'.$id,
			 ],[
				 'typeable_id.unique' => $this->typeInfo($request['type'], $request['typeable_id']) .' video already exist!',
			 ]);

			 if ($validator->fails()) {
					 return redirect(Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
			 }

			  $video = Video::find($id);
				$video->name=   $request['name'];
				$video->request_condition=  $request['request_condition'];
				$video->url=  $request['url'];
				$video->lang = $request['lang'];
				$video->type = $request['type'];
				$video->typeable_id = $request['typeable_id'];
				$video->save();

				Cache::forget('drm_help_videos');
			  CRUDBooster::redirect(CRUDBooster::adminPath('videos'), trans('Help video Updated'), 'success');
		 }


		 private function typeInfo($type_id, $typable_id){
			 	$types = config('help_video.types');
			 	$type = $types[$type_id]?? [];
			 	$type_name = $type['name']?? '';
			 	$typable_name = $type['data'][$typable_id]?? '';
				return trim($typable_name.' '.$type_name);
		 }

	}
