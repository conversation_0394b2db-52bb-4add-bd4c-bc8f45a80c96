<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\AppStoreService;

class AppStoreController extends Controller
{
    public AppStoreService $service;

    public function __construct(AppStoreService $service)
    {
        $this->service = $service;
    }

    public function checkAppPurchased(Request $request): \Illuminate\Http\JsonResponse
    {
        $rules = ['app_id' => 'required','user_id'   => 'required'];

        $request->validate($rules);
        $data = $request->only(array_keys($rules));

        $result =  $this->service->checkAppPurchased($data['app_id'],$data['user_id']);

        return response()->json([
          'code'=>200,
          'status'=>'SUCCESS',
          'result'  => $result
        ]);
    }

    public function checkImportPlan(Request $request): \Illuminate\Http\JsonResponse
    {
        $rules = ['user_id' => 'required'];
        $request->validate($rules);
        $data = $request->only(array_keys($rules));
        $result =  $this->service->checkImportPlan($data['user_id']);
        return response()->json([
          'code'=>200,
          'status'=>'SUCCESS',
          'result'  => $result
        ]);
    }
}
