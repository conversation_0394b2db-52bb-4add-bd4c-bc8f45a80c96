<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\ChannelManager\ChangeChannelProductConnectionStatus;
use App\Models\ChannelProduct;
use Exception;
use Illuminate\Http\Request;
use App\Services\Modules\Export\ExportServices;
use App\Services\ChannelProductService;
use DB;

class ChannelRequestHandler extends Controller
{
    public function update(Request $request)
    {
        $request->validate($this->rules());
        $data = $request->only(array_keys($this->rules()));

        ExportServices::updateConnected($data['product_ids'],$data['user_id']);

        return response()->json([
          'code'=>200,
          'status'=>'SUCCESS'
        ]);
    }

    private function rules(): array
    {
        return [
            'user_id'  => 'required|numeric',
            'product_ids'  => 'required',
        ];
    }

    /**
     * @throws Exception
     */
    public function updateShopProducts(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate($this->rules());
        $data = $request->only(array_keys($this->rules()));

        $res = app(ChannelProductService::class)->updateShopProducts($data['product_ids'],$data['user_id']);

        return response()->json([
          'code'=>200,
          'status'=>'SUCCESS',
          'data' => $res
        ]);
    }

    public function changeConnectionStatus(Request $request)
    {
        $validData = $request->validate([
            'product_id' => 'required',
            'user_id' => 'required|numeric',
            'errors' => 'array|nullable'
        ]);

        $product = ChannelProduct::where([
            'id' => $validData['product_id'],
            'user_id' => $validData['user_id']
        ])->first();

        if ($product) {
            ChangeChannelProductConnectionStatus::dispatch($product->id, $validData['errors']);
        }

    }

    public function changeDtConnectionStatus(Request $request){
        $password = $request->header('userPassToken');
        $token = $request->header('userToken');
        $shop = \App\Shop::where('user', $token)->where('password', $password)->select('id', 'user_id')->first();
        if (empty($shop)) {
            return response()->json(['success' => false, 'message' => 'Shop does not exists!',
            ]);
        }

        $user_id = $shop->user_id;
        $password = $request->header('userPassToken');
        $dataIds = json_decode($_REQUEST['ids']);
        $dataType = $_REQUEST['type'];
        if($dataType == 'INSERT'){
            foreach($dataIds as $id){
                DB::table('channel_products')->where('user_id', $user_id)->where('id', $id)->update(['connection_status' => 1]);
            }
        }else if($dataType == 'DELETE'){
            foreach($dataIds as $id){
                DB::table('channel_products')->where('user_id', $user_id)->where('id', $id)->update(['connection_status' => 2]);
            }
        }
        return response()->json(['success' => true, 'message' => 'Data updated Successfully',
    ]);

    }
}
