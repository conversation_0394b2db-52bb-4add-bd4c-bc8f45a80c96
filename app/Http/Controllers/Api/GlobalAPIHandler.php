<?php

namespace App\Http\Controllers\Api;

use App\Models\ChannelProduct;
use App\Models\Marketplace\ProductSafetyGPSR;
use App\NewOrder;
use App\ReturnLabelCredit;
use http\Client\Curl\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Enums\ChannelProductConnectedStatus;
use App\Models\Marketplace\MarketplaceParentCategory;
use App\Services\Marketplace\NewWarehouseService;
use Illuminate\Support\Facades\Cache;
use Exception;

class GlobalAPIHandler extends \App\Http\Controllers\Controller
{
    public function shipcloudWebhook(Request $request)
    {
        try {

            $data = json_decode(request()->getContent(), true) ?? [];
            \Log::channel('command')->info($data);

            debug_log(json_encode($data),'SHIPCLOUD_DATA');
            if(empty($data['data'])) return;

            $payload = $data['data'] ?? [];
            $id = $payload['id'] ?? null;
            $event_type = isset($data['type']) ? $data['type'] : '';

            $token = config('shipcloud_return.token');

            $shipcloud = new \App\Services\Shipcloud\Shipcloud($token);
            $shipment = $shipcloud->shipments()->find($id);
            if($shipment['service'] !== 'returns') return;

            $userOrderId = \App\OrderTrackings::where('shipment_data->id', $id)
            ->whereNotNull('order_id')
            ->select('order_id')
            ->value('order_id');
            if(empty($userOrderId)) return;

            $packages = collect($shipment['packages'][0]['tracking_events'] ?? []);

            $picked_up = $packages->firstWhere('status', 'picked_up');
            if($picked_up){
                ReturnLabelCredit::where('order_id', $userOrderId)->update(['status' => 'picked up', 'type' => 1]);
                app(NewWarehouseService::class)->deliveryNoteProcess($userOrderId);
            }

            $transit = $packages->firstWhere('status', 'transit');
            if($transit){
                ReturnLabelCredit::where('order_id', $userOrderId)->update(['status' => 'transit', 'type' => 1]);
            }

            if(!($event_type === 'shipment.tracking.delivered')) return;

            $delivered = $packages->firstWhere('status', 'delivered');
            if(empty($delivered)) return;
            ReturnLabelCredit::where('order_id', $userOrderId)->update(['status' => 'delivered', 'type' => 1]);

            $status_update = 'Retoure eingegangen';
            $order = \App\NewOrder::where('marketplace_order_ref', $userOrderId)
            ->where('status', '<>', $status_update)
            ->where('credit_number', 0)
            ->where('cms_user_id', 2455)
            ->first();
            if(empty($order) || empty($order->id)) return;
            call_user_func('\App\Helper\OrderHelper::callerOfTrigger', $status_update, $order);

            $referencedOrder = NewOrder::find($userOrderId);
            if(empty($referencedOrder)) return;
            call_user_func('\App\Helper\OrderHelper' . '::callerOfTrigger', $status_update, $referencedOrder);

        } catch(\Exception $ge) {}
    }


    // Sync order return label status
    public function syncReturDeliverd($id)
    {
        $dropmatix_order_id = \App\NewOrder::where('id', $id)->value('order_id_api');
        $order_id = (int)str_replace('mp-return-', '', $dropmatix_order_id);

        $shipcloud_id =\App\OrderTrackings::whereNotNull('order_id')
        ->where('order_id', $order_id)
        ->orderBy('id', 'desc')
        ->select('shipment_data->id as shipcloud_id')
        ->value('shipcloud_id');
        if(empty($shipcloud_id)) return;
        $token = config('shipcloud_return.token');

        $shipcloud = new \App\Services\Shipcloud\Shipcloud($token);
        $shipment = $shipcloud->Shipments()->find($shipcloud_id);
        if($shipment['service'] !== 'returns') return;

        $delivered = collect($shipment['packages'][0]['tracking_events'] ?? [])->firstWhere('status', 'delivered');
        if(empty($delivered)) return;

        $status_update = 'Retoure eingegangen';
        $userOrderId = \App\OrderTrackings::where('order_id', $order_id)
        ->whereNotNull('order_id')
        ->select('order_id')
        ->value('order_id');
        if(empty($userOrderId)) return;

        $order = \App\NewOrder::where('marketplace_order_ref', $userOrderId)
        ->where('status', '<>', $status_update)
        ->where('credit_number', 0)
        ->where('cms_user_id', 2455)
        ->first();
        if(empty($order) || empty($order->id)) return;
        call_user_func('\App\Helper\OrderHelper::callerOfTrigger', $status_update, $order);

        $referencedOrder = \App\NewOrder::find($userOrderId);
        if(empty($referencedOrder)) return;
        call_user_func('\App\Helper\OrderHelper' . '::callerOfTrigger', $status_update, $referencedOrder);
    }

    public function createAgbLog(Request $request)
    {
        $data = $request->validate([
            'user_id' => ['required', 'numeric'],
            'agb' => ['required', 'array'],
            'change_log' => ['required', 'array'],
            'message' => ['required', 'string'],
            'ip_address' => ['required', 'string'],
        ]);

        $agb = is_string($data['agb']) ? json_decode($data['agb']) : (array) $data['agb'];
        $change_log = is_string($data['change_log']) ? json_decode($data['change_log']) : (array) $data['change_log'];

        try {
            create_agb_log($data['user_id'], $agb, $change_log, $data['message'], $data['ip_address']);
        } catch (\Exception $e) {
            return response(['message' => 'Your request has an error'], 400);
        }
        return response(['message' => 'AGB Log inserted successfully']);
    }

    //vat id check
    public function vatIdChecker()
    {
        $vatid = $_REQUEST['vat_id'];

        $isSkipCheck = false;
        if (isset($_REQUEST['country_id'])) {
            $isSkipCheck = (bool) (intval($_REQUEST['country_id']) === 83);
        } elseif (isset($_REQUEST['country_name'])) {
            $isSkipCheck = (bool) (strtolower($_REQUEST['country_name']) === 'switzerland');
        }

        return response()->json(\DRM::checkTaxNumber($vatid, $isSkipCheck));
    }

    //Product count
    public function importProductData(Request $request)
    {
        try {
            $userToken = $request->userToken;
            $userPassToken = $request->userPassToken;

            if (empty($userToken) || empty($userPassToken)) {
                throw new \Exception('Access denied - Token');
            }

            $shop = \App\Shop::where([
                'username' => $userToken,
                'password' => $userPassToken,
            ])->first();

            $user_id = $shop->user_id;
            $shop_id = $shop->id;

            $token = $request->token;
            if (($token != 'dtm3decr435mpdt') || empty($user_id)) {
                throw new \Exception('Access denied');
            }

            $plan = resolve('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
            $is_unlimited_product_plan = (isset($plan['plan_total']) && $plan['plan_total'] >= 9999999999) ? true : false;

            $banner = true;
            $free_500 = $plan['free_500'] ? true : false;
            $product = drmTotalProduct($user_id);
            if ($plan['days'] < 0) {
                $plan['days'] = 0;
            }
            if ($plan['plan'] == "Expired") {
                $message = "Package : " . $plan['plan'];
            } else if ($plan['plan'] == "500 Free Products" && $plan['product_amount'] > 0) {
                $message = "Package : " . $plan['plan'];
            } else if ($plan['product_amount'] <= 0) {
                $message = "You Have Reached The Limit of 500 Free Products";
            } else if ($plan['plan'] == "none") {
                $message = "Package : " . $plan['limit'];
            } else {
                $message = __('menu.REMAIN_DAYS') . "<b>" . $plan['days'] . "</b>" . __('menu.DAYS');
            }

            if ($plan['plan'] == "Trial" || $plan['plan'] == "none") {
                $banner = false;

                if ($plan['days'] > 0) {
                    $limit_amount = $product + 5;
                }
            } else {
                $limit_amount = $plan['plan_total'];
                if ($limit_amount < 0) {
                    $limit_amount = 0;
                }
            }

            // $import_pay_btn_url = url('admin/import-payment');
            // $import_upgrade_btn_label = __('products.buy_now');
            // $import_pay_btn = '<span><a class="btn-import-sidebar" href="'.$import_pay_btn_url.'">'.$import_upgrade_btn_label.'! <i class="fa fa-heart fa-beat"></i></a></span>';

            if (!empty($plan['is_unlimited'])) {
                $limit_amount = $plan['plan'];
            }

            $droptienda_total_product = DB::table('channel_products')->where('channel', 10)->where('shop_id', $shop_id)->where('is_connected', 1)->where('connection_status', ChannelProductConnectedStatus::CONNECTED)->count();

            return response()->json([
                'success' => true,
                'products' => $droptienda_total_product,
                'limit_amount' => $limit_amount,
                'all_products' => $product,

            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'products' => 0,
                'limit_amount' => 0,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function filter_carnot_category()
    {
        $d = \App\Models\ScCat::doesntHave('children')
            ->with('baps')
            ->get();
        // toSql();
        $main_obj = [];
        $i = 0;
        foreach ($d as $root) {
            $m = $this->processScrapItem($root, null);
            if ($m) {
                $category = 'Kategorien,' . $m->category;
                $object = [
                    'cat_id' => $root->id,
                    'link' => $root->link,
                    'category_str' => $category,
                ];
                $main_obj[$i] = $object;
                $i++;
            }
        }
        return response()->json(['data' => ($main_obj)]);
    }
    public function processScrapItem($item, $category_str)
    {
        $category = $item->name;
        $category = str_replace("- ", "-", $category);
        $category = str_replace(" -", "-", $category);
        $category = str_replace(" - ", "-", $category);

        $category = str_replace(",", " ", $category);
        $category = trim(preg_replace('/[\t\n\r\s]+/', ' ', $category));

        $category_str = $category . ',' . $category_str;
        $category_str = trim($category_str);
        $category_str = ltrim($category_str, ',');
        $category_str = rtrim($category_str, ',');
        if ($item->baps) {
            return $this->processScrapItem($item->baps, $category_str);
        }

        $item->category = $category_str;
        return $item;
    }


    public function wohlaufShipmentCsv()
    {
        $config = [
            'host' => "ftp.nexware.de", // required
            'root' => '/', // required
            'username' => "wohlauf.com", // required
            'password' => '3UBQtJ3EUm', // required
            'port' => 21,
        ];

        $service = new \App\Services\FTP\FTPService($config);
        return $service->ftpFilesByDir('/ship/');
    }

    public function wohlaufShipmentCsvDownload($slug)
    {
        $config = [
            'host' => "ftp.nexware.de", // required
            'root' => '/', // required
            'username' => "wohlauf.com", // required
            'password' => '3UBQtJ3EUm', // required
            'port' => 21,
        ];
        return downloadFtpFile($config['host'], $config['username'], $config['password'], 'ship/' . $slug);
    }

    public function orderShippmentStatus(Request $request)
    {
        try {
            if ($request->token != 'Zd6tQv8Cvd') return ['failed'];

            $validator = Validator::make($request->all(), [
                'order_id' => ['required'],
                'parcel_number' => ['nullable'],
                'parcel_service' => ['nullable'],
                'status' => ['required', 'in:cancel,shipped,exception,transfer,exception_delivery,stock_unavailable'],
                'message' => ['required'],
                'date' => ['nullable'],
                'error_level' => ['nullable'],
            ]);

            if ($validator->fails()) {
                throw new \Exception($validator->errors()->first());
            }

            $payload = $request->only(['parcel_number', 'parcel_service', 'status', 'message', 'date', 'error_level']);
            return app(\App\Services\Order\OrderShippment::class)->updateStatus($request->order_id, $payload);
        } catch (\Exception $e) {
            return ["failed", $e->getMessage()];
        }
    }

    public function orderTrackingSync(Request $request)
    {
        try {
            if ($request->token != 'Zd6tQv8Cvd') return ['failed'];

            $validator = Validator::make($request->all(), [
                'order_id' => ['required'],
                'parcel_number' => ['required'],
                'parcel_service' => ['required'],
                // 'message' => ['required'],
                'date' => ['nullable'],
                'error_level' => ['nullable'],
            ]);

            if ($validator->fails()) {
                throw new \Exception($validator->errors()->first());
            }

            $payload = $request->only(['parcel_number', 'parcel_service', 'message', 'date', 'error_level']);

            return app(\App\Services\Order\OrderShippment::class)->updateTracking($request->order_id, $payload);
        } catch (\Exception $e) {
            return ["failed", $e->getMessage()];
        }
    }

    public function check24ChannelOrderEvent(Request $request)
    {
        $token = $request->header('authorization');
        if (!\Str::startsWith($token, 'Basic ')) return;

        $token = base64_decode(\Str::substr($token, 6));

        if ($token && $token = @trim(explode(':', $token)[0])) {
            $shopId = \App\Shop::where([
                'channel' => 14,
                'username' => $token,
            ])
                ->value('id');

            if (empty($shopId)) return;

            $curl = curl_init(env('BACKGROUND_BASE_URL') . '/sync/' . $shopId);
            // Returns the data/output as a string instead of raw data
            curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);

            curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "GET");
            // get stringified data/output. See CURLOPT_RETURNTRANSFER
            $data = curl_exec($curl);
            // get info about the request
            $info = curl_getinfo($curl);
            // close curl resource to free up system resources
            $responseCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            curl_close($curl);

            return $responseCode;
        }
    }

    public function dtOrderStatus(Request $request){
        $token   = $request->header('userToken');
		$password = $request->header('userPassToken');




        $order_id = $request->order_id;
        $status = $request->order_status;
		$order = $this->getOrderInfo($token, $password, $order_id);
        if(!$order) {
            return [
                'success' => false
            ];
        }

		if($order) {
			$response = call_user_func('\App\Helper\OrderHelper' . '::callerOfTrigger', $status, $order, []);
		}
        return response()->json([
			'success' => true,
			'order_id' => $order_id,
            'response' => $response
		]);
    }



    public function dtSendEmail(Request $request){

        try {

            $validator = Validator::make($request->input(), [
                'mail_to' => 'required|email',
                'mail_from' => 'required|email',
                'mail_body' => 'required|max:5000',
                'mail_subject' => 'required|max:200',
                'attachment' => 'nullable',
            ]);

            if ($validator->fails()) {
                throw new Exception($validator->errors()->first());
            }

            $token = $request->header('userToken');
            $password = $request->header('userPassToken');

            $shop = \App\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();
            if(empty($shop)) throw new Exception('Access denied!');

            $data = [
                'email_to' => $request->mail_to,
                'email_from' => $request->mail_from,
                'body' => $request->mail_body,
                'subject' => $request->mail_subject,
            ];

            if (!(filter_var($data['email_from'], FILTER_VALIDATE_EMAIL) && filter_var($data['email_to'], FILTER_VALIDATE_EMAIL))) {
                throw new Exception("Something Wrong! Email Not Sent!.");
            }

            $attachment = $request->attachment;

            app('drm.mailer')
            ->getMailer($shop->user_id,$data['email_from'])
            ->send('admin.new_order.email_invoice_template', $data, function ($messages) use ($data, $attachment) {
                // $messages->from($data['email_from']);
                $messages->to($data['email_to']);
                $messages->subject($data['subject']);

                if(!empty($attachment))
                {
                    $pdf_stream = \PDF::loadHTML($attachment)->setWarnings(false)->output();
                    $messages->attachData($pdf_stream, 'report.pdf', [
                        'mime' => 'application/pdf',
                    ]);
                }
            });

            return response()->json([
                'success' => true,
                'message' => 'Email sent successfully!',
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 402);
        }
    }


    private function getOrderInfo($token, $password, $order_id)
	{
		try {
			$shop = \App\Shop::where('username', $token)->where('password', $password)->select('id', 'user_id')->first();
			if(empty($shop)) throw new \Exception('Shop not found!');

			$order_id_api = 'drop_t' . $shop->id . '_' . $order_id;

            return NewOrder::with('parcel', 'order_trackings.parcel')
                ->where('cms_user_id', $shop->user_id)
                ->where('shop_id', $shop->id)
                ->where('order_id_api', $order_id_api)
                ->first();
		}catch(\Exception $e) {
            debug_log('Token: '.$token.' Password: '.$password.' Order ID: '.$order_id,'DT_ORDER_STATUS');
			return [
				'success' => false,
				'message' => $e->getMessage().' '.$e->getLine(),
			];
		}
	}

    // MP parent categories
    public function mpParentCategories(Request $request)
    {
        return MarketplaceParentCategory::where('is_active',1)
        ->orderBy('name')
        ->select('name as label', 'id as value')
        ->get()
        ->toArray();
    }

    // Country list
    public function getCountryList(Request $request)
    {
        return \App\Country::select('name as label', 'id as value')
        ->orderBy('name')
        ->get()
        ->toArray();
    }

    // Validation API
    public function validationApi(\Illuminate\Http\Request $request)
    {
        $payload = $request->input();
        $transectionId = $request->header('X-Transaction-Id');
        $referer = $request->header('X-Transaction-Referer') ?? $_SERVER['HTTP_REFERER'];
        $token = 'a98ec94f1f6af5d2db42dbf34b15ef98fef8fa248617636bf7a88fca8c24be1a';

        return app(\App\Services\UiValidation\UiValidation::class)->validate($payload, $referer, $transectionId);
    }


    public function validationApiBulk(\Illuminate\Http\Request $request)
    {
        $payload = $request->input();
        $transectionId = $request->header('X-Transaction-Id');
        $referer = $request->header('X-Transaction-Referer') ?? $_SERVER['HTTP_REFERER'];
        $token = 'a98ec94f1f6af5d2db42dbf34b15ef98fef8fa248617636bf7a88fca8c24be1a';

        $res = [];
        if(!empty($payload) && is_array($payload))
        {
            foreach($payload as $req)
            {
                try {
                    $validation = app(\App\Services\UiValidation\UiValidation::class)->validate($req['payload'], $referer, $transectionId);
                    $res[] = ['id' => $req['id'], 'status' => $validation['status']];
                } catch (\Exception $e){}

            }
        }

        return $res;
    }



    // MP widget products
    public function mpPromotionProducts($id)
    {
        // $products = DB::connection('marketplace')
        // ->table('marketplace_products')
        // ->join('marketplace_categories as mCat', 'marketplace_products.category_id', 'mCat.id')
        // ->where('mCat.parent_id', $id)
        // ->where('marketplace_products.stock', '>', 0)
        // // ->whereRaw("round((uvp - vk_price) / vk_price * 100, 2) >= 30")
        // ->selectRaw("round((uvp - vk_price) / vk_price * 100, 2) as profit_val, marketplace_products.id, marketplace_products.name, ean, marketplace_products.image, brand")
        // ->orderByRaw("CASE shipping_method WHEN 2 THEN shipping_method END DESC, RAND()")
        // ->groupBy('marketplace_products.id')
        // ->having('profit_val', '>=', 30)
        // ->limit(15)
        // ->get();


        return Cache::remember("mp_promotion_{$id}_cat", 3600, function() use ($id) {
            $products = \App\MarketplaceProducts::where('status', 1)
            ->whereHas('category', function($cat) use ( $id ) {
               $cat->where('parent_id', $id);
            })
            ->where('stock', '>', 0)
            ->whereRaw("round((uvp - vk_price) / vk_price * 100, 2) >= 30")
            ->selectRaw("round((uvp - vk_price) / vk_price * 100, 2) as profit_val, id, name, ean, image, brand")
            ->orderByRaw("CASE shipping_method WHEN 2 THEN shipping_method END DESC, RAND()")
            ->limit(15)
            ->get();


            $eanList = $products->pluck('ean')->toArray();
            $ratings = DB::table('product_price_api')->whereIn('ean', $eanList)
            ->selectRaw('ean, AVG(rating) as rating')
            ->groupBy('ean')
            ->get()
            ->keyBy('ean')
            ->toArray();

            return $products->map(function($product) use ($ratings) {

                // Rating
                $rating = isset($ratings[$product->ean]) && $ratings[$product->ean]->rating ? round($ratings[$product->ean]->rating, 2) : 0;
                $rating = $rating >= 4 ? $rating : rand(3, 4) + rand(0, 1);

                // Images
                $images = json_decode($product->image, true) ?? [];
                $key = rand(0, count($images)- 1);

                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'ean' => $product->ean,
                    'image' => $images[$key],
                    'profit' => number_format($product->profit_val, 2, ',', '.'),
                    'brand' => $product->brand,
                    'rating' => $rating,
                ];
            })
            ->toArray();
        }) ?? [];
    }

    // Campaign unsubscribe FAKE
    public function campaignUnsubscribeFake($slug)
    {
        if(request()->expectsJson())
        {
            return response()->json(['message' => 'Operation success!']);
        }

        return view('admin.drm_email_marketings.campaign_unsubscribe');
    }

    // Campaign unsubscribe
    public function campaignUnsubscribe($slug)
    {
        $token = base64_decode($slug);
        $payload = \DRM::stringEncryption($token, true);
        if(!drmIsJSON($payload)) abort(404, "Invalid action");

        $data = json_decode($payload, true);

        $campaignId = $data['campaign'];
        $stepId = $data['step'];
        $userId = $data['user_id'];
        $email = $data['email'];

        // Get Customer ID
        $customerId = DB::table('new_customers')
        ->where('user_id', $userId)
        ->where('email', $email)
        ->value('id');

        if(empty($customerId)) abort(404, "Invalid action");

        // Unsubscribe campaign
        $updated = DB::table('new_customers')
        ->where('id', $customerId)
        ->whereNull('df_block_time')
        ->update([
            'df_block_time' => now()
        ]);

        if(!$updated) abort(404, "Invalid action");

        // Get unsubscribe ID
        $unsubscribeId = DB::table('campaign_unsubscribers')
        ->where('customer_id', $customerId)->value('id');

        $payload = [
            'campaign_id' => $campaignId,
            'step_id' => $stepId,
            'user_id' => $userId,
            'customer_id' => $customerId,
            'email' => $email,
        ];

        if ($unsubscribeId)
        {
            $payload['updated_at'] = now();
            DB::table('campaign_unsubscribers')
            ->where('id', $unsubscribeId)
            ->update($payload);
        } else {
            $payload['created_at'] = now();
            $payload['updated_at'] = now();
            DB::table('campaign_unsubscribers')->insert($payload);
        }

        if(request()->expectsJson())
        {
            return response()->json(['message' => 'Operation success!']);
        }

        return view('admin.drm_email_marketings.campaign_unsubscribe');
    }


    //Clear unpaid shipcloud
    public function clearUnpaidShipcloud()
    {
        app(\App\Services\Order\ReturnLabel::class)->clearUnPaid();
    }


    // Droptienda license renew
    public function dtLicenseRenew(Request $request)
    {
        if ($request->header('token') != 'Zd6tQv8Cvd') return ['failed'];
        return app(\App\Services\Droptienda\DtShopCharge::class)->dtLicenseRenew();
    }

    // DT shop category update
    public function createDtShopCategoryInvoice(Request $request, $id)
    {
        try {

            if ($request->header('token') != 'Zd6tQv8Cvd') return ['failed'];

            $shop = \App\Shop::where('id', $id)->first();
            if( !($shop && $shop->id && $shop->channel == 10) ) return;

            $signGroup = DB::table('options')
            ->where('option_key', 'sign_in_from_group')
            ->where('option_group', 'sign_in_from_group')
            ->where('user_id', $shop->user_id)
            ->value('option_value');

            if(!empty($signGroup) && in_array($signGroup, [12, 13, 14, 15]))
            {
                $shop->update(['category' => $signGroup]);
                app(\App\Services\Droptienda\DtShopCharge::class)->categoryAction($shop, $signGroup);

                DB::table('options')->where('option_key', 'sign_in_from_group')
                ->where('option_group', 'sign_in_from_group')
                ->where('user_id', $shop->user_id)
                ->update(['option_key' => 'sign_in_from_group_used']);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    public function getProductSafetyInfo(Request $request) {
        $productId = (int)$request->product_id;

        $channelProduct = ChannelProduct::where([
            'id' => $productId
        ])->select([
            'id',
            'user_id',
            'marketplace_product_id'
        ])->first();


        if($channelProduct->marketplace_product_id){
            $safetyInfo = ProductSafetyGPSR::where([
                'product_id' => $productId,
            ])->select([
                'safety_company_name',
                'safety_street',
                'safety_country_id',
                'safety_city',
                'safety_zip_code',
                'safety_email'
            ])->first();

            if($safetyInfo){
                $countryName = DB::table('all_country')->where('id', $safetyInfo->safety_country_id)->value('country');
                $companyName = $safetyInfo->safety_company_name;
                $address = $safetyInfo->safety_street. ' '. $safetyInfo->safety_zip_code. ' '. $safetyInfo->safety_city. ' '. $countryName;
                $email = $safetyInfo->safety_email;
            }
        }
        else {
            $user = User::find($channelProduct->user_id);
            $companyName = $user->first_name. ' '. $user->last_name;
            $address = $companyName;
            $email = $user->email;
        }

        return response([
            'name' => $companyName,
            'address' => $address,
            'email' => $email,
        ]);
    }

}
