<?php
namespace App\Http\Controllers\Api;

use App\Appointment;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\Request;
use Illuminate\Http\Request as LaravelRequest;
use Illuminate\Support\Facades\DB;
use phpseclib3\File\ASN1\Maps\GeneralName;
use Spatie\GoogleCalendar\Event;

class GoogleCalendarController extends Controller
{
    public function googleEvents(LaravelRequest $request)
    {
        try {
            $calenderConfiguration = $this->getCredentials($request->user_id);
            \Config::set('google-calendar', $calenderConfiguration);
            $startDateTime = Carbon::now()->subYear();
            $google_events = Event::get($startDateTime);
            $evensts = [];
            foreach ($google_events as $event) {
                $temp = [];
                $temp['title'] = $event->googleEvent->summary;
                $temp['start'] = $event->googleEvent->start->dateTime;
                $temp['end'] = $event->googleEvent->end->dateTime;
                $temp['email'] = $event->googleEvent->creator->email;
                $temp['note'] = $event->googleEvent->description;
                $temp['google_link'] = $event->googleEvent->htmlLink;
                array_push($evensts, $temp);
            }
            return $evensts;
        } catch (Exception $e) {
            return 'Something went wrong!';
        }
    }

    protected function googleEventCreate(LaravelRequest $request)
    {
        $calenderConfiguration = $this->getCredentials($request->user_id);
        \Config::set('google-calendar', $calenderConfiguration);

        if ($calenderConfiguration['calendar_id'] != '0') {
            $event = new Event;
            $event->name = $request->title;
            $event->description = $request->description . ' ' . $request->phone;
            $event->startDateTime = Carbon::parse($request->date.'T'.$request->start);
            $event->endDateTime =  Carbon::parse($request->date.'T'.$request->end);
            $event = $event->save();

            Appointment::where('id', $request->booking_id)->where('user_id', $request->user_id)->update(
                [
                    'event_id' => $event->id
                ]);
        }

    }

    protected function googleEventUpdate($event_id, $appointment, $new_appiontment)
    {

        $calenderConfiguration = $this->getCredentials(CRUDBooster::myParentId());
        \Config::set('google-calendar', $calenderConfiguration);

        if ($calenderConfiguration['calendar_id'] != '0') {

            $event = Event::find($event_id);;

            $event->name = $new_appiontment[0]['title'];
            $event->description = $new_appiontment[0]['description'];
            $event->startDateTime = Carbon::parse($new_appiontment[0]['start']);
            $event->endDateTime = Carbon::parse($new_appiontment[0]['end']);
            $event = $event->save();

            if ($appointment->event_id == null) {
                $appointment->event_id = $event->id;
                $appointment->save();
            }
        }
    }

    protected function getCredentials($user_id)
    {
        $credentials = DB::table('google_calendar_credentials')->where('user_id', $user_id)->first();
        $calenderConfiguration = app('App\Http\Controllers\AdminAppointmentBookingController')->googleDataMapping($credentials);
        return $calenderConfiguration;
    }
}
