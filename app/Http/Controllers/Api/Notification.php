<?php
namespace App\Http\Controllers\Api;

use App\Events\MessageSentEvent;
use App\Http\Controllers\Controller;
use App\Notifications\DRMNotification;
use App\User;
use Illuminate\Http\Request;

class Notification extends Controller
{
    public function push(Request $request): \Illuminate\Http\JsonResponse
    {
        $request->validate($this->rules());


        $data = $request->only(array_keys($this->rules()));
        // $hasPermission = DB::table('notification_trigger')
        //             ->where('hook', $data['hook'])->where('status', 0)->first();
        // if (!empty($hasPermission)) {
            // if (isHookRemainOnSidebar($data['hook']) && isLocal()) {
            //     User::find($data['user_id'])->notify(new DRMTelegramNotification($data['message'], $data['hook'],$data['url']));
            // }else{
            User::find($data['user_id'])->notify(new DRMNotification($data['message'], $data['hook'],$data['url'], '', $data['room_id']));
            // }
        // }
        // $dd = [
        //     $data['message'], $data['hook'], $data['url'], $data['room_id']
        // ];
        // _log($dd);


        $message = [
            'message' => $request->message,
            'order_id' => $request->room_id,
            'order_key' => $request->order_id,
            'recipient' => 'me',
            'sender' => $request->sender,
            'created_at' => $request->created_at,
            'time' => $request->time,
        ];

        # TODO: Real time not working from DT works from postman.
        // $message = [
        //     "message" => "Statc  text",
        //     "order_id" => 4,
        //     "recipient" => "me",
        //     "sender" => Array
        //         (
        //             "name" => "ABTanjir",
        //             "email" => "<EMAIL>"
        //         ),
        //     "created_at" => "2021-10-05 10:33:13",
        //     "time" => "1 second ago"
        // ];
        // _log($message);
        if($request->action == 'push_chat'){
            event(new MessageSentEvent($message));
        }
        // return $message;
        return response()->json([
          'code'=>200,
          'status'=>'SUCCESS'
        ]);
    }

    private function rules(): array
    {
        return [
            'user_id'  => 'required|numeric',
            'message'  => 'required',
            'url'      => 'required',
            'hook'     => 'nullable',
            'room_id'  =>  'nullable',
        ];
    }
}
