<?php


namespace App\Http\Controllers\Api;
use App\Enums\Apps;
use App\Http\Controllers\Controller;
use App\Jobs\ChannelManager\AutoTransfer;
use App\Services\DRMProductService;
use Illuminate\Http\Request;

class ProductAPIController extends Controller
{
    private DRMProductService $productService;

    public function __construct(DRMProductService $productService)
    {
        $this->productService = $productService;
    }

    public function deleteProduct(Request $request)
    {
        $this->productService->destroy($request->product_ids,$request->user_id);
    }
    public function autoTransfer(Request $request)
    {
        if(professionalOrHigher()){
            AutoTransfer::dispatch($request->product_ids,$request->product_ids,$request->lang ?? "de");
        }
    }
}
