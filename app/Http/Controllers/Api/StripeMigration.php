<?php


namespace App\Http\Controllers\Api;

use App\StripePayment;
use App\Models\Stripe\StripeTax;
use App\Services\Stripe\Latest\StripeService;

use App\Services\Payment\ServiceList;

class StripeMigration 
{
    use ServiceList;

    protected StripeService $service;

    public function __construct()
    {
        $this->service = new StripeService('stripe_key_2455');
    }
    
    public function adjust()
    {        
        $collection = [];

        $subscriptions = $this->service->stripe()->subscriptions->all(['limit' => 30, 'status' => 'active']);
        foreach($subscriptions as $subscription)
        {
            try {
                $collection[] = $this->subscriptionAdjust($subscription);
            } catch(\Exception $e)
            {
                dump($e->getMessage(). "  ID:  {$subscription->id}");
            }
        }

        return $collection;
    }


    private function subscriptionAdjust($subscription)
    {
        $plan = $subscription->plan->toArray();
        $metadata = $subscription->metadata->toArray();

        $interval = $plan['interval'];
        $price = round($plan['amount'] / 100, 2);

        $metadata['interval'] = $interval;

        $product = $this->service->stripe()->products->retrieve($plan['product'], []);
        $metadata['description'] = $product['name'];

        $metadata = $this->newMetadata($metadata);

        $this->service->stripe()->subscriptions->update(
            $subscription->id,
            ['metadata' => $metadata]
        );

        \DB::table('stripe_payments')
        ->where('subscription_id', $subscription->id)
        ->update([
            'metadata' => json_encode($metadata),
            'purchase_type' => $metadata['service_id'],
            'version' => 1,
            'provider' => StripePayment::STRIPE,
        ]);

        $metadata['id'] = $subscription->id;
        return $metadata;
    }

    private function newMetadata($data)
    {
        if(in_array($data['purchase_item_type'], ['import', 'mp_category']))
        {
            $data['total'] = isset($data['discount']) ? $data['sub_total'] - $data['discount'] : $data['sub_total'];
        }

        $payload = [
            'item_id' => $data['item_id'] ?? null,
            'plan_id' => $data['plan_id'] ?? null,
            'service' => $data['purchase_item_type'],
            'user_id' => $data['user_id'],
            'total' => round($data['total'], 2),
            'sub_total' => $data['sub_total'],
            'discount' => $data['discount'] ?? 0,
            'coupon' => $data['coupon'] ?? null,
            'total_tax' => $data['total_tax'] ?? 0,
            'vat_number' => $data['vat_number'] ?? null,
            'interval' => $data['interval'] ?? '',
            'description' => $data['description'] ?? '',
        ];


        if($payload['service'] === "app")
        {
            $payload['description'] = "App purchase: ".\DB::table('app_stores')
            ->where('id', '=', $payload['item_id'])
            ->value('menu_name');

        } else if($payload['service'] === "import")
        {
            $payload['description'] = "Import tariff: ".$payload['description'];
        }

        $payload['tax_rate'] = $data['total_tax'] > 0 ? 21 : 0;
        $payload['title'] = $data['title'] ?? $payload['description'];
        $payload['service_id'] = $this->serviceTypeIdByName($payload['service']);


        return $payload;
    }

}