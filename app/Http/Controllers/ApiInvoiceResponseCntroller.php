<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use ServiceKey;
use App\Http\Resources\StripeOrderFetchResource;
use Illuminate\Support\Collection;



use App\Helper\LengowApi;
use DateTime;
use App\Helper\GambioApi;
use App\Helper\ShopifyApi;
use App\Helper\EbayApi;
use \Hkonnet\LaravelEbay\EbayServices;
use \DTS\eBaySDK\Trading\Services;
use \DTS\eBaySDK\Trading\Types;
use \DTS\eBaySDK\Trading\Enums;
use \DTS\eBaySDK\Constants;
use Ebay;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use App\Notifications\DRMNotification;
use App\User;
use Carbon\Carbon;
use \Illuminate\Support\Arr;
// use Automattic\WooCommerce\Client; // need to install

class ApiInvoiceResponseCntroller extends Controller
{
	//Remove later
    public function getOrderData($order_id){

		$order = DB::table('drm_orders_new')->find($order_id);
    	$data = [];
    	if($order){
            $shop = \App\Shop::find($order->shop_id);
            if($shop){
            	$data['shop_id'] = $shop->id;
            	$data['shop_name'] = $shop->shop_name;
            	$data['order_id'] = $order->id;
            	$data['order_id_api'] = $order->order_id_api;
        		if($shop->channel == 1)
                {
                   $data['data'] = $this->syncGambioorderSearch($shop, $order->order_id_api);
                }
                if($shop->channel == 2)
                {
                   $data['data'] = $this->syncLengowOrderSearch($shop, $order->order_id_api);
                }
                if($shop->channel == 3)
                {
                   $data['data'] = $this->syncYategoOrder($shop, $order->order_id_api);
                }
                if($shop->channel == 4)
                {
                   $data['data'] = $this->syncEbayOrder($shop, $order->order_id_api);
                }
                if($shop->channel == 6)
                {
                   $data['data'] = $this->syncShopifyOrder($shop, $order->order_id_api);
                }
            }
    	}

    	return $data; 
    }

    public function getNewOrderData($order_id){

		$order = DB::table('new_orders')->find($order_id);
    	$data = [];
    	if($order){
            $shop = \App\Shop::find($order->shop_id);
            if($shop){
            	$data['shop_id'] = $shop->id;
            	$data['shop_name'] = $shop->shop_name;
            	$data['order_id'] = $order->id;
            	$data['order_id_api'] = $order->order_id_api;
        		if($shop->channel == 1)
                {
                   $data['data'] = $this->syncGambioorderSearch($shop, $order->order_id_api);
                }
                else if($shop->channel == 2)
                {
                   $data['data'] = $this->syncLengowOrderSearch($shop, $order->order_id_api);
                }
                else if($shop->channel == 3)
                {
                   $data['data'] = $this->syncYategoOrder($shop, $order->order_id_api);
                }
                else if($shop->channel == 4)
                {
                   $data['data'] = $this->syncEbayOrder($shop, $order->order_id_api);
                }
                else if($shop->channel == 5)
                {
                   $data['data'] = $this->syncAmazonOrder($shop, $order->order_id_api);
                }
                else if($shop->channel == 6)
                {
                   $data['data'] = $this->syncShopifyOrder($shop, $order->order_id_api);
                }
                // else if($shop->channel == 7)
                // {
                //    $data['data'] = $this->syncWooCommerceOrder($shop, $order->order_id_api);
                // }
            }
    	}

    	return $data; 
    }

   	public function syncGambioorderSearch($shop, $search)
    {
        try{
	        if ($shop) {
	            $user = $shop->username;
	            $pass = $shop->password;
	            $shopId = $shop->id;
	            $base_url = $shop->url;
	            $api_path = "api.php/v2/";
	        } else {
	            throw new \Exception("You have not configured any shop yet!");
	        }

	        $auth = base64_encode("$user:$pass");

	        $per_page = 50;
	        $url  = $base_url . $api_path . "orders/$search";

	        $headers = array("Authorization: Basic " . $auth);

	        $ch = curl_init();
	        curl_setopt($ch, CURLOPT_URL, $url);
	        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
	        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
	        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");

	        $response = curl_exec($ch);
	        $responseCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	        $errNo = curl_errno($ch);
	        $errStr = curl_error($ch);
	        curl_close($ch);

	        if ($responseCode != 200) {
	            throw new \Exception("Shop connection problem!");
	        }

			return json_decode($response);
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }

    public function syncLengowOrderSearch($shop, $search)
    {
    	try{
	        if (!$shop) {
	            throw new \Exception("You have not configured any Lengow shop yet!");
	        }

	        $access_token = $shop->username;
	        $secret   = $shop->password;
	        $lengow = new LengowApi($access_token, $secret);

	        if ($lengow->token == "") {
	            throw new \Exception("$shop->shop_name shop token problem!");
	        }
			return $lengow->getOrderByID($search);
	        return [];
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }


    function syncYategoOrder($shop, $search)
    {
        try{
	        if (!$shop) {
	        	throw new \Exception("You have not configured any Yatego shop yet!");
	        }
	        $order_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_order";

	        $order_content = @file_get_contents($order_url);
	        if (!$order_content) {
	            throw new \Exception("Can not access url or no order found!");
	        }

	        @unlink(storage_path() . "/tempOrderTest.csv");
            @unlink(storage_path() . "/tempOrderProduct.csv");

	        $putted_orders = @file_put_contents(storage_path() . "/tempOrderTest.csv", $order_content);
	        if (!$putted_orders) {
	            throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderTest.csv");
	        }

	        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
	        $reader->setInputEncoding('UTF-8');
	        $reader->setDelimiter(';');
	        $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderTest.csv"));

	        $order_arr = $spreadsheet->getActiveSheet()->toArray();
	        $order_columns = $order_arr[0];
	        unset($order_arr[0]);

	        if (count($order_arr)) {

				$order_id_from = $order_arr[1][1];
		        $order_id_to = $order_arr[count($order_arr)][1];

		        $order_product_url = "https://www1.yatego.com/admin/modules/yatego/orders.php?user=" . $shop->username . "&passwd=" . $shop->password . "&action=csv_products&von=" . $order_id_from . "&bis=" . $order_id_to . "&varids=1";
		        $product_content = @file_get_contents($order_product_url);

		        $putted = @file_put_contents(storage_path() . "/tempOrderProduct.csv", $product_content);
		        if (!$putted) {
		            throw new \Exception("Content can not be putted to file " . storage_path() . "/tempOrderProduct.csv");
		        }


		        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
		        $reader->setInputEncoding('UTF-8');
		        $reader->setDelimiter(';');
		        $spreadsheet = $reader->load(realpath(storage_path() . "/tempOrderProduct.csv"));

		        $product_arr = $spreadsheet->getActiveSheet()->toArray();
		        $product_columns = $product_arr[0];
		        unset($product_arr[0]);
		        $product_arr_new = [];
		        foreach ($product_arr as $item) {
		            $product = @array_combine($product_columns, $item);

		            if (!$product) {
		                throw new \Exception('Error in product details. Please contact admin.');
		            }

		            $product_arr_new[] = $product;
		        }

		        $product_collection = collect($product_arr_new);

		        foreach ($order_arr as $k => $item) {
		        	$order_data = @array_combine($order_columns, $item);
		        	$order_data['products'] = $product_collection->where('Bestellnummer', $order_data['Bestellnummer'])->toArray();
		        	if($order_data['Order_ID'] == $search)  return $order_data;
		        }
	        }
	        return [];
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }

    public function syncEbayOrder($shop, $search)
    {
    	try{
    		if($shop){
	    		$ebay_service = new EbayServices();
				$service = $ebay_service->createTrading();
				$request = new Types\GetOrdersRequestType();
				$request->RequesterCredentials = new Types\CustomSecurityHeaderType();
				$request->RequesterCredentials->eBayAuthToken = $shop->password;
				$request->CreateTimeFrom = \DateTime::createFromFormat('Y-m-d H:i:s', '2019-02-01 18:33:00');
				$request->CreateTimeTo = \DateTime::createFromFormat('Y-m-d H:i:s', now());
				$request->OrderStatus = 'All';
				$response = $service->getOrders($request);
				if(json_decode($response,true)['Ack'] == 'Success'){
					$response = (object)(json_decode($response,true)['OrderArray']);
					$all_orders = $response->Order;

					if($all_orders){
						foreach ($all_orders as $order){
							if($order['OrderID'] == $search) return $order;
						}
					}
				}else{
					throw new \Exception('Authentication error!');
				}
    		}else{
    			throw new \Exception('You have not configured any Ebay shop!');
    		}
    		return [];
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }


    // Shopify Orders
    public function syncShopifyOrder($shop, $search)
    {
    	try{
    		if($shop){
		        $url = $shop->url . 'admin/api/2020-01/orders/'.$search.'.json';
		        $client = new \GuzzleHttp\Client();
		        $response = $client->request('GET', $url, [
		            'auth' => [$shop->username, $shop->password]
		        ]);
		        if($response->getStatusCode() !== 200){
		        	throw new \Exception('Connection problem!');
		        }
				$data = $response->getBody()->getContents();
				return (json_decode($data));
	    	}else{
	    		throw new \Exception('You have not configured shop!');
	    	}
	    	return [];
	    }  catch (\Exception $e) {
	    	return $e->getMessage();
	    }
    }    

    //Amazon Orders
    public function syncAmazonOrder($shop, $search)
    {
        try{

            $access_token = $shop->username;
            $secret   = $shop->password;

            $client = new \App\Services\Amazon\AZClient([
                'Marketplace_Id' => 'A1PA6795UKMFR9',
                'Seller_Id' => $shop->username,
                'Access_Key_ID' => '********************',
                'Secret_Access_Key' => 'oEIgkGejJYPU9NeTZs3CVacxqNuML2kx1P14ODnw',
                'MWSAuthToken' => $shop->password
            ]);

            $data = [];
            $data['order'] = $order = $client->GetOrder($search);
            $data['order_items'] = $order_items = ($order)? $client->ListOrderItems($order['AmazonOrderId']) : [];
            return $data;

        }catch(\Exception $e) {
            return $e->getMessage();
        }
    }

    //Woocommerce Order
//     public function syncWooCommerceOrder($shop, $search)
//     {
//         try{
//             if ($shop) {
//                 $url = $shop->url;
//                 $user = $shop->username;
//                 $pass = $shop->password;
//             } else {
//                 throw new \Exception("Invalid shop!");
//             }
// //dd('order/'.$search);
//             $woocommerce = new Client($url, $user, $pass);
//             return $woocommerce->get('orders');

//         }catch (\Exception $e) {
//             return $e->getMessage();
//         }
//     }

}
