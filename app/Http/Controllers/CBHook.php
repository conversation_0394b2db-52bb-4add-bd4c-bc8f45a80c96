<?php
namespace App\Http\Controllers;

use DB;
use Request;
use App\TrakerTimeSpent;
use CRUDBooster;
use Session;
use Illuminate\Support\Facades\Log;

class CBHook extends Controller {

	/*
	| --------------------------------------
	| Please note that you should re-login to see the session work
	| --------------------------------------
	|
	*/
	public function afterLogin() {
		//After login event create
        try {

            if(CRUDBooster::isSubUser()){
                $parent_user_id = CRUDBooster::myParentId();
                $parent_user = DB::table('cms_users')->where('id', '=', $parent_user_id)->whereNotNull('status')->exists();
                if(!$parent_user){
                    $this->getLogout('Your parent user is blocked or not exists!');
                    return;
                }
            }

            //Update time
            DB::table('cms_users')->where('id', CRUDBooster::myId())->update(['updated_at' => now()]);

            //Remove inactive tags
            $customer_id = DB::table('new_customers')->where('user_id', 2455)->where('cc_user_id', CRUDBooster::myId())->value('id');

            if(!empty($customer_id)){
                $tag_ids = config('user_block.inactive_tag_list')?? [];
                $inactive_tag_ids = DB::table('dropfunnel_customer_tags')->where('insert_type', 14)->where('customer_id', $customer_id)->whereIntegerInRaw('tag_id', $tag_ids)->pluck('id')->toArray();

                if($tag_ids && !empty($inactive_tag_ids)){
                    DB::table('dropfunnel_customer_tags')->where('insert_type', 14)->where('customer_id', $customer_id)->delete();

                    //insert tag
                    try {

                        if(!empty($customer_id))
                        {
                            $tag = 'Welcome back';
                            \App\DropfunnelCustomerTag::insertTag($tag, 2455, $customer_id, 14);
                        }
                    }catch (\Exception $e) {}
                }
            }

// Performance issue
            // $timeSpent = new TrakerTimeSpent();
            // $timeSpent->user_id = CRUDBooster::myId();
            // $timeSpent->href = request()->url();
            // $timeSpent->seconds = 1;
            // $timeSpent->save();
// Performance issue

            usleep( 1 * 1000 );
        } catch (\Exception $exception) {
            Log::channel('command')->error('Login Traker Time insert Error:- '.$exception->getMessage());
        }
	}

	public function getLogout($msg = '')
    {
        $me = CRUDBooster::me();
        CRUDBooster::insertLog(trans("crudbooster.log_logout", ['email' => $me->email]), '', 'logout');
        $logoutURL = drm_login_url($me);

        DB::table(config('crudbooster.USER_TABLE'))->where("id", $me->id)->update([
            'is_logged_in' => 0,
        ]);

        //Delete login info to drop campus
        // $user_mac = getMacAddress();
        // $user_ip = getIpAddress();
        // resolve(\App\Services\DropCampus\DropmatixCampus::class)->deleteDrmLoginDetail($me->email, $user_mac, $user_ip);
        //Detele login info to drop campus END
        
        Session::flush();
        setcookie('user_id', session('admin_id'), time() - 1000);
        setcookie('user_name', session('admin_name'), time() - 1000);

        // Logout auth user
        authUserLogout();

        $notice = trim(trans("crudbooster.message_after_logout").' '.$msg);
        CRUDBooster::redirect($logoutURL, $notice, 'warning');
    }
}
