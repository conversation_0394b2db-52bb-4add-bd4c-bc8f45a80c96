<?php

namespace App\Http\Controllers;

use App\Shop;
use Exception;
use App\Enums\Channel;
use App\Enums\CategoryType;
use Illuminate\Support\Arr;
use Illuminate\Http\Request;
use App\Models\View\Categories;
use League\Csv\InvalidArgument;
use App\Enums\DroptiendaSyncType;
use App\Enums\DroptiendaSyncEvent;
use Illuminate\Support\Facades\DB;
use App\Models\ChannelUserCategory;
use App\Models\DroptiendaSyncHistory;
use App\Models\ChannelProductCategory;
use App\Services\ChannelProductService;
use App\Services\ChannelCategoryService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;

class ChannelCategoryController extends Controller
{
    private ChannelCategoryService $categoryService;
    private ChannelProductService $productService;

    public function __construct(ChannelCategoryService $categoryService , ChannelProductService $productService)
    {
        $this->categoryService = $categoryService;
        $this->productService = $productService;
    }

    public function index(Request $request)
    {

        $currentUserId = CRUDBooster::myParentId();

        $shop = Shop::select(['id','shop_name','channel','lang',])
            ->where(['id' => $request->shop,'user_id' => $currentUserId])
            ->orderBy('channel')
            ->first();

        $data['lang'] = $shop->lang;
        $data['activeChannel'] = $shop;

        $data['page_title'] = "<img width='30px' src='".getChannelLogo($shop->channel)."'> &nbsp;".ucfirst(strtolower($shop->shop_name)).' '.__('Categories');
        $payload = [
            'user_id' => $currentUserId,
            'channel' => $shop->channel,
            'shop_id' => $shop->id
        ];

        $main_query = Categories::where('parent', 0)
        ->where($payload)
        ->orderBy('category_name');

        $data['channelCategories'] = $main_query->paginate($request->limit ?? 20);

        $data['channelCategories']->getCollection()->map(function($category){
            $category->total_products = $category->product_count;
            $category->sum_count = $this->productCount($category, $category->total_products);
            return $category;
        });

        $data['total'] = $data['channelCategories']->total();
        return view('channel_categories.index', $data);
    }


    private function productCount($collection, &$count = 0){
        if($collection->child_category->count()){
            $collection->child_category->map(function($category){
                $category->total_products = $category->product_count;
                $category->sum_count = $this->productCount($category, $category->total_products);
                return $category;
            });

            $count += $collection->child_category->sum('sum_count');

        }
        return $count;
    }

    public function newIndex(Request $request)
    {
        return view('channel_categories.new.index');
    }

    public function quickUpdate(Request $request){
        $currentUserId = CRUDBooster::myId();
        $lang = app('App\Services\UserService')->getLang($currentUserId);

        $name   = $request->name;
        $id     = (int)$request->pk;
        $value  = $this->categoryService->sanitizeCategoryName($request->value);
        $channel = $request->channel;
        $res = $this->categoryService->updateOrCreate($id, [$name=>$value] , $currentUserId, $channel);

        if(!isset($res['errors'])){
            return response()->json(['success'=>true]);
        }
        else {
            throw new \Exception("Category already exists !");
        }
    }


    public function updateChildPosition(Request $request)
    {
        try {

            $this->categoryService->updateTree(
                $request->parent,
                $request->id,
                CRUDBooster::myParentId(),
                $request->channel
            );

            CRUDBooster::redirectBack('Saved successfully!', 'success');

        } catch (\Exception $e) {
            CRUDBooster::redirectBack($e->getMessage());
        }
    }

    /**
     * @deprecated
     **/
    public function updateTree(Request $request)
    {
        $this->categoryService->updateTree(
            $request->parent,
            $request->category_id,
            CRUDBooster::myParentId(),
            $request->channel
        );
    }

    public function delete(Request $request)
    {
        $res = array();
        try {
            $res = $this->categoryService->destroy($request->category_ids,CRUDBooster::myParentId());
        } catch (Exception $e) {}
        // return (isLocal() || in_array(CRUDBooster::myParentId(),[2454,2565,2494])) ? response($res) : \redirect()->back();
        return response($res);
    }

    public function create(Request $request)
    {
        try {
            $currentUserId = CRUDBooster::myParentId();
            $category_name = $request->category_name;
            $res = $this->categoryService->updateOrCreate(
                '',
                [
                    'category_name'=>$category_name,
                    'channel' => $request->channel,
                    'shop_id' => $request->shop_id
                ],
                $currentUserId
            );

        } catch (\Throwable $th) {
            //throw $th;
        }
        return \redirect()->back();
    }

    public function getChild(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $res = $this->categoryService->getChild($request->parent,$user_id);

        return response(['data' => $res]);
    }

    /**
     * Generate category suggestion
     */
    private function _childQuery($categoryArr, $level = 0, array $categoriesId = [])
    {
        $shop_id = $_REQUEST['shop_id'];
        $user_id = CRUDBooster::myParentId();

        $search = $categoryArr[$level];
        $nextSearch = $categoryArr[$level + 1] ?? null;


        $isRequiredChild = $nextSearch || count($categoryArr) - 1 > $level;

        $search = !$isRequiredChild && $search ? '%'.$search.'%' : $search;
        $nextSearch = $nextSearch && !isset($categoryArr[$level + 2]) ? '%'.$nextSearch.'%' : $nextSearch;

        $children = ChannelUserCategory::where('shop_id', $shop_id)
        ->where('user_id', $user_id);

        if($search)
        {
            $children->where('category_name', 'like', $search);
        }

        if($isRequiredChild)
        {
            $children->whereHas('child_category', function ($query) use ($nextSearch, $shop_id) {
                if($nextSearch)
                {
                    $query->where('category_name', 'like', $nextSearch);
                }
                $query->where('shop_id', $shop_id);
            });
        }

        if(empty($level))
        {
            $children->where('parent', 0);
        } else {
            $children->whereIntegerInRaw('parent', $categoriesId);
        }

        if($isRequiredChild)
        {
            $children->with(['child_category' => function ($query) use ($nextSearch, $shop_id) {

                if($nextSearch)
                {
                    $query->where('category_name', 'like', $nextSearch);
                }

                $query->where('shop_id', $shop_id)
                ->select('id', 'category_name', 'parent');
            }]);
        }

        $result = $children->select('id', 'category_name', 'parent')
        ->orderBy('category_name', 'asc')
        ->limit(20)
        ->get();

        $level += 2;

        if(!isset($categoryArr[$level])) {

            return $result->map(function($child) {

                if($child->relationLoaded('child_category')) {
                    $child->tree = $child->child_category;
                    unset($child->child_category);
                }

                return $child;
            });
        }


        return $result->map(function($child) use ($categoryArr, $level) {
            if($child->child_category->isNotEmpty())
            {
                $childItems = $this->_childQuery($categoryArr, $level, $child->child_category->pluck('id')->toArray());
                $child->tree = $child->child_category->map(function ($item) use ($childItems) {
                    $item->tree = $childItems->where('parent', $item->id);
                    unset($item->child_category);
                    return $item;
                });
            }

            unset($child->child_category);
            return $child;
        });
    }


    private function _generateSuggestionString($collection, $tree = null)
    {

        return $collection->map(function($item) use ($tree) {


            $label = $tree ? $tree->label .' > '.$item->category_name : $item->category_name;

            $tree = (object)['id' => $item->id, 'label' => $label];

            if(!empty($item->tree)) return $this->_generateSuggestionString($item->tree, $tree);
            return $tree;

        })
        ->flatten()
        ->toArray();
    }

    public function searchCategory()
    {
        $activeChannel = $_REQUEST['activeChannel'];
        $shop_id = $_REQUEST['shop_id'];

        $stringArray = preg_split("/( - |>|,|;)/", $_REQUEST['searchString']);
        $categoryArr = array_map('trim', $stringArray);

        if(!empty($categoryArr[0]) && $categories = $this->_childQuery(array_values($categoryArr), 0))
        {
            $data = $this->_generateSuggestionString($categories);
            return response()->json($data);
        }

        return response()->json([]);
    }

    /**
     * @throws InvalidArgument
     * @throws \League\Csv\Exception
     */
    public function refreshShopCategories(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        return app('App\Services\Modules\Export\Droptienda')->exportCategories($user_id,$request->shop_id);
//
//
//        $categories = ChannelUserCategory::where([
//            'channel'   => Channel::DROPTIENDA,
//            'user_id'   => $user_id,
//            'parent'    => 0
//        ])->get();
//
//        $shop = Shop::where([
//            'user_id' => $user_id,
//            'channel' => Channel::DROPTIENDA
//        ])->first();
//
//        foreach ($categories as $category)
//        {
//            DroptiendaSyncHistory::create([
//                'sync_type' => DroptiendaSyncType::CATEGORY,
//                'sync_event' => $category->shop_category_id ? DroptiendaSyncEvent::UPDATE : DroptiendaSyncEvent::CREATE,
//                'model_id' => $category->id,
//                'user_id' => $category->user_id,
//            ]);
//        }
//
//        if(substr($shop->url , -1) == '/'){
//            $url = $shop->url;
//        }else {
//            $url = $shop->url.'/';
//        }
//
//        $url = $url.'api/v1/category_reset';
//
//        try {
//            $ch = curl_init($url);
//            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "POST");
//            curl_exec($ch);
//            curl_close($ch);
//        }catch (Exception $e){}
//
//        return true;
    }

    public function countCategoryProducts(Request $request): \Illuminate\Http\JsonResponse
    {
        $exists = ChannelProductCategory::whereIn('category_id',$request->category_ids)->where('category_type',CategoryType::USER)->exists();
        return response()->json([
            'exists' => $exists
        ]);
    }

}
