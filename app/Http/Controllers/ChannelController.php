<?php

namespace App\Http\Controllers;

use App\Models\BlockedChannel;
use App\Shop;
use Illuminate\Http\Request;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\DB;

class ChannelController extends Controller
{
    public function index(Request $request)
    {
        $user_id = CRUDBooster::myParentId();

        if(CRUDBooster::isSupplier() || CRUDBooster::isSubUser()) abort(404);  //FEAT:: Channel Modification

        //DRM channel lang
        $country_id = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($country_id);

        $channel_add_info = app('App\Http\Controllers\Product\DRMProductController')->userChannelAddition($user_id);
        if($channel_add_info['channel_add_left'] <= 0){
            return redirect(CRUDBooster::adminPath('drm_products?lang='.$lang))->with(['message' => 'Sorry! You have touched channel limit!', 'message_type' => 'danger']);
        }

        if(empty($lang)) throw new \Exception('Channel language problem!');

        $has_customer_profile = DB::table('new_customers')->where('cc_user_id', $user_id)->exists();
        if( empty($has_customer_profile) && !(CRUDBooster::isSuperadmin()) ){
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_all_orders'), 'Please setup your customer profile!');
        }

        $shop = Shop::where('lang', $lang);

        if(!CRUDBooster::isSuperadmin()){
            $shop->where('user_id', $user_id);
        }

        $shop = $shop->select('channel', 'lang')->groupBy('channel')->get();
        $upcommings = config('upcomming_shops', []);
        $has_billing = \App\BillingDetail::where('user_id', $user_id)->exists();

        $protected_shops_modules = config('protectedShops.modules', []);
        $protected_shops_modules = array_keys($protected_shops_modules);
        // protected shops

        //Plan renew cancel status
        $is_renew_cancel = DB::table('purchase_import_plans')->join('import_plans', 'import_plans.id', '=', 'purchase_import_plans.import_plan_id')->where('purchase_import_plans.cms_user_id', $user_id)->value('purchase_import_plans.is_renew_cancel');

        //Index data
        $data = [
            'page_title' => __('menu.MENUADDCHANNEL'),
            'page_icon' => 'fa fa-list-alt',
            'lang' => $lang,
            'country_id' => $country_id,
            'shop' => $shop,
            'upcommings' => $upcommings,
            'has_billing' => $has_billing,
            'protected_shops_modules' => $protected_shops_modules,
            'can_use_protected_shops' => true,
            'is_renew_cancel' => $is_renew_cancel,
            'blocked_channels' => userBlockedChannels($user_id, $country_id),
        ];

        return view('admin.channel.index', $data);
    }
}
