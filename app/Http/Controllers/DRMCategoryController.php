<?php

namespace App\Http\Controllers;

use App\Models\DrmCategory;
use Illuminate\Http\Request;
use App\Services\DRMCategoryService;
use App\Services\DRMProductService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Arr;

class DRMCategoryController extends Controller
{
    private DRMCategoryService $categoryService;
    private DRMProductService $productService;

    public function __construct(DRMCategoryService $categoryService , DRMProductService $productService)
    {
        $this->categoryService = $categoryService;
        $this->productService = $productService;
    }

    public function index(Request $request)
    {
        $currentUserId = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);
        $search_by_field = $request->get('search_by_field_column');
        $filters = $request->all();

        $data['page_title'] = __("DRM Categories");
        $limit = 30;
        
        $data['categories'] = $this->categoryService->all([
            'user_id' => $currentUserId,
            'country_id' => $languageId,
            'lang'    => $lang,
            'limit'   => $limit
        ]);

        if(!empty($search_by_field) && !empty($filters['q'])){
            $q = $filters['q'];
            if($search_by_field=='name'){
                $data['categories'] = $this->categoryService->all([
                    'user_id' => $currentUserId,
                    'country_id' => $languageId,
                    'lang'    => $lang,
                    'limit'   => $limit,
                    'category_name' => "%$q%"
                ]);
            }
            if($search_by_field=='id'){
                $data['categories'] = $this->categoryService->all([
                    'user_id' => $currentUserId,
                    'country_id' => $languageId,
                    'lang'    => $lang,
                    'limit'   => $limit,
                    'id' => "%$q%"
                ]);
            }
        }
        return view('admin.drm_categories.index', $data);
    }

    public function quickUpdate(Request $request){
        $currentUserId = CRUDBooster::myParentId();
        $country = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($country);

        $id   = (int)$request->pk;
        $category_name = $request->value;
        $res = $this->categoryService->updateOrCreate($id, ['category_name_'.$lang => $category_name] , $currentUserId, $country,$lang);

        if(!isset($res['errors'])){
            return response()->json(['success'=>true],200);
        }
        else {
            throw new \Exception("Category already exists !");
        }
    }

    public function delete($ids)
    {
        $ids = explode(",", $ids);
        try {
            $res = $this->categoryService->destroy($ids, CRUDBooster::myId());
            if(count($res['not_deleted']) == 0){
                return redirect()->back();
            }
            else {
                return redirect()->back()->withErrors([
                    'msg' => "Some categories could not be deleted. Because they are linked with some products"
                ]);
            }
        } catch (Exception $e) {

        }

        return redirect()->back();
    }

    public function create(Request $request)
    {
        $currentUserId = CRUDBooster::myParentId();
        $country = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($country);

        try {
            $currentUserId = CRUDBooster::myId();
            $category_name = $request->category_name;
            $res = $this->categoryService->updateOrCreate('', ['category_name_'.$lang => $category_name] , $currentUserId, $country,$lang);

        } catch (\Throwable $th) {
            //throw $th;
        }
        return \redirect()->back();
    }

}
