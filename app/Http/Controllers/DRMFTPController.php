<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use League\Flysystem\Adapter\Ftp;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemException;
use League\Flysystem\ConnectionRuntimeException;
use App\FtpCredential;
use CRUDBooster;
use Session;

class DRMFTPController extends Controller
{
	//Filesystem
	private $filesystem = null;

    //Index view
    public function index(){

        //Check if purchased
        if(!DrmUserHasPurchasedApp(CRUDBooster::myParentId(), config('global.ftp_app_id')) && !CRUDBooster::isSuperadmin() ){
            CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
        }


        $data = [];
        $data['page_title'] = 'DRM FTP';
        return view('ftp.index', $data);
    }

                // $config = [
            //  'host' => 'ftp.archimple.com', // required
      //           'username' => '<EMAIL>', // required
      //           'password' => 'Mv!ZVr[luCa)', // required
      //       ];           

            // $config = [
            //     'host' => '************', // required
            //     'username' => 'binocsvde', // required
            //     'password' => 'BJ1ZPHh9', // required
            // ];

    //Load Login
    public function loginView(Request $request){
        try{
            Session::forget('drm_ftp_current_dir'.CRUDBooster::myId());
            Session::forget('drm_ftp_user_credentials'.CRUDBooster::myId());
            $this->filesystem = null;

            $message = $request->message;
            $data = [];
            $data['page_title'] = 'DRM FTP Login';
            $data['credential'] = FtpCredential::where('user_id', CRUDBooster::myId())->first();
            return response()->json([
                'success' => true,
                'html' => view('ftp.login', $data)->render(),
                'is_logged' => false,
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

	//FTP login action
	public function ftpLogin(Request $request)
    {
    	$request->validate([
    		'host_name' => 'required',
    		'user_name' => 'required',
    		'password' => 'required',
            'port' => 'required',
    	]);

    	$config = [
			'host' => $request->host_name, // required
            'username' => $request->user_name, // required
            'password' => $request->password, // required
            'port' => $request->port,
        ];

        $this->init($config);
        if($this->filesystem){
            $this->filesystem->listContents('/');
            Session::put('drm_ftp_user_credentials'.CRUDBooster::myId(), $config);
            FtpCredential::updateOrCreate(['user_id' => CRUDBooster::myId()],[
                'host_name' => $config['host'],
                'user_name' => $config['username'],
                'password' => $config['password'],
                'host_port' => $config['port']
            ]);
            return response()->json([
                'success' => true,
                'html' => view('ftp.index-content')->render()
            ]);
        }else{
            return response()->json([
                'success' => false,
                'html' => $e->getMessage()
            ]);
        }
    }


	//INIT FTP
	private function init(array $config){
		$adapter = new Ftp([
            'host' => $config['host'], // required
            'root' => '/', // required
            'username' => $config['username'], // required
            'password' => $config['password'], // required
            'port' => $config['port'],
            'ssl' => false,
            'timeout' => 90,
            'utf8' => false,
            'passive' => true,
            'transferMode' => FTP_BINARY,
            'systemType' => null, // 'windows' or 'unix'
            'ignorePassiveAddress' => null, // true or false
            'timestampsOnUnixListingsEnabled' => false, // true or false
            'recurseManually' => false // true
        ]);
        $this->filesystem = new Filesystem($adapter);
	}


    //Return folder & files by dir
    public function ftpContentsByDir(Request $request){
        $is_logged = false;
    	try{

            if(!Session::exists('drm_ftp_user_credentials'.CRUDBooster::myId())){
                return response()->json([
                    'success' => true,
                    'is_logged' => false,
                ]);
            }
            $this->init(Session::get('drm_ftp_user_credentials'.CRUDBooster::myId()));
            $is_logged = true;

    		$dir = $request->directory ?? '/';
    		
	    	$allPaths = $this->filesystem->listContents($dir);
	    	$files = array_filter($allPaths, function($item){
				return strtolower($item['type'] === 'file');
			}); 

			$folders = array_filter($allPaths, function($item){
				return strtolower($item['type'] === 'dir');
			}); 

			$folder_collection = collect($folders)->groupBy('dirname');
            Session::put('drm_ftp_current_dir'.CRUDBooster::myId(), $dir);

			return response()->json([
    			'successs' => true,
    			'folders' => view('ftp.dir', ['filesystem' => $this->filesystem, 'folders' => $folders, 'dir' => '/' ])->render(),
    			'files' => $files,
                'is_logged' => $is_logged,
    		]);  		
    	}catch(\Exception $e){
    		return response()->json([
    			'successs' => false,
    			'message' => $e->getMessage(),
                'is_logged' => $is_logged,
    		]);
    	}
    }

    //FTP files by dir
    public function ftpFilesByDir(Request $request){
        $is_logged = false;
    	try{

            if(!Session::exists('drm_ftp_user_credentials'.CRUDBooster::myId())){
                return response()->json([
                    'success' => true,
                    'is_logged' => false,
                ]);
            }
            $this->init(Session::get('drm_ftp_user_credentials'.CRUDBooster::myId()));
            $is_logged = true;

    		$dir = $request->directory ?? '/';
    		
	    	$allPaths = $this->filesystem->listContents($dir);
	    	$files = array_filter($allPaths, function($item){
				return strtolower($item['type'] === 'file');
			});

            Session::put('drm_ftp_current_dir'.CRUDBooster::myId(), $dir);

			return response()->json([
    			'successs' => true,
    			'files' => $files,
                'is_logged' => $is_logged,
    		]);  		
    	}catch(\Exception $e){
    		return response()->json([
    			'successs' => false,
    			'message' => $e->getMessage(),
                'is_logged' => $is_logged,
    		]);
    	}
    }

    //Store file to ftp
    public function storeFileToFtp(Request $request)
    {
        $is_logged = false;
        try{
            if(!Session::exists('drm_ftp_user_credentials'.CRUDBooster::myId())){
                return response()->json([
                    'success' => true,
                    'is_logged' => false,
                ]);
            }
            $this->init(Session::get('drm_ftp_user_credentials'.CRUDBooster::myId()));
            $is_logged = true;

            if ($request->hasFile('ftp_file')) {
                $file = $request->file('ftp_file');
                $fileName = pathinfo($file->getClientOriginalName(),PATHINFO_FILENAME).'_'.time().'.'.pathinfo($file->getClientOriginalName(),PATHINFO_EXTENSION); // original name that it was uploaded with
                $path = Session::get('drm_ftp_current_dir'.CRUDBooster::myId())?? '/';
                $remoteName = $path.'/'.$fileName;
                // upload a file
                if ($this->filesystem->put($remoteName, file_get_contents($file) )) {
                    $status = true;
                    $message =  "successfully uploaded $fileName";
                } else {
                    $status = false;
                    $message =  "There was a problem while uploading $fileName";
                }
            } else {
                $status = false;
                $message = 'No file selected';
            }
            return response()->json(['success' => $status,'message' => $message, 'dirname' => $path, 'is_logged' => $is_logged], 200);            
        }catch(\Exception $e){
            return response()->json(['success' => false,'message' => $e->getMessage(), 'is_logged' => $is_logged]);
        }
    }

    //Load FTP modal to import module
    public function importModal(Request $request){
        try{
            $ftp_logged = Session::exists('drm_ftp_user_credentials'.CRUDBooster::myId());

            if($ftp_logged){
                return response()->json([
                    'success' => true,
                    'html' => view('ftp.index-content')->render(),
                    'is_logged' => true,
                ]);                
            }else{
               return $this->loginView($request); 
            }
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
