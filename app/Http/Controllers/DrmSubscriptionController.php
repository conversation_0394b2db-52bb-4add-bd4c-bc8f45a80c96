<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\DrmSubscription;
use App\User;
use DB;
use App\Notifications\DRMNotification;
use App\NewOrder;
use App\StripePayment;
use ServiceKey;
use CRUDBooster;

use App\Mail\DRMSEndMail;
use Illuminate\Support\Facades\Mail;

class DrmSubscriptionController extends Controller
{
	//Subscription action
    public function subscriptionAction(){

    }

    //purchase SCA
    public function purchaseSCA($purchase_data){
		// //DB::beginTransaction();
    	try{

    		$message = [];

    		$subscription_type = $purchase_data['type'];
    		$drm_sub_id = $purchase_data['drm_sub_id'];
    		$intend_id = $purchase_data['id'];

    		$purchase_data['manual_inv_ref'] = DB::table('manual_subscriptions')->where('id', $drm_sub_id)->value('inv_ref');


            if(DB::table('new_orders')->whereIn('cms_user_id', [98, 2454, 2455, 2439])->where(['order_id_api' => $intend_id, 'shop_id' => 8])->exists()){
            	throw new \Exception('Already Exist!');  //STRIPE_CLIENT
            }

	    	$user = User::with('billing_detail')->find($purchase_data['user_id']);
	    	if(is_null($user)) throw new \Exception('User not Exist!');

	    	// if(is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

	    	//Increment single pay coupon usages
            if (isset($purchase_data['coupon']) && $purchase_data['coupon']) {
                DB::table('coupons')->where('coupon_id', $purchase_data['coupon'])->increment('single_pay');
            }

	    	if($subscription_type == 'subscription_plan'){
				if($this->appPurchase($purchase_data, $user)){
					$message = ['success'=> true, 'message'=>'App purchase success!'];
				}else{
					throw new \Exception('Purchase failed!');
				}
	    	}else if($subscription_type == 'subscription_import'){
				if($this->importPurchase($purchase_data, $user)){
					$message = ['success'=> true, 'message'=> __('Import Plan Purchase success!'), 'return_url'=> CRUDBooster::adminPath('drm_imports/import') ];
				}else{
					throw new \Exception('Purchase failed!');
				}
	    	}else if($subscription_type == 'subscription_template'){
				if($this->templatePurchase($purchase_data, $user)){
					$message = ['success'=> true, 'message'=>'Template purchase success!', 'return_url'=> CRUDBooster::adminPath('template-store') ];
				}else{
					throw new \Exception('Purchase failed!');
				}
	    	}else{
	    		throw new \Exception('Sorry, Purchase failed!');

	    	}

	    	//Update Subscription
            DB::table('manual_subscriptions')->where('id', $drm_sub_id)->update([
            	'price'=> $purchase_data['sub_total'],
            	'start_date'=> $purchase_data['period_start'],
            	'end_date'=> $purchase_data['period_end'],
            	'status'=> 1,
            	'payment_status' => 'succeeded',
            	'inv_ref' => null,
            ]);
            
			////DB::commit();// Commiting  ==> There is no problem whatsoever
		    return $message;
	    }catch (\Exception $e) {
	        ////DB::rollBack();// rollbacking  ==> Something went wrong
	        return ['success'=> false, 'message'=>$e->getMessage()];
	    }

    }



    //purchase app
    private function appPurchase($purchase_data, User $user){

    	//DB::beginTransaction();
    	try{
			$app = DB::table('app_stores')->find($purchase_data['app_id']);
            if( is_null($app) ) throw new \Exception('Invalid app!');

            $intend_id = $purchase_data['id'];
            if(NewOrder::where(['order_id_api' => $intend_id, 'cms_user_id' => 2455, 'shop_id' => 8])->exists()) throw new \Exception('Already purchased!');

            $user = User::with('billing_detail')->find($purchase_data['user_id']);
            // if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

		    //Cancel old subscription
	    	$old = DB::table('purchase_apps')->where(['cms_user_id' => $user->id, 'app_id' => $app->id])->whereNotNull('stripe_subscription_id')->first();

            if($old !=null){
            	resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2455', '', $old->stripe_subscription_id);
            }

            $purchare_db = [];
	    	$purchare_db['plan_id']                 = $purchase_data['plan_id'];
            $purchare_db['stripe_plan_id']          = $purchase_data['stripe_plan_id'];
            $purchare_db['stripe_customer_id']      = $purchase_data['stripe_customer_id'];
            $purchare_db['payer_email']             = $user->email;
            $purchare_db['type']                    = $purchase_data['interval_type'];
            $purchare_db['stripe_subscription_id']  = $purchase_data['subscription_id'];
            $purchare_db['status']                  = 'active';
            $purchare_db['subscription_date_start'] = $purchase_data['period_start'];
            $purchare_db['subscription_date_end']   = $purchase_data['period_end'];

            $filter_purchase_data = array_filter($purchare_db);
            if($filter_purchase_data){
                DB::table('purchase_apps')->updateOrInsert([
                    'app_id' => $app->id, 'cms_user_id' => $user->id],
                    $filter_purchase_data
                );
            }

	    	$invoice_data = [];
	    	$invoice_data['discount'] = $purchase_data['discount'];
            $invoice_data['total'] = $purchase_data['total'];
            $invoice_data['sub_total'] = $purchase_data['sub_total'];
            $invoice_data['price'] = $purchase_data['sub_total'];
            $invoice_data['product_name'] = $app->menu_name;
            $invoice_data['description'] = 'App Store Purchase Complete. App Name is "' . $app->menu_name.'"';
            $invoice_data['subscription_id'] = $purchase_data['subscription_id'];
            $invoice_data['id'] = $purchase_data['id'];
            $invoice_data['client_id'] = 2455;  //STRIPE_CLIENT_DT

            $payment_intend_id = $purchase_data['intend_id'] ?? null;
            $invoice_data['intend_id'] = $payment_intend_id;

            $invoice_data['manual_inv_ref'] = isset($purchase_data['manual_inv_ref']) ? $purchase_data['manual_inv_ref'] : null;
            $this->updateInvoice($invoice_data);

			if (in_array($app->id, [42, 45])) {
                updateUserAppsDate($user->id, $app->id, $purchase_data['plan_id'], $purchase_data['period_end']);
            }

	    	//DB::commit();// Commiting  ==> There is no problem whatsoever
	    	return true;
    	}catch(\Exception $e){
    		//DB::rollBack();// rollbacking  ==> Something went wrong
    		return false;
    	}
    }

    //Import plan purchase
    private function importPurchase($purchase_data, User $user){
    	//DB::beginTransaction();
    	try{
			$database_plan = DB::table('import_plans')->find($purchase_data['plan_id']);
	    	$old = DB::table('purchase_import_plans')->where('cms_user_id', $user->id)->first();

		    //Cancel old subscription
	    	if($old !=null){
	    		resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2455', '', $old->stripe_subscription_id);
	    	}

	    	$purchare_db = [];
		    $purchare_db['price']                 	= $purchase_data['total'];
		    $purchare_db['import_plan_id']         	= $purchase_data['plan_id'];
	        $purchare_db['stripe_plan_id']          = $purchase_data['stripe_plan_id'];
	        $purchare_db['stripe_customer_id']      = $purchase_data['stripe_customer_id'];
	        $purchare_db['payer_email']             = $user->email;
	        $purchare_db['type']                    = $purchase_data['interval_type'];
	        $purchare_db['stripe_subscription_id']  = $purchase_data['subscription_id'];
	        $purchare_db['status']                  = 1;
	        $purchare_db['start_date'] 				= $purchase_data['period_start'];
	        $purchare_db['end_date']   				= $purchase_data['period_end'];
	        $purchare_db['product_amount_import']   = $database_plan->product_amount;
	    	DB::table('purchase_import_plans')->updateOrInsert( ['cms_user_id'=> $user->id], $purchare_db);



	    	$invoice_data = [];
	    	$invoice_data['discount'] = $purchase_data['discount'];
            $invoice_data['total'] = $purchase_data['total'];
            $invoice_data['sub_total'] = $purchase_data['sub_total'];
            $invoice_data['price'] = $database_plan->amount;
            $invoice_data['product_name'] = $database_plan->plan;
            $invoice_data['description'] = 'Import Plan Purchase. Plan Name is "'.$database_plan->plan;
            $invoice_data['subscription_id'] = $purchase_data['subscription_id'];
            $invoice_data['id'] = $purchase_data['id'];
            $invoice_data['client_id'] = 2455;  //STRIPE_CLIENT_DT

            $payment_intend_id = $purchase_data['intend_id'] ?? null;
			$invoice_data['intend_id'] = $payment_intend_id;

            $invoice_data['manual_inv_ref'] = isset($purchase_data['manual_inv_ref']) ? $purchase_data['manual_inv_ref'] : null;
            $this->updateInvoice($invoice_data);



	    	//DB::commit();// Commiting  ==> There is no problem whatsoever

	    	return true;
    	}catch(\Exception $e){
    		//DB::rollBack();// rollbacking  ==> Something went wrong
    		return false;
    	}
    }


    //purchase template
    private function templatePurchase($purchase_data, User $user){

    	//DB::beginTransaction();
    	try{

			$drm_subscription = DB::table('manual_subscriptions')->find($purchase_data['drm_sub_id']);
            if( is_null($drm_subscription) ) throw new \Exception('Invalid subscription!');

            $subscription_id = $purchase_data['subscription_id']; //Subscription id

            $intend_id = $purchase_data['id'];
            if(NewOrder::where(['order_id_api' => $intend_id, 'cms_user_id' => 2439, 'shop_id' => 8])->exists()) throw new \Exception('Already purchased!');

            $user = User::with('billing_detail')->find($purchase_data['user_id']);

			//Subscription logic
            if($purchase_data['interval_type'] == 'year'){
				$purchase_data['period_start'] = now();
            	$purchase_data['period_end'] = now()->addYear();
            }else{
            	$purchase_data['period_start'] = now();
	            $purchase_data['period_end'] = now()->addMonth();
            }

            //Initial purchase data
            $subscription_data = [
                'identity' => $purchase_data['id'],
                'updated_at' => \Carbon\Carbon::now(),
                'status' => 1,
                'end_date' => $purchase_data['period_end']
            ];

            $is_flat_rate = $drm_subscription->plan_id > 13;
            $f_r_text = ($is_flat_rate)? 'flat rate' : 'plan';
            $template_description = 'Droptienda template '.ucfirst($purchase_data['interval_type']).'ly '.$f_r_text.' purchase. End date: '. $purchase_data['period_end'];


            //Update subscription
            if($is_flat_rate){
                DB::table('dt_flat_rates')->where('user_id', $user->id)->where('subscription_id', $subscription_id)->update($subscription_data);
            }else{
                $subscription_data['deleted_at'] = null;
                DB::table('template_purchases')->where([
                    'user_id' => $cms_client,
                    'template_id' => $purchase_data['app_id'],
                ])
                ->where('subscription_id', $subscription_id)
                ->update($subscription_data); 
            }

	    	$invoice_data = [];
	    	$invoice_data['discount'] = $purchase_data['discount'];
            $invoice_data['total'] = $purchase_data['total'];
            $invoice_data['sub_total'] = $purchase_data['sub_total'];
            $invoice_data['price'] = $purchase_data['sub_total'];
            $invoice_data['product_name'] = 'Droptienda template subscription '.$subscription_id;
            $invoice_data['description'] = $template_description;
            $invoice_data['subscription_id'] = $subscription_id;
            $invoice_data['id'] = $purchase_data['id'];
            $invoice_data['client_id'] = 2439;  //STRIPE_CLIENT_DT

            $payment_intend_id = $purchase_data['intend_id'] ?? null;
			$invoice_data['intend_id'] = $payment_intend_id;

            $invoice_data['manual_inv_ref'] = isset($purchase_data['manual_inv_ref']) ? $purchase_data['manual_inv_ref'] : null;
            $this->updateInvoice($invoice_data);

	    	//DB::commit();// Commiting  ==> There is no problem whatsoever
	    	return true;
    	}catch(\Exception $e){
    		//DB::rollBack();// rollbacking  ==> Something went wrong
    		return false;
    	}
    }

    //update invoice
    private function updateInvoice($invoice_data){

		$discount = $invoice_data['discount']?? 0;
        $total = $invoice_data['total']?? 0;
        $sub_total = $invoice_data['sub_total']?? 0;
        $sub_id = $invoice_data['subscription_id'];
        $intend_id = $invoice_data['id'];

        $client_id = $invoice_data['client_id'];
        $payment_intend_id = $invoice_data['intend_id'] ?? null;

    	//Insert order to daily account
		$taxShow = config('global.tax_for_invoice');
		$price = $invoice_data['price']; //$request->fixed_price.'00';
		$order_info = [
			'total' => round($total,2),
			'sub_total' => round($sub_total,2),
			'discount' => round($discount, 2),
			'payment_type'  => "Stripe Card",
			'status'    => "paid",
			'order_id_api'  => $intend_id,
			'order_date' => date('Y-m-d H:i:s'),
			'intend_id' => $payment_intend_id,
		];

		$carts = [];
		$cart_item = [];
		$cart_item['id'] = 1;
		$cart_item['product_name'] = $invoice_data['product_name'];
		$cart_item['description'] = $invoice_data['description'];
		$cart_item['qty'] = 1;
		$cart_item['rate'] = round($price,2);
		$cart_item['tax'] = $taxShow;
		$cart_item['product_discount'] = 0;
		$cart_item['amount'] = round($price, 2);
		$carts[] = $cart_item;
		$order_info['cart'] = json_encode($carts);
		if($sub_id && (strpos($sub_id, 'drm_') !== false)){
			DB::table('new_orders')->where('cms_user_id', $client_id)->where('order_id_api', $sub_id)->update($order_info);
		}

		//Update invoice ref
		if(isset($invoice_data['manual_inv_ref']) && !empty($invoice_data['manual_inv_ref']))
		{
			$manual_inv_ref = $invoice_data['manual_inv_ref'];
			DB::table('new_orders')->where('cms_user_id', $client_id)->where('order_id_api', $manual_inv_ref)->update($order_info);
		}
    }

    //Failed payment action
    public function failedPayment($object_id){
    	//1. Check if paywall and Drm manual subscription - processing payment failed
    	$payment = StripePayment::whereIn('purchase_type', [6, 7])->whereNotIn('status', ['succeeded'])->where('object_id', $object_id)->first();
    	if($payment){
    		$purchase_data = $payment->data;
    		if($payment->purchase_type == 7){
    			$sub_id = $purchase_data['subscription_id'];
    			if($sub_id && (strpos($sub_id, 'drm_') !== false)){
					// DB::table('new_orders')->where('cms_user_id', 98)->where('order_id_api', $sub_id)->update(['status' => 'incasso']);
					DB::table('manual_subscriptions')->where('id', $purchase_data['drm_sub_id'])->update(['payment_status' => 'due']);
					//DRM Subscription status set to due
				}
    		}else if($payment->purchase_type == 6){
    			$paywall_id = $purchase_data['paywall_id'];
    			DB::table('monthly_paywalls')->where('id', $paywall_id)->update(['status' => 'due']);
    		}
    	}
    }

    //subscription payment email
    public function failedSubscriptionPayment($object_id){
    	try{
	    	//1. Check if paywall and Drm manual subscription - processing payment failed
	    	$payment = StripePayment::whereIn('purchase_type', [1, 5])->whereNotIn('status', ['succeeded', 'active'])->where('object_id', $object_id)->first();
	    	if($payment){
	    		$purchase_data = $payment->data;

	    		$sub_id = $purchase_data['subscription_id'];
	    		$hosted_invoice_url = $purchase_data['hosted_invoice_url'];

	    		$item = null;
	    		if($payment->purchase_type == 1){

					$item = DB::table('purchase_apps')
			            ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
			            ->join('subscription_plans','purchase_apps.plan_id','subscription_plans.id')
			            ->where('purchase_apps.stripe_subscription_id',$sub_id)
			            ->select('purchase_apps.type as interval','purchase_apps.cms_user_id as user_id','app_stores.menu_name as item_name', 'purchase_apps.id as pay_item_id', 'subscription_plans.price as amount')
			            ->first();
	    		}else if($payment->purchase_type == 5){
	    			$item = DB::table('purchase_import_plans')
			            ->join('import_plans','import_plans.id','=','purchase_import_plans.import_plan_id')
			            ->where('purchase_import_plans.stripe_subscription_id',$sub_id)
			            ->select('import_plans.interval as interval','purchase_import_plans.cms_user_id as user_id','import_plans.plan as item_name','purchase_import_plans.id as pay_item_id', 'import_plans.amount as amount')
			            ->first();
	    		}


	    		if($sub_id && $item && $hosted_invoice_url){

	    			$total 		= $purchase_data['total'];
	    			$sub_total 	= $purchase_data['sub_total'];
	    			$discount 	= $purchase_data['discount'];

	    			$user = User::find($item->user_id);

	    			if(!DB::table('new_orders')->where('cms_user_id', 2455)->where('order_id_api', $object_id)->exists()){
			            $taxShow = config('global.tax_for_invoice');
			            $total_tax = ($total * $taxShow) / 100;
			            $order_info = [
			                'user_id' => 2455,
			                'cms_client' => $user->id,
			                'order_date' => date('Y-m-d H:i:s'),
			                'total' => round(($total), 2),
			                'sub_total' => round($sub_total, 2),
			                'discount' => round($discount, 2),
			                'discount_type' => 'fixed',
			                'total_tax' => 0,
			                'payment_type' => "Stripe Card",
			                'status' => "reminder_inkasso",
			                'currency' => "EUR",
			                'adjustment' => 0,
			                'insert_type' => 3,
			                'shop_id' => 8,
			                'order_id_api' => $object_id,
			            ];

			            $carts = [];
			            $cart_item = [];
			            $cart_item['id'] = 1;
			            $cart_item['product_name'] = $item->item_name;
			            $cart_item['description'] = $object_id;
			            $cart_item['qty'] = 1;
			            $cart_item['rate'] = round($sub_total, 2);
			            $cart_item['tax'] = $taxShow;
			            $cart_item['product_discount'] = 0;
			            $cart_item['amount'] = round($sub_total, 2);
			            $carts[] = $cart_item;
			            $order_info['cart'] = json_encode($carts);
			            app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $user->id);
	    			}

	    			//Make job to send 7 days email
		    		$tags = [
					    'user' =>  $user->name,
					    'due_desctiption' =>  $item->item_name,
					    'amount' =>  number_format((float)$item->amount, 2, ',', '.').' EUR',
					    'date' =>  date('Y-m-d'),
					    'pay_url' =>  $purchase_data['hosted_invoice_url'],
					];

					// $user_email = $user->email;
					// if(filter_var($user_email, FILTER_VALIDATE_EMAIL)){
					// 	$slug = 'stripe_payment_failed'; //Page slug
					// 	$mail_data = DRMParseMailTemplate($tags, $slug); //Generated html
					// 	app('drm.mailer')->getMailer()->to($user_email)->send(new DRMSEndMail($mail_data)); //Send
					// }else{
					// 	throw new \Exception('Invalid user email. User ID:'.$paywall->user_id);
					// }
	    		}
	    	}

    	}catch(\Exception $e){}
    }

}
