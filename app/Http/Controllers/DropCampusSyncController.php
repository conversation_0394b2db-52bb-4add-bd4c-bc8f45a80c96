<?php
// SED -> ll

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\User;
use App\NewCustomer;
use App\DropfunnelCustomerTag;
use App\NewOrder;
use Illuminate\Support\Facades\Log;
use App\Notifications\DRMNotification;
use Illuminate\Support\Facades\Validator;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;

class DropCampusSyncController extends Controller
{
    public $cms_user_id = 2454;

    // sync customers details to dropcampus account
    public function syncCustomer()
    {
        try {
            $data = json_decode(request()->getContent(), true);
            $email = $data['email'];
            if(empty($email)) return;

            $customer_info = [
                "customer_full_name" => $data['firstname'] . " " . $data['lastname'],
                'insert_type' => 2,
                "currency" => 'EUR',
                "company_name" => '',
                'default_language' => 'DE',
                'email' => $email,
                "phone" => $data['mobile'],
                'city' => $data['address']['city'] ?? '',
                'state' => $data['address']['state'] ?? '',
                'zip_code' => $data['address']['zip'] ?? '',
                'address' => $data['address']['address'] ?? '',
                'country' => $data['address']['country'] ?? '',

                //shipping
                'street_shipping' => $data['address']['address'] ?? '',
                'city_shipping' => $data['address']['city'] ?? '',
                'state_shipping' => $data['address']['state'] ?? '',
                'zipcode_shipping' => $data['address']['zip'] ?? '',
                'country_shipping' => $data['address']['country'] ?? '',

                //billing
                'street_billing' => $data['address']['address'] ?? '',
                'city_billing' => $data['address']['city'] ?? '',
                'state_billing' => $data['address']['state'] ?? '',
                'zipcode_billing' => $data['address']['zip'] ?? '',
                'country_billing' => $data['address']['country'] ?? '',
                'user_id' => $this->cms_user_id,
            ];

            //Add customer to dropcampus account
            $customerId =  app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);
            DropfunnelCustomerTag::insertTag('dropcampus', 2454, $customerId, 24);

            $message_title = 'A customer is synced from dropcampus!' . PHP_EOL . 'Email: ' . $data['email'];
            User::find($this->cms_user_id)->notify(new DRMNotification($message_title, 'CustomerSyncUserscreen'));

        } catch (\Exception $e) {}
    }

    // transactions sync from dropcampus
    public function syncTransaction()
    {
        try {

            $data = json_decode(request()->getContent(), true);

            // $validator = Validator::make($data, [
            //     'customer_email' => 'required',
            //     'total' => 'required',
            // ]);
            
            if (is_null($data)) {
                throw new \Exception("Invalid VOD data!");
            }

            $customer = $data['user'];
            $email = $customer['email'];

            if(empty($email)) return;

            $customer_info = [
                "full_name" => $customer['name'],
                "currency" => 'EUR',
                'default_language' => 'DE',
                'insert_type' => 2,
                'vat_number' => $customer['vat_id'] ?? '',
            ];
            
            //DropCampusCustomer
            $customer = NewCustomer::updateOrCreate([
                'email' => $email,
                'user_id' => $this->cms_user_id,
            ],
                $customer_info
            );

            $order_info['user_id'] = $this->cms_user_id;
            $transaction = $data['transaction'];

            $tax_rate = $transaction['tax_rate'] ?? 0;
            $discount = $transaction['discount'] ?? 0;
            $sub_total = round($transaction['actual_amount'],2) ?? 0;

            // $tax = ($sub_total - $discount) * ($tax_rate / 100);
            // $total = $sub_total + $tax - $discount;
            $tax = $transaction['tax'] ?? 0;
            $total = round($transaction['final_amo'],2) ?? 0;
            
            $product_price = $total;
            $product_name = $transaction['product_name'];

            $product_name = preg_replace('/[\[{\(].*?[\]}\)]/' , '', $product_name);
            $product_name = trim($product_name);
            
            if (isset($customer->id)) {
                $order_info['drm_customer_id'] = $customer->id;
            }

            $order_info['shop_id'] = 108;
            $order_info['total'] = $total;
            $order_info['insert_type'] = 2;
            $order_info['total_tax'] = $tax;
            $order_info['order_date'] = date('Y-m-d H:i:s',strtotime($transaction['created_at']));
            $order_info['tax_rate'] = $transaction['tax_rate'] ?? NULL;
            $order_info['order_id_api'] = $transaction['transaction_id'];

            $order_info['adjustment'] = 0;
            $order_info['currency'] = 'EUR';
            $order_info['discount'] = $discount;
            $order_info['sub_total'] = $sub_total;
            $order_info['discount_type'] = "fixed";
            $order_info['payment_type'] = 'Stripe Card';
            $order_info['billing'] = vodCustomerAddress($customer);
            $order_info['shipping'] = vodCustomerAddress($customer);
            $order_info['customer_info'] = vodCustomerInfo($customer);
            $order_info['vat_number'] = $customer->vat_number;
            $order_info['tax_version'] = 1;
            

            $order_info['status'] = drmOrderLabelByGroupId('paid');

            $item_price = $transaction['actual_amount'];

            $cart_item = [[
                "id" => 1,
                "product_name" => $product_name,
                'qty' => 1,
                'rate' => $item_price, //$transaction['actual_amount'],
                'unit' => 1,
                'offer_id' => $transaction['coupon_id'] ?? NULL,
                'tax' => $transaction['tax_rate'],
                'product_discount' => $discount,
                'amount' => $item_price,
            ]];

            $order_info['cart'] = json_encode($cart_item);

            $total = (float)$transaction['actual_amount'];
            if($total > 89)
            {
                $tag = '6 Monate';
            }else if($total > 23)
            {
                $tag = '1 Monat';
            }else if( $total  > 0)
            {
                $tag = 'Wochenzugang';
            }else {
                $tag = 'Buchlogin';
            } 
    
            app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
            DropfunnelCustomerTag::insertTag($tag, 2454, $customer->id, 24);

            $message_title = 'A transaction occurred in DropCampus!' . PHP_EOL . 'Customer Email:' . $email . PHP_EOL . 'Product Name:' . $product_name . PHP_EOL . 'Amount: ' . $total;
            User::find($this->cms_user_id)->notify(new DRMNotification($message_title, 'DROPCAMPUS_TRANSACTION'));

        } catch (\Exception $e) {
            Log::channel('uscreen')->info($e->getMessage());
        }

        // $transaction_id = $transaction['transaction_id'];
        // $order_id = NewOrder::where([
        //         ['cms_user_id', '=', $this->cms_user_id],
        //         ['order_id_api', '=', $transaction_id]
        //     ])->value('id');

        // return response()->json(compact('order_id', 'transaction_id'), 200);
    }

    // Add customer-delete-tag to dropcampus's customer
    public function customerDelete()
    {
        try {
            $data = json_decode(request()->getContent(), true);

            if(empty($data)) return;

            //Insert to DropCampus
            $dropCampus_customer = NewCustomer::where(['email' => $data['email'], 'user_id' => $this->cms_user_id])->first();
            if ($dropCampus_customer) {
                $tag = ucfirst($data['title']);

                //insert tag
                if($tag){
                    try{
                        DropfunnelCustomerTag::insertTag($tag, $this->cms_user_id, $dropCampus_customer->id, 22);
                    }catch(\Exception $ev){}
                }

                $customer_url = CRUDBooster::adminPath('drm_all_customers/detail/' . $dropCampus_customer->id);
                $message_title = $dropCampus_customer->full_name . "'s " . $tag;
                User::find($this->cms_user_id)->notify(new DRMNotification($message_title, 'DROPCAMPUS_CUSTOMER_DELETE', $customer_url));
            }

        } catch (\Exception $e) {
            User::find(71)->notify(new DRMNotification('customer delete error ' . $e->getMessage() . ' Line:' . $e->getLine(), 'DROPCAMPUS_CUSTOMER_DELETE', '#'));
        }
    }

    // payment reject tag to customer
    public function paymentReject()
    {
        try {
            $data = json_decode(request()->getContent(), true);

            if(empty($data)) return;

            //Insert to DropCampus
            $dropCampus_customer = NewCustomer::where(['email' => $data['email'], 'user_id' => $this->cms_user_id])->first();
            if ($dropCampus_customer) {
                $tag = ucfirst($data['title']);

                //insert tag
                if($tag){
                    try{
                        DropfunnelCustomerTag::insertTag($tag, $this->cms_user_id, $dropCampus_customer->id, 23);
                    }catch(\Exception $ev){}
                }

                $customer_url = CRUDBooster::adminPath('drm_all_customers/detail/' . $dropCampus_customer->id);
                $message_title = $dropCampus_customer->full_name . "'s " . $tag;
                User::find($this->cms_user_id)->notify(new DRMNotification($message_title, 'DROPCAMPUS_PAYMENT_REJECT', $customer_url));
            }

        } catch (\Exception $e) {
            User::find(71)->notify(new DRMNotification('add payment reject tag error ' . $e->getMessage() . ' Line:' . $e->getLine(), 'DROPCAMPUS_PAYMENT_REJECT', '#'));
        }
    }

    public function decryptor($encrypted_value) 
    {
        $options = 0;
        $ciphering = "AES-128-CTR"; //Cipher method
		
		$encryption_iv = "gmk9SnVPrr0LagSM"; // Non-NULL Initialization Vector for encryption
		$encryption_key = "6B4C4ECD35DED"; //encryption key
        
        $encrypted_value = base64_decode($encrypted_value); // To avoid '/' in the url

		return openssl_decrypt($encrypted_value, $ciphering, $encryption_key, $options, $encryption_iv);
    }

    public function getInvoice($slug)
    {
        $transaction_id = $this->decryptor($slug);
        $order_id = NewOrder::where([
                ['cms_user_id', '=', $this->cms_user_id],
                ['order_id_api', '=', $transaction_id]
            ])->value('id');

        if (empty($order_id)) abort(404);

        return app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_invoice_pdf($order_id);
    }

    public function testRoute()
    {
        try {
            $res = NewOrder::with('customer')->where([
                ['cms_user_id', '=', $this->cms_user_id],
                ['order_date', '>', '2022-01-07'],
                ['order_id_api', 'like', '%coupon_%'],
            ])
            ->get()
            ->map(function($item){
                return [
                    'email' => $item->customer->email,
                    'order_id_api' => $item->order_id_api,
                    'product_name' => json_decode($item->cart)[0]->product_name
                ];
            })->toArray();
        }
        catch (Exception $e) {
            $res = $e;
        }

        return response()->json(compact('res'), 200);
    }
}