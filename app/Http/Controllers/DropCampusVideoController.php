<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\DropCampus\DropCampusVideoService;
use App\User;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Notifications\DRMNotification;

class DropCampusVideoController extends Controller
{
    private DropCampusVideoService $video_service;

    public function __construct(DropCampusVideoService $service)
    {
        $this->video_service = $service;
    }

    public function insertVideoTagToDrm(Request $request)
    {
        if(!$request->secret_api_key){
            return response()->json([
                'status'=>400,
                'success'=>false,
                'message'=>"Secret api is not found",
            ]);
        }

        $key_varify = $this->video_service->secretKeyVarify($request->secret_api_key);

        if($key_varify){
            $data = $request->all();

            if(empty($data['domain_name'])) return;

            $domain_name = $data['domain_name'];
            $user_id = '';
            if($domain_name == 'dropmatix'){
                $user_id = 2455;
            }elseif($domain_name == 'expertise'){
                $user_id = 98;
            }

            $drm_customer = $this->video_service->getCustomerIdByEmail($data['email'], $user_id);

            if ($drm_customer) {
                $title = $data['title'];

                //insert tag
                if($title){
                    $this->video_service->insertTagToDrm($title, $user_id, $drm_customer->id, 3);

                    $tag_score = $this->video_service->checkTagExists($user_id, $title, $drm_customer->id);

                    // Only at first time notification will send
                    if($tag_score == 1){
                        $customer_url = CRUDBooster::adminPath('drm_all_customers/detail/' . $drm_customer->id);
                        $message_title = $drm_customer->full_name . ' watched ' . $title;
                        User::find($user_id)->notify(new DRMNotification($message_title, 'DROPCAMPUS_WATCHED_VIDEO', $customer_url));
                    }
                    
                    return response()->json([
                        'status'=> 200,
                        'success'=> true,
                        'message'=> 'Tag Inserted Successfully !',
                    ]);
                }

            }else{
                return response()->json([
                    'status'=> 400,
                    'success'=> false,
                    'message'=> "No Customer Found !",
                ]);
            }

        }else{
            return response()->json([
                'status'=> 400,
                'success'=> false,
                'message'=>"Secret api is not matched",
            ]);
        }
    }
}
