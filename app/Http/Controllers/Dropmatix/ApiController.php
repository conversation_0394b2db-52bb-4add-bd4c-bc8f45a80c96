<?php
namespace App\Http\Controllers\Dropmatix;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

use App\User;
use App\NewOrder;
use App\Jobs\InvoiceArchiveJob;
use App\Notifications\DRMNotification;
use App\Services\ProductApi\TransferProduct; //Transfer product to API
use App\Jobs\UpdateProductRq;

use Exception;

class ApiController extends Controller
{
    const TOKEN = 'eyij8je90AcD1';

    //Archive invoices
    public function archiveInvoice(Request $request): JsonResponse
    {
        try {

            $this->verify();

            $rules = ['orders' => 'required|array|min:1', 'user_id'   => 'required'];
            $request->validate($rules);
            $data = $request->only(array_keys($rules));


            $user = User::find($data["user_id"]);
            $order_ids = $data["orders"];

            if ($user && $order_ids) {
                $orders = NewOrder::whereIn('id', $order_ids)->pluck('id')->toArray();
                if (count($orders)) {
                    InvoiceArchiveJob::dispatch($orders, $user)->onQueue('file-archive');
                    return response()->json([
                        'success' => true,
                        'message' => 'Archive processing! After completing the process, we sent you notification!',
                    ]);
                } else {
                    throw new Exception("Invalid order selection. Please select only valid invoices.");
                }
            } else {
                throw new Exception("Error Processing Request");
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Archive failed. Error: ' . $e->getMessage(),
            ], 400);
        }
    }


    //Send notification
    public function sendNotification(Request $request): JsonResponse
    {
        try {

            $this->verify();

            $rules = $this->notificationRules();
            $request->validate($rules);
            $data = $request->only(array_keys($rules));

            $massage = $data['message'];
            $hook = $data['hook'] ?? '';
            $url = $data['url'] ?? '';
            $room_id = $data['room_id'] ?? '';

            $user = User::find($data["user_id"]);
            User::find($data['user_id'])->notify(new DRMNotification($massage, $hook, $url, '', $room_id));

            return response()->json([
                'success' => true,
                'message' => 'Notification send successfully',
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Notification send failed. Error: ' . $e->getMessage(),
            ], 400);
        }
    }

    //Notification rules
    private function notificationRules(): array
    {
        return [
            'user_id'  => 'required|numeric',
            'message'  => 'required',
            'url'      => 'nullable',
            'hook'     => 'nullable',
            'room_id'  =>  'nullable',
        ];
    }


    //Update product rq
    public function updateProductRq(Request $request): JsonResponse
    {
        try {

            $this->verify();

            $rules = ['order_id' => 'required', 'user_id'   => 'required'];
            $request->validate($rules);
            $data = $request->only(array_keys($rules));

            $order = NewOrder::where('cms_user_id', $data['user_id'])
                ->where('id', $data['order_id'])
                ->whereNotNull('shop_id')
                ->select('cart', 'shop_id', 'cms_user_id')
                ->first();

            if($order && $order->id)
            {
                $carts = json_decode($order->cart);
                UpdateProductRq::dispatch($carts, $order->cms_user_id, $order->shop_id);

                return response()->json([
                    'success' => true,
                    'message' => 'RQ request send successfully',
                ]);
            }

            throw new Exception('Order not found!');

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'RQ request send failed. Error: ' . $e->getMessage(),
            ], 400);
        }
    }

    //Campaign request
    public function sendCampaign(Request $request): JsonResponse
    {
        try {

            $this->verify();

            $rules = ['tag_id' => 'required'];
            $request->validate($rules);
            $data = $request->only(array_keys($rules));

            app('App\Http\Controllers\NewShopSyncController')->sendCampaignMailJobAfterInserting($data['tag_id']);

            return response()->json([
                'success' => true,
                'message' => 'Campaign request send successfully',
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Campaign request send failed. Error: ' . $e->getMessage(),
            ], 400);
        }
    }


    //Campaign request
    public function sendOrderToInternel(Request $request): JsonResponse
    {
        try {

            $this->verify();

            $rules = ['order_id' => 'required'];
            $request->validate($rules);
            $data = $request->only(array_keys($rules));

            //Send order to internel
            app('\App\Services\Marketplace\InternelSyncService')->transferOrderToInternel($data['order_id']);

            return response()->json([
                'success' => true,
                'message' => 'Internel request send successfully!',
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Internel request send failed. Error: ' . $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Send to analysis
     */
    public function sendToAnalysis(Request $request): JsonResponse
    {
        try {

            $this->verify();

            $rules = [
                'user_id' => 'required',
                'country_id' => 'required',
                'products_id' => 'required|array|min:1',
            ];

            $request->validate($rules);
            $data = $request->only(array_keys($rules));
            
            $res = app(TransferProduct::class)->transferToAnalysis($data['user_id'], $data['products_id'], $data['country_id']);

            return response()->json([
                'success' => true,
                'message' => 'Request send successfully!',
            ]);

        } catch (Exception $e) {

            \Illuminate\Support\Facades\Log::info($e->getMessage().' Line:'. $e->getLine());

            return response()->json([
                'success' => false,
                'message' => 'Request send failed. Error: ' . $e->getMessage(),
            ], 400);
        }
    }

    private function verify()
    {
        $token = request()->header('token');
        if($token != self::TOKEN) {
            throw new Exception("Token mismatch");
        }
    }
}
