<?php

namespace App\Http\Controllers\DroptiendaChat;

use App\ChatAnswer;
use App\ChatList;
use App\Http\Controllers\Controller;
use App\DroptiendaMessages;
use Carbon\Carbon;
use Illuminate\Http\Request;
use CRUDBooster;
use DateTime;
use DateTimeZone;
use DB;

class ChatController extends Controller
{


	public function index()
	{
        $data = [];
		$data['page_title'] = 'Droptienda order messages';

        $message_list = ChatList::with('order')
        ->orderBy('chat_sorting', 'DESC')
        ->where('user_id', CRUDBooster::myId())
        ->get()
        ->map(function($item){
            // echo '<pre>';
			// $item = $items->first();
            $shop_id = $item->order->shop_id;
            $order_id_api = $item->order->order_id_api;
            $id_api_str = 'drop_t' . $shop_id . '_';

            $dt_id = str_replace($id_api_str, '', $order_id_api);

            $unread = DroptiendaMessages::where(['order_id' => $item->order_id])
            ->whereNull('read_at')
            ->whereNull('user_id')
            ->count();

            return [
                'order_id' => $item->order_id,
                'status' => drmHistoryLabel($item->order->status),
                'is_read' => !empty($item->user_id) && !empty($item->read_at),
                'unread' => $unread,
                'recipient' => empty($item->user_id)? 'me' : 'other',
                'dt_id' => $dt_id,
            ];

        })
        ->toArray();


		$first_item = isset($_REQUEST['room']) && $_REQUEST['room'] ?  collect($message_list)->firstWhere('order_id', $_REQUEST['room']) : collect($message_list)->first();
		$messages = DroptiendaMessages::where('order_id', $first_item['order_id'])
        ->whereIn('chat_preference', ['', 'drm'])
		->select('id', 'message', 'read_at', 'order_id', 'user_id', 'created_at', 'sender', 'sent_time')
		->orderBy('id', 'asc')
		->get()
		->map(function($m) {

            $date = new DateTime("now", new DateTimeZone('Europe/Berlin') );
            $time_now = Carbon::parse($date->format('Y-m-d H:i:s'));
            $sent_time = Carbon::parse($m->sent_time);
			$m->time = $sent_time->diffForHumans($time_now);

			$m->recipient = empty($m->user_id)? 'me' : 'other';
			$m->sender = $m->sender;
			$m->dt_id = $m->dt_id;
			return $m;
		});


		$unread = collect($messages)->whereNull('read_at')->whereNull('user_id')->count();

		$meta = [
			'status' => $first_item['status'],
			'order_id' => $first_item['order_id'],
			'unread' => $unread,
			'dt_id'  => $first_item['dt_id'],
		];

		$data['messages'] = $messages;
		$data['tabs'] = $message_list;
		$data['meta'] = $meta;
		$data['chat_answers'] = DB::table('chat_answers')
                                ->select('*')
                                ->where('user_id', CRUDBooster::myId())
                                ->get();

        $dt_faqs = [];
        try {
            $shop = \App\Shop::where('status', 1)->where('user_id', CRUDBooster::myId())->first();
            $shop_url = rtrim($shop->url, '/').'/api/v1/faq_data';

            // $shop_url = 'https://bamboo.droptienda.rocks/api/v1/faq_data'; # todo: REMOVE
            $curl = curl_init();
            curl_setopt_array($curl, array(
            CURLOPT_URL => $shop_url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => [
                'userToken' => $shop->username,
                'userPassToken' => $shop->password,
            ],
            CURLOPT_HTTPHEADER => array(
                    'Cookie: SameSite=None; laravel_session=PI0TdOA3WaDVfAmId2eJMkT3NfT3H2CLsFhBHwf3'
                ),
            ));
            $response = curl_exec($curl);
            $raw_faqs = json_decode($response);
            curl_close($curl);
        } catch (\Throwable $th) {
            //throw $th;
        }

        if($raw_faqs->success){
            $raw_faqs = json_decode($raw_faqs->data);
            foreach ($raw_faqs as $key => $raw_faq) {
                if( !empty($raw_faq->question) && !empty($raw_faq->answer) ){
                    $dt_faqs[] = [
                        'question' => $raw_faq->question,
                        'answer' => \strip_tags_with_whitespace($raw_faq->answer),
                    ];
                }
            }
        }

		$data['dt_faqs'] = $dt_faqs;
        $data['chat_preference'] = get_option('chat_preference', 'chat', CRUDBooster::myId());

		if(empty($message_list)) {
            // CRUDBooster::redirectBack('Sorry, You have no droptienda message', 'info');
		    $data['page_title'] = 'You don\'t have any messages';
            return view('admin.new_customer.dt-chat-empty', $data);
		}else{
            return view('admin.new_customer.dt-chat', $data);
        }
	}

	public function initialize($customer_id)
	{


		$message_list = DroptiendaMessages::where('customer_id', $customer_id)
		->with('order:id,status')
		->select('read_at', 'order_id', 'user_id')
		->orderBy('id', 'asc')
		->get()
		->groupBy('order_id')
		->map(function($items) {
			$item = $items->first();
			$unread = $items->whereNull('read_at')->whereNull('user_id')->count();

			return [
				'order_id' => $item->order_id,
				'status' => drmHistoryLabel($item->order->status),
				'is_read' => !empty($item->user_id) && !empty($item->read_at),
				'unread' => $unread,
				'recipient' => empty($item->user_id)? 'me' : 'other',
			];
		})
		->toArray();

		$first_item = collect($message_list)->first();
		$messages = DroptiendaMessages::where('order_id', $first_item['order_id'])
		->select('id', 'message', 'read_at', 'order_id', 'user_id', 'created_at', 'sender')
		->orderBy('id', 'asc')
		->get()
		->map(function($m) {
			$m->time = $m->created_at->diffForHumans();
			$m->recipient = empty($m->user_id)? 'me' : 'other';
			return $m;
		});


		$unread = collect($messages)->whereNull('read_at')->whereNull('user_id')->count();

		$meta = [
			'status' => $first_item['status'],
			'order_id' => $first_item['order_id'],
			'unread' => $unread,
		];

		return response()->json([
			'success' => true,
			'data' => ['messages' => $messages, 'tabs' => $message_list, 'meta' => $meta],
		]);
	}

	public function fetch(Request $request, $order_id)
	{
		$order = \App\NewOrder::where('id', $order_id)
			->where('cms_user_id', CRUDBooster::myParentId())
			->select('drm_customer_id', 'id', 'status', 'order_id_api', 'shop_id')
			->first();

		if(empty($order)) {
			return [];
		}

		$order_id_api = $order->order_id_api;
		$shop_id = $order->shop_id;
		$id_api_str = 'drop_t' . $shop_id . '_';
		$dt_id = str_replace($id_api_str, '', $order_id_api);


		$messages = DroptiendaMessages::where('order_id', $order->id)
		->where(function($q) {
			$q->whereNull('chat_preference')->orWhere('chat_preference', 'drm');
		})
		->select('id', 'message', 'read_at', 'order_id', 'user_id', 'created_at', 'sent_time', 'sender')
		->orderBy('id', 'asc')
		->get()
		->map(function($m) {
            $date = new DateTime("now", new DateTimeZone('Europe/Berlin') );
            $time_now = Carbon::parse($date->format('Y-m-d H:i:s'));
            $sent_time = Carbon::parse($m->sent_time);
			$m->time = $sent_time->diffForHumans($time_now);
			$m->recipient = empty($m->user_id)? 'me' : 'other';
			return $m;
		});

		$unread = collect($messages)->whereNull('read_at')->whereNull('user_id')->count();

		$meta = [
			'status' => drmHistoryLabel($order->status),
			'order_id' => $order->id,
			'unread' => $unread,
			'dt_id' => $dt_id,
		];


        # send user all oprders unread message count
        $tab_info = DroptiendaMessages::whereHas('order', function($o) {
            $o->where('cms_user_id', CRUDBooster::myParentId());
        })
        ->with('order:id,status,order_id_api,shop_id')
        ->select('read_at', 'order_id', 'user_id')
        ->orderBy('id', 'asc')
        ->get()
        ->groupBy('order_id')
        ->map(function($items) {
            $item = $items->first();
            $unread = $items->whereNull('read_at')->whereNull('user_id')->count();

            $order_id_api = $item->order->order_id_api;
            $shop_id = $item->order->shop_id;
            $id_api_str = 'drop_t' . $shop_id . '_';

            $dt_id = str_replace($id_api_str, '', $order_id_api);

            return [
                'order_id' => $item->order_id,
                'status' => drmHistoryLabel($item->order->status),
                'is_read' => !empty($item->user_id) && !empty($item->read_at),
                'unread' => $unread,
                'recipient' => empty($item->user_id)? 'me' : 'other',
                'dt_id' => $dt_id,
            ];
        })
        ->toArray();


        $shop = \App\Shop::where('status', 1)->where('user_id', CRUDBooster::myId())->first();
        $shop_url = rtrim($shop->url, '/');
        @file_get_contents( $shop_url."/api/v1/drm_is_active" );

		return response()->json([
			'success' => true,
            'tab_info' => $tab_info,
			'data' => ['messages' => $messages, 'meta' => $meta],
		]);
	}

    public function chatPreference(Request $request){
        set_option('chat_preference', 'chat', $request->use_drm_chat);
    }

    public function manageQA(Request $request){
		$request->validate([
			'question' => 'required',
			'answer' => 'required',
		]);
        $ans = [
            'question' => htmlspecialchars($request->question),
            'answer' => htmlspecialchars($request->answer),
            'user_id' => CRUDBooster::myId()
        ];

        if( isset($request->chat_answers_id) ){
            $updated = ChatAnswer::where([
                'id' => $request->chat_answers_id,
                'user_id' => CRUDBooster::myId(),
            ])->update($ans);
        }else{
            $updated = ChatAnswer::create($ans);
        }
        if($updated){
            return [
                'status' => 'success',
                'message' => 'Answer preset added successfully!',
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Unable to create the preset, please try again.'
        ];
    }

    public function deleteQA(Request $request){
		$request->validate([
			'chat_answers_id' => 'required',
		]);

        $updated = ChatAnswer::where([
            'id' => $request->chat_answers_id,
            'user_id' => CRUDBooster::myId(),
        ])->delete();

        if($updated){
            return [
                'status' => 'success',
                'message' => 'Answer preset deleted successfully!',
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Unable to delete the preset, please try again.'
        ];
    }

    public function archive(Request $request){
		$request->validate([
			'order_id' => 'required',
		]);

        $updated = ChatList::where([
            'order_id' => $request->order_id,
            'user_id' => CRUDBooster::myId(),
        ])->delete();

        if($updated){
            return [
                'status' => 'success',
                'message' => 'Chat has been moved to archive!',
            ];
        }

        return [
            'status' => 'error',
            'message' => 'Unable to archive the chat'
        ];
    }

	public function unread(){
        $message_list = DroptiendaMessages::whereHas('order', function($o) {
            $o->where('cms_user_id', CRUDBooster::myParentId());
        })
        ->with('order:id,status,order_id_api,shop_id')
        ->select('read_at', 'order_id', 'user_id')
        ->orderBy('id', 'asc')
        ->get()
        ->groupBy('order_id')
        ->map(function($items) {
            $item = $items->first();
            $unread = $items->whereNull('read_at')->whereNull('user_id')->count();

            $order_id_api = $item->order->order_id_api;
            $shop_id = $item->order->shop_id;
            $id_api_str = 'drop_t' . $shop_id . '_';

            $dt_id = str_replace($id_api_str, '', $order_id_api);

            return [
                'order_id' => $item->order_id,
                'status' => drmHistoryLabel($item->order->status),
                'is_read' => !empty($item->user_id) && !empty($item->read_at),
                'unread' => $unread,
                'recipient' => empty($item->user_id)? 'me' : 'other',
                'dt_id' => $dt_id,
            ];
        })
        ->toArray();
        return $message_list;
    }


    public function send(Request $request, $order_id)
	{
		$request->validate([
			'message' => 'required|min:1|max:500',
		]);
        $order = \App\NewOrder::where('id', $order_id)
                ->where('cms_user_id', CRUDBooster::myParentId())
                ->select('id', 'status')->first();
		$customer_id = \App\NewOrder::where('id', $order_id)
			->where('cms_user_id', CRUDBooster::myId())
			->value('drm_customer_id');

		$user = DB::table('cms_users')->where('id', CRUDBooster::myId())->select('name', 'email')->first();
		$sender = [
			'name' => $user->name,
			'email' => $user->email,
		];


        $parseable = [
            '[status]' => drmHistoryLabel($order->status)
        ];
        $message = strParse($request->message, $parseable);

        $date = new DateTime("now", new DateTimeZone('Europe/Berlin') );
        $sent_time = $date->format('Y-m-d H:i:s');
		$data = DroptiendaMessages::create([
            'order_id' => $order_id,
			'customer_id' => $customer_id,
			'message' => $message,
			'user_id' => CRUDBooster::myId(),
			'sender' => $sender,
            'sent_time' => $sent_time
		]);

		$data->time = $data->created_at->diffForHumans();
		$data->recipient = empty($data->user_id)? 'me' : 'other';

        #update chat tab order
        $chat_list = DB::table('chat_list')
        ->where(['order_id' => $order_id]);



        $chat_sorting = DroptiendaMessages::get()->count();
        if( $chat_list->get()->count() ){
            $chat_list->update([
                'deleted_at' => Null,
                'chat_sorting' => $chat_sorting
            ]);
        }

		return response()->json([
			'success' => true,
			'data' => $data,
		]);
	}


	public function read($order_id)
	{
		$order = \App\NewOrder::where('id', $order_id)
			->where('cms_user_id', CRUDBooster::myParentId())
			->select('id', 'cms_user_id')->first();

		if($order && $order->id) {

			//Read messages
			DB::table('droptienda_messages')->where('order_id', $order_id)->whereNull('user_id')->whereNull('read_at')->update(['read_at' => now()]);

			//Read notifications
			\DB::table('notifications')
	        ->whereNotNull('data->room_id')
	        ->where('data->room_id', $order->id)
	        ->where('data->hook', 'DROPTIENDA_ORDER_CHAT')
	        ->whereNull('read_at')
	        ->where('notifiable_id', $order->cms_user_id)
	        ->update(['read_at' => now()]);
		}

		return response()->json([
			'success' => true,
			'order_id' => $order_id,
		]);
	}


}
