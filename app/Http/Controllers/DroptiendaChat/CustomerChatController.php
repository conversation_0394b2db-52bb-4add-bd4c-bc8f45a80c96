<?php

namespace App\Http\Controllers\DroptiendaChat;

use App\Http\Controllers\Controller;
use App\DroptiendaMessages;
use App\Events\MessageSentEvent;
use Illuminate\Http\Request;

use DB;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;

class CustomerChatController extends Controller
{

	public function fetch(Request $request, $order_id)
	{

		$token   = $request->header('userToken');
		$password = $request->header('userPassToken');
		$order = $this->getOrderInfo($token, $password, $order_id);

		$messages = DroptiendaMessages::where('order_id', $order['id'] )
		->select('id', 'message', 'read_at', 'order_id', 'user_id', 'created_at', 'sender')
		->orderBy('id', 'asc')
		->get()
		->map(function($m) {
			$m->time = $m->created_at->diffForHumans();
			$m->recipient = empty($m->user_id)? 'me' : 'other';
			return $m;
		});

		$unread = collect($messages)->whereNull('read_at')->whereNotNull('user_id')->count();

		$meta = [
			'status' => $order['status'],
			'order_id' => $order['id'],
			'unread' => $unread,
		];

		return response()->json([
			'success' => true,
			'data' => ['messages' => $messages, 'meta' => $meta],
		]);
	}

	public function send(Request $request, $order_id)
	{
		$request->validate([
			'message' => 'required|min:1|max:500',
			// 'sender_name' => 'required|min:2|max:80',
			// 'sender_email' => 'required|min:2|max:80',
		]);

		$token   = $request->header('userToken');
        $password = $request->header('userPassToken');
		$order = $this->getOrderInfo($token, $password, $order_id);

		$sender = [
			'name' => 'Sender',
			'email' => '<EMAIL>',
		];
		# TODO: Remove testing code
        $message = [
			'order_id' => 4,
			'customer_id' => 1,
			'message' => $request->message,
			'sender' => $sender,
		];
		$data = DroptiendaMessages::create($message);

		$data->time = $data->created_at->diffForHumans();
		$data->recipient = empty($data->user_id)? 'me' : 'other';


        event(new MessageSentEvent($message));
		return response()->json([
			'success' => true,
			'data' => $data,
		]);
	}

	private function getOrderInfo($token, $password, $order_id)
	{
		$shop = \App\Shop::where('user', $token)->where('password', $password)->select('id', 'user_id')->first();
		$order_id_api = 'drop_t' . $shop->id . '_' . $order_id;

		$order = \App\NewOrder::where('cms_user_id', $shop->user_id)
			->where('shop_id', $shop->id)
			->where('order_id_api', $order_id_api)
			->select('id', 'status', 'drm_customer_id')->first();

		return ['id' => $order->id, 'status' => drmHistoryLabel($order->status), 'drm_customer_id' => $order->drm_customer_id ];
	}

	public function read(Request $request, $order_id)
	{
		$token   = $request->header('userToken');
        $password = $request->header('userPassToken');
		$order = $this->getOrderInfo($token, $password, $order_id);

		$update = false;

		if($order) {
			$update = DB::table('droptienda_messages')->where('order_id', $order['id'])->whereNotNull('user_id')->whereNull('read_at')->update(['read_at' => now()]);
		}

		return response()->json([
			'success' => (bool)$update,
			'order_id' => $order_id,
		]);
	}


	public function unreadMessages(Request $request)
	{
		$token   = $request->header('userToken');
        $password = $request->header('userPassToken');

		// $request->validate([
		// 	'orders' => 'required|array|min:1'
		// ]);

		$orders_id = $request->orders;
		if(empty($orders_id)) return [];



		$shop = \App\Shop::where('user', $token)->where('password', $password)->select('id', 'user_id')->first();

        if(empty($shop)) {
			return response()->json([
				'success' => false,
				'message' => 'Shop not found!',
			]);
		}

		$shop_id = $shop->id;
		$orders_api_id = collect($orders_id)->map(function($order_id) use ($shop_id) {
			return 'drop_t' . $shop_id . '_' . $order_id;
		})
		->toArray();


		//Get orders id
		$order_list = \App\NewOrder::whereIn('order_id_api', $orders_api_id)->where('cms_user_id', $shop->user_id)->pluck('id')->toArray();

		$data = DroptiendaMessages::whereIntegerInRaw('order_id', $order_list)
		->whereNotNull('user_id')
		->whereNull('read_at')
		->select('read_at', 'order_id', 'user_id')
		->orderBy('id', 'asc')
		->get()
		->groupBy('order_id')
		->map(function($items) {

			$item = $items->first();
			$unread = $items->count();

			return [
				'id' => $item->order_id,
				'unread' => $unread,
			];
		})
		->pluck('unread', 'id')
		->toArray();

		return response()->json([
			'success' => true,
			'data' => $data,
		]);
	}

}

