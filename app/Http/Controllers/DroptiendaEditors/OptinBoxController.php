<?php

namespace App\Http\Controllers\DroptiendaEditors;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use CRUDBooster;
use DB;

class OptinBoxController extends Controller
{

    public function getTemplates(Request $request)
    {
        try {
            $password = $request->header('userPassToken');
            $token = $request->header('userToken');
            $shop = \App\Shop::where('user', $token)->where('password', $password)->select('id', 'user_id')->first();

            $path = public_path() . "/optin_templates.json";
            $default_templates = json_decode(file_get_contents($path), true);

            foreach($default_templates as $key => $default){
                $default_templates[$key]['image'] = url('/').'/images/'.$default['image'];
            }

            $custom_tempalte = DB::table('opln_mails')
                                ->join('optin_pages','optin_pages.parent_slug','=','opln_mails.slug')
                                ->where('opln_mails.user_id', $shop->user_id)
                                ->select('opln_mails.id','opln_mails.title','optin_pages.html','optin_pages.css','opln_mails.slug as from_class_name','opln_mails.thumbnail')
                                ->orderBy('opln_mails.id','desc')
                                ->get();
            return response()->json([
                'success' => true,
                'data' => [
                    'default' => $default_templates,
                    'custom' => $custom_tempalte
                ],
            ]);
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage() . ' ' . $e->getLine(),
            ];
        }
    }


}
