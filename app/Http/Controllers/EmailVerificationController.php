<?php

namespace App\Http\Controllers;

// use App\Services\Marketplace\InternelSyncService;
use CRUDBooster;
use Carbon\Carbon;
use App\Mail\DRMSEndMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Doctrine\DBAL\Driver\Exception;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\tariffController;

class EmailVerificationController extends Controller
{
    public function verifyToken()
    {
        $payload = [];
        if(isset($_GET['relavida-login']) && isset($_GET['auth-token']) && $login_token = $_GET['auth-token'])
        {
            $login_token = base64_decode($login_token);
            $token = \DRM::stringEncryption($login_token, true);
            $payload = @json_decode($token, true) ?? [];
        }

        $data = [];
        $data['token'] = $_GET['token'];
        $data['email'] = Session::get('email');
        $data['pass'] = Session::get('password');
        Session::put('user_theme', $_GET['theme'] ?? null);

        if(empty($data['email']) && $payload['email'])
        {
            $data['email'] = $payload['email'];
            Session::put('email', $data['email']);
        }

        return view('auth.email_verify')->with($data);
    }

    public function store(Request $request)
    {
        $request->validate([
            'email_verification_code' => 'integer|required',
        ]);

        if(CRUDBooster::myId()){

            // auth()->check()
            // $user = auth()->user();

            $user = CRUDBooster::me();


            if($user->two_factor_code != $_POST['email_verification_code']) $user = null;
        }else $user = DB::table(config('crudbooster.USER_TABLE'))->where("two_factor_code", $_POST['email_verification_code'])->first();


        if(empty($user)){
            return redirect()->back()->withErrors(['two_factor_code' => 'The two factor code you have entered does not match']);
        }

        $expirationTime = Carbon::parse($user->two_factor_expires_at);
        //Two factor code
        if(Carbon::now()->isBefore($expirationTime)){
            DB::table('cms_users')->where("id", $user->id)->update([
                'status' => 'Active',
                'two_factor_code' => null,
                'email_verified_at' => now(),
            ]);

            DB::table('takeappointment')->insert(
                ['user_id' => $user->id, 'payment_date_for' => 1, 'payment_date_remaining' => 1]
            );

            $messageType = 'message';
            $message = 'Account is created ..You Can Login Now!';

            Session::forget('ref_id');

            //        Sync with internel API
            // app(InternelSyncService::class)->transferVendorData($user->id);

            return redirect(drm_login_url($user))->with($messageType, $message);
        }else return redirect()->back()->withErrors(['two_factor_code' => 'The email verification code you have entered does not match']);
        ##########################NOT NEED################
        $expire_date = $user->two_factor_expires_at;
        $expire_time = strtotime($expire_date);
        $current_date = date('d-m-Y H:i:s');
        $current_time = strtotime($current_date);
        $time = $expire_time - $current_time;

        if ($user != null && $time > 0) {

            //$this->setLogin($user);

            DB::table('cms_users')->where("id", $user->id)->update([
                'status' => 'Active',
                'two_factor_code' => null,
                'email_verified_at' => now(),
            ]);

            $insert_appoinment_data = DB::table('takeappointment')->insert(
                ['user_id' => $user->id, 'payment_date_for' => 1, 'payment_date_remaining' => 1]
            );

            $tags = [
                'user_name' => $user->name,
                'user_email' => $user->email,
                'password_confirmation' => '*******'
            ];

            $messageType = 'message';
            $message = 'Account is created ..You Can Login Now!';
            if(is_relavida())
            {
                $message = 'Account ist erstellt, Sie können sich nun einloggen!';
                $messageType = 'success';
            }

            if($user->id == 2817 || ($user->user_theme && $user->user_theme === 'relavida'))
            {
                $mail_data = $this->welcomeMailData($tags, 'relavida');

                // Relavida SMTP
                if( DB::table('user_mail_configurations')->where('user_id', 2817)->where('active', 1)->exists())
                {
                    app('drm.mailer')->getMailer(2817)->to($user->email)->send(new DRMSEndMail($mail_data));
                } else {
                    app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));
                }
            }

            // Referrer Bonus Credit
            if($user->referrer_id && ($user->referrer_id == 2592)){
                $tariffController = new tariffController();
                $status = \App\Enums\CreditType::CREDIT_ADD;

                // app('App\Http\Controllers\tariffController')->CreditUpdate($user->referrer_id, 250, 'credit_add');
                // app('App\Http\Controllers\tariffController')->drmUserCreditAdd($user->referrer_id, 250, 'Supplier Registration Bonus', 5, $status);

                (new \App\Services\Tariff\Credit\RefillCredit)->refillBonusCredit($user->referrer_id, 250, \App\Services\Tariff\Credit\CreditType::SUPPLIER_REGISTRATION_BONUS);
            }


            session()->forget('ref_id');

            //        Sync with internel API
            // app(InternelSyncService::class)->transferVendorData($user->id);

            return redirect(drm_login_url($user))->with($messageType, $message);

        } else {
            return redirect()->back()->withErrors(['two_factor_code' => 'The email verification code you have entered does not match']);

        }

    }

    //Two factor mail data
    public function twoFactorMailData($tags, $slug)
    {
        $user_id = 2817;
        $page = DB::table('two_factor_email_template')
        ->where('cms_user_id', $user_id)
        ->select("mail_subject AS subject", "email_template AS content", "sender_email")
        ->first();

        $mail_data = DRMParseMailTemplate($tags, '', 'en', $page);
        $from_email = $page->sender_email;
        return $mail_data;
    }

    //Welcome mail data
    public function welcomeMailData($tags, $slug)
    {
        $user_id = 2817;
        $page = DB::table('welcome_email_template')
        ->where('cms_user_id', $user_id)
        ->select("mail_subject AS subject", "email_template AS content", "sender_email")
        ->first();

        $mail_data = DRMParseMailTemplate($tags, '', 'en', $page);
        $from_email = $page->sender_email;

        // if(!empty($from_email))
        // {
        //     $mail_data['from'] = $from_email;
        // }

        return $mail_data;
    }

    public function customerEmailVerification($slug){

        $data['customer_info'] = $customer_info = DB::table('new_customers')
            ->where('slug', $slug)
            ->select('id','full_name','email_verification','status','slug','verified_date')
            ->first();

        if(empty($data['customer_info'])){
            $data['title'] = 'Invalid';
            $data['message'] = 'Invalid link, Please check again your verification link.';
            return view('admin.drm_customer.customer_email_verified',$data);
        }

        if ($customer_info->email_verification == 0) {
            DB::table('new_customers')->where([
                'id' => $customer_info->id,
                'slug' => $customer_info->slug
            ])
            ->update([
                'email_verification' => 1,
                'verified_date' => now(),
                'status' => 1
            ]);
            $data['title'] = 'Thank You';
            $data['message'] = 'Your email has been successfully verified !';

            // Send campaign email
            try {
                $this->pedingCustomerCampaign($customer_info->id);
            } catch(\Exception $e){}

        } elseif ($customer_info->email_verification == 1) {
            $data['title'] = 'Verified';
            $data['message'] = 'Your account already verified !';
        }

        return view('admin.drm_customer.customer_email_verified',$data);
    }


    private function pedingCustomerCampaign($customerId)
    {
        $campaigns = DB::table('campaign_tags')->join('dropfunnel_customer_tags', function($join) use ($customerId) {
            $join->on('dropfunnel_customer_tags.tag_id', '=', 'campaign_tags.tag_id')
            ->where('dropfunnel_customer_tags.customer_id', $customerId)
            ->whereNull('dropfunnel_customer_tags.deleted_at');
        })
        ->select('campaign_tags.campaign_id as campaign_id')
        ->groupBy('campaign_tags.campaign_id')
        ->pluck('campaign_id', 'campaign_id')
        ->toArray() ?? [];

        if(empty($campaigns)) return;

        foreach($campaigns as $campaignId)
        {
            try {
                app('App\Http\Controllers\NewShopSyncController')->campaignMailJob($campaignId);
            } catch (\Exception $e) {}
        }
    }


    public function resendCode()
    {
        // Fetch user
        $email = $_REQUEST['email'];

        if(CRUDBooster::myId()){
            // auth()->check()
            // $user = auth()->user();
            $user = CRUDBooster::me();
        }else{
            $user = DB::table('cms_users')->where("email", $email)->select('id', 'name', 'email','two_factor_code','two_factor_expires_at')->orderBy('id', 'desc')->first();
        }

        if(empty($user))
        {
            return CRUDBooster::redirectBack("Sorry, Something went wrong!", "warning");
        }

        $expirationTime = Carbon::parse($user->two_factor_expires_at);
        //Two factor code
        if(Carbon::now()->isBefore($expirationTime)){
            $two_factor_code = $user->two_factor_code;
        }else {
            $two_factor_code = rand(100000, 999999);
        }

        //Save two factor code
        DB::table('cms_users')->where("id", $user->id)->update([
            'two_factor_code' => $two_factor_code,
            'two_factor_expires_at' => now()->addMinutes(10)
        ]);

        //Data send to mail
        $tags = [
            'user_name' => $user->name,
            'user_email' => $user->email,
            'two_factor_code' => $two_factor_code,
        ];

        $slug = 'two_factor_varification';
        $lang = getUserSavedLang($user->email);
        $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
        if(isLocal()){
            \Mail::to($user->email)->send(new DRMSEndMail($mail_data));
        }else app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));

        //Set email om session
        Session::put('email',  $user->email);

        return back()->with('Success, Verification email sent.');//->action('EmailVerificationController@verifyToken');
    }
}
