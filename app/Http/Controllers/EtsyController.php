<?php

namespace App\Http\Controllers;

use App\Enums\Channel;
use App\Helper\AppStore;
use App\Models\EtsyProduct;
use App\Services\Modules\Export\Etsy;
use App\TaxRate;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Services\ChannelProductService;

class EtsyController extends Controller
{
    public function exportDrmProduct($productIds, $userId, $shippingTemplateId = null)
    {
        $taxRate = data_get(TaxRate::where('country', 'Germany')->first(), 'charge', 16);
        foreach ($productIds as $productId) {
            $this->updateOrCreateEtsyProduct($productId, $userId, $taxRate, $shippingTemplateId);
        }
    }

    public function deleteListing($productIds, $userId)
    {
        $shopDetail = DB::table('shops')
            ->where('user_id', $userId)
            ->where('channel', Channel::ETSY)
            ->get()
            ->first();

        $credentials = \App\Models\EtsyCredential::select([
            'consumer_key',
            'consumer_secret',
            'access_token',
            'access_token_secret',
            'temporary_credentials',
        ])->where('shop_id', $shopDetail->id)
            ->first()
            ->toArray();
        foreach ($productIds as $listingId) {
            $data = [
                'params' => ['listing_id' => $listingId]
            ];

            $response = app(\App\Services\Modules\Export\Etsy\EtsyService::class)->deleteListing($data, $credentials);
            DB::table('etsy_products')->where(['item_id' => $listingId, 'user_id' => $userId])->delete();
        }
    }

    private function uploadListingImage($listingId, $imageUrl, $credentials)
    {
        $data = [
            'params' => [
                'listing_id' => $listingId,
            ],
            'data' => [
                'image' => $imageUrl
            ]
        ];

        return app(\App\Services\Modules\Export\Etsy\EtsyService::class)->uploadListingImage($data, $credentials);
    }

    private function deleteListingImage($listingId, $listingImageId, $credentials)
    {
        $data = [
            'params' => [
                'listing_id' => $listingId,
                'listing_image_id' => $listingImageId,
            ],
        ];

        return app(\App\Services\Modules\Export\Etsy\EtsyService::class)->deleteListingImage($data, $credentials);
    }

    public function findUserShops($shopId)
    {
        $credentials = \App\Models\EtsyCredential::select([
            'consumer_key',
            'consumer_secret',
            'access_token',
            'access_token_secret',
            'temporary_credentials',
        ])->where('shop_id', $shopId)
            ->first()
            ->toArray();

        return app(\App\Services\Modules\Export\Etsy\EtsyService::class)->findUserShops($credentials);
    }

    public function syncListingImages($productId, $shopId, $credentials)
    {
        try {
            $etsyProduct = EtsyProduct::query()->where(['drm_product_id' => $productId, 'shop_id' => $shopId])->first();
            $drmProduct = DB::table('drm_products')->where(['id' => $productId])->first();
            $etsyImages = json_decode(data_get($etsyProduct, 'images', json_encode([])), true);
            $drmImages = collect(json_decode(data_get($drmProduct, 'image', json_encode([])), true) ?? [])->pluck('src');

            // Sync new images
            foreach (($drmImages->diff($etsyImages))->reverse() as $imageSource) {
                $response = $this->uploadListingImage($etsyProduct->item_id, $imageSource, $credentials);
                $etsyImages[$response['results'][0]['listing_image_id']] = $imageSource;
            }

            // sync deleted images
            foreach (collect($etsyImages)->diff($drmImages) as $imageId => $imageSource) {
                $response = $this->deleteListingImage($etsyProduct->item_id, $imageId, $credentials);
                unset($etsyImages[$imageId]);
            }
            $etsyProduct->update(['images' => json_encode($etsyImages)]);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }

    private function updateOrCreateEtsyProduct($productId, $userId, $taxRate, $shippingTemplateId = null)
    {
        $shopDetail = \App\Shop::where('user_id', $userId)->where('channel', Channel::ETSY)->get()->first();
        $etsyProduct = DB::table('etsy_products')->where(['drm_product_id' => $productId, 'shop_id' => $shopDetail->id])->first();
        $postData = $this->getProductDetails($userId, $productId, $taxRate);
        if (!empty($shippingTemplateId)) {
            $postData['shipping_template_id'] = (int) $shippingTemplateId;
        }

        $credentials = \App\Models\EtsyCredential::select([
            'consumer_key',
            'consumer_secret',
            'access_token',
            'access_token_secret',
            'temporary_credentials',
        ])->where('shop_id', $shopDetail->id)
            ->first()
            ->toArray();

        if ($etsyProduct && $postData) {
            // update product
            try {
                $response = app(\App\Services\Modules\Export\Etsy\EtsyService::class)->updateProduct([
                    'params' => ['language' => 'de', 'listing_id' => $etsyProduct->item_id],
                    'data' => $postData
                ], $credentials);

                DB::table('etsy_products')->updateOrInsert(
                    ['drm_product_id' => $productId, 'shop_id' => $shopDetail->id],
                    [
                        'item_id' => $response['results'][0]['listing_id'],
                        'user_id' => $userId
                    ]
                );

                $this->syncListingImages($productId, $shopDetail->id, $credentials);
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        } elseif ($postData) {
            // create new product
            try {
                $response = app(\App\Services\Modules\Export\Etsy\EtsyService::class)->createProduct([
                    'params' => ['language' => 'de',],
                    'data' => $postData
                ], $credentials);

                DB::table('etsy_products')->updateOrInsert(
                    ['drm_product_id' => $productId, 'shop_id' => $shopDetail->id],
                    [
                        'item_id' => $response['results'][0]['listing_id'],
                        'user_id' => $userId
                    ]
                );

                $this->syncListingImages($productId, $shopDetail->id, $credentials);
            } catch (\Exception $e) {
                Log::error($e->getMessage());
            }
        }
    }

    public function getProductDetails($userId, $productId, $taxRate)
    {
        $lang = 'de';
        $table = "drm_translation_" . $lang;
        $shopDetail = DB::table('shops')
            ->where('user_id', $userId)
            ->where('channel', Channel::ETSY)
            ->get()
            ->first();

        $product = DB::table($table)
            ->join('drm_products', 'drm_products.id', '=', $table . '.product_id')
            ->where('drm_products.user_id', $userId)
            ->whereNull('drm_products.deleted_at')
            ->select('drm_products.*', 'drm_products.description as descriptionProduct', 'drm_products.name as product_category_nameroduct', 'drm_products.id as product_id', $table . '.description', $table . '.title as name')
            ->where('drm_products.id', $productId)
            ->first();

        $category_etsy = DB::table('drm_product_categories')
            ->join('drm_category', 'drm_category.id', '=', 'drm_product_categories.category_id')
            ->join('drm_category_mapping', 'drm_category_mapping.drm_category_id', '=', 'drm_product_categories.category_id')
            ->select('drm_category_mapping.mapping_id')
            ->where(['drm_product_categories.product_id' => $productId, 'shop_id' => $shopDetail->id])
            ->first();

        $category_id = $category_etsy->mapping_id;
        $product_price = DB::table('drm_product_shop_prices')->where(['drm_product_id' => $productId, 'shop_id' => $shopDetail->id])->first();

        $variables_tag = ["#number", "#ean", "#ek_price", "#vk_price", "#weight", "#size", "#color", "#note", "#production_year", "#brand", "#materials", "#tags", "#status", "#gender"];

        foreach ($variables_tag as $key => $variables_tag_value) {
            if ($variables_tag_value == "#number") {
                $variables_tag_info[$key] = $product->item_number;
            }
            if ($variables_tag_value == "#ean") {
                $variables_tag_info[$key] = $product->ean;
            }
            if ($variables_tag_value == "#ek_price") {
                $variables_tag_info[$key] = $product->ek_price;
            }
            if ($variables_tag_value == "#vk_price") {
                $variables_tag_info[$key] = $product->vk_price;
            }
            if ($variables_tag_value == "#weight") {
                $variables_tag_info[$key] = $product->item_weight;
            }
            if ($variables_tag_value == "#size") {
                $variables_tag_info[$key] = $product->item_size;
            }
            if ($variables_tag_value == "#color") {
                $variables_tag_info[$key] = $product->item_color;
            }
            if ($variables_tag_value == "#note") {
                $variables_tag_info[$key] = $product->note;
            }
            if ($variables_tag_value == "#production_year") {
                $variables_tag_info[$key] = $product->production_year;
            }
            if ($variables_tag_value == "#brand") {
                $variables_tag_info[$key] = $product->brand;
            }
            if ($variables_tag_value == "#materials") {
                $variables_tag_info[$key] = $product->materials;
            }
            if ($variables_tag_value == "#tags") {
                $variables_tag_info[$key] = $product->tags;
            }
            if ($variables_tag_value == "#status") {
                $variables_tag_info[$key] = $product->status;
            }
            if ($variables_tag_value == "#gender") {
                $variables_tag_info[$key] = $product->gender;
            }
        }

        $images = json_decode($product->image);
        if (is_array($images)) {
            $explode_images = (isset($images[0])) ? $explode_images = getImageArray($images) : "";
        }

        $product->title = trim(str_replace($variables_tag, $variables_tag_info, $product->name));
        $product->description = trim(str_replace($variables_tag, $variables_tag_info, $product->description));
        $product->price = $product_price->vk_price ?? $product->vk_price;

        $product->tags = array_values(array_filter(explode(',', str_replace('#', '', $product->tags))));
        $product->images = $explode_images;

        $productData = [
            'quantity' => (int) $product->stock,
            'title' => $product->name,
            'description' => strip_tags($product->description),
            'price' => $this->calculatePrice($product->price, $taxRate),
            'state' => 'active',
            'taxonomy_id' => (int) $category_id,
            'who_made' => 'someone_else',
            'is_supply' => true,
            'when_made' => 'made_to_order',
        ];


//        foreach($product->tags as $tag) {
//            $tag = str_replace(' ', '_', trim($tag));
//            if (strlen($tag) <= 30 && preg_match("/[^\p{L}\p{Nd}\p{Zs}\-'™©®]/u", $tag) == 0) {
//                $productData['tags'][] = $tag;
//            }
//        }

        if (isset($product) && !empty($category_id)) {
            return $productData;
        }

        return null;
    }

    private function calculatePrice($price, $rate)
    {
        return (float) round($price + ($price * ($rate / 100)), 2);
    }

    public function getInventory($userId, $listingId)
    {
        $shopDetail = DB::table('shops')
            ->where('user_id', $userId)
            ->where('channel', Channel::ETSY)
            ->get()
            ->first();

        $credentials = \App\Models\EtsyCredential::select([
            'consumer_key',
            'consumer_secret',
            'access_token',
            'access_token_secret',
            'temporary_credentials',
        ])->where('shop_id', $shopDetail->id)
            ->first()
            ->toArray();

        $response = app(Etsy\EtsyService::class)
            ->getInventory(['params' => ['listing_id' => $listingId]], $credentials);

        return $response;
    }

    public function getShippingTemplates($userId)
    {
        $shopDetail = DB::table('shops')
            ->where('user_id', $userId)
            ->where('channel', Channel::ETSY)
            ->get()
            ->first();

        $credentials = \App\Models\EtsyCredential::select([
            'consumer_key',
            'consumer_secret',
            'access_token',
            'access_token_secret',
            'temporary_credentials',
        ])->where('shop_id', $shopDetail->id)
            ->first()
            ->toArray();

        return app(\App\Services\Modules\Export\Etsy\EtsyService::class)->shippingTemplates($credentials);
    }

    public function etsyExportPreview(Request $request)
    {
        $products = $request->product_ids;
        $products = explode(',', $products);

        $data['page_title'] = "Etsy Export Setup";
        $data['product_ids'] = $products;
        $user_id = CRUDBooster::myId();

        $lang = app('App\Services\UserService')->getLang($user_id);
    
        $data['lang'] = $lang;
        $data['products'] = app(ChannelProductService::class)->getUserProducts($products,$user_id);

        if (count($data['products'])) {
            try {
                $data['shipping_templates'] = $this->getShippingTemplates(CRUDBooster::myId())['results'];

                return view('admin.drm_export.etsy_export_preview_new', $data);
            } catch (\Exception $e) {
                CRUDBooster::redirect(route('channel.product.index',['channel' => Channel::ETSY]), "Invalid Etsy shop credentials. Please update your Etsy shop", "warning");
            }
        } else {
            CRUDBooster::redirect(route('channel.product.index',['channel' => Channel::ETSY]), "No products found !", "warning");
        }
    }
}
