<?php

namespace App\Http\Controllers\Export;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class OttoExportController extends Controller
{
    public function export($userId, $productIds)
    {
        print $userId;
        $productDetails = [];
        foreach ($productIds as $productId)
        {
            $product = $this->getSingleProduct($productId);
            $productDetail = [
                "productName" => "UBN-11779",
                "sku" => $product->item_number,
                "ean" => $product->ean,
                "gtin" => "00012345600012",
                "isbn" => "978-3-16-148410-0",
                "upc" => "042100005264",
                "pzn" => "PZN-4908802",
                "mpn" => "H2G2-42",
                "moin" => "93992000200",
                "offeringStartDate" => "2019-10-19T10=>00=>15.000+02=>00",
                "releaseDate" => "2019-10-19T10=>00=>15.000+02=>00",
                "maxOrderQuantity" => 5,
                "productDescription" => [
                    "category" => "Outdoorjacke",
                    "brand" => $product->brand,
                    "productLine" => "501",
                    "manufacturer" => "3M",
                    "productionDate" => "2019-10-19T10=>00=>15.000+02=>00",
                    "multiPack" => true,
                    "bundle" => false,
                    "fscCertified" => true,
                    "disposal" => false,
                    "productUrl" => "http=>//myproduct.somewhere.com/productname/",
                    "description" => $product->description,
                    "bulletPoints" => [
                        "My top key information..."
                    ],
                    "attributes" => [
                        [
                            "name" => "Bundweite",
                            "values" => [
                                "34"
                            ],
                            "additional" => true
                        ]
                    ]
                ],
                "mediaAssets" => $this->imagesFormat(json_decode($product->image)),
                "delivery" => [
                    "type" => "PARCEL",
                    "deliveryTime" => $product->delivery_days
                ],
                "pricing" => [
                    "standardPrice" => [
                        "amount" => $product->vk_price,
                        "currency" => "EUR"
                    ],
                    "vat" => "FULL",
                    "msrp" => [
                        "amount" => $product->vk_price,
                        "currency" => "EUR"
                    ],
                    "sale" => [
                        "salePrice" => [
                            "amount" => $product->vk_price,
                            "currency" => "EUR"
                        ],
                        "startDate" => Carbon::now()->toDateTimeString(),
                        "endDate" => Carbon::now()->toDateTimeString()
                    ],
                    "normPriceInfo" => [
                        "normAmount" => 100,
                        "normUnit" => "g",
                        "salesAmount" => 500,
                        "salesUnit" => "g"
                    ]
                ],
                "logistics" => [
                    "packingUnitCount" => 3,
                    "packingUnits" => [
                        [
                            "weight" => $product->item_weight,
                            "width" => 600,
                            "height" => 200,
                            "length" => 300
                        ]
                    ]
                ]
            ];
            array_push($productDetails, $productDetail);
        }
        dd($productDetails);
    }

    public function getSingleProduct($productId)
    {
        return DB::table('drm_products')->where('id', $productId)->get()->first();
    }

    public function imagesFormat($images)
    {
        $formatted = [];
        foreach ($images as $image)
        {
            $formattedImage = $this->imageFormat($image);
            array_push($formatted, $formattedImage);
        }
        return $formatted;
    }

    public function imageFormat($image)
    {
        $formatted = [];
        $formatted['type'] = "IMAGE";
        $formatted['location'] = $image->src;
        return $formatted;
    }
}
