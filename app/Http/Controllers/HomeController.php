<?php

namespace App\Http\Controllers;

use App\Http\Controllers\AdminDrmImportsController;
use App\Services\Modules\Export\Etsy;
use App\Services\Modules\Export\Otto;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Request;
use App\Jobs\ProcessTranslation;
use App\Jobs\ImportToGambio;

use Illuminate\Support\Facades\DB;
use PDF;
use CRUDBooster;
use Illuminate\Support\Facades\Storage;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\Str;
use XMLReader;
use SimpleXMLElement;
use League\Csv\Writer;
use League\Csv\Reader;
use League\Csv\Statement;
use App\DrmImport;
use App\Events\ManualUpdateEvent;
use Illuminate\Support\LazyCollection;
use App\DrmProduct;
use App\custom_paywall_charge;
use Illuminate\Support\Facades\Redis;
use App\Services\Modules\Export\Ebay\Api as EbayApi;
use Illuminate\Support\Arr;
use GuzzleHttp\Client;
use App\Services\Modules\Export\Amazon;
use App\Services\Modules\Export\Ebay;
use App\Services\Modules\Export\Shopify;
use App\Services\Modules\Export\ExportServices;
use App\Helper\GambioApi;
use App\Services\Notification\Notification;
use App\Models\Marketplace\Product;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        //$this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        return view('home');
    }

    public function redis_test(Request $request)
    {
        try
        {
            $redis = Redis::connect('127.0.0.1', 3306);
            return response('redis working');
        } catch (\Predis\Connection\ConnectionException $e)
        {
            return response('error connection redis');
        }
    }

    public function fiximage()
    {
    }

    public function testjob()
    {

        // $products = Product::where('supplier_id', 2626)->get();

        // $products->each(function ($item) {
        //     $price = 0;
        //     $price = $item->ek_price + ($item->ek_price * 0.05);
        //     $price = (float)str_replace(',', '', number_format($price, 2));
        //     $item->update(['vk_price' => $price]);
        // });

        // dd("done");

        dd("stop");

        //  $string = "Ringgröße";
        //  dd($string);
        // $imports = DB::table('drm_imports')
        //     ->where('id', '>', 95)
        //     ->where('id', '<>', 97)
        //     ->where('id', '<>', 105)
        //     ->where('id', '<', 107)
        //     ->get();

        // //foreach($imports as $value){
        // $csv = $this->csvToArray();
        // $products = DB::table('drm_products')->where('drm_import_id', 95)->get();

        // $data = [];
        // foreach ($csv as $key => $values)
        // {
        //     $data[$values['SKU']] = $values['Beschreibung'];
        // }

        // foreach ($products as $value)
        // {
        //     $insert['description'] = $data[$value->item_number];
        //     DB::table('drm_products')
        //         ->where('id', $value->id)
        //         ->update($insert);
        // }

        //}


        // foreach($products as $value){
        //     $values['description'] = $data[$value->ean];
        //     $values['update_enabled'] = '0';

        //     DB::table('drm_products')
        //     ->where('id',$value->id)
        //     ->update($values);
        // }


        // for($i = 95; $i<107; $i++){
        //     DB::table('drm_product_categories')->where('drm_import_id',$i)->delete();
        //     $category = DB::table('drm_products')->where('drm_import_id',$i)->get();

        //     $categories = [];
        //       foreach ($category as $key => $value) {
        //         if(!in_array($value->category, $categories)){
        //           $categories[] = $value->category;
        //         }
        //       }

        //       foreach ($categories as $key => $value) {
        //         DB::table('drm_product_categories')->insert(['drm_import_id' => $i, 'category' => $value]);
        //       }
        // }
    }


    public function test()
    {
        $products = DB::table('drm_products')
            ->select('drm_products.id', 'drm_products.name', 'drm_products.description', 'drm_translation.title', 'drm_translation.description AS transalated_desc')
            ->join('drm_translation', 'drm_products.ean', '=', 'drm_translation.ean')
            ->paginate(500);

        return view('testtable', compact('products'));
    }

    public function priceFix()
    {
        $products = DB::table('drm_products')->where('user_id', 52)->whereNull('ek_price')->get();


        foreach ($products as $key => $values)
        {
            $ek_price = (float)$values->vk_price / 1.1;

            $price = number_format($ek_price, 2);

            DB::table('drm_products')->where('id', $values->id)->update(['ek_price' => $price]);
        }
    }

    public function translatetest()
    {
        ProcessTranslation::dispatch();
    }


    public function xmlfix()
    {

        $context = stream_context_create(array('http' => array('header' => 'Accept: application/xml')));
        $url = 'http://bagyourmoments.com/CSV_list/DIFOX-22390344-TECDAT.XML';
        $xml = file_get_contents($url, false, $context);
        $xml = simplexml_load_string($xml);
        dd($xml);
    }


    public function testCsv()
    {
        $vat = "1";

        dd(is_numeric($vat));
    }

    public function xmlToJsonHeader($xml)
    {
        $array = $this->simpleXmlToArray($xml);

        foreach ($array as $key => $value)
        {
            $headers_array[] = $key;
        }

        $Headers = json_encode($headers_array);

        return $Headers;
    }

    function simpleXmlToArray($xmlObject)
    {
        $array = [];
        foreach ($xmlObject->children() as $node)
        {
            $array[$node->getName()] = is_array($node) ? simplexml_to_array($node) : (string)$node;
        }
        return $array;
    }

    public function saveCategories()
    {
        $category = DB::table('drm_products')->where('drm_import_id', 36)->get();

        $categories = [];
        foreach ($category as $key => $value)
        {
            if (!in_array($value->category, $categories))
            {
                $categories[] = $value->category;
            }
        }

        foreach ($categories as $key => $value)
        {
            DB::table('drm_product_categories')->insert(['drm_import_id' => 36, 'category' => $value]);
        }
    }


    public function categoryFix()
    {
        $products = DB::table('drm_category')->get();
        dd(count($products));
        $count = 0;
        foreach ($products->chunk(50) as $chunk)
        {
            foreach ($chunk as $value)
            {
                $count++;
                // $import = DB::table('drm_imports')->where('id',$value->drm_import_id)->first();
                // dd($import);
                // $language = DB::table('countries')->where('id',$import->country_id)->first();
                // $table = 'drm_translation_'.$language->language_shortcode;

                // $data['title'] = $value->title;
                // $data['description'] = $value->description;
                // $data['ean'] = $value->ean;
                // $data['product_id'] = $value->drm_products_id;

                DB::table('drm_products')->update(['country_id' => 1]);
                echo $count . " Products Updated";
            }
        }
    }


    public function fixLanguage()
    {
        $products = DB::table('drm_translation_en')
            ->join('drm_products', 'drm_products.id', 'drm_translation_en.product_id')
            ->select('drm_translation_en.id')
            ->where('drm_import_id', 240)->get();


        foreach ($products->chunk(200) as $chunk)
        {
            foreach ($chunk as $value)
            {
                $count++;

                //DB::table('drm_translation_en')->where('id',$value->id)->delete();
                echo $count . " Items Deleted <br>";
            }
        }
    }


    public function csvTest()
    {
        $key = null;
        $key_count = 0;
        $array = array();
        $path = 'test.xlsx';
        $type = "xlsx";
        if ($type == 'csv' || $type == 'txt')
        {
            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
            $reader->setInputEncoding('UTF-8');
            $reader->setDelimiter($delimiter);
            $spreadsheet = $reader->load($path);
        } else
        {
            $spreadsheet = IOFactory::load($path);
        }
        $spreadsheet = $spreadsheet->getActiveSheet()->toArray();

        $total = count($spreadsheet);
        for ($i = 0; $i <= 5; $i++)
        {
            if ($total >= $i + 1)
            {
                $collection[$i] = $spreadsheet[$i];
            }
        }
        dd($collection);
        $valid = $this->validateFile($collection);
        dd($valid);
        return $array;
    }


    public function validateFile($collection)
    {
        $valid = true;
        $count = 0;
        foreach ($collection as $key => $value)
        {
            if ($key == 0)
            {
                if (containsOnlyNull($value))
                {
                    $valid = false;
                }

                if (hasBigString($value))
                {
                    $valid = false;
                }
                if (count($value) < 2)
                {
                    $valid = false;
                }
                $valid = checkArrayKey($value, $collection);
                // if(arrayNullCount($value)>4){
                // 	$valid = false;
                // }
            } else
            {
                // if(containsOnlyNull($value)){
                // 	$count++;
                // }
                $utf_8 = makeArrayUtf8(makeArrayUtf8($value));
                dd($utf_8);
                if (json_encode($utf_8) == false)
                {
                    $valid = false;
                }
            }
        }
        // if(count($collection)==5 && $count>2){
        // 	$valid = false;
        // }
        return $valid;
    }


    public function csvToArray()
    {

    }


    public function enableUpdate()
    {
        $count = 0;
        $all_categories = DB::table('drm_category')->cursor();
        echo '
        <!DOCTYPE html>
            <html lang="en">
            <head><meta http-equiv="Content-Type" content="text/html; charset=utf-8">
              <title>Manual Update</title>

              <meta name="viewport" content="width=device-width, initial-scale=1">
              <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
              <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
              <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
            </head>
            <body>

            <div class="container">
            <div style="margin-top:20%">
                <h2>Updating</h2>
            </div>

              <div class="progress">
                <div class="progress-bar progress-bar-warning progress-bar-striped" aria-valuenow="0" role="progressbar" aria-valuemin="0" width="0%">

                </div>
              </div>
                <div style="margin-top:20px;color:green">
                    <center id="count"></center>
                </div>

            </div>

            </body>
            </html>
        ';


        // DB::table('drm_import_categories')->delete();
        $profits = DB::table('drm_product_profit_margins')->cursor();
        // foreach ($chunks as $all_categories){
        $total = $profits->count();
        $all = [];
        foreach ($profits as $profit)
        {
            // $count++;
            // if($cat->country_id==1){
            //   $trans_cat= "category_name_de";
            // }
            // if($cat->country_id==2){
            //   $trans_cat= "category_name_en";
            // }
            // if($trans_cat!=null){
            //   $original = (array)$cat;
            //   if($original[$trans_cat] == null){
            //     $update[$trans_cat] = $cat->category_name;
            //     DB::table('drm_category')->where('id',$cat->id)->update($update);
            //   }
            // }

            // $import_id = $cat->drm_import_id;
            // if($import_id==null){
            //   $profit = DB::table('drm_product_profit_margins')->where('category_id',$cat->id)->first();
            //   $import_id = $profit->drm_import_id;
            //   $update['drm_import_id']=$import_id;
            //   if($update['drm_import_id']!=null){
            //     DB::table('drm_category')->where('id',$cat->id)->update($update);
            //   }
            //
            // }

            $import_id = $profit->drm_import_id;
            $cat_id = $profit->category_id;

            $import_category = DB::table('drm_import_categories')->where('drm_import_id', $import_id)->where('category_id', $cat_id)->count();
            if (!$import_category)
            {
                $all[] = [
                    'category_id' => $cat_id,
                    'drm_import_id' => $import_id
                ];
            }


            $new_width = ($count / $total) * 100;
            echo "<script>
                    $('#count').html('<b>Processing " . $count . " / " . $total . " Items (" . number_format($new_width, 2) . " %)</b>');
                    $('.progress-bar').css('width','" . $new_width . "%');
                </script>";

        }
        DB::table('drm_import_categories')->insert($all);
        // }


        // $total = 6002;
        // foreach($this->csvToArray() as $a){
        //   $count++;
        //   $new_width = ($count / $total) * 100;
        //   echo "<script>
        //           $('#count').html('<b>Processing ".$count." / ".$total." Items (".number_format($new_width,2)." %)</b>');
        //           $('.progress-bar').css('width','".$new_width."%');
        //       </script>";
        //
        //       dump($a);
        // }


        // $endtime = microtime(true);
        // $timediff = $endtime - $starttime;
        //
        // echo $timediff."<br><br>";
        //
        // print memory_get_peak_usage()/1024;

    }


    public function getImageJson($array, $prefix)
    {

        echo "<script>
          $('#count').html('<b>Processing " . $count . " / " . $total . " Items (" . number_format($new_width, 2) . " %)</b> (Dupplicate : " . $dupplicate . ")');
          $('.progress-bar').css('width','" . $new_width . "%');
      </script>";

        $final_img = array();
        foreach ($array as $key => $value)
        {
            $img = str_replace(' ', '', $value);
            if ($img != null)
            {
                if (filter_var($img, FILTER_VALIDATE_URL) === FALSE)
                {

                    $value = $this->importImagePrefix($prefix, $img['src']);
                } else
                {
                    $value = $img['src'];
                }


                $img_data['id'] = $key + 1;
                if ($key > 10)
                {
                    $img_data['status'] = 0;
                } else
                {
                    $img_data['status'] = 1;
                }
                $img_data['src'] = $value;
                $final_img[] = $img_data;
            }
        }
        $json_image = json_encode($final_img);
        return $json_image;
    }


    public function importImagePrefix($prefix, $image)
    {
        $image = substr($image, 2);
        if (substr($prefix, -1) == '/')
        {
            $image = $prefix . $image;
        } else
        {
            $image = $prefix . '/' . $image;
        }
        return $image;
    }

    public function importImageSuffix($suffix, $image)
    {
        $image = $image . $suffix;
        return $image;
    }

    public function page($name)
    {

        $page = DB::table('drm_pages')->where('page_name', $name)->first();
        //dd($page);
        return view('front.page', ['page' => $page]);
    }


    public function xmlDesc()
    {
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);

        $countIx = 0;
        $pIx = 0;
        $descriptions = [];
        $xml = new XMLReader();
        $xml->open('DIFOX-22390344-TECDAT.XML');

        $match = 'articlelist';
        while ($xml->read() && $xml->name != $match)
        {
            //dd($xml->name);
        }

        while ($xml->name == $match)
        {
            $element = new SimpleXMLElement($xml->readOuterXML());

            $allarticle = $element->article;

            dd(count($allarticle));
            foreach ($allarticle as $key => $value)
            {
                dd($value);
                $prod = array(
                    'name' => strval($value->name),
                    'ArticleNr' => strval($value->attributes()->ArticleNr),
                    'ArtikelGruppenNr' => strval($value->attributes()->ArtikelGruppenNr),
                    'ArtikelGruppenName' => strval($value->attributes()->ArtikelGruppenName),
                    'SetArtikel' => strval($value->attributes()->SetArtikel),
                    'brand' => strval($value->brand),
                    'description' => $value->tecdat,
                );

                $object = json_decode(json_encode($prod), true);
                $description = $object['description'];

                // if(isset($object['description']['group'])){
                //     $name = $object['description']['group']['@attributes']['name'];
                //     if(isset($object['description']['group']['proberty'])){
                //         $proberty = $object['description']['group']['proberty'];

                //         $desc = $name;
                //         if(is_array($proberty)){
                //             foreach($proberty as $key => $value){
                //                 $desc.=' '.$value;
                //             }
                //         }
                //         $descriptions[$object['ArticleNr']] = $desc;
                //     }
                // }

                $descriptions[$object['ArticleNr']] = json_encode($description);

                $pIx++;
            }
            $countIx++;

            $xml->next($match);
            unset($element);
        }

        //  $i = 0;
        // foreach($descriptions as $key => $value){
        //     $values["description"] = $value;
        //     DB::table('drm_products')->where('item_number',(string)$key)->where('drm_import_id',43)->update($values);
        // }

        // $data = DB::table('drm_products')->where('drm_import_id',43)->where('description',null)->get();

        // foreach($data as $key => $value){
        //     if(isset($descriptions[$value->item_number])){
        //       $values["description"] = $descriptions[$value->item_number];
        //         DB::table('drm_products')->where('item_number',(string)$value->item_number)->where('drm_import_id',43)->update($values);
        //     }

        // }

        print "Number of items=$countIx\n";
        print "Number of products=$pIx\n";

        print "memory_get_usage() =" . memory_get_usage() / 1024 . "kb\n";
        print "memory_get_usage(true) =" . memory_get_usage(true) / 1024 . "kb\n";
        print "memory_get_peak_usage() =" . memory_get_peak_usage() / 1024 . "kb\n";
        print "memory_get_peak_usage(true) =" . memory_get_peak_usage(true) / 1024 . "kb\n";

        print "custom memory_get_process_usage() =" . $this->memory_get_process_usage() . "kb\n";


        $xml->close();

        /**
         * Returns memory usage from /proc<PID>/status in bytes.
         *
         * @return int|bool sum of VmRSS and VmSwap in bytes. On error returns false.
         */
    }

    public function memory_get_process_usage()
    {
        $status = file_get_contents('/proc/' . getmypid() . '/status');

        $matchArr = array();
        preg_match_all('~^(VmRSS|VmSwap):\s*([0-9]+).*$~im', $status, $matchArr);

        if (!isset($matchArr[2][0]) || !isset($matchArr[2][1]))
        {
            return false;
        }

        return intval($matchArr[2][0]) + intval($matchArr[2][1]);
    }

    public function getDesc()
    {
    }

    public function umlautsFix()
    {
        $data = DB::table('drm_products')->get();
        foreach ($data as $item)
        {
            $image = json_decode($item->image);
            if ($image == null)
            {
                dd($item);
                DB::table('drm_products')->where('id', $item->id)->delete();
            }
        }
    }


    public function translationFix()
    {
        $products = DB::table('drm_products')
            ->whereNull('language_id')
            ->get();

        foreach ($products->chunk(200) as $chunk)
        {
            foreach ($chunk as $value)
            {
                $product['title'] = $value->name;
                $product['description'] = $value->description;
                $product['user_id'] = $value->user_id;
                $product['product_id'] = $value->id;
                $product['ean'] = $value->ean;

                $import = DB::table('drm_imports')->where('id', $value->drm_import_id)->first()->language_id;

                if ($import == '2')
                {
                    DB::table('drm_translation_de')->insert($product);
                }
                //$country = DB::table('countries')->where('id',$value->country_id)->first();


            }
        }
    }


    public function nullDesc()
    {
        $data = DB::table('drm_products')->where('description', '')->delete();
        dd($data);
    }


    function file_get_contents_utf8($fn)
    {
        $content = file_get_contents($fn);
        return mb_convert_encoding(
            $content,
            'UTF-8',
            mb_detect_encoding($content, 'UTF-8, ISO-8859-1', true)
        );
    }

    public function checkImport()
    {
        $data = DB::table('drm_product_categories')->get();
        foreach ($data as $key => $porduct)
        {
            $import = DB::table('drm_imports')->where('id', $porduct->drm_import_id)->first();
            $values['user_id'] = $import->user_id;

            DB::table('drm_product_categories')->where('id', $porduct->id)->update($values);
        }
    }


    public function checkCount()
    {
        $data = DB::table('drm_products')->where('drm_import_id', 248)->get();

        foreach ($data as $value)
        {
            DB::table('product_update_status')->insert(['product_id' => $value->id]);
        }
    }


    public function fixprice()
    {
        $products = DB::table('drm_products')->where('drm_import_id', 1)->get();
        foreach ($products->chunk(100) as $chunk)
        {
            foreach ($chunk as $value)
            {
                //dd($value);
                //$ek_price = str_replace(',','',$value->ek_price);

                //$vk_price = (float)$value->vk_price;
                $ek_price = str_replace(',', '', $value->ek_price);
                //$ek_price = $vk_price/1.1;

                //dd($ek_price,$vk_price);

                //$vk_price = ((float)$ek_price*0.25) + (float)$ek_price;

                $data['ek_price'] = $ek_price;

                //$data['vk_price'] = number_format($vk_price,2);

                DB::table('drm_products')->where('id', $value->id)->update($data);
            }
        }
    }


    public function fixCalc()
    {
        //$imports = DB::table('drm_imports')->get();
        $product = DB::table('drm_products')->where('id', 873)->first();
        dd($product);
        $count = 0;
        echo '
        <!DOCTYPE html>
            <html lang="en">
            <head>
              <title>Manual Update</title>

              <meta name="viewport" content="width=device-width, initial-scale=1">
              <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
              <script src="https://code.jquery.com/jquery-3.4.1.min.js"></script>
              <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
            </head>
            <body>

            <div class="container">
            <div style="margin-top:20%">
                <h2>Updating</h2>
            </div>


              <div class="progress">
                <div class="progress-bar progress-bar-warning progress-bar-striped" aria-valuenow="0" role="progressbar" aria-valuemin="0" width="0%">

                </div>
              </div>
                <div style="margin-top:20px;color:green">
                    <center id="count"></center>
                </div>

            </div>

            </body>
            </html>
        ';

        //$total = count($array);

        foreach ($imports as $import)
        {
            if ($import->id == 3)
            {
                $categories = DB::table('drm_category')->where('drm_import_id', $import->id)->get();
                $calculation = DB::table('drm_product_profit_margins')->where('drm_import_id', $import->id)->get();
                dd($categories);
            }
        }


        //$count++;
        //$new_width = ($count / $total) * 100;


        echo "<script>
        $('#count').html('<b>Processing " . $count . " / " . $total . " Items (" . number_format($new_width, 2) . " %)</b>');
        $('.progress-bar').css('width','" . $new_width . "%');
        </script>";
    }


    // Creating for sending mail with charged amount for a All User
    public function send_email_charged($user_id)
    {

        $data = [];
        $data['payable_charged'] = DB::table('drm_orders_new')
            ->where('cms_user_id', $user_id)
            ->where('char_status', 0)
            ->select(DB::raw('SUM(total) as total_price'), DB::raw('count(Distinct id) as total_order'))
            ->first();

        // jahidulhasanzahid
        $custom_paywall_charge = custom_paywall_charge::where('user_id', $user_id)->select('charge')->first();
        if ($custom_paywall_charge != null)
        {
            $charge_percentage = $custom_paywall_charge->charge;
        } else
        {
            $charge_percentage = 1.4;
        }
        // jahdiulhasanzahid

        $data['_payable'] = number_format((($charge_percentage * $data['payable_charged']->total_price) / 100 + $data['payable_charged']->total_order * 0.30), 2, ',', '.');


        $data['page_title'] = 'Invoice Details';
        $data['user'] = DB::table('cms_users')
            ->join('drm_orders_new', 'cms_users.id', '=', 'drm_orders_new.cms_user_id')
            ->where('cms_users.id', $user_id)
            ->first();

        // $data['product_list'] = DB::table('drm_order_products')->where('drm_order_id',$order_id)->get();

        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $user_id)->orderBy('id', 'desc')->first();

        $pdf = PDF::loadView('admin.drm_order.charged_pdf', $data);

        if (!(filter_var($data['user']->email, FILTER_VALIDATE_EMAIL)))
        {
            return;
        }
        try
        {
            app('drm.mailer')->getMailer($user_id)->send('admin.drm_order.charged_mailD', $data, function ($message) use ($data, $pdf) {
                $message->to($data['user']->email, $data['user']->name)
                    ->subject('Charged Amount this month from DRM')
                    ->attachData($pdf->output(), "charged_invoice.pdf");
            });
        } catch (JWTException $exception)
        {
            $this->serverstatuscode = "0";
            $this->serverstatusdes = $exception->getMessage();
        }
        return back();
    }

    // Creating for sending mail with charged amount for a All User
    public function send_email_charged_all()
    {

        $month = $_REQUEST['month'] ?? date('m');
        $_User_list = DB::table('cms_users')->where('id_cms_privileges', 3)->get();

        $str = '';
        foreach ($_User_list as $key => $id)
        {
            $str = uniqid();

            $data = [];
            $data['payable_charged'] = DB::table('drm_orders_new')
                ->where('cms_user_id', $id->id)
                ->where('cms_user_id', '!=', 98)
                ->where('char_status', 0)
                ->whereMonth('order_date', $month)
                ->whereYear('order_date', date('Y'))
                ->select(DB::raw('SUM(total) as total_price'), DB::raw('count(Distinct id) as total_order'))
                ->first();

            if ($data['payable_charged']->total_order == 0)
            {
                continue;
            }

            // jahidulhasanzahid
            $custom_paywall_charge = custom_paywall_charge::where('user_id', $id->id)->select('charge')->first();
            if ($custom_paywall_charge != null)
            {
                $charge_percentage = $custom_paywall_charge->charge;
            } else
            {
                $charge_percentage = 1.4;
            }
            // jahidulhasanzahid

            $data['_payable'] = number_format((($charge_percentage * $data['payable_charged']->total_price) / 100 + $data['payable_charged']->total_order * 0.30), 2, ',', '.');


            $data['page_title'] = 'Invoice Details';
            $data['user'] = DB::table('cms_users')
                ->join('drm_orders_new', 'cms_users.id', '=', 'drm_orders_new.cms_user_id')
                ->where('cms_users.id', $id->id)
                ->first();

            // $data['product_list'] = DB::table('drm_order_products')->where('drm_order_id',$order_id)->get();

            $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $id->id)->orderBy('id', 'desc')->first();

            $pdf = PDF::loadView('admin.drm_order.charged_pdf', $data);

            if (!(filter_var($data['user']->email, FILTER_VALIDATE_EMAIL)))
            {
                return;
            }
            try
            {
                // app('drm.mailer')->getMailer()->send('admin.drm_order.charged_mailD', $data, function ($message) use ($data, $pdf) {
                //     $message->to($data['user']->email, $data['user']->name)
                //         ->subject('Charged Amount this month from DRM')
                //         ->attachData($pdf->output(), "charged_invoice.pdf");
                // });

                // jahidulhasanzahid
                $billingDetails = DB::table('billing_details')->where('user_id', $id->id)
                    ->join('countries', 'countries.id', '=', 'billing_details.country_id')
                    ->first();

                $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</br>E-mail: $billingDetails->email</br>Phone: $billingDetails->phone</p>";

                $price = $data['_payable'];
                $order_info = [
                    'user_id' => 98,
                    'cms_client' => $id->id,
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => $price,
                    'sub_total' => round($data['payable_charged']->total_order * 0.30, 2),
                    'total_tax' => round(0.00, 2),
                    'payment_type' => "Monthly Charge",
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => "Charge",
                    'shop_id' => 14,
                    'billing' => $detailsInformationForBilling,
                    'order_id_api' => $str,
                ];

                // 1st line
                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', 'Paywall Sold ');
                $order_info['description'][] = iconv('UTF-8', 'ASCII//TRANSLIT', 'Monthly Charge Paywall Sold Of Every  sales charge of ' . $charge_percentage . '% + 0,30 Cent fix.<br>Number of orders:' . $data['payable_charged']->total_order . ' in the period from 01.' . $month . ' to 31.' . $month . '.' . date('Y') . '
' . $data['payable_charged']->total_order . ' x 0,30 Cent.');
                $order_info['qty'][] = $data['payable_charged']->total_order;
                $order_info['rate'][] = $data['payable_charged']->total_order;
                $order_info['tax'][] = 0.00;
                $order_info['product_discount'][] = 0 ?? 0;

                $amount = $data['payable_charged']->total_order * .3;
                $order_info['amount'][] = $amount;

                // 2nd line
                $order_info['product_name'][] = iconv('UTF-8', 'ASCII//TRANSLIT', 'Paywall Sold () ');
                $order_info['description'][] = iconv('UTF-8', 'ASCII//TRANSLIT', 'In the period from 01.' . $month . ' to 31.' . $month . '.' . date('Y') . ', sales in the amount of ' . $data['payable_charged']->total_price . ' EUR were realised. Sales participation of ' . $charge_percentage . '%, based on ' . $data['payable_charged']->total_price . ' EUR');
                $order_info['qty'][] = $data['payable_charged']->total_order;
                $order_info['rate'][] = $data['payable_charged']->total_price;
                $order_info['tax'][] = 0.00;
                $order_info['product_discount'][] = 0 ?? 0;
                $order_info['amount'][] = $price - $amount;


                app('App\Http\Controllers\AdminDrmOrdersController')->internalOrderInsert($order_info);
                // jahidulhasanzahid
            } catch (JWTException $exception)
            {
                $this->serverstatuscode = "0";
                $this->serverstatusdes = $exception->getMessage();
            }
        }
        return 1;
    }

    public function show_charged_invoice($user_id)
    {
        // jahidulhasanzahid
        $custom_paywall_charge = custom_paywall_charge::where('user_id', $user_id)->select('charge')->first();
        if ($custom_paywall_charge != null)
        {
            $charge_percentage = $custom_paywall_charge->charge;
        } else
        {
            $charge_percentage = 1.4;
        }
        // jahidulhasanzahid
        $data = [];
        // dd($user_id);
        $data['payable_charged'] = DB::table('drm_orders_new')
            ->where('cms_user_id', $user_id)
            ->where('char_status', 0)
            ->select(DB::raw('SUM(total) as total_price'), DB::raw('count(Distinct id) as total_order'))
            ->first();
        $data['_payable'] = number_format((($charge_percentage * $data['payable_charged']->total_price) / 100 + $data['payable_charged']->total_order * 0.30), 2, ',', '.');

        // dd($data['payable_charged']);
        $data['page_title'] = 'Invoice Details';
        $data['user'] = DB::table('cms_users')
            ->join('drm_orders_new', 'cms_users.id', '=', 'drm_orders_new.cms_user_id')
            ->where('cms_users.id', $user_id)
            ->first();

        // $data['product_list'] = DB::table('drm_order_products')->where('drm_order_id',$order_id)->get();

        $data['setting'] = DB::table('drm_invoice_setting')->where('cms_user_id', $id)->first();

        $pdf = PDF::loadView('admin.drm_order.charged_pdf', $data);

        return $pdf->stream();
    }

    // View charged amount
    public function viewChargedAmount($user_id)
    {
        // jahidulhasanzahid
        $custom_paywall_charge = custom_paywall_charge::where('user_id', $user_id)->select('charge')->first();
        if ($custom_paywall_charge != null)
        {
            $charge_percentage = $custom_paywall_charge->charge;
        } else
        {
            $charge_percentage = 1.4;
        }
        // jahidulhasanzahid

        $data['page_title'] = 'Charged Amount';
        $_user = DB::table('cms_users')
            ->join('drm_orders_new', 'cms_users.id', '=', 'drm_orders_new.cms_user_id')
            ->where('cms_users.id', $user_id)
            ->where('drm_orders_new.char_status', 0)
            ->select(DB::raw('SUM(drm_orders_new.total) as total_price'), 'cms_users.*', DB::raw('count(Distinct drm_orders_new.id) as total_order'))
            ->first();
        $_paid_charged = DB::table('drm_orders_new')
            ->where('cms_user_id', $user_id)
            ->where('char_status', 1)
            ->select(DB::raw('SUM(total) as total_price'), DB::raw('count(Distinct id) as total_order'))
            ->first();

        $_total_charge = number_format((($charge_percentage * $_user->total_price) / 100 + $_user->total_order * 0.30), 2, ',', '.');
        $_total_paid = number_format((($charge_percentage * $_paid_charged->total_price) / 100 + $_paid_charged->total_order * 0.30), 2, ',', '.');

        $data = array(
            'charged_due_amount' => $_total_charge,
            'charged_paid_amount' => $_total_paid,
            'total_order' => $_user->total_order,
            'total_order_price' => $_user->total_price,
            'user_name' => $_user->name
        );

        return view('admin.drm_order.charged_amount', $data);
    }


    public function moveFiles()
    {
        $allImport = DrmImport::cursor();
        $fileds = DB::table('drm_product_fields')->cursor();

        $all_fileds = collect($fileds->all());
        foreach ($allImport as $import)
        {
            $path = $import->csv_file_path;
             $path = str_replace('public','storage',$path);
             $exist = $this->does_url_exists('http://sisdemo.club/drm_montanes/public/'.$path);
             if($exist){
               $s3_path = str_replace('storage','public',$path);
               $result = Storage::disk('spaces')->exists($s3_path);
               if($result == false){
                 dump($result);
                  $file = file_get_contents('http://sisdemo.club/drm_helga/public/'.$path);
                  Storage::disk('spaces')->put($s3_path,$file,'public');
               }
             }

            $field = $all_fileds->where('drm_import_id', $import->id)->first();
            if ($field != null)
            {
                $headers = json_decode($import->csv_headers, true);
                $demo = json_decode($import->demo_data, true);
                if (is_array($demo) && is_array($headers))
                {
                    if (count($headers) == count($demo))
                    {
                        $demo_data = array_combine($headers, $demo);
                        $demo_data = removeNullKeys($demo_data);

                        $format = $import->money_format;
                        $ek_price = $demo_data[$field->ek_price];
                        $vk_price = $demo_data[$field->vk_price];
                        if ($ek_price != null)
                        {
                            $mod_ek = drm_convert_european_to_decimal($ek_price, $format);
                        }
                        if ($ek_price != null)
                        {
                            $mod_vk = drm_convert_european_to_decimal($vk_price, $format);
                        }

                        if (($ek_price != 0 && $mod_ek == 0) || ($vk_price != 0 && $mod_vk == 0))
                        {
                            if ($format == 1)
                            {
                                $import->money_format = 2;
                            } else
                            {
                                $import->money_format = 1;
                            }
                        } else
                        {
                            $import->money_format = $format;
                        }
                        $import->save();
                    }
                }
            }
        }
    }

    function does_url_exists($url)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_NOBODY, true);
        curl_exec($ch);
        $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        if ($code == 200)
        {
            $status = true;
        } else
        {
            $status = false;
        }
        curl_close($ch);
        return $status;
    }


    function readTheFile1($path)
    {
        $lines = [];
        $handle = fopen($path, "r");
        while (!feof($handle))
        {
            $lines[] = trim(fgets($handle));
        }
        fclose($handle);
        return $lines;
    }

    function readTheFile2($path)
    {
        $handle = fopen($path, "r");
        while (!feof($handle))
        {
            yield trim(fgets($handle));
        }
        fclose($handle);
    }

    function getProgress()
    {
        $data = [
            "XTSOL" => "XTSOL",
            "Hersteller/Liferant" => "Kahrs GmbH",
            "Haendler-Artikelnummer" => "00004646",
            "Saunella-Artikelnummer" => "440100001",
            "EAN" => "4260678060003",
            "Kategorie" => "Saunen Bausätze",
            "Titel" => "KARIBU Saunatür Variante 1, Holztür mit Isolierglas, links / rechts verwendbar Durchgang 64 cm x 173 cm, Rahmen 3,8/4 cm",
            "EK - Mwst." => "294,11",
            "EK + Mwst." => "349,99",
            "UVP - Mwst." => "294,11",
            "UVP + Mwst." => "349,99",
            "Marge zum Haendler %" => "",
            "Marge zum Haendler €" => "8,40",
            "VK - Mwst." => "302,51",
            "VK + Mwst." => "359,99",
            "VK Aufschlag" => "",
            "Bestand" => "50",
            "Lieferzeiten" => "1-2 Wochen",
            "Lieferkosten" => "149,00",
            "Bild Prefix" => "https://www.saunella.com/images/product_images/original_images/",
            "Bild All in One" => "00004665-01-karibu-saunatuer-variante-1-holztuer-isolierglas-links-rechts-verwendbarZWcHYoBFSIr2O.jpg",
            "Bild Suffix" => "",
            "Beschreibung" => "<h4><strong>Energiesparend: Karibu Saunat&uuml;r f&uuml;r 38/40 mm Rahmen</strong></h4><p>Diese Saunat&uuml;r von Karibu ist besonders energiesparend. Es handelt sich um eine Holzt&uuml;r mit bronziertem Isolierglas, das w&auml;rmed&auml;mmend wirkt. Die Saunat&uuml;r ist rechts und links anschlagbar. Der T&uuml;rrahmen besteht aus Massivholz. Geeignet f&uuml;r Karibu Saunen mit 38/40 mm Wandst&auml;rke.</p><p>Produktinformationen:<br />Material: Massivholz, Isolierglas<br />Ma&szlig;e: 64 x 173 cm (B x H)<br />Herkunft made in Germany<br />Gewicht: 44,60kg</p>",
            "Bild 1" => "https://www.saunella.com/images/product_images/original_images/00004665-01-karibu-saunatuer-variante-1-holztuer-isolierglas-links-rechts-verwendbarZWcHYoBFSIr2O.jpg",
            "Bild 2" => "https://www.saunella.com/images/product_images/original_images/",
            "Bild 3" => "https://www.saunella.com/images/product_images/original_images/",
            "Bild 4" => "https://www.saunella.com/images/product_images/original_images/",
            "Bild 5" => "https://www.saunella.com/images/product_images/original_images/",
            "Bild 6" => "https://www.saunella.com/images/product_images/original_images/",
            "Bild 7" => "https://www.saunella.com/images/product_images/original_images/",
            "Bild 8" => "https://www.saunella.com/images/product_images/original_images/",
            "Bild 9" => "https://www.saunella.com/images/product_images/original_images/",
            "Bild 10" => "https://www.saunella.com/images/product_images/original_images/",
        ];

        $fields = [
            "ean" => "EAN",
            "ek_price" => "EK - Mwst.",
            "vk_price" => "VK Aufschlag"
        ];
        $fields = (object)$fields;
        $drm = DB::table('drm_imports')->where('id', 25)->first();
        $product_categories = DB::table('drm_product_categories')->cursor();
        $product_categories = $product_categories->where('product_id', 24667)->pluck('category_id')->toArray();

        $price = drmPriceCalculation($fields, $data, $drm, $product_categories);
        dd($price);
    }

    public function fixImages()
    {
        $products = DB::table('drm_products')->select('id', 'image')->get();
        $chunks = $products->chunk(500);
        $count = 0;
        foreach ($chunks as $products)
        {
            foreach ($products as $product)
            {
                $count++;
                if (strpos($product->image, 'drm.software'))
                {
                    $images = json_decode($product->image, true);
                    if ($images)
                    {
                        $image = array_map('replace', $images);
                        $json = json_encode($image);
                        DB::table('drm_products')->where('id', $product->id)->update(['image' => $json]);
                    }
                }
            }
            echo $count . "<br />";
        }
    }

    public function removeDuplicates()
    {
        // $this->fixUpdateStatus();
        $products = DB::table('drm_products')
            ->where('drm_import_id',2331)->get();
        //

        foreach ($products as $key => $value)
        {
            $duplicates = DB::table('drm_products')
                ->where('user_id',$value->user_id)
                ->where('ean',$value->ean)
                ->where('id','<>',$value->id)
                ->get();
            $value->translations = $duplicates;
        }
        $data['products'] = $products;
        return view('testtable', $data);
        //   $this->removeThomasDuplicate();
    }

    public function fixUpdateStatus()
    {
        // $products = DrmProduct::where('update_status','LIKE', '%"0": "ek_price"%')->cursor();
        // $chunks = $products->chunk(5000);
        // foreach($chunks as $chunk){
        //     foreach($chunk as $product)
        //     {
        //         $update_status = json_decode($product->update_status,true);
        //         unset($update_status[0]);
        //         $update_status['ek_price'] = 1;

        //         $product->update_status = json_encode($update_status);
        //         $product->save();
        //     }
        // }

        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        $profit_margins = DB::table('drm_product_profit_margins')->where('drm_import_id', 314)->cursor();

        foreach ($profit_margins as $key => $value)
        {
            \App\Jobs\UpdateProductProfit::dispatch($value->id, 314, 61);
        }
    }

    public function delete()
    {
        $id = $_REQUEST['id'];
//        $ean = $_REQUEST['ean'];
//        $translation = DB::table('drm_translation_de')->where('id', $id)->first();
//        $product_id = DB::table('drm_translation_de')->where('product_id', $translation->product_id)->where('id', '<>', $id)->update(['ean' => $ean]);
//        DB::table('drm_translation_de')->where('id', $id)->delete();
        DB::table('drm_products')->where('id',$id)->delete();
        DB::table('drm_product_categories')->where('product_id',$id)->delete();
    }

    public function changeLogs()
    {
        ini_set('memory_limit', -1);
        $all_ids = DB::table('drm_change_logs')->groupBy('product_id')->whereNotNull('product_id')->pluck('product_id')->toArray();
        // dd((memory_get_peak_usage(true)/1024/1024));
        $chunks = array_chunk($all_ids, 200);
        foreach ($chunks as $chunk)
        {
            foreach ($chunk as $id)
            {
                $logs = DB::table('drm_change_logs')->where('product_id', $id)->orderBy('id', 'DESC')->take(5);
                $all = $logs->get();
                $user = $logs->first()->id_cms_users;
                $data = [];
                foreach ($all as $key => $value)
                {
                    $pieces = explode(' ', $value->description);
                    $last_word = array_pop($pieces);
                    if (strpos($value->description, 'changed'))
                    {
                        $status = 2;
                    }
                    if (strpos($value->description, 'created'))
                    {
                        $status = 1;
                    }
                    if (strpos($value->description, 'enabled update'))
                    {
                        $status = 3;
                    }
                    $data[] = [
                        'ip' => $value->ipaddress,
                        'user' => $value->id_cms_users,
                        'time' => $value->created_at,
                        'field' => $last_word,
                        'status' => $status
                    ];
                }
                $insert = json_encode($data);
                $insertData = [
                    'product_id' => $id,
                    'log' => $insert
                ];
                DB::table('product_change_logs')->insert($insertData);
            }
        }
    }

    function jsonshoptest()
    {
        $info = DB::table('ebay_products')->where('id', 8)->first();
        $gambio_info = DB::table('gambio_products')->where('id', 49746)->first();
        //DB::table('drm_products')->where('ean',4251606205153)->update(['connected_shops'=>$data]);

        $product = DB::table("drm_products")->whereIn('id', [390226, 571057])->get()->toarray();

        dd($product, $info, $gambio_info);
    }


    public function transferProducts()
    {
        $all_products = DB::table('drm_products')
            ->select('drm_products.*', 'drm_translation_de.title', 'drm_translation_de.description as description')
            ->join('drm_translation_de', 'drm_translation_de.product_id', '=', 'drm_products.id')
            ->where('drm_products.user_id', '<>', 61)->get();
        foreach ($all_products->chunk(1000) as $chunks)
        {
            $ids = $chunks->pluck('id')->toArray();
            $update_status = DB::table('product_update_status')->whereIn('product_id', $ids)->cursor();
            $update_status_all = collect($update_status->all());

            $categories = DB::table('drm_product_categories')->whereIn('product_id', $ids)->cursor();
            $categories_all = collect($categories->all());

            $insertArray = [];
            foreach ($chunks as $value)
            {
                $status = $update_status_all->where('product_id', $value->id)->first();
                $status = (array)$status;
                unset($status['product_id']);
                unset($status['drm_import_id']);
                $category = $categories_all->where('product_id', $value->id)->pluck('category_id')->toArray();
                if (!$status)
                {
                    $status = [];
                }
                $status = json_encode($status);
                $data = (array)$value;
                unset($data['category']);
                unset($data['update_enabled']);
                unset($data['language_id']);
                unset($data['name']);
                $data['update_status'] = $status;
                $data['category_ids'] = json_encode($category);
                $insertArray[] = $data;
            }
            DB::table('drm_products_de')->insert($insertArray);
            dump((memory_get_peak_usage(true) / 1024 / 1024));
        }
    }

    public function changeImportCategory()
    {
        $imports = DB::table('drm_imports')->get();
        foreach ($imports as $import)
        {
            $category_ids = DB::table('drm_import_categories')->where('drm_import_id', $import->id)->pluck('category_id')->toArray();
            $unique = array_unique($category_ids);
            $ids = json_encode(array_values($unique));
            DB::table('drm_imports')->where('id', $import->id)->update(['category_ids' => $ids]);
        }
    }

    public function changeUpdateStatus()
    {
        $all_products = DB::table('product_update_status')
            ->get();
        foreach ($all_products->chunk(1000) as $chunks)
        {
            foreach ($chunks as $value)
            {
                $status = (array)$value;
                unset($status['product_id']);
                unset($status['drm_import_id']);
                unset($data['category']);
                if (!$status)
                {
                    $status = [];
                }
                $status = json_encode($status);
                $data['update_status'] = $status;
                DB::table('drm_products')->where('id', $value->product_id)->update($data);
            }
        }
    }

    public function productDuplicate()
    {
        $ids = DB::table('drm_products')
            ->groupBy("ean", "user_id")
            ->havingRaw("COUNT(ean) > 1")
            ->orderBy('id', 'asc')
            ->get();
        dd($ids);
        foreach ($ids as $key => $product)
        {
            $all = DB::table('drm_products')
                ->where('id', '>', $product->id)
                ->where('ean', $product->ean)
                ->where('user_id', $product->user_id)
                // ->where('ek_price',$product->ek_price)
                // ->where('vk_price',$product->vk_price)
                ->delete();
        }
    }


    public function getShopInfo()
    {
        $all_ids = DB::table('drm_products')->where('user_id', CRUDBooster::myId())->pluck('id')->toArray();
        dd($this->getConnectedShops($all_ids));
    }

    public function culrRequestExec($curl_url, $curl_header, $alldata = null, $request_type = 'POST')
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => $curl_url,
            CURLOPT_HEADER => 0,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_CUSTOMREQUEST => $request_type,
            CURLOPT_HTTPHEADER => $curl_header,
            CURLOPT_POSTFIELDS => $alldata,
            CURLOPT_FOLLOWLOCATION => true,
        ));

        $output = curl_exec($curl);
        return $output;
    }

    public function allowCats()
    {
        $all = DB::table('ebay_categories')->where('status', 0)->get();

        $allowed = [
            'Marke',
            'Hersteller',
            'Herstellernummer',
            'Farbe',
            'Stil',
            'Obermaterial',
            'Produktart',
            'Abteilung',
            'Fahrrad',
            'Schuhgröße',
            'Material',
            'Modell'
        ];

        $count = 0;
        foreach ($all as $cat)
        {
            $array = json_decode($cat->aspects, true);
            $aspects = array_keys($array);
            $diff = array_diff($aspects, $allowed);
            if (count($diff) == 0)
            {
                DB::table('ebay_categories')->where('id', $cat->id)->update(['status' => 1]);
                $count++;
            }
        }
        dd($count);
    }


    public function getConnectedShops($product_ids = [])
    {
        $api_url = env('EXPORT_BASE_URL', 'http://138.197.184.210');

        if ($_REQUEST['product_ids'] != null)
        {
            $product_ids = explode(',', $_REQUEST['product_ids']);
        }

        if (is_array($product_ids))
        {
            $product_ids = array_map('makeInt', $product_ids);
        }

        $mapping = [];
        foreach (array_chunk($product_ids, 1000) as $ids)
        {
            $post = [
                'product_ids' => $ids,
            ];
            $data = http_build_query($post);
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url . "/api/v1/products/map?" . $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            $server_output = curl_exec($ch);
            $channel_mapping = json_decode($server_output, true);
            curl_close($ch);
            dump($server_output);
            if ($channel_mapping['product_channel_map'])
            {
                foreach ($channel_mapping['product_channel_map'] as $key => $value)
                {
                    $mapping[$key] = $value;
                }
            }
        }

        return $mapping;
    }


    public function randEan()
    {

    }

    public function updateChannelProducts($products, $user_id)
    {
        $url = "https://drm.software/api/channel_products/update";

        foreach (array_chunk($products, 30) as $product)
        {
            $data = [
                'product_ids' => $product,
                'user_id' => $user_id
            ];
            $ch = curl_init();
            $headers = array("accept: application/json", "content-type: application/json");

            curl_setopt_array($ch, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_HEADER => 1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_POSTFIELDS => json_encode($data),
                CURLOPT_FOLLOWLOCATION => true
            ));

            $output = curl_exec($ch);
            dump($output);
        }
    }


    public function amazonTest()
    {
        $product_ids = array(2238940);
        return Ebay::export($product_ids, 62);
    }

    public function etsyTest()
    {
        $product_ids = [
            2238601
        ];

        // $res = App\Services\Modules\Export\Etsy::export($product_ids, 62);
        return $res;
    }

    public function ottoTest()
    {
        $product_ids = [
            2238601
        ];

        return Otto::export(71, $product_ids);
    }

    public function ebayFix()
    {
        $all_ids = DB::table('drm_products')->where('user_id', 413)
            ->pluck('id')->toArray();

        foreach (array_chunk($all_ids, 40) as $ids)
        {
            $connected_shops = ExportServices::updateConnected($ids, 413);
            AdminDrmExportsController::updateAnyConnectedShopProduct($ids, 413, "de");
        }
        // $this->shopPriceFix();
    }


    public function shopPriceFix()
    {
        // $product_ids = DB::table('drm_product_shop_prices')->whereIn('shop_id',[72,88,116,191,192,145])->pluck('drm_product_id')->toArray();
        // DB::table('drm_product_shop_prices')->whereIn('shop_id',[72,88,116,191,192,145])->delete();
        // $affected = array();
        // foreach($shop_prices->chunk(500) as $prices){
        //     foreach($prices as $price)
        //     {
        //         $product = DB::table('product_change_logs')->where('product_id',$price->drm_product_id)->first();
        //         $log = json_decode($product->log);
        //         if($log){
        //             foreach($log as $lg){
        //                 if(strtotime($lg->time) == strtotime($price->created_at)){
        //                     DB::table('drm_product_shop_prices')->where('id',$price->id)->delete();
        //                     $affected[] = $price->drm_product_id;
        //                 }
        //             }
        //         }
        //     }
        // }
        // $affected = array_unique($affected);
        $product_ids = DB::table('drm_products')->where('user_id', 75)->pluck('id')->toArray();
        foreach (array_chunk($product_ids, 10) as $prices)
        {
            // ExportServices::updateConnected($prices,75);
            AdminDrmExportsController::updateAnyConnectedShopProduct($prices, 75, "de");
        }

        // foreach($affected as $value){
        //     $product = DB::table('drm_products')->where('id',$value)->first();

        //     if($product){
        //         ExportServices::updateConnected([$value],$product->user_id);
        //         AdminDrmExportsController::updateAnyConnectedShopProduct([$value],$product->user_id,"de");
        //     }
        // }
    }


    public function gambioTest()
    {

        $products = DB::table('drm_products')->where('user_id',1837)->get();

        foreach($products as $product)
        {
            $image_json = json_decode($product->image,true);
            if(!is_array($image_json)){
                $image_json = json_decode($image_json,true);
                DB::table('drm_products')->where('id',$product->id)->update(['image' => json_encode($image_json)]);
            }
        }
        dd('Ok');

        try {
            $products = DrmProduct::whereNull('title')->get();

            foreach($products->chunk(500) as $chunk)
            {
                $translations_de = DB::table('drm_translation_de')
                    ->select('product_id','title','description')
                    ->whereIn('product_id',$chunk->pluck('id')->toArray() ?? array())->get();

                $translations_en = DB::table('drm_translation_en')
                    ->select('product_id','title','description')
                    ->whereIn('product_id',$chunk->pluck('id')->toArray() ?? array())->get();

                foreach($chunk as $product)
                {
                    $title = array();
                    $description = array();

                    $translation_de = $translations_de->where('product_id',$product->id)->first();
                    $translation_en = $translations_en->where('product_id',$product->id)->first();

                    if(!empty($translation_de))
                    {
                        $title['de'] = $translation_de->title;
                        $description['de'] = $translation_de->description;
                    }

                    if(!empty($translation_en))
                    {
                        $title['en'] = $translation_en->title;
                        $description['en'] = $translation_en->description;
                    }

                    // if(is_array($product->image[0])){
                    //     $product->image = collect($product->image)->pluck('src')->toArray();
                    // }

                    $product->title = $title;
                    $product->description = $description;
                    $product->save();
                }
            }
        } catch (\Throwable $th) {}


        dd(peakMemory());

        // $ids = array();
        // $shop = \App\Shop::where('channel', 1)->where('user_id', 96)->first();
        // $gambioApi = new GambioApi($ids, $shop, "de", 96);
        // $gambioApi->findDuplicateCategories($shop);
        // // $result = $gambioApi->searchApiCategory("root");

        // // // dd($result);
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();
        Redis::flushDB();


        // $prod = DB::table('drm_deleted_products')
        //     ->select('drm_deleted_products.*', 'drm_imports.user_id as import_user')
        //     ->leftJoin('drm_imports', 'drm_imports.id', '=', 'drm_deleted_products.drm_import_id')->get();

        // foreach ($prod as $key => $value)
        // {
        //     // dd($value);
        //     DB::table('drm_deleted_products')->where('id', $value->id)->update(['user_id' => $value->import_user]);
        // }


        $products = DB::table('drm_products')->where('user_id',96)->pluck('id')->toArray();
        AdminDrmExportsController::updateAnyConnectedShopProduct($products,96,"de");
    }



    protected string $token = '';

    public function getData(string $restUrl)
    {
        $client = new Client();
        try
        {
            $res = $client->request('GET', 'https://api.otto.market' . $restUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->token,
                    'Accept' => 'application/json',
                ]
            ]);
            return json_decode($res->getBody());
        } catch (GuzzleException $e)
        {
            return json_decode((string)[]);
        }
    }

    public function generateToken()
    {
        $client = new Client();
        try
        {
            $res = $client->request('POST', 'https://api.otto.market/v1/token', [
                'headers' => [
                    'Accept' => 'application/json',
                    'Content-Type' => 'application/x-www-form-urlencoded',
                ],
                'form_params' => [
                    'username' => 'api_1013069',
                    'password' => 't6W52ra%APp@',
                    'grant_type' => 'password',
                    'client_id' => 'token-otto-api',
                ]
            ]);
            $body = json_decode($res->getBody());
            $this->token = $body->access_token;

            return '';
        } catch (GuzzleException $e)
        {
            return json_decode((string)[]);
        }
    }

    public function ottoCategoriesTest()
    {
        $parentInserted = 0;
        $childrenInserted = 0;

        $this->generateToken();
        $nextUrl = '/v1/products/categories?page=0&limit=500';
        $formattedCategoryGroups = [];
        $formattedCategoryGroups2 = [];

        while (true)
        {
            $body = $this->getData($nextUrl);
            if (gettype($body) == 'object')
            {

                $categoryGroups = $body->categoryGroups;
                foreach ($categoryGroups as $categoryGroup)
                {
                    $categoryId = DB::table('otto_categories')->insertGetId([
                        'name' => $categoryGroup->categoryGroup,
                        'level' => 0,
                        'parent_id' => null,
                        'created_at' => Carbon::now()->toDateTimeString(),
                        'updated_at' => Carbon::now()->toDateTimeString()
                    ]);
                    $parentInserted++;
                    $subCategories = [];
                    foreach ($categoryGroup->categories as $category)
                    {
                        array_push($subCategories, [
                            'name' => $category,
                            'level' => 1,
                            'parent_id' => $categoryId,
                            'created_at' => Carbon::now()->toDateTimeString(),
                            'updated_at' => Carbon::now()->toDateTimeString()
                        ]);
                    }
                    DB::table('otto_categories')->insert($subCategories);
                    $childrenInserted = $childrenInserted + count($subCategories);
                }

                $links = $body->links;
                $nextUrl = '';
                if (count($links) > 0)
                {
                    foreach ($links as $link)
                    {
                        if ($link->rel === "next")
                        {
                            $nextUrl = $link->href;
                        }
                    }
                }
            } else
            {
                $this->generateToken();
                continue;
            }

            $totalInserted = $parentInserted + $childrenInserted;
            print "$parentInserted => $childrenInserted => $totalInserted \n";

            if (strlen($nextUrl) > 0)
            {
                print 'fetching new data' . "\n";
                continue;
            } else
            {
                print implode(",", [implode(",", $formattedCategoryGroups), implode(",", $formattedCategoryGroups2)]);
                break;
            }

        }
    }

    public function downloadDT()
    {
        return redirect('https://drm-file.fra1.digitaloceanspaces.com/public/DT/Droptienda.zip');
    }


    public function moveTranslations()
    {
        try {
            $products = DrmProduct::where('user_id',62)->whereNull('title')->get();

            foreach($products->chunk(500) as $chunk){
              $translations_de = DB::table('drm_translation_de')
              ->select('product_id','title','description')
              ->whereIn('product_id',$chunk->pluck('id')->toArray() ?? array())->get();

              $translations_en = DB::table('drm_translation_en')
              ->select('product_id','title','description')
              ->whereIn('product_id',$chunk->pluck('id')->toArray() ?? array())->get();

              foreach($chunk as $product)
              {
                $title = array();
                $description = array();

                $translation_de = $translations_de->where('product_id',$product->id)->first();
                $translation_en = $translations_en->where('product_id',$product->id)->first();

                if(!empty($translation_de)){
                  $title['de'] = $translation_de->title;
                  $description['de'] = $translation_de->description;
                }

                if(!empty($translation_en)){
                  $title['en'] = $translation_en->title;
                  $description['en'] = $translation_en->description;
                }

                $product->title = $title;
                $product->description = $description;
                $product->save();
              }
            }
        } catch (\Throwable $th) {}
    }

    public function moveImages()
    {

    }

    public function tgdvjdj()
    {
        return view('channel_products.modals.import_price');
    }
}
