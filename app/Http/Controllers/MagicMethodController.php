<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Services\DRMProductService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use DB;
use App\Services\Keepa\Automagic;
use App\Services\Keepa\Keepa;
use App\Services\ChannelProductService;
use App\Services\DRMCategoryService;
use App\Models\DrmCategory;
use App\Models\DRMProductCategory;


class MagicMethodController extends Controller
{
   private DRMProductService $productService;
   private DRMCategoryService $categoryService;
     //OpenAI GPT3 Engine names :
     private $engines = [
        "babbage" => "text-babbage-001",
        "curies" => "text-curie-001",
        "ada" => "text-ada-001",
        "davinci" => "text-davinci-003"
     ];
 
     //Put your OpenAI API Token !
     private $token = "***************************************************";


     public function __construct(DRMProductService $productService, DRMCategoryService $categoryService)
    {
        $this->productService = $productService;
        $this->categoryService = $categoryService;
        $this->module_id = config('modules.list.'.$this->slug);
    }



    function autoFillMail(Request $req){

      // $user_import_plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck(CRUDBooster::myParentId());
      // $check = in_array($user_import_plan['plan'], ['Free', 'Trial']);
      $userId = CRUDBooster::myParentId();

      // if($check==false){
       $token_limit = @get_token_credit(CRUDBooster::myParentId());
       $total_credits = $token_limit['remain_credit'] ?? 0;
       
       if($req->credit > $total_credits){
            return [
                'status' => 'error',
                'error' => 'limit_exceeded',
                //'redirect' => url('admin/app_form'),
                'message' => 'Credit Limit Finished . Please Purchase more credits !.'
            ];
        }
      // }
        
        //prompt or you can say user input
        $prompt = "Please create an email template with the following facts:" .$req->subject;
        //choose model !
        //Davinci is the most powerful engine
        $engine = $this->engines['davinci'];
        //max tokens you want as an output
        //1 token is almost 0.75 word
        $maxTokens = 500;   
        //Using Laravel HTTP
        $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => "Bearer $this->token"
            ])->post("https://api.openai.com/v1/engines/$engine/completions", [
                'prompt' => $prompt,
                "temperature" => 0.7,
                "max_tokens" => $maxTokens,
                "top_p" => 1,
                "frequency_penalty" => 0,
                "presence_penalty" => 0,
            ]);

            //dd($response);

        //Now check if the request was successful or not !
        //After checking print result !

        if($response->failed()){
                return "Request Failed !";
        }
        else{

            //OpenAI API result
            // if($check){
            //    charge_token_trial($req->credit,$userId);
            // }else{
               charge_token($req->credit,$userId);
            // }
            
            return $response['choices'][0]['text'];
        }
    }

    function autoFillMailCampaign(Request $req){
       
        $token_limit = @get_token_credit(CRUDBooster::myParentId());
        $total_credits = $token_limit['remain_credit'] ?? 0;
        $userId = CRUDBooster::myParentId();
      //   $user_import_plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck(CRUDBooster::myParentId());
      //   $check = in_array($user_import_plan['plan'], ['Free', 'Trial']);

      //   if($check==false){
         if($req->credit > $total_credits){
            return [
                'status' => 'error',
                'error' => 'limit_exceeded',
                'message' => 'Credit Limit Finished . Please Purchase more credits !.'
            ];
         }
      //   }

       
         
         $prompt = "Please create an email campaign for me with the following content.".$req->subject.".The email should be ".$req->words." words.";
         $engine = $this->engines['davinci'];
         $maxTokens = 500;   
         $response = Http::withHeaders([
                 'Content-Type' => 'application/json',
                 'Authorization' => "Bearer $this->token"
             ])->post("https://api.openai.com/v1/engines/$engine/completions", [
                 'prompt' => $prompt,
                 "temperature" => 0.7,
                 "max_tokens" => $maxTokens,
                 "top_p" => 1,
                 "frequency_penalty" => 0,
                 "presence_penalty" => 0,
             ]);
 
         if($response->failed()){
                 return "Request Failed !";
         }
         else{
             //charge_token($req->credit,$userId);
            // if($check){
            //    charge_token_trial($req->credit,$userId);
            // }else{
               charge_token($req->credit,$userId);
            // }

             $email_template = '<table width="100%" cellspacing="0" cellpadding="0" border="0" style="background:rgb(233, 234, 234);">
             <tbody>
                <tr>
                   <td>
                      <div style="margin:0 auto;width:600px;padding:0px">
                         <p><br></p>
                         <table class="main" style="background-color: rgb(255, 255, 255); width: 600px; border-spacing: 0px; border-collapse: collapse;" data-mce-selected="1" width="100%" cellspacing="0" cellpadding="0" border="0" align="center">
                            <tbody>
                               <tr>
                                  <td class="element-content" style="padding: 10px 50px; font-family: Arial; font-size: 13px; color: rgb(0, 0, 0); line-height: 22px; border-collapse: collapse;" align="left">Dear [Name],
                                     <p>'.$response['choices'][0]['text'].'</p>
                                  </td>
                               </tr>
                            </tbody>
                         </table>
                      </div>
                   </td>
                </tr>
          </table>';

             //$email_template = "<p>".$response['choices'][0]['text']."</p>";
          
             DB::table('email_marketings')->where('id',$req->id)->update([
                'name' => $req->subject,
                'email_template' => $email_template
             ]);
             return $$email_template;
         }
     }

     function autoFillCategory(Request $request){
      $automagic_product_limit = @get_token_credit(CRUDBooster::myParentId());
      $automagic_product_limit = $automagic_product_limit['remain_credit'] ?? 0;

      $userId = CRUDBooster::myParentId();
      $products = $this->productService->findUserProductsWithCat($request->product_ids, $userId)->get();

      $product_eans = array_column($products->toArray(), 'ean');

      if($request->credit > $automagic_product_limit){
          return [
              'status' => 'error',
              'error' => 'limit_exceeded',
              //'redirect' => url('admin/app_form'),
              'message' => 'Automagic data completion limit excedeed.'
          ];
      }

      $keepa = new Keepa(env('KEEPA_API_KEY'));
      $keepa_products = $keepa->code($product_eans, $product_param);
      foreach ($products as $key => $product) {
         $keepa_product = $keepa_products->product($product->ean);
         $keepa_price = $keepa_products->latestPrice($product->ean);
         $keepa_cat_tree = $keepa_products->categoryTree($product->ean) ?? [];

            $last_keepa_category_id = end($keepa_cat_tree)['catId'] ?? false;
            if($last_keepa_category_id){
               $cat_name = implode( "/", array_column((array)$keepa_cat_tree, 'name')  );

               # product dont have category need autocomplete

               $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
               $lang = app('App\Services\UserService')->getProductLanguage($country_id);
               $drmCategory = [
                  'id' => $last_keepa_category_id,
                  'category_name_'.$lang => $cat_name,
                  'user_id' => CRUDBooster::myId(),
                  'country_id' => $country_id,
               ];

               #get category id or create
               $drm_cat_id = DrmCategory::firstOrCreate(
                  ['id' => $last_keepa_category_id],
                  $drmCategory
               )->id;

               #assign category to product
               DRMProductCategory::create([
                  'product_id' => $product->id,
                  'country_id' => $product->country_id,
                  'category_id' => $drm_cat_id,
               ]);

            }
            charge_token($request->credit,$userId);
         }
         $response = [
            'status' => 'success',
            'message' => "Product informations updated magically!"
      ];
      return $response;
      
     }

   function autoFillCombination(Request $request){
      $userId = CRUDBooster::myParentId();
      $token_limit = @get_token_credit(CRUDBooster::myParentId());
      $total_credits = $token_limit['remain_credit'] ?? 0;

      // $user_import_plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck(CRUDBooster::myParentId());
      // $check = in_array($user_import_plan['plan'], ['Free', 'Trial']);

      // if($check==false){
         if($request->credit > $total_credits){
            return [
                'status' => 'error',
                'error' => 'limit_exceeded',
                'message' => 'Credit Limit Finished . Please Purchase more credits !.'
            ];
         }
      // }

      $data_types = explode(",", $request->dataTypes);
      if(in_array('master',$data_types)){
         $this->magicAutocompleteMaster($request->productId);
      }
      if(in_array('title',$data_types)){
         $this->magicAutocompleteTitle($request->productId,$request->language);
      }
      if(in_array('description',$data_types)){
         $this->magicAutocompleteDescription($request->productId,$request->language);
      }
      if(in_array('category',$data_types)){
         $this->magicAutocompleteCategory($request->productId);
      }

      charge_token($request->credit,$userId);
      // if($check){
      //    charge_token_trial($request->credit,$userId);
      // }else{
      //    charge_token($request->credit,$userId);
      // }

      $response = [
         'status' => 'success',
         'message' => 'Product informations updated magically!'
     ];
     return $response;

      //dd(explode(",", $request->dataTypes), $request->credit);
   }

   public function magicAutocompleteMaster($productId){
      $request = request();
      $count_updated_product = 0;
      //dd($productId);
      $userId = CRUDBooster::myParentId();
      $products = $this->productService->findUserProductsWithCat([$productId], $userId)->get();
      $product_eans = array_column($products->toArray(), 'ean');


      $product_param = [
          'history' => 0,
          'buybox' => 0,
          'rental' => 0,
          'stats' => 1
      ];
      $keepa = new Keepa(env('KEEPA_API_KEY'));
      $keepa_products = $keepa->code($product_eans, $product_param);

      foreach ($products as $key => $product) {
      
          $updated = false;
          $drm_cat_id = false;
          $keepa_product = $keepa_products->product($product->ean);
          //dd( $keepa_product);

          $keepa_price = $keepa_products->latestPrice($product->ean);
          $keepa_cat_tree = $keepa_products->categoryTree($product->ean) ?? [];

          $update_product = [];

          $last_keepa_category_id = end($keepa_cat_tree)['catId'] ?? false;

          if($last_keepa_category_id && $product->drm_categories->isEmpty()){
              $cat_name = implode( "/", array_column((array)$keepa_cat_tree, 'name')  );
              # product dont have category need autocomplete

              $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
              $lang = app('App\Services\UserService')->getProductLanguage($country_id);
              $drmCategory = [
                  'id' => $last_keepa_category_id,
                  'category_name_'.$lang => $cat_name,
                  'user_id' => CRUDBooster::myId(),
                  'country_id' => $country_id,
              ];

              #get category id or create
              $drm_cat_id = DrmCategory::firstOrCreate(
                  ['id' => $last_keepa_category_id],
                  $drmCategory
              )->id;

              #assign category to product
              DRMProductCategory::create([
                  'product_id' => $product->id,
                  'country_id' => $product->country_id,
                  'category_id' => $drm_cat_id,
              ]);
              $updated = true;
          }

          $magic_fields = [
              'brand' => $product->brand,
              'item_color' => $product->item_color,
              'item_weight' => $product->item_weight,
              'item_size' => $product->item_size,
              'ek_price' => $product->ek_price,
              'packaging_length'=> $product->packaging_length,
              'packaging_width'=> $product->packaging_width,
              'packaging_height'=> $product->packaging_height,
          ];
          $empty_fields = hasEmptyValue($magic_fields);

          $languageId = app('App\Services\UserService')->getProductCountry(CRUDBooster::myParentId());
          $lang = app('App\Services\UserService')->getProductLanguage($languageId);

          //dd($keepa_product->packageHeight,$empty_fields);

          //update backup 
              
          $product_old_info = DB::table('drm_products')->where('ean',$product->ean)->first();
          //dd($product_old_info);
          if($product_old_info){
              $update_backup_table = DB::table('drm_products_master_data_backup')->updateOrInsert(['product_id'=> $product_old_info->id,'user_id'=>$userId],[
                 'brand'=> $product_old_info->brand,
                 'item_color'=> $product_old_info->item_color,
                 'item_weight'=> $product_old_info->item_weight,
                 'item_unit'=> $product_old_info->item_unit,
                 'item_size'=> $product_old_info->item_size,
                 'ek_price'=> $product_old_info->ek_price,
                 'packaging_length'=> $product_old_info->packaging_length,
                 'packaging_width'=> $product_old_info->packaging_width,
                 'packaging_height'=> $product_old_info->packaging_height,

              ]);
          }

          if(!empty($empty_fields)){
              # only send request if atleast one required field is empty
              if(\in_array('brand', $empty_fields)) $update_product['brand'] = $keepa_product->brand;
              if(\in_array('item_color', $empty_fields)) $update_product['item_color'] = $keepa_product->color;
              if(\in_array('item_weight', $empty_fields)){
               $update_product['item_weight'] = $keepa_product->itemWeight;
               if(!empty($keepa_product->itemWeight)){
                  $update_product['item_unit'] = "Gram";
               }
              } 
              if(\in_array('item_size', $empty_fields)) $update_product['item_size'] = $keepa_product->size;
              if(\in_array('ek_price', $empty_fields)) $update_product['ek_price'] = $keepa_price;
              if(\in_array('packaging_length', $empty_fields)) $update_product['packaging_length'] = $keepa_product->packageLength;
              if(\in_array('packaging_width', $empty_fields)) $update_product['packaging_width'] = $keepa_product->packageWidth;
              if(\in_array('packaging_height', $empty_fields)) $update_product['packaging_height'] = $keepa_product->packageHeight;
              //dd($update_product['packaging_height']);
              $this->productService->update($product->id, $update_product, $lang, 'manual');

              # dont count if nothing updated. above function dont return if product updated or not
              $updated = true;

              
              //dd($product);

          }
          $industry_template_data = json_decode($product->industry_template_data);

          if( !empty($industry_template_data) ){
              if(isset($keepa_product->model)){
                  if(empty($industry_template_data->juvely->model->$lang)){
                      $industry_template_data->juvely->model->$lang = $keepa_product->model;
                      $data['industry_template_data'] = json_encode($industry_template_data);
                      $this->productService->update($product->id, $data, $lang, 'manual');
                      $updated = true;
                  }
              }
          }
          if($updated) $count_updated_product++;
      }

      $response = [
          'status' => 'success',
          'message' => 'Product informations updated magically!'
      ];
      return $response;
   }

   function magicAutocompleteCategory($productId){
      $userId = CRUDBooster::myParentId();
      $products = $this->productService->findUserProductsWithCat([$productId], $userId)->get();

      $product_eans = array_column($products->toArray(), 'ean');

      try{
         $keepa = new Keepa(env('KEEPA_API_KEY'));
         $keepa_products = $keepa->code($product_eans, $product_param);
         foreach ($products as $key => $product) {
            $keepa_product = $keepa_products->product($product->ean);
            $keepa_price = $keepa_products->latestPrice($product->ean);
            $keepa_cat_tree = $keepa_products->categoryTree($product->ean) ?? [];
   
               $last_keepa_category_id = end($keepa_cat_tree)['catId'] ?? false;
               if($last_keepa_category_id){
                  $cat_name = implode( "/", array_column((array)$keepa_cat_tree, 'name')  );
   
                  # product dont have category need autocomplete
   
                  $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                  $lang = app('App\Services\UserService')->getProductLanguage($country_id);
                  $drmCategory = [
                     'id' => $last_keepa_category_id,
                     'category_name_'.$lang => $cat_name,
                     'user_id' => CRUDBooster::myId(),
                     'country_id' => $country_id,
                  ];
   
                  #get category id or create
                  $drm_cat_id = DrmCategory::firstOrCreate(
                     ['id' => $last_keepa_category_id],
                     $drmCategory
                  )->id;
   
                  #assign category to product
                  DRMProductCategory::create([
                     'product_id' => $product->id,
                     'country_id' => $product->country_id,
                     'category_id' => $drm_cat_id,
                  ]);
   
               }
            }
            $response = [
               'status' => 'success',
               'message' => "Product informations updated magically!"
         ];
         return $response;
      }catch(\Exception $e){
         return $e;
      }
     
      
   }

   function magicAutocompleteTitle($productId,$language){

      $userId = CRUDBooster::myParentId();
       $products = $this->productService->findUserProductsWithCat([$productId], $userId)->get();
       $productTilte = array_column($products->toArray(), 'name');
       $prompt = "provide a suitable title for this product :" .$productTilte[0] ."in language".$language;
       $engine = $this->engines['davinci'];
       $maxTokens = 500;   
       //Using Laravel HTTP
       $response = Http::withHeaders([
               'Content-Type' => 'application/json',
               'Authorization' => "Bearer $this->token"
           ])->post("https://api.openai.com/v1/engines/$engine/completions", [
               'prompt' => $prompt,
               "temperature" => 0.7,
               "max_tokens" => $maxTokens,
               "top_p" => 1,
               "frequency_penalty" => 0,
               "presence_penalty" => 0,
           ]);

       if($response->failed()){
               return "Request Failed !";
       }
       else{
           $value  = array(
            //"de"=>str_replace('"', "", $response['choices'][0]['text'])
            "de"=> $response['choices'][0]['text']
           );
           $productTilte = DB::table('drm_products')->where('id',$productId)->update([
               'title'=>$value
            ]);
  
           return true;
       }
   }

   function magicAutocompleteDescription($productId,$language){

      $userId = CRUDBooster::myParentId();
       $products = $this->productService->findUserProductsWithCat([$productId], $userId)->get();
       $productTilte = array_column($products->toArray(), 'name');
       $prompt = "describe" .$productTilte[0] ."in language".$language;
       $engine = $this->engines['davinci'];
       $maxTokens = 500;   
       //Using Laravel HTTP
       $response = Http::withHeaders([
               'Content-Type' => 'application/json',
               'Authorization' => "Bearer $this->token"
           ])->post("https://api.openai.com/v1/engines/$engine/completions", [
               'prompt' => $prompt,
               "temperature" => 0.7,
               "max_tokens" => $maxTokens,
               "top_p" => 1,
               "frequency_penalty" => 0,
               "presence_penalty" => 0,
           ]);

       if($response->failed()){
               return "Request Failed !";
       }
       else{
           $value  = array(
            //"de"=>str_replace('"', "", $response['choices'][0]['text'])
            "de"=> $response['choices'][0]['text']
           );

           $productTilte = DB::table('drm_products')->where('id',$productId)->update([
               'description'=>$value
            ]);
  
           return true;
       }
   }

   function autoFillStepMailCampaign(Request $req){

    $token_limit = @get_token_credit(CRUDBooster::myParentId());
    $total_credits =$token_limit['remain_credit'] ?? 0;
    $userId = CRUDBooster::myParentId();
    if($req->credit > $total_credits){
         return [
             'status' => 'error',
             'error' => 'limit_exceeded',
             'message' => 'Credit Limit Finished . Please Purchase more credits !.'
         ];
    }
     
     $prompt = "Please create an email campaign for me with the following content.".$req->subject.".The email should be ".$req->words." words.";
     $engine = $this->engines['davinci'];
     $maxTokens = 500;   
     $response = Http::withHeaders([
             'Content-Type' => 'application/json',
             'Authorization' => "Bearer $this->token"
         ])->post("https://api.openai.com/v1/engines/$engine/completions", [
             'prompt' => $prompt,
             "temperature" => 0.7,
             "max_tokens" => $maxTokens,
             "top_p" => 1,
             "frequency_penalty" => 0,
             "presence_penalty" => 0,
         ]);

     if($response->failed()){
             return "Request Failed !";
     }
     else{
         charge_token($req->credit,$userId);

         $email_template = '<table width="100%" cellspacing="0" cellpadding="0" border="0" style="background:rgb(233, 234, 234);">
         <tbody>
            <tr>
               <td>
                  <div style="margin:0 auto;width:600px;padding:0px">
                     <p><br></p>
                     <table class="main" style="background-color: rgb(255, 255, 255); width: 600px; border-spacing: 0px; border-collapse: collapse;" data-mce-selected="1" width="100%" cellspacing="0" cellpadding="0" border="0" align="center">
                        <tbody>
                           <tr>
                              <td class="element-content" style="padding: 10px 50px; font-family: Arial; font-size: 13px; color: rgb(0, 0, 0); line-height: 22px; border-collapse: collapse;" align="left">Dear [Name],
                                 <p>'.$response['choices'][0]['text'].'</p>
                              </td>
                           </tr>
                        </tbody>
                     </table>
                  </div>
               </td>
            </tr>
      </table>';

         //$email_template = "<p>".$response['choices'][0]['text']."</p>";
      
         DB::table('dropfunnel_step_mails')->where('id',$req->id)->update([
            'subject' => $req->subject,
            'email_body' => $email_template
         ]);
         return $$email_template;
     }
   }

   function resetMasterData(){
      $req = request();
      $userId = CRUDBooster::myParentId();
      $product_old_info = DB::table('drm_products_master_data_backup')->where('product_id',$req->productId)->where('user_id',$userId)->first();
      if($product_old_info){
         $update_backup_table = DB::table('drm_products')->updateOrInsert(['id'=> $product_old_info->product_id],[
            'brand'=> $product_old_info->brand,
            'item_color'=> $product_old_info->item_color,
            'item_weight'=> $product_old_info->item_weight,
            'item_unit'=> $product_old_info->item_unit,
            'item_size'=> $product_old_info->item_size,
            'ek_price'=> $product_old_info->ek_price,
            'packaging_length'=> $product_old_info->packaging_length,
            'packaging_width'=> $product_old_info->packaging_width,
            'packaging_height'=> $product_old_info->packaging_height,

         ]);
         return [
            'status' => 'success',
            'success' => 'Reset Successfully',
            'message' => 'Master Data reset successfully !'
        ];
     }
     return [
      'status' => 'error',
      'error' => 'Reset Failed',
      'message' => 'Nothing to Reset !'
  ];
   }
}
