<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\MailGunWebHookHistory;
use DRM;
use Exception;

class MailTrackingController extends Controller
{
	public function open($slug)
	{
		$token = base64_decode($slug);
        $token = \DRM::stringEncryption($token, true);
        $payload = @json_decode($token, true) ?? [];
        if($payload) {
        	$this->insertPayload('opened', $payload);
        }

		$pixel = "\x47\x49\x46\x38\x39\x61\x1\x0\x1\x0\x80\x0\x0\xff\xff\xff\x0\x0\x0\x21\xf9\x4\x1\x0\x0\x0\x0\x2c\x0\x0\x0\x0\x1\x0\x1\x0\x0\x2\x2\x44\x1\x0\x3b";
		return response($pixel,200,[
		    'Content-Type' => 'image/gif',
		    'Content-Length' => strlen($pixel),
		]);
	}


	//mailgun log
    private function insertPayload(string $event, array $payload)
    {
        try {

        	$insert_data = [
                $event => now()->format('Y-m-d H:i:s'),
                'customer_email' => $payload['email'],
            ];

            $check_arr = [
                'user_id' => $payload['user_id'],
                'customer_id' => $payload['customer_id'],
                'campaign_id' => $payload['campaign_id'],
            ];

            if (!empty($payload['step_mail_id'])) { // for step mail
                $check_arr['step_id'] = $payload['step_mail_id'];
            }

            $insert_arr = array_filter($insert_data);

            if(MailGunWebHookHistory::where($check_arr)->exists()) return;
            MailGunWebHookHistory::create(array_merge($check_arr, $insert_arr));


            // if ($event == 'delivered' && !empty($payload['last_step'])) {
            //     $customerIds = DB::table('campaign_history')->where(['campaign_id' => $campaign_id, 'user_id' => $user_id])->pluck('customer_id')->toArray();
            //     if (!empty($customerIds)) {
            //         $campaign = DB::table('email_marketings')->select('stop_tag')->where(['id' => $campaign_id, 'user_id' => $user_id])->first();
            //         foreach ($customerIds as $customerId) {
            //             DropfunnelCustomerTag::insertTag($campaign->stop_tag, $user_id, $customerId, 12);
            //         }
            //     }
            // }

        } catch (Exception $e) {
            Log::channel('command')->info($e);
        }
    }

}