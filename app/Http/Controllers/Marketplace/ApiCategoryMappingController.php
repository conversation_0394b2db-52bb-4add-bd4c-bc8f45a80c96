<?php

namespace App\Http\Controllers\Marketplace;

use DB;
use App\DrmProduct;
use App\Models\DrmCategory;
use Illuminate\Http\Request;
use App\Models\Marketplace\Product;
use App\Services\DRMProductService;
use App\Http\Controllers\Controller;
use App\Jobs\Marketplace\MpCategorySync;
use App\Models\Marketplace\Category;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Marketplace\ApiCategory;
use Illuminate\Support\Facades\Session;
use App\Models\Export\ApiCategoryExport;
use App\Models\Import\ApiCategoryImport;
use App\Models\Marketplace\MarketplaceApiList;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Cache;

class ApiCategoryMappingController extends Controller
{
    public function mappingApiCategory($api_id){

        $data = [];
        $data['page_title'] = 'Marketplace Api Category Mapping';
        Session::put('mp_api_id', $api_id);
        $search = trim($_REQUEST['search_by_query']);
        $filterBy = trim($_REQUEST['filter_by']);
        $data['auto_sync_status'] = MarketplaceApiList::Where('api_id', $api_id)->first()->auto_sync;
        
        $limit = $_REQUEST['limit'] ?? 50;
        if($search){
            $data['api_categories'] = ApiCategory::where('api_id',$api_id)->where('api_category_name', 'like', '%' . $search . '%')->paginate($limit);
        }else if($filterBy == "in_complete"){
            $data['api_categories'] = ApiCategory::where('api_id',$api_id)->where('is_complete',0)->paginate($limit);
        }else{
            $data['api_categories'] = ApiCategory::where('api_id',$api_id)->paginate($limit);
        }
        $data['mp_categories'] = Category::get();
        return view('marketplace.api_category_mapping.index', $data);
    }

    public function allMarketplaceApi(){
        $data = [];
        $data['page_title'] = 'API Category';
        $search = trim($_REQUEST['search_by_query']);
        $limit = trim($_REQUEST['limit']) ?? 20;
        if($search){
            $data['all_mp_apis'] = MarketplaceApiList::Where('api_name', 'like', '%' . $search . '%')->paginate($limit);
        }else{
            $data['all_mp_apis'] = MarketplaceApiList::paginate( $limit);
        }
        return view('marketplace.all_api_list.index', $data);
    }

    public function marketplaceApiSave(Request $request){

       $request->validate([
            'apiname' => 'required',
       ]);
       $data = [
           'api_name'      => $request->apiname,
           'api_clientKey' => $request->apiclientKey,
           'api_secretKey' => $request->apisecretKey
       ];
       try{
          if($request->type == 'create'){
           $createResponse = MarketplaceApiList::create($data);
            MarketplaceApiList::where('id',$createResponse->id)->update(['api_id'=>$createResponse->id]);
            return redirect()->back()->with(['message_type' => 'success', 'message' => 'Api create sucessfull']);
          }else{
            MarketplaceApiList::where('id',$request->api_id)->update($data);
            return redirect()->back()->with(['message_type' => 'success', 'message' => 'Api update sucessfull']);
          }

         }catch(\Exception $e){
            return redirect()->back()->with(['message_type' => 'error', 'message' => $e->getMessage()]);
        }
    }

    public function editMarketplaceApi(Request $request){

       $marketplace_api =  MarketplaceApiList::where('id', $request->api_id)->first();
       return response()->json([
        'code'=>200,
        'data'=>$marketplace_api,
      ]);

    }

    public function importapicategory(){
        $data = [];
        $data['page_title'] = 'Import Marketplace Api Category';
        $data['all_mp_apis'] = MarketplaceApiList::get();
        return view('marketplace.api_category_mapping.import-api-category', $data);
    }

    public function exportApiCategory($api_id){

        return Excel::download(new ApiCategoryExport($api_id), 'Marketplace-Api-Category.xlsx');
    }

    public function excelApiCategorySave(Request $request){

        $request->validate([
            'file' => 'required|mimes:xlx,xls,xlsx',
            'mp_api_id'=>'required'
        ]);
        $mp_api_id = $request->mp_api_id;
        try{
           Excel::import(new ApiCategoryImport($mp_api_id),request()->file('file'));
           return redirect()->back()->with(['message_type' => 'success', 'message' => 'Api Category Import Sucessfull']);
        }catch(\Exception $e){
            return redirect()->back()->with(['message_type' => 'error', 'message' => $e->getMessage()]);
        }
    }

    public function categoryMappingSave(Request $request){

        $id = $request->id;
        $api_id = $request->api_id;
        $mp_category_id  = $request->mp_category_id ?? 0;
        $auto_sync_status = $request->auto_sync_status ?? 0;

        if($mp_category_id == 0){
            ApiCategory::where('id',$id)->where('api_id',$api_id)->update([
                'mp_category_id'=>$mp_category_id,
                'is_complete'=>0,
                'sync_status'=>0,
            ]);
            $success = false;
            $message = 'Unmapping category';
        }else{
            $api_category = ApiCategory::where('id',$id)->where('api_id',$api_id)->first();

            if($auto_sync_status == 1){

                $api_category->mp_category_id = $mp_category_id;
                $api_category->is_complete    = 1;
                $api_category->sync_status    = 2;
                $api_category->update();
                
                dispatch(new MpCategorySync($api_category->id,$api_category->api_id));

                $success = true;
                $message = 'Save mapping category. Category sync is started in the background';

            }else{
                // ApiCategory::where('id',$id)->where('api_id',$api_id)->update([
                //     'mp_category_id'=>$mp_category_id,
                //     'is_complete'=>1
                // ]);

                $api_category->mp_category_id = $mp_category_id;
                $api_category->is_complete    = 1;
                $api_category->update();

                $success = true;
                $message = 'Save mapping category';
            }
        }

      return response()->json([
        'code'=>200,
        'success'=>$success,
        'message'=>$message
      ]);

    }

    public function categoryMappingWithMpProduct(){
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $api_id = request()->api_id;
        $category_id = request()->category_id;

        $mapped_category = ApiCategory::where('id',$category_id)->where('api_id',$api_id)
                                        ->where('is_complete',1)
                                        ->select('id','api_id','api_category_id','mp_category_id')
                                        ->first();

        if($mapped_category){

            $local_products = Product::with('core_products')
                                            ->where('api_id' , $mapped_category->api_id)
                                            ->where('api_category_id',$mapped_category->api_category_id)
                                            ->where('category_id','!=',$mapped_category->mp_category_id)
                                            ->get();

            if(count($local_products) > 0){
                $mp_category = Category::find($mapped_category->mp_category_id);

                foreach($local_products as $local_product){

                    $drm_products = $local_product->core_products;

                   if(count($drm_products) > 0){
                        foreach($drm_products as $drm_product){

                            $drmCategory = DrmCategory::where('user_id',$drm_product->user_id)
                                                        ->where('category_name_de', $mp_category->name)
                                                        ->first();

                            if (!$drmCategory) {
                                $drmCategory = DrmCategory::create([
                                    'category_name_de' => $mp_category->name,
                                    'user_id'          => $drm_product->user_id,
                                    'country_id'       => 1,
                                ]);
                            }
                            $updateableColumns['category'] = [$drmCategory->id];

                            app(\App\Services\DRMProductService::class)->update($drm_product->id,$updateableColumns);
                        }
                   }

                    $local_product->category_id = $mapped_category->mp_category_id;
                    $local_product->update();
                }
            }
            return response()->json([
                'code'=>200,
                'success'=>true,
                'message'=>'Changed Product category by mapping category'
            ]);

        }else{
            return response()->json([
                'code'=>200,
                'success'=>true,
                'message'=>'We need to map the category first'
            ]);
        }

    }

     public function b2bCategoryMappingWithMP(){

        $mapped_categories = ApiCategory::where('api_id',2)
                                        ->where('is_complete',1)
                                        ->select('id','api_id','api_category_id','mp_category_id')
                                        ->get();

        $categoryies = Category::get();

        if(!isset($_GET['mapped']))
        dd($mapped_categories);

        if(count($mapped_categories) > 0){

            foreach($mapped_categories as $mapped_category){

                $local_products = Product::with('core_products')
                                            ->where('api_id' , $mapped_category->api_id)
                                            ->where('api_category_id',$mapped_category->api_category_id)
                                            ->where('category_id','!=',$mapped_category->mp_category_id)
                                            ->get();

                foreach($local_products as $local_product){

                    $drm_products = $local_product->core_products;

                    if(count($drm_products) > 0){

                        $mp_category = $categoryies->where('id',$mapped_category->mp_category_id)->first();

                        foreach($drm_products as $drm_product){

                            $drmCategory = DrmCategory::where('user_id',$drm_product->user_id)
                                                        ->where('category_name_de', $mp_category->name)
                                                        ->first();

                            if (!$drmCategory) {
                                $drmCategory = DrmCategory::create([
                                    'category_name_de' => $mp_category->name,
                                    'user_id'          => $drm_product->user_id,
                                    'country_id'       => 1,
                                ]);
                            }
                            $updateableColumns['category'] = [$drmCategory->id];

                            app(\App\Services\DRMProductService::class)->update($drm_product->id,$updateableColumns);
                        }
                    }

                    $local_product->category_id = $mapped_category->mp_category_id;
                    $local_product->update();
                }

            }

            return response()->json([
                'code'=>200,
                'success'=>true,
                'message'=>'Changed Product category by mapping category'
            ]);

        }else{
            return response()->json([
                'code'=>200,
                'success'=>true,
                'message'=>'We need to map the category first'
            ]);
        }

    }

    public function autoSyncEnableDisable(){
        $id = request()->id;
        $marketplace_api_list   = MarketplaceApiList::where('id',$id)->first();
        if($marketplace_api_list->auto_sync == 1){
            $marketplace_api_list->auto_sync = 0;
        }else{
            $marketplace_api_list->auto_sync = 1;
        }
        $marketplace_api_list->update();


        return response()->json([
            'code'=>200,
            'success'=>true,
            'status'=>$marketplace_api_list->auto_sync,
        ]);
    }

    public function categoryAutoMapping($id,$api_id){
        
        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);
        $count = 0;
        $mapped_category = ApiCategory::where('id',$id)->where('api_id',$api_id)
                                        ->where('is_complete',1)
                                        ->select('id','api_id','api_category_id','mp_category_id','sync_status')
                                        ->first();

        if(isset($mapped_category)){

            $local_products = Product::with('core_products')
                                            ->where('api_id' , $mapped_category->api_id)
                                            ->where('api_category_id',$mapped_category->api_category_id)
                                            ->where('category_id','!=',$mapped_category->mp_category_id)
                                            ->get();

            if(count($local_products) > 0){
                $mp_category = Category::find($mapped_category->mp_category_id);

                foreach($local_products as $local_product){

                    $drm_products = $local_product->core_products;

                   if(count($drm_products) > 0){
                        foreach($drm_products as $drm_product){

                            $update_status = json_decode($drm_product->update_status, 1);
                            if(isset($update_status['category']) && $update_status['category'] == 1){
                                
                                $drmCategory = DrmCategory::where('user_id',$drm_product->user_id)
                                                        ->where('category_name_de', $mp_category->name)
                                                        ->first();

                                if (!$drmCategory) {
                                    $drmCategory = DrmCategory::create([
                                        'category_name_de' => $mp_category->name,
                                        'user_id'          => $drm_product->user_id,
                                        'country_id'       => 1,
                                    ]);
                                }
                                $updateableColumns['category'] = [$drmCategory->id];

                                app(\App\Services\DRMProductService::class)->update($drm_product->id,$updateableColumns);

                            }
                        }
                   }

                    $local_product->category_id = $mapped_category->mp_category_id;
                    $local_product->update();

                    $count++;
                }
            }

            $mapped_category->sync_status = 1;
            $mapped_category->update();

            info('Mapped category count- '.$count);

        }else{
            return response()->json([
                'code'=>200,
                'success'=>true,
                'message'=>'We need to map the category first'
            ]);
        }

    }



    public function mpMenu()
    {
        return Cache::remember('mp_menu_html_cache_' . CRUDBooster::myId(), 1800, function () {
            return view('marketplace.partials.mp-menu')->render();
        });        
    }

}
