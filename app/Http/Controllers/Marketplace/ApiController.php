<?php

namespace App\Http\Controllers\Marketplace;

use App\Enums\Marketplace\Credentials;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\ApiCredential;
use App\Models\Marketplace\MarketplaceSyncedOrder;
use App\Services\Marketplace\Internel\ChangePasswordDataType;
use App\Services\Marketplace\Internel\GeneralDataType;
use App\Services\Marketplace\Internel\TestCredentialsDataType;
use App\Services\Marketplace\InternelAPISoapClient;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use App\Services\DRMProductService;
// use App\Services\Marketplace\InternelSyncService;
use App\User;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use SimpleXMLElement;
use App\DrmProduct;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use Illuminate\Support\Facades\DB;


class ApiController extends Controller
{
    private $api_address = Credentials::API_ADDRESS;
	private $authLogin;
	private $authPassword;
	private $version;

	private $cli;

    public function __construct(InternelAPISoapClient $cli)
    {
        $haveCredentials = ApiCredential::take(1)->first();

        $this->authLogin        = $haveCredentials->api_user_name;
        $this->authPassword     = $haveCredentials->api_password;
        $this->version          = Credentials::API_VERSION;

        $this->cli              = $cli;
    }

    public function apiInfo()
    {
        $credentials = ApiCredential::first();
        try {
            $expirationMessage = $this->testCredentials(new TestCredentialsDataType() );
        } catch (Exception $e) {
            $expirationMessage = '-';
        }
        return view('marketplace.api.apiInfo', compact('credentials', 'expirationMessage'));
    }

    public function postSetPassword(TestCredentialsDataType $credentials)
    {
        if ( !CRUDBooster::isSuperadmin() )
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'You are unauthorised !' , 'danger');

        $request = $_REQUEST;

        $credentials->authLogin     = $request['authLogin'];
        $credentials->authPassword  = $request['authPassword'];
        $credentials->version       = $this->version;

        $xml = new SimpleXMLElement( $this->cli->testCredentials($credentials) );

        if (isset($xml->Error)) {
//            echo "Error code: {$xml->Error->ErrorCode}\n";
            $errorMessage = "{$xml->Error->ErrorMsg}\n";
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $errorMessage , 'danger');
        } else {
            try{
                $cren = ApiCredential::take(1)->first();
                if (!$cren) {
                    $cren = new ApiCredential();
                }
                $cren->api_address = $this->api_address;
                $cren->api_user_name = $credentials->authLogin;
                $cren->api_password = $credentials->authPassword;
                $cren->histories = array_merge(ApiCredential::first()->histories ?? [], ['AuthLogin and AuthPassword setted']);

                $cren->save();
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Credentials Updated successfully !' , 'info');
            } catch (Exception $e) {
                \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $e , 'danger');
            }
        }
        \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong !' , 'danger');
    }


    public function postchangePasswordSubmit(ChangePasswordDataType $cdt)
    {

        if ( !CRUDBooster::isSuperadmin() )
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'You are unauthorised !' , 'danger');
        $request = $_REQUEST;

        $haveCredential = ApiCredential::take(1)->first();

        $cdt->authLogin = $haveCredential->api_user_name;
        $cdt->version = $this->version;
        $cdt->authPassword = $haveCredential->api_password;

        $cdt->newPassword = app(\App\Services\Marketplace\InternelSyncService::class)->generateNewpassword();

        Log::channel('uscreen')->info('Password trying to be changed to '.$cdt->newPassword);

        $response = $this->cli->changePassword($cdt);
        $xml      = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            $errorMessage = "{$xml->Error->ErrorMsg}\n";
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Erro: '.$errorMessage, 'danger');
        } else {
            $haveCredential->update([
                'api_password' => $request['new_password'],
            ]);
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Password changed successfully.' , 'info');
        }
    }

    public function saveSyncNotificationEmail()
    {
        $request = $_REQUEST;
        $updatedEmail = $request['sync_notification_email'];

        request()->validate([
            'sync_notification_email' => 'email'
        ]);


        try{
            ApiCredential::limit(1)->first()->update([
                'sync_notification_email' => $updatedEmail
            ]);
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Updated successfully.' , 'info');
        } catch (Exception $e) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Something went wrong.' , 'danger');
        }
    }

    public function testConnection ()
    {
        $response = $this->cli->testConnection('Farhad');
        var_dump($response);
    }

    public function testCredentials (TestCredentialsDataType $credentials)
    {
        $credentials->authLogin     = $this->authLogin;
        $credentials->authPassword  = $this->authPassword;
        $credentials->version       = $this->version;

        $xml = new SimpleXMLElement( $this->cli->testCredentials($credentials) );

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
            return ['message' => "{$xml->Success}."."Password will expire in {$xml->PasswordExpirationTime} days."];
        }
    }

    public function changePassword (ChangePasswordDataType $cpdt)
    {

        dd('I am stopped here.');

        $cpdt->authLogin    = $this->authLogin;
        $cpdt->authPassword = $this->authPassword;
        $cpdt->version      = $this->version;
        $cpdt->newPassword  = 'bl)+@OqOXsJi>20pMEhF';

        $response = $this->cli->changePassword($cpdt);
        $xml      = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
        }
    }

    public function transferIncomingDeliveries (GeneralDataType $gdt)
    {
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $newDelivery        = new SimpleXMLElement("<NewDelivery/>");
        $delivery           = $newDelivery->addChild("Delivery");
        $delivery->addChild('DeliveryID', '12341234');
        $delivery->addChild('DeliveryDate', '2017-07-26');

//        Not mendatory
        $vendor = $delivery->addChild('Vendor');
        $vendor->addChild('VendorID', '02-1234');

        $product = $delivery->addChild('Product');
        $product->addChild('ProductID', '64589317');
        $product->addChild('Quantity', '50');
        $product->addChild('ValueNet', '81.29');

        $gdt->xmlData = $newDelivery->asXML();

        $response           = $this->cli->newDelivery( $gdt );
        $xml                = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            foreach ($xml->Delivery as $delivery) {
                echo "Delivery ID: {$delivery->DeliveryID}\n";
                echo "TrackingNumber : {$delivery->Status}\n";
                if (isset($delivery->Error)) {
                    echo "Error code: {$delivery->Error->ErrorCode}\n";
                    echo "Error message: {$delivery->Error->ErrorMsg}\n";
                }
            }
        }
    }

    public function retriveIncomingDeliveryOrderStatus (GeneralDataType $gdt)
    {
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $deliveryStatus = new SimpleXMLElement('<DeliveryStatus/>');
        $deliveryStatus->addChild('StatusMode', 'COMPLETED');
        $delivery = $deliveryStatus->addChild('Delivery');
        $delivery->addChild('DeliveryID', '102');

        $gdt->xmlData = $deliveryStatus->asXML();
        $response     = $this->cli->deliveryStatus($gdt);
        $xml          = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}"."<br />";
            echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
            foreach ($xml->Delivery as $delivery) {
                echo "Delivery ID: {$delivery->DeliveryID}"."<br />";
                echo "Status : {$delivery->Status}"."<br />";
                echo "Date : {$delivery->Date}"."<br />";
                echo "<br />";

                if (isset($delivery->Error)) {
                    echo "Error code: {$delivery->Error->ErrorCode}"."<br />";
                    echo "Error message: {$delivery->Error->ErrorMsg}"."<br />";
                }
            }
        }
    }

    public function removeIncomingDeliveries (GeneralDataType $gdt)
    {
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $removeDelivery = new SimpleXMLElement('<RemoveDelivery/>');
        $delivery = $removeDelivery->addChild('Delivery');
        $delivery->addChild('DeliveryID', 'd-101');

        $gdt->xmlData = $removeDelivery->asXML();
        $response     = $this->cli->removeDelivery($gdt);
        $xml          = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error Message: {$xml->Error->ErrorMsg}\n";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            foreach ($xml->Delivery as $delivery) {
                echo "Delivery ID: {$delivery->DeliveryID}\n";
                echo "Status : {$delivery->Status}\n";
                if (isset($delivery->Error)) {
                    echo "Error code: {$delivery->Error->ErrorCode}\n";
                    echo "Error message: {$delivery->Error->ErrorMsg}\n";
                }
            }
        }
    }

    public function transferVendorData (GeneralDataType $gdt)
    {
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $supplier       = User::find(241);
        $vendorID       = $supplier->id;
        $vendorName     = $supplier->name;
        $country        = strtoupper( $supplier->billing_detail->country->country_shortcut );
        $city           = $supplier->billing_detail->city;
        $zipCode        = $supplier->billing_detail->zip;
        $street         = $supplier->billing_detail->address;
        $houseNumber    = '12';
        $email          = $supplier->email;

//        dd($vendorID, $vendorName, $country, $city, $zipCode, $street, $houseNumber, $email);

        $newVendor = new SimpleXMLElement ("<NewVendor/>");
        $vendor = $newVendor->addChild("Vendor");
        $vendor->addChild('VendorID','V000'.$vendorID);
        $vendor->addChild('VendorName',$vendorName);
        $vendor->addChild('Country','PL');
        $vendor->addChild('City',$city);
        $vendor->addChild('ZipCode',$zipCode);
        $vendor->addChild('Street',$street);
        $vendor->addChild('HouseNumber',$houseNumber);

        $gdt->xmlData = $newVendor->asXML();

        $response = $this->cli->newVendor($gdt);
        $xml = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            foreach ($xml->Vendor as $vendor) {
                if ( $vendor->Status = 'ADDED' ) {
                    $supplier->update([
                        'marketplace_vendor_id' => $vendor->VendorID,
                    ]);
                }
                echo "VendorID: {$vendor->VendorID}\n";
                echo "Status: {$vendor->Status}\n";
                if (isset($vendor->Error)) {
                    echo "Error code: {$vendor->Error->ErrorCode}\n";
                    echo "Error message: {$vendor->Error->ErrorMsg}\n";
                }
            }
        }
    }

    public function retriveVendorData ()
    {
        // app(InternelSyncService::class)->retriveVendorData();
    }

    public function updateVendorData (GeneralDataType $gdt)
    {
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $updateVendor = new SimpleXMLElement("<UpdateVendor/>");
        $vendor = $updateVendor->addChild('Vendor');
        $vendor->addChild('VendorID', "V01");
        $vendor->addChild("VendorName", "A big Company vendor");
        $vendor->addChild("Country", 'PL');
        $vendor->addChild("City", "Wrocław");
        $vendor->addChild("ZipCode", "80337");
        $vendor->addChild("Street", "Dworcowa");

        $gdt->xmlData = $updateVendor->asXML();

        $response = $this->cli->updateVendor($gdt);
        $xml = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}\n";
            echo "Error message: {$xml->Error->ErrorMsg}\n";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}\n";
            foreach ($xml->Vendor as $vendor) {
                echo "VendorID: {$vendor->VendorID}\n";
                echo "Status: {$vendor->Status}\n";
                if (isset($vendor->Error)) {
                    echo "Error code: {$vendor->Error->ErrorCode}\n";
                    echo "Error message: {$vendor->Error->ErrorMsg}\n";
                }
            }
        }
    }

    public function transferProductData (GeneralDataType $gdt)
    {
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $item = \App\Models\Marketplace\Product::where('id', 1)->first();

        $newProduct = new SimpleXMLElement("<NewProduct/>");

        $product    = $newProduct->addChild('Product');
        $product->addChild('ProductID', $item->id);
        $product->addChild('ProductName', $item->name);
        $product->addChild('ProductNumber', $item->item_number ?? 'NULL');
        $product->addChild('VendorID', "V00".$item->collection->supplier->id);
        $product->addChild('NetWeight', $item->item_weight ?? '1');
        $product->addChild('GrossWeight', '1');

//        $productDimentions = $product->addChild('ProductDimensions');
//        $productDimentions->addChild('Height', '30');
//        $productDimentions->addChild('Width', '150');
//        $productDimentions->addChild('Length', '120');

        $product->addChild('EAN', $item->ean);
        $product->addChild('OriginCountry', 'EU');
//        $product->addChild('HSCode', );

        $gdt->xmlData = $newProduct->asXML();

        $response = $this->cli->newProduct($gdt);
        $xml      = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}"."<br />";
            echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
            foreach ($xml->Product as $product) {
                if ( $product->Status == "ADDED" ) {
                    $item->update([
                        'marketplace_product_id' => $product->ProductID,
                    ]);
                }
                echo "Product Id : {$product->ProductID}"."<br />";
                echo "Status : {$product->Status}"."<br />";

                if (isset($product->Error)) {
                    echo "Error code: {$product->Error->ErrorCode}"."<br />";
                    echo "Error message: {$product->Error->ErrorMsg}"."<br />";
                }
            }
        }
    }

    public function retriveProductData (GeneralDataType $gdt)
    {
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $getProduct = new SimpleXMLElement("<GetProduct/>");
        $product    = $getProduct->addChild("Product");
        $product->addChild("ProductID", "162");

        $gdt->xmlData = $getProduct->asXML();
        $response     = $this->cli->getProduct($gdt);
        $xml          = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}"."<br />";
            echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
            foreach ($xml->Product as $product) {

                echo "Product Id : {$product->ProductID}"."<br />";
                echo "Name : {$product->ProductName}"."<br />";
                dd($product);

                if (isset($product->Error)) {
                    echo "Error code: {$product->Error->ErrorCode}"."<br />";
                    echo "Error message: {$product->Error->ErrorMsg}"."<br />";
                }
            }
        }


    }

    public function updateProductData (GeneralDataType $gdt)
    {
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $updateProduct = new SimpleXMLElement("<UpdateProduct/>");
        $product = $updateProduct->addChild("Product");
        $product->addChild("ProductID", "P0001");
        $product->addChild("ProductName", "Office Calculator edited");
//        $product->addChild("VendorID", "V01");
        $product->addChild("NetWeight", "140");
        $product->addChild("GrossWeight", "190");

//        $productDimentions = $product->addChild("ProductDimensions");
//        $productDimentions->addChild("Height", "28");
//        $productDimentions->addChild("Width", "148");
//        $productDimentions->addChild("Lenght", "118");

//        $product->addChild("OriginCountry", "LT");
        $gdt->xmlData   = $updateProduct->asXML();
        $response       = $this->cli->updateProduct($gdt);
        $xml            = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}"."<br />";
            echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
            foreach ($xml->Product as $product) {

                echo "Product Id : {$product->ProductID}"."<br />";
                echo "Status : {$product->Status}"."<br />";

                if (isset($product->Error)) {
                    echo "Error code: {$product->Error->ErrorCode}"."<br />";
                    echo "Error message: {$product->Error->ErrorMsg}"."<br />";
                }
            }
        }
    }

    public function retriveStockData (GeneralDataType $gdt)
    {
        $gdt->addCredentials([
            'authLogin'     => $this->authLogin,
            'authPassword'  => $this->authPassword,
            'version'       => $this->version,
        ]);

        $stockReport    = new SimpleXMLElement("<GetStockReport/>");
//        $product        = $stockReport->addChild("Product");
//        $product->addChild("ProductID");

        $gdt->xmlData   = $stockReport->asXML();
        $response       = $this->cli->getStockReport($gdt);
        $xml            = new SimpleXMLElement($response);

        if (isset($xml->Error)) {
            echo "Error code: {$xml->Error->ErrorCode}"."<br />";
            echo "Error message: {$xml->Error->ErrorMsg}"."<br />";
        } else {
            echo "Your password will expire in: {$xml->PasswordExpirationTime}"."<br />";
            foreach ($xml->Product as $product) {

                echo "Product Id : {$product->ProductID}"."<br />";
                echo "Quantity : {$product->ProductQuantity->AvailableFree}"."<br />";

                if (isset($product->Error)) {
                    echo "Error code: {$product->Error->ErrorCode}"."<br />";
                    echo "Error message: {$product->Error->ErrorMsg}"."<br />";
                }
            }
        }

    }

    // Delete Bigbuy Api Variantion Product
    public function deleteVariationProduct(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $url = 'rest/catalog/products';
        $variation_products = app(\App\Services\Marketplace\BigBuyApi\BigBuyApiService::class)->fetchData($url);
        foreach($variation_products as $product){
            if(!isset($product['ean13']) || empty($product['ean13'])){
                $local_product = Product::where('api_id', 4)
                                        ->where('api_product_id',$product['id'])->first();
                if($local_product && $local_product->id){

                    $drmProducts = DrmProduct::where('marketplace_product_id',$local_product->id)->get();
                    if($drmProducts && $drmProducts->count() > 0){
                        foreach($drmProducts as $drmProduct){
                            app(DRMProductService::class)->destroy($drmProduct->id,$drmProduct->user_id);
                        }
                    }
                    $local_product->delete();
                    Log::info('Variation product delete-'.$product['id']);
                }
            }
        }
        Log::info('All variation product delete successfully');
    }

    // Delete Bigbuy Api Product Which Is Not Available In API
    public function deleteProduct(){

        ini_set('max_execution_time', -1);
        ini_set('memory_limit', -1);

        $url = 'rest/catalog/products';
        $variation_products = app(\App\Services\Marketplace\BigBuyApi\BigBuyApiService::class)->fetchData($url);
        // $allApiProducts = collect(json_decode(Redis::get('bigbuy_product_veriation'), true));
        $apiProducts = collect($variation_products)->pluck('id')->toArray();

        $all_product = new Product();
        $local_products = $all_product->where('api_id', 4)
                                    ->select('api_product_id')->get();
        $local_api_category_id = $local_products->pluck('api_product_id')->toArray();
        foreach($local_api_category_id as $product){
            if(!in_array($product,$apiProducts)){
                $local_product = $all_product::where('api_id', 4)
                                        ->where('api_product_id',$product)->first();
                if($local_product && $local_product->id){
                    $drmProducts = DrmProduct::where('marketplace_product_id',$local_product->id)->get();
                    $frontend_product = MpCoreDrmTransferProduct::where('marketplace_product_id',$local_product->id)->all();
                    if($drmProducts && $drmProducts->count() > 0){
                        foreach($drmProducts as $drmProduct){
                            app(DRMProductService::class)->destroy($drmProduct->id,$drmProduct->user_id);
                        }
                    }
                    if($frontend_product){
                        $frontend_product->delete();
                    }
                    if($local_product){
                        $local_product->delete();
                    }
                    Log::info('Api remove product delete-'.$product);
                }
            }
        }
        Log::info('All api remove product delete successfully');
    }

}
