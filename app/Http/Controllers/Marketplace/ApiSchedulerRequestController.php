<?php

namespace App\Http\Controllers\Marketplace;

use App\Models\Marketplace\MarketplaceSyncedOrder;
use App\Http\Controllers\Controller;
use App\Services\Marketplace\InternelSyncService;
use Illuminate\Http\Request;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\DB;
use App\Enums\Marketplace\ProductStatus;

class ApiSchedulerRequestController extends Controller
{
    public $errorMsg;

    public function __construct () {

        if ( \App\Enums\Marketplace\Credentials::API_SECRET_KEY != end(request()->segments()) ) {
            $this->errorMsg = response()->json([
                'authentication' => 'FAILURE',
                'message'        => 'Invalid Secret key.'
            ]);
        }

    }

    public function scheduleOrderStatusUpdate(){

        if ( isset($this->errorMsg)  ) return $this->errorMsg;

        $responseArray = app(InternelSyncService::class)->scheduleSyncOrderStatus() ?? [];

        foreach($responseArray as $orderId => $orderStatusInfos)
        {
            $parcel_name     = $orderStatusInfos['ShipmentProvider'];
            $tracking_number = $orderStatusInfos['TrackingNumber'];

            $synced_order       = MarketplaceSyncedOrder::where('order_id',$orderId)->first();
            $order_email_status = $synced_order->email_status;

            $synced_order->status_history    = $orderStatusInfos;
            $synced_order->status            = $orderStatusInfos['Status'];
            $synced_order->shipment_provider = $orderStatusInfos['ShipmentProvider'];
            if(!empty($parcel_name) && !empty($tracking_number) && $order_email_status == 0){
                $synced_order->email_status  = 1;
            }
            $synced_order->update();

            // MarketplaceSyncedOrder::where('order_id', $orderId)
            // ->update([
            //     'status_history'    => $orderStatusInfos,
            //     'status'            => $orderStatusInfos['Status'],
            //     'shipment_provider' => $orderStatusInfos['ShipmentProvider'],
            // ]);

            /*=======================================================
            =================Shipment & tracking number==============
            ========================================================*/

            $orderId = substr($orderId, 3);
            if(!empty($parcel_name) && !empty($tracking_number) && $order_email_status == 0) {
                $orders = DB::table('new_orders')->whereNotNull('marketplace_order_ref')->where(function($q) use ($orderId) {
                    $q->where('id', $orderId)->orWhere('marketplace_order_ref', $orderId);
                })
                ->get()
                ->each(function($order) use ($parcel_name, $tracking_number) {
                    // app('\App\Http\Controllers\AdminDrmAllOrdersController')->saveOrderTrackingNumber($order->id, $order->cms_user_id, $parcel_name, $tracking_number);
                });
            }
        }

        return  $responseArray;
    }


    public function scheduleDeliveryStatusSync()
    {
        $response = app(InternelSyncService::class)->scheduleSyncDeliveryStatus();
    }

    public function updateProductStockInfo ()
    {
        if ( isset($this->errorMsg)  ) return $this->errorMsg;

        $syncedProductsIds = Product::whereNotNull('marketplace_product_id')->whereNull('deleted_at')
                            ->select('marketplace_product_id')->chunk(45, function ($productIdsChunk) {

            $productIdsArray = $productIdsChunk->pluck('marketplace_product_id')->toArray();
            $synced_products_stock = app(InternelSyncService::class)->retriveManyStockData($productIdsArray);

            foreach ($synced_products_stock as $productId => $stockAvailable) {
                // $p = Product::where('marketplace_product_id', $productId);
                $p = Product::where('marketplace_product_id',$productId)->first();

                if(isset($p)){
                    if($stockAvailable > 0 && $p->status != ProductStatus::ACTIVE){
                        $product_status = ProductStatus::GOODS_RECEIVED;
                    }else{
                        $product_status = $p->status;
                    }

                    $p->internel_stock  = $stockAvailable;
                    $p->status          = $product_status;
                    $p->update();

                }

                // Product::find($productId)->update([
                //     'internel_stock'    => $stockAvailable,
                // ]);
            }

        });

    }
}
