<?php

namespace App\Http\Controllers\Marketplace;

use App\DrmProduct;
use App\Http\Controllers\Controller;
use App\Models\DrmCategory;
use App\Models\Marketplace\AutoTransferSubscription;
use App\Models\Marketplace\Category;
use App\User;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Country;
use App\Models\Marketplace\Product;
use App\DeliveryCompany;
use App\Services\Marketplace\AutoTransferService;
use \App\NewOrder;
use Log;
use Carbon\Carbon;
use App\Notifications\DRMNotification;
use App\Models\Marketplace\MpCoreDrmTransferProduct;


class AutoTransferController extends Controller
{
    public function postMakeSubscription ()
    {
        $attributes = [
            'user_id'                   => CRUDBooster::myParentId(),
            'category_id'               => request()->category_id,
            'transfered_products_limit' => \App\Enums\Marketplace\AutoTransfer::TRANSFER_PRODUCT_MAX_LIMIT,
            'transfered_products_count' => 0,
            'subscription_fee_per_month'=> \App\Enums\Marketplace\AutoTransfer::SUBSCRIPTION_FEE_PER_MONTH,
            'billing_info'              => [],
            'history'                   => [],
            'subscription_activated_at' => \Carbon\carbon::now(),
            'subscription_closing_at'   => \Carbon\carbon::now()->addMonth(),
        ];

        $autoSubscription = AutoTransferSubscription::where(['user_id'=>CRUDBooster::myParentId(),'category_id'=>request()->category_id])->first();
        if(!$autoSubscription){
            $autoSubscription = AutoTransferSubscription::create($attributes);
        }

        app(AutoTransferService::class)->AutoTransferSync();

        \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Product Automatic Transfer Active Successfully.", 'info');

    }


    public function checkoutForm ()
    {
        $categoryId = request()->category_id ?? \Session::get('mp_auto_subscription_category_id_'.CRUDBooster::myParentId());
        $item = Category::findOrFail($categoryId);
        $user = User::find( CRUDBooster::myParentId() );
        $data['alreadyApplyFilter'] = false;
        $data['special_user'] = false; 
        $currentSubscription = $this->userCurrentSubscription($categoryId);

        $userPlan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user->id);
        if($currentSubscription || in_array($userPlan['import_plan_id'], is_dt_user() ? [31] :  [26, 27])){//has customer Professional ||  Tarrif
            $selectedBrands = $currentSubscription->brand ?? [] ;
            $selectedSkipBrands = $currentSubscription->skip_brand ?? [];
            $selectedShippingGroup = $currentSubscription->filter_shipping_group ?? []; 

            $date = \Carbon\Carbon::today()->subDays(30);
            $transferedProductsQuery = MpCoreDrmTransferProduct::whereIn('drm_product_id', array_values($currentSubscription->transfered_product_ids ?? []));
            // With where('created_at', '>=', $date)// last 30 days
            $totalTransferedProducts = clone $transferedProductsQuery;
            $transferedProducts = $transferedProductsQuery->where('created_at', '>=', $date)->count() ?? 0;
            $countTotalTransferedProducts = $totalTransferedProducts->count() ?? 0;
            $productQuery = Product::where('status', \App\Enums\Marketplace\ProductStatus::ACTIVE)->where('category_id', $categoryId);
            $brands = $this->getCategoryBaseBrands($productQuery);
            $shippingGroups = $this->getCategoryBaseShippingGroups($productQuery);
            $totalProducts = $productQuery->count();

            $data['filter_ek'] = $currentSubscription->filter_ek ?? [];
            $data['filter_vk'] = $currentSubscription->filter_uvp ?? [];
            $data['filter_profit'] = $currentSubscription->filter_profit ?? [];
            $data['filter_quantity'] = $currentSubscription->filter_quantity ?? []; 
            $data['update_at_brand'] = $currentSubscription->update_at_brand ? date('d-m-Y H:i', strtotime($currentSubscription->update_at_brand)) : '';
            $data['update_at_skip_brand'] = $currentSubscription->update_at_skip_brand ? date('d-m-Y H:i', strtotime($currentSubscription->update_at_skip_brand)) : '';
            $data['filter_shipping_cost'] = $currentSubscription->filter_shipping_cost ?? []; 
            $data['filter_delivery_day'] = $currentSubscription->filter_delivery_day ?? []; 

            if($data['filter_ek'] || $data['filter_uvp'] || $data['filter_profit'] || $data['filter_quantity'] || $data['filter_shipping_cost'] || $data['filter_delivery_day']){
                $data['alreadyApplyFilter'] = true; 
            }

            if(CRUDBooster::myParentId() === 3993 && !$currentSubscription){
                $selectedSkipBrands = ["791"];
                $data['filter_ek'] = ["40", null];
                $data['filter_vk'] = ["0", null];
                $data['filter_profit'] = ["0", null];
                $data['filter_quantity'] = ["5",null]; 
                $data['filter_shipping_cost'] = ["0", "7.99"]; 
                $data['filter_delivery_day'] = ["2", "4"]; 
                $data['special_user'] = true; 
            }

            return view('marketplace.partials.auto_transfer_subscrip_cancel_modal', compact(
                'data',
                'item',
                'transferedProducts',
                'countTotalTransferedProducts',
                'brands',
                'userPlan',
                'selectedBrands',
                'selectedSkipBrands',
                'selectedPriceRange',
                'totalProducts',
                'shippingGroups',
                'selectedShippingGroup',
            ));
        }else{
            //  $isTrial = app(\App\Services\Marketplace\CategoryService::class)->isTrialSubscribedForThisUser($categoryId);
            //  return view('marketplace.partials.auto_transfer_modal', compact('item', 'user', 'billing', 'userData', 'term', 'appDescription', 'isTrial'));
        }
    }

    //Purchase Marketplace auto transfer subscription
    public function purchasemarketplaceAutoTransfer($purchase_data)
    {
        //DB::beginTransaction();
        try {
            $intend_id = $purchase_data['id'];
            $plan_id = $purchase_data['plan_id'];
            $interval = $purchase_data['interval_type'];


            if (is_null($plan_id)) {
                throw new \Exception('You are not accessible to use this feature, Please Contact with DRM Customer Care!');
            } else {
                $discount = $purchase_data['discount'] ?? 0;
                $total = $purchase_data['total'] ?? 0;
                $sub_total = $purchase_data['sub_total'] ?? 0;

                if(NewOrder::where(['order_id_api' => $intend_id, 'cms_user_id' => 2455, 'shop_id' => 8])->exists()) throw new \Exception('Already purchased!');  //STRIPE_CLIENT_DT

                $item_name = 'marketplace Auto Transfer '.$interval.'ly subscription';
                $item_description = $intend_id;
                $subscription_id = $purchase_data['subscription_id'];


                //Increment single pay coupon usages
                if (isset($purchase_data['coupon']) && $purchase_data['coupon']) {
                    DB::table('coupons')->where('coupon_id', $purchase_data['coupon'])->increment('single_pay');
                }

                //cms client
                $cms_client = $purchase_data['user_id'];
                $user = User::with('billing_detail')->find($cms_client);
                if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

                //Initial purchase data
                $subscription_data = [
                    'identity' => $purchase_data['id'],
                    'purchase_data' => json_encode($purchase_data)
                ];

                //Initialize subscription data
                $subscription_data['subscription_id']       =   $purchase_data['subscription_id'];
                $subscription_data['start_date']            =   $purchase_data['period_start'];
                $subscription_data['end_date']              =   $purchase_data['period_end'];
                $subscription_data['plan_id']               =   $plan_id;
                $subscription_data['stripe_customer_id']    =   $purchase_data['stripe_customer_id']?? null;

                $subscription_data['created_at']            =   now();
                $subscription_data['updated_at']            =   now();
                $subscription_data['total']                 =   $total;
                $subscription_data['sub_total']             =   $sub_total;
                $subscription_data['discount']              =   $discount;
                $subscription_data['category_id']           =   \Session::get('mp_auto_subscription_category_id_'.$user->id);
                $subscription_data['transfered_products_limit']           =   500;
                $subscription_data['transfered_products_count']           =   0;
                $subscription_data['range_by']              =   null;
                $subscription_data['brand']                 =   null;
                $subscription_data['price_range']           =   null;

                //Status
                $subscription_data['status'] = 1;

                 //Unsubscribe old plan
                $old_subscription_id = \App\Models\Marketplace\AutoTransferSubscription::where('user_id', $cms_client)
                                        ->where('subscription_id', '!=', $subscription_id)
                                        ->where('category_id', '!=', $subscription_data['category_id'])
                                        ->value('subscription_id');
                                        
                if(!empty($old_subscription_id)){
                    resolve(\App\Services\Stripe\Latest\CancelSubscription::class)('stripe_key_2455', '', $old_subscription_id);
                }

                //Update old plan
                $old_subscription = \App\Models\Marketplace\AutoTransferSubscription::where('user_id', $cms_client)
                                        ->where('category_id', '=', $subscription_data['category_id'])
                                        ->first();
                if(($old_subscription)){
                    //Subscription update
                    $subscription_data['transfered_products_count'] = $old_subscription->transfered_products_count ?? $subscription_data['transfered_products_count'];  
                    $subscription_data['transfered_product_ids'] = $old_subscription->transfered_product_ids ?? [];
                    $subscription_data['created_at'] = $old_subscription->created_at ?? $subscription_data['created_at'];
                    $old_subscription->update($subscription_data);
                }else{
                    //Subscription create or insert
                    \App\Models\Marketplace\AutoTransferSubscription::updateOrCreate(['user_id' => $cms_client, 'subscription_id' => $purchase_data['subscription_id']], $subscription_data);
                }
                // Send Data to drm core
                // $this->transferProductsInstantly($user->id, $subscription_data['category_id']);

                $payment_intend_id = $purchase_data['intend_id'] ?? null;

                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($total * $taxShow) / 100;
                $order_info = [
                    'user_id' => 2455,  //STRIPE_CLIENT_DT
                    'cms_client' => $cms_client,
                    'order_date' => date('Y-m-d H:i:s'),
                    'total' => round(($total), 2),
                    'sub_total' => round($sub_total, 2),
                    'discount' => round($discount, 2),
                    'discount_type' => 'fixed',
                    'total_tax' => 0,
                    'payment_type' => 'Stripe',
                    'status' => "paid",
                    'currency' => "EUR",
                    'adjustment' => 0,
                    'insert_type' => \App\Enums\InsertType::MP_CATEGORY,
                    'shop_id' => 8,
                    'order_id_api' => $intend_id,
                    'intend_id' => $payment_intend_id,
                ];

                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = $item_name;
                $cart_item['description'] = $item_description;
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($sub_total, 2);
                $cart_item['tax'] = $taxShow;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($sub_total, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);
                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $cms_client);
            }

            //DB::commit();    // Commiting  ==> There is no problem whatsoever
            return ['success' => true, 'message' => 'Successfully purchase marketplace auto transfer subscription!', 'is_mp_category' => true];
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    public function transferProductsInstantly($userId, $categoryId)
    {
      
        $products = $this->getCountAllFilterProducts(request(),false);

        $productIds = $products->pluck('id')->toArray();

        if ($productIds) {
            $transferProductResponse = app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)
                ->transferAllFilteredProductsToDrm($productIds, [], $categoryId, $userId);
        } else {
            $transferProductResponse =
                [
                    'success' => false,
                    'message' => "Auto transfer filter added successfully, but no products found from this filter.",
                ];
        }
        return $transferProductResponse;
    }

    public function unsubscribeCategory($id)
    {
        $subscription = \App\Models\Marketplace\AutoTransferSubscription::where('category_id', $id)->where('user_id', \CRUDBooster::myParentId())->where('status', 1)->first();
        $unsubscribe = $subscription->update([
            'status' => 0,
        ]);
        if ($unsubscribe) {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Category unsubscribe successfully.", 'success');
        } else {
            \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Something went wrong.", 'error');
        }
    }

    public function convertRangeByOld ($rangeBy)
    {
      if ($rangeBy == 'ek_price') {
        return 'vk_price';
      } else if ($rangeBy == 'vk_price') {
        return 'uvp';
      } else if($rangeBy == 'profit'){
        return '(uvp - vk_price)/vk_price*100';
      }else if($rangeBy == 'stock') {
        return 'stock';
      } else {
        return $rangeBy;
      }
    }

    public function convertRangeBy($rangeBy)
    {
       $vk_price_with_markup = 'vk_price';
 
       if ($rangeBy === 'ek_price' || $rangeBy === 'profit') {
          try {
                $agreement = DB::table('mp_payment_agreements')
                   ->where('user_id', '=', CRUDBooster::myParentId())
                   ->where('type', '=', 1)
                   ->select('price_markup')
                   ->first();
 
                $price_markup = $agreement ? $agreement->price_markup : 0.0;
                $vk_price_with_markup = DB::raw('(vk_price + ((' . $price_markup . ' * vk_price) / 100))');
          } catch (\Exception $e) {
 
          }
       }
 
       switch ($rangeBy) {
          case 'ek_price':
             return $vk_price_with_markup;
          case 'vk_price':
             return 'uvp';
          case 'profit':
             return DB::raw('(uvp - ' . $vk_price_with_markup . ')/' . $vk_price_with_markup . '*100');
          case 'stock':
             return 'stock';
          default:
             return $rangeBy;
       }
    }

    public function productAutoTransferFilterAddOrAll ()
    {
        try{
            
            $categoryId = request()->category_id;
            $userId = CRUDBooster::myParentId();
            $autoTransferSubscription = AutoTransferSubscription::where(['user_id'=>$userId,'category_id'=>$categoryId])->first();

            $subscription_data['brand']        =   request()->filterSelectBrand ?? [];
            $subscription_data['skip_brand']   =   request()->filterSelectSkipBrand ?? [];
            $subscription_data['filter_ek'] = request()->ekPriceRange ?? [];
            $subscription_data['filter_uvp'] = request()->vkPriceRange ?? [];
            $subscription_data['filter_profit'] = request()->profitPriceRange ?? [];
            $subscription_data['filter_quantity'] = request()->quantityPriceRange ?? [];
            $subscription_data['filter_shipping_cost'] = request()->shippingPriceRange ?? [];
            $subscription_data['filter_delivery_day'] = request()->deliveryDaysRange ?? [];
            $subscription_data['filter_shipping_group'] = request()->filterShippingGroups ?? [];

            $userPlan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($userId);//check customer current tariff
            if(!$autoTransferSubscription && in_array($userPlan['import_plan_id'], is_dt_user() ? [31] :  [26, 27])){
                $subscription_data['user_id']    = $userId;
                $subscription_data['category_id'] = $categoryId;
                $subscription_data['status']     = 1;
                AutoTransferSubscription::create($subscription_data);
            }
            AutoTransferSubscription::where(['user_id'=>$userId,'category_id'=>$categoryId])->update($subscription_data);

            if($autoTransferSubscription->end_date > now() || in_array($userPlan['import_plan_id'], is_dt_user() ? [31] :  [26, 27])){
                $totalTransfer = $this->transferProductsInstantly($userId, $categoryId);
            }

            return $totalTransfer;

        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }

    }

    //Purchase Marketplace auto transfer subscription for 14 days trail
    public function trialMarketplaceProductsAutoTransfer()
    {

        $purchase_data = request()->all();
        //DB::beginTransaction();
        try {
            $plan_id = '000';
            if (is_null($plan_id)) {
                throw new \Exception('You are not accessible to use this feature, Please Contact with DRM Customer Care!');
            } else {
                //cms client
                $cms_client = CRUDBooster::myParentId();
                $user = User::with('billing_detail')->find($cms_client);
                if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

                //Initial purchase data
                $subscription_data = [
                    'identity' => $purchase_data['id'],
                    'purchase_data' => json_encode($purchase_data)
                ];
                $time = strtotime(date('Y-m-d h:i:s'));
                $trail_period = date("Y-m-d h:i:s", strtotime("+14 days", $time));
    
                //Initialize subscription data
                $subscription_data['subscription_id']       =   'free_trial_'.$cms_client;
                $subscription_data['start_date']            =   now();
                $subscription_data['end_date']              =   $trail_period;
                $subscription_data['plan_id']               =   $plan_id;
                $subscription_data['stripe_customer_id']    =   $purchase_data['stripe_customer_id']?? null;

                $subscription_data['created_at']            =   now();
                $subscription_data['updated_at']            =   now();
                $subscription_data['total']                 =   0;
                $subscription_data['sub_total']             =   0;
                $subscription_data['discount']              =   0;
                $subscription_data['category_id']           =   \Session::get('mp_auto_subscription_category_id_'.$user->id);
                $subscription_data['transfered_products_limit']           =   500;
                $subscription_data['transfered_products_count']           =   0;
                $subscription_data['range_by']              =   null;
                $subscription_data['brand']                 =   $purchase_data['filter_by'];
                $subscription_data['price_range']           =   $purchase_data['price_range'];

                //Status
                $subscription_data['status'] = 1;
                $subscription_data['is_trail_used'] = 1;


                //Update old plan
                $old_subscription = \App\Models\Marketplace\AutoTransferSubscription::where('user_id', $cms_client)
                                        ->where('category_id', '=', $subscription_data['category_id'])
                                        ->first();
                if(($old_subscription)){
                    //Subscription update
                    $subscription_data['transfered_products_count'] = $old_subscription->transfered_products_count ?? $subscription_data['transfered_products_count'];  
                    $subscription_data['transfered_product_ids'] = $old_subscription->transfered_product_ids ?? [];
                    $subscription_data['created_at'] = $old_subscription->created_at ?? $subscription_data['created_at'];
                    $old_subscription->update($subscription_data);
                }else{
                    //Subscription create or insert
                    \App\Models\Marketplace\AutoTransferSubscription::updateOrCreate(['user_id' => $cms_client, 'subscription_id' => $purchase_data['subscription_id']], $subscription_data);
                }                // Send Data to drm core
                // $this->transferProductsInstantly($user->id, $subscription_data['category_id']);
            }

            //DB::commit();    // Commiting  ==> There is no problem whatsoever
            return ['success' => true, 'message' => 'Successfully purchase marketplace auto transfer 14 days trial subscription!'];
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    // public function productsTransferCount(){

    //     $products = Product::where(['category_id' => request()->category_id, 'status' => \App\Enums\Marketplace\ProductStatus::ACTIVE])
        
    //     ->count();
    // }

    public function userCurrentSubscription($categoryId){
        $subscription = \App\Models\Marketplace\AutoTransferSubscription::where('category_id', $categoryId)
            ->where('user_id', \CRUDBooster::myParentId())
            ->where('status', 1)
            ->select(
                'transfered_product_ids',
                'brand',
                'skip_brand',
                'update_at_brand',
                'update_at_skip_brand',
                'filter_ek',
                'filter_uvp',
                'filter_profit',
                'filter_quantity',
                'filter_shipping_cost',
                'filter_delivery_day',
                'filter_shipping_group',
            )->first();
        if ($subscription) {
            return $subscription;
        } else {
            return false;
        }
    }

    public function getCategoryBaseBrands(object $products){
        $brands = $products->with('productBrand')
            ->select('marketplace_products.brand', 'marketplace_product_brand.brand_name')
            ->distinct('marketplace_product_brand.brand_name')
            ->leftJoin('marketplace_product_brand', 'marketplace_products.brand', '=', 'marketplace_product_brand.id')
            ->orderBy('marketplace_product_brand.brand_name')
            ->get();
        return $brands;
    }

    public function getAllFilterMaxRange(){

        $productQuery = Product::whereIn('status', [\App\Enums\Marketplace\ProductStatus::ACTIVE])
            ->where('category_id', request()->category_id)
            ->select(
                DB::raw('MAX(' . $this->convertRangeBy('ek_price') . ') as maximumEkPrice'),
                DB::raw('MAX(' . $this->convertRangeBy('vk_price') . ') as maximumVkPrice'),
                DB::raw('MAX(' . $this->convertRangeBy('profit') . ') as maximumProfit'),
                DB::raw('MAX(' . $this->convertRangeBy('stock') . ') as maximumStock'),
                DB::raw('MAX(' . $this->convertRangeBy('shipping_cost') . ') as maximumShippingCost'),
                DB::raw('MAX(CAST(delivery_days AS SIGNED)) as maximumDeliveryDays'),

            )->first();
        return [
            'ek_price' => intval(ceil($productQuery->maximumEkPrice)),
            'vk_price' => intval(ceil($productQuery->maximumVkPrice)),
            'profit'   => intval(ceil($productQuery->maximumProfit)),
            'stock'    => intval(ceil($productQuery->maximumStock)),
            'shipping_cost' => intval(ceil($productQuery->maximumShippingCost)),
            'delivery_days' => intval(ceil($productQuery->maximumDeliveryDays)),
        ];
    }

    public function getCountAllFilterProducts(Request $request,$isCountRequest = true){

        $category_id = $request->category_id;
        $productQuery = Product::whereIn('status', [\App\Enums\Marketplace\ProductStatus::ACTIVE])
                       ->where('category_id',$category_id);

        $priceRanges = [
            'ekPriceRange' => 'ek_price',
            'vkPriceRange' => 'vk_price',
            'quantityPriceRange' => 'stock',
            'shippingPriceRange' => 'shipping_cost',
            'deliveryDaysRange'  => 'delivery_days',
        ];

        foreach ($priceRanges as $requestParam => $column) {
            $range = $request->input($requestParam, []);
            if (isset($range[1]) && $range[1] > 0) {
                $rangeColumn = $this->convertRangeBy($column);
                $productQuery->whereBetween($rangeColumn, [intval($range[0]), intval($range[1])]);
            }
        }
        if ($request->has('profitPriceRange')) {
            $range = $request->input('profitPriceRange', []);
            if (isset($range[1]) && $range[1] > 0) {
                $minPrice = intval($range[0]);
                $maxPrice = intval($range[1]);
                $rangeColumn = $this->convertRangeBy('profit');
                $productQuery->whereRaw("$rangeColumn BETWEEN $minPrice AND $maxPrice");
            }
        }
        if(!empty($request->filterSelectBrand)){
            $productQuery->whereIn('brand', $request->filterSelectBrand);
        }
        if (!empty($request->filterSelectSkipBrand)) {
            $productQuery->whereNotIn('brand', $request->filterSelectSkipBrand);
        }

        if (!empty($request->filterShippingGroups)) {
            $productQuery->whereIn('delivery_company_id', $request->filterShippingGroups);
        }

        if($isCountRequest){
            return response()->json([
                'totalProducts' => $productQuery->count() ?? 0,
            ]);
        }else{
            return  $productQuery;
        }
    }

    private function getCategoryBaseShippingGroups(object $products)
    {
        $shippingGroupsColor = \App\Enums\Marketplace\ApiCategoryName::SHIPPING_GROUP_COLOR;
        $hasProductsShippingGroupIds =$products->select('delivery_company_id')
                ->distinct()
                ->pluck('delivery_company_id')
                ->reject(fn ($value) => $value === null)
                ->toArray();
            
        return array_intersect($shippingGroupsColor, array_flip(array_flip($hasProductsShippingGroupIds)));
    }

}
