<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Interfaces\BikeApiInterface;
use App\Jobs\Marketplace\BikeApiUpdatedProductsSync;
use App\Models\Marketplace\ApiProduct;
use App\Models\Marketplace\BikeApiCredential;
use App\Models\Marketplace\BikeApiProductSegment;
use App\Models\Marketplace\Collection;
use App\Models\Marketplace\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class BikeApiController extends Controller implements BikeApiInterface
{
    public $addresses;
    public $apiService;

    public function __construct ()
    {
        $this->apiService   = new \App\Services\Marketplace\BikeApi\BikeApiService();
        $this->addresses    = \App\Enums\Marketplace\ApiResources::ADDRESSES;
    }


    // START:: Products
    // START:: Products

    public function buildProductSyncJobs ()
    {
        $row = BikeApiCredential::first();
        $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'].'?page='.$row->next_import_page ?? 1;
        $response = $this->apiService->fetchData($apiAddress);

        try {
            if ( !array_key_exists('data',$response) || empty($response['data']) ) {
                return [
                    'status' => 'terminated',
                    'msg'    => 'Terminated ! No products found.',
                ];
            }

            $currentPage = $response['meta']['current_page'];
            $lastPage    = $response['meta']['last_page'];

            $jobsCount = 0;
            $tmpPageCount = 1;
            $pagesArr = [];

            for ( $page = $currentPage; $page <= $lastPage; $page++ ) {

                $pagesArr[] = $page;
                if(isLocal() && $ $jobsCount > 1) {
                    break;
                }

                if ( $tmpPageCount == 10 || ($page == $lastPage && $tmpPageCount < 10)) {
                    \App\Jobs\Marketplace\BikeApiProductSync::dispatch($pagesArr);
                    $tmpPageCount = 1;
                    $jobsCount++;
                } else {
                    $tmpPageCount++;
                }

                $row->update([
                    'next_import_page' => $page,
                ]);

                if(isLocal()) {
                    if ($page == $currentPage+100) break;
                }
            }
            $row->decrement('next_import_page',1);
            return [
                'created_jobs_count' => $jobsCount,
            ];
        } catch (\Exception $e){
            dd('Hello, Exception');
        }

    }

    public function buildChangesProductsSyncJobs ($days = 1)
    {
        $segments = BikeApiProductSegment::all();
        foreach ($segments as $segment) {
            $segmentCode = $segment->code;
            BikeApiUpdatedProductsSync::dispatch($segmentCode);
        }
    }

    public function getAllProducts ($pages)
    {
        $row = BikeApiCredential::first();
        $allProducts = [];
        try {
            foreach ($pages as $page) {
                try{
                    $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'] . '?page=' . $page;
                    $response = $this->apiService->fetchData($apiAddress);

                    if (!array_key_exists('data', $response) || empty($response['data'])) {
                        break;
                    } else {
                        $allProducts = array_merge($allProducts, $response['data']);
                        $this->apiService->ch = curl_init();
                    }
                } catch (\Exception $e) {
                    return $e->getMessage();
                }
            }
            $this->apiService->pushProductsFromApi($allProducts);
        } catch (\Exception $e) {
            return 'Got exception:: '.$e->getMessage();
        }
    }

    public function getProductById ($id)
    {
        $address =  $this->addresses['FETCH_PRODUCT_BY_ID'].$id;
        $response = $this->apiService->fetchData($address);
        dd($response);
    }

    public function productListArguments()
    {
        $address        = $this->addresses['LIST_ALL_SEGMENT'];
        $response       = $this->apiService->fetchData($address);

        $attributes = [];
        $allSegments    = $response['data'];
        foreach ( $allSegments as $segment ) {
            $attributes[] = [
                'code'      => $segment['code'],
                'name'      => $segment['name'],
                'status'    => 1,
            ];
        }
        BikeApiProductSegment::insert($attributes);
    }

    public function productsPerSegment($segment)
    {
        $address = $this->addresses['LIST_PRODUCTS_PER_SEGMENT'].$segment;
        $response = $this->apiService->fetchData($address);
        dd($response);
    }



    public function v2FetchChangedProductsForNDays($segmentCode, $days = 1)
    {
        $address = 'https://portal.internet-bikes.com/api/twm/v2/segment/'.$segmentCode.'/changes/'.$days;
        $response = $this->apiService->fetchData($address);    $this->apiService->ch = curl_init();
        $allProducts = [];
        try {
            if ( !array_key_exists('data',$response) || empty($response['data']) ) {
                return [
                    'status' => 'terminated',
                    'msg'    => 'Terminated ! No products found.',
                ];
            } else {
                $allProducts = array_merge($allProducts, $response['data']);
                if ( $response['meta']['last_page'] > 0 ) {
                    $startPage = $response['meta']['current_page']+1;
                    $lastPage  = $response['meta']['last_page'];
                    for ( $s = $startPage; $s <= $lastPage; $s++ ) {
                        $addr = $address.'?page='.$s;
                        $resp =  $this->apiService->fetchData($addr);
                        $allProducts = array_merge($allProducts, $resp['data']);

                        $this->apiService->ch = curl_init();
                    }
                    $insertInfo = $this->apiService->pushProductsFromApi($allProducts);
                }
                return $insertInfo;
            }
        } catch (\Exception $e) {
            dd($e->getMessage());
        }
    }


    // END:: Products
    // END:: Products

    // START:: Stock
    // START:: Stock
    public function fetchStockChangesInTheLastNMinutes($minutes = null)
    {
        $row = BikeApiCredential::first();
        $now =  \Carbon\Carbon::now()->toDateTimeString();
        $now =  \Carbon\Carbon::parse($now);

        $lastStockUpdatedAt = \Carbon\Carbon::parse($row->stock_updated_at ?? '2000-01-01');
        $minutes =  $now->diffInMinutes($lastStockUpdatedAt) + 1;

        $address = 'https://portal.internet-bikes.com/api/twm/products/changes/'.$minutes;

        $response = $this->apiService->fetchData($address);
        $updatedProductsCount = 0;
        $updatedProductsIds   = [];


        try {
            if ( count($response) > 0 ) {
                $collection = collect($response);
                foreach ( $collection->chunk(100) as $takeChunk ) {
                    foreach ( $takeChunk as $eachOfChunk ) {
                        $product = Product::where('api_product_id', $eachOfChunk['id'])->first();
                        if ( $product && ($product->stock != $eachOfChunk['stock']) ) {
                            $updatedAttributes = [
                                'stock'             => $eachOfChunk['stock'],
                                'old_stock'         => $product->stock,
                                'stock_updated_at'  => \Carbon\Carbon::now()
                            ];
                            $product->update($updatedAttributes);
                            ++$updatedProductsCount;
                            $updatedProductsIds[] = $product->id;
                        }
                    }
                }
            }
            $row->update([
                'stock_updated_at' => $now,
            ]);
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'msg'    => $e->getMessage(),
            ];
        }
        return [
            'updatedProductsCount' => $updatedProductsCount,
            'updated_products_ids' => $updatedProductsIds,
        ];
    }

    public function fetchStockPerProductById($id)
    {
        $address = 'https://portal.internet-bikes.com/api/twm/stock/'.$id;
        $response = $this->apiService->fetchData($address);
        dd($response);
    }
    // END:: Stock
    // END:: Stock




    // START:: Tracking codes
    // START:: Tracking codes
    public function listAllTrackingCodeForUser()
    {
        $address = \App\Enums\Marketplace\ApiResources::ADDRESSES['LIST_ALL_TRACKING_CODES_FOR_USER'];
        $response = $this->apiService->fetchData($address);
        dd($response);
    }

    public function fetchTrackingCodeById($id)
    {
        $address = 'https://portal.internet-bikes.com/api/twm/trackingcodes/'.$id;
        $response = $this->apiService->fetchData($address);
        dd($response);
    }
    // END:: Tracking Codes
    // END:: Tracking Codes



    // START:: Orders
    // START:: Orders
    public function listAllOrdersForUser()
    {
        $address = \App\Enums\Marketplace\ApiResources::ADDRESSES['LIST_ALL_ORDERS'];
        $response = $this->apiService->fetchData($address);
        dd($response);
    }

    public function fetchOrderById($id)
    {
        $address = 'https://portal.internet-bikes.com/api/twm/orders/'.$id;
        $response = $this->apiService->fetchData($address);
        dd($response);
    }

    public function submitAnOrder()
    {

    }

    public function fetchOrdersForlastNDays($days)
    {
        $address = 'https://portal.internet-bikes.com/api/twm/orders/latest/'.$days;
        $response = $this->apiService->fetchData($address);
        dd($response);
    }
    // END:: Orders
    // END:: Orders


    public function fallbackProductSync() {
        $row = BikeApiCredential::first();

        $apiAddress = $this->addresses['LIST_ALL_PRODUCTS'].'?per_page=700';
        $response = $this->apiService->fetchData($apiAddress);

        $results = $this->apiService->pushProductsFromApi($response['data'], 20);

        $row->update([
            'next_import_page' => $response['meta']['current_page']+1,
        ]);
    }
}
