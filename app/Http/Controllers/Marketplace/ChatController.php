<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\User;
use App\Notifications\DRMNotification;
use crocodicstudio\crudbooster\helpers\CRUDBooster;

class ChatController extends Controller
{
    public function __construct()
    {

    }

    public function getUserwiseProductsChatList(Request $request)
    {
        if ( \CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || \CRUDBooster::isCustomer() ){
            $chats = \App\Models\Marketplace\ChatList::where('product_id', $request->product_id)
                    ->where('customer_id', \CRUDBooster::myParentId())
                    ->with('product')
                    ->with('customer')
                    ->with('replies')
                    ->orderBy('id', 'desc')
                    ->get();
            return view('marketplace.product.chats.chat_histories_modal_part', compact('chats'));
        }
    }

    public function dashboardChatHistories(Request $request)
    {
        $data = [];

        $info = resolve(\App\Services\Support\WidgetData::class)->__invoke($request);
        $chats = $info['chats'];
        $user_ids = $info['user_ids'];
        
        $data['view'] = view('marketplace.product.chats.dashboard_chat_histories', compact('chats'))->render();
        $data['user_ids'] = $user_ids;

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }


    public function sendMessageToSupplier(Request $request)
    {

        $product = \App\Models\Marketplace\Product::find($request->product_id);
        $customer = \App\User::find($request->customer_id);
        $deliveryCompany = \App\DeliveryCompany::find($request->delivery_company_id);
        $chatListHtml = '';

        // // remove link (http)
        //    $chatContent = preg_replace('/(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|$!:,.;]*[A-Z0-9+&@#\/%=~_|$]/i', '[removed]', $request->chat_content);

        // // remove link (https)
        // $chatContent = preg_replace('/\b((https?|ftp|file):\/\/|www\.)[-A-Z0-9+&@#\/%?=~_|$!:,.;]*[A-Z0-9+&@#\/%=~_|$]/i', '[removed]', $chatContent);

        $chatContent = str_replace(['www.','https://','http://',' ftp://','file://'], ' ', $request->chat_content);

        // remove email
        $chatContent = preg_replace("/[^@\s]*@[^@\s]*\.[^@\s]*/", '[removed]',$chatContent);
        // remove phone number
        // $chatContent = preg_replace("[789][0-9]{9}", '[removed]',$chatContent);

        $supplier = \App\User::find($deliveryCompany->supplier_id);

        if(request()->text_bold == 'bold' && request()->text_italic == 'italic'){
            $chatContent = '<i>'.'<b>'.$chatContent.'</b>'.'</i>';
        }
        elseif(request()->text_bold == 'bold'){
            $chatContent = '<b>'.$chatContent.'</b>';
        }
        elseif(request()->text_italic == 'italic'){
            $chatContent = '<i>'.$chatContent.'</i>';
        }

        try {
            $chat = \App\Models\Marketplace\ChatList::create([
                'customer_id' => $customer->id,
                'product_id'  => $product->id,
                'delivery_company_id' => $deliveryCompany->id,
                'chat_content'  => $chatContent
            ]);

            $chats = \App\Models\Marketplace\ChatList::where('product_id', $product->id)
                    ->with('product')
                    ->with('customer')
                    ->with('replies')
                    ->orderBy('created_at', 'desc')
                    ->get();
            return view('marketplace.product.chats.chat_histories_modal_part', compact('chats'));
        } catch (\Exception $e) {
            dd($e);
        }
    }

    public function dashboardParticularProductChatDetails(Request $request)
    {
        $product = \App\Models\Marketplace\Product::find($request->product_id);
        $deliveryCompany = \App\DeliveryCompany::where('supplier_id', \CRUDBooster::myParentId())->first();

        $chats = \App\Models\Marketplace\ChatList::where('product_id', $product->id)
                ->orderBy('created_at', 'desc')
                ->get();
        return view('marketplace.product.chats.dashboard_particular_product_chat_modal', compact('chats'));


        if ( $deliveryCompany->id != $product->delivery_company_id  ) {
            return response()->json([
                'error' => 404,
                'message' => 'Product not found'
            ]);
        } else {
            $chats = \App\Models\Marketplace\ChatList::where('product_id', $product->id)
                    ->orderBy('created_at', 'desc')
                    ->get();
            return view('marketplace.product.chats.dashboard_particular_product_chat_modal', compact('chats'));

        }
    }

    public function sendReplayToMessage(Request $request)
    {

        $chats = \App\Models\Marketplace\ChatList::find($request->chat_id);

        $product = \App\Models\Marketplace\Product::find($request->product_id);
        $product_name = $product->name;

        $replierId = \CRUDBooster::myParentId();

        $replyContent = $request->reply_content;

        if(request()->text_bold == 'bold' && request()->text_italic == 'italic'){
            $replyContent = '<i>'.'<b>'.$replyContent.'</b>'.'</i>';
        }
        elseif(request()->text_bold == 'bold'){
            $replyContent = '<b>'.$replyContent.'</b>';
        }
        elseif(request()->text_italic == 'italic'){
            $replyContent = '<i>'.$replyContent.'</i>';
        }

        $reply = \App\Models\Marketplace\ChatReply::create([
            'replier_id' => $replierId,
            'chat_id'    => $request->chat_id,
            'reply_content' => $replyContent,
        ]);

        $message_title = __('Answer_received_from') .$product_name;
        User::find($chats->customer_id)->notify( new DRMNotification($message_title));

        if ( $reply ) {
            return response()->json([
                'success' => 'yes',
            ]);
        } else {
            return response()->json([
                'success' => 'no',
            ]);
        }
    }
}
