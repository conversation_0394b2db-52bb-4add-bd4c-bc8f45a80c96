<?php

namespace App\Http\Controllers\Marketplace;

use App\Enums\CollectionStatus;
use App\Enums\VisibilityStatus;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\UserGroup;
use App\Services\Marketplace\CategoryService;
use App\Services\Marketplace\CollectionService;
use App\Services\Marketplace\ProductService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CollectionController extends Controller
{
    private $collection;

    public function __construct(CollectionService $collection)
    {
        $this->collection = $collection;
    }

    public function index(Request $request)
    {
        $collections = $this->collection->all($request->all());
        $page_title = 'Collections';

        return view('marketplace.collection.index', compact('collections', 'page_title'));
    }

    public function create()
    {
        return view('marketplace.collection.create');
    }

    public function store(Request $request)
    {
        try {
            $productListingPrices = [];
            foreach ($request->product_listing_price as $userGroupId => $item) {
                foreach ($item as $subscription => $price) {
                    if(!empty(floatval($price))) {
                        $productListingPrices[] = [
                            'subscription_type' => $subscription,
                            'price' => floatval($price),
                            'user_group' => $userGroupId,
                        ];
                    }
                }
            }

            $collection = $this->collection->store($request->all());
            foreach ($request->collection_listing_price as $userGroupId => $item) {
                foreach ($item as $subscription => $price) {
                    if(!empty(floatval($price))) {
                        $collection->listing_prices()->create([
                            'subscription_type' => $subscription,
                            'price' => floatval($price),
                            'user_group' => $userGroupId,
                            'listable_id' => $collection->id,
                            'listable_type' => get_class($collection),
                        ]);
                    }
                }
            }

            app(ProductService::class)->saveCollectionProducts(
                array_merge(
                    $request->only('product_ids', 'product_prices'),
                    ['collection_id' => $collection->id, 'product_listing_price' => $productListingPrices]
                )
            );

            return redirect()->route('marketplace::collection.index')->with(['_status' => 'success', '_msg' => 'Successfully Created Collection']);
        } catch (\Exception $exception) {
            return redirect()->route('marketplace::collection.index')->with(['_status' => 'fails', '_msg' => 'Unable to create!']);
        }
    }

    public function edit($id)
    {
        $collection = $this->collection->getById($id);

        return view('marketplace.collection.edit', compact('collection'));
    }

    public function update($id, Request $request)
    {
        try {
            $image = $request->file('image');
            $this->collection->update($id, $request->all());

            return redirect()->back()->with(['_status' => 'success', '_msg' => 'Successfully Updated Collection']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to update!']);
        }
    }

    public function changeStatus($id)
    {
        try {
            $collection = $this->collection->getById($id);
            $event = $collection->status == CollectionStatus::ACTIVE ? 'Deactivated' : 'Activated';

            $this->collection->update($id, [
                'status' => $collection->status == CollectionStatus::ACTIVE ?
                    CollectionStatus::INACTIVE : CollectionStatus::ACTIVE
            ]);

            return redirect()->back()->with(['_status' => 'success', '_msg' => 'Successfully '. $event .' Collection']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to update!']);
        }
    }

    public function destroy($id)
    {
        try {
            $this->collection->destroy($id);

            return redirect()->back()->with(['_status' => 'success', '_msg' => 'Successfully Deleted Collection']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to Delete!']);
        }
    }

    public function preview(Request $request)
    {
        $this->validate($request, [
            'collections' => 'required'
        ]);

        $collections = $this->collection->all(
            ['include' => $request->get('collections')],
            false
        );

        $userGroups = UserGroup::select('id', 'name')->where('status', VisibilityStatus::ACTIVE)->get();

        $categories = app(CategoryService::class)->all();
        $page_title = 'Generate Collection';

        return view('marketplace.collection.preview', compact('collections', 'categories', 'userGroups', 'page_title'));
    }

    public function productDetail($id)
    {
        $collections = \App\Models\Marketplace\Collection::find($id);
        $products = app(ProductService::class)->all()->where('collection_id', $collections->id)->all();
        // $products = DB::table('marketplace_products')->where('collection_id', $collections->id)->all();
        return view('marketplace.collection.product-detail', compact('products', 'collections'));
    }

}
