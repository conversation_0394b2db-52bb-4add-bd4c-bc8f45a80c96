<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Models\Marketplace\ApiCredential;
use App\Models\Marketplace\Banner;
use App\Models\Marketplace\Category;
use App\Models\Marketplace\Collection;
use App\Models\Marketplace\Product;
use App\Models\MarketplaceProfitCalculation;
use Illuminate\Http\Request;

class DataTransferController extends Controller
{
    public function productDataTransfer()
    {
        $product = new Product;
        $product1 = $product->setConnection('mysql')->get();

        // $product1 = $product->setConnection('marketplace')->first();
        // dd($product1);

        $product1->map(function($items) use($product){

            $data['api_product_id']         = $items->api_product_id;
            $data['collection_id']          = $items->collection_id;
            $data['delivery_company_id']    = $items->delivery_company_id;
            $data['supplier_id']            = $items->supplier_id;
            $data['name']                   = $items->name;
            $data['ean']                    = $items->ean;
            $data['item_number']            = $items->item_number;
            $data['ek_price']               = $items->ek_price;
            $data['vk_price']               = $items->vk_price;
            $data['uvp_price']              = $items->uvp;
            $data['profit']                 = $items->profit_price ?? 0;
            $data['vat']                    = $items->vat;
            $data['shipping_method']        = $items->shipping_method;
            $data['shipping_cost']          = $items->shipping_cost;
            $data['stock_info'] = [
                'stock'                     => $items->stock,
                'old_stock'                 => $items->old_stock,
                'stock_updated_at'          => $items->stock_updated_at,
                'internel_stock'            => $items->internel_stock,
                'old_internel_stock'        => $items->old_internel_stock,
                'internel_stock_updated_at' => $items->internel_stock_updated_at,
            ];
            $data['calculation_id']         = $items->calculation_id;
            $data['description']            = $items->description;
            $data['images']                 = $items->image;
            $data['status']                 = $items->status;
            $data['product_misc'] = [
                'brand'          => $items->brand,
                'color'          => $items->item_color,
                'item_weight'    => $items->item_weight,
                'item_size'      => $items->item_size,
                'gender'         => $items->gender,
                'materials'      => $items->materials,
                'tags'           => $items->tags,
                'note'           => $items->note,
                'production_year'=> $items->production_year,
                'delivery_days'  => $items->delivery_days,
                'country_id'     => $items->country_id,
                'language_id'    => $items->language_id,
            ];
            $data['internel_id']            = $items->marketplace_product_id;
            $data['internel_api_response']  = $items->marketplace_api_response;
            $data['is_top']                 = $items->is_top_product;
            $data['is_top_datetime']        = $items->top_product_datetime;
            $data['misc']                   = null;

            $product->setConnection('marketplace')->updateOrCreate($data);
        });

        return "Product Data Transfer Success";

    }

    public function categoryDataTransfer()
    {
        $categories = new Category;
        $categories1 = $categories->setConnection('mysql')->get();

        $categories1->map(function($items) use($categories){
            $data['name'] = $items->name;
            $data['slug'] = $items->slug;
            $data['photo'] = $items->photo;
            $data['is_active'] = $items->is_active;
            $data['is_top'] = $items->is_top_category;
            $categories->setConnection('marketplace')->updateOrCreate($data);
        });

        return "Category Data Transfer Success";
    }
    public function collectionDataTransfer()
    {
        $collection = new Collection();
        $collection1 = $collection->setConnection('mysql')->get();
        $collection1->map(function($items) use ($collection){

            $data['name']                = $items->name;
            $data['supplier_id']         = $items->supplier_id;
            $data['delivery_company_id'] = $items->delivery_company_id;
            $data['source_type']         = $items->source_type;
            $data['source_url']          = $items->source_url;
            $data['csv_link']            = $items->csv_file_link;
            $data['updated_csv_link']    = $items->updated_csv;
            $data['misc']                = [];
            $data['status']              = $items->status;

            $collection->setConnection('marketplace')->updateOrCreate($data);
        });

        return "collection Data Transfer Success";
    }

    public function bannerDataTransfer()
    {
        $banners = new Banner();
        $banners1 = $banners->setConnection('mysql')->get();
        $banners1->map(function($items) use($banners){
            $data['image'] = $items->image;
            $data['status'] = $items->status;
            $banners->setConnection('marketplace')->updateOrCreate($data);
        });
        return "Banner Successfully transfer";
    }
    public function apiCredentialDataTransfer()
    {
        // api token null problem
        $apicredentials = new ApiCredential();
        $apicredentials1 = $apicredentials->setConnection('mysql')->get();
        $apicredentials1->map(function($items) use($apicredentials){
            $data['api_name'] = null;
            $data['api_address'] = $items->api_address;
            $data['api_username'] = $items->api_user_name;
            $data['api_password'] = $items->api_password;
            $data['api_token'] = null;
            $data['api_token_updated_at'] = null;
            $data['api_token_next_update_time'] = null;
            $data['api_last_sync'] = null;
            $data['misc'] = null;
            $data['status'] = $items->status;

            $apicredentials->setConnection('marketplace')->updateOrCreate($data);
        });
        return "Api Credential Successfully transfer";
    }

    public function profitCalculationDataTransfer()
    {
        $profitCalculation = new MarketplaceProfitCalculation();
        $profitCalculation1 = $profitCalculation->setConnection('mysql')->get();
        $profitCalculation1->map(function($items) use($profitCalculation){
            $data['name'] = $items->name;
            $data['user_id'] = $items->user_id;
            $data['shipping_cost'] = $items->shipping_cost;
            $data['profit_percent'] = $items->profit_percent;
            $data['additional_charge'] = $items->additional_charge;
            $data['uvp'] = $items->uvp;
            $data['round_scale'] = $items->round_scale;
            $data['dynamic_shipping_cost'] = $items->dynamic_shipping_cost;

            $profitCalculation->setConnection('marketplace')->updateOrCreate($data);
        });
        return "Marketplace Profit Calculation Successfully transfer";
    }

}
