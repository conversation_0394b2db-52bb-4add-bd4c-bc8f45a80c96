<?php

namespace App\Http\Controllers\Marketplace;

use App\Enums\Marketplace\EmailSetting;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\ApiCredential;
use App\Models\Marketplace\EmailCredential;
use App\Models\Marketplace\ProductSyncedHistory;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\Request;

class EmailController extends Controller
{
    public function getSendProductSyncedEmail ($historyId)
    {
        $userSetting = EmailCredential::where('user_id', CRUDBooster::myParentId())->first();
        $defaultSetting = EmailSetting::SETTING;
        return view('marketplace.synced_mail_form', compact('historyId', 'userSetting', 'defaultSetting'));
    }

    public function postSendProductSyncedEmail (Request $request)
    {
        $data = [];
        $history = ProductSyncedHistory::find($request->history_id);
        $emailInfos = EmailCredential::find( CRUDBooster::myParentId() );

        $emailTo = ApiCredential::limit(1)->first()->sync_notification_email;

        if ( $emailInfos ) {
            $data['email_from'] = $emailInfos->email_from;
            $data['email_to']   = $emailTo;
            $data['subject']    = $emailInfos->email_subject;
        } else {
            $data = [];
            $data['email_from'] = EmailSetting::SETTING['EMAIL_FROM'];
            $data['email_to']   = EmailSetting::SETTING['EMAIL_TO'];
            $data['subject']    = EmailSetting::SETTING['EMAIL_SUBJECT'];
        }

        $pdf_view = 'marketplace.product.export_pdf';
        $pdfStream = \PDF::loadView($pdf_view, ['products'=>$history->products()])->setWarnings(false)->stream();
//            Mail Send to . . .

        app('drm.mailer')->getMailer(CRUDBooster::myParentId(),$data['email_from'])->send('marketplace.mail_template.product_export',$data, function ($messages) use ($data, $pdfStream) {
            // $messages->from($data['email_from']);
            $messages->to($data['email_to']);
            $messages->subject($data['subject']);
            $messages->attachData($pdfStream, 'products.pdf', [
                'mime' => 'application/pdf',
            ]);
        });

        $redirectUrl = CRUDBooster::adminPath('marketplace_products');
        \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect($redirectUrl, "Product Synced and email sent. Thank you.", 'info');
    }
}
