<?php

namespace App\Http\Controllers\Marketplace;

use PDF;
use App\User;
use Exception;
use App\Country;
use App\NewOrder;
use Carbon\Carbon;
use App\DrmProduct;
use App\Enums\Apps;
use App\NewCustomer;
use App\BillingDetail;
use App\DeliveryCompany;
use App\Mail\DRMSEndMail;
use App\Models\DrmCategory;
use Illuminate\Support\Str;
// use App\Enums\CollectionStatus;
use App\Traits\ProjectShare;
use Illuminate\Http\Request;
use App\DropmatixProductBrand;
use App\Enums\VisibilityStatus;
//  use App\Services\Marketplace\InternelSyncService;
use App\Enums\Marketplace\Status;
use App\Models\Marketplace\Banner;
// use App\Services\Otto\Api\Products;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use Illuminate\Support\Facades\Log;
// use App\Models\Marketplace\Collection;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\Category;
use Illuminate\Support\Facades\Mail;
// use App\Services\Marketplace\OrderService;
use Illuminate\Support\Facades\Cache;
use App\Models\Marketplace\UserAccess;
use App\Models\Marketplace\ApiCategory;
use Illuminate\Support\Facades\Session;
use App\Enums\Marketplace\FakeSuppliers;
use App\Enums\Marketplace\ProductStatus;
use App\Jobs\ChannelManager\AutoTransfer;
use Illuminate\Support\Facades\Validator;
use App\Services\Marketplace\ProductService;
use App\Services\ProductApi\TransferProduct;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Models\Marketplace\AutoTransferSubscription;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Jobs\Marketplace\BulkMarketplaceProductDelete;
use App\Models\Marketplace\MarketplaceProductAnalysis;
use App\Models\Marketplace\MarketplaceProductExportFeed;
use App\Services\Marketplace\MarketplaceAnalysisService;
use App\Jobs\Marketplace\MarketplaceProductTransferToDrm;
use App\Jobs\Marketplace\MarketplaceProductTransferBasedOnDrmEan;
use App\Jobs\DestroyProduct;
use App\Jobs\Marketplace\CustomerBulkParentCategoryRemove;
use App\Jobs\MpTransferableIdStoreJob;
use App\MarketplaceProducts;
use App\TransferMarketplaceToDrm;

class MarketPlaceController extends Controller
{
    use ProjectShare;
    private $productService;
    private $marketplaceAnalysisService;

    public function __construct(ProductService $productService,MarketplaceAnalysisService $marketplaceAnalysisService )
    {
        $this->productService = $productService;
        $this->marketplaceAnalysisService = $marketplaceAnalysisService;
    }

    public function setSession(){
        $key = request()->key ?? '';
        $value = request()->value ?? '';
        $time = request()->time ?? 60;
        $type = request()->type ?? 'set';
        $type == 'set' ? session()->put($key,$value,$time) : session()->forget($key);
    }

    public function index()
    {
        $data = [];
        $data['page_title'] = 'DRM Marketplace';
        $data['all_categories'] = Category::where('parent_id', null)->get();
        $data['top_categories'] = Category::where(['is_active' => '1', 'is_top_category' => 1])->limit(8)->get();
        $data['top_products'] = Product::where(['status' => '1', 'is_top_product' => 1])->limit(10)->get();
        $data['banners'] = \App\Models\Marketplace\Banner::where('status', VisibilityStatus::ACTIVE)->get();

        return view('marketplace.index', $data);
    }

    public function productDetails($catId = null)
    {
        $data = [];
        $data['is_top_category'] = false;
        //      make decission is it al products view or not
        $data['allProductsView'] = ($catId == null || $catId == 0);

        // Marketplace Agreement
        if ( \CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || \CRUDBooster::isSupplier() ) {
            $data['MPAgreement']    = true;
        } else {
            // For Customer user which has marketplace access
            $data['MPAgreement']    = \App\User::find(\CRUDBooster::myParentId())->mp_terms_conditions_confirmation_time;
        }


        if ( $data['allProductsView'] ) {
            $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myParentId())->first();
            $accessAbleCategories = array_map('intval', $accessRow->accessable_categories??[]);

            // $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', CRUDBooster::myParentId())
            // ->select('accessable_categories')
            // ->get()->toArray();

            // $accessAbleCategories = array_column($accessRow[0]['accessable_categories'], 'accessable_categories') ?? [];
            // if(empty($accessAbleCategories)){
            //     $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', CRUDBooster::myParentId())->first();
            //     $accessAbleCategories = array_map('intval', $accessRow->accessable_categories??[]);
            // }
            $data['banners']        = Banner::where('status', Status::ACTIVE)
                                    ->orderBy('id', 'desc')
                                    ->get();

            $data['topProducts']    = app(\App\Services\Marketplace\ProductService::class)->fetchTopProducts();

            $data['topCategories']  = Category::with(['products' => function($query) {
                $query->where(['status' => ProductStatus::ACTIVE])->whereNotIn('supplier_id', FakeSuppliers::FAKE_IDS);
            }])->where('is_active', Status::ACTIVE);

            if ( !empty($accessAbleCategories) && \CRUDBooster::myPrivilegeName() == 'Customer' ) {
                $data['topCategories'] = $data['topCategories']->whereIn('id', $accessAbleCategories);
            }
            $data['topCategories'] = $data['topCategories']->get();

            if(count($data['topCategories']) > 0) {
                foreach($data['topCategories'] as $category) {
                    if(count($category->products) > 0) {
                        $data['is_top_category'] = true;
                        break;
                    }
                }
            }

            $data['newProducts']    = collect();
            $newProductDate = Carbon::now()->subDays(90);
            // $lastCollection = Collection::where(['status' => CollectionStatus::ACTIVE])->orderBy('id','desc')->first();

            if ( CRUDBooster::myPrivilegeName() == 'Customer' ) {
                $data['newProducts'] = Product::where(['status' => ProductStatus::ACTIVE])//where('created_at', '>=', $newProductDate)
                ->whereNotIn('supplier_id', FakeSuppliers::FAKE_IDS);

                if ( !empty($accessAbleCategories) ) {
                    $data['newProducts'] = $data['newProducts']->whereIn('category_id', $accessAbleCategories);
                }
                $data['newProducts'] = $data['newProducts']->orderBy('id', 'desc')
                    ->limit(100)
                    ->get();
            } else {
                $data['newProducts'] = Product::where(['status' => ProductStatus::ACTIVE])->orderBy('id', 'desc')->limit(100)->get();
            }

        } else {
            $data['base_category'] = Category::find($catId);

            $productQuery = Product::where('status', \App\Enums\Marketplace\ProductStatus::ACTIVE);

            if ($catId) {
                $productQuery->where('category_id', $catId);
            }

            $p = Product::where('status', \App\Enums\Marketplace\ProductStatus::ACTIVE)
                    ->where('category_id', $catId)
                    ->select('id')
                    ->pluck('id')
                    ->toArray();
            $uId = \CRUDBooster::myParentId();
            $totalTransferred = 0;

            $chunks = array_chunk($p, 5000);
            foreach ($chunks as $chunk) {
                $transferred = \App\DrmProduct::where('user_id', $uId)
                   ->whereIntegerInRaw('marketplace_product_id', $chunk)
                   ->selectRaw("count(id) as transferredCount")
                   ->first()
                   ->transferredCount;
                $totalTransferred += $transferred;
            }
            $data['totalTransferred'] = $totalTransferred;

            $rangeBy = Session::get('mp_store_range_by'.\CRUDBooster::myParentId()) ?? 'ek_price';
            $rangeBy = $this->convertRangeBy($rangeBy);

            $countingAttributes = $productQuery->selectRaw("MIN($rangeBy) as minimumPrice, MAX($rangeBy) as maximumPrice, count(id) as totalProducts")->first();

            $data['totalCount'] = $countingAttributes->totalProducts;
            $data['minimumPrice'] = intval( floor( (float)$countingAttributes->minimumPrice ) );
            $data['maximumPrice'] = intval( ceil( (float)$countingAttributes->maximumPrice ) );

            //For find out category wise best seller app....
            $appsId=\App\TrendCategories::select('drm_app_id')->where('marketplace_cat_id', $catId)->first();

            if($appsId && $appsId->drm_app_id){
                // $catApps=\App\AppStore::select('app_stores.menu_name')
                //     ->where('app_stores.id', $appsId->drm_app_id)
                //     ->first();
                // $data['appName'] = $catApps->menu_name;
                $data['appsId'] = $appsId->drm_app_id;

                $results = DB::table('purchase_apps')
                    ->where('purchase_apps.type', '!=', 'Free Trail')
                    ->where('purchase_apps.cms_user_id', CRUDBooster::myParentId())
                    ->where('purchase_apps.app_id', $appsId->drm_app_id)
                    ->where(function ($query) {
                        $query->where('purchase_apps.subscription_date_end', '>', date('Y-m-d'))
                            ->orwhere('purchase_apps.subscription_life_time', 1);
                    })
                    ->get();

                if( $results && $results[0] ){
                    $data['isPurchase'] = true;
                } else
                    $data['isPurchase'] = false;
            }

        }

        return view('marketplace.product_details', $data);
    }

    public function productsByCategory(Request $request)
    {
        $price_range = $request->price_range;
        $errorMsg = '';
        $catIds = $request->query('cat_ids', []);
        $usersAccessAbleCategories = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myParentId())->first()->accessable_categories;

        // $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', CRUDBooster::myParentId())
        //     ->select('accessable_categories')
        //     ->get()->toArray();

        // $usersAccessAbleCategories = array_column($accessRow[0]['accessable_categories'], 'accessable_categories') ?? [];
        // if(empty($usersAccessAbleCategories)){
        //     $usersAccessAbleCategories = \App\Models\Marketplace\UserAccess::where('user_id', CRUDBooster::myParentId())->first()->accessable_categories ?? [];
        // }

        if (\CRUDBooster::isCustomer() && \CRUDBooster::myParentId() != 2455) {
            $result = array_intersect($catIds, $usersAccessAbleCategories ?? []);
            $catIds = $result;

            if (!count($catIds)) {
                $errorMsg = "Sorry, You don't have access to this page.";
            }
        }
        $products = collect([]);
        if (!empty($catIds)) {
            if ($request->filter_by) {
                Session::put('mp_store_filter'.\CRUDBooster::myParentId(), $request->filter_by);
            } else {
                Session::forget('mp_store_filter'.\CRUDBooster::myParentId());
            }

            $rangeColumn = $this->convertRangeBy($request->range_by);
            $results = $this->productService->productsFilteringConditionApply([
                'status' => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                'q' => $request->query('q', ''),
                'category_ids' => in_array(0, $catIds) ? [] : $catIds,
                'filter_by' => $request->filter_by,
                'filter_by_input' => $request->filter_by_input ?? '',
                'filter_by_select' => $request->filter_by_select ?? '',
                'price_range' => $request->price_range,
                'range_by' => $rangeColumn,
            ], true);

            $products = $results['paginated_collection'];
            $allProducts = $results['allProducts'];
            $collection = \App\Models\Marketplace\Product::whereIn('category_id', in_array(0, $catIds) ? [] : $catIds)->where('status', \App\Enums\Marketplace\ProductStatus::ACTIVE)->select("id", "ek_price", "vk_price", "uvp");

            $attributes = $collection->selectRaw("id, MIN($rangeColumn) AS min_price, MAX($rangeColumn) AS max_price, count(id) AS totalCount")->first();
            $totalCount = $attributes->totalCount;
            $minimumPrice = intval( floor( (float)$attributes->min_price ) );
            $maximumPrice = intval( ceil( (float)$attributes->max_price ) );
            // $collection = \App\Models\Marketplace\Product::whereIn('category_id', in_array(0, $catIds) ? [] : $catIds)
            //                 ->where('status', \App\Enums\Marketplace\ProductStatus::ACTIVE);
            // $totalCount = $collection->count();
            $onlyAvailableCount = $collection->onlyAvailable()->count();
            $soldOutCount = $collection->soldOut()->count();
            $importProduct = app(\App\Http\Controllers\AdminDrmImportsController::class)->importProductCheck(\CRUDBooster::myParentId());
            $transferPlan = $importProduct['plan'] ? $importProduct['plan'] : null;
            $transferPlanLimit = $importProduct['limit'] ? $importProduct['limit']  : null;
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;
            $transferedProducts = $importProduct ? $importProduct['total_product'] : 0;
        }

        return view('marketplace.product_list', compact(
                                                'products',
                                                'errorMsg',
                                                'onlyAvailableCount',
                                                'soldOutCount',
                                                'totalCount',
                                                'minimumPrice',
                                                'maximumPrice',
                                                'price_range',
                                                'allProducts',
                                                'transferLimit',
                                                'transferedProducts',
                                                'transferPlanLimit',
                                                'transferPlan'
                                                ));
    }


    // New Function created for Product page optimized

    public function productDetailsNew($catId = null)
    {
        $catId = ($catId == null || $catId == 0) ? 'all' : $catId;

        $data = [];


        $data['is_top_category'] = false;
        //      make decission is it al products view or not
        // $data['allProductsView'] = ($catId == null || $catId == 0);

        // Marketplace Agreement
        if ( \CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || \CRUDBooster::isSupplier() ) {
            $data['MPAgreement']    = true;
        } else {
            // For Customer user which has marketplace access
            $data['MPAgreement']    = \App\User::find(\CRUDBooster::myParentId())->mp_terms_conditions_confirmation_time;
        }

        /*
        if ( $data['allProductsView'] ) {
            $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myParentId())->first();
            $accessAbleCategories = array_map('intval', $accessRow->accessable_categories??[]);

            $data['banners']        = Banner::where('status', Status::ACTIVE)
                                    ->orderBy('id', 'desc')
                                    ->get();

            $data['topProducts']    = app(\App\Services\Marketplace\ProductService::class)->fetchTopProducts();

            $data['topCategories']  = Category::with(['products' => function($query) {
                $query->where(['status' => ProductStatus::ACTIVE])->whereNotIn('supplier_id', FakeSuppliers::FAKE_IDS);
            }])->where('is_active', Status::ACTIVE);

            if ( !empty($accessAbleCategories) && \CRUDBooster::myPrivilegeName() == 'Customer' ) {
                $data['topCategories'] = $data['topCategories']->whereIn('id', $accessAbleCategories);
            }
            $data['topCategories'] = $data['topCategories']->get();

            if(count($data['topCategories']) > 0) {
                foreach($data['topCategories'] as $category) {
                    if(count($category->products) > 0) {
                        $data['is_top_category'] = true;
                        break;
                    }
                }
            }

            $data['newProducts']    = collect();
            $newProductDate = Carbon::now()->subDays(90);
            // $lastCollection = Collection::where(['status' => CollectionStatus::ACTIVE])->orderBy('id','desc')->first();

            if ( CRUDBooster::myPrivilegeName() == 'Customer' ) {
                $data['newProducts'] = Product::where(['status' => ProductStatus::ACTIVE])//where('created_at', '>=', $newProductDate)
                ->whereNotIn('supplier_id', FakeSuppliers::FAKE_IDS);

                if ( !empty($accessAbleCategories) ) {
                    $data['newProducts'] = $data['newProducts']->whereIn('category_id', $accessAbleCategories);
                }
                $data['newProducts'] = $data['newProducts']->orderBy('id', 'desc')
                    ->limit(100)
                    ->get();
            } else {
                $data['newProducts'] = Product::where(['status' => ProductStatus::ACTIVE])->orderBy('id', 'desc')->limit(100)->get();
            }
            */
        // } else {


            $is_all_category = $catId === 'all';

            if($is_all_category)
            {
                if(!CRUDBooster::isSuperadmin())
                {
                    $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myParentId())->first();
                    $send_category = $accessRow->accessable_categories ?? [];

                    // $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', CRUDBooster::myParentId())
                    //             ->select('accessable_categories')
                    //             ->get()->toArray();
                    // $send_category = array_column($accessRow[0]['accessable_categories'], 'accessable_categories') ?? [];
                    // if(empty($send_category)){
                    //     $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myParentId())->first();
                    //     $send_category = $accessRow->accessable_categories ?? [];
                    // }
                }else {
                    $send_category = [0];
                }
            }else {
                $send_category = [$catId];
            }

            $data['base_category'] = $is_all_category ? null : Category::find($catId);

            $productQuery = Product::where('status', \App\Enums\Marketplace\ProductStatus::ACTIVE);

            if ($catId && !$is_all_category) {
                $productQuery = $productQuery->where('category_id', $catId);
            }

            $rangeBy = Session::get('mp_store_range_by'.\CRUDBooster::myParentId()) ?? 'ek_price';
            $rangeBy = $this->convertRangeBy($rangeBy);

            $countingAttributes = $productQuery->selectRaw("MIN($rangeBy) as minimumPrice, MAX($rangeBy) as maximumPrice, count(id) as totalProducts")->first();

            $data['totalCount'] = $countingAttributes->totalProducts;
            $data['minimumPrice'] = intval( floor( (float)$countingAttributes->minimumPrice ) );
            $data['maximumPrice'] = intval( ceil( (float)$countingAttributes->maximumPrice ) );

            //For find out category wise best seller app....
            $appsId=\App\TrendCategories::select('drm_app_id');

            if(!$is_all_category)
            {
                $appsId->where('marketplace_cat_id', $catId);
            }
            $appsId = $appsId->first();

            if($appsId && $appsId->drm_app_id){

                $data['appsId'] = $appsId->drm_app_id;

                $results = DB::table('purchase_apps')
                    ->where('purchase_apps.type', '!=', 'Free Trail')
                    ->where('purchase_apps.cms_user_id', CRUDBooster::myParentId())
                    ->where('purchase_apps.app_id', $appsId->drm_app_id)
                    ->where(function ($query) {
                        $query->where('purchase_apps.subscription_date_end', '>', date('Y-m-d'))
                            ->orwhere('purchase_apps.subscription_life_time', 1);
                    })
                    ->get();

                if( $results && $results[0] ){
                    $data['isPurchase'] = true;
                } else
                    $data['isPurchase'] = false;
            }

            $send_category = array_map('intval', $send_category);
            $data['send_category'] = json_encode($send_category);

            $importProduct = app(\App\Http\Controllers\AdminDrmImportsController::class)->importProductCheck(\CRUDBooster::myParentId());
            $data['transferLimit'] = $importProduct ? $importProduct['product_amount'] : 0;
            $data['transferPlan'] = $importProduct['plan'] ? $importProduct['plan'] : null;
            $data['transferPlanLimit'] = $importProduct['limit'] ? $importProduct['limit']  : null;

        // }

        return view('marketplace.new_product_details', $data);
    }
    public function productsByCategoryNew(Request $request)
    {
        $price_range = $request->price_range;
        $errorMsg = '';
        $catIds = $request->query('cat_ids', []);
        $usersAccessAbleCategories = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myParentId())->first()->accessable_categories ?? [];

        // $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', CRUDBooster::myParentId())
        //     ->select('accessable_categories')
        //     ->get()->toArray();
        // $usersAccessAbleCategories = array_column($accessRow[0]['accessable_categories'], 'accessable_categories') ?? [];
        // if(empty($usersAccessAbleCategories)){
        //     $usersAccessAbleCategories = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myParentId())->first()->accessable_categories ?? [];
        // }
        if (\CRUDBooster::isCustomer() && \CRUDBooster::myParentId() != 2455) {
            $catIds = array_intersect($catIds, $usersAccessAbleCategories);

            if (!count($catIds)) {
                $errorMsg = "Sorry, You don't have access to this page.";
            }
        }
        $products = collect([]);
        if (!empty($catIds)) {
            if ($request->filter_by) {
                Session::put('mp_store_filter'.\CRUDBooster::myParentId(), $request->filter_by);
            } else {
                Session::forget('mp_store_filter'.\CRUDBooster::myParentId());
            }
            $auto_transfer = $request->auto_transfer ? $request->auto_transfer : null;
            $rangeColumn = $this->convertRangeBy($request->range_by);
            $results = $this->productService->productsFilteringConditionApplyNew([
                'status' => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                'q' => $request->query('q', ''),
                'category_ids' => in_array(0, $catIds) ? [] : $catIds,
                'filter_by' => $request->filter_by,
                'filter_by_input' => $request->filter_by_input ?? '',
                'filter_by_select' => $request->filter_by_select ?? '',
                'price_range' => $request->price_range,
                'range_by' => $rangeColumn,
                'filter_quantity' => $request->filter_quantity ?? null
            ], true);

            $products = $results['paginated_collection'];
            if($auto_transfer){
                return response()->json([
                    'totalProducts' => $products->total(),
                ]);
            }
            $allProducts =100;
         //   $collection = \App\Models\Marketplace\Product::whereIn('category_id', in_array(0, $catIds) ? [] : $catIds)->where('status', \App\Enums\Marketplace\ProductStatus::ACTIVE)->select("id", "ek_price", "vk_price", "uvp");

         //   $attributes = $collection->selectRaw("id, MIN($rangeColumn) AS min_price, MAX($rangeColumn) AS max_price, count(id) AS totalCount")->first();
            $totalCount = 100;
            $minimumPrice = 0; //intval( floor( (float)$attributes->min_price ) );
            $maximumPrice = 1000;//intval( ceil( (float)$attributes->max_price ) );
            // $collection = \App\Models\Marketplace\Product::whereIn('category_id', in_array(0, $catIds) ? [] : $catIds)
            //                 ->where('status', \App\Enums\Marketplace\ProductStatus::ACTIVE);
            // $totalCount = $collection->count();
            $onlyAvailableCount = 100;//$collection->onlyAvailable()->count();
            $soldOutCount = 10;//$collection->soldOut()->count();
            $importProduct = app(\App\Http\Controllers\AdminDrmImportsController::class)->importProductCheck(\CRUDBooster::myParentId());
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;
            $transferedProducts = $importProduct ? $importProduct['total_product'] : 0;
        }

        return view('marketplace.new_product_list', compact(
                                                'products',
                                                'errorMsg',
                                                'onlyAvailableCount',
                                                'soldOutCount',
                                                'totalCount',
                                                'minimumPrice',
                                                'maximumPrice',
                                                'price_range',
                                                'allProducts',
                                                'transferLimit',
                                                'transferedProducts'
                                                ));
    }

    public function productsByCategoryIds(Request $request)
    {

        $catIds = $request->query('cat_ids', []);
        $usersAccessAbleCategories = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myParentId())->first()->accessable_categories;

        // $accessRow = \App\Models\Marketplace\UserAccess::where('user_id', CRUDBooster::myParentId())
        // ->select('accessable_categories')
        // ->get()->toArray();
        // $usersAccessAbleCategories = array_column($accessRow[0]['accessable_categories'], 'accessable_categories') ?? [];
        // if(empty($usersAccessAbleCategories)){
        //     $usersAccessAbleCategories = \App\Models\Marketplace\UserAccess::where('user_id', \CRUDBooster::myParentId())->first()->accessable_categories ?? [];
        // }

        if (\CRUDBooster::isCustomer() && \CRUDBooster::myParentId() != 2455) {
            $result = array_intersect($catIds, $usersAccessAbleCategories ?? []);
            $catIds = $result;

            if (!count($catIds)) {
                $errorMsg = "Sorry, You don't have access to this page.";
            }
        }
        if (!empty($catIds)) {
            if ($request->filter_by) {
                Session::put('mp_store_filter'.\CRUDBooster::myParentId(), $request->filter_by);
            } else {
                Session::forget('mp_store_filter'.\CRUDBooster::myParentId());
            }

            $rangeColumn = $this->convertRangeBy($request->range_by);
            $results = $this->productService->filteringApplyForGetAllProductIds([
                'status' => \App\Enums\Marketplace\ProductStatus::ACTIVE,
                'q' => $request->query('q', ''),
                'category_ids' => in_array(0, $catIds) ? [] : $catIds,
                'filter_by' => $request->filter_by,
                'filter_by_input' => $request->filter_by_input ?? '',
                'filter_by_select' => $request->filter_by_select ?? '',
                'price_range' => $request->price_range,
                'range_by' => $rangeColumn,
            ], true);

            $allProductsIds =$results['allProductsIds'];
            $importProduct = app(\App\Http\Controllers\AdminDrmImportsController::class)->importProductCheck(\CRUDBooster::myParentId());
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;
        }
        return response()->json([
            'allProductsIds' => $allProductsIds,
            'transferLimit'     => $transferLimit
        ]);
    }

    public function getBrandsOfCategory ($catId, $price,$rangeBy, $isAutoTransfer = false)
    {

        if(!$isAutoTransfer){
            $price_range = explode(',',$price);
            $minPrice = $price_range[0];
            $maxPrice = $price_range[1];
        }else{
            $minPrice = $price[0];
            $maxPrice = $price[1];
        }

        $range_by = $this->convertRangeBy($rangeBy);
        if($range_by == '(uvp - vk_price)/vk_price*100'){
            $brands = \App\Models\Marketplace\Product::with('productBrand')
                ->select('brand')
                ->where('category_id', $catId)
                ->where('status',ProductStatus::ACTIVE)
                ->whereRaw("((uvp - vk_price)/vk_price*100) >= $minPrice")
                ->whereRaw("((uvp - vk_price)/vk_price*100) <= $maxPrice")
                ->distinct()
                ->orderBy('brand', 'asc')
                ->get();
        }else{
            $brands = \App\Models\Marketplace\Product::with('productBrand')
                ->select('brand')
                ->where('category_id', $catId)
                ->where('status',ProductStatus::ACTIVE)
                ->where($range_by, '>=', intval($minPrice))
                ->where($range_by, '<=', intval($maxPrice))
                ->distinct()
                ->orderBy('brand', 'asc')
                ->get();
        }
        if(!$isAutoTransfer){
            $options = '<option value="0" selected>Select brand</option>';
            foreach($brands as $product) {
                $brand = is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand ?? '';
                $options.= "<option data-toggle='tooltip' data-placement='left' value='".$product->brand."'>".$brand."</option>";
            }
            return $options;
        } else {
            return $brands;
        }

    }

    public function getSubCategoriesFromParentCategory ($categoryId,$price,$rangeBy) {
        $price_range = explode(',',$price);
        $minPrice = $price_range[0];
        $maxPrice = $price_range[1];

        $range_by = $this->convertRangeBy($rangeBy);
        if($range_by == '(uvp - vk_price)/vk_price*100'){
            $subcategory = \App\Models\Marketplace\Product::where('category_id', $categoryId)
                ->where('status',ProductStatus::ACTIVE)
                ->whereRaw("((uvp - vk_price)/vk_price*100) >= $minPrice")
                ->whereRaw("((uvp - vk_price)/vk_price*100) <= $maxPrice")
                ->distinct()
                ->orderBy('category', 'asc')
                ->pluck('category')
                ->toArray();
        } else {
            $subcategory = \App\Models\Marketplace\Product::where('category_id', $categoryId)
                ->where('status',ProductStatus::ACTIVE)
                ->where($range_by, '>=', intval($minPrice))
                ->where($range_by, '<=', intval($maxPrice))
                ->distinct()
                ->orderBy('category', 'asc')
                ->pluck('category')
                ->toArray();
        }

        $options = '<option value="0" selected>Select sub category</option>';
        foreach(($subcategory) as $sub_cat) {
            if($sub_cat[0]){
                $options.= "<option data-toggle='tooltip' data-placement='left' value='".$sub_cat[0]."'>".str_replace(',', ' > ', $sub_cat[0],)."</option>";
            }
        }
        return $options;
    }

    public function checkoutModal($id)
    {
        $product = Product::find($id);

        $user = User::find(CRUDBooster::myParentId());

        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'marketplace_privacy')->first();
        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }

        return view('marketplace.product.checkout_form', compact('product', 'user', 'term'));
    }

    public function viewModal($id)
    {
        // $product = Product::with(['category', 'supplier', 'collection'])->find($id);
        // $supplierName = $product->collection->supplier->name ?? null;
        $product = Product::with('mainCategory','productBrand')->find($id);
        // $user = User::find(CRUDBooster::myParentId());
        $discount = 0.0;
        if($product->mainCategory->start_date <= now() && $product->mainCategory->end_date >= now() && $product->mainCategory->is_offer_active){
            $discount = $product->mainCategory->discount_percentage;
        }
        if($product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active){
            $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
        }
        // return view('marketplace.product.view_form', compact('product', 'user', 'supplierName'));
        return view('marketplace.product.view_form', compact('product', 'discount'));

    }

    public function productDisconnectModal($id)
    {
        $mp_product = Product::find($id);
        $drm_product_id = MpCoreDrmTransferProduct::where('marketplace_product_id', $mp_product->id)
            ->orWhere('mp_product_ean', $mp_product->ean)
            ->where('user_id', CRUDBooster::myParentId())
            ->pluck('drm_product_id')->toArray();

        if($drm_product_id){
            app(\App\Services\DRMProductService::class)->destroy($drm_product_id, CRUDBooster::myParentId());
            return response()->json( [
                'status'=> true,
                'message'=> __("marketplace.disconncet_success_message"),
            ]);
        } else {
            return response()->json( [
                'status'=> false,
                'message'=> 'Something went wrong!',
            ]);
        }
    }

        public function maketplaceAnalysisView($id){
            $data = [];
            $product = Product::with('mainCategory:id,start_date,end_date,is_offer_active,discount_percentage')->select('id','ean','offer_start_date','offer_end_date','is_offer_active','discount_percentage','category_id','api_id')->find($id);

            $discount = 0.0;
            if($product->mainCategory->start_date <= now() && $product->mainCategory->end_date >= now() && $product->mainCategory->is_offer_active){
                $discount = $product->mainCategory->discount_percentage;
            }
            if($product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active){
                $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
            }
            
            $user = User::find(CRUDBooster::myParentId());
            $MarketplaceProductAnalysis = MarketplaceProductAnalysis::select('id','product_info')
            ->where('user_id',$user->id)->where('product_id',$id)->whereDay('created_at',Carbon::now()->format('d'))->where('ean',$product->ean)->orderBy('id','desc')->first();
            if($MarketplaceProductAnalysis != null){
                $product_profit = $this->productService->profitCalculation($MarketplaceProductAnalysis);
                $cvCalculation =  $this->productService->mpCvCalculation($MarketplaceProductAnalysis);

                $query_limit = $this->marketplaceAnalysisService->appLimitition($user->id);
                $total_query = $this->marketplaceAnalysisService->userTotalUsed($user->id);
                $data['used'] = $this->marketplaceAnalysisService->queryUsedInfo($total_query, $query_limit);
                $data['used_percentage'] = $this->marketplaceAnalysisService->queryUsedPercentage($total_query, $query_limit);
                return view('marketplace.product.marketplace_analysis', compact('product','MarketplaceProductAnalysis','product_profit','cvCalculation','data','discount'));
            }else{
                return response()->json( [
                    'status'=> false,
                    'product'=> $product,
                ]);
            }
        }

    public function productPayment()
    {
        return view('marketplace.product_payment');
    }

    public function productShoppingCart()
    {
        return view('marketplace.product_shoppnigcart');
    }

    public function purchaseProductSca($purchase_data)
    {
        //DB::beginTransaction();
        try {
            $user = User::with('billing_detail')->find($purchase_data['user_id']);
            if (is_null($user->term_accept)) {
                $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);
            }

            $discount = $purchase_data['discount'] ?? 0;
            $total = $purchase_data['total'] ?? 0;
            $sub_total = $purchase_data['sub_total'] ?? 0;
            $intendId = $purchase_data['id'];
            $product = app(ProductService::class)->getById($purchase_data['product_id']);

            $collection = \App\Models\Marketplace\Collection::find($product->collection_id);
            $seller_id = $collection->supplier_id;

            $customer_id = $this->marketplaceCustomerCreateFromUser($user->id, $seller_id);

            // Save payment information on marketplace_purchase_products table
            $order_info = [
                'user_id' => $seller_id,
                'drm_customer_id' => $customer_id,
                'order_date' => date('Y-m-d H:i:s'),
                'total' => round(($total), 2),
                'sub_total' => round($sub_total, 2),
                'discount' => round($discount, 2),
                'discount_type' => 'fixed',
                'total_tax' => 0,
                'payment_type' => "Stripe Card",
                'payment_date' => date('Y-m-d H:i:s'),
                'status' => "paid",
                'currency' => "EUR",
                'adjustment' => 0,
                'order_id_api' => $intendId,
            ];

            if (!is_null($customer_id)) {
                $customer = NewCustomer::find($customer_id);
                $order_info['customer_info'] = customerToCustomerInfoJson($customer);
                $order_info['billing'] = $customer->billing;
                $order_info['shipping'] = $customer->shipping;
            }

            $carts = [];
            $cart_item = [];
            $cart_item['id'] = 1;
            $cart_item['product_name'] = $product->name;
            $cart_item['description'] = 'Marketplace Purchase Complete. Product Name is "' . $product->name . '".Purchase Date ' . date('Y-m-d H:i:s');
            $cart_item['qty'] = 1;
            $cart_item['rate'] = round($product->price, 2);
            $cart_item['tax'] = 0;
            $cart_item['product_discount'] = 0;
            $cart_item['amount'] = round($product->price, 2);
            $carts[] = $cart_item;
            $order_info['cart'] = json_encode($carts);

            $order = app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);

            if ($order) {
                $daily_customer_id = $this->marketplaceCustomerCreateFromUser($user->id, $seller_id);
                if (!is_null($daily_customer_id)) {
                    $customer = NewCustomer::find($daily_customer_id);
                    $order_info['customer_info'] = customerToCustomerInfoJson($customer);
                    $order_info['billing'] = $customer->billing;
                    $order_info['shipping'] = $customer->shipping;
                }

                $order_info['drm_customer_id'] = $daily_customer_id;
                $order_info['user_id'] = 98;
                $order_info['order_date'] = date('Y-m-d H:i:s');
                $order_info['insert_type'] = 7; //Marketplace
                $order_info['shop_id'] = 8;
                $order_info['order_id_api'] = 'drm_market_' . $order->cms_user_id . '_' . $order->id;
                $daily_order = app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
                // app(InternelSyncService::class)->transferOutgoingOrders($daily_order->id);
            }

            //DB::commit();

            if (request()->ajax()) {
                return ['success' => true, 'message' => 'Product purchase success!'];
            } else {
                CRUDBooster::redirect(route('marketplace::store'), "Product purchase success", "success");
            }
        } catch (\Exception $e) {
            //DB::rollBack();
            if (request()->ajax()) {
                return ['success' => false, 'message' => $e->getMessage()];
            } else {
                CRUDBooster::redirect(route('marketplace::store'), "Product purchase failed. ERROR: " . $e->getMessage(), "error");
            }
        }
    }

    public function getSupplierRegisterFrame()
    {
        $db_countries = DB::table('countries')->select('name', 'id', 'country_shortcut as code')->get();
        $countries = $db_countries->keyBy('name')->toArray();
        $countriesJson = json_encode($countries);
        $countries = $db_countries->keyBy('id')->toArray();
        $countriesRevJson = json_encode($countries);

        return view('marketplace.iframe.supplier_registration', compact('countriesJson', 'countriesRevJson'));
    }

    public function supplierRegistration($slug = null)
    {
        $valid_slugs = ['relavida'];

        if(!empty($slug) && !in_array($slug, $valid_slugs)) {
            if(isset( $_SERVER['HTTP_X_REQUESTED_WITH'] ) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest'){
                return response()->json([
                    'error_list' => ['Something went wrong!'],
                ], 422);
            }else {
                return redirect(\Request::server('HTTP_REFERER'))->with(['error_msg' => 'Something went wrong!']);
            }
        }

        $theme_slug = !empty($slug) ? $slug : null;

        $skipVatCheck = isset($_REQUEST['country_id']) && intval($_REQUEST['country_id']) === 83;

        $validator = Validator::make($_REQUEST, [
            'name' => 'required',
            'email' => 'required|email|unique:cms_users',
            'password' => 'required|min:6',
            'country_id' => 'required',
            'city' => 'required',
            'zip' => 'required',
            'address' => 'required',
            'vat_id' => ['nullable', function ($attribute, $value, $fail) use ($skipVatCheck) {
                $vat_checker = \DRM::checkTaxNumber($value);
                if(!$vat_checker['success'] && !$skipVatCheck){
                    $fail('The '.$attribute.' is invalid. '.$vat_checker['message']);
                }
            }],
        ]);

        if ($validator->fails()) {

            if(isset( $_SERVER['HTTP_X_REQUESTED_WITH'] ) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest'){
                return response()->json([
                    'error_list' => $validator->errors()->all()
                ], 422);
            }

            return redirect(\Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
        }


        // Validate Email and phone on API
        try {
            $email = $_REQUEST['email'];
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);
            if(isset($_REQUEST['phone']) && !empty($_REQUEST['phone']))
            {
                $phone = $_REQUEST['phone'];
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
            }
        } catch (\Exception $e) {
            if(isset( $_SERVER['HTTP_X_REQUESTED_WITH'] ) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest'){
                return response()->json([
                    'error_list' => [$e->getMessage()],
                ], 422);
            }

            return redirect(\Request::server('HTTP_REFERER'))->withErrors([$e->getMessage()])->withInput();
        }


        //Insert reference id
        $ref_id = session()->get("ref_id");
        $ref_user_id = null;
        if ($ref_id != null) {
            $ref_user_id = DB::table("cms_users")->where("ref_id", $ref_id)->value('id');
        }else{
            if(isset($_REQUEST['user_referred_by']) && $_REQUEST['user_referred_by']){
                $ref_user_id = (int) $_REQUEST['user_referred_by'];
            }
        }

        //Create User
        $user = User::create([
            'name' => $_REQUEST['name'],
            'email' => $_REQUEST['email'],
            'password' => \Illuminate\Support\Facades\Hash::make($_REQUEST['password']),
            'id_cms_privileges' => '4',
            'status' => null,
            'referrer_id' => $ref_user_id,
            'ref_id' => Str::random(25),
            'created_at' => now(),
            'two_factor_code' => rand(100000, 999999),
            'two_factor_expires_at' => now()->addMinutes(10),
            'user_theme' => $theme_slug,
        ]);

        if($theme_slug === 'relavida')
        {
            DB::table('relavida_user_types')->insert([
                'user_id' => $user->id,
                'type' => $_REQUEST['relavida_user_type'],
            ]);
        }

        $groupId = $theme_slug === 'relavida' ? 3 : 4;
        app(\App\Http\Controllers\RegistrationController::class)->assignUserGroup($user->id, $groupId);

        if(empty($user)) return redirect(\Request::server('HTTP_REFERER'))->with(['error_msg' => 'Something went wrong!']);

        //Billing data
        $billing = request()->only(['country_id', 'city', 'zip', 'address', 'company_name', 'email', 'phone', 'vat_id']);
        BillingDetail::updateOrCreate(['user_id' => $user->id], $billing); //Save billing details


        //Initial appointment point insert
        DB::table('takeappointment')->updateOrInsert([
            'user_id' => $user->id
        ],
        [
            'payment_date_for' => 1,
            'payment_date_remaining' => 1
        ]);

        //Delivery company data
        $deliveryCompanyData = [
            'name'      => $user->name,
            'zip'       => request()->zip,
            'country_id'=> request()->country_id,
            'phone'     => request()->phone,
            'address'   => request()->address,
            'contact_name'  => $user->name,
            'note'          => 'Marketplace Supplier',
            'is_marketplace_supplier'   => 1,
            'supplier_id'   => $user->id,
            'state'         => request()->city,
        ];

        //Save delivery company data
        DeliveryCompany::updateOrCreate([
            'user_id' => \App\Enums\Apps::DROPMATIX_ID,
            'email'     => $user->email
        ], $deliveryCompanyData);

        //Add tag to customer profile
        $this->tagInsertToCustomer($user->id,'Anmeldung als Lieferant', 4, 2455);
        app(\App\Http\Controllers\RegistrationController::class)->assignUserTag($user->id, 'Anmeldung als Lieferant');

        //Two factor email
        $tags = [
            'user_name' => $user->name,
            'user_email' => $user->email,
            'two_factor_code' => $user->two_factor_code,
        ];

        if($user->id == 2817 || ($user->user_theme && $user->user_theme === 'relavida'))
        {
            $mail_data = app(\App\Http\Controllers\EmailVerificationController::class)->twoFactorMailData($tags, 'relavida');

            // Relavida SMTP
            if( DB::table('user_mail_configurations')->where('user_id', 2817)->where('active', 1)->exists())
            {
                app('drm.mailer')->getMailer(2817)->to($user->email)->send(new DRMSEndMail($mail_data));
            } else {
                app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));
            }

        } else {
            $slug = 'two_factor_varification';
            $lang = getUserSavedLang($user->email);
            $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
        }

        if(!isLocal()){
            app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));
        }

        Session::put('email', $_REQUEST['email']);
        Session::put('password', $_REQUEST['password']);
        Session::put('user_theme', $theme_slug);

        //Sync with internel API
        // app(InternelSyncService::class)->transferVendorData($user->id);

        // Do accounting
        app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

        if(isset( $_SERVER['HTTP_X_REQUESTED_WITH'] ) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest'){
            return response()->json([
                'redirect_url' => url('email_verify_token?token='.$user->ref_id.'&theme='.$theme_slug),
            ], 200);
        }

        return redirect()->action('EmailVerificationController@verifyToken');
    }


    public function emailTemplateSettings()
    {
        if(CRUDBooster::myParentId() != 2817) abort(404);

        $data = [];
        $data['page_title'] = 'Templates';

        $authId = CRUDBooster::myParentId();
        $data['has_welcome_email'] = DB::table('welcome_email_template')->where('cms_user_id', $authId)->exists();
        $data['has_2fa_email'] = DB::table('two_factor_email_template')->where('cms_user_id', $authId)->exists();
        return view('admin.drm_email_marketings.supplier_email_setting', $data);
    }

    public function emailTemplateSetting($slug)
    {
        if(CRUDBooster::myParentId() != 2817) abort(404);

        if( $slug === 'two_factor_email_template')
        {
            $data['page_title'] = '2-FA verification email template';
            $authId = CRUDBooster::myParentId();
            $data['mail'] = DB::table('two_factor_email_template')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
            $data['senderMails'] = \App\SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
            $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

            $data['template_save_url'] = 'marketplace/content-templates/two_factor_email_template';
            $data['template_test_url'] = 'marketplace/content-templates/two_factor_email_template/test';

            $data['item'] = 'two_factor_email_template';
            return view('admin.drm_email_marketings.welcome_email_template', $data);
        }

        $data['page_title'] = 'Welcome email template';
        $authId = CRUDBooster::myParentId();
        $data['mail'] = DB::table('welcome_email_template')->where('cms_user_id', $authId)->orderBy('id', 'desc')->first();
        $data['senderMails'] = \App\SenderEmailSetting::where('user_id', $authId)->whereNull('verification_code')->get();
        $data['userEmail'] = DB::table('cms_users')->where('id', $authId)->value('email');

        $data['template_save_url'] = 'marketplace/content-templates/welcome_email_template';
        $data['template_test_url'] = 'marketplace/content-templates/welcome_email_template/test';
        $data['item'] = 'welcome_email_template';
        return view('admin.drm_email_marketings.welcome_email_template', $data);
    }


    public function showTemplateContent($slug)
    {
        if(CRUDBooster::myParentId() != 2817) abort(404);

        if(!in_array($slug, ['relavida_impressum', 'relavida_agb', 'relavida_data_protection', 'relavida_agb_produktimportservice'])) abort(404);

        $page = DB::table('drm_pages')->where('page_name', $slug)->select('id', 'page_content', 'page_title', 'page_name')->first();
        if(empty($page)) abort(404);

        $data = [];
        $data['page_title'] = $page->page_title;
        $data['content'] = $page->page_content;
        $data['form_url'] = route('marketplace::template_save', $page->id);
        return view('marketplace.template_content', $data);
    }

    public function saveTemplateContent($id)
    {
        if(CRUDBooster::myParentId() != 2817) abort(404);

        $slug = DB::table('drm_pages')->where('id', '=', $id)->value('page_name');

        if(!in_array($slug, ['relavida_impressum', 'relavida_agb', 'relavida_data_protection', 'relavida_agb_produktimportservice'])){
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Invalid access', 'warning');
        }

        $content = request()->input('content');
        $saved = DB::table('drm_pages')->where('id', '=', $id)->update(['page_content' => $content]);

        $cache_key = 'drm_pages_'.$slug;
        $cache_key_full = 'drm_pages_'.$slug.'_full';
        Cache::forget($cache_key);
        Cache::forget($cache_key_full);

        CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Template updated successfully!', 'success');
    }

    public function saveEmailTemplateSetting($slug)
    {
        if(CRUDBooster::myParentId() != 2817) abort(404);

        Validator::make($_REQUEST, [
            'sender_email' => 'required|email'
        ])->validate();

        if(!in_array($slug, ['welcome_email_template', 'two_factor_email_template']))
        {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], 'Invalid access', 'warning');
            return;
        }

        $data = [
            'mail_subject' => $_REQUEST['mail_subject'],
            'head_text' => $_REQUEST['head_text'],
            'auto_mail' => (int)$_REQUEST['auto_mail'],
            'email_template' => $_REQUEST['email_template'],
            'bcc_email' => $_REQUEST['bcc_email'],
            'created_at' => now(),
            'updated_at' => now()
        ];

        if (!empty($_REQUEST['sender_email'])) {
            $data['sender_email'] = $_REQUEST['sender_email'];
        }

        $authId = CRUDBooster::myParentId();

        DB::table($slug)->updateOrInsert(
            [
                'cms_user_id' => $authId
            ], $data
        );

        CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans('Email Setting Changed'), 'success');
    }

    public function testEmailTemplateSetting($slug)
    {
        if(CRUDBooster::myParentId() != 2817) abort(404);

        try{

            if(!in_array($slug, ['welcome_email_template', 'two_factor_email_template']))
            {
                throw new \Exception('Invalid access');
            }

            $email_to = $_REQUEST['test_email'];
            if(empty($email_to))
            {
                throw new \Exception('Email can not be empty!');
            }

            //Email tags
            $tags = [
                'user_name' => $user->name,
                'user_email' => $user->email,
                'password_confirmation' => '*******',
                'two_factor_code' => rand(1111, 7777),
            ];

            if ($slug === 'two_factor_email_template')
            {
                $mail_data = app(\App\Http\Controllers\EmailVerificationController::class)->twoFactorMailData($tags, 'relavida');
            } else {
                $mail_data = app(\App\Http\Controllers\EmailVerificationController::class)->welcomeMailData($tags, 'relavida');
            }

            // Relavida SMTP
            if( DB::table('user_mail_configurations')->where('user_id', 2817)->where('active', 1)->exists())
            {
                app('drm.mailer')->getMailer(2817)->to($user->email)->send(new DRMSEndMail($mail_data));
            } else {
                app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));
            }

            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans('Email sent!'), 'success');

        }catch (\Exception $e) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans('Email sent faield!') . ' ' . $e->getMessage(), 'error');
        }
    }

    //Create customer
    //params: user_id , seller_id
    private function marketplaceCustomerCreateFromUser($user_id, $seller_id)
    {
        $user_data = User::find($user_id);
        $user = User::with('billing_detail')->has('billing_detail')->find($user_id);
        $customer_id = null;
        $customer_info = [];

        if ($user) {
            $billing = $user->billing_detail;
            $country = ($billing->country) ? (($billing->country->id == 2) ? 'United Kingdom' : $billing->country->name) : null;
            $language = ($billing->country) ? $billing->country->language : null;

            $customer_info["customer_full_name"] = $user->name;
            $customer_info["company_name"] = $billing->company_name;
            $customer_info["currency"] = 'EUR';
            $customer_info['email'] = $user->email;
            $customer_info['phone'] = $billing->phone;
            $customer_info['address'] = $billing->address;
            $customer_info['city'] = $billing->city;
            $customer_info['country'] = $country;
            $customer_info['default_language'] = $language;
            $customer_info['zip_code'] = $billing->zip;
            $customer_info['state'] = null;
            $customer_info['vat_number'] = $billing->vat_id;

            //shipping
            $customer_info['street_shipping'] = $billing->address;
            $customer_info['city_shipping'] = $billing->city;
            $customer_info['state_shipping'] = null;
            $customer_info['zipcode_shipping'] = $billing->zip;
            $customer_info['country_shipping'] = $country;

            //billing
            $customer_info['street_billing'] = $billing->address;
            $customer_info['city_billing'] = $billing->city;
            $customer_info['state_billing'] = null;
            $customer_info['zipcode_billing'] = $billing->zip;
            $customer_info['country_billing'] = $country;
        } else {
            $customer_info["customer_full_name"] = $user_data->name;
            $customer_info["currency"] = 'EUR';
            $customer_info['email'] = $user_data->email;
        }

        $customer_info['insert_type'] = 6;
        $customer_info['user_id'] = $seller_id;

        $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);
        return $customer_id;
    }

    public function transferProductToDrm ($productId = null, $attributes = [])
    {
        //DB::beginTransaction();
        try {
            if ($productId != null) {
                $pId = $productId;
            } else {
                $pId = request()->product_id;
            }
           $product = Product::with('additionalInfo','productBrand')->where('id', $pId)->first();

            $manufacturer = null;
            $manufacturer_link = null;
            $manufacturer_id = null;
            $custom_tariff_number = null;
            $shipping_company_id = null;
            $region = null;
            $country_of_origin = null;
            $min_stock = null;
            $min_order = null;
            $gross_weight = null;
            $net_weight = null;
            $product_length = null;
            $product_width = null;
            $product_height = null;
            // $volume = null;
            $packaging_length = null;
            $packaging_width = null;
            $packaging_height = null;
            $item_unit = null;
            $packing_unit = null;
            // $volume_gross = null;

            if( $product->additionalInfo ){
                $manufacturer = $product->additionalInfo->manufacturer;
                $manufacturer_link = $product->additionalInfo->manufacturer_link;
                $manufacturer_id = $product->additionalInfo->manufacturer_id;
                $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                $shipping_company_id = $product->additionalInfo->shipping_company_id;
                $region = $product->additionalInfo->region;
                $country_of_origin = $product->additionalInfo->country_of_origin;
                $min_stock = $product->additionalInfo->min_stock;
                $min_order = $product->additionalInfo->min_order;
                $gross_weight = $product->additionalInfo->gross_weight;
                $net_weight = $product->additionalInfo->net_weight;
                $product_length = $product->additionalInfo->product_length;
                $product_width = $product->additionalInfo->product_width;
                $product_height = $product->additionalInfo->product_height;
                // $volume = $product->additionalInfo->volume;
                $packaging_length = $product->additionalInfo->packaging_length;
                $packaging_width = $product->additionalInfo->packaging_width;
                $packaging_height = $product->additionalInfo->packaging_height;
                $item_unit = $product->additionalInfo->item_unit;
                $packing_unit = $product->additionalInfo->packing_unit;
                // $volume_gross = $product->additionalInfo->volume_gross;
            }

            $user_id = CRUDBooster::myParentId();
            $ean_exist = DrmProduct::where('ean', $product->ean)->where('user_id', $user_id)->first();
            if($ean_exist){
                return response()->json([
                    'status'    => 422,
                    'message'   => 'This ean allready existed to your account. You can not transfer this product.',
                ]);
            } else {
                if ( Product::isInDrmProductList($product) ) {
                    if ( !(Product::isInMpDrmCoreTable($product->id)) ) {
                        $inDrm = DrmProduct::select('id','marketplace_product_id','user_id','created_at','updated_at')->where('marketplace_product_id', $product->id)->get();

                        MpCoreDrmTransferProduct::insert([
                            'drm_product_id' => $inDrm->id,
                            'mp_product_ean' => $inDrm->ean,
                            'marketplace_product_id' => $inDrm->marketplace_product_id,
                            'user_id' => $inDrm->user_id,
                            'created_at' => $inDrm->created_at,
                            'updated_at' => $inDrm->created_at,
                        ]);
                    }
                    return response()->json([
                        'status'    => 422,
                        'message'   => 'Product allready existed to this account.',
                    ]);
                }

                // $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                $country_id = $product->country_id ?? 1; 
                $lang = app('App\Services\UserService')->getProductLanguage($country_id);

                $discount = 0.0;
                if ($product->category_id) {
                    $category = Category::find($product->category_id);
                    if ($category) {
                        $drmCategory = DrmCategory::firstOrCreate(
                            ['category_name_'.$lang => $category->name, 'user_id' => $user_id],
                            ['country_id' => $country_id]
                        );

                        if ($category->start_date <= now() && $category->end_date >= now() && $category->is_offer_active) {
                            $discount = $category->discount_percentage;
                        }
                    }
                }

                $deliveryCompanyRow = [
                    'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                    'name'          => 'Dropmatix Systema SL',
                    'contact_name'  => 'Dropmatix Systema SL',
                    'zip'           => '07200',
                    'state'         => 'FELANITX',
                    'country_id'    => 8,
                    'email'         => '<EMAIL>',
                    'address'       => 'C/ HORTS, 33',
                    'note'          => 'Marketplace Supplier',
                    'is_marketplace_supplier' => 1,
                ];

                $deliveryCompany = DeliveryCompany::where('user_id', $user_id)
                    ->where('name', $deliveryCompanyRow['name'])
                    ->where('email', '<EMAIL>')
                    ->first();

                if ( !$deliveryCompany ) {
                    $deliveryCompanyRow['user_id'] = $user_id;
                    $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
                }

                $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                    $shippingCost = $product->shipping_cost > 0 ? $product->shipping_cost : 9.68;
                } else {
                    $shippingCost = $product->shipping_cost;
                }

                if ($product->real_shipping_cost == 0 && isEnterpriceOrTrialUser($user_id)) {
                    $shippingCost = 0.0;
                }
                
                if ( $product->brand ) {
                    $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                    $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id',$user_id)->first();
                    if ( !$drmBrand ) {
                        if(!empty($mpBrandName)){
                            $drmBrand = DropmatixProductBrand::create([
                                'brand_name' =>  $mpBrandName,
                                'user_id' => $user_id,
                                'brand_logo'=>($product->productBrand->brand_logo ?? [])
                            ]);
                        }
                    }
                }

                if( $product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
                    $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                }

                $mp_agreement = DB::table('mp_payment_agreements')
                        ->where('user_id', '=', $user_id ?? $user_id)
                        ->where('type', '=', 1)
                        ->select('price_markup')
                        ->first();
                $price_markup = $mp_agreement ? $mp_agreement->price_markup : 0.0;

                $drmProductInfo             = [
                    'user_id'               => $user_id,
                    'country_id'            => $country_id,
                    'language_id'           => null,
                    'name'                  => $product->brand .' '. $product->name,
                    'item_number'           => $product->item_number,
                    'ean'                   => $product->ean,
                    'additional_eans'       => json_encode($product->additional_eans),
                    'image'                 => $product->image,
                    'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id, false, $discount, $product->api_id),
                    'vk_price'              => 0.00,
                    'vat'                   => $product->vat ?? null,
                    'tax_type'              => $product->tax_type ?? 1,
                    'stock'                 => (empty($stock)) ? 0 : $stock,
                    'category'              => $drmCategory->id ?? null,
                    'ean_field'             => 1,
                    'item_weight'           => $product->item_weight ?? null,
                    'item_size'             => $product->item_size ?? null,
                    'item_color'            => $product->item_color ?? null,
                    'note'                  => $product->note ?? null,
                    'production_year'       => $product->production_year ?? null,
                    'brand'                 => $drmBrand ? $drmBrand->id : null,
                    'materials'             => $product->materials ?? null,
                    'tags'                  => $product->tags ?? null,
                    'update_enabled'        => $product->update_enabled ?? null,
                    'status'                => $product->status ?? null,
                    'gender'                => $product->gender ?? null,
                    'uvp'                   => $product->uvp ? $product->uvp + (($product->uvp * $price_markup)/100) : 0.00,
                    'title'                 => [
                                                 $lang => $product->name ?? null,
                                               ],
                    'update_status'         => makeUpdateStatusJson(),

                    // 'short_description' => json_encode($product->description),
                        'description' => [
                            $lang => $product->description ?? null,
                        ],
                    'delivery_company_id'     => $deliveryCompany->id,
                    'marketplace_supplier_id' => $product->supplier_id ?? '',
                    'marketplace_product_id'  => $product->id,
                    'marketplace_shipping_method' => $product->shipping_method,
                    'shipping_cost'               => $shippingCost ?? 0,
                    'delivery_days'               => $product->delivery_days,
                    'industry_template_data'      => json_encode($product->industry_template_data),
                    'product_type'                => \App\Enums\ProductType::MP_MANUAL_TRANSFER,
                    'mp_category_offer'           => $discount,
                    // Additional columns
                    'manufacturer'                => $manufacturer,
                    'manufacturer_link'           => $manufacturer_link,
                    'manufacturer_id'             => $manufacturer_id,
                    'custom_tariff_number'        => $custom_tariff_number,
                    'shipping_company_id'         => $shipping_company_id ?? 8,
                    'region'                      => $region,
                    'country_of_origin'           => $country_of_origin,
                    'min_stock'                   => $min_stock,
                    'min_order'                   => $min_order,
                    'gross_weight'                => $gross_weight,
                    'net_weight'                  => $net_weight,
                    'product_length'              => $product_length,
                    'product_width'               => $product_width,
                    'product_height'              => $product_height,
                    // 'volume'                      => $volume,
                    'packaging_length'            => $packaging_length,
                    'packaging_width'             => $packaging_width,
                    'packaging_height'            => $packaging_height,
                    'item_unit'                   => $item_unit,
                    'packaging_unit'                => $packing_unit,
                    // 'volume_gross'                => $volume_gross,
                    'mp_price_markup'             => $price_markup,
                    'marketplace_delivery_company_id'=> $product->delivery_company_id,
                    'im_handel'                   => $product->im_handel,
                ];

                $drmProductInfo = array_merge($drmProductInfo,$attributes);

                $drmProduct = DrmProduct::create($drmProductInfo);

                DB::table('drm_product_categories')->insert([
                    'product_id' => $drmProduct->id,
                    'category_id' => $drmCategory->id,
                ]);

                MpCoreDrmTransferProduct::insert([
                    'drm_product_id' => $drmProduct->id,
                    'mp_product_ean' => $drmProduct->ean,
                    'marketplace_product_id' => $drmProduct->marketplace_product_id,
                    'user_id' => $drmProduct->user_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                DB::table('mp_top_sale_products')->where('user_id', $user_id)->where('product_id', $product->id)->delete();

                //DB::commit();
                $one_one_sync = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->get();
                if($one_one_sync[0]->one_one_sync == 1){
                    app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user_id);
                }
                return $drmProduct;
            }

        } catch (\Exception $e) {
            //DB::rollBack();
            return response()->json([
                'status'        => 'error :: '.$e->getMessage(),
                'error_msg'     => 'Something went wrong',
            ]);
        }
    }

    public function transferAllFilteredProductsToDrm ($productId = null, $attributes = [], $categoryId = null, $user_id = null)
    {
        try {

            $productIds =  request()->product_id ? array_map('intval', explode(',', request()->product_id)) : $productId;
            $user_id = $user_id ?? CRUDBooster::myParentId();
            $tranferable_ids = [];

            foreach( array_chunk($productIds, 10000) as $chunk_ids ){
                $id_exists = DrmProduct::select('marketplace_product_id')
                            ->where('user_id', $user_id)
                            ->whereIn('marketplace_product_id', $chunk_ids)
                            ->get()
                            ->pluck('marketplace_product_id')
                            ->toArray();

                $tranferable_ids[] = array_diff($chunk_ids, $id_exists);
            }

            $tranferable_ids = array_merge(...$tranferable_ids);
            if(empty($tranferable_ids)){
                return [
                    'success' => false,
                    'message' => "Products already transfered to drm. Please select new products.",
                ];
            }

            $importProduct = app(\App\Http\Controllers\AdminDrmImportsController::class)->importProductCheck($user_id ?? \CRUDBooster::myParentId());
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;
            $transferPlan = $importProduct['plan'] ? $importProduct['plan'] : null;
            $transferPlanLimit = $importProduct['limit'] ? $importProduct['limit'] : null;

            // Mp store page new product cache clear
            $cache_key = 'cat_access_new_products_'.$user_id;
            $this->mpCacheClear($cache_key);
            // Mp store page new product cache clear END

            if((($transferPlan && $transferPlan == 'Trial') && ($transferPlanLimit && $transferPlanLimit == 'Unlimited')) || ($transferPlanLimit && $transferPlanLimit == 'Unlimited')){
                // if(count($tranferable_ids) > 150){

                    foreach( array_chunk($tranferable_ids, 400) as $tranferable_id ){
                        dispatch(new MarketplaceProductTransferToDrm($tranferable_id, $user_id, $categoryId, true))->onQueue('mp_product_trans'); //->onQueue('keepa');
                    }

                    return [
                        'status' => true,
                        'is_hit_jobs' => true,
                        'message' => 'Your selected ' .count($tranferable_ids) . ' products transferred on going on the background process. It may take some time to transfer all products.',
                    ];
                // } else {
                //     return $this->transferUnlimitedProductsToDrm($tranferable_ids, $user_id, $categoryId);
                // }
            } else if($transferLimit && $transferLimit > 0){
                // if(count($tranferable_ids) > 150){

                    foreach( array_chunk($tranferable_ids, 400) as $tranferable_id ){
                        dispatch(new MarketplaceProductTransferToDrm($tranferable_id, $user_id, $categoryId))->onQueue('mp_product_trans'); //->onQueue('keepa');
                    }

                    return [
                        'status' => true,
                        'is_hit_jobs' => true,
                        'message' => 'Your selected ' .count($tranferable_ids). ' products transferred on going on the background process. It may take some time to transfer all products.',
                    ];
                // } else {
                //     return $this->transferTarifLimitedProductsToDrm($tranferable_ids, $user_id, $categoryId);
                // }
            } else {
                return response()->json([
                    'status'      => true,
                    'message'     => 'Your DRM products transfer limit exceed! Please Upgrade your tarif plan.',
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'status'        => 'error :: '.$e->getMessage(),
                'error_msg'     => 'Something went wrong',
            ]);
        }

    }

    public function insertTermsAndConditionsTime()
    {
        try {
            //DB::beginTransaction();
            $userId    = \CRUDBooster::myParentId();
            $inserted  = \App\User::find($userId)->update([
                'mp_terms_conditions_confirmation_time' => \Carbon\Carbon::now(),
            ]);

            // START:: Insert Abg Log
            $agb = [
                'agb'       => 'mp_store_terms_and_conditions',
                'shop_info' => [
                    'shop_name' => 'Marketplace',
                    'shop_url' => 'n/a',
                ],
            ];
            $change_log = array();
            $supplier   = User::find($userId);
            $message    = "{$supplier->name} Accepted Marketplace AGB";

            $termExists = \App\AgbLogs::where([
                'user_id' => $userId,
                'message' => $message
            ])->exists();

            if(!$termExists){
                create_agb_log( $supplier->id, $agb, $change_log, $message );
            }
            // END:: Insert Abg Log

            //DB::commit();
            if ( $inserted ) {
                return response()->json([ 'status'=>'success' ]);
            } else {
                return response()->json([ 'status'=>'not_success' ]);
            }
        } catch (\Exception $e) {
            //DB::rollBack();
            return response()->json([ 'status'=>'not_success' ]);
        }

    }

    public function getPriceRangeBasedOnEkOrVkPrice (Request $request)
    {
        $rangeBy = $this->convertRangeBy($request->range_by);

        $attributes = \App\Models\Marketplace\Product::where('category_id', intval($request->category_id))
               ->where('status', \App\Enums\Marketplace\ProductStatus::ACTIVE)
               ->selectRaw("MIN($rangeBy) as minimumPrice, MAX($rangeBy) as maximumPrice")
               ->first();
            Session::put('mp_store_range_by'.\CRUDBooster::myParentId(), $request->range_by);

        return response()->json([
            'minimumPrice' => $attributes->minimumPrice,
            'maximumPrice' => $attributes->maximumPrice,
        ]);
    }

    public function getCountTotalProductsOfEachCategoryForStore()
    {
        if (!\CRUDBooster::isCustomer() || \CRUDBooster::isDropmatixEinkauf() || \CRUDBooster::isDropMatrix() ) {
            $allCategories = [];
        } else {
            $allCategories = UserAccess::where('user_id', CRUDBooster::myParentId())->first()->accessable_categories;
            // $access_category = UserAccess::where('user_id', CRUDBooster::myParentId())
            //                             ->select('accessable_categories')
            //                             ->get()->toArray();
            // $allCategories = array_column($access_category[0]['accessable_categories'], 'accessable_categories')?? [];
            // if(empty($allCategories)){
            //     $allCategories = UserAccess::where('user_id', CRUDBooster::myParentId())->first()->accessable_categories ?? [];
            // }
        }
        $responseArr = [];
        $totalsGroupBy = [];

        if(!empty($allCategories)){
            $totalsGroupBy = Product::where('country_id', 1)->where('status', ProductStatus::ACTIVE)
                        ->whereIn('category_id', $allCategories)
                        ->select('category_id', DB::raw('count(*) as total'))
                        ->groupBy('category_id')
                        ->get();
        } else {
            $totalsGroupBy = Product::where('country_id', 1)->where('status', ProductStatus::ACTIVE)
                        ->select('category_id', DB::raw('count(*) as total'))
                        ->groupBy('category_id')
                        ->get();
        }

       // Updating count
        foreach ($totalsGroupBy as $total) {
            $responseArr[$total->category_id] = $total->total;
        }

        return response()->json($responseArr);
    }

    public function convertRangeBy ($rangeBy)
    {
      if ($rangeBy == 'ek_price') {
        return 'vk_price';
      } else if ($rangeBy == 'vk_price') {
        return 'uvp';
      } else if($rangeBy == 'profit'){
        return '(uvp - vk_price)/vk_price*100';
      }else if($rangeBy == 'stock'){
        return 'stock';
      } else{
        return $rangeBy;
      }
    }

    public function storeProductsSerch(Request $request){

        $products = Product::search($request->search,$request->category)
                                ->where('name','!=','')
                                ->where('status',ProductStatus::ACTIVE)
                                ->take(10)
                                ->get();
        $response = array();
        if(isset($products)){
            foreach($products as $product){
                $response[] = array(
                    'id'     => $product->id,
                    'text'   => $product->name .' ( '.$product->ean.' ) '
                );
            }
        }
        return response()->json($response);
    }

    public function replaceDrmProductSupplierByMpSupplier($id){

        if($id){
            $drm_product = DrmProduct::where([
                        'user_id' => CRUDBooster::myParentId(),
                        'id'      =>$id,
                        'mp_offer'=>1
                        ])->first();

            if(isset($drm_product)){
                $drm_ek = round($drm_product['ek_price'],2);
                $mp_product = Product::where('status',ProductStatus::ACTIVE)->select('ean','vk_price')->where('ean',$drm_product->ean)->where('vk_price','<',$drm_ek)->orderBy('vk_price')->first();

                if(!empty($mp_product)){
                    $drm_product->ek_price = $mp_product->vk_price;
                    $drm_product->mp_offer = null;
                    $drm_product->stock = $mp_product->stock;
                    $drm_product->marketplace_supplier_id = $mp_product->supplier_id;
                    $drm_product->marketplace_product_id = $mp_product->id;
                    $drm_product->update();
                }
            }
        }
        return redirect()->back();
    }

    public function mpOfferHide($id){
        DrmProduct::where([
                    'id'=>$id,
                    'user_id' => CRUDBooster::myParentId(),
                    'mp_offer'=>1
                    ])->update(['mp_offer'=>0]);
        return redirect()->back();
    }

    public function getDrmCheaperProductFromMp(){
        $id = request()->drmProductId;
        $drm_product = DrmProduct::where([
                    'id'=>$id,
                    'mp_offer'=>1
                    ])->first();

        if(isset($drm_product)){
            $drm_ek = round($drm_product->ek_price,2);
            $mp_product = Product::where('status',ProductStatus::ACTIVE)->select('ean','vk_price')->where('ean',$drm_product->ean)->where('vk_price','<',$drm_ek)->orderBy('vk_price')->first();

            return response()->json(['vk_price'=>$mp_product->vk_price],200);
        }
    }

    public function replaceDrmBulkProductSupplierByMpSupplier(){
        $drm_product_ids = request()->product_ids;

        $drm_products = DrmProduct::where('user_id' , CRUDBooster::myParentId())->select('id','ean','ek_price')
                                    ->whereIn('id',$drm_product_ids)
                                    ->where('mp_offer',1)
                                    ->get();

        if(isset($drm_products)){
            foreach($drm_products as $drm_product){

                $drm_ek = round($drm_product->ek_price,2);
                $mp_product = Product::where('status',ProductStatus::ACTIVE)->select('id','ean','vk_price')->where('ean',$drm_product->ean)->where('vk_price','<',$drm_ek)->orderBy('vk_price')->first();

                if(!empty($mp_product)){
                    $drm_product->ek_price = $mp_product->vk_price;
                    $drm_product->mp_offer = null;
                    $drm_product->stock = $mp_product->stock;
                    $drm_product->marketplace_supplier_id = $mp_product->supplier_id;
                    $drm_product->marketplace_product_id = $mp_product->id;
                    $drm_product->update();
                }
            }
        }

        return "success";
    }

    public function countCheapPriceProductInDrm(){
        $product_count = DrmProduct::select('id','mp_offer')->where([
            'user_id'=>CRUDBooster::myParentId(),
            'mp_offer'=>1])->count('id');

        return response()->json(['product_count'=>$product_count],200);
    }
    public function checkIsDrmProduct($id){

        if ( DrmProduct::select('marketplace_product_id','user_id')
                        ->where('marketplace_product_id', $id)
                        ->where('user_id', \CRUDBooster::myParentId())->exists() )
            {
                return true;
            } else {
                return false;
            }
    }

    // public function transferToAnalysis(Request $request){

    //     $request->validate([
    //         'product_ids' => 'required|array|min:1',
    //     ],
    //     [
    //         'product_ids.*' => 'Please select at least one product',
    //     ]);

    //     $user_id = CRUDBooster::myParentId();
    //     $product_ids = $request->product_ids;

    //     $res = app(MarketPlaceProductTransfer::class)->transferToAnalysis($user_id, $product_ids);
    //     return response()->json($res);
    // }

    public function xmlFeedList(){
        $data['xml_feeds'] = MarketplaceProductExportFeed::get();
        $data['page_title'] = 'Marketplace Product Export Feed';
        return view('marketplace.category.xmd_feeds',$data);
    }
    public function xmlFeedDelete($id){
        MarketplaceProductExportFeed::find($id)->delete();
        CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Feed Successfully Deleted", "success");
    }

    public function searchScript(){
        return response()->view('marketplace.iframe.mp_all_product_search_script')
            ->header('Content-Type', 'application/javascript');
    }

    public function postMarketplaceProductSerach(){
        $search_keyword = $_REQUEST['search_by'];

        if($search_keyword){

            $products = \App\MarketplaceProducts::whereNotNull('vk_price')
                        ->where('vk_price', '>', 0)
                        ->where('status', 1)
                        ->where(function($q) use ($search_keyword){
                            $q->where('ean', $search_keyword);
                            $q->orWhere('name', 'LIKE', '%'.$search_keyword.'%');
                            $q->orWhere('description', 'LIKE', '%'.$search_keyword.'%');
                        })
                        ->latest()
                        ->take(24)
                        ->get()
                        ->map(function($product){
                            $profit_percent = round(($product->vk_price - $product->ek_price) / $product->ek_price * 100, 2);
                            return [
                                'id' => $product->id,
                                'ean' => $product->ean,
                                'images' => json_decode($product->image, true),
                                'profit_percentage' => $profit_percent,
                            ];
                        })
                        ->unique('ean')
                        ->sortByDesc('profit_percentage')
                        ->chunk(5)
                        ->toArray();

        }

        $html = '';

        // <div class="mp_iframe_result_button"><a href="#"> Button </a></div>

        if($products){
            foreach($products as $item){
                $html.= '<div class="searched_product_row">';
                $row = '';
                foreach($item as $value){
                    $row.= '<div class="searched_product_col_md_3">
                                <div class="mp_iframe_product_show">
                                    <img src="'.$value['images'][0].'" alt="" width="180px" height="180px"/>
                                    <p class="mp_iframe_result_title">Profit '.$value['profit_percentage'].'% </p>
                                    <div class="mp_iframe_result_overlay"></div>

                                </div>
                            </div>';
                }
                $html.= $row . '</div> <br>';
            }
        }

        return response()->json([
            'data' => $html
        ]);
    }

    public function productStockInformation(Request $request) {
        $ean = $request->ean;
    
        $data = MarketplaceProducts::where(function ($q) use ($ean) {
                $q->where('ean', $ean)->orWhereJsonContains('additional_eans', $ean);
            })
            ->where('status', ProductStatus::ACTIVE)
            ->where('internel_sync_status', 0)
            ->where('shipping_method', \App\Enums\Marketplace\ShippingMethod::FULFILLment)
            ->select('id', 'ean', 'additional_eans', 'internel_stock', 'old_internel_stock', 'internel_stock_updated_at')
            ->orderBy('internel_stock_updated_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $data,
        ]);
    }

    public function getSupplierRegistrationScript($slug = null)
    {
        $valid_slugs = ['relavida'];
        if(!empty($slug) && !in_array($slug, $valid_slugs)) return;


        $db_countries = DB::table('countries')->select('name', 'id', 'country_shortcut as code')->get();
        $countries = $db_countries->keyBy('name')->toArray();
        $countriesJson = json_encode($countries);
        $countries = $db_countries->keyBy('id')->toArray();
        $countriesRevJson = json_encode($countries);

        $data = [
            'action_url' => url('supplier/registration'),
            'theme' => '#fd6500',
            'theme_lighten' => '#ff7d27',
            'countriesJson' => $countriesJson,
            'countriesRevJson' => $countriesRevJson,
        ];

        $view = 'supplier_registration_script';
        if(!empty($slug)) {
            $data = $this->supplier_form_data($slug);
            $data['countriesJson'] = $countriesJson;
            $data['countriesRevJson'] = $countriesRevJson;
            $view = 'relavida_registration_script';
        }

        return response()->view($view, $data)
            ->header('Content-Type', 'application/javascript');
    }

    //Supplier form theme
    private function supplier_form_data($slug)
    {
        return [
            'action_url' => url('supplier/registration/'.$slug),
            'theme' => '#36788a',
            'theme_lighten' => '#4192a7',
        ];
    }

    public function getSupplierCountryList()
    {
        $country_list = \App\Country::whereNotIn('id', [2, 11])->get();

        $html = '<option value="">Choose Country</option>';

        if(isset($_GET['lang']) && $_GET['lang'] == 'de')
        {
            $html = '<option value="">Land</option>';
        }

        if($country_list){
            foreach($country_list as $country){
                $html .= '<option value="'.$country->id.'">'.$country->name.'</option>';
            }
        }else{
            $html = '';
        }

        if($html){
            return response()->json([
                'success' => true,
                'data' => $html
            ], 200);
        }else{
            return response()->json([
                'success' => false,
                'data' => 'No Country Found !'
            ], 400);
        }
    }

    public function categoryProductInfo(){

        $category_id = request()->categoryId;
        $products = Product::where('category_id',$category_id)->select('id','stock','status')->get();

        $data['total_products'] = $products->count('id');
        $data['total_products_active_product'] = $products->where('status',1)->count('id');
        $data['stock_zero'] = $products->where('stock',0)->count('id');
        $data['stock_zero_with_active'] = $products->where('stock',0)->where('status',1)->count('id');
        $data['stock_one'] = $products->where('stock',1)->count('id');
        $data['stock_one_with_active'] = $products->where('stock',1)->where('status',1)->count('id');
        $data['stock_greter_than_one'] = $products->where('stock','>',1)->count('id');
        $data['stock_greter_than_one_active'] = $products->where('stock','>',1)->where('status',1)->count('id');

        return view('marketplace.category.category_product_info',$data);
    }

    public function getMarketplaceCategoriesForUserFilter($parent_cat) {
        $selected_child = Session::get('child_category'.\CRUDBooster::myParentId()) ?? [];

        $parent_ids = explode(',', $parent_cat);
        $all_categories = \App\Models\Marketplace\Category::whereIn('parent_id', $parent_ids)->where('is_active', 1)->orderBy('name')->get();
        $options = '<option value="0" disabled>Select child category</option>';

        foreach(($all_categories) as $category) {
            if($selected_child && in_array( $category->id, $selected_child)){
                $options.= "<option data-toggle='tooltip' data-placement='left' value='".$category->id."' selected>".str_replace(',', ' > ', $category->name,)."</option>";
            } else{
                $options.= "<option data-toggle='tooltip' data-placement='left' value='".$category->id."'>".str_replace(',', ' > ', $category->name,)."</option>";
            }
        }

        return $options;
    }

    public function getMarketplaceParentCategories() {
        $selected_parent = Session::get('parent_category'.\CRUDBooster::myParentId()) ?? [];

        $all_categories = \App\Models\Marketplace\MarketplaceParentCategory::where('is_active', 1)->orderBy('name')->get();
        $options = '<option value="0" disabled>Select parent category</option>';

        foreach(($all_categories) as $category) {
            if($selected_parent && in_array( $category->id, $selected_parent)){
                $options.= "<option data-toggle='tooltip' data-placement='left' value='".$category->id."' selected>".str_replace(',', ' > ', $category->name,)."</option>";
            } else{
                $options.= "<option data-toggle='tooltip' data-placement='left' value='".$category->id."'>".str_replace(',', ' > ', $category->name,)."</option>";
            }
        }

        return $options;
    }

    function categoryProductCheck(){
        $category_id = request()->category_id;
        $products = \DB::connection('marketplace')
            ->table('marketplace_products')
            ->where('category_id', $category_id)
            ->groupBy('country_id')
            ->selectRaw('country_id, COUNT(*) as total')
            ->get();

        $countries = Country::select('id', 'name', 'country_shortcut')->whereIn('id', $products->pluck('country_id')->toArray() ?? [])->pluck('name', 'id');
        return view('marketplace.category.category_delete_modal', compact('products', 'countries', 'category_id'));
    }
//sub-cat delete
    public function deleteCategory(){
        $id = array(request()->category_id);
        ini_set('max_execution_time', '0');
        ini_set('memory_limit',-1);
        $products = Product::select('id', 'image')->with('MpCoreDrmTransferProduct:marketplace_product_id,drm_product_id,user_id', 'core_products:marketplace_product_id,id,user_id')->whereIn('category_id',$id)->get();
        foreach ($products->chunk(100) as $product) {
            dispatch(new BulkMarketplaceProductDelete($product));

        }
        Category::whereIn('id',$id)->delete();
        ApiCategory::whereIn('mp_category_id', $id)->update(['mp_category_id' => '', 'is_complete' => 0]);
        return response()->json(['message_type' => 'success', 'message' => 'Category Deleted successfully']);
    }

    public function getFilterElementsforProductOverview($element, $value = null){
        $searchCategory = session('filter_by_select');
        $searchCategoryId = session('filter_by_input');
        $isSupplier = CRUDBooster::isSupplier();

        $product_query = \App\Models\Marketplace\Product::with('productBrand');

        $parent_category_id = session()->get('filter_category_is_main_'.CRUDBooster::myParentId());
        $category_id = session()->get('filter_category_is_sub_'.CRUDBooster::myParentId());
        if ($parent_category_id || $category_id) {
            $category_ids = $parent_category_id 
                ? Category::where('parent_id', $parent_category_id)->pluck('id')->toArray() 
                : [$category_id];

            $product_query->whereIn('category_id', $category_ids);
        }

        if($element == 'delivery_company_id'){
            $supplier_ids = $product_query->distinct()->select('delivery_company_id')->pluck('delivery_company_id');
            $supplier_list = \App\DeliveryCompany::whereIn('id', $supplier_ids)
                ->orderBy('name', 'ASC')
                ->pluck('name', 'id');

            $options = "<select name='search_by_query' class='form-control input-sm select2' style='width: 150px;' id='search-by-query'>";
            $options.= "<option value='all' selected>All " .__('Supplier')."</option>";

            foreach ($supplier_list as $id => $name) {
                $selected = $value == $id ? 'selected' : '';
                $options .= "<option $selected data-toggle='tooltip' data-placement='left' value='$id'>$name</option>";
            }

            $options .= "</select>";
            return $options;
        }

        else if($element == 'brand'){
            if ($isSupplier) {
                $product_query->where('supplier_id', CRUDBooster::myParentId());
            }

            $brand_ids = $product_query->distinct()->select('brand')->pluck('brand');
            $brand_names = \App\Models\Marketplace\ProductBrand::whereIn('id', $brand_ids)
                ->orderBy('brand_name', 'ASC')
                ->pluck('brand_name', 'id');

            $options = "<select name='search_by_query' class='form-control input-sm select2' style='width: 150px;' id='search-by-query'>";
            $options.= "<option value='all' selected>All " .__("marketplace.".$element)."</option>";

            foreach ($brand_names as $id => $name) {
                $selected = $value == $id ? 'selected' : '';
                $options .= "<option $selected data-toggle='tooltip' data-placement='left' value='$id'>$name</option>";
            }

            $options .= "</select>";
            return $options;

        } else if($searchCategory == 'category_id'){
            if(\CRUDBooster::isSuperAdmin() || \CRUDBooster::isDropMatrix() || CRUDBooster::hasDropmatixMpSupport()){
                if (strpos($searchCategoryId, "_cat")) {
                    $categoryId = explode("main_cat_", $searchCategoryId)[1];
                    $categoryList = Category::where('parent_id', $categoryId)->whereNotNull('parent_id')->pluck('id')->toArray();
                    Session::put('filter_category_is_main_' . CRUDBooster::myParentId(), $categoryId);
                    Session::forget('filter_category_is_sub_' . CRUDBooster::myParentId());
                    $elements = \App\Models\Marketplace\Product::whereIn('category_id', $categoryList)
                        ->where($element, '!=', null)
                        ->orderBy($element,'ASC')
                        ->distinct()
                        ->pluck($element);
                } else {
                    Session::put('filter_category_is_sub_' . CRUDBooster::myParentId(), $searchCategoryId);
                    Session::forget('filter_category_is_main_' . CRUDBooster::myParentId());
                    $elements = \App\Models\Marketplace\Product::where('category_id', $searchCategoryId)
                        ->where($element, '!=', null)
                        ->orderBy($element,'ASC')
                        ->distinct()
                        ->pluck($element);
                }
            } else {
                $elements = \App\Models\Marketplace\Product::where('category_id', $searchCategoryId)->where($element, '!=', null)->orderBy($element,'ASC')->distinct()->pluck($element);
            }
        } else {
            $elements = \App\Models\Marketplace\Product::where($element, '!=', null)->orderBy($element,'ASC')->distinct()->pluck($element);
        }
        $options = "<select name='search_by_query' class='form-control input-sm select2' style='width: 150px;' id='search-by-query'>";
        $options.= "<option value='all' selected>All " .__("marketplace.".$element == 'shipping_cost' ? "Shipping_Cost" : $element)."</option>";
        foreach($elements as $element) {
            if($element){
                $selected = $value == $element ? 'selected' : '';
                $options.= "<option $selected data-toggle='tooltip' data-placement='left' value='".$element."'>".$element."</option>";
            }
        }
        $options.= "</select>";
        return $options;

    }

    public function getFilterElementsOfCategory($catId, $price,$rangeBy, $element){

        $price_range = explode(',',$price);
        $minPrice = $price_range[0];
        $maxPrice = $price_range[1];

        $range_by = $this->convertRangeBy($rangeBy);
        if($range_by == '(uvp - vk_price)/vk_price*100'){
            $brands = \App\Models\Marketplace\Product::where('category_id', $catId)
                ->where('status',ProductStatus::ACTIVE)
                // ->where('stock', '>', 1)
                ->whereRaw("((uvp - vk_price)/vk_price*100) >= $minPrice")
                ->whereRaw("((uvp - vk_price)/vk_price*100) <= $maxPrice")
                ->distinct()
                ->orderBy($element, 'asc')
                ->pluck($element)
                ->toArray();
        }else{
            $brands = \App\Models\Marketplace\Product::where('category_id', $catId)
                ->where('status',ProductStatus::ACTIVE)
                // ->where('stock', '>', 1)
                ->where($range_by, '>=', intval($minPrice))
                ->where($range_by, '<=', intval($maxPrice))
                ->distinct()
                ->orderBy($element, 'asc')
                ->pluck($element)
                ->toArray();
        }

        $options = "<option value='all' selected>All " .__("marketplace.".$element)."</option>";
        foreach(array_filter($brands) as $brand) {
            $options.= "<option data-toggle='tooltip' data-placement='left' value='".$brand."'>".$brand."</option>";
        }
        return $options;

    }

    public function autoTransferDependencies($user_id = null, $auto_tranfer_ids = []){

        $one_one_sync = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->get();
        if($one_one_sync[0]->one_one_sync == 1){
            app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user_id);
        }

        if(professionalOrHigher($user_id)){
            AutoTransfer::dispatch(array_values($auto_tranfer_ids), $user_id, "de");
        }
    }

    public function ProductBrandUpdate($productIds,$brandId,$selected_brand_name,$selected_brand_logo){

        try{

            $products = Product::with('core_products:marketplace_product_id,id,user_id')
                    ->select('id','brand')
                    ->whereIn('id', $productIds)
                    ->where('brand','!=',$brandId)
                    ->get();

            Product::whereIn('id', $productIds)->where('brand','!=',$brandId)->update(['brand'=> $brandId]);

            foreach($products as $product){

                $core_products = $product->core_products;

                if(count($core_products) > 0){
                    foreach($core_products as $core_product){
                        $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $selected_brand_name) . '%')
                                    ->where('user_id',$core_product->user_id)->first();
                        if ( $drmBrand ) {
                            $drmBrand = $drmBrand;
                        } else {
                            if(!empty($selected_brand_name)){
                                $drmBrand = DropmatixProductBrand::create([
                                    'brand_name' =>  $selected_brand_name,
                                    'user_id' =>$core_product->user_id,
                                    'brand_logo'=>([$selected_brand_logo] ?? [])
                                ]);
                            }
                        }

                        $updateableColumns['brand'] = $drmBrand->id;
                        app(\App\Services\DRMProductService::class)->update($core_product->id, $updateableColumns);
                    }
                }
            }

        }catch(Exception $e){
            info($e->getMessage());
        }

    }

    public function assignMarketplaceProductCalculation($product_ids, $calculation){

        $channelProducts = Product::with('core_products')->whereIn('id', $product_ids)->get();

        foreach ($channelProducts as $channelProduct) {
            $old_vk_price = $channelProduct->vk_price;
            $new_vk_price = $this->calculatePrice($channelProduct->ek_price, $calculation, $channelProduct->uvp, $channelProduct->shipping_cost);

            if (round($new_vk_price, 2) != round($old_vk_price, 2) && $new_vk_price > 0) {
                $channelProduct->calculation_id = $calculation->id ?? null;
                $channelProduct->vk_price = $new_vk_price;
                $channelProduct->old_vk_price = $old_vk_price;
                $channelProduct->vk_price_updated_at  = \Carbon\Carbon::now();
                $channelProduct->update();
            }

            if(!blank($channelProduct->core_products)) {
                $this->calculatedPriceSyncToDrm($channelProduct->core_products, $new_vk_price);
            }
        }

    }

    public function calculatePrice($price, $calculation, $uvp = 0, $shipping_cost = 0)
    {
        if ($calculation->dynamic_shipping_cost && $shipping_cost > 0) {
            $calculation_shipping_cost = $shipping_cost;
        } else {
            $calculation_shipping_cost = $calculation->shipping_cost;
        }

        try {
            if ($calculation->uvp) {
                $price = $uvp;
            } else {
                $price = $price + $price
                    * ($calculation->profit_percent / 100)
                    + $calculation_shipping_cost
                    + $calculation->additional_charge;

                if ($calculation->round_scale != null) {
                    $prices = explode('.', $price);
                    if ($prices[1] != 0) {
                        $price = $prices[0] + $calculation->round_scale;
                    }
                }
            }
            return (float)str_replace(',', '', number_format($price, 2));
        } catch (\Throwable $th) {
            return $price;
        }
    }

    public function calculatedPriceSyncToDrm($products, $price)
    {
        $mp_vk_price = $price;
        foreach($products as $product){
            $mp_price_markup_discount = !blank($product->mp_price_markup) ? ($product->mp_price_markup * $mp_vk_price) / 100 : 0;
            $cat_offer_discount = !blank($product->mp_category_offer) ? ($product->mp_category_offer * $mp_vk_price) / 100 : 0;
    
            $mp_new_vk_price = round($mp_vk_price + $mp_price_markup_discount - $cat_offer_discount, 2);
    
            if (round($product->ek_price, 2)  != $mp_new_vk_price) {
                $updateableColumns['ek_price'] = $mp_new_vk_price;
                app(\App\Services\DRMProductService::class)->update($product->id, $updateableColumns);
            }
        }
    }

    public function getTax(){
        $tax = \App\Country::select('name','country_shortcut','tax_rate','reduced_tax_rate')->orderBy('name')->get();
        return response()->json($tax);
    }

    public function transferUnlimitedProductsToDrm($tranferable_ids = [], $user_id = null, $category_id = null){
        $products = Product::with('additionalInfo','productBrand')->whereIn('id', $tranferable_ids)->where('status', 1)->get();
        // $ean_exist = DrmProduct::select('ean')->where('user_id', $user_id ?? CRUDBooster::myParentId())->whereIn('ean', $products->pluck('ean')->toArray())->pluck('ean')->toArray();
        // $products = $products->filter(function ($value, $key) use ($ean_exist) {
        //     return !in_array($value['ean'], $ean_exist);
        // });

        if(count($products)){
            $mp_core_data = [];
            $auto_transfer_ids = [];
            $drm_product_categories = [];
            $autoTransferSubscription = null;
            $total_transfered = 0;
            $trial_checked = 0;

            if($category_id){
                $autoTransferSubscription = AutoTransferSubscription::where(['user_id'=>$user_id,'category_id'=>$category_id])->first();
                $auto_transfer_ids = $autoTransferSubscription->transfered_product_ids ?? [];
                $total_transfered = $autoTransferSubscription->transfered_products_count ?? 0;
            }

            $deliveryCompanyRow = [
                'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                'name'          => 'Dropmatix Systema SL',
                'contact_name'  => 'Dropmatix Systema SL',
                'zip'           => '07200',
                'state'         => 'FELANITX',
                'country_id'    => 8,
                'email'         => '<EMAIL>',
                'address'       => 'C/ HORTS, 33',
                'note'          => 'Marketplace Supplier',
                'is_marketplace_supplier' => 1,
            ];
            $deliveryCompany = DeliveryCompany::where('user_id', $user_id ?? CRUDBooster::myParentId())
                ->where('name', $deliveryCompanyRow['name'])
                ->where('email', '<EMAIL>')
                ->first();
            if ( !$deliveryCompany ) {
                $deliveryCompanyRow['user_id'] = $user_id ?? CRUDBooster::myParentId();
                $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
            }

            $mp_agreement = DB::table('mp_payment_agreements')
                ->where('user_id', '=', $user_id ?? CRUDBooster::myParentId())
                ->where('type', '=', 1)
                ->select('price_markup')
                ->first();

            foreach($products as $key => $product){
                // Additional columns check
                $manufacturer = null;
                $manufacturer_link = null;
                $manufacturer_id = null;
                $custom_tariff_number = null;
                $shipping_company_id = null;
                $region = null;
                $country_of_origin = null;
                $min_stock = null;
                $min_order = null;
                $gross_weight = null;
                $net_weight = null;
                $product_length = null;
                $product_width = null;
                $product_height = null;
                // $volume = null;
                $packaging_length = null;
                $packaging_width = null;
                $packaging_height = null;
                $item_unit = null;
                $packing_unit = null;
                // $volume_gross = null;

                if( $product->additionalInfo ){
                    $manufacturer = $product->additionalInfo->manufacturer;
                    $manufacturer_link = $product->additionalInfo->manufacturer_link;
                    $manufacturer_id = $product->additionalInfo->manufacturer_id;
                    $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                    $shipping_company_id = $product->additionalInfo->shipping_company_id;
                    $region = $product->additionalInfo->region;
                    $country_of_origin = $product->additionalInfo->country_of_origin;
                    $min_stock = $product->additionalInfo->min_stock;
                    $min_order = $product->additionalInfo->min_order;
                    $gross_weight = $product->additionalInfo->gross_weight;
                    $net_weight = $product->additionalInfo->net_weight;
                    $product_length = $product->additionalInfo->product_length;
                    $product_width = $product->additionalInfo->product_width;
                    $product_height = $product->additionalInfo->product_height;
                    // $volume = $product->additionalInfo->volume;
                    $packaging_length = $product->additionalInfo->packaging_length;
                    $packaging_width = $product->additionalInfo->packaging_width;
                    $packaging_height = $product->additionalInfo->packaging_height;
                    $item_unit = $product->additionalInfo->item_unit;
                    $packing_unit = $product->additionalInfo->packing_unit;
                    // $volume_gross = $product->additionalInfo->volume_gross;
                }

                // $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                $country_id = $product->country_id ?? 1; 
                $lang = app('App\Services\UserService')->getProductLanguage($country_id);

                $discount = 0.0;
                if ($product->category_id && !in_array($product->supplier_id, [2817])) {
                    $category = Category::find($product->category_id);
                    if ($category) {
                        $drmCategory = DrmCategory::firstOrCreate(
                            ['category_name_'.$lang => $category->name, 'user_id' => $user_id],
                            ['country_id' => $country_id]
                        );

                        if ($category->start_date <= now() && $category->end_date >= now() && $category->is_offer_active) {
                            $discount = $category->discount_percentage;
                        }
                    }
                }

                if(in_array($product->supplier_id, [2817])) {
                    $category = DB::table('channel_user_categories')->where('id', $product->category_id)->first();
                    $drmCategory = DrmCategory::where('category_name_'.$lang, $category->category_name)->where('user_id',$user_id)->first();
                    if ( !$drmCategory ) {
                        $drmCategory = DrmCategory::create([
                            'category_name_'.$lang => $category->category_name,
                            'user_id' => $user_id,
                            'country_id' => $country_id,
                        ]);
                    }
                }

                $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                    $shippingCost = $product->shipping_cost > 0 ? $product->shipping_cost : 9.68;
                } else {
                    $shippingCost = $product->shipping_cost;
                }

                if ($product->real_shipping_cost == 0 && isEnterpriceOrTrialUser($user_id)) {
                    $shippingCost = 0.0;
                }

                if ( $product->brand ) {
                    $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                    $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id', $user_id ?? CRUDBooster::myParentId())->first();
                    if ( !$drmBrand ) {
                        if(!empty($mpBrandName)){
                            $drmBrand = DropmatixProductBrand::create([
                                'brand_name' =>  $mpBrandName,
                                'user_id' => $user_id ?? CRUDBooster::myParentId(),
                                'brand_logo'=>($product->productBrand->brand_logo ?? [])
                            ]);
                        }
                    }
                }

                if( $product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
                    $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                }

                $price_markup = $mp_agreement ? $mp_agreement->price_markup : 0.0;

                $drm_product_info           = [
                    'user_id'               => $user_id ?? CRUDBooster::myParentId(),
                    'country_id'            => $country_id,
                    'language_id'           => null,
                    'name'                  => $product->brand .' '. $product->name,
                    'item_number'           => $product->item_number,
                    'ean'                   => $product->ean,
                    'additional_eans'       => json_encode($product->additional_eans),
                    'image'                 => $product->image,
                    'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id ?? CRUDBooster::myParentId(), false, $discount, $product->api_id),
                    'vk_price'              => 0.00,
                    'vat'                   => $product->vat ?? null,
                    'tax_type'              => $product->tax_type ?? 1,
                    'stock'                 => (empty($stock)) ? 0 : $stock,
                    'category'              => $drmCategory->id ?? null,
                    'ean_field'             => 1,
                    'item_weight'           => $product->item_weight ?? null,
                    'item_size'             => $product->item_size ?? null,
                    'item_color'            => $product->item_color ?? null,
                    'note'                  => $product->note ?? null,
                    'production_year'       => $product->production_year ?? null,
                    'brand'                 => $drmBrand ? $drmBrand->id : null,
                    'materials'             => $product->materials ?? null,
                    'tags'                  => $product->tags ?? null,
                    'update_enabled'        => $product->update_enabled ?? null,
                    'status'                => $product->status ?? null,
                    'gender'                => $product->gender ?? null,
                    'uvp'                   => $product->uvp ? $product->uvp + (($product->uvp * $price_markup)/100) : 0.00,
                    'title'                 => [
                                                $lang => $product->name ?? null,
                                            ],
                    'update_status'         => makeUpdateStatusJson(),
                    // 'short_description' => json_encode($product->description),
                        'description' => [
                            $lang => $product->description ?? null,
                        ],
                    'delivery_company_id'     => $deliveryCompany->id,
                    'marketplace_supplier_id' => $product->supplier_id ?? '',
                    'marketplace_product_id'  => $product->id,
                    'marketplace_shipping_method' => $product->shipping_method,
                    'shipping_cost'               => $shippingCost ?? 0,
                    'delivery_days'               => $product->delivery_days,
                    'industry_template_data'      => json_encode($product->industry_template_data),
                    'product_type'                => $category_id ? \App\Enums\ProductType::MP_AUTO_TRANSFER : \App\Enums\ProductType::MP_MANUAL_TRANSFER,
                    'mp_category_offer'           => $discount,
                    // Additional columns
                    'manufacturer'                => $manufacturer,
                    'manufacturer_link'           => $manufacturer_link,
                    'manufacturer_id'             => $manufacturer_id,
                    'custom_tariff_number'        => $custom_tariff_number,
                    'shipping_company_id'         => $shipping_company_id ?? 8,
                    'region'                      => $region,
                    'country_of_origin'           => $country_of_origin,
                    'min_stock'                   => $min_stock,
                    'min_order'                   => $min_order,
                    'gross_weight'                => $gross_weight,
                    'net_weight'                  => $net_weight,
                    'product_length'              => $product_length,
                    'product_width'               => $product_width,
                    'product_height'              => $product_height,
                    // 'volume'                      => $volume,
                    'packaging_length'            => $packaging_length,
                    'packaging_width'             => $packaging_width,
                    'packaging_height'            => $packaging_height,
                    'item_unit'                   => $item_unit,
                    'packaging_unit'              => $packing_unit,
                    // 'volume_gross'                => $volume_gross,
                    'mp_price_markup'             => $price_markup,
                    'marketplace_delivery_company_id'=> $product->delivery_company_id,
                    'im_handel'                   => $product->im_handel,
                ];

                $drmProduct = DrmProduct::create($drm_product_info);

                if ($trial_checked == 0) {
                    app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial($user_id ?? \CRUDBooster::myParentId());
                    $trial_checked = 1;
                }

                $drm_product_categories[] = [
                    'product_id' => $drmProduct->id,
                    'category_id' => $drmProduct->category,
                    'country_id' => $drmProduct->country_id,
                ];
                $mp_core_data[] = [
                    'drm_product_id' => $drmProduct->id,
                    'mp_product_ean' => $drmProduct->ean,
                    'marketplace_product_id' => $drmProduct->marketplace_product_id,
                    'user_id' => $drmProduct->user_id,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];

                $auto_transfer_ids[$drmProduct->marketplace_product_id] = $drmProduct->id;
            }

            // DB::table('drm_products')->insert($drm_product_info);
            // $drmProducts = DrmProduct::create($drm_product_info);

            if(!empty($drm_product_categories) && !empty($mp_core_data)) {
                DB::table('drm_product_categories')->insert($drm_product_categories);
                MpCoreDrmTransferProduct::insert($mp_core_data);

                if($category_id){
                    $autoTransferSubscription->update(['transfered_product_ids'=>$auto_transfer_ids, 'transfered_products_count'=>$total_transfered + $key + 1]);
                }

                $one_one_sync = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->get();
                if($one_one_sync[0]->one_one_sync == 1){
                    app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user_id);
                }

                if(professionalOrHigher($user_id)){
                    AutoTransfer::dispatch(array_values($auto_transfer_ids),$user_id,$lang ?? "de");
                }

                return response()->json([
                    'status'      => true,
                    'message'     => 'Successfully transferred '.($key+1).' Products',
                ]);
            }
        } else {
            return response()->json([
                'status'    => false,
                'message'   => 'These ean products already exist in your account. You can not transfer these products.',
            ]);
        }

    }

    public function transferTarifLimitedProductsToDrm($tranferable_ids = [], $user_id = null, $category_id = null){
        $products = Product::with('additionalInfo','productBrand')->whereIn('id', $tranferable_ids)->where('status', 1)->get();
        // $ean_exist = DrmProduct::select('ean')->where('user_id', $user_id ?? CRUDBooster::myParentId())->whereIn('ean', $products->pluck('ean')->toArray())->pluck('ean')->toArray();
        // $products = $products->filter(function ($value, $key) use ($ean_exist) {
        //     return !in_array($value['ean'], $ean_exist);
        // });

        if(count($products)){
            $mp_core_data = [];
            $auto_transfer_ids = [];
            $drm_product_categories = [];
            $autoTransferSubscription = null;
            $total_transfered = 0;
            $trial_checked = 0;

            if($category_id){
                $autoTransferSubscription = AutoTransferSubscription::where(['user_id'=>$user_id,'category_id'=>$category_id])->first();
                $auto_transfer_ids = $autoTransferSubscription->transfered_product_ids ?? [];
                $total_transfered = $autoTransferSubscription->transfered_products_count ?? 0;
            }

            $deliveryCompanyRow = [
                'user_id'       => \App\Enums\Apps::DROPMATIX_ID,
                'name'          => 'Dropmatix Systema SL',
                'contact_name'  => 'Dropmatix Systema SL',
                'zip'           => '07200',
                'state'         => 'FELANITX',
                'country_id'    => 8,
                'email'         => '<EMAIL>',
                'address'       => 'C/ HORTS, 33',
                'note'          => 'Marketplace Supplier',
                'is_marketplace_supplier' => 1,
            ];
            $deliveryCompany = DeliveryCompany::where('user_id', $user_id ?? CRUDBooster::myParentId())
                ->where('name', $deliveryCompanyRow['name'])
                ->where('email', '<EMAIL>')
                ->first();
            if ( !$deliveryCompany ) {
                $deliveryCompanyRow['user_id'] = $user_id ?? CRUDBooster::myParentId();
                $deliveryCompany = DeliveryCompany::create($deliveryCompanyRow);
            }

            $importProduct = app(\App\Http\Controllers\AdminDrmImportsController::class)->importProductCheck($user_id ?? \CRUDBooster::myParentId());
            $transferLimit = $importProduct ? $importProduct['product_amount'] : 0;

            $mp_agreement = DB::table('mp_payment_agreements')
                    ->where('user_id', '=', $user_id ?? CRUDBooster::myParentId())
                    ->where('type', '=', 1)
                    ->select('price_markup')
                    ->first();

            foreach($products as $key => $product){
                if($transferLimit > $key) {
                    // Additional columns check
                    $manufacturer = null;
                    $manufacturer_link = null;
                    $manufacturer_id = null;
                    $custom_tariff_number = null;
                    $shipping_company_id = null;
                    $region = null;
                    $country_of_origin = null;
                    $min_stock = null;
                    $min_order = null;
                    $gross_weight = null;
                    $net_weight = null;
                    $product_length = null;
                    $product_width = null;
                    $product_height = null;
                    // $volume = null;
                    $packaging_length = null;
                    $packaging_width = null;
                    $packaging_height = null;
                    $item_unit = null;
                    $packing_unit = null;
                    // $volume_gross = null;

                    if( $product->additionalInfo ){
                        $manufacturer = $product->additionalInfo->manufacturer;
                        $manufacturer_link = $product->additionalInfo->manufacturer_link;
                        $manufacturer_id = $product->additionalInfo->manufacturer_id;
                        $custom_tariff_number = $product->additionalInfo->custom_tariff_number;
                        $shipping_company_id = $product->additionalInfo->shipping_company_id;
                        $region = $product->additionalInfo->region;
                        $country_of_origin = $product->additionalInfo->country_of_origin;
                        $min_stock = $product->additionalInfo->min_stock;
                        $min_order = $product->additionalInfo->min_order;
                        $gross_weight = $product->additionalInfo->gross_weight;
                        $net_weight = $product->additionalInfo->net_weight;
                        $product_length = $product->additionalInfo->product_length;
                        $product_width = $product->additionalInfo->product_width;
                        $product_height = $product->additionalInfo->product_height;
                        // $volume = $product->additionalInfo->volume;
                        $packaging_length = $product->additionalInfo->packaging_length;
                        $packaging_width = $product->additionalInfo->packaging_width;
                        $packaging_height = $product->additionalInfo->packaging_height;
                        $item_unit = $product->additionalInfo->item_unit;
                        $packing_unit = $product->additionalInfo->packing_unit;
                        // $volume_gross = $product->additionalInfo->volume_gross;
                    }

                    // $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                    $country_id = $product->country_id ?? 1; 
                    $lang = app('App\Services\UserService')->getProductLanguage($country_id);

                    $discount = 0.0;
                    if ($product->category_id && !in_array($product->supplier_id, [2817])) {
                        $category = Category::find($product->category_id);
                        if ($category) {
                            $drmCategory = DrmCategory::firstOrCreate(
                                ['category_name_'.$lang => $category->name, 'user_id' => $user_id],
                                ['country_id' => $country_id]
                            );
    
                            if ($category->start_date <= now() && $category->end_date >= now() && $category->is_offer_active) {
                                $discount = $category->discount_percentage;
                            }
                        }
                    }

                    if(in_array($product->supplier_id, [2817])) {
                        $category = DB::table('channel_user_categories')->where('id', $product->category_id)->first();
                        $drmCategory = DrmCategory::where('category_name_'.$lang, $category->category_name)->where('user_id',$user_id)->first();
                        if ( !$drmCategory ) {
                            $drmCategory = DrmCategory::create([
                                'category_name_'.$lang => $category->category_name,
                                'user_id' => $user_id,
                                'country_id' => $country_id,
                            ]);
                        }
                    }

                    $stock = ($product->shipping_method == 1) ? $product->stock : $product->internel_stock;

                    if ($product->shipping_method==\App\Enums\Marketplace\ShippingMethod::FULFILLment) {
                        $shippingCost = $product->shipping_cost > 0 ? $product->shipping_cost : 9.68;
                    } else {
                        $shippingCost = $product->shipping_cost;
                    }

                    if ($product->real_shipping_cost == 0 && isEnterpriceOrTrialUser($user_id)) {
                        $shippingCost = 0.0;
                    }

                    if ( $product->brand ) {
                        $mpBrandName = (is_numeric($product->brand) ? $product->productBrand->brand_name : $product->brand) ?? '';
                        $drmBrand = DropmatixProductBrand::where(DB::raw('UPPER(brand_name)'), 'like', '%' . strtoupper( $mpBrandName) . '%')->where('user_id', $user_id ?? CRUDBooster::myParentId())->first();
                        if ( !$drmBrand ) {
                            if(!empty($mpBrandName)){
                                $drmBrand = DropmatixProductBrand::create([
                                    'brand_name' =>  $mpBrandName,
                                    'user_id' => $user_id ?? CRUDBooster::myParentId(),
                                    'brand_logo'=>($product->productBrand->brand_logo ?? [])
                                ]);
                            }
                        }
                    }

                    if( $product->offer_start_date <= now() && $product->offer_end_date >= now() && $product->is_offer_active == 1){
                        $discount += $product->discount_percentage ? ($product->discount_percentage - $product->discount_percentage/4) : 0.0;
                    }

                    $price_markup = $mp_agreement ? $mp_agreement->price_markup : 0.0;

                    $drm_product_info           = [
                        'user_id'               => $user_id ?? CRUDBooster::myParentId(),
                        'country_id'            => $country_id,
                        'language_id'           => null,
                        'name'                  => $product->brand .' '. $product->name,
                        'item_number'           => $product->item_number,
                        'ean'                   => $product->ean,
                        'additional_eans'       => json_encode($product->additional_eans),
                        'image'                 => $product->image,
                        'ek_price'              => userWiseVkPriceCalculate($product->vk_price ?? 0.0, $user_id ?? CRUDBooster::myParentId(), false, $discount, $product->api_id),
                        'vk_price'              => 0.00,
                        'vat'                   => $product->vat ?? null,
                        'tax_type'              => $product->tax_type ?? 1,
                        'stock'                 => (empty($stock)) ? 0 : $stock,
                        'category'              => $drmCategory->id ?? null,
                        'ean_field'             => 1,
                        'item_weight'           => $product->item_weight ?? null,
                        'item_size'             => $product->item_size ?? null,
                        'item_color'            => $product->item_color ?? null,
                        'note'                  => $product->note ?? null,
                        'production_year'       => $product->production_year ?? null,
                        'brand'                 => $drmBrand ? $drmBrand->id : null,
                        'materials'             => $product->materials ?? null,
                        'tags'                  => $product->tags ?? null,
                        'update_enabled'        => $product->update_enabled ?? null,
                        'status'                => $product->status ?? null,
                        'gender'                => $product->gender ?? null,
                        'uvp'                   => $product->uvp ? $product->uvp + (($product->uvp * $price_markup)/100) : 0.00,
                        'title'                 => [
                                                    $lang => $product->name ?? null,
                                                ],
                        'update_status'         => makeUpdateStatusJson(),
                        // 'short_description' => json_encode($product->description),
                            'description' => [
                                $lang => $product->description ?? null,
                            ],
                        'delivery_company_id'     => $deliveryCompany->id,
                        'marketplace_supplier_id' => $product->supplier_id ?? '',
                        'marketplace_product_id'  => $product->id,
                        'marketplace_shipping_method' => $product->shipping_method,
                        'shipping_cost'               => $shippingCost ?? 0,
                        'delivery_days'               => $product->delivery_days,
                        'industry_template_data'      => json_encode($product->industry_template_data),
                        'product_type'                => $category_id ? \App\Enums\ProductType::MP_AUTO_TRANSFER : \App\Enums\ProductType::MP_MANUAL_TRANSFER,
                        'mp_category_offer'           => $discount,
                        // Additional columns
                        'manufacturer'                => $manufacturer,
                        'manufacturer_link'           => $manufacturer_link,
                        'manufacturer_id'             => $manufacturer_id,
                        'custom_tariff_number'        => $custom_tariff_number,
                        'shipping_company_id'         => $shipping_company_id ?? 8,
                        'region'                      => $region,
                        'country_of_origin'           => $country_of_origin,
                        'min_stock'                   => $min_stock,
                        'min_order'                   => $min_order,
                        'gross_weight'                => $gross_weight,
                        'net_weight'                  => $net_weight,
                        'product_length'              => $product_length,
                        'product_width'               => $product_width,
                        'product_height'              => $product_height,
                        // 'volume'                      => $volume,
                        'packaging_length'            => $packaging_length,
                        'packaging_width'             => $packaging_width,
                        'packaging_height'            => $packaging_height,
                        'item_unit'                   => $item_unit,
                        'packaging_unit'              => $packing_unit,
                        // 'volume_gross'                => $volume_gross,
                        'mp_price_markup'             => $price_markup,
                        'marketplace_delivery_company_id'=> $product->delivery_company_id,
                        'im_handel'                   => $product->im_handel,

                    ];

                    $drmProduct = DrmProduct::create($drm_product_info);
                    if ($trial_checked == 0) {
                        app(\App\Http\Controllers\AdminDrmImportsController::class)->setImportTrial($user_id ?? \CRUDBooster::myParentId());
                        $trial_checked = 1;
                    }

                    $drm_product_categories[] = [
                        'product_id' => $drmProduct->id,
                        'category_id' => $drmProduct->category,
                        'country_id' => $drmProduct->country_id,
                    ];
                    $mp_core_data[] = [
                        'drm_product_id' => $drmProduct->id,
                        'mp_product_ean' => $drmProduct->ean,
                        'marketplace_product_id' => $drmProduct->marketplace_product_id,
                        'user_id' => $drmProduct->user_id,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];

                    $auto_transfer_ids[$drmProduct->marketplace_product_id] = $drmProduct->id;

                }
            }

            if(!empty($drm_product_categories) && !empty($mp_core_data)) {
                DB::table('drm_product_categories')->insert($drm_product_categories);
                MpCoreDrmTransferProduct::insert($mp_core_data);

                if($category_id){
                    $autoTransferSubscription->update(['transfered_product_ids'=>$auto_transfer_ids, 'transfered_products_count'=>$total_transfered + $key + 1]);
                }

                $one_one_sync = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->get();
                if($one_one_sync[0]->one_one_sync == 1){
                    app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user_id);
                }

                if(professionalOrHigher($user_id)){
                    AutoTransfer::dispatch(array_values($auto_transfer_ids),$user_id,$lang ?? "de");
                }

                return response()->json([
                    'status'      => true,
                    'message'     => 'Successfully transferred '.($key+1).' Products',
                ]);
            }
        } else {
            return response()->json([
                'status'    => false,
                'message'   => 'These ean products already exist in your account. You can not transfer these products.',
            ]);
        }

    }

    public function mpToDrmProductsAutoTransfer($auto_tranfer_ids, $user_id, $category_id){
        return $this->transferAllFilteredProductsToDrm (array_map('intval', explode(',', $auto_tranfer_ids)), [], $category_id, $user_id);
    }

    public function deleteSelectedParentCategory($parentCategoryId, $user_id){
        try{
            $all_categories = Category::where('parent_id', $parentCategoryId)->pluck('id')->toArray();
            $user_access_status = UserAccess::where('user_id', $user_id)->first() ?? [];
            // update UserAccess process
            $update_accessable_parent_categories = array_diff($user_access_status->accessable_parent_categories ?? [], [$parentCategoryId] ?? []);
            $update_accessable_categories = array_diff($user_access_status->accessable_categories ?? [],$all_categories ??[]);

            $check_accessable_categories = $user_access_status['check_accessable_categories'] ?? [];
            // update set_by_admin
            foreach ($check_accessable_categories as &$item) {
                if ($item["accessable_categories"] === $parentCategoryId) {
                    $item["set_by_admin"] = 1;
                }
            }

            AutoTransferSubscription::where('user_id', $user_id)->whereIn('category_id', $all_categories ?? [])->update(
                ['end_date' => now(),'status' => 0,]
            );
            $languageId = app('App\Services\UserService')->getProductCountry($user_id);
            $lang = app('App\Services\UserService')->getProductLanguage($languageId);
            $mp_product_ids = Product::whereIn('category_id', $all_categories ?? [])->pluck('id')->toArray() ?? [];

            foreach (array_chunk($mp_product_ids, 500) as $chunkIds) {
                $drm_product_id = DrmProduct::whereIn('marketplace_product_id', $chunkIds)
                                ->where('user_id', $user_id)->pluck('id')->toArray();

                if (!empty($drm_product_id)) {
                    Log::info('DestroyProduct job dispatch');
                    DestroyProduct::dispatch($drm_product_id, $user_id, $lang, $languageId);
                }
            }

            UserAccess::where('user_id', $user_id)->update([
                'accessable_parent_categories' => array_values($update_accessable_parent_categories),
                'accessable_categories'        => array_values($update_accessable_categories),
                'check_accessable_categories'  => array_values($check_accessable_categories)
            ]);

        }catch(\Exception $e){
            Log::info(["Delete Selected ParentCategory failed", $e->getMessage()]);
        }
    }

    public function backupProductsMpToDrmTransfer($auto_tranfer_ids, $user_id){
        return $this->transferAllFilteredProductsToDrm (array_map('intval', explode(',', $auto_tranfer_ids)), [], null, $user_id);
    }

    public function customerBulkParentCategoryRemove(){
        $user_id = request()->user_id ?? CRUDBooster::myParentId();
        $user_access_status = UserAccess::where('user_id', $user_id)->select('id', 'accessable_categories')->first();
        if ($user_access_status) {
            foreach (array_chunk($user_access_status->accessable_categories, 50) as $categoryIds) {
                dispatch(new CustomerBulkParentCategoryRemove($categoryIds, $user_id));
            }
            $user_access_status->update([
                'accessable_parent_categories' => [],
                'accessable_categories'        => [],
                'check_accessable_categories'  => []
            ]);
            cache()->forget('UserCategoryAccess_' . CRUDBooster::myParentId());
        }
    }

    public function marketplaceProductRemoveFromDrm($mp_product_id = [], $user_id){
        $drm_product_id = MpCoreDrmTransferProduct::whereIn('marketplace_product_id', $mp_product_id)
                          ->where('user_id', $user_id)->pluck('drm_product_id')->toArray();
        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);
        if (!empty($drm_product_id)) {
            foreach (array_chunk($drm_product_id, 200) as $chunkIds) {
                DestroyProduct::dispatch($chunkIds, $user_id, $lang, $languageId);
            }
        }
    }

    public function exclusiveCategoryAdded(){

        $categoryId = request()->category_id;
        $selectedUsers = request()->selected_users ?? [];
        $category = Category::find($categoryId);
        if (!$category) {
            return response()->json([
                'status' => false,
                'message' => 'Category not found',
            ]);
        }

        $exclusiveStatus = count($selectedUsers) > 0 ? 1 : 0;
        $category->update([
            'exclusive_user_id' => $exclusiveStatus === 1 ? $selectedUsers : null,
            'exclusive_status' => $exclusiveStatus,
        ]);

        if ($category) {
            $message = $exclusiveStatus === 1
                ? __('marketplace.exclusive_category_assign_success_msg')
                : __('marketplace.exclusive_category_remove_msg');

            return response()->json([
                'status' => true,
                'message' => $message,
            ]);
        } else {
            return response()->json([
                'status' => false,
                'message' => 'Something went wrong!',
            ]);
        }
    }

    public function mpCacheClear($key)
    {
        Cache::forget($key);
    }
}
