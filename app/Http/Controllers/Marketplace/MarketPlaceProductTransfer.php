<?php

namespace App\Http\Controllers\Marketplace;

use App\Jobs\ProcessCPTransfer;
use App\Services\ProductApi\Services\Countdown;
use App\Services\ProductApi\Services\Rainforest;
use App\Models\Product\AnalysisProduct;
use App\MarketplaceProducts;
use App\Services\ProductApi\Services\ProductApiInterface;
use App\Models\Product\CPAnalysisRequest;
use App\Services\ProductApi\Services\GoogleShopping;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class MarketPlaceProductTransfer
{
     private array $analysisService = [
		Countdown::class,
		Rainforest::class,
        GoogleShopping::class,
	];

     public function transferToAnalysis($user_id, array $product_ids)
     {

          $id_exists = AnalysisProduct::whereIntegerInRaw('product_id', $product_ids)
          ->where('user_id', $user_id)->pluck('product_id')->toArray();

          $tranferable_ids = array_diff($product_ids, $id_exists);
          if(empty($tranferable_ids)) throw new \Exception('Products already exported to analysis. Please select new products.');

          $products_ean = MarketplaceProducts::whereIntegerInRaw('id', $tranferable_ids)
          ->pluck('ean')
          ->toArray();

          $ean_exists = AnalysisProduct::whereIn('ean', $products_ean)
          ->where('user_id', $user_id)
          ->pluck('ean')
          ->toArray();

          $products = MarketplaceProducts::whereIntegerInRaw('id', $tranferable_ids)
          ->whereNotIn('ean', $ean_exists)
          ->whereNotNull('name')
          ->select('id', 'name as product_title', 'ean', 'image', 'ek_price')
          ->get()
          ->map(function($product) {
               return [
                    'title' => $product->product_title,
                    'product_id' => $product->id,
                    'ean' => trim($product->ean),
                    'image' => $product->image ?? [],
                    'purchase_price' => $product->ek_price,
                    'source' => 2,
               ];
          })->toArray();

          return $this->send($user_id, $products);
     }

     private function send($user_id, array $products, $override = false)
     {
          $analysisService = $this->analysisService;
          $requests = [];

        //   foreach($analysisService as $service)
        //   {
        //        $analysisApi = new $service;
        //        $driver_name = $analysisApi->driver();
        //        $requests[$driver_name.'_id'] = $this->sendToAPI($analysisApi, $products, $user_id);
        //   }

          $insert_data = collect($products)->map(function($product) use ($requests, $user_id) {
               $time = Carbon::now();
               $product['user_id'] = $user_id;
               $product['created_at'] = $time;
               $product['updated_at'] = $time;
               return array_merge($product, $requests);
          })->toArray();

          if(!$override){
               AnalysisProduct::insert($insert_data);
          }else{
               foreach ($insert_data as $data){
                    DB::table('analysis_products')
                    ->updateOrInsert(
                         ['ean' => $data['ean']],
                         $data
                    );
               }
          }

          if(!isLocal()){
            ProcessCPTransfer::dispatch($user_id);
          }

          $total_transfered = count($insert_data);
          $s = ($total_transfered > 1) ? 's' : '';

          return [
               'success' => true,
               'message' => "$total_transfered product$s export successfully",
          ];
     }

     private function sendToAPI(ProductApiInterface $analysisApi, array $products, $user_id)
     {
          $collection_id_column = $analysisApi->collectionColumnName();

          $cp_request_model = CPAnalysisRequest::where('user_id', $user_id)->whereNotNull($collection_id_column)->orderBy('id', 'desc')->first();
          $exists = $cp_request_model ? $analysisApi->requestExist($cp_request_model->$collection_id_column) : -1;
          $products_count = count($products);

          //Add request on existing collection
          if($exists >= $products_count && $analysisApi->addProducts($products, $cp_request_model->$collection_id_column) )
          {
               return $cp_request_model->id;

          }else{

               $collection_body = $this->generateCollectionBody($user_id); //Collection body

               #if not enough limit for this collection
               $collection_id = $analysisApi->createCollection($collection_body);

               $cp_request_model = CPAnalysisRequest::create([
                    'user_id' => $user_id,
                    $collection_id_column => $collection_id,
               ]);

               //Add request on collection
               $analysisApi->addProducts($products, $collection_id);
          }

          return $cp_request_model->id;
     }

     private function generateCollectionBody($user_id)
     {
          $interval = DB::table('ca_intervals')->where('user_id', $user_id)->select('duration', 'type')->first();
          $collection_interval_data = [
               "schedule_type" => "daily",
               "schedule_hours" => [9],
          ];

          if($interval && $interval->type)
          {
               $user_interval = $this->dbIntervalFormat($interval->type, $interval->duration);
               $collection_interval_data = $this->generateInterval($user_interval);
          }

          $collection_body = [
               "name" => "Collection - $user_id - " . date('Y-m-d'),
               "enabled" => True,
               "priority" => "normal",
               "notification_as_csv" => True,
               "notification_as_json" => True,
               "include_html" => 'False'
          ];

          return array_merge($collection_body, $collection_interval_data);
     }

     private function dbIntervalFormat($duration_type, $interval_duration)
     {
          if($duration_type === 'hours')
          {
               $value_arr = explode(',', $interval_duration);
               $value = array_map('intval', $value_arr);

               return [
                    'interval' => 'daily',
                    'value' => $value,
               ];
          }

          if($duration_type === 'minutes')
          {
               return [
                    'interval' => 'minutes',
                    'value' => (int)$interval_duration,
               ];
          }
     }

     //generate interval data
     private function generateInterval(array $interval_data)
     {
          if($interval_data['interval'] === 'daily')
          {
               return [
                    "schedule_type" => "daily",
                    "schedule_hours" => $interval_data['value'],
               ];
          }

          if($interval_data['interval'] === 'minutes')
          {
               return [
                    "schedule_type" => "minutes",
                    "schedule_minutes" => $this->minuteValue($interval_data['value']),
               ];
          }

          return false;
     }

     //minutes value
     private function minuteValue($minutes)
     {
          if($minutes === 60)
          {
               return 'every_hour';

          }else if($minutes > 0 && $minutes < 60)
          {
               return 'every_'.$minutes.'_minutes';
          }

          return false;
     }
}
