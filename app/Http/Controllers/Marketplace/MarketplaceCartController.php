<?php

namespace App\Http\Controllers\Marketplace;

use DB;
use App\User;
use Exception;
use App\NewOrder;
use App\NewCustomer;
use App\BillingDetail;
use App\Traits\PlaceOrder;
use App\Http\Controllers\Controller;
use App\Notifications\DRMNotification;
use Illuminate\Support\Facades\Validator;
use App\Models\Marketplace\MarketplaceCart;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Services\DRM\DRMService;
use App\Services\Payment\PaymentTax;

class MarketplaceCartController extends Controller
{
    use PlaceOrder;

    public function fetchCartProducts()
    {
        try {
            $user_id = CRUDBooster::myParentId();
            $data['user'] = User::with('customer_profile')->where('id', $user_id)->first();
            $data['cart_products'] = MarketplaceCart::with('product', 'product.mainCategory')->where('user_id', $user_id)->get();

            $paymentTax = new PaymentTax($user_id, 2455);
            $data['vatNumber'] = $paymentTax->vatNumber() ?? false;
            $data['taxRate']   = $paymentTax->taxRate();

            $data['shipping_discount'] = 0.00;
            $data['isEnterpriceOrTrialUser'] = isEnterpriceOrTrialUser($user_id);
            // if ($data['isEnterpriceOrTrialUser']) {
            //     $cart_items = $data['cart_products']->reject(function ($cart_product) {
            //         return $cart_product->product->real_shipping_cost == 0;
            //     });

            //     $data['shipping_discount'] = $this->shippingCostDiscountCalculation($cart_items, false);
            // } else if(professionalOrHigher($user_id)){
            //     $data['shipping_discount'] = $this->shippingCostDiscountCalculation($data['cart_products'], true);
            // }

            return view('marketplace.store.cart.new_cart_products', $data);
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }

    public function storeCartProducts()
    {
        try {
            $product_id  = request()->product_id;
            $product_qty = request()->qty;
            $max_qty     = request()->maxqty;
            $user_id     = CRUDBooster::myParentId();

            if ($product_qty > 0 && $product_qty <= $max_qty) {
                return app(\App\Services\Marketplace\MarketplaceCartService::class)->productStoreInCart($product_id, $product_qty, $user_id);
            } else {
                return response()->json(['status' => 'error', 'message' => 'Product quantity is not available or exceeded']);
            }
        } catch (\Exception $e) {
            return response()->json(['status' => 'error', 'message' => $e->getMessage()]);
        }
    }


    public function updateCartProducts()
    {
        try {
            $user_id    = CRUDBooster::myParentId();
            MarketplaceCart::where('user_id', $user_id)->where('id', request()->id)->update(['product_qty' => request()->update_qty]);
            return response()->json(['status' => true, 'message' => 'Product quantity update on cart successfully']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    public function removeCartProducts()
    {
        try {
            $user_id    = CRUDBooster::myParentId();
            MarketplaceCart::where('user_id', $user_id)->where('id', request()->id)->delete();
            return response()->json(['status' => true, 'message' => 'Product removed from cart successfully']);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    public function storeCartCheckoutForm()
    {
        $user = User::with('customer_profile')->find(CRUDBooster::myParentId());
        $countries  = DB::table('tax_rates')
            ->selectRAW("id, country as name, UPPER(country_code) as country_shortcut, charge")
            ->get();

        $paymentTax = new PaymentTax($user->id, 2455);
        $vatNumber = $paymentTax->vatNumber() ?? false;
        $taxRate = $paymentTax->taxRate();

        $quantity = $_REQUEST['qty'];
        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();

        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }

        $cart_products = MarketplaceCart::with('product')->where('user_id', $user->id)->get();

        $shipping_discount = 0.00;
        $isEnterpriceOrTrialUser = isEnterpriceOrTrialUser($user->id);
        // if ($isEnterpriceOrTrialUser) {
        //     $cart_items = $cart_products->reject(function ($cart_product) {
        //         return $cart_product->product->real_shipping_cost == 0;
        //     });
        //     $shipping_discount = $this->shippingCostDiscountCalculation($cart_items, false);        
        // } else if(professionalOrHigher($user->id)){
        //     $shipping_discount = $this->shippingCostDiscountCalculation($cart_products, true);
        // }

        $is_upadat_form = false;

        $hasPaypal = app(DRMService::class)->paypalCheck(User::DROPMATIX_ACCOUNT_ID);

        return view('marketplace.store.cart.checkout_form', compact(
            'cart_products',
            'user',
            'term',
            'is_upadat_form',
            'countries',
            'hasPaypal',
            'vatNumber',
            'taxRate',
            'shipping_discount',
            'isEnterpriceOrTrialUser'
        ));
    }

    public function storeCartCustomerCreateUpdate()
    {

        try {

            $validator = Validator::make(request()->all(), [
                'shipping_first_namm' => 'required',
                'shipping_last_name' => 'required',
                'shipping_zip_code' => 'required',
                'shipping_city' => 'required',
                'shipping_street' => 'required',
                'shipping_email' => 'required',
                'shipping_phone_number' => 'required',
                'shipping_country_code' => 'required',
                'shipping_house_number' => 'required',
            ]);

            if ($validator->fails()) {
                return response()->json(['status' => false, 'message' => "Please fill all shipping required fields"]);
            }


            $country = DB::table('countries')->where('language_shortcode', strtolower($_REQUEST['shipping_country_code']))
                ->orWhere('language_shortcode', $_REQUEST['shipping_country_code'])
                ->first();

            $customer = NewCustomer::where('user_id', \CRUDBooster::myParentId())->where('email', $_REQUEST['shipping_email'])->first();

            if (isset($customer)) {

                $customer_update_info['customer_full_name'] = $_REQUEST['shipping_first_namm'] . ' ' . $_REQUEST['shipping_last_name'];
                $customer_update_info['company_name'] = $_REQUEST['shipping_company_name'];
                $customer_update_info['country'] = $country->name ?? $_REQUEST['shipping_country_code'];
                $customer_update_info['city'] = $_REQUEST['shipping_city'];
                $customer_update_info['zip_code'] = $_REQUEST['shipping_zip_code'];
                $customer_update_info['address'] = $_REQUEST['shipping_street'] . ' ' . $_REQUEST['shipping_house_number'];
                $customer_update_info['state'] = $_REQUEST['shipping_street'];
                $customer_update_info['phone'] = $_REQUEST['shipping_phone_number'];
                $customer_update_info['currency'] = "EUR";

                $customer_update_info['country_shipping'] = $country->name ?? $_REQUEST['Country'];
                $customer_update_info['shipping_name'] = $_REQUEST['shipping_first_namm'] . ' ' . $_REQUEST['shipping_last_name'];
                $customer_update_info['shipping_company'] = $_REQUEST['shipping_company_name'];
                $customer_update_info['street_shipping'] = $_REQUEST['shipping_street'] . ' ' . $_REQUEST['shipping_house_number'];
                $customer_update_info['address_shipping'] = $_REQUEST['shipping_street'] . ' ' . $_REQUEST['shipping_house_number'];
                $customer_update_info['zipcode_shipping'] = $_REQUEST['shipping_zip_code'];
                $customer_update_info['city_shipping'] = $_REQUEST['shipping_city'];
                $customer_update_info['is_same_address'] = true;

                app('App\Http\Controllers\AdminDrmAllCustomersController')->update_customer($customer_update_info, $customer->id);

                $customer_id = $customer->id;
            } else {

                $new_customer = new NewCustomer();
                $new_customer->full_name = $_REQUEST['shipping_first_namm'] . ' ' . $_REQUEST['shipping_last_name'];
                $new_customer->company_name = $_REQUEST['shipping_company_name'];
                $new_customer->phone = $_REQUEST['shipping_phone_number'];
                $new_customer->email = $_REQUEST['shipping_email'];
                $new_customer->currency = "EUR";
                $new_customer->address = $_REQUEST['shipping_street'] . ' ' . $_REQUEST['shipping_house_number'];
                $new_customer->city = $_REQUEST['shipping_city'];
                $new_customer->zip_code  = $_REQUEST['shipping_zip_code'];
                $new_customer->country = $country->name ?? $_REQUEST['shipping_country_code'];
                $new_customer->status = 1;
                $new_customer->insert_type  = 7;
                $new_customer->user_id = \CRUDBooster::myParentId();
                $new_customer->cc_user_id = \CRUDBooster::myParentId();
                $new_customer->billing = json_encode([
                    "name"      => $_REQUEST['shipping_first_namm'] . ' ' . $_REQUEST['shipping_last_name'],
                    "company"   => $_REQUEST['shipping_company_name'] ?? null,
                    "street"    => $_REQUEST['shipping_street'] . ' ' . $_REQUEST['shipping_house_number'],
                    "address"   => $_REQUEST['shipping_street'] . ' ' . $_REQUEST['shipping_house_number'],
                    "zip_code"  => $_REQUEST['shipping_zip_code'] ?? null,
                    "city"      => $_REQUEST['shipping_city'] ?? null,
                    "state"     => null,
                    "country"   => $country->name ?? $_REQUEST['shipping_country_code'],
                ]);
                $new_customer->shipping = json_encode([
                    "name"      => $_REQUEST['shipping_first_namm'] . ' ' . $_REQUEST['shipping_last_name'],
                    "company"   => $_REQUEST['shipping_company_name'] ?? null,
                    "street"    => $_REQUEST['shipping_street'] . ' ' . $_REQUEST['shipping_house_number'],
                    "address"   => $_REQUEST['shipping_street'] . ' ' . $_REQUEST['shipping_house_number'],
                    "zip_code"  => $_REQUEST['shipping_zip_code'] ?? null,
                    "city"      => $_REQUEST['shipping_city'] ?? null,
                    "state"     => null,
                    "country"   => $country->name ?? $_REQUEST['shipping_country_code'],
                ]);

                $new_customer->save();
                $customer_id = $new_customer->id;
            }

            MarketplaceCart::where('user_id', \CRUDBooster::myParentId())
                ->update([
                    'customer_id' => $customer_id
                ]);
            return response()->json(['status' => true, 'message' => "update success"]);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $e->getMessage()]);
        }
    }

    public function getCartOrderSupplier($order_id)
    {

        $order = NewOrder::with('shop:id,lang,channel')->find($order_id);

        if (is_null($order)) return false;

        //Products
        $products = $order->products;
        if (is_null($products)) return false;

        //Channel
        $channel = collect($order->shop)->toArray();

        //Is automail active
        // $is_automail_active = (bool)DB::table('drm_supplier_mail')->where('cms_user_id', $order->cms_user_id)->value('auto_mail');


        //send FTP file
        try {
            $user_has_ftp = DB::table('ftp_credentials')->where(['user_id' => $order->cms_user_id, 'is_i7o' => 1])->exists();
            $user_has_ftp_send = $user_has_ftp ? DB::table('drm_supplier_mail')->where('cms_user_id', $order->cms_user_id)->where('auto_send_ftp', '=', 1)->exists() : 0;
            if ($user_has_ftp_send) {
                app(\App\Services\FTP\OrderFTPService::class)->sendOrders([$order->id], $order->cms_user_id);
            }
        } catch (Exception $ex) {
        }

        //Find supplier
        $product_collection = collect($products);

        $supplier_products = [];
        $carts = [];

        foreach ($product_collection as $product) {
            if (!empty($product->mp_supplier_id)) {
                $supplier_products[$product->mp_supplier_id][] = $product;
            }

            $carts[] = $product;
        }


        //Update order cart & handling time
        if (!empty($carts)) {
            //Min delivery days
            $min_delivery_days = collect($carts)->min('delivery_days');
            $order_update = ['cart' => json_encode($carts)];

            if ($min_delivery_days) {
                $order_update['delivery_days'] = $min_delivery_days;
            }
            $order_update['marketplace_paid_status'] = 1;

            // STORE_ORDER_STATUS_ON_DATABASE
            $order->update($order_update); //Update order
        }
        //Send order to suppliers
        $supplier_products = array_filter($supplier_products);

        if (is_array($supplier_products) && $supplier_products) {
            try {
                return $this->sendMpSupplierProduct($order, $supplier_products);
            } catch (Exception $ee) {
                return false;
            }
        }
    }

    private function sendMpSupplierProduct(NewOrder $order, $supplier_products)
    {
        $marketplace_carts = collect($supplier_products)->flatten()->whereNotNull('marketplace_product_id')->toArray();
        //Create marketplace invoice
        if (!empty($marketplace_carts)) {
            $this->createNewMarketplaceSellinvoice($order, $marketplace_carts, $supplier_products);
        }

        // foreach ($products as $supplier_id => $carts) {
        //     $marketplace_carts = collect($carts)->whereNotNull('marketplace_product_id')->toArray();

        //     //Is automail active
        //     if ($is_automail_active) {
        //         $this->placeOrderProduct($order, $carts, $supplier_id);
        //     }

        //     //Create marketplace invoice
        //     if (!empty($marketplace_carts)) {
        //         $this->createNewMarketplaceSellinvoice($order, $marketplace_carts);
        //     }
        // }
    }

    private function createNewMarketplaceSellinvoice(NewOrder $order, $marketplace_carts, $supplier_products)
    {
        if (empty($marketplace_carts)) return false;

        $marketplace_ids = collect($marketplace_carts)->pluck('marketplace_product_id')->toArray();
        $marketplace = \App\Models\Marketplace\Product::whereIntegerInRaw('id', $marketplace_ids)
            ->select('vk_price', 'id', 'delivery_company_id', 'vat')
            ->get();
        if (empty($marketplace)) return false;

        $marketplace_price_info = $marketplace->pluck('vk_price', 'id')->toArray();
        $marketplace_supplier_info = $marketplace->pluck('delivery_company_id', 'id')->toArray();
        $marketplace_vat_info = $marketplace->pluck('vat', 'id')->toArray();
        $order_id_api = 'marketplace_' . $order->shop_id . 'u' . $order->cms_user_id . 'o' . $order->id;
        $dropmatix_id = 2455;
        $client_id = $order->cms_user_id;

        //Has VAT number
        $paymentTax = new PaymentTax($client_id, 2455);
        $tax_rate = $paymentTax->taxRate() ?? 0;

        $has_vat_number = false;
        $vat_number = BillingDetail::where('user_id', $client_id)->where('country_id', '<>', 8)->value('vat_id');
        if (!empty($vat_number)) {
            $has_vat_number = true; //$vat_checker['success'];
            $tax_rate = 0;
        }

        $carts = [];
        foreach ($marketplace_carts as $cart) {

            $marketplace_product_id = $cart->marketplace_product_id;
            if (!isset($marketplace_price_info[$marketplace_product_id])) continue;

            $rate = $marketplace_price_info[$marketplace_product_id];
            $qty = $cart->qty ?? 1;
            $amount = $rate * $qty;

            $cart->mp_supplier_id = $marketplace_supplier_info[$marketplace_product_id] ?? null;
            if ($has_vat_number) {
                $cart->tax = 0;
            } else {
                $cart->tax = $tax_rate;
            }
            $cart->rate = $rate;
            $cart->amount = $amount;

            $carts[] = (array)$cart;
        }


        if (empty($carts)) return false;

        $total_amount = collect($carts)->sum('amount');

        //Price calculation
        $total = $total_amount + $order->shipping_cost - $order->shipping_discount;
        $sub_price = $total_amount;
        $discount = 0;
        $total_tax = 0;


        if (!$has_vat_number) {
            $total_tax = (($total * $tax_rate) / 100);
            $total = $total + $total_tax;
        }

        //Generate customer info
        $order_info = [
            'user_id' => $dropmatix_id,
            'cms_client' => $client_id,
            'order_date' => $order->order_date,
            'total' => round(($total), 2),
            'sub_total' => round($sub_price, 2),
            'discount' => round($discount, 2),
            'discount_type' => 'fixed',
            'total_tax' => round($total_tax, 2),
            'payment_type' => $order->payment_type,
            'status' => 'paid',
            'currency' => "EUR",
            'adjustment' => 0,
            'insert_type' => \App\Enums\InsertType::MARKETPLACE_SELL,
            'shop_id' => 440,
            'order_id_api' => $order_id_api,
            'invoice_date' => $order->invoice_date,
            'carts' => $carts,
            'marketplace_order_ref' => $order->id,
            'shipping_cost' => $order->shipping_cost,
            'intend_id' => $_REQUEST['intend_id'] ?? null,
            'tax_rate' => $tax_rate,
            'vat_number' => $has_vat_number ? $vat_number : null,
            'tax_version' => 1,
            'shipping_discount' => $order->shipping_discount,
            'supplier_carts' => $supplier_products,
        ];

        return $this->insertMarketplaceCartOrder($order_info, $client_id);
    }

    private function insertMarketplaceCartOrder($order_info, $user_id, $order_return = false)
    {
        $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->userBillingToCustomerProfile($user_id, $order_info['user_id']);

        if (!is_null($customer_id)) {
            $customer = NewCustomer::find($customer_id);

            if ($customer) {
                $order_info['drm_customer_id'] = $customer_id;

                $order_info['customer_info'] = customerToCustomerInfoJson($customer);
                $order_info['billing'] = $customer->billing;
                // $order_info['shipping'] = $customer->shipping;

                $order_info['tax_number'] = $customer->tax_number;

                $new_order = $this->insertMarketplaceCartOrderToDatabase($order_info);
                if ($new_order) {
                    return ($order_return) ? $new_order : true;
                }
            }
        }
        return false;
    }

    private function insertMarketplaceCartOrderToDatabase($order_info)
    {
        $validator = Validator::make($order_info, [
            'user_id' => 'required',
            'shop_id' => 'required',
            'order_id_api' => 'required',
            'drm_customer_id' => 'required',
            'marketplace_order_ref' => 'required'
        ]);
        if ($validator->fails()) {
            return null;
        }


        /* ----------------- Check duplication ------------------ */
        $check['cms_user_id'] = $cms_user_id = $order_info['user_id'];
        $check['order_id_api'] = $order_id_api = $order_info['order_id_api'];
        if (DB::table('new_orders')->where($check)->count() > 0) return false;

        $check['shop_id'] = $shop_id = $order_info['shop_id'];
        if (DB::table('new_orders')->where($check)->count() > 0) return false;

        //Extra layer of duplication check
        if (DB::table('new_orders')->where(['order_id_api' => $order_id_api, 'cms_user_id' => $cms_user_id, 'shop_id' => $shop_id])->count() > 0) {
            return false;
        }

        // Invoice number generator start
        $inv_setting = DB::table('drm_invoice_setting')->where('cms_user_id', $check['cms_user_id'])->orderBy('id', 'desc')->select('start_from_1', 'start_invoice_number', 'suffix')->first();
        $start_from_1 = (bool)$inv_setting->start_from_1;
        $inv_suffix = $inv_setting->suffix;

        $last_order_item = DB::table('new_orders')->where('cms_user_id', $check['cms_user_id'])->where('invoice_number', '!=', -1)->where('credit_number', 0);

        if ($start_from_1) {
            $last_order_item->whereYear('created_at', date('Y'));
        }

        $inv1 = $last_order_item->orderByRaw('CAST(invoice_number AS UNSIGNED) desc')->first()->invoice_number + 1;
        $inv2 = $inv_setting->start_invoice_number;
        $invoice_number = ($start_from_1) ? $inv1 : (($inv1 > $inv2) ? $inv1 : $inv2);


        //Extra layer of duplication check
        $inv_used_count = DB::table('new_orders')->where(['cms_user_id' => $cms_user_id, 'invoice_number' => $invoice_number])->where('credit_number', 0);
        if ($start_from_1) {
            $inv_used_count->whereYear('created_at', date('Y'));
        }
        $inv_used_count = $inv_used_count->count();

        if ($inv_used_count > 0) {
            User::find(71)->notify(new DRMNotification('DUPLICATE INV NUBMER TRY: ' . inv_number_string($invoice_number, $inv_suffix) . ' USER ID: ' . $cms_user_id . ' SHOP_ID: ' . $shop_id . ' API_ID: ' . $order_id_api, 'DUPLICATE_INV_TRY'));
            return false;
        }
        //Invoice number generator end


        /* -------------- Status ------------------- */
        $status = $order_info['status'] ?? "nicht_bezahlt";

        /* ------------------ insert order ----------------- */
        $row['invoice_number'] = $invoice_number;
        $row['invoice_date'] = $order_info['invoice_date'];

        $row['order_date'] = $order_info['order_date'];
        $row['insert_type'] = $order_info['insert_type'];

        $status = drmOrderLabelByGroupId($status);

        if (strpos($order_info['total'], ",")) {
            $have = [".", ","];
            $will_be = ["", "."];
            $order_info['total'] = str_replace($have, $will_be, $order_info['total']);
        }
        $row['total'] = $total = abs($order_info['total']);
        $row['sub_total'] = abs($order_info['sub_total']);
        $row['total_tax'] = abs($order_info['total_tax']);

        $row['drm_customer_id'] = $drm_customer_id = $order_info['drm_customer_id'];

        $row['discount'] = abs($order_info['discount']);
        $row['discount_type'] = $order_info['discount_type'];
        $row['adjustment'] = $order_info['adjustment'];
        $row['payment_type'] = $order_info['payment_type'];
        $row['currency'] = $order_info['currency'];
        $row['shipping_cost'] = abs($order_info['shipping_cost']);
        $row['customer_info'] = $order_info['customer_info'];
        $row['billing'] = $order_info['billing'];
        $row['marketplace_paid_status'] = 1;
        $row['shipping_discount'] = $order_info['shipping_discount'];

        // TODO:: DROPMATIX
        $row['shipping'] = $order_info['shipping'];
        if ($cms_user_id == 2455 && $row['insert_type'] == \App\Enums\InsertType::MARKETPLACE_SELL && !empty($order_info['marketplace_order_ref'])) {
            $row['shipping'] = DB::table('new_orders')->where('id', $order_info['marketplace_order_ref'])->value('shipping');
        }

        $row['client_note'] = $order_info['client_note'];
        $row['status'] = $status;
        $row['cms_client'] = $order_info['cms_client'];
        $row['payment_status'] = $order_info['payment_status'] ?? null;

        $row['eur_total'] = $order_info['eur_total'] ?? $total;
        $row['currency_rate'] = $order_info['currency_rate'] ?? 1;

        $row['cart'] = $carts_json = json_encode($order_info['carts']);
        $row['inv_pattern'] = drm_invoice_number_format($invoice_number, $inv_suffix);
        $row['marketplace_order_ref'] = $order_info['marketplace_order_ref'];

        //TAX NUMBER
        $row['tax_rate'] = $order_info['tax_rate'];
        $row['vat_number'] = $order_info['vat_number'];
        $row['tax_version'] = $order_info['tax_version'];
        $row['intend_id'] = $order_info['intend_id'];
        $row['tax_number'] = $order_info['tax_number'] ?? null;

        if (DB::table('new_orders')->where(['cms_user_id' => $cms_user_id, 'order_id_api' => $order_id_api, 'total' => $total, 'drm_customer_id' => $drm_customer_id])->exists()) {
            User::find(71)->notify(new DRMNotification('DUPLICATE ORDER TRY USER ID: ' . $cms_user_id . ' SHOP_ID: ' . $shop_id . ' API_ID: ' . $order_id_api, 'DUPLICATE_ORDER_TRY'));
            return false;
        }


        if (isset($order_info['dropmatix_sub_total'])) {
            $row['dropmatix_sub_total'] = $order_info['dropmatix_sub_total'];
            $row['dropmatix_total_tax'] = $order_info['dropmatix_total_tax'];
            $row['dropmatix_discount']  = $order_info['dropmatix_discount'];
            $row['dropmatix_shipping_cost'] = $order_info['dropmatix_shipping_cost'];
            $row['dropmatix_tax_rate']      = $order_info['dropmatix_tax_rate'];
        } else {
            $dropmatixData = app(\App\Services\Order\Store\UpdateDropmatix::class)->get($row);
            if (!empty($dropmatixData)) {
                $row = array_merge($row, $dropmatixData);
            }
        }

        // STORE_ORDER_ON_DATABASE
        $order = NewOrder::updateOrCreate($check, $row); //Insert order
        if (empty($order)) return false;

        $marketplace_order_ref = $order->id;
        DB::table('new_orders')->where('id', $order_info['marketplace_order_ref'])->update(['marketplace_order_ref' => $marketplace_order_ref]);
        $client_order = DB::table('new_orders')->where('id', $marketplace_order_ref)->first();

        //Log History
        try {
            drmOrderFirstHistory($order);
        } catch (Exception $eee) {
        }

        $this->sendDropmatixOrderToSupplier($order);
        $is_automail_active = (bool)DB::table('drm_supplier_mail')->where('cms_user_id', $order->cms_user_id)->value('auto_mail');
        foreach ($order_info['supplier_carts'] as $supplier_id => $carts) {
            //Is automail active
            if ($is_automail_active) {
                $this->placeOrderProduct($client_order, $carts, $supplier_id);
            }

            $this->supplierInvoice($order, $client_order, $carts);
        }

        $send_status_mail = true;
        $send_order_email = true;

        //Send order mail
        try {
            $channel = \App\Shop::where('id', $order->shop_id)->value('channel');

            $channel_order_auto_mail = DB::table('drm_order_mail')
                ->where(['cms_user_id' => $order->cms_user_id, 'channel' => $channel])
                ->first();

            if ($send_order_email && $channel_order_auto_mail) {

                if ($channel_order_auto_mail->auto_mail) {
                    app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id, $channel);
                    $send_status_mail = false;
                }
            } else if ($send_order_email && DB::table('drm_order_mail')->where('cms_user_id', $check['cms_user_id'])->whereNull('channel')->value('auto_mail')) {
                app('App\Http\Controllers\AdminDrmAllOrdersController')->send_email($order->id);
                $send_status_mail = false;
            }
        } catch (Exception $exv) {
        }


        //Send status mail
        if ($send_status_mail) {
            send_order_email($order->id);
        }

        return true;
    }


    function shippingCostDiscountCalculation($cart_products, $is_professional_user = false) {
        return 0.0;
        $totalShippingCost = 0;
        $discountedShippingCost = 0.00;
        $groupedCartProducts = $cart_products->groupBy('product.delivery_company_id');

        foreach ($groupedCartProducts as $cartProductsForCompany) {
            $cartQuantity = $cartProductsForCompany->sum('product_qty');
            $shippingCosts = $cartProductsForCompany->pluck('product.shipping_cost')->toArray();

            if ($cartProductsForCompany->count() >= 2 || $cartQuantity >= 2) {
                $totalShippingCost += $cartProductsForCompany->sum(function ($item) {
                    return $item->product_qty * $item->product->shipping_cost;
                });

                $highestShippingCost = max($shippingCosts);
                $discountedShippingCost += $highestShippingCost + ($cartQuantity - 1) * ($is_professional_user ? 2.5 : 1.5);
            } else {
                $totalShippingCost += array_sum($shippingCosts);
            }
        }
        
        if($discountedShippingCost > $totalShippingCost) return 0;
        return $discountedShippingCost > 0 ? $totalShippingCost - $discountedShippingCost : 0;

    }
    
}
