<?php

namespace App\Http\Controllers\Marketplace;

use DB;
use SimpleXMLElement;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Http\JsonResponse;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\Category;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use App\Jobs\Marketplace\MarketplaceIMHandelSync;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Models\Marketplace\MpCoreDrmTransferProduct;
use App\Models\Marketplace\MarketplaceParentCategory;
use App\Jobs\Marketplace\CategoryWiseDiscountSyncToDrm;
use App\Models\Marketplace\MarketplaceProductExportFeed;

class MarketplaceParentCategoryController extends \crocodicstudio\crudbooster\controllers\MarketplaceCBController
{
    public $parent_category;

    public function cbInit() {

        $this->table = "marketplace_parent_categories";
    }
    public function __construct(MarketplaceParentCategory $parent_category)
    {
        $this->parent_category = $parent_category;
    }
    public function index(Request $request){
        
        $query = $this->parent_category;
        if($request->start_offer != ''){
            $query = $query->whereDate('start_date', '>=', $request->start_offer);
        }
        if ($request->end_offer != '') {
            $query = $query->whereDate('end_date', '<=', $request->end_offer);
        }

        $parent_categories = $query->get();
        $productField = $this->productField();
        $all_channels = collect(config('channel.list'))->pluck('name', 'type')->toArray();
        return view('marketplace.perent_category.index',compact('parent_categories','productField', 'all_channels'));
    }

    public function create(){
        $all_channels = collect(config('channel.list'))->pluck('name', 'type')->toArray();

        return view('marketplace.perent_category.create', compact('all_channels'));
    }

    public function store(Request $request){
        $validator = $this->parent_category->validator($request);
        if ($validator->fails()) return CRUDBooster::redirect($_SERVER['HTTP_REFERER'],$validator->errors(),"error");
        $this->parent_category->create([
            'name'=>$request->name,
            'channels' => $request->channels,
            'im_handel' => $request->im_handel,
            'is_active' => $request->is_active,
            'discount_percentage'=>$request->discount_percentage,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
            'is_offer_active' => $request->is_offer_active,
        ]);
        return redirect()->to(CRUDBooster::adminPath('marketplace/parent/category'));
    }

    public function edit($id){
        $parent_category = $this->parent_category->where('id',$id)->first();
        $all_channels = collect(config('channel.list'))->pluck('name', 'type')->toArray();
        return view('marketplace.perent_category.edit',compact('parent_category', 'all_channels'));
    }

    public function update($id,Request $request){

        $validator = Validator::make(request()->all(),[
            'name'       => 'required|string|min:3|max:70|unique:marketplace.marketplace_parent_categories,name,'.$id,
            'channels'   => 'required|array|min:1',
            'is_active'  => 'required',
        ]);

        if ($validator->fails()) {
            $message = $validator->errors()->all();
            return redirect()->back()->with(['message' => implode('<br>', $message), 'message_type' => 'danger']);
        }

        $parent_category = $this->parent_category->find($id);
        $old_im_handel = $parent_category->im_handel ?? 0;
        if(isset($parent_category->end_date) && Carbon::parse($parent_category->end_date)->format('Y-m-d') != $request->end_date){
            $parent_category->old_offer_date = Carbon::parse($parent_category->start_date)->format('Y-m-d').' - '. Carbon::parse($parent_category->end_date)->format('Y-m-d') .'<br>'. $parent_category->discount_percentage.'%';
        }
        
        $parent_category->name = $request->name;
        $parent_category->channels = $request->channels;
        $parent_category->im_handel = $request->im_handel;
        $parent_category->is_active = $request->is_active;
        $parent_category->discount_percentage = $request->discount_percentage;
        $parent_category->start_date = $request->start_date;
        $parent_category->end_date = $request->end_date;
        $parent_category->is_offer_active = $request->is_offer_active;
        $parent_category->update();

        if($parent_category->is_offer_active && date($parent_category->start_date) <= date("Y-m-d") && date($parent_category->end_date) >= date("Y-m-d")){
            $categoryIds = Category::where('parent_id', $id)->where('is_parent_offer', '!=', 2)->pluck('id')->toArray() ?? [];
            if(!blank($categoryIds)){
                Category::whereIn('id', $categoryIds)->update([
                    'discount_percentage'=>$parent_category->discount_percentage,
                    'start_date' => $parent_category->start_date,
                    'end_date' => $parent_category->end_date,
                    'is_offer_active' => $parent_category->is_offer_active,
                    'is_parent_offer' => 1,
                ]);

                $productIds = Product::whereIn('category_id',$categoryIds)->pluck('id')->toArray();
                $checkExistInDrm = MpCoreDrmTransferProduct::whereIn('marketplace_product_id',$productIds)->get();
                if(count($checkExistInDrm) > 0){
                    foreach ($checkExistInDrm->chunk(500) as $product) {
                        dispatch(new CategoryWiseDiscountSyncToDrm($product, $parent_category->discount_percentage ?? 0.0));
                    }
                }
            }
        }

        if($old_im_handel != $request->im_handel){

            $categoryIds = Category::where('parent_id', $id)->pluck('id')->toArray() ?? [];
            if(!blank($categoryIds)){
                Category::whereIn('id', $categoryIds)->update(['im_handel' => $request->im_handel]);
                
                $productIds = Product::whereIn('category_id',$categoryIds)->pluck('id')->toArray();
                if(count($productIds) > 0){
                    foreach (array_chunk($productIds,300) as $product_ids) {
                        dispatch(new MarketplaceIMHandelSync($product_ids, $request->im_handel ?? 0.00));
                    }
                }
            }
            $this->mpCategoryCacheRemove();
        }

        return redirect()->to(CRUDBooster::adminPath('marketplace/parent/category'));
    }

    public function delete($id){
        try{
            $parentCategory = $this->parent_category->where('id',$id)->first();
            if($parentCategory->count_category == 0){       
                Category::where('parent_id',$id)->update(['parent_id'=>NULL]);
                $parentCategory->delete();
                return response()->json(['success' => true,'message' => 'Parent Category Deleted successfully']);
            }else{
                 return response()->json(['success' => false]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => 500,
            ]);
        }
    }

    public function parentResporse(){
        $respose = $this->parent_category->select('id','name')->get();
            $categoryOptionsHTML = '<option value="">Select a category</option>';
	        foreach ( $respose as $category ) {
	        	$categoryOptionsHTML .= '<option value="'.$category->id.'">'.$category->name.'</option>';
	        }

        return $categoryOptionsHTML;
    }

    public function productField(){
        $product_fields = [
            'name' => __("Title"),
            'description'=> __("Description"),
            'ean'=> __("EAN"),
            'item_number'=> __("Item Number"),
            'uvp'=> __("UVP"),
            'vk_price' => __("Vk Price"),
            'stock'=> __("Stock"),
            'delivery_days'=> __("Handling Time"),
            'shipping_cost' => __("Shipping Cost"),
            'item_weight'=> __("Item Weight"),
            'item_size'=> __("Item Size"),
            'item_color'=> __("Item Color"),
            'note'=> __("Note"),
            'production_year'=> __("Production Year"),
            'brand'=> __("Brand"),
            'materials'=> __("Materials"),
            'tags'=> __("Tags"),
            'gender'=> __("Gender"),
            'status'=> __("Status"),
            'image'=> __("Image"),
            'industry_template_data'=> __("Industry Template"),
        ];
        return  $product_fields;
    }

    public function parentCategoryXmlFeedUrl(){

        $select_column = request()->select_column;
        $parenCategoryIds  = explode(',',request()->categoryIds);
        $categoryIds = Category::whereIn('parent_id',$parenCategoryIds)->get()->pluck('id');
        try {

            $str = str_shuffle('abscdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890!');
            $str = rtrim(mb_strimwidth($str, 0, 12, '', 'UTF-8'));
            $product_feed = new MarketplaceProductExportFeed();
            $product_feed->parent_category_id = request()->categoryIds;
            $product_feed->category_id      = implode(",",json_decode($categoryIds));
            $product_feed->product_column   = $select_column;
            $product_feed->xml_url   = $str.time();
            $product_feed->save();

            $url = url('').'/mp/downloadxml/parent?id='.$product_feed->xml_url;
            return $url;
        }catch(\Exception $e){
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Something went wrong", "error");
        }


    }

    public function genarateProductXml()
    {
        ini_set('max_execution_time', '0');
        ini_set('memory_limit',-1);

        $request_id = request()->id;

        if(empty($request_id)){
            return "Please set the URL feed id";
        }

        $product_feed = MarketplaceProductExportFeed::where('xml_url',$request_id)->first();

        $selected_columns = $product_feed->product_column;
        $cateogory_id = explode(',',$product_feed->category_id);

        if(empty($selected_columns) || empty($cateogory_id)){
            return "Something went wrong";
        }

        $products = Product::where('status',1)
                    ->whereIn('category_id',$cateogory_id)
                    ->get();

        if (is_null($products)) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Category Products not found", "error");
        }

        $xml = new SimpleXMLElement('<?xml version="1.0" encoding="utf-8"?><articles></articles>');

        foreach ($products as $key => $product) {

            $article = $xml->addChild('article');
            $basic_information = $article->addChild('product_basic_information');

            foreach($selected_columns as $column){

                if($column == 'name'){
                    $basic_information->addChild('product_name', htmlspecialchars($product->name));
                }else if($column == 'description'){
                    $basic_information->addChild('description', htmlspecialchars($product->description));
                }else if($column == 'ean'){
                    $basic_information->addChild('ean', $product->ean);
                }else if($column == 'item_number'){
                    $basic_information->addChild('item_number', $product->item_number);
                }else if($column == 'uvp'){
                    $basic_information->addChild('uvp', $product->uvp);
                }else if($column == 'vk_price'){
                    $basic_information->addChild('vk_price', $product->vk_price);
                }else if($column == 'stock'){
                    $basic_information->addChild('stock', $product->stock);
                }else if($column == 'delivery_days'){
                    $basic_information->addChild('delivery_days', $product->delivery_days);
                }else if($column == 'shipping_cost'){
                    $basic_information->addChild('shipping_cost', $product->shipping_cost);
                }else if($column == 'item_weight'){
                    $basic_information->addChild('item_weight', htmlspecialchars($product->item_weight));
                }else if($column == 'item_size'){
                    $basic_information->addChild('item_size', htmlspecialchars($product->item_size));
                }else if($column == 'item_color'){
                    $basic_information->addChild('item_color', htmlspecialchars($product->item_color));
                }else if($column == 'production_year'){
                    $basic_information->addChild('production_year', htmlspecialchars($product->production_year));
                }else if($column == 'brand'){
                    $basic_information->addChild('brand', htmlspecialchars($product->brand));
                }else if($column == 'materials'){
                    $basic_information->addChild('materials', htmlspecialchars($product->materials));
                }else if($column == 'gender'){
                    $basic_information->addChild('gender', htmlspecialchars($product->gender));
                }else if($column == 'tags'){
                    $basic_information->addChild('tags', htmlspecialchars($product->tags));
                }else if($column == 'status'){
                    $basic_information->addChild('status', htmlspecialchars($product->status));
                }else if($column == 'note'){
                    $basic_information->addChild('note', htmlspecialchars($product->note));
                }

                if($column == 'image'){
                    $images = $article->addChild('images');
                    foreach($product->image as $image){
                        $images->addChild('image', htmlspecialchars($image));
                    }
                }

                if($column == 'industry_template_data'){
                    $industryTemplateData = json_decode(json_encode($product->industry_template_data), true);
                    $industryTemplateData = $industryTemplateData[$product->mainCategory->industry_template];
                    if(isset($industryTemplateData)){

                        $industry_template_data = $article->addChild('imagindustry_template_dataes');
                        $productCountry = \App\Country::find($product->country_id);

                        if ( $productCountry->country_shortcut == 'en' || $productCountry->country_shortcut == 'uk' ) {
                            $countryShortcut = 'en';
                        } else {
                            $countryShortcut = 'de';
                        }

                        foreach ( $industryTemplateData as $key=>$value ){
                            $industry_template_data->addChild($key, $value[$countryShortcut]);
                        }
                    }
                }
            }
        }

        $file_path = 'marketplace_product_export/marketplace_products.xml';

        Storage::disk('spaces')->put($file_path, $xml->asXML(), 'public');
        $file = Storage::disk('spaces')->url($file_path);


        header('Content-type: application/x-www-form-urlencode');
        header('Content-Transfer-Encoding: Binary');
        header("Content-disposition: attachment; filename=\"".$file."\"");

        readfile($file);

        if (Storage::disk('spaces')->exists($file_path)) {
            Storage::disk('spaces')->delete($file_path);
        }

    }
    
    public function parentIndustryTemplates(){
        $marketplaceCategorys = $this->parent_category->orderBy('name')->get();
        $industryTemplates = \App\Enums\Marketplace\IndustryTemplates::TEMPLATES;
        return view('marketplace.perent_category.industry_templates',compact('marketplaceCategorys','industryTemplates')); 
    }

    public function saveIndustryTemplates(Request $request){
        try {
            if( $request->category_id && $request->industry_template ) {
               
                $this->parent_category->where('id',$request->category_id)->update([
                    'industry_template' => $request->industry_template,
                ]);
                Category::where('parent_id',$request->category_id)->update([
                    'industry_template' => $request->industry_template
                ]);
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Industry Template info updated", "success");
            }else{
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Missing category or industry template", "info");
            }
        } catch (\Exception $e) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Something went wrong".$e->getMessage(), "error");
        }
    }

    public function deleteIndustryTemplates(){
        try {
            $this->parent_category->where('id',request()->id)->update([
                'industry_template' => null,
            ]);
            Category::where('parent_id',request()->id)->update([
                'industry_template' => null,
            ]);
            return response()->json([
                'success' => true,
                'message' => '',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong',
            ]);
        }
    }

    public function storeParentCategoryChannels(): JsonResponse 
    {
        try { 
            $request = $_REQUEST;
            $parent_category_id = request('parent_category_id', 0);
            $selected_channels  = request('selected_channels', '');

            if (!empty($parent_category_id)) {
                MarketplaceParentCategory::where('id', $parent_category_id)
                    ->update([
                        'channels' => $selected_channels,
                    ]);

                return response()->json([
                    'success' => true,
                    'message' => __('Parent category channels updated successfully!'),
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => __('Something went wrong! Please try again.'),
            ]);
        } catch(\Exception $ex) {
            return response()->json([
                'success' => false,
                'message' => $ex->getMessage()
            ], 422);
        }
    }

    public function mpCategoryCacheRemove()
    {
        if(!isLocal()){
            try{
                $curl = curl_init();
                curl_setopt_array($curl, array(
                    CURLOPT_URL => "207.154.254.191/api/v1/mp-category-cache-remove",
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => "",
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => "GET",
                ));
    
                $response = curl_exec($curl);
                $err = curl_error($curl);
    
                curl_close($curl);
    
            }catch (\Exception $ee) {}
        }
    }

}
