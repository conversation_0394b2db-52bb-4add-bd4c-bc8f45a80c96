<?php

namespace App\Http\Controllers\Marketplace;

use Carbon\Carbon;
use App\Enums\CreditType;
use Illuminate\Http\Request;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use App\Services\ProductApi\ProductApi;
use App\Enums\Marketplace\AnalysisAccess;
use App\Services\Marketplace\ProductService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Models\Marketplace\MarketplaceProductAnalysis;

class MarketplaceProductAnalysisController extends Controller
{
    public $channel = ['google.de','amazon.de','ebay.de'];

    public $productService;

    public function __construct(ProductService $productService)
    {
        $this->productService = $productService;
    }

    public function getProductAnalysisInfo(){
        $userId = CRUDBooster::myParentId();
        $ean = request()->product_ean;
        $product_id = request()->product_id;
        $data = [
            'product'=> null,
            'google'=> null,
            'amazon'=> null,
            'ebay'=> null,
            'error'=>null
        ];
       $MarketplaceProductAnalysis = MarketplaceProductAnalysis::select('id','product_info')
                                    ->where('user_id',$userId)
                                    ->where('product_id',$product_id)->whereDay('created_at',Carbon::now()->format('d'))
                                    ->where('ean',$ean)->orderBy('id','desc')
                                    ->first();                                       
        if($MarketplaceProductAnalysis){
            return [
                'status'=> true,
                'product_info'=> $MarketplaceProductAnalysis->product_info
            ];
        }else{
            // $year = Carbon::now()->format('Y');
            // $month = Carbon::now()->format('m');
            // $limitation = MarketplaceProductAnalysis::where('user_id',$userId)->whereYear('created_at', '=',$year)->whereMonth('created_at', '=', $month)->get()->count();
            // $DrmUserHasPurchasedApp = DrmUserHasPurchasedApp($userId,config('global.marketplace_analysis_app_id_local'));
            // if($userId == 3109){
            //     $DrmUserHasPurchasedApp = true;
            // }
            // if($limitation < AnalysisAccess::FREE_LIMITITON || $DrmUserHasPurchasedApp){
            //     $haveAnalysisAccess = true;
            // }else{
            //     $haveAnalysisAccess = false;
            // }
            $user_token = get_token_credit($userId);
            
            $data['product'] = Product::with('productBrand:id,brand_name')->select('name','ean','brand','image','ek_price','vk_price','uvp')->where('id',$product_id)->where('ean',$ean)->first();
            $data['product']['supplier_or_delevery'] = $this->getProductDeliveryCompany($product_id);
            $data['product']['brand'] = $data['product']['productBrand'] ? $data['product']['productBrand']['brand_name'] : '-';//replace brand id to brand name
            if($user_token['remain_credit']){
                
                foreach($this->channel as $channel){
                   $productResponse = $this->mp_api_data($ean,$channel);
                    if($productResponse['status']){
                        if($productResponse['source'] == 'google.de'){
                            $data['google'] = $productResponse;
                        }else if($productResponse['source'] == 'amazon.de'){
                            $data['amazon'] = $productResponse;
                        }else if($productResponse['source'] == 'ebay.de'){
                            $data['ebay'] = $productResponse;
                        }
                    }else{
                        $data['error'] = $productResponse['message'];
                    }
                }       
                MarketplaceProductAnalysis::create(['user_id'=>$userId,'ean'=>$ean,'product_id'=>$product_id,'product_info'=>$data]);

                // app('App\Http\Controllers\tariffController')->CreditUpdate($userId, 1, 'credit_deduct');
                // app('App\Http\Controllers\tariffController')->drmUserCreditAdd($userId, 1,'MP PRODUCT ANALYSIS', CreditType::PRODUCT_ANALYSIS, CreditType::CREDIT_REMOVE);

                (new \App\Services\Tariff\Credit\ChargeCredit)->charge($userId, 1, \App\Services\Tariff\Credit\CreditType::MP_PRODUCT_ANALYSIS);
                return [
                    'status'=> true,
                    'analysis_access'=>true,
                    'product_info'=> $data
                ];
            }else{
                return [
                    'status'=> false,
                    'analysis_access'=>false
                ];
            }
        }

    }

    public function mp_api_data($ean,$source){
       
        $ean  = $ean ?? '';
        $source = $source ?? '';
        $domain = $source;
        $domain_column = (strpos($domain, 'ebay') !== false) ? 'ebay' : 'google';
        $domain_column = (strpos($domain, 'amazon') !== false) ? 'amazon' : $domain_column;
        $price_column = $domain_column.'_price';
        $rating_column = $domain_column.'_rating';
        $rating_count_column = $domain_column.'_rating_count';
        $product_number_column = $domain_column.'_product_number';
        $last_sync_column = $domain_column.'_last_sync';
        
            $productApi = new ProductApi($domain);
            $product = $productApi->search('ean', $ean)->fetch();

        if($product->response->request_info->success){
            $price = 0;
            $rating = 0;

            if($product->price() || $product->offerPrice() || $product->rating() || $product->productNumber() || $product->ratingCount() || $product->title()){

                if($product->price()){
                    $price = $product->price();
                }

                if($product->title()){
                    $title = $product->title();
                }

                elseif($product->offerPrice()){
                    $price = $product->price();
                }

                if($product->rating()){
                    $rating = $product->rating();
                }

                if($product->productNumber()){
                    $productNumber = $product->productNumber();
                }

                if($product->ratingCount()){
                    $ratingCount = $product->ratingCount();
                }
                return [
                    'status'=> true,
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    'source' => $source,
                    $last_sync_column => now(),
                ];
            }
        }
        else{
            $response = [
                'status'=> false,
                'message'=> $product->response->request_info->message,
            ];
            return $response;
        }
    }

    public function getProductDeliveryCompany($productId)
    {
        $product = Product::find($productId);

        if ( $product->delivery_company_id ) {
            return \App\DeliveryCompany::find($product->delivery_company_id)->name;
        } elseif ($product->supplier_id) {
            return \App\User::where('id', $product->supplier_id)->first()->name ?? '-';
        } else {
            return '-';
        }
    }

    public function getCompareTypeProfitCalculation(Request $request){
        $analysisProductId = $request->mp_analysis_id;
        $CompareType = $request->compare_type;
        $MarketplaceProductAnalysis = MarketplaceProductAnalysis::where('id',$analysisProductId)->select('product_info')->first();
        $profit = $this->productService->profitCalculation($MarketplaceProductAnalysis,$CompareType);
         return [
            'profit'=>$profit,
        ];
    }
}