<?php

namespace App\Http\Controllers\Marketplace;

use App\Models\Marketplace\SupplierBrand;
use Illuminate\Http\Request;
use \App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Storage;
use App\Models\Marketplace\ProductBrand;
use Illuminate\Support\Facades\Validator;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Request as req;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use App\Http\Controllers\AdminDrmImportsController;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use App\Services\Marketplace\Brand\MarketplaceProductBrandService;
use DB;
use App\Models\Marketplace\Product;

class MarketplaceProductBrandsController extends Controller

{
    private MarketplaceProductBrandService $brandService;
    public function __construct(MarketplaceProductBrandService $brandService)
    {
       $this->brandService = $brandService;
    }

    public function index(Request $request)
    {
        $currentUserId = CRUDBooster::myParentId();
        $data['page_title'] = "Marketplace Product Brands";
        $search = trim($_REQUEST['search_by_query']);
        $filterBy = trim($_REQUEST['filter_by']);
        $limit = $_REQUEST['limit'] ?? 20;
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();

        if ( \CRUDBooster::isSupplier()){

            $query = DB::connection('marketplace')->table('marketplace_product_brand')
                    ->join('supplier_brands', 'supplier_brands.brand_id', '=', 'marketplace_product_brand.id')
                    ->where('supplier_brands.user_id',$currentUserId);
            if($search) $query->where('brand_name', 'like', '%' . $search . '%');
            $data['product_brands'] = $query->select('marketplace_product_brand.*', 'supplier_brands.brand_logo as supplier_brand_logo')
                                            ->orderBy('id', 'desc')->paginate($limit);

        }else{
            $query =  DB::connection('marketplace')->table('marketplace_product_brand');
            if($search)$query->where('brand_name', 'like', '%' . $search . '%');
            $data['product_brands'] = $query->orderBy('id', 'desc')->paginate($limit);

        }

        return view('marketplace.brand.index', $data);
    }

    public function saveBrandLogo(Request $request)
    {
        if (!CRUDBooster::isSuperadmin() && !CRUDBooster::isDropMatrix() && !CRUDBooster::hasDropmatixMpSupport()) {
            if (CRUDBooster::isSubUser() && (!sub_account_can('add') && !sub_account_can('all_modules', 122))) {
                return 0;
            }
        }
        return $this->brandService->saveBrandLogo($request);
    }

    public function deleteBrandLogo(Request $request)
    {
        return $this->brandService->deleteBrandLogo($request);
    }

    public function postAddBrand(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'brand_name' => 'required',
            'image' => 'nullable',
            'contact_person' => 'nullable',
            'email' => 'nullable',
            'street_and_house_number' => 'nullable',
            'postcode' => 'nullable',
            'city' => 'nullable',
            'country' => 'nullable',
        ]);
        if ($validator->fails()) {
            $message = $validator->errors()->all();
            return redirect()->back()->with(['message' => implode(', ', $message), 'message_type' => 'danger']);
        }
        return $this->brandService->postAddBrand($request);
    }

    public function getEditBrand($id)
    {   $data['page_title'] = __('Edit Brand');

        if(\CRUDBooster::isSupplier()){
            $currentUserId = CRUDBooster::myParentId();
            $data['product_brand'] = DB::connection('marketplace')->table('marketplace_product_brand')->where('marketplace_product_brand.id',$id)
                    ->join('supplier_brands', 'supplier_brands.brand_id', '=', 'marketplace_product_brand.id')
                    ->where('supplier_brands.user_id',$currentUserId)
                    ->select(
                        'marketplace_product_brand.id',
                        'marketplace_product_brand.user_id',
                        'marketplace_product_brand.brand_name',
                        'marketplace_product_brand.contact_person',
                        'marketplace_product_brand.email',
                        'marketplace_product_brand.street_and_house_number',
                        'marketplace_product_brand.postcode',
                        'marketplace_product_brand.city',
                        'marketplace_product_brand.country',
                        'supplier_brands.brand_logo'
                    )
                    ->first();
            $data['replace_brands'] = [];
        }else{
            $data['product_brand'] = DB::connection('marketplace')->table('marketplace_product_brand')->where('id',$id)->first();
            $data['replace_brands'] = DB::connection('marketplace')->table('marketplace_product_brand')->where('id', '<>', $id)->where('brand_name', 'LIKE', '%' . $data['product_brand']->brand_name . '%')->get();
        }

        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();

        return view('marketplace.brand.edit_brand', $data);
    }

    public function removeLogo(Request $request){
        return $this->brandService->removeLogo($request);
    }

    public function updateLogo(Request $request){

        // if (!CRUDBooster::isSuperadmin()) {
        //     if (CRUDBooster::isSubUser() && (!sub_account_can('add') && !sub_account_can('all_modules', 122))) {
        //         return 0;
        //     }
        // }
        return $this->brandService->updateLogo($request);


    }

    public function updateBrand(Request $request){

        $validator = Validator::make($request->all(), [
            'brand_name' => 'required',
            'image' => 'nullable',
            'contact_person' => 'nullable',
            'email' => 'nullable',
            'street_and_house_number' => 'nullable',
            'postcode' => 'nullable',
            'city' => 'nullable',
            'country' => 'nullable',
        ]);

        if ($validator->fails()) {
            $message = $validator->errors()->all();
            return redirect()->back()->with(['message' => implode(', ', $message), 'message_type' => 'danger']);
        }

        return $this->brandService->updateBrand($request);

    }


    public function deleteBrands()
    {
        $brand_ids = $_REQUEST['brand_ids'];
        return $this->brandService->destroy($brand_ids);
    }

    public function getImportBrands(Request $request)
    {
        $data['page_menu'] = \Route::getCurrentRoute()->getActionName();
        $data['page_title'] = __('customer.Import_tittle');
        if ($request->file && !$request->import) {
            $file = base64_decode($request->file);
            $file = storage_path('app/' . $file);
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $rows = app('App\Http\Controllers\AdminDrmImportsController')->csvToArray($file, $type, 'auto', false);
            if(!array_key_exists('Name',$rows[0])){
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Invalid csv Imported", "warning");
            }
            else{
            $countRows = ($rows) ? count($rows) : 0;
            Session::put('total_data_import', $countRows);
            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }
            $table_columns = ['brand_name', 'brand_logo'];
            $labels = array_map(function ($str) {
                return ucwords(str_replace("_", " ", $str));
            }, $table_columns);
            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }
        }

        return view('marketplace.brand.import_brands', $data);
    }

    public function uploadBrandCsv()
    {
        if (req::hasFile('userfile')) {
            $file = req::file('userfile');
            $ext = $file->getClientOriginalExtension();

            $validator = Validator::make([
                'extension' => $ext,
            ], [
                'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV,txt,TXT,XML,xml',
            ]);
            if ($validator->fails()) {
                $message = $validator->errors()->all();
                return redirect()->back()->with(['message' => implode('<br/>', $message), 'message_type' => 'warning']);
            }
            //Create Directory Monthly
            $filePath = 'brand_uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
            Storage::makeDirectory($filePath);
            //Move file to storage
            $filename = md5(Str::random(5)).'.'.$ext;
            $url_filename = '';
            if (Storage::putFileAs($filePath, $file, $filename)) {
                $url_filename = $filePath.'/'.$filename;
            }
            $url = route('marketplace::product.brand.importBrands').'?file='.base64_encode($url_filename);
            return redirect($url);
        } else {
            return redirect()->back();
        }
    }

    public function brandImportDone(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Import Products"]);
        Session::put('select_column', $request->get('select_column'));
        return view('marketplace.brand.import_brands', $data);
    }

    public function doImportChunk(Request $request)
    {
        try{
           
            $file_md5 = md5($request->get('file'));
            if ($request->get('file') && $request->get('resume') == 1) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }
                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }
            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);
            $table_columns = [
                'brand_name',
                'brand_logo'
            ];
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);
            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);
            $count = 0;
            
            
            $new_brand = [];
            try {
                foreach ($rows as $row) {
                    $count++;
                    Cache::put('success_' . $file_md5, $count);
                    $tmp_data = [];
                    foreach ($table_columns as $key => $value) {
                        if($value == 'brand_name'){
                            $tmp_data[$value] = trim( $row[$select_column[$key]] );
                        }else if($value == 'brand_logo'){
                            $tmp_data[$value] = !empty($select_column[$key]) ? json_encode([$row[$select_column[$key]]]) : json_encode(['https://drm.software/Marketing_assets/img/no-product-image.jpg']);
                        }
                        
                    }
                    $tmp_data['user_id'] = $user_id;
                    $tmp_data['created_at'] = Carbon::now()->toDateTimeString();
                    $tmp_data['updated_at'] = Carbon::now()->toDateTimeString();
                    $new_brand[] = $tmp_data;
                }
                $brand_names = collect($rows)->pluck('Name')->toArray();
               
                $existing_brandss = ProductBrand::select('brand_name', 'brand_logo')->whereIn('brand_name', $brand_names)->get();
                $existing_brands = $existing_brandss->pluck('brand_name')->toArray();

                $brands_unique_name = array_unique(array_map('strtolower', array_column($new_brand, 'brand_name')));
                $new_brand = array_intersect_key($new_brand, $brands_unique_name);
                $new_brands = array_filter($new_brand, function ($value) use ($existing_brands) {
                    return !in_array(strtolower($value['brand_name']), array_map('strtolower', $existing_brands));

                });
                $new_brands = array_values($new_brands);
                              
                if($new_brands){
                    collect($new_brands)
                    ->chunk(500)
                    ->each(function($brand_chunk) use($user_id){
                       
                        $brand_chunk = $brand_chunk->toArray();
                        // dd($brand_chunk[0]['brand_logo']);
                        if(CRUDBooster::isSupplier()){
                            
                            ProductBrand::insert($brand_chunk);
                            
                                foreach ($brand_chunk as $value) {
                                    $brand_name = $value['brand_name'];
                                    $brand_logo = json_decode($value['brand_logo']);
                                    $brand_id = ProductBrand::where('brand_name', $brand_name)->value('id');
                                    SupplierBrand::create(['user_id' => $user_id, 'brand_id' => $brand_id,'brand_logo' => $brand_logo]);
                                   
                                }
                        }
                        else{
                        ProductBrand::insert($brand_chunk);
                        }
                    });
                    
                } if(CRUDBooster::isSupplier() && !empty($existing_brandss)){
                    $checkSupplierHasBrand = SupplierBrand::where('user_id', $user_id)->pluck('brand_id')->toArray();
                    
                    foreach ($existing_brandss as $brand) {
                      
                        $brand_id = ProductBrand::where('brand_name', $brand->brand_name)->value('id');
                       
                        if(!in_array($brand_id, $checkSupplierHasBrand)){
                            
                            SupplierBrand::create(['user_id' => $user_id, 'brand_id' => $brand_id, 'brand_logo' => $brand->brand_logo]
                            
                        );
                    }
                    }
                }
                return response()->json(['status' => true, 'message' => "Brand Imported !!!"]);
            }catch(\Exception $e){
                $e = (string) $e;
                Cache::put('error_' . $file_md5, $e, 500);
                return response()->json(['status' => false, 'message' => $e]);
            }
        }catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => "Sorry, something went wrong"]);
        }
    }

    public function getUpdateBrands()
    {
        $data['page_title'] = __('Update Brands');
        if (req::get('file') && !req::get('import')) {
            $file = base64_decode(req::get('file'));
            $file = storage_path('app/' . $file);
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $rows = app('App\Http\Controllers\AdminDrmImportsController')->csvToArray($file, $type, 'auto', false);
            if(!array_key_exists('Name',$rows[0])){
                CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Invalid csv Imported", "warning");
            }
            else{
            $countRows = ($rows) ? count($rows) : 0;
            Session::put('total_data_import', $countRows);
            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }
            $table_columns = ['brand_name', 'brand_logo'];
            $labels = array_map(function ($str) {
                return ucwords(str_replace("_", " ", $str));
            }, $table_columns);
            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }
    }
        return view('marketplace.brand.update_brands', $data);
    }

    public function uploadUpdateBrandCsv()
    {
        if (req::hasFile('userfile')) {
            $file = req::file('userfile');
            $ext = $file->getClientOriginalExtension();
            $validator = Validator::make([
                'extension' => $ext,
            ], [
                'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV,txt,TXT,XML,xml',
            ]);
            if ($validator->fails()) {
                $message = $validator->errors()->all();

                return redirect()->back()->with(['message' => implode('<br/>', $message), 'message_type' => 'warning']);
            }
            //Create Directory Monthly
            $filePath = 'brand_uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
            Storage::makeDirectory($filePath);

            //Move file to storage
            $filename = md5(Str::random(5)).'.'.$ext;
            $url_filename = '';
            if (Storage::putFileAs($filePath, $file, $filename)) {
                $url_filename = $filePath.'/'.$filename;
            }
            $url = route('marketplace::product.brand.updateBrands').'?file='.base64_encode($url_filename);

            return redirect($url);
        } else {
            return redirect()->back();
        }
    }

    public function brandUpdateDone(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Update Brands"]);
        Session::put('select_column', $request->get('select_column'));
        return view('marketplace.brand.update_brands', $data);
    }

    public function doUpdateChunk(Request $request)
    {
        try{
                   
            $file_md5 = md5($request->get('file'));
            if ($request->get('file') && $request->get('resume') == 1) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }
                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }
            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);

            $table_columns = [
                'brand_name',
                'brand_logo'
            ];
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);
            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);
            $count = 0;
          
           

            $new_brands = [];
            $update_data = [];
            try {
                foreach ($rows as $row) {
                    $count++;
                    Cache::put('success_' . $file_md5, $count);
                    $tmp_data = [];
                    $tmp_update_data = [];
                    foreach ($table_columns as $key => $value) {
                        if($value == 'brand_name'){
                            $tmp_data[$value] = trim( $row[$select_column[$key]] );
                        }else if($value == 'brand_logo'){
                            $tmp_data[$value] = !empty($select_column[$key]) ? json_encode([$row[$select_column[$key]]]) : json_encode(['https://drm.software/Marketing_assets/img/no-product-image.jpg']);
                        }
                    }
                    $brand_names = collect($rows)->pluck('Name')->toArray();
                    $existing_brands = ProductBrand::select('id', 'brand_name', 'brand_logo')->whereIn('brand_name', $brand_names)->get();
                    $existing_brands->filter(function($item) use ($tmp_data, &$tmp_update_data){
                        $cmp_res = strcasecmp($item->brand_name, $tmp_data['brand_name']);
                        if( $cmp_res == 0 ){
                            $tmp_update_data['id'] = $item->id;
                        }
                    });
                    if( !empty($tmp_update_data) ){
                        $update_data[$tmp_update_data['id']] = $tmp_data['brand_logo'];
                    }else{
                        $tmp_data['user_id'] = $user_id;
                        $tmp_data['created_at'] = Carbon::now()->toDateTimeString();
                        $tmp_data['updated_at'] = Carbon::now()->toDateTimeString();
                        $new_brands[] = $tmp_data;
                    }
                }
                $brands_unique_name = array_unique(array_column($new_brands, 'brand_name'));
                $new_brands = array_intersect_key($new_brands, $brands_unique_name);
                
                
                if($update_data){
                    collect($update_data)
                    ->chunk(200)
                    ->each(function($brand_update_chunk) use($user_id){
                        $brand_update_chunk = $brand_update_chunk->toArray();
                        $cases = [];
                        $ids = [];
                        $params = [];
                        foreach ($brand_update_chunk as $key => $value) {
                            $cases[] = "WHEN {$key} then ?";
                            $params[] = $value;
                            $ids[] = $key;
                        }
                        $ids = implode(',', $ids);
                        $cases = implode(' ', $cases);
                        \DB::connection('marketplace')->update("UPDATE marketplace_product_brand SET `brand_logo` = CASE `id` {$cases} END WHERE `id` in ({$ids}) AND `user_id` = {$user_id}", $params);
                    });
                }
                if($new_brands){
                    collect($new_brands)
                    ->chunk(500)
                    ->each(function($brand_chunk) use($user_id) {
                        $brand_chunk = $brand_chunk->toArray();
                         if(CRUDBooster::isSupplier()){
                            ProductBrand::insert($brand_chunk);
                                foreach ($brand_chunk as $value) {
                                    $brand_name = $value['brand_name'];
                                    $brand_id = ProductBrand::where('brand_name', $brand_name)->value('id');
                                    $brand_logo = json_decode($value['brand_logo']);
                                    // dd($value['brand_logo']);

                                    SupplierBrand::create(['user_id' => $user_id, 'brand_id' => $brand_id, 'brand_logo' => $brand_logo]);
                            }
                        }
                        else{
                        ProductBrand::insert($brand_chunk);
                        }
                    });
                } if (CRUDBooster::isSupplier() && !empty($existing_brands)) {
                    $checkSupplierHasBrand =  SupplierBrand::where('user_id', $user_id)->pluck('brand_id')->toArray();
                    
                    foreach ($existing_brands as $brand) {
                        $brand_id = ProductBrand::where('brand_name', $brand->brand_name)->value('id');
                        if (!in_array($brand_id, $checkSupplierHasBrand)) {
                            
                            SupplierBrand::create(['user_id' => $user_id, 'brand_id' => $brand_id, 'brand_logo' => $brand->brand_logo] 
                                
                            );
                        }
                    }
                }
                return response()->json(['status' => true, 'message' => "Brand Imported !!!"]);
            }catch(\Exception $e){
                $e = (string) $e;
                Cache::put('error_' . $file_md5, $e, 500);
                return response()->json(['status' => false, 'message' => $e]);
            }
        }catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => "Sorry, something went wrong"]);
        }
    }

    public function getBrands(){
        $currentUserId = CRUDBooster::myParentId();
        if ( \CRUDBooster::isSupplier()){

            $brands = DB::connection('marketplace')->table('marketplace_product_brand')
                    ->join('supplier_brands', 'supplier_brands.brand_id', '=', 'marketplace_product_brand.id')
                    ->where('supplier_brands.user_id',$currentUserId)
                    ->select('marketplace_product_brand.id','marketplace_product_brand.brand_name','supplier_brands.brand_logo as brand_logo')
                    ->get();

        }else{
            $brands =  DB::connection('marketplace')->table('marketplace_product_brand')->get();

        }
        
        $html = '<option value="">--Select Brands--</option>';
        
        foreach($brands as $brand){
            $html .= '<option data-brand_logo="'.json_decode($brand->brand_logo)[0].'" data-brand_name="'.$brand->brand_name.'" value="'.$brand->id.'">'.$brand->brand_name.'</option>';
        }
        return response()->json(['status' => true, 'data' => $html]);
    }

    public function getBrandProductsInfo(Request $request){
        $products = Product::where('brand',$request->brand_id)->select('id','stock','status')->get();

        $data['total_products'] = $products->count('id');
        $data['total_products_active_product'] = $products->where('status',1)->count('id');
        $data['stock_zero'] = $products->where('stock',0)->count('id');
        $data['stock_zero_with_active'] = $products->where('stock',0)->where('status',1)->count('id');
        $data['stock_one'] = $products->where('stock',1)->count('id');
        $data['stock_one_with_active'] = $products->where('stock',1)->where('status',1)->count('id');
        $data['stock_greter_than_one'] = $products->where('stock','>',1)->count('id');
        $data['stock_greter_than_one_active'] = $products->where('stock','>',1)->where('status',1)->count('id');
        
        return view('marketplace.category.category_product_info', $data);
    }
}
