<?php

namespace App\Http\Controllers\Marketplace;

use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\Category;
use phpDocumentor\Reflection\Types\Null_;
use App\Models\Marketplace\MarketplaceSponsor;
use crocodicstudio\crudbooster\helpers\CRUDBooster;

class MarketplaceSponsorController extends Controller
{
    public function index(){
        $supplier_id = (CRUDBooster::myParentId()) ? CRUDBooster::myParentId() : CRUDBooster::myParentId();
        $sponsors = MarketplaceSponsor::where('supplier_id',$supplier_id)->get();
        return view('marketplace.sponsor.index',compact('sponsors'));
    }
    public function create(){
        $supplier_id = CRUDBooster::myParentId();
        $category_ids = Product::where('supplier_id',$supplier_id)->where('status',1)->where('stock','>',0)->groupBy('category_id')->pluck('category_id')->toArray();
        $categories = Category::whereIn('id', $category_ids)->get(['id','name']);
        // $categories = Category::get(['id','name']);
        return view('marketplace.sponsor.create',compact('categories'));
    }
    public function details($id){
        $sponsor = MarketplaceSponsor::find($id);
        $products = Product::whereIn('id',$sponsor->product_id)->get();
        return view('marketplace.sponsor.details',compact('sponsor','products'));
    }

    public function getProdct(){

        $search_string = request()->search;
        $category_id = request()->category_id;
        $product_id = request()->product_id;

        $query = Product::where('supplier_id', CRUDBooster::myParentId())
                ->where('status',1)
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->where('shipping_method', 1)
                            ->where('stock', '>', 0);
                    });
                    $query->orWhere(function ($query) {
                        $query->where('shipping_method', '<>', 1)
                            ->where('internel_stock', '>', 0);
                    });
                })

                ->whereDoesntHave('core_connection')
                ->select('id','name','image','ean','ek_price','stock','internel_stock','shipping_method');

        if(!empty($search_string)){
            $query->where(DB::raw(
                "REPLACE(
                    CONCAT(
                        COALESCE(name,''),' ',
                        COALESCE(ean,''),' '
                    ),
                '  ',' ')"
                ),
            'like', '%' . $search_string .'%');
        }

        if(!empty($category_id)){
            $query->where('category_id',$category_id);
        }
        $products = $query->orderByRaw('id = ? desc', [$product_id])->orderby('name','asc')->get();

        if(count($products) > 0){
            return view('marketplace.sponsor.serch_response',compact('products'));
        }else{
            return __('Product Not Found');
        }

    }

    public function store(Request $request){

        $this->validation($request);

        $sponsor = new MarketplaceSponsor();
        $sponsor->name        = $request->name;
        $sponsor->supplier_id = CRUDBooster::myParentId();
        $sponsor->start_date  = date('Y-m-d', strtotime($request->start_date));
        $sponsor->end_date    = date('Y-m-d', strtotime("+4 week", strtotime($request->start_date)));
        $sponsor->type        = $request->type;
        $sponsor->budget      = $request->budget;
        $sponsor->product_id  = $request->productid;
        $sponsor->save();

        return redirect()->to('admin/marketplace/sponsor');
    }

    public function ajaxForSponsorProduct(){
        $date = date('Y-m-d');
        $type = (int)request()->type;
        if(!empty($type)){
            $sponsors = MarketplaceSponsor::where(['type'=>$type,'status'=>1])
                                            ->where('start_date','<=',$date)
                                            ->where('end_date','>=',$date)
                                            ->orderBy('budget','DESC')
                                            ->get()->pluck('product_id');
            // $products = [];
            // foreach($sponsors as $sponsor){
            //     foreach($sponsor->product_id as $id){
            //         $product = Product::where('id',$id)
            //                                 ->where('stock','!=',0)
            //                                 ->where('status',\App\Enums\Marketplace\ProductStatus::ACTIVE
            //                                 )
            //                                 ->first();
            //         if(!empty($product)){
            //             $products[] = $product;
            //         }
            //     }

            // }
            $collection = new Collection($sponsors);
            $allProductIds = $collection->collapse();
            $products = Product::whereIn('id', $allProductIds)
                ->where('stock', '!=', 0) ->where(  'status',\App\Enums\Marketplace\ProductStatus::ACTIVE)
                ->get();
            $requestUrl = request()->request_route == 1 ? true : false;
            $user = CRUDBooster::myParentId();
            return view("marketplace.sponsor.sponsor_slider",compact('products','user', 'requestUrl'));
        }
    }

    public function edit($id){
        $data['sponsor'] = MarketplaceSponsor::find($id);
        $data['products'] = Product::whereIn('id',$data['sponsor']->product_id)->select('id','name','stock')->get();
        $data['categories'] = Category::get(['id','name']);
        return view("marketplace.sponsor.edit",$data);
    }

    public function update($id,Request $request){
        $this->validation($request);

        $sponsor = MarketplaceSponsor::where('id',$id)->first();
        $sponsor->name        = $request->name;
        $sponsor->supplier_id = CRUDBooster::myParentId();
        $sponsor->start_date  = $request->start_date;
        $sponsor->end_date    = $request->end_date;
        $sponsor->type        = $request->type;
        $sponsor->budget      = $request->budget;
        $sponsor->product_id  = $request->productid;
        $sponsor->update();

        return redirect()->to('admin/marketplace/sponsor');
    }
    public function delete($id){
        MarketplaceSponsor::find($id)->delete();
        return redirect()->back();
    }

    public function activeInactive($id){
        $sponsor = MarketplaceSponsor::where('id',$id)->first();
        if($sponsor->status == 1){
            $sponsor->status = 0;
        }else{
            $sponsor->status = 1;
        }
        $sponsor->update();
        return redirect()->back();
    }
    public function validation($request){
		$request->validate([
            'name' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',
            'type' => 'required',
            'budget' => 'required',
            'productid' => 'required',
        ]);
	}
}
