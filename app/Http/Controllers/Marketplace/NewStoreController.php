<?php

namespace App\Http\Controllers\Marketplace;

use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\Marketplace\NewStore\NewStoreService;

class NewStoreController extends Controller
{
  public $newStoreService;

  public function __construct(NewStoreService $newStoreService){
    $this->newStoreService = $newStoreService;
  }

  public function productStoreNew(){
    redirectToV2('/marketplace');
    return $this->newStoreService->productStoreNew();
  }

  public function parentCategories(Request $request){
    return $this->newStoreService->parentCategories($request);
  }

  public function productBrand(Request $request){
    return $this->newStoreService->productBrand($request);
  }

  public function productPriceRange(Request $request){
    return $this->newStoreService->productPriceRange($request);
  }

  public function productMaterial(Request $request){
    return $this->newStoreService->productMaterial($request);
  }

  public function productColor(Request $request){
    return $this->newStoreService->productColor($request);
  }

  public function productSize(Request $request){
    return $this->newStoreService->productSize($request);
  }

  public function categorySearch(Request $request){
    return $this->newStoreService->categorySearch($request);
  }

  public function brandSearch(Request $request){
    return $this->newStoreService->brandSearch($request);
  }

  public function viewProductModal($id){
    return $this->newStoreService->viewProductModal($id);
  }

  public function categoryWiseBestSellerApp(Request $request){
    return $this->newStoreService->categoryWiseBestSellerApp($request);
  }

  public function notAccessibleParentCategories(Request $request){
    return $this->newStoreService->notAccessibleParentCategories($request);
  }

  public function newStoreBannerShow(){
    return $this->newStoreService->newStoreBannerShow();
  }

  public function storeConditionProductShow(Request $request){
    return $this->newStoreService->storeConditionProductShow($request);
  }
  
  public function deleteSelectedParentCategory(Request $request){
    return $this->newStoreService->deleteSelectedParentCategory($request);
  }
  public function storeBanner(){
    return $this->newStoreService->storeBanner();
  }

  public function storeBannerAdd(Request $request)
  {
    return $this->newStoreService->storeBannerAdd($request);
  }
  
  public function hasParentAccess(Request $request){
    return $this->newStoreService->hasParentAccess($request);
  }

  public function storeBannerDelete(Request $request){
    return $this->newStoreService->storeBannerDelete($request);    
  }

  public function storeHeaderBannerClose(){
    return $this->newStoreService->storeHeaderBannerClose();
  }
   public function storeShareProduct($category_id,$product_id,Request $request){
    $ref_id = $request->get('ref_id');
    return $this->newStoreService->storeShareProduct($category_id,$product_id,$ref_id);
   }

  public function productShippingCostFilter(Request $request){
    return $this->newStoreService->productShippingCostFilter($request);
  }

  public function countSingleCategoryProducts(Request $request){
    return $this->newStoreService->countSingleCategoryProducts($request);
  }

  public function shippingGroupColor(Request $request)
  {
    return $this->newStoreService->shippingGroupColor($request);
  }

  public function categoryAutoTransferEnableOrDisable(Request $request){
    return $this->newStoreService->categoryAutoTransferEnableOrDisable($request);
  }

  public function productDeliveryDaysFilter(Request $request){
    return $this->newStoreService->productDeliveryDaysFilter($request);
  }

  public function bestSellingProducts(){
    return $this->newStoreService->bestSellingProducts();
  }

  public function moreBestSellingProducts(){
    return $this->newStoreService->moreBestSellingProducts();
  }

  public function mpNewProducts(){
    return $this->newStoreService->mpNewProductsFormat();
  }
}
