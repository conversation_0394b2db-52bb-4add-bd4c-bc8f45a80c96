<?php

namespace App\Http\Controllers\Marketplace;

use DB;
use Request;
use App\User;
use CRUDBooster;
use App\NewOrder;
use http\Exception;
use App\NewCustomer;
use Illuminate\Support\Str;
use App\Models\Marketplace\Product;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\Collection;
use App\Models\Marketplace\MpVirtualCredit;
use App\Services\Marketplace\InternelSyncService;
use App\Models\Marketplace\MarketplaceSyncedOrder;

class OrderController extends Controller
{
    public function __construct ()
    {

    }

    private function colData()
    {
        $col = [];
        $col[] = ['label'=>'ID', 'name'=>'id'];
        $col[] = ['label'=>'Order ID', 'name'=>'order_id'];
        $col[] = ['label'=>'Order Date', 'name'=>'order_id'];

        return $col;
    }

    public function getCreateOrder()
    {
        $data = [];

        $suppliers = User::whereNotNull('marketplace_vendor_id')->where('id_cms_privileges', 4)->get();

        $data['page_title']     = 'Order Create';
        $form = [];



        $form['Client'] = [
            'ClientFirstName'   => ['label'=>trans('customer.CustomerFirstName'), 'required'=>1, 'maxlength'=>20,'title'=>'Max 20 characters'],
            'ClientLastName'    => ['label'=>trans('customer.CustomerLastName'), 'required'=>1, 'maxlength'=>20,'title'=>'Max 20 characters'],
            'CompanyName'       => ['label'=>trans('customer.COMPANYNAME'),'maxlength'=>20,'title'=>'Max 20 characters'],
        ];

        $form['ShipmentMethod'] = [
            'REGULAR', 'BY_NOON', 'SATURDAY'
        ];
        $form['PaymentMethod']  = [
            'TRANSFER', 'CASH'
        ];

//        $collectionIds = Collection::where('supplier_id', \CRUDBooster::myParentId())->pluck('id')->toArray();
        // $products   = Product::where('status', \App\Enums\ProductStatus::ACTIVE)->whereNotNull('marketplace_product_id')->get();
        if(\CRUDBooster::isSupplier()){
            $products   = Product::where('supplier_id',\CRUDBooster::myParentId())->whereNotNull('marketplace_product_id')->Where('marketplace_product_id','!=','')->get();
        }else{
            $products   = Product::whereNotNull('marketplace_product_id')->Where('marketplace_product_id','!=','')->get();
        }
        // $products   = Product::where('status', \App\Enums\ProductStatus::ACTIVE)->get();
        $languages  = DB::table('countries')->where('is_active', 1)->orderBy('language_shortcode')->get();
        $allCountries  = DB::table('tax_rates')->orderBy('country')->get();

        $countries  = DB::table('tax_rates')
            ->selectRAW("id, country as name, UPPER(country_code) as country_shortcut, charge")
            ->get();
        $form['countries'] = $countries;

          $mpcountries = $countries->keyBy('name')->toArray();
          $mpcountriesJson = json_encode($mpcountries);
          $mpcountries = $countries->keyBy('country_shortcut')->toArray();
          $mpcountriesRevJson = json_encode($mpcountries);

        $customerTypes = \App\Enums\Marketplace\Orders::CUSTOMER_TYPES;

        return view('marketplace.orders.create_form', compact('data', 'form', 'products', 'suppliers', 'allCountries',
            'languages', 'customerTypes' , 'mpcountriesJson', 'mpcountriesRevJson' ));
    }

    
    public function postCreateOrder()
    {
        request()->validate([
            'ClientFirstName' => 'required|max:20',
            'ClientLastName' => 'required|max:20',
            'CompanyName' => 'max:20',
            'Country' => 'required|max:2',
            'City' => 'required|max:30',
            'ZipCode' => 'required|max:10',
            'Street' => 'required|max:30',
            'HouseNumber' => 'required|max:5',
            'Email' => 'required|max:40',
            'PhoneNumber' => 'required|max:15',
        ]);

        try{

            if(!isLocal()){
                $email = $_REQUEST['Email'];
                app(\App\Services\UiValidation\UiValidation::class)->validateEmail($email);
                if(isset($_REQUEST['PhoneNumber']) && !empty($_REQUEST['PhoneNumber']))
                {
                    $phone = $_REQUEST['PhoneNumber'];
                    app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
                }
            }
                    

            if($_REQUEST['drm_customer_id'] == 0){
                
                $country = DB::table('countries')->where('language_shortcode', strtolower($_REQUEST['Country']))
                            ->orWhere('language_shortcode', $_REQUEST['Country'])
                            ->first();

                $customer = NewCustomer::where('user_id',\CRUDBooster::myParentId())->where('email',$_REQUEST['Email'])->first();

                if(isset($customer)){

                    $customer_update_info['customer_full_name'] = $_REQUEST['ClientFirstName'].' '.$_REQUEST['ClientLastName'];
                    $customer_update_info['company_name'] = $_REQUEST['CompanyName'];
                    $customer_update_info['country'] = $country->name ?? $_REQUEST['Country'];
                    $customer_update_info['city'] = $_REQUEST['City'];
                    $customer_update_info['zip_code'] = $_REQUEST['ZipCode'];
                    $customer_update_info['address'] = $_REQUEST['Street'].' '.$_REQUEST['HouseNumber']; 
                    $customer_update_info['state'] = $_REQUEST['Street'];
                    $customer_update_info['phone'] =$_REQUEST['PhoneNumber'];
                    $customer_update_info['currency'] = "EUR";

                    $customer_update_info['country_shipping'] = $country->name ?? $_REQUEST['Country'];
                    $customer_update_info['shipping_name']= $_REQUEST['ClientFirstName'].' '.$_REQUEST['ClientLastName'];
                    $customer_update_info['shipping_company']= $_REQUEST['CompanyName'];
                    $customer_update_info['street_shipping']= $_REQUEST['Street'].' '.$_REQUEST['HouseNumber'];
                    $customer_update_info['address_shipping']= $_REQUEST['Street'].' '.$_REQUEST['HouseNumber'];
                    $customer_update_info['zipcode_shipping']= $_REQUEST['ZipCode'];
                    $customer_update_info['city_shipping']= $_REQUEST['City'];
                    $customer_update_info['is_same_address'] = true;
                    
                    app('App\Http\Controllers\AdminDrmAllCustomersController')->update_customer($customer_update_info, $customer->id);
                    $_REQUEST['drm_customer_id'] = $customer->id;

                }else{

                    $new_customer = new NewCustomer();
                    $new_customer->full_name = $_REQUEST['ClientFirstName'].' '.$_REQUEST['ClientLastName'];
                    $new_customer->company_name = $_REQUEST['CompanyName'];
                    $new_customer->phone = $_REQUEST['PhoneNumber'];
                    $new_customer->email = $_REQUEST['Email'];
                    $new_customer->currency = "EUR";
                    $new_customer->address = $_REQUEST['Street'].' '.$_REQUEST['HouseNumber'];
                    $new_customer->city = $_REQUEST['City'];
                    $new_customer->zip_code  = $_REQUEST['ZipCode'];
                    $new_customer->country = $country->name ?? $_REQUEST['Country'];
                    $new_customer->status = 1;
                    $new_customer->insert_type  = 7;
                    $new_customer->user_id = \CRUDBooster::myParentId();
                    $new_customer->billing = json_encode([
                        "name"      => $_REQUEST['ClientFirstName'].' '.$_REQUEST['ClientLastName'],
                        "company"   => $_REQUEST['CompanyName'] ?? null,
                        "street"    => $_REQUEST['Street'].' '.$_REQUEST['HouseNumber'],
                        "address"   => $_REQUEST['Street'].' '.$_REQUEST['HouseNumber'],
                        "zip_code"  => $_REQUEST['ZipCode'] ?? null,
                        "city"      => $_REQUEST['City'] ?? null,
                        "state"     => null,
                        "country"   => $country->name ?? $_REQUEST['Country'],
                    ]);
                    $new_customer->shipping = json_encode([
                        "name"      => $_REQUEST['ClientFirstName'].' '.$_REQUEST['ClientLastName'],
                        "company"   => $_REQUEST['CompanyName'] ?? null,
                        "street"    => $_REQUEST['Street'].' '.$_REQUEST['HouseNumber'],
                        "address"   => $_REQUEST['Street'].' '.$_REQUEST['HouseNumber'],
                        "zip_code"  => $_REQUEST['ZipCode'] ?? null,
                        "city"      => $_REQUEST['City'] ?? null,
                        "state"     => null,
                        "country"   => $country->name ?? $_REQUEST['Country'],
                    ]);

                    $new_customer->save();

                    $_REQUEST['drm_customer_id'] = $new_customer->id;
                }
            }

            if(strtolower($_REQUEST['Country']) == 'de'){
                $_REQUEST['shipping_cost']  = 8.45 * (array_sum(request()->productQty));
            }else{
                $_REQUEST['shipping_cost']  = 22.45 * (array_sum(request()->productQty));
            }

            $request = $_REQUEST;


            // Do accounting
            app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

            /**
             * Create supplier manual order
             * 
             * 1. Create entry on new_orders
             * 2. Payment confirm
             * 3. Sent to internel API
             * 
             */
            // if(\CRUDBooster::isSupplier())
            // {  
                app(InternelSyncService::class)->orderSendToDrm();
                return CRUDBooster::redirect(url('admin/marketplace/orders'), 'Order Synced Successfully.', 'success');
            // }

            // $response = app(InternelSyncService::class)->transferOutgoingOrdersWithMultipleProducts($request);

            // if ( $response['successOrderCount'] > 0 ) {
            //     \crocodicstudio\crudbooster\helpers\CRUDBooster::redirect(  url('admin/marketplace/orders'), $response['successOrderCount'].' Order Synced Successfully.', 'success');
            // }else {
            //     return redirect($_SERVER['HTTP_REFERER'])->with(['message' =>  $response['error'], 'message_type' => 'danger'])->withInput();
            // }
        }catch(\Exception $e){
            return redirect($_SERVER['HTTP_REFERER'])->with(['message' =>  $e->getMessage(), 'message_type' => 'danger'])->withInput();
        }
        
    }

    public function postGetCustomerInfo()
    {
        $customer = NewCustomer::where('id',$_REQUEST['customer_id'])->first();

        $country  = DB::table('tax_rates')
                    ->where('country', $customer->country)
                    ->select('country_code', 'charge')
                    ->first();

        $fullName       = trim($customer->full_name);

        $allWordsArray = str_word_count($fullName, 1);

        if ( count($allWordsArray) > 1 ) {
            $lastName   = array_pop($allWordsArray);
            $firstName  = implode(' ', $allWordsArray);
        } else {
            $firstName  = implode(' ', $allWordsArray);
            $lastName   = '';
        }

        return response()->json([
            'c_first_name'      => $firstName,
            'c_last_name'       => $lastName,
            'customer_name'     => $customer->full_name,
            'company_name'      => $customer->company_name,
            'country'           => (array)$country,
            'city'              => $customer->city,
            'zip_code'          => $customer->zip_code,
            'email'             => $customer->email,
            'phone'             => $customer->phone,
            'billing'           => json_decode($customer->billing, 1),
            'shipping'          => json_decode($customer->shipping, 1),
        ]);
    }

    public function fetchCustomersDataByCustomerType()
    {
        $customerType = request()->customer_type;

        if(!\CRUDBooster::isSupplier()){
            switch ( $customerType ) {

                case "Dropmatix":
                    $customers = NewCustomer::where('user_id', \App\Enums\Marketplace\Orders::DROPMATIX_ID)->get();
                    break;

                case "DropCampus":
                    $customers = NewCustomer::where('user_id', \App\Enums\Marketplace\Orders::DROPCAMPUS_ID)->get();
                    break;

                case "Droptienda":
                    $customers = NewCustomer::where('user_id', \App\Enums\Marketplace\Orders::DROPTIANDA_ID)->get();
                    break;

                case "ExpertiseRocks":
                    $customers = NewCustomer::where('user_id', \App\Enums\Marketplace\Orders::EXPERTISEROCKS_ID)->get();
                    break;
            }
        }else{
            $customers = NewCustomer::where('user_id', \CRUDBooster::myParentId())->get();
        }


        return view('marketplace.orders.partials.customers_list', compact('customers'));
    }

    public function mpManualOrderInfo()
    {
        $data['page_title'] = __('marketplace.marketplace_manual_order');
        $queryStr = trim($_REQUEST['search_by_query']);
        $limit = $_REQUEST['limit'] ?? 20;

        $query = NewOrder::where('cms_user_id', isPatrickSpecial() ? User::LAYAN_ACCOUNT_ID : 2455);
        if(str_contains($queryStr, 'Marketplace sell') || str_contains($queryStr, 'MP Category'))
        {
            $queryStr = explode(" ", $queryStr)[1];
        }else{
            $queryStr = explode(" ", $queryStr)[0];
        }
    
        if(str_contains($queryStr, '-'))
        {
            $pieces = explode('-', $queryStr);
            $queryStr = array_pop($pieces);
        }
    
        $queryStr = trim($queryStr);
        $query->where(function($qq) use ($queryStr) {
            $qq->where('.id', "like", "%" . $queryStr . "%")
               ->orWhere('.order_id_api', "like", "%" . $queryStr . "%");
        });

        $data['order_data'] = $query->orderBy('id', 'DESC')->paginate($limit);

        $data['customers'] = DB::table('cms_users')->where('id_cms_privileges', 3)->get();
        
        return view('marketplace.manual_order.index', $data);
    }

    public function mpManualOrderInfoChange()
    {

        try {
            $product_id = $_REQUEST['product_id'];
            $order_id = $_REQUEST['order_id'];
    
            $product = Product::select('id', 'ean', 'delivery_company_id','shipping_method')->where('id', $product_id)->first();
            $order = NewOrder::select('id', 'cart')->where('cms_user_id', isPatrickSpecial() ? User::LAYAN_ACCOUNT_ID : 2455)->where('id', $order_id)->first();
            if( isset($product) && isset($order)){
                $order_cart = json_decode($order->cart, true);
                
                foreach ($order_cart as &$item) {
                    $item["item_number"] = $product->ean;
                    $item["ean"] = $product->ean;
                    $item["mp_supplier_id"] = $product->delivery_company_id;
                    $item["marketplace_product_id"] = $product->id;
                    $item["shipping_method"] = $product->shipping_method;
                }
                
                // STORE_ORDER_STATUS_ON_DATABASE
                $order->update(['cart' => $order_cart]);
    
                return response()->json([
                    'message' => __('marketplace.ean_replace_successfully'),
                    'status'  => '200'
                ]);
            } else {
                return response()->json([
                    'message' => __('marketplace.product_not_found'),
                    'status'  => '422'
                ]);
            }

        } catch(\Exception $e){
            return response()->json([
                'status'        => 'error :: '.$e->getMessage(),
                'message'     => 'Something went wrong',
            ]);
        }
    }

    public function mpManualOrderResend()
    {
        try {
            $order_id = $_REQUEST['order_id'];
            $order = NewOrder::select('id', 'mp_api_id')->where('cms_user_id', isPatrickSpecial() ? User::LAYAN_ACCOUNT_ID : 2455)->where('id', $order_id)->first();

            if( isset($order) ){

                // STORE_ORDER_STATUS_ON_DATABASE
                $order->update(['mp_api_id' => NULL]);
                app('\App\Services\Marketplace\InternelSyncService')->transferOrderToInternel($order_id);
                
                return response()->json([
                    'message' => __('marketplace.order_resend_request'),
                    'status'  => '200'
                ]);
            } else {
                return response()->json([
                    'message' => 'Something went wrong',
                    'status'  => '422'
                ]);
            }

        } catch(\Exception $e){
            return response()->json([
                'status'        => 'error :: '.$e->getMessage(),
                'message'     => 'Something went wrong',
            ]);
        }
    }

    public function getInfoForOrderResend()
    {
        $order_id = $_REQUEST['order_id'];

        $order = NewOrder::select('id','cart')->where('id', $order_id)->where('cms_user_id', isPatrickSpecial() ? User::LAYAN_ACCOUNT_ID : 2455)->first();
        $order_cart = json_decode($order->cart, true) ?? [];

        if($_REQUEST['ean_replace']){
            $ean = [];
            foreach($order_cart as $exist_ean){
                $ean[] = $exist_ean['ean'];
            }
            $eanString = implode(', ', array_unique($ean) ?? []);

            return $eanString;
        } else {
            return view('marketplace.manual_order.order_resend_modal', compact('order_cart'));
        }

    }

    public function orderPaymentRefund() {

        try {
            request()->validate([
                'type' => 'required|in:1,2', // Ensure type is either 1 or 2
                'order_id' => 'required',
                'user_id' => 'required',
                'amount' => 'required|numeric', // Ensure amount is numeric
            ]);
    
            // Retrieve the validated input data
            $data = request()->only(['order_id', 'user_id', 'amount', 'type']);

            DB::beginTransaction();
    
            // Determine message and insert type based on the type value
            if ($data['type'] == 1) {
                $available_credit = MpVirtualCredit::where('user_id', $data['user_id'])->sum('amount') ?? 0;
                
                if($available_credit < $data['amount']) return redirect()->back()->with(['message' => 'Insufficient credit','message_type' => 'error']);

                $message = "Marketplace Order payment. ID: " . $data['order_id'] . ". Amount: " . str_replace('.',',',$data['amount']) . "&#8364;";
                $insert_type = 0; // Payment
                $data['amount'] = $data['amount'] * -1;
            } else {
                $message = "Refund Marketplace order ID: " . $data['order_id'];
                $insert_type = 1; // Refund
            }

            $virtual_credit_id = DB::table('mp_virtual_credit')->insertGetId([
                'user_id' => $data['user_id'],
                'amount' => $data['amount'],
                'created_at' => now(),
                'updated_at' => now(),
            ]);
    
            DB::table('mp_virtual_credit_log')->insert([
                'mp_virtual_credit_id' => $virtual_credit_id,
                'order_id' => $data['order_id'],
                'message' => $message,
                'type' => $insert_type,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
    
            DB::commit();
    
            return redirect()->back()->with([
                'message' => $message,
                'message_type' => 'success'
            ]);
    
        } catch (\Exception $e) {
            DB::rollBack(); // Ensure rollback on error
            return redirect()->back()->with([
                'message' => $e->getMessage(),
                'message_type' => 'danger'
            ])->withInput();
        }
    }
}