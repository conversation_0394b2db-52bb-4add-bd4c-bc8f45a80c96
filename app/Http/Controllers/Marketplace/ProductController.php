<?php

namespace App\Http\Controllers\Marketplace;

use App\Enums\VisibilityStatus;
use App\Enums\PermissionStatus;
use App\Enums\ProductStatus;
use App\Http\Controllers\Controller;
use App\Models\Marketplace\Category;
use App\Services\Marketplace\ProductService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    private $product;

    public function __construct(ProductService $product)
    {
        $this->product = $product;
    }

    public function index(Request $request)
    {
        $params = $request->all();
        // dd($params);
        if (CRUDBooster::myPrivilegeName() == 'Suppliers') {
            $params = array_merge($params, ['supplier_id' => CRUDBooster::myParentId()]);
        } 
        $params = array_merge($params, ['status' => [ProductStatus::ACTIVE, ProductStatus::INACTIVE]]);
        $products = $this->product->all($params);
// dd($products);
        $page_title = 'Products';

        return view('marketplace.product.index', compact('products', 'page_title'));
    }

    public function create()
    {
        return view('marketplace.product.create');
    }

    public function store(Request $request)
    {
        try {


            return redirect()->route('marketplace::product.index')->with(['_status' => 'success', '_msg' => 'Successfully Created Product']);
        } catch (\Exception $exception) {
            return redirect()->route('marketplace::product.index')->with(['_status' => 'fails', '_msg' => 'Unable to create!']);
        }
    }

    public function edit($id)
    {
        $product = $this->product->getById($id);
        $page_title = 'Product Edit';
        $categories = Category::select('id', 'name')->where('is_active', VisibilityStatus::ACTIVE)->get();

        return view('marketplace.product.edit', compact('product','page_title', 'categories'));
    }

    public function update($id, Request $request)
    {
        try {
            $data = $request->all();
            if(!isset($data['is_top_product'])){
                $data['is_top_product'] = 0;
            }
            $this->product->update($id, $data);
            // dd('t');

            return redirect()->route('marketplace::product.index');
        } catch (\Exception $e) {
            return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to update!']);
        }
    }

    public function changeStatus($id)
    {
        try {
            $product = $this->product->getById($id);
            $event = $product->status == VisibilityStatus::ACTIVE ? 'Deactivated' : 'Activated';

            $this->product->update($id, [
                'status' => $product->status == VisibilityStatus::ACTIVE ?
                    VisibilityStatus::INACTIVE : VisibilityStatus::ACTIVE
            ]);

            return redirect()->back()->with(['_status' => 'success', '_msg' => 'Successfully '. $event .' Product']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to update!']);
        }
    }

    public function destroy($id)
    {
        try {
            $this->product->destroy($id);

            return redirect()->back()->with(['_status' => 'success', '_msg' => 'Successfully Deleted Product']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to Delete!']);
        }
    }

    public function changePermission($id)
    {
        try {
            $product = $this->product->getById($id);
            $event = $product->is_top_product == PermissionStatus::YES ? 'No' : 'Yes';

            $this->product->update($id, [
                'is_top_product' => $product->is_top_product == PermissionStatus::YES ?
                PermissionStatus::NO : PermissionStatus::YES
            ]);

            return redirect()->back()->with(['_status' => 'success', '_msg' => 'Successfully '. $event .' Product']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['_status' => 'fails', '_msg' => 'Unable to update!']);
        }
    }
}
