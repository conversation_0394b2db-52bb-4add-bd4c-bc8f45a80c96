<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\File;
use App\Mail\DRMSEndMail;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Log;
use DB;

class SuppliersController extends Controller
{
    use \App\Traits\ProjectShare;
    public $suppliersService;
    public $limit;

    public function __construct()
    {
        $this->suppliersService = new \App\Services\Marketplace\SuppliersService();
        $this->limit            = 20;
    }

    public function getSubAccounts(Request $request)
    {
        $limit = $request->limit ?? $this->limit;

        $subSuppliers = \App\Models\Marketplace\Supplier::where('is_sub_supplier', true)
                        ->where('parent_id', \CRUDBooster::myParentId())
                        ->orderBy('id', 'desc')
                        ->paginate($limit);
        $accesses     = \App\Enums\Marketplace\SuppliersAccesses::ACCESSES;
        $page_title   = 'Sub Suppliers';

        return view('marketplace.supplier.sub_suppliers', compact('subSuppliers', 'accesses', 'page_title'));
    }

    public function postAddSupplier(Request $request)
    {
        $supplierAccesses = null;
        if ( $request->accesses ) {
            $supplierAccesses = $request->accesses;
        }

        $validator = Validator::make($_REQUEST, [
            'name' => 'required',
            'email' => 'required|email|unique:cms_users',
            'password' => 'required|min:6',
        ]);

        if ($validator->fails()) {
            return redirect(\Request::server('HTTP_REFERER'))->withErrors($validator)->withInput();
        }

        try {
            //DB::beginTransaction();

            // START::Create User
            $user = \App\User::create([
                'name' => $_REQUEST['name'],
                'email' => $_REQUEST['email'],
                'photo' => null,
                'password' => \Illuminate\Support\Facades\Hash::make($_REQUEST['password']),
                'id_cms_privileges' => '4',
                'status' => null,
                'referrer_id' => $ref_user_id,
                'ref_id' => Str::random(25),
                'created_at' => now(),
                'two_factor_code' => rand(100000, 999999),
                'two_factor_expires_at' => now()->addMinutes(10),
                'parent_id' => \CRUDBooster::myParentId(),
                'status' => 'Active',
                'email_verified_at' => now(),
            ]);


            try {
                if (request()->hasFile('photo')) {
                    $disk_name = isLocal() ? 'spaces' : 'dropmatix';
                    $file = request()->file('photo');
                    $extension = $file->getClientOriginalExtension();
                    $file_name = \Illuminate\Support\Str::uuid();
                    $dir = 'users/' . $user->id . '/profile/' . $file_name . '.' . $extension;
                    Storage::disk($disk_name)->put($dir, $file->get(), 'public');

                    // User update photo
                    $user->update([
                        'photo' => Storage::disk($disk_name)->url($dir),
                    ]);
                }                
            } catch (\Exception $eex){}

            app(\App\Http\Controllers\RegistrationController::class)->assignUserGroup($user->id, 4);


            // END::Create User


            // START::Manage Billing data
            // ( Parent billing info == Newly created account's billing )
            // $parentBilling = $this->suppliersService->getBillingInfoOfUser(\CRUDBooster::myParentId())
            //                 ->toArray();
            // $parentBilling['user_id'] = $user->id;
            // \App\BillingDetail::updateOrCreate(['user_id' => $user->id], $parentBilling); //Save billing details
            // END::Manage Billing data


            //Initial appointment point insert
            DB::table('takeappointment')->updateOrInsert([
                'user_id' => $user->id
            ],
            [
                'payment_date_for' => 1,
                'payment_date_remaining' => 1
            ]);

            // START:: Manage Delivery Company Data
            $deliveryCompany = $this->suppliersService->createDeleveryCompanyFromUser($user->id);
            // END:: Manage Delivery Company Data

            //Add tag to customer profile

            $this->tagInsertToCustomer($user->id,'Registrierung als Unterlieferant', 4, 2455);
            app(\App\Http\Controllers\RegistrationController::class)->assignUserTag($user->id, 'Registrierung als Unterlieferant');
            // $supplier_tag_str = DB::table('cms_settings')
            // ->where('name', '=', 'supplier_tag_str')
            // ->value('content');

            // if(!empty($supplier_tag_str) && $tags = explode(',', $supplier_tag_str))
            // {
            //     $this->tagInsertToCustomer($user->id, $tags, 4, 2455);
            // }

            //Two factor email
            // $tags = [
            //     'user_name' => $user->name,
            //     'user_email' => $user->email,
            //     'password_confirmation' => $user->password,
            // ];
            // $slug = 'welcome_email';

            // $lang = getUserSavedLang($user->email);
            // $mail_data = DRMParseMailTemplate($tags, $slug, $lang);

            // try {
            //     app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));
            // } catch (\Exception $e) {}

            $data = [
                'sub_supplier_accesses' => $supplierAccesses,
            ];
            // Create entry in supplier account
            $mpSupplier = $this->suppliersService->createEntryForSupplier($user, 1, $data);

            //DB::commit();
            Session::put('email', $_REQUEST['email']);
            Session::put('password', $_REQUEST['password']);

            return redirect()->back()->with(['message_type' => 'success', 'message' => 'Supplier created successfully.']);
        } catch (\Exception $e) {
            //DB::rollBack();
            Log::info($e);
            return redirect()->back()->with(['message_type' => 'danger', 'message' => 'Something went wrong.']);
        }
    }

    public function postEditSupplier(Request $request)
    {
        $user = \App\User::find($request->id);
        try {
            //DB::beginTransaction();

            if ($request->photo) {
                // $photo = uploadImage(request()->photo, 'marketplace-sub-suppliers');

                $src="uploads/".$user->id."/".date('Y-m')."/";
                $path = storage_path("app/".$src);
                File::makeDirectory($path, $mode = 0777, true, true);
                $name = $_FILES['photo']['name'];
                $target_file = $target_dir . basename($_FILES["file"]["name"]);
                $imageFileType = strtolower(pathinfo($target_file,PATHINFO_EXTENSION));
                $file_path = move_uploaded_file($_FILES['photo']['tmp_name'],$path.$name);
                $photo=$src.''.$name;
            } else {
                $photo = null;
            }
            $user->update([
                'name' => $request->name,
                'photo' => $photo ?? $user->photo,
            ]);
            //DB::commit();
            return redirect()->back()->with(['message_type' => 'success', 'message' => 'Supplier info updated successfully.']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['message_type' => 'danger', 'message' => 'Something went wrong.']);
        }
    }

    public function deleteSubSupplier(Request $request)
    {
        $supplierId = $request->supplier_id;
        $user = \App\User::find($supplierId);
        try {
            //DB::beginTransaction();
            if ( $user ) {
                $user->delete();
            }
            $subSupplier = \App\Models\Marketplace\Supplier::where('cms_user_id', $supplierId)
                         ->where('is_sub_supplier', true)
                         ->first();
            if ( $subSupplier ) {
                $subSupplier->delete();
            }

            $deliveryCompany = \App\DeliveryCompany::where('supplier_id', $request->supplier_id)
                                ->where('is_marketplace_supplier', 1)
                                ->first();
            if ( $deliveryCompany ) {
                $deliveryCompany->delete();
            }
            //DB::commit();
            return redirect()->back()->with(['message_type' => 'success', 'message' => 'Supplier deleted successfully.']);
        } catch(\Exception $e) {
            //DB::rollBack();
            return redirect()->back()->with(['message_type' => 'danger', 'message' => 'Something went wrong.']);
        }
    }

    public function updateSubUsersAccess(Request $request)
    {
        $subSupplier = \App\Models\Marketplace\Supplier::where('cms_user_id', $request->user_id)
                        ->first();
        try {
            $subSupplier->update([
                'sub_supplier_accesses' => $request->accesses ?? null,
            ]);
            return redirect()->back()->with(['message_type' => 'success', 'message' => 'Access Updated successfully.']);
        } catch (\Exception $e) {
            return redirect()->back()->with(['message_type' => 'danger', 'message' => 'Something went wrong !']);
        }
    }


    public function getSubSuppliersAccessesHtml(Request $request)
    {
        $subSupplier = \App\Models\Marketplace\Supplier::where('cms_user_id', $request->user_id)
                        ->first();
        $allAccesses = \App\Enums\Marketplace\SuppliersAccesses::ACCESSES;
        $supplierAccesses = $subSupplier->sub_supplier_accesses ?? [];
        return view('marketplace.supplier.sub_supplier_access_form', compact('supplierAccesses', 'allAccesses', 'subSupplier'));
    }


}
