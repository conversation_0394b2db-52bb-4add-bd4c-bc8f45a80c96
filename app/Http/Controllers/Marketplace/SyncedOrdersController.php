<?php namespace App\Http\Controllers\Marketplace;

use App\Models\Marketplace\MarketplaceSyncedOrder;
use App\Models\Marketplace\Product;
use App\Services\Marketplace\InternelSyncService;
use Session;
use Request;
use DB;
use CRUDBooster;

class SyncedOrdersController extends \crocodicstudio\crudbooster\controllers\MarketplaceCBController
{

    protected $syncedOrdersStatus;

    public function cbInit()
    {
        # START CONFIGURATION DO NOT REMOVE THIS LINE
        $this->title_field = "id";
        $this->limit = "40";
        $this->orderby = "id,desc";
        $this->global_privilege = true;
        $this->button_table_action = true;
        $this->button_bulk_action = true;
        $this->button_action_style = "button_icon";
        $this->button_add = false;
        $this->button_edit = false;
        $this->button_delete = false;
        $this->button_detail = true;
        $this->button_show = true;
        $this->button_filter = true;
        $this->button_import = false;
        $this->button_export = true;
        $this->table = "marketplace_synced_orders";
        # END CONFIGURATION DO NOT REMOVE THIS LINE

        # START COLUMNS DO NOT REMOVE THIS LINE
        $this->col = [];

        $this->col[] = ["label" => "Order Id", "name" => "order_id"];
        $this->col[] = ["label" => "Order Date", "name" => "order_date"];
        $this->col[] = ["label" => "Status", "name" => "status"];
        $this->col[] = ["label" => "Current Status", "name" => "status_history"];
        $this->col[] = ["label" => "Tracking Number", "name" => "status_history"];
        $this->col[] = ["label" => "Transport Service", "name" => "shipment_provider"];
        $this->col[] = ["label" => "Customer & Products Info", "name" => "id"];
//        $this->col[] = ["label" => "Action", "name" => "id"];


// Custom code for status update
        // $this->syncedOrdersStatus = app(InternelSyncService::class)->scheduleSyncOrderStatus();
        // foreach ( $this->syncedOrdersStatus as $orderId=>$info ) {
        //     MarketplaceSyncedOrder::where('order_id', $orderId)->update([
        //         'status_history' => $info,
        //     ]);
        // }

        # END COLUMNS DO NOT REMOVE THIS LINE

        # START FORM DO NOT REMOVE THIS LINE
        // $this->form = [];
        // $this->form[] = ['label' => 'Order Date', 'name' => 'order_date', 'type' => 'date', 'validation' => 'required|date', 'width' => 'col-sm-10'];
        // $this->form[] = ['label' => 'Order Id', 'name' => 'order_id', 'type' => 'select2', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10', 'datatable' => 'order,id'];
        // $this->form[] = ['label' => 'Product Ids', 'name' => 'product_ids', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
        // $this->form[] = ['label' => 'Status', 'name' => 'status', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
        // $this->form[] = ['label' => 'Status History', 'name' => 'status_history', 'type' => 'text', 'validation' => 'required|min:1|max:255', 'width' => 'col-sm-10'];
        // $this->form[] = ['label' => 'Supplier Id', 'name' => 'supplier_id', 'type' => 'select2', 'validation' => 'required|integer|min:0', 'width' => 'col-sm-10', 'datatable' => 'supplier,id'];
        # END FORM DO NOT REMOVE THIS LINE

        # OLD START FORM
        //$this->form = [];
        //$this->form[] = ['label'=>'Order Date','name'=>'order_date','type'=>'date','validation'=>'required|date','width'=>'col-sm-10'];
        //$this->form[] = ['label'=>'Order Id','name'=>'order_id','type'=>'select2','validation'=>'required|min:1|max:255','width'=>'col-sm-10','datatable'=>'order,id'];
        //$this->form[] = ['label'=>'Product Ids','name'=>'product_ids','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        //$this->form[] = ['label'=>'Status','name'=>'status','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        //$this->form[] = ['label'=>'Status History','name'=>'status_history','type'=>'text','validation'=>'required|min:1|max:255','width'=>'col-sm-10'];
        //$this->form[] = ['label'=>'Supplier Id','name'=>'supplier_id','type'=>'select2','validation'=>'required|integer|min:0','width'=>'col-sm-10','datatable'=>'supplier,id'];
        # OLD END FORM

        /*
| ----------------------------------------------------------------------
| Sub Module
| ----------------------------------------------------------------------
        | @label          = Label of action
        | @path           = Path of sub module
        | @foreign_key    = foreign key of sub table/module
        | @button_color   = Bootstrap Class (primary,success,warning,danger)
        | @button_icon    = Font Awesome Class
        | @parent_columns = Sparate with comma, e.g : name,created_at
|
*/
        $this->sub_module = array();


        /*
        | ----------------------------------------------------------------------
        | Add More Action Button / Menu
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @url         = Target URL, you can use field alias. e.g : [id], [name], [title], etc
        | @icon        = Font awesome class icon. e.g : fa fa-bars
        | @color           = Default is primary. (primary, warning, succecss, info)
        | @showIf          = If condition when action show. Use field alias. e.g : [id] == 1
        |
        */
        $this->addaction = array();
        $this->addaction[] = ['url'=>'javascript:seeOrderStatus([id]);', 'icon'=>'fa fa-info'];




        /*
        | ----------------------------------------------------------------------
        | Add More Button Selected
        | ----------------------------------------------------------------------
        | @label       = Label of action
        | @icon            = Icon from fontawesome
        | @name            = Name of button
        | Then about the action, you should code at actionButtonSelected method
        |
        */
        $this->button_selected = array();


        /*
        | ----------------------------------------------------------------------
        | Add alert message to this module at overheader
        | ----------------------------------------------------------------------
        | @message = Text of message
        | @type    = warning,success,danger,info
        |
        */
        $this->alert = array();


        /*
        | ----------------------------------------------------------------------
        | Add more button to header button
        | ----------------------------------------------------------------------
        | @label = Name of button
        | @url   = URL Target
        | @icon  = Icon from Awesome.
        |
        */
        $this->index_button = array();


        /*
        | ----------------------------------------------------------------------
        | Customize Table Row Color
        | ----------------------------------------------------------------------
        | @condition = If condition. You may use field alias. E.g : [id] == 1
        | @color = Default is none. You can use bootstrap success,info,warning,danger,primary.
        |
        */
        $this->table_row_color = array();


        /*
        | ----------------------------------------------------------------------
        | You may use this bellow array to add statistic at dashboard
        | ----------------------------------------------------------------------
        | @label, @count, @icon, @color
        |
        */
        $this->index_statistic = array();


        /*
        | ----------------------------------------------------------------------
        | Add javascript at body
        | ----------------------------------------------------------------------
        | javascript code in the variable
        | $this->script_js = "function() { ... }";
        |
        */
        $this->script_js = '
            function seeOrderStatus(orderId)
            {
                $.ajax({
                    url: "/admin/marketplace/orders/status/"+orderId,
                    method: "GET",
                    success: function(response) {
                        console.log(response);
                        $(".order_id").text( response.data.OrderID );
                        $(".tracking_number").text( response.data.TrackingNumber );
                        $(".status").text( response.data.Status );
                        $(".status_date").text( response.data.Date );
                        riseModal();
                    },
                    error: function(Xhr){
                        console.log(Xhr);
                    }
                });
            }

            function riseModal()
            {
                $("#getOrderStatusModal").modal("show");
            }

            $(".product-info-modal-btn").click(function(){
                orderId = $(this).attr("order-id");
                $.ajax({
                    url: ADMIN_PATH+"/marketplace/orders/all-products/"+orderId,
                    method: "GET",
                    success: function(response) {
                        $(".orderProductTable").empty().append(response);
                        $("#orderProductsModal").modal();
                        console.log(response);
                    },
                    error: function(Xhr){
                        console.log(Xhr);
                    }
                });
            });

            $(".customer-info-modal-btn").click(function(){
                orderId = $(this).attr("order-id");
                $.ajax({
                    url: ADMIN_PATH+"/marketplace/orders/customer-info/"+orderId,
                    method: "GET",
                    success: function(response) {
                        $(".orderCustomerInfoTable").empty().append(response);
                        $("#orderCustomerInfoModal").modal();
                        console.log(response);
                    },
                    error: function(Xhr){
                        console.log(Xhr);
                    }
                });
            });
        ';


        /*
            | ----------------------------------------------------------------------
            | Include HTML Code before index table
            | ----------------------------------------------------------------------
            | html code to display it before index table
            | $this->pre_index_html = "<p>test</p>";
            |
            */
        $this->pre_index_html = null;


        /*
        | ----------------------------------------------------------------------
        | Include HTML Code after index table
        | ----------------------------------------------------------------------
        | html code to display it after index table
        | $this->post_index_html = "<p>test</p>";
        |
        */
        $this->post_index_html = '
            <div class="modal fade" id="getOrderStatusModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                            <h4 class="modal-title changable-title">Order Info </h4>
                        </div>
                        <div class="modal-body changable-body">
                            <table class="table table-bordered table-sm">
                                <tr>
                                    <th>Order ID : </th>
                                    <td class="order_id"></td>
                                </tr>
                                <tr>
                                    <th>Tracking number : </th>
                                    <td class="tracking_number"></td>
                                </tr>
                                <tr>
                                    <th>Status : </th>
                                    <td class="status"></td>
                                </tr>
                                <tr>
                                    <th>Status Updated Date : </th>
                                    <td class="status_date"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="orderProductsModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title changable-title">Order Products Info </h4>
                        </div>
                        <div class="modal-body changable-body orderProductTable">

                        </div>
                    </div>
                </div>
            </div>

            <div class="modal fade" id="orderCustomerInfoModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                            <h4 class="modal-title changable-title">Customer Info </h4>
                        </div>
                        <div class="modal-body changable-body orderCustomerInfoTable">

                        </div>
                    </div>
                </div>
            </div>
        ';


        /*
        | ----------------------------------------------------------------------
        | Include Javascript File
        | ----------------------------------------------------------------------
        | URL of your javascript each array
        | $this->load_js[] = asset("myfile.js");
        |
        */
        $this->load_js = array();


        /*
        | ----------------------------------------------------------------------
        | Add css style at body
        | ----------------------------------------------------------------------
        | css code in the variable
        | $this->style_css = ".style{....}";
        |
        */
        $this->style_css = NULL;


        /*
        | ----------------------------------------------------------------------
        | Include css File
        | ----------------------------------------------------------------------
        | URL of your css each array
        | $this->load_css[] = asset("myfile.css");
        |
        */
        $this->load_css = array();


    }


    /*
    | ----------------------------------------------------------------------
    | Hook for button selected
    | ----------------------------------------------------------------------
    | @id_selected = the id selected
    | @button_name = the name of button
    |
    */
    public function actionButtonSelected($id_selected, $button_name)
    {
        //Your code here

    }


    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate query of index result
    | ----------------------------------------------------------------------
    | @query = current sql query
    |
    */
    public function hook_query_index(&$query)
    {
        if ( !\crocodicstudio\crudbooster\helpers\CRUDBooster::isSupplier() ) {
            $query->orderBy('id', 'desc');
        } else {
            $query->where('supplier_id', \crocodicstudio\crudbooster\helpers\CRUDBooster::myParentId());
        }
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate row of index table html
    | ----------------------------------------------------------------------
    |
    */
    public function hook_row_index($column_index, &$column_value)
    {
        if ( $column_index == 4  ) {
            $c_v = json_decode($column_value, true);
            $column_value = $c_v['Status'] ?? 'N/A';
        }

        if ( $column_index == 5  ) {
            $c_v = json_decode($column_value, true);
            $column_value = $c_v['TrackingNumber'] ?? 'N/A';
        }

        if ( $column_index == 6  ) {
            $column_value = "<b>".$column_value."</b>" ?? 'N/A';
        }

        if ( $column_index == 7  ) {
            $v = "";
            $order    = MarketplaceSyncedOrder::where('id', $column_value)->first();

            $customerFullname = $order->customer_info['first_name'].' '.$order->customer_info['last_name'];
            $v        = '<a href="javascript:void(0)" class="customer-info-modal-btn" order-id="'.$order->id.'">'.$customerFullname.'</a>';
            $v       .= " | ".'<a href="javascript:void(0)" class="product-info-modal-btn" order-id="'.$order->id.'">Products </a>';

            $column_value = $v;
        }

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before add data is execute
    | ----------------------------------------------------------------------
    | @arr
    |
    */

    public function hook_before_add(&$postdata)
    {
        //Your code here
    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after add public static function called
    | ----------------------------------------------------------------------
    | @id = last insert id
    |
    */
    public function hook_after_add($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for manipulate data input before update data is execute
    | ----------------------------------------------------------------------
    | @postdata = input post data
    | @id       = current id
    |
    */
    public function hook_before_edit(&$postdata, $id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after edit public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_edit($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command before delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_before_delete($id)
    {
        //Your code here

    }

    /*
    | ----------------------------------------------------------------------
    | Hook for execute command after delete public static function called
    | ----------------------------------------------------------------------
    | @id       = current id
    |
    */
    public function hook_after_delete($id)
    {
        //Your code here

    }

    //By the way, you can still create your own method in here... :)

    public function getOrderStatus($orderId)
    {
        $response = app(InternelSyncService::class)->retriveOrderStatus( MarketplaceSyncedOrder::find($orderId) );
        return response()->json(['success' => true, 'data' => $response])->setStatusCode(200);
    }

    public function getAllProducts ($orderId)
    {
        $history = MarketplaceSyncedOrder::where('id', $orderId)->first()->history;
        return view('marketplace.orders.partials.order_products_table', compact('history'));
    }

    public function getCustomerInfo ($orderId)
    {
        $customerInfo = (object)MarketplaceSyncedOrder::where('id', $orderId)->first()->customer_info;
        $response = '';
        $response .= '<table class="table table-hover table-striped table-bordered">';
        $response .= '<tr><th>First Name</th><td>'.$customerInfo->first_name??''.'</td></tr>';
        $response .= '<tr><th>Last Name</th><td>'.$customerInfo->last_name??''.'</td></tr>';
        $response .= '<tr><th>City</th><td>'.$customerInfo->city??''.'</td></tr>';
        $response .= '<tr><th>Zip Code</th><td>'.$customerInfo->zip_code??''.'</td></tr>';
        $response .= '<tr><th>Street</th><td>'.$customerInfo->street??''.'</td></tr>';
        $response .= '</table';

        return $response;

    }


}
