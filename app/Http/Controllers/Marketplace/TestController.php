<?php

namespace App\Http\Controllers\Marketplace;

use App\Http\Controllers\Controller;
use App\Models\Marketplace\ApiCredential;
use App\Models\Marketplace\Delivery;
use App\Models\Marketplace\MarketplaceSyncedOrder;
use App\Services\Marketplace\InternelSyncService;
use Illuminate\Http\Request;

class TestController extends Controller
{
    public function apiInfo()
    {
        $credentials = ApiCredential::first();
        return view('marketplace.api.apiInfo', compact('credentials'));
    }

    public function orderStatusUpdate(){

        $responseArray = app(InternelSyncService::class)->scheduleSyncOrderStatus();

        foreach($responseArray as $orderId => $orderStatusInfos)
        {
            MarketplaceSyncedOrder::where('order_id', $orderId)
                                    ->update([
                                        'status_history' => $orderStatusInfos
                                    ]);
        }
    }




    public function scheduleDeliveryStatusSync()
    {
        $response = app(InternelSyncService::class)->scheduleSyncDeliveryStatus();
    }
}
