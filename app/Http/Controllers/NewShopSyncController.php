<?php

namespace App\Http\Controllers;
use App\Traits\EmailCampaignTrait;

use App\NewCustomer;
use App\DropfunnelCustomerTag;
use Illuminate\Support\Facades\DB;

class NewShopSyncController extends Controller
{
    use EmailCampaignTrait;

    //Campaign mail job
    public function campaignMailJob($id)
    {
        $this->sendCampaignByCampaignId($id);
    }

    public function sendCampaignMailJobAfterInserting($tag_id)
    {
        $this->sendCampaignByTagId($tag_id);
    }


        //Insert customer tag
    public function insertCustomerTag($order, $customer_id = null)
    {
        if (empty($order)) {
            return false;
        }

        try {

            $shop_types = [
                1 => 'Gambio',
                2 => 'Lengow',
                3 => 'Yatego',
                4 => 'Ebay',
                5 => 'AMAZON',
                6 => 'Shopify',
                7 => 'Woocommerce',
                8 => 'ClouSale',
                9 => 'Chrono24',
                10 => 'Droptienda',
                11 => 'Etsy',
                12 => 'Otto',
                13 => 'Kaufland',
                201 => 'Marketplace',
            ];

            $decodedCartTag = json_decode($order->cart, true);
            $drm_customer_id = $customer_id ?? $order->drm_customer_id;
            $cms_user_id = $order->cms_user_id;
            $shop_id = $order->shop_id;

            if (empty($drm_customer_id)) return;

            $tagsArray = [];

            //Product tag
            foreach ($decodedCartTag as $productNameasTag) {
                $tag_label = strip_tags(preg_replace_array('/"/', [' '], $productNameasTag['product_name']));
                $tagsArray[] = trim(ltrim($tag_label, '"')); // removing double quote from product name
            }

            //Channel tag

            $shop = \App\Shop::where('id', $shop_id)->select('channel', 'shop_name')->first();
            if ($shop) {
                $channel_name = (isset($shop_types[$shop->channel])) ? trim($shop_types[$shop->channel]) : null;

                if ($channel_name) {
                    $tagsArray[] = $channel_name;
                }
            }

            //Country tag
            $tagsArray[] = DB::table('new_customers')->where('id', '=', $drm_customer_id)->value('country');

            // Insert campaign mail
            $label_arr = array_filter(array_map('trim', $tagsArray));
            if (!empty($label_arr)) {
                foreach ($label_arr as $tag) {
                    DropfunnelCustomerTag::insertTag($tag, $cms_user_id, $drm_customer_id, 1);
                }
            }

            //Insert lead
            $hasOrder = NewCustomer::whereHas('orders', function($q){
                $q->where('new_orders.invoice_number', '!=', -1)
                ->where('new_orders.test_order', '!=', 1)
                ->where('new_orders.credit_number', '=', 0)
                ->whereNull('new_orders.credit_ref')
                ->where('new_orders.eur_total', '>', 0);
            })
            ->where('id', $drm_customer_id)
            ->exists();

            if($hasOrder)
            {
               DropfunnelCustomerTag::insertLeads($cms_user_id, $drm_customer_id, 2);
            }
            //Lead insert end

        } catch (Exception $e) {
            Log::channel('command')->info('Customer tag insertion failed! ORDER_ID: ' . $order->id);
        }
    }
}
