<?php

namespace App\Http\Controllers;
use Illuminate\Http\Request;
use CRUDBooster;
use DB;
use App\Notifications\DRMNotification;
use App\User;
use App\NewOrder;
use App\InvoicePayment;
use App\StripePayment;
use App\Services\DRM\DRMService;

class OfferPaymentController extends  Controller{
    //invoice payment
    public function offerPayment($id)
    {
        $user_id = CRUDBooster::myParentId();

        $data = [];
        $data['order'] = $order = NewOrder::findOrFail($id);
        $data['total'] = $total = $order->eur_total;
        $data['page_title'] = 'Invoice payment ' . number_format((float)$total, 2, ',', '.') . '€';
        $data['stripe_key_id'] = 'stripe_key_'.$order->cms_user_id;
        if(!empty($user_id)){
            $hasPaypal = app(DRMService::class)->paypalCheck($order->cms_user_id);

            $data['hasPaypal'] = $hasPaypal;

            return view('admin.new_order.payment', $data);
        }
        $user_info = User::where('id',$order->cms_user_id)->select('id','name','email')->first();
        $data['user_id'] = $user_info->id;
        $data['name'] = $user_info->name;
        $data['email'] = $user_info->email;
        $data['order_status'] = $order->status;
        return view('admin.new_order.external_payment', $data);
    }
}
