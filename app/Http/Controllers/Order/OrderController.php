<?php

namespace App\Http\Controllers\Order;

use App\Http\Controllers\Controller;
use Request;
use App\NewOrder;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use DB;

class OrderController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $data = [];
        $data['limit'] = $limit = (Request::get('limit')) ? Request::get('limit') : 20;

        $result = NewOrder::with('customer:id,full_name,vat_number', 'user:id,name', 'shop:id,shop_name', 'supplier:id,name', 'parcel:id,parcel_name');


        if(!CRUDBooster::isSuperadmin()){
            $result->where('cms_user_id', CRUDBooster::myParentId());
        }

        $this->filter_query($result);

        $result->select('new_orders.id', 'new_orders.drm_customer_id', 'new_orders.cms_user_id', 'new_orders.shop_id', 'new_orders.supplier_id', 'new_orders.total', 'new_orders.order_id_api', 'new_orders.status', 'new_orders.invoice_number', 'new_orders.credit_number', 'new_orders.invoice_date', 'new_orders.order_date', 'new_orders.currency', 'new_orders.eur_total', 'new_orders.supplier_time', 'new_orders.parcel_id', 'new_orders.delivery_days', 'new_orders.mail_sent', 'new_orders.package_number', 'new_orders.parcel_id', 'new_orders.shipment_data', 'new_orders.delivery_day_date', 'new_orders.insert_type', 'new_orders.inv_pattern', 'new_orders.test_order', 'new_orders.credit_ref', 'new_orders.created_at');

        //Sorting column
        $filter_is_orderby = false;
        if (Request::get('filter_column')) {

            $filter_column = Request::get('filter_column');
            $filter_val_keys = array_keys($filter_column);
            $filter_key_arr = ['id', 'order_id_api', 'shop_name', 'customer_name', 'invoice_number', 'credit_number', 'order_date', 'status', 'supplier', 'tax_number', 'parcel_name', 'delivery_days', 'user_name', 'total'];
            $filter_keys = array_intersect($filter_val_keys, $filter_key_arr);

            if($filter_keys){
                foreach ($filter_column as $key => $fc) {
                    $key = strtolower($key);
                    if(!in_array($key, $filter_key_arr)) continue;
                    $sorting = @$fc['sorting'];

                    if ($sorting != '') {
                        if ($key) {
                            if ($key == 'invoice_number') {
                                $result->orderByRaw('CAST(invoice_number AS SIGNED) ' . $sorting);
                            } else if ($key == 'delivery_days') {
                                $result->orderByRaw('CAST(delivery_days AS SIGNED) ' . $sorting);
                            } else if ($key == 'total') {
                                $result->orderByRaw('CAST(total AS SIGNED) ' . $sorting);
                            } else if ($key == 'order_date') {
                                $result->orderByRaw('CAST(order_date AS datetime) ' . $sorting);
                            } else if ($key == 'user_name') {
                                $result->join('cms_users', 'new_orders.cms_user_id', '=', 'cms_users.id')->orderBy('cms_users.name', $sorting);
                            } else if ($key == 'shop_name') {
                                $result->join('shops', 'new_orders.shop_id', '=', 'shops.id')->orderBy('shops.shop_name', $sorting);
                            } else if ($key == 'customer_name') {
                                $result->join('new_customers', 'new_orders.drm_customer_id', '=', 'new_customers.id')->orderBy('new_customers.full_name', $sorting);
                            } else if ($key == 'supplier') {
                                $result->join('delivery_companies', 'new_orders.supplier_id', '=', 'delivery_companies.id')->orderBy('delivery_companies.name', $sorting);
                            } else if ($key == 'tax_number') {
                                $result->join('new_customers', 'new_orders.drm_customer_id', '=', 'new_customers.id')->orderBy('new_customers.vat_number', $sorting);
                            } else if ($key == 'parcel_name') {
                                $result->join('user_parcel_services', 'new_orders.parcel_id', '=', 'user_parcel_services.id')->orderBy('user_parcel_services.parcel_name', $sorting);
                            } else {
                                $result->orderby('new_orders.'.$key, $sorting);
                            }
                            $filter_is_orderby = true;
                        }
                    }
                }
            }
            //Sorting end
        }

        //Get order result
        if ($filter_is_orderby == true) {
            $result = $result->paginate($limit);
        } else {
            if(in_array(CRUDBooster::myParentId(), [61])){
                $result = $result->orderByRaw('CAST(order_date AS datetime) desc')->orderByRaw('CAST(invoice_number AS SIGNED) desc')->paginate($limit);
            }else{
               $result = $result->orderByRaw('CAST(invoice_number AS SIGNED) desc')->orderByRaw('CAST(order_date AS datetime) desc')->paginate($limit);
            }
        }

        //Grand total
        $grand_total = $this->grnadTotal($result);
        $credits_ref = $this->creditsRefs($result);


        $data['result'] = $result;
        $data['page_title'] = '<i class="fa fa-glass"></i> Orders';

        $data['index_statistic'] = $this->statisticsData();
        $data['columns'] = $this->colData();
        $data['button_selected'] = [];
        $data['grand_total'] = $grand_total;
        $data['button_filter'] = true;
        $data['button_bulk_action'] = true;
        $data['button_table_action'] = true;

        $data['credits_ref'] = $credits_ref;

        $data['mainPath'] = route('order::index');

        $data['drm_status'] = $drm_status = config('global.drm_order_status');
        $array_status = array_change_key_case($drm_status);
        $array_status_keys = array_change_key_case(array_keys($array_status));
        $data['user_custom_status'] = $user_custom_status = DB::table('user_order_statuses')->where('user_id', CRUDBooster::myParentId())->whereNotIn('value', $array_status)->whereNotIn('value', $array_status_keys)->get(['label', 'value']);

        return view('order.index', $data);
    }

    //Statistics data
    private function statisticsData(){
        $order_statt = orderStatisticsData();
        $total_order = (isset($order_statt->total_order)) ? $order_statt->total_order : 0;
        $credit_note_count = (isset($order_statt->credit_note_count)) ? $order_statt->credit_note_count : 0;

        $eur_total_sum = (isset($order_statt->eur_total_sum)) ? $order_statt->eur_total_sum : null;
        $eur_credit_note_sum = (isset($order_statt->eur_credit_note_sum)) ? $order_statt->eur_credit_note_sum : null;
        $eur_prof_sum = (isset($order_statt->eur_prof_sum)) ? $order_statt->eur_prof_sum : null;

        $order_average_value = (isset($order_statt->order_average_value)) ? $order_statt->order_average_value : [];

        $best_selling_shop = (isset($order_statt->best_selling_shop) && $order_statt->best_selling_shop) ? 'Sales on '.ucfirst(strtolower($order_statt->best_selling_shop)) : null;
        $best_selling_shop_item = (isset($order_statt->best_selling_shop_item)) ? $order_statt->best_selling_shop_item : 0;


        $index_statistic = [];
        $stat_css_class = 'new_order_header_stat';

        $index_statistic[] = ['label' => __('order.average_per_order'), 'img_icon' => asset('images/order_module/1.png'), 'count' => '', 'eur_amount' => $order_average_value, 'icon' => 'fa fa-eur', 'color' => 'order-stat average-order ' . $stat_css_class, 'width' => 'col-md-2'];

        $index_statistic[] = ['label' => $best_selling_shop, 'img_icon' => asset('images/order_module/2.png'), 'count' => $best_selling_shop_item, 'icon' => 'fa fa-area-chart', 'color' => 'order-stat total-order ' . $stat_css_class, 'width' => 'col-md-2'];

        $index_statistic[] = ['label' => __('order.TOTAL_ORDERS'), 'count' => $total_order, 'img_icon' => asset('images/order_module/3.png'), 'icon' => 'fa fa-area-chart', 'color' => 'order-stat total-order ' . $stat_css_class, 'width' => 'col-md-2'];

        $index_statistic[] = ['label' => __('order.PROFORMA_INVOICE'), 'count' => '', 'img_icon' => asset('images/order_module/4.png'), 'eur_amount' => $eur_prof_sum, 'icon' => 'fa fa-google-wallet', 'color' => 'order-stat proforma-invoice ' . $stat_css_class, 'width' => 'col-md-2'];

        $index_statistic[] = ['label' => __('order.CREDIT') . ' (' . $credit_note_count . ')', 'img_icon' => asset('images/order_module/5.png'), 'count' => '', 'eur_amount' => $eur_credit_note_sum, 'icon' => 'fa fa-eur', 'color' => 'order-stat credit_note_stat ' . $stat_css_class, 'width' => 'col-md-2'];

        $index_statistic[] = ['label' => __('order.TAX_SUMMARY'), 'count' => '', 'img_icon' => asset('images/order_module/6.png'), 'icon' => 'fa fa-eur', 'tax_summery' => true, 'color' => 'order-stat tax_summery_header ' . $stat_css_class, 'width' => 'col-md-2'];

        $index_statistic[] = ['label' => __('order.TOTAL_SALES'), 'count' => '', 'img_icon' => asset('images/order_module/7.png'), 'eur_amount' => $eur_total_sum, 'icon' => 'fa fa-eur', 'color' => 'order-stat order_turnover_header ' . $stat_css_class, 'width' => 'col-md-2'];

        return $index_statistic;
    }


    //Columns data
    private function colData(){
        $col = [];
        $col[] = ["label" => "ID", "name" => "id"];
        $col[] = ["label" => "Products", "name" => "id"];

        $col[] = ["label" => __('order.ORDER_ID'), "name" => "order_id_api", 'width' => 100];
        if (CRUDBooster::isSuperadmin())
            $col[] = ["label" => "User", "name" => "user_name"];
        $col[] = ["label" => __('order.SHOP_NAME'), "name" => "shop_name", 'width' => 100];
        $col[] = ["label" => __('order.CUSTOMER_NAME'), "name" => "customer_name", 'width' => 100];

        $col[] = ["label" => __('order.Invoice'), "name" => "invoice_number"];
        $col[] = ["label" => wordwrap(__('order.CREDIT_NUMBER'), 10, '<br />'), "name" => "credit_number"];

        $col[] = ["label" => __('order.ORDER_DATE'), "name" => "order_date"];
        $col[] = ["label" => __('order.DELIVERY_STATUS'), "name" => "status", 'width' => 100];
        $col[] = ["label" => __('order.Amount'), "name" => "total"];

        $col[] = ["label" => __('order.Supplier'), "name" => "supplier"];
        $col[] = ["label" => __('order.TAX_NUMBER'), "name" => "tax_number"];
        $col[] = ["label" => "Tracking", "name" => "parcel_name", 'width' => 100];

        $col[] = ["label" => "Handling Time", "name" => "delivery_days"];
        return $col;
    }


    //order grandtotal
    private function grnadTotal($original_result){
        $data = clone $original_result;

        $collection = collect($data);
        $result = collect($collection['data']);

        $eur_total = $result->sum('eur_total');
        $currency['EUR'] = 0;
        $orders_group = $result->groupBy('currency');

        if($orders_group->isNotEmpty()){
          foreach ($orders_group as $key => $orders) {
            $order_sum = $orders->sum("total");
            if( ($key == '') ||  (strtolower($key) == 'eur')){
              $currency['EUR'] += $order_sum;
            }else{
              $currency[$key] = $order_sum;
            }
          }
        }
        return [
          'total_eur' => $eur_total,
          'currency' => $currency
        ];
    }

    //Credits refs
    private function creditsRefs($original_result){
        $credits = [];
        $data = clone $original_result;
        $collection = collect($data);
        $credit_ids = collect($collection['data'])->whereNotNull('credit_ref')->pluck('credit_ref')->toArray();
        if(!empty($credit_ids)){
            $credits = NewOrder::whereIn('id', $credit_ids)->pluck('id')->toArray();
        }

        return $credits;
    }

    //Filter query
    private function filter_query(&$query)
    {
        if (!CRUDBooster::isSuperadmin()) {
            $query->where('cms_user_id', CRUDBooster::myParentId());

            if (CRUDBooster::isSubUser() && (sub_account_can('inkasso_status') || sub_account_can('all_modules', 122))) {
                $query->whereIn('new_orders.status', ['inkasso']);
            }
        }

        //Invoice ref
        if (isset($_REQUEST['inv_ref']) && $_REQUEST['inv_ref']) {
            $query->where('new_orders.id', $_REQUEST['inv_ref']);
        }

        //Credit ref
        if (isset($_REQUEST['credit_ref']) && $_REQUEST['credit_ref']) {
            $query->where('new_orders.id', $_REQUEST['credit_ref']);
        }

        //Droptienda item
        if (isset($_REQUEST['droptienda-item']) && $_REQUEST['droptienda-item']) {
            $query->where('new_orders.order_id_api', 'drop_t_'.$_REQUEST['droptienda-item']);
        }

        if (isset($_REQUEST['filter'])) {
            if ($_REQUEST['filter'] === "shipped") {
                $query->where('new_orders.status', 'Shipped');
            } else if ($_REQUEST['filter'] === "other") {
                $query->where('new_orders.status', '!=', 'Shipped');
            } else if ($_REQUEST['filter'] === "proforma-invoice") {
                $query->where('new_orders.invoice_number', '-1');
            } else if ($_REQUEST['filter'] === "credit_note") {
                $query->where('new_orders.credit_number', '>', 0);
            }
        }

        if (isset($_REQUEST['order_status']) && $_REQUEST['order_status']) {
            if ($_REQUEST['order_status'] === "proforma-invoice") {
                $query->where('new_orders.invoice_number', '-1');
            } else if ($_REQUEST['order_status'] === "order_placed") {
                $query->whereNotNull('supplier_id');
            } else {
                $query->where('new_orders.status', $_REQUEST['order_status']);
            }
        }

        if (isset($_REQUEST['filter']) && ($_REQUEST['filter'] === "proforma-invoice")) {
        } else if (isset($_REQUEST['order_status']) && (($_REQUEST['order_status'] === "proforma-invoice"))) {
        } else {
            $query->where(function ($q) {
                return $q->where('new_orders.invoice_number', '!=', -1)->orWhereNull('new_orders.invoice_number');
            });
        }

        if (isset($_REQUEST['order_chanel']) && $_REQUEST['order_chanel']) {

            //Channel ids
            $channel_arr = config('global.channels');
            array_push($channel_arr, 200);

            $chanel_shop_ids = \App\Shop::whereIn('channel', $channel_arr);
            if ($_REQUEST['order_chanel'] != "all") {
                $chanel_shop_ids->where('channel', $_REQUEST['order_chanel']);
            }
            if (!CRUDBooster::isSuperadmin()) {
                $chanel_shop_ids->where('user_id', '=', CRUDBooster::myParentId());
            }
            $chanel_shop_ids = $chanel_shop_ids->pluck('id')->toArray();
            //Get users all channel id

            //Filter channel id
            $query->whereIn('new_orders.shop_id', $chanel_shop_ids);
        }

        if ($_REQUEST['date_from']) {
            $date_from = $_REQUEST['date_from'] . ' 00:00:00';
            $query->whereRaw('cast(order_date as datetime) >= \'' . $date_from . '\'');
        }
        if ($_REQUEST['date_to']) {
            $date_to = $_REQUEST['date_to'] . ' 23:00:00';
            $query->whereRaw('cast(order_date as datetime) <= \'' . $date_to . '\'');
        }

        //Field rearch
        if (isset($_REQUEST["search_by_field"])) {
            $search = Request::get("q");
            $search_field = strtolower($_REQUEST["search_by_field"]);

            if(in_array($search_field, ['invoice_number', 'credit_number'])){

                //Credit 0 for invoice search
                if($search_field == 'invoice_number'){
                    $query->where('credit_number', 0);
                }

                //Find credit or invoice number
                $query->where(function ($q) use($search, $search_field) {
                    $q->orWhere($search_field, $search);
                });

            }else if($search_field == 'full_name'){

                //Search by customer name
                $query->whereHas('customer', function($cus) use($search){
                    $cus->where("full_name", "like", "%". $search ."%");
                });

            }else{

                //Default search
                $query->where(function ($q) use($search){
                    $q->orWhere('id', "like", "%" . $search . "%")
                    ->orWhere('order_id_api', "like", "%" . $search . "%")
                    ->whereHas('customer', function($cus) use($search){
                        $cus->where("full_name", "like", "%" . $search . "%");
                    });
                });
            }
        }
        //Field search end
    }


    //Order actions
    public function orderActions($row){
        $is_super_user = CRUDBooster::isSuperadmin();

        $actions = [];

        //API response action
        if($is_super_user){
            if( ((int)$row->insert_type === 1) && isShopExist($row->shop_id) ){
                $actions['a'] = ['title' => 'API Response', 'url' => url('api/response/new-invoice/'.$row->id), 'color' => 'info', "icon" => "fa fa-list"];
            }
        }

        //Credit note create or download
        if ( $this->hasPermission('credit_note') ) {
            if( ($row->credit_number == 0) && ($row->invoice_number > 0) && ($row->test_order != 1) ){
                $actions['b'] = ['title' => 'Gutschrift erstellen', 'url' => 'javascript:makeCreditNoteInit('.$row->id.')', 'color' => 'darkgray', "img-icon" => asset('images/icons/credit-note.svg')];
            }

            if($row->credit_number > 0){
                $actions['b'] = ['title' => 'Credit Note Download', 'url' => CRUDBooster::adminPath('drm_all_orders/credit-note/'.$row->id), 'target' => '_blank', "img-icon" => asset('images/icons/invoice.svg'), 'color' => 'darkgray', "icon" => "fa fa-list"];
            }
        }

        //Test order
        if ( $this->hasPermission('test_order') ) {
            $order_can_make_test = (!in_array(strtolower($row->status), ['storniert', 'canceled'] ) && ($row->test_order != 1) && ($row->invoice_number > 0))? true : false;
            if (test_order_btn()) {
                if($order_can_make_test){
                    $actions['c'] = ['title' => 'Test Invoice', 'url' => CRUDBooster::adminPath('drm_all_orders/status-test/'.$row->id), 'color' => 'darkgray', 'confirmation_confirmButtonText' => testOrderYesBtn(), 'confirmation_btn_show' => testOrderYesBtnShow(), 'confirmation_text' => 'Do you want to mark this order as Test Order?', "img-icon" => asset('images/icons/test-order.svg'), 'confirmation' => true];
                }
            } else {
                if($order_can_make_test){
                    $actions['c'] = ['title' => 'Test Invoice', 'url' => '#', 'color' => 'darkgray', 'confirmation_confirmButtonText' => testOrderYesBtn(), 'confirmation_title' => 'Ops', 'confirmation_cancelButtonText' => '5/5 Ok', 'confirmation_btn_show' => testOrderYesBtnShow(), 'confirmation_text' => 'Your test order limit over. Please contact support.', "img-icon" => asset('images/icons/test-order.svg'), 'confirmation' => true];
                }
            }
        }

        //Customer details
        if ($this->hasPermission('show_customer_detail')) {
            if(!empty($row->drm_customer_id)){
               $actions['d'] = ['title' => 'Customer', 'url' => CRUDBooster::adminPath('drm_all_customers/detail/'.$row->id), 'icon' => 'fa fa-user', "img-icon" => asset('images/icons/customer.svg'), 'color' => 'darkgray'];
            }

        }

        //Send email
        if ($this->hasPermission('send_email')) {
            if( is_null($row->mail_sent) && $row->drm_customer_id ){
                $actions['e'] = ['title' => 'Send Email', 'url' => CRUDBooster::adminPath('drm_all_orders/send-email/'.$row->id), "img-icon" => asset('images/icons/email.svg'), 'color' => 'darkgray'];
            }

            if(!is_null($row->mail_sent)){
                $actions['e'] = ['title' => 'Resend Email', 'url' => CRUDBooster::adminPath('drm_all_orders/send-email/'.$row->id), 'color' => 'warning', "img-icon" => asset('images/icons/email.svg')];
            }
        }

        //Delivery note download
        if ($this->hasPermission('delivery_note')) {
            $actions['f'] = ['title' => 'Delivery Note', 'url' => CRUDBooster::adminPath('drm_all_orders/delivery-notes/'.$row->id), 'target' => '_blank', "img-icon" => asset('images/icons/delivary-note.svg'), 'color' => 'darkgray', "icon" => "fa fa-list"];
        }

        //Invoice
        if ($this->hasPermission('show_invoice')) {
            if($row->credit_number == 0){
              $actions['g'] = ['title' => 'Invoice', 'url' => CRUDBooster::adminPath('drm_all_orders/detail/'.$row->id), 'target' => '_blank', "img-icon" => asset('images/icons/invoice.svg'), 'color' => 'darkgray', "icon" => "fa fa-list"];
            }
        }

        if(CRUDBooster::isUpdate()){
            $actions['h'] = ['title' => trans("crudbooster.action_edit_data"), 'url' => CRUDBooster::adminPath('drm_all_orders/edit/'.$row->id), 'color' => 'darkgray', "img-icon" => asset('images/icons/pencil.svg')];
        }

        //Delete
        if (CRUDBooster::isSuperadmin() || (CRUDBooster::myParentId() == 98)) {
            $actions['i'] = ['title' => 'Delete', 'url' => CRUDBooster::adminPath('drm_all_orders/delete/'.$row->id), 'color' => 'darkgray', "img-icon" => asset('images/icons/delete.svg'), 'confirmation' => true];
        }

        //End action
        return $actions;
    }

    //generate action links
    public function orederActionLinks($a){
        $confirm_alert = false;
        $url = $a['url'];

        $data_url = null;
        $confirmation_class = null;
        $alert = [];
        $confirm_box = '';

        if (isset($a['confirmation']) && ! empty($a['confirmation']) && $a['confirmation']) {
            $alert['confirmation_title'] = ! empty($a['confirmation_title']) ? $a['confirmation_title'] : trans('crudbooster.confirmation_title');
            $alert['confirmation_text'] = ! empty($a['confirmation_text']) ? $a['confirmation_text'] : trans('crudbooster.confirmation_text');
            $alert['confirmation_type'] = ! empty($a['confirmation_type']) ? $a['confirmation_type'] : 'warning';
            $alert['confirmation_showCancelButton'] = empty($a['confirmation_showCancelButton']) ? 'true' : 'false';
            $alert['confirmation_confirmButtonColor'] = ! empty($a['confirmation_confirmButtonColor']) ? $a['confirmation_confirmButtonColor'] : '#DD6B55';
            $alert['confirmation_confirmButtonText'] = ! empty($a['confirmation_confirmButtonText']) ? $a['confirmation_confirmButtonText'] : trans('crudbooster.confirmation_yes');;
            $alert['confirmation_cancelButtonText'] = ! empty($a['confirmation_cancelButtonText']) ? $a['confirmation_cancelButtonText'] : trans('crudbooster.confirmation_no');;
            $alert['confirmation_closeOnConfirm'] = empty($a['confirmation_closeOnConfirm']) ? 'true' : 'false';
            $alert['confirmation_btn_show'] = empty($a['confirmation_btn_show'])?  'true' : $a['confirmation_btn_show'];
            $confirm_alert = true;

            $confirm_box = '
            swal({
                title: "'.$alert['confirmation_title'].'",
                text: "'.$alert['confirmation_text'].'",
                type: "'.$alert['confirmation_type'].'",
                showCancelButton: '.$alert['confirmation_showCancelButton'].',
                confirmButtonColor: "'.$alert['confirmation_confirmButtonColor'].'",
                confirmButtonText: "'.$alert['confirmation_confirmButtonText'].'",
                cancelButtonText: "'.$alert['confirmation_cancelButtonText'].'",
                closeOnConfirm: '.$alert['confirmation_closeOnConfirm'].',
                showConfirmButton: '.$alert['confirmation_btn_show'].',
                },
                function(){  location.href="'.$url.'"});
            ';
            $confirm_box = str_replace('"', '&quot;', $confirm_box);

            $data_url = "data-url=\"$url\"";
            $url = 'javascript:;';
            $confirmation_class = 'url_confirmation_action';
            $onclick = 'onclick="'.addslashes($confirm_box).'"';
        }

        $label = $a['label'];
        $title = ($a['title']) ?: $a['label'];

        $color = $a['color'] ?: 'primary';
        $confirmation = $a['confirmation'];
        $target = $a['target'] ?: '_self';

        $fa_icon = isset($a['icon'])? $a['icon'] : null;
        $img_icon = isset($a['img-icon'])? $a['img-icon'] : null;


        $icon = ($img_icon)? "<img src='".$img_icon."' width='15' height='15'>" : "<i class='".$fa_icon."'></i>";
        return "<li><a class='btn btn-xs btn-$color $confirmation_class' $data_url $onclick title='$title' href='$url' target='$target'>$icon $label</a></li>";
    }

    //Check permission
    private function hasPermission($access){
        return (!CRUDBooster::isSubUser() || (CRUDBooster::isSubUser() && (sub_account_can($access) || sub_account_can('all_modules', 122))))? true : false;
    }


}
