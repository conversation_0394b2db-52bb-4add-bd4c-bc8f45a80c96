<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use CRUDBooster;
use App\OrderStatusGroup;
use App\OrderStatus;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Cache;
use DB;

class OrderStatusController extends Controller
{
    public function index(){
        if(!CRUDBooster::isSuperAdmin()){
            CRUDBooster::redirect(CRUDBooster::adminPath(),trans("crudbooster.denied_access"));
        }

    	$data = [];
    	$data['page_title'] = 'DRM order status manage.';
    	$data['status_groups'] = OrderStatusGroup::with('statuses')->get();;
    	return view('admin.order_status.index', $data);
    }

    //Html edit modal
    public function htmlModal(Request $request){

        if(!CRUDBooster::isSuperAdmin()){
            return response()->json([
             'success' => false,
             'message' => 'You do not have access permission.'
            ]);
        }

    	$request->validate([
    		'id' 		=> 'required',
    		'column' 	=> 'required',
    	]);

        try{
            $trigger_list = [
                'CREATE_CREDIT_NOTE' => 'create credit note',
                'CREATE_TEST_ORDER' => 'create test order',
                'REMAINDER_START' => 'remainder email will start for 7 days',
                'SERVICE_BLOCK' => 'block service',
                'MARK_AS_PAID' => 'order mark as paid',
                'MARK_AS_SHIPPED' => 'order mark as shipped',
            ];

            $data = [];
            $data['id'] = $id = $request->id;
            $data['column'] = $column = $request->column;
            $data['title'] = Str::title( str_replace('_', ' ', $column) );
            $data['status_group'] = $status_group = OrderStatusGroup::with('statuses')->find($id);
            $data['trigger_list'] = $trigger_list;
            $data['statuses'] = $status_group->statuses()->pluck('id')->toArray();
            $data['all_statuses'] = DB::table('order_statuses')->select('id', 'status_name')->get();
            $html = view('admin.order_status.modal_'.$request->column, $data)->render();

            return response()->json([
                'success' => true,
                'html' => $html,
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => true,
                'message' => 'Sorry! Something went wrong!',
            ]);
        }
    }

    //Update order status value
    public function update(Request $request, OrderStatusGroup $order_group){
        try{

            if(!CRUDBooster::isSuperAdmin()){
                return response()->json([
                 'success' => false,
                 'message' => 'You do not have access permission.'
                ]);
            }

            $request->validate([
                'column' => ['required', Rule::in(['group_name', 'trigger_name', 'group_color', 'group_description', 'statuses'])],
            ]);

            $column = $request->input('column');
            $value = $request->input('value');
            if($column == 'statuses'){
                DB::table('order_statuses')->where(['status_group_id' => $order_group->id])->update(['status_group_id' => null]);
                if($value){
                    DB::table('order_statuses')->whereIn('id', $value)->update(['status_group_id' => $order_group->id]);
                }
            }else{
                if(in_array($column, ['group_name'])){
                    $request->validate([
                        'value' => 'required|unique:order_status_groups,'.$column.','.$order_group->id,
                    ]);
                }
                $order_group->{$column} = $value;
                $order_group->save();
            }
            Cache::forget('drm_order_statuses');
            return response()->json([
                'success' => true,
                'message' => Str::title( str_replace('_', ' ', $column) ).' value update successfully!'
            ]);

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
