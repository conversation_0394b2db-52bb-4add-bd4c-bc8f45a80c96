<?php

namespace App\Http\Controllers\Payments\Paypal;

use App\Http\Controllers\Controller;
use App\Services\Payment\PurchasePayloadFormat;
use App\Services\Payment\ServiceList;
use App\Services\Paypal\PaypalService;
use App\Services\Stripe\Latest\ReturnCallback;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Services\Stripe\Latest\StripeService;

class PaymentController extends Controller
{
    use ServiceList;

    private PaypalService $paypal;

    public function __construct(PaypalService $paypal) {
        $this->paypal = $paypal;
    }

    /**
     * Paypal payment
     * https://dropmatix_v9.test/payment/invoice-payment/31838
     */
    public function payment(Request $request)
    {
        try {

            $metadata = $request->only([
                'user_id', 'purchase_item_type', 'item_id', 'plan_id',
                'source', 'language_id', 'product_id', 'profit_share_id',
                'range_by', 'filter_by', 'price_range',
            ]);


            // DT Tariff
            if($metadata['purchase_item_type'] === 'dtlicense')
            {
                $metadata['purchase_item_type'] = 'dt_tariff';
            }

            $validator = Validator::make($metadata, [
                'user_id' => 'required|exists:cms_users,id',
                'purchase_item_type' => ['required', Rule::in($this->serviceNameList())],
                'item_id' => 'nullable|required_if:purchase_item_type,app,marketplace_product,monthly_paywall,drm_subscription,invoice_payment,quiz,dt,shipcloud_payment',
                'plan_id' => 'nullable|required_if:purchase_item_type,appointment,plan,import,dt',

                'source' => 'nullable|required_if:purchase_item_type,translate',
                'language_id' => 'nullable|required_if:purchase_item_type,translate',
                'product_id' => 'nullable|required_if:purchase_item_type,translate',
            ]);

            if ($validator->fails()) {
                throw new \Exception($validator->errors()->first().' Please try again after refreshing this page!');
            }

            $payload = [
                'service' => $metadata['purchase_item_type'],
                'id' => $metadata['item_id'] ?? null,
                'plan_id' => $metadata['plan_id'],
                'coupon_id' => $request->input('coupon_id'),
                'user_id' => $metadata['user_id'],
            ];


            if($payload['service'] === 'translate')
            {
                $payload['price'] = $request->price;
                $payload['translate_category'] = $metadata['translate_category'] ?? null;
                $payload['source'] = $metadata['source'];
                $payload['language_id'] = $metadata['language_id'];
                $payload['product_id'] = $metadata['product_id'];

            }else if($payload['service'] === 'import' && !empty($request->profit_share_id)) {

                $payload['profit_share_id'] = $request->profit_share_id;

            }else if(in_array($payload['service'], ['buy_product', 'buy_pre_product', 'mp_cart_checkout'])) {

                $payload['price'] = $request->price;
                $payload['id'] = $metadata['order_id'] ?? null;
                
            }

            // Stripe coupon validity
            if (isset($payload['coupon_id']) && $payload['coupon_id']) {
                $stripeService = new StripeService($this->paypal->client()->getStripeKey());
                $payload['coupon'] = $stripeService->retrieveCoupon($payload['coupon_id']);
            }

            $purchaseItem = [];
            $serviceName = $payload['service'];
            if (isset($this->services[$serviceName])) {
                $purchaseItem = resolve($this->services[$serviceName])->getItem($payload);
            }

            $purchasePayload = resolve(PurchasePayloadFormat::class)($purchaseItem);

            return response()->json([
                'success' => true,
                'data' => ! empty($purchasePayload['interval']) ?
                    $this->paypal->createSubscription($purchasePayload) :
                    $this->paypal->createOrder($purchasePayload),
            ], 200);

        } catch (Exception $e) {
            return resolve(ReturnCallback::class)(['message' => $e->getMessage()]);
        }
    }

    // https://dropmatix_v9.test/payment/paypal/callback-return/stripe_key_2455?subscription_id=I-N56WVMV00923&ba_token=BA-42P16527TM699592U&token=4T805150XA132642P
    public function paymentCallback(Request $request, string $key)
    {
        try {
            if ($request->query('token') && $subscriptionId = $request->query('subscription_id')) {
                return $this->paypal->captureSubscription($subscriptionId);

            } elseif ($request->query('PayerID') && $orderId = $request->query('token')) {

                return $this->paypal->captureOrder($orderId);
            }
        } catch (Exception $e) {
            return resolve(ReturnCallback::class)(['message' => $e->getMessage()]);
        }
    }

    public function paymentCallbackCancel(Request $request, string $key)
    {
        return resolve(ReturnCallback::class)(['message' => 'Payment failed!']);
    }
}
