<?php

namespace App\Http\Controllers\Payments\Paypal;

use App\Http\Controllers\Controller;
use Dropmatix\Paypal\Paypal;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use CRUDBooster;

class PaypalController extends Controller
{
    public function index()
    {
        redirectToV2('/accounting');

        $this->keyChangePermission();

        $slug = $this->paypalKeySlug();
        $token = DB::table('paypal_keys')
            ->where('slug', $this->paypalKeySlug())
            ->select('public_key', 'secret_key', 'id')
            ->first();

        $data = [];
        $data['page_title'] = "Paypal tokens";
        $data['token'] = $token;
        $data['public_key_org'] = $data['token']->public_key;
        $data['secret_key_org'] = $data['token']->secret_key;
        return view('stripeToken.paypal', $data);
    }


    public function save(Request $request)
    {
        redirectToV2('/accounting');

        $this->keyChangePermission();

        $request->validate([
            'public_key' => 'required|max:125',
            'secret_key' => 'required|max:125',
        ]);

        try{
            //initialize value
            $slug = $this->paypalKeySlug();
            $id = DB::table('paypal_keys')->where('slug', $slug)->value('id');
            $public_key = trim($request->public_key);
            $secret_key = trim($request->secret_key);

            // Validate paypal token
            $paypal = new Paypal(
                $public_key,
                $secret_key,
                $slug
            );
            $paypal->generateToken();

            $err = null;

            //Insert or update value
            if(empty($id)){
                $created = DB::table('paypal_keys')->insert([
                    'user_id' => CRUDBooster::myParentId(),
                    'slug' => $slug,
                    'public_key' => $public_key,
                    'secret_key' => $secret_key,
                ]);
                $err = $created? null : 'Token failed to save. Please try again!';
            }else{
                $changed = DB::table('paypal_keys')->where('id', $id)->update([
                    'public_key' => $public_key,
                    'secret_key' => $secret_key,
                ]);

                $err = $changed? null : 'Nothing changed!';
            }

            // Paypal webhook
            if ($err == null) {
                try {
                    $events = [
                        'PAYMENT.SALE.REFUNDED',
                        'PAYMENT.SALE.COMPLETED',
                        'PAYMENT.REFUND.COMPLETED',
                        'CHECKOUT.ORDER.COMPLETED',
                        'CHECKOUT.ORDER.APPROVED',
                        'BILLING.SUBSCRIPTION.ACTIVATED',
                        'PAYMENT.CAPTURE.COMPLETED',
                    ];

                    $urlSlug = str_replace('_', '-', $slug);
                    $paypal->webhook->createWebHook(url('/').'/api/paypal-webhook/'.$urlSlug, $events);

                } catch (\Exception $ee) {}
            }
            // Paypal webhook end

            //Forget cache
            Cache::forget($slug);

            if($err){
                throw new \Exception($err);
            }

            return response()->json([
                'success' => true,
                'message' => 'Token saved successfully!'
            ], 200);

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }



    //Delete token
    public function delete($id){

        redirectToV2('/accounting');
        
        $this->keyChangePermission();
        try{
            $delete = DB::table('paypal_keys')->where('id', '=', $id)->where('user_id', CRUDBooster::myParentId())->delete();
            if($delete){
                $slug = $this->paypalKeySlug();
                //Forget cache
                Cache::forget($slug);

                return response()->json([
                    'success' => true,
                    'message' => 'Token delete successfully!'
                ], 200);
            }
            throw new \Exception('Something went wrong!');
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    private function paypalKeySlug(): string
    {
        return isPatrickSpecial() ? 'paypal_key_'.CRUDBooster::myId() : 'paypal_key_'.CRUDBooster::myParentId();
    }

    private function keyChangePermission(){
        // if(!in_array(CRUDBooster::myParentId(), [98, 2454, 2455, 2439, 212, 2494])){
        //  abort(404);
        // }
    }
}
