<?php

namespace App\Http\Controllers\Payments\Paypal;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\Paypal\PaypalService;
use Exception;

class WebhookController extends Controller
{

    public function subscriptionRetry($slug, $subscription)
    {
        $paypal = new PaypalService($slug);

        $data = $paypal->client()->subscription->get($subscription);
        if(empty($data)) return;

        $payload = [
            'status' => $data['status'],
            'event_type' => 'PAYMENT.SALE.COMPLETED',
            'resource' => $data,
        ];

        $client = new \GuzzleHttp\Client(['verify' => false]);
        $response = $client->request('POST', url('/api/paypal-webhook/'.$slug), [
            'json' => $payload,
        ]);

        return $response->getBody()->getContents();
    }

    public function webhook(Request $request, $key)
    {
        try {

            $event = json_decode($request->getContent(), true) ?? [];

            \Log::channel('payment')->info($event);
            
            $event_type = $event['event_type'];
            if(empty($event_type)) return;

            $paypal = new PaypalService($key);

            // BILLING.SUBSCRIPTION.ACTIVATED

            switch ($event_type) {
                case 'PAYMENT.SALE.COMPLETED':
                    $this->subscriptionActivated($paypal, $event);
                    break;
                case 'BILLING.SUBSCRIPTION.PAYMENT.FAILED':
                    $this->subscriptionPaymentFailed($paypal, $event);
                    break;

                case 'PAYMENT.CAPTURE.COMPLETED':
                case 'CHECKOUT.ORDER.APPROVED':
                case 'CHECKOUT.ORDER.COMPLETED':
                    $this->checkoutApproved($paypal, $event);
                    break;

                default:
                    echo 'Received unknown event type '.$event->type;
            }
            
        } catch (Exception $e) {
            return response($e->getMessage(), 400);
        }
    }

    // Subscription activated
    protected function subscriptionActivated(PaypalService $paypalService, array $event)
    {
        // Make previous due paid
        if($paypalService->makePreviousDuePaid($event)) {
            return;
        }

        $paypalService->renewSubscription($event);
    }

    // Subscription payment failed
    protected function subscriptionPaymentFailed(PaypalService $paypalService, array $event)
    {
        $paypalService->failedSubscriptionPayment($event);
    }

    // Checkout approved
    protected function checkoutApproved(PaypalService $paypalService, array $event)
    {
        $paypalService->checkoutApproved($event);
    }
}