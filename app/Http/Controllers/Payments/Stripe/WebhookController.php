<?php

namespace App\Http\Controllers\Payments\Stripe;

use Illuminate\Http\Request;
use App\Models\Stripe\ManualSubscription;
use App\Services\Payment\ServiceList;
use App\StripePayment;
use App\Services\Stripe\Latest\SuccessCallback;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Services\Stripe\Latest\InvoicePaymentCallback;
use App\Services\Stripe\Latest\PaymentCallback;
use App\Services\Stripe\Latest\InvoiceInitialize;
use Exception;
use App\NewOrder;
use Carbon\Carbon;

class WebhookController extends \App\Http\Controllers\Controller
{
	use ServiceList;

	public function manualSubscriptionRenew(Request $request, $id)
	{
		$subscription = ManualSubscription::where('status', 1)
		->whereDate('period_end', '<=', now())
		->find($id);
		if(!($subscription && $subscription->id)) return;

		$payload = $subscription->metadata ?? [];

		$serviceName = $payload['service'];
        if (!isset($this->services[$serviceName])) return;

        $payment = [];
        $payment['object_type'] = 'invoice';
        $payment['object_id'] = $object_id = $subscription->invoice_id;

        // //If exist on DB create
        if (StripePayment::where('version', 1)->where(['object_type' => $payment['object_type'], 'object_id' => $object_id, 'provider' => StripePayment::MANUAL])->exists()) {
            return;
        }

        $startDate = now()->format('Y-m-d');
        $interval = $payload['interval'];

        if($interval === 'day'){
            $endDate = now()->addDay()->format('Y-m-d');
        } else if ($interval === 'week') {
            $endDate = now()->addWeek()->format('Y-m-d');

        } else if ($interval === 'month') {
            $endDate = now()->addMonth()->format('Y-m-d');

        } else if ($interval === 'year') {
            $endDate = now()->addYear()->format('Y-m-d');
        } else {
        	return;
        }

        $payment['subscription_id'] = $subscription->subscription_id;
        $payment['intend_id'] = $subscription->invoice_id;

        $payment['created'] = now();
        $payment['status'] = 'succeeded';
        $payment['metadata'] = $metadata = $payload;
        $payment['response'] = [];

        //Price details - invoice payment
        $payment['sub_total'] = $metadata['sub_total'];
        $payment['total'] = $metadata['total'];

        if (isset($metadata['discount']) && $metadata['discount']) {
            $payment['discount'] = $metadata['discount'];
        }

        if(!empty($metadata['contract_for']))
        {
            $lastContract = StripePayment::where('subscription_id', $payment['subscription_id'])
            ->orderBy('id', 'desc')
            ->value('contract_end_at');

            $lastContractDate = $lastContract ? Carbon::parse($lastContract) : null;
            $newPerionStartDate = Carbon::parse($payment['period_start']);

            $payment['contract_end_at'] = empty($lastContractDate) || $lastContractDate->lt($newPerionStartDate) ? $newPerionStartDate->addMonths($metadata['contract_for']) : $lastContractDate;
        }

        $payment['period_start'] = $startDate;
        $payment['period_end'] = $endDate;
        $payment['user_id'] = $metadata['user_id'] ?? null;
        $payment['purchase_type'] = $this->serviceTypeIdByName($metadata['service'] ?? '');

        $filter_payment_data = array_filter($payment);
        $filter_payment_data['is_stripe_response'] = 1;
        $filter_payment_data['version'] = 1;

        $payment = StripePayment::updateOrCreate(['object_type' => $payment['object_type'], 'object_id' => $object_id, 'provider' => StripePayment::MANUAL], $filter_payment_data);

        $subscription->update([
        	'invoice_id' => 'siv_'.$metadata['user_id'].Str::random(15),
        	'period_end' => $endDate,
        	'last_run_at' => now(),
        ]);

        return resolve(SuccessCallback::class)($payment);
	}



	public function webhook(Request $request, $key)
	{
		try {
            $key = str_replace('-', '_', $key);
            $stripeKey = DB::table('stripe_keys')->where('slug', $key)->select('webhook_secret')->first();
            if (empty($stripeKey)) {
                throw new Exception('Webhook denied!');
            }

            if (! empty($stripeKey->webhook_secret)) {
                $event = \Stripe\Webhook::constructEvent(
                    $request->getContent(), $request->header('stripe-signature'), $stripeKey->webhook_secret
                );
            } else {

                if (config('app.stripe_secure_webhook')) {
                    throw new Exception('Invalid Signature');
                }

                $event = \Stripe\Event::constructFrom(
                    json_decode($request->getContent(), true)
                );
            }

        } catch (Exception $e) {
            return response($e->getMessage(), 400);
        }



        // Handle the event
        switch ($event->type) {
            case 'payment_intent.succeeded':
                $this->paymentIntentSuccess($event->data->object);
                break;
            case 'invoice.paid':
                $this->invoicePaid($event->data->object);
                break;
            case 'invoice.payment_failed':
                $this->invoicePaymentFailed($event->data->object);
                break;
            case 'charge.refunded':
            	$this->chargeRefunded($event->data->object);
                break;
            default:
                echo 'Received unknown event type '.$event->type;
        }

        // "invoice.payment_succeeded",
        // "invoice.payment_failed",
        // "invoice.paid",
        // "payment_intent.canceled",
        // "payment_intent.requires_action",
        // "payment_intent.succeeded",
        // "payment_intent.payment_failed"
    }

    /**
     * Pending on history
     */
    public function isPending($object_id, $object_type): bool
    {
        return StripePayment::where('version', 1)->where(['object_id' => $object_id, 'object_type' => $object_type, 'provider' => StripePayment::STRIPE])->whereNull('used_at')->exists();
    }

    /**
     * Exists on history
     */
    public function isDuplicate($object_id, $object_type): bool
    {
        return StripePayment::where('version', 1)->where(['object_id' => $object_id, 'object_type' => $object_type, 'provider' => StripePayment::STRIPE])->exists();
    }


    /**
     * Make previous due paid
     */
    public function makePreviousDuePaid($object_id, $object_type): bool
    {
        $payment = StripePayment::where('version', 1)
        ->where(['object_id' => $object_id, 'object_type' => $object_type, 'provider' => StripePayment::STRIPE])
        ->where('status', '<>', 'paid')
        ->first();

        if(blank($payment)) return false;

        $status = 'paid';

        $payment->update(['status' => $status]);

        // Change order status
        $order = DB::table('new_orders')->where(['order_id_api' => $object_id, 'intend_id' => $payment->intend_id])->first();
        if(blank($order)) return false;

        if(DB::table('order_logs')->where('order_id', $order->id)->where('payload->status', $status)->exists()) return false;
        DB::table('new_orders')->where('id', $order->id)->update(['status' => $status]);

        $log_message = 'Status changed from ' . drmHistoryLabel($order->status) . ' to ' . drmHistoryLabel($status) . ' successfully!';

        updateOrderHistory($order, $status, $log_message);

        return true;
    }

    /**
     * Payment Intent success
     */
    private function paymentIntentSuccess(\Stripe\PaymentIntent $intent)
    {
        if ($this->isPending($intent->id, $intent->object)) {
            app(PaymentCallback::class)($intent, true);
        }
    }

    /**
     * Invoice paid
     */
    private function invoicePaid(\Stripe\Invoice $invoice)
    {
        // Existing subscription
        if ($this->isPending($invoice->id, $invoice->object)) {
            return app(InvoicePaymentCallback::class)($invoice, true);
        }

        // Make previous due paid
        if($this->makePreviousDuePaid($invoice->id, $invoice->object)) {
            return;
        }

        // Duplicate check
        if ($this->isDuplicate($invoice->id, $invoice->object)) {
            return;
        }

        if ($invoice->subscription) {
            resolve(InvoiceInitialize::class)($invoice, true);

            return app(InvoicePaymentCallback::class)($invoice, true);
        }
    }

    /**
     * Invoice payment failed
     */
    private function invoicePaymentFailed(\Stripe\Invoice $invoice)
    {
        // Existing subscription
        if ($this->isPending($invoice->id, $invoice->object)) {
            return app(InvoicePaymentCallback::class)($invoice, true);
        }

        // Duplicate check
        if ($this->isDuplicate($invoice->id, $invoice->object)) {
            return;
        }

        if ($invoice->subscription) {
            resolve(InvoiceInitialize::class)($invoice, true);
            return app(InvoicePaymentCallback::class)($invoice, true);
        }
    }

   	//Refund payment
	public function chargeRefunded(\Stripe\Charge $charge){
		try{

			if(empty($charge->payment_intent)) return 'Empty intend ID';

			$payment_intent = $charge->payment_intent;
			$paid = $charge->paid;
			$status = $charge->status;

			$order = NewOrder::where('intend_id', $payment_intent)->first();
			if(empty($order) || empty($order->id)) return 'Empty order';

			if($status === 'succeeded')
			{
                // STORE_ORDER_STATUS_ON_DATABASE
				$order->update(['status' => 'refund_completed']);
			}
			updateOrderHistory($order, 'payment_refund_webhook', 'Stripe payment refund. Status: ' . $status);
			return 'Refund response success';
		}catch(Exception $e) {
			return $e->getMessage();
		}
	}
}
