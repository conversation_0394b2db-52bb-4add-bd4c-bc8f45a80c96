<?php
namespace App\Http\Controllers\Product;


use Illuminate\Http\Request;
use App\Helper\DrmHelper;
use DateTime;
use App\Services\ProductApi\Services\Rainforest;
use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Enums\AmazonDomain;
use App\MarketplaceProducts;

class AmazonASINConvertController
{

	public function index(Request $request)
	{
		$userId = CRUDBooster::myParentId();

        $data = [];
        if ($request->get('file') && !$request->get('import')) {
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;

            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = [
                'ean',
                'purchase_price',
                'uvp',
                'image',
                'title',
                'item_number',
                'brand',
                'supplier',
                'availability',
            ];

            $labels = [
                0 => 'EAN',
                1 => __('Purchase Price'),
                2 => __('UVP'),
                3 => __('Image'),
                4 => __('Product Name'),
                5 => __('Item Number'),
                6 => __('brand'),
                7 => __('Supplier'),
                8 => __('Inventory'),
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        $data['page_title'] = 'Competitive analysis import';
        $data['countries'] = AmazonDomain::DOMAINLIST;

        return view('admin.cp_analysis.asin_to_ean_convert.index', $data);
	}

    public function upload_ean(Request $request){

        if ($request->has('asin')) {
            Cache::put('cp_asin_to_ean_convert', []);

            $asin = $request->asin;
            $domain = $request->domain;
            $details = DB::table('amazon_asin_collections')->where('asin', $asin)->where('amazon_domain_code', $domain)->pluck('ean')->toArray();
            if(!$details){
                $rainforest = new Rainforest();
                $details = $rainforest->getEanFromAsin($asin, $domain);
                foreach($details as $item){
                    DB::table('amazon_asin_collections')->updateOrInsert(
                        [
                            'ean' => $item,
                            'asin' => $asin,
                            'amazon_domain_code' => $domain,
                            'domain' => 3
                        ],
                        []
                    );
                }
            }
            $results = [];
            foreach($details as $item){
                $mp_product = MarketplaceProducts::where('ean', $item)->first();
                $mp_details = [];
                if($mp_product){
                    $mp_details['id'] = $mp_product->id;
                    $mp_details['title'] = $mp_product->name;
                    $mp_details['price'] = $mp_product->vk_price;
                    $mp_details['image'] = $mp_product->image ? json_decode($mp_product->image)[0] : "https://dropmatix.fra1.digitaloceanspaces.com/images/no_image.jpg";
                }
                $results[] = ['ean' => $item, 'mp_details' => $mp_details];
            }

            $url = route('drm.amazon_asin_convert.amazon.asin.convert').'?asin='.$asin;
            Cache::put('cp_asin_to_ean_convert', $results);
            return redirect($url)->with(['details' => $results]);
        } else {
            return redirect()->back();
        }
    }
}
