<?php
namespace App\Http\Controllers\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Helper\DrmHelper;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\AdminDrmImportsController;
use Illuminate\Support\Facades\Session;
use Exception;
use Illuminate\Support\Facades\Cache;
use App\Models\Product\AmazonTopSellers;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\LazyCollection;

class AmazonTopSellerController
{
    public function index(Request $request){
        $user_id = CRUDBooster::myParentId();
        
        $seller_list = AmazonTopSellers::whereNull('deleted_at');

        $filters = $request->all();

        if(!empty($filters['q'])){
          $q = $filters['q'];
          $seller_list->where('name', 'LIKE', "%$q%");
        }

        if($filters['uses_fba'] != null && $filters['uses_fba'] != 'all'){
            $seller_list->where('uses_fba', $filters['uses_fba']);
        }

        if($filters['category'] && $filters['category'] != 'all'){
            $seller_list->where('category', $filters['category']);
        }

        if($filters['min_rating'] && $filters['max_rating']){
            if($filters['min_rating'] <= $filters['max_rating']){
                $seller_list->where('rating', '>=', $filters['min_rating'])->where('rating', '<=', $filters['max_rating']);
                $data['min_rating'] = $filters['min_rating'];
                $data['max_rating'] = $filters['max_rating'];
            }
        }

        $order_by = null;

        $sort_filter = $request->get('filter_column') ?? [];
        if(!empty($sort_filter))
        {
            $order_by = key($sort_filter);
            $sorting = $sort_filter[$order_by]['sorting'];
        }

        if(!$order_by && !$sorting){
            $order_by = 'id';
            $sorting = 'asc';
        }

        if($order_by && $sorting){
            $seller_list->orderBy($order_by, $sorting);
        }

        $limit = $request->get('limit') ?? 20;

        $data['products'] = $seller_list->paginate($limit);
        $data['total'] = $data['products']->total();

        $data['categories'] = AmazonTopSellers::groupBy('category')->orderBy('category')->pluck('category')->toArray() ?? [];

        $columns = $this->table_column();
        $data['all_columns'] = $columns;

        $data['uses_fba'] = $filters['uses_fba'];
        $data['selected_cat'] = $filters['category'];

        $data['user_id'] = $user_id;
        
        return view('admin.cp_analysis.amazon_top_seller.index', $data);
    }

    public function table_column(){
        return [
            // 'id'                    => ["label" => __('ID') , "sorting" => true],
            'rank'                  => ["label" => __('Rank') , "sorting" => true],
            'name'                  => ["label" => __('Seller Name') , "sorting" => true],
            'rating'                => ["label" => __('Rating') , "sorting" => true],
            'rating_count_total'    => ["label" => __('rating_count_total') , "sorting" => true],
            'rating_count_last_30'  => ["label" => __('rating_count_30') , "sorting" => true],
            'uses_fba'              => ["label" => __('uses_fba') , "sorting" => false],
            'verified_listing'      => ["label" => __('Verified Listing') , "sorting" => true],
            'category'              => ["label" => __('Category') , "sorting" => true],
        ];
    }

    public function import(Request $request){

        $userId = CRUDBooster::myParentId();

        $data = [];
        if ($request->get('file') && !$request->get('import')) {
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;

            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = [
                'rank',
                'name',
                'rating',
                'rating_count_total',
                'rating_count_last_30',
                'uses_fba',
                'verified_listing',
                'category'
            ];

            $labels = [
                0 => __('Rank'),
                1 => __('Seller Name'),
                2 => __('Rating'),
                3 => __('rating_count_total'),
                4 => __('rating_count_30'),
                5 => __('uses_fba'),
                6 => __('Verified Listing'),
                7 => __('Category'),
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        $data['page_title'] = 'Amazon Top Sellers Import';
        return view('admin.cp_analysis.amazon_top_seller.admin_csv_upload', $data);
    }

    public function uploadCsv(Request $request){

        if ($request->hasFile('userfile')) {

            try{

                $extensions = array("xls","xlsx","csv");
                $file = $request->file('userfile');
                $ext = $file->getClientOriginalExtension();
                if(!in_array($ext, $extensions)){
                    $url = route('drm.amazon_top_seller.import');

                    return CRUDBooster::redirect($url, "Please Upload a Valid File");
                } 

                $filePath = 'uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
                Storage::makeDirectory($filePath);

                $filename = md5(Str::random(5)).'.'.$ext;
                $url_filename = '';
                if (Storage::putFileAs($filePath, $file, $filename)) {
                    $url_filename = $filePath.'/'.$filename;
                }
                $url = route('drm.amazon_top_seller.import').'?file='.base64_encode($url_filename);

                return redirect($url);
            } catch(Exception $e){
                $url = route('drm.amazon_top_seller.import');

                return CRUDBooster::redirect($url, "Please Upload a Valid File");
            }
        } else {
            return redirect()->back();
        }
    }

    public function done_import(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Import Products"]);
        Session::put('select_column', $request->get('select_column'));

        return view('admin.cp_analysis.amazon_top_seller.admin_csv_upload', $data);
    }

    public function do_import_chunk(Request $request)
    {
        try{
            $file_md5 = md5($request->get('file'));

            if ($request->get('file') && $request->get('action_type') != 0) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }

                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }

            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);

            $table_columns = [
                'rank',
                'name',
                'rating',
                'rating_count_total',
                'rating_count_last_30',
                'uses_fba',
                'verified_listing',
                'category'
            ];

            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $this->csvReader($file, $type, 'auto', false);

            $count = 0;

            $csvSellerList = [];
            foreach ($rows as $row) {
                $count++;
                if($request->get('action_type') != 0){
                    Cache::put('success_' . $file_md5, $count);
                }
                $row = (object) $row;
                foreach ($select_column as $csvColName => $val) {

                    $colname = $table_columns[$csvColName];

                    if($colname == 'uses_fba'){
                        $row->$val = $row->$val == 'yes' ? 1 : 0;
                    }
                    elseif($colname == 'category'){
                        $row->$val = preg_replace("/\([^)]+\)/","",$row->$val);
                        $row->$val = trim($row->$val);
                    }

                    $data[$colname] = $row->$val;
                }

                $data['deleted_at'] = null;
                $csvSellerList[] = $data;

            }

            AmazonTopSellers::whereNull('deleted_at')->delete();

            $chunks = array_chunk($csvSellerList, 500);
            foreach ($chunks as $chunk) {
                foreach ($chunk as $data){
                    AmazonTopSellers::create(
                        $data
                    );
                }
            }
        
            return response()->json(['status' => true, 'message' => $res['message']]);
        }catch (Exception $e) {
            dd($e);
            return response()->json(['status' => false, 'message' => "Sorry, something went wrong"]);
        }
    }

    public function getSelectedIds($params){
        $ids = KeepaCSVProduct::where('interval', $params['interval'])->where('category_id', $params['category']);
        if($params['mp_only'] == 1){
            $ids->where('mp_available', 1);
        }

        $search_by_field = $params['search_by_field_column'];

        if(!empty($search_by_field) && !empty($filters['q'])){
          $q = $params['q'];
          if($search_by_field == 'title' || $search_by_field == 'all'){
              $q = "%$q%";
          }
          if($search_by_field != 'all'){
              $products->where($search_by_field, 'LIKE', $q);
          }else{
              $products->where(function($p) use ($q) {
                  $p->where('ean', 'LIKE', $q);
                  $p->orWhere('asin', 'LIKE', $q);
                  $p->orWhere('title', 'LIKE', "%$q%");
                  $p->orWhere('id', 'LIKE', $q);
              });
          }
        }
        return $ids->pluck('id')->toArray();
    }

    public function csvReader($csv, $type, $delimiter, $deleteFile = true)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $paths = explode(';', $csv);
        $key = null;
        $key_count = 0;
        $array = array();
        $rand = Str::random(40);
        foreach ($paths as $path) {
            if ($deleteFile) {
                $path = Storage::disk('spaces')->url($path);
                $file_type = pathinfo($path, PATHINFO_EXTENSION);
                $file = file_get_contents($path);
                file_put_contents($rand . '.' . $file_type, $file);
                $localpath = $rand . '.' . $file_type;
            } else {
                $localpath = $path;
            }
            if ($type == 'csv' || $type == 'txt') {
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');

                if ($delimiter != 'auto') {
                    $reader->setDelimiter($delimiter);
                }
                $spreadsheet = $reader->load($localpath);
            } else {
                $spreadsheet = IOFactory::load($localpath);
            }

            // Convert all cell values to string
            $spreadsheet->getActiveSheet()
                ->getStyle('A1:'.$spreadsheet->getActiveSheet()->getHighestDataColumn().$spreadsheet->getActiveSheet()->getHighestDataRow())
                ->getNumberFormat()
                ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);

            $spreadsheetArray = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
            $collection = LazyCollection::make($spreadsheetArray);

            if ($key == null) {
                $key = array_map('trim', $collection->first());
                $key_count = count($key);
            }
            $key = array_map('removeDots', $key);
            $collection = $collection->except(1);
            foreach ($collection as $row) {
                if (count($row) == $key_count && !containsOnlyNull($row)) {
                    $array[] = array_combine($key, $row);
                }

            }

            if (!pathIsUrl($path) && $deleteFile) {
                unlink($localpath);
            }
        }
        return $array;
    }
}