<?php
namespace App\Http\Controllers\Product;


use Illuminate\Http\Request;

use App\Helper\DrmHelper;
use DateTime;

use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use Illuminate\Support\Facades\DB;
use App\Models\Product\AnalysisBuyingChoices;
use App\MarketplaceProducts;
use App\DrmProduct;

class AnalysisBuyingChoicesController
{
    public function index(Request $request)
	{
        
		$user_id = CRUDBooster::myParentId();

        $sorting = null;
        $order_by = null;

        $sort_filter = $request->get('filter_column') ?? [];
        if(!empty($sort_filter))
        {
            $order_by = key($sort_filter);
            $sorting = $sort_filter[$order_by]['sorting'];
        }

        $search_by_field = $request->get('search_by_field_column');
        $filters = $request->all();

        $products = DB::table('analysis_buying_choices')->join('analysis_products', 'analysis_buying_choices.analysis_product_id', '=', 'analysis_products.id')
                        ->leftJoin('drm_products', function ($join) {
                            $join->on('analysis_products.ean', '=', 'drm_products.ean');
                            $join->on('analysis_products.user_id', '=', 'drm_products.user_id');
                        })
                        ->where('analysis_buying_choices.user_id', $user_id)
                        ->select('analysis_buying_choices.*', 'analysis_products.title', 'analysis_products.ean', 'analysis_products.amazon_product_number', 'analysis_products.image', 'analysis_products.amazon_rating', 'analysis_products.amazon_rating_count', 'analysis_products.amazon_price', 'analysis_products.amazon_also_bought', 'drm_products.vk_price as core_price');

        if($order_by && $sorting){
            $products->orderBy($order_by, $sorting);
        }

        if(!empty($search_by_field) && !empty($filters['q'])){
            $q = $filters['q'];
            if($search_by_field == 'title' || $search_by_field == 'all'){
                $q = "%$q%";
            }
            if($search_by_field != 'all'){
                $products->where('analysis_products.'.$search_by_field, 'LIKE', $q);
            }else{
                $products->where(function($p) use ($q) {
                    $p->where('analysis_products.ean', 'LIKE', $q);
                    $p->orWhere('analysis_products.title', 'LIKE', "%$q%");
                    $p->orWhere('analysis_products.id', 'LIKE', $q);
                });
            }
        }

        $limit = $request->get('limit') ?? 20;
		$products = $products->orderBy('analysis_buying_choices.id','desc')->paginate($limit);

        $table_column = $this->table_column();

		$product_collection = $products->getCollection()
            ->map(function($item) use ($user_id) {
                // dd($item->ean, $item->amazon_also_bought);
                $mp_product_original = MarketplaceProducts::where('ean', $item->ean)->orderBy('vk_price', 'asc')->orderBy('stock', 'desc')->first();

                $image = json_decode($item->image);

                if(empty($image)){
                    $image = asset('images/blank-product.png');
                }

                $buying_choices = json_decode($item->amazon_also_bought) ?? [];

                foreach($buying_choices as $q){
                    $core_product = DrmProduct::where('ean', $q->ean)->where('user_id', $user_id)->orderBy('vk_price', 'asc')->orderBy('stock', 'desc')->select('id', 'vk_price')->first();
                    $q->core_exists = empty($core_product) ? false : true;
                    $q->core_id = empty($core_product) ? null : $core_product->id;
                    $q->core_price = empty($core_product) ? 0 : $core_product->vk_price;
                    $mp_product = MarketplaceProducts::where('ean', $q->ean)->orderBy('vk_price', 'asc')->orderBy('stock', 'desc')->select('id', 'vk_price')->first();
                    $q->mp_exists = empty($mp_product) ? false : true;
                    $q->mp_id = empty($mp_product) ? null : $mp_product->id;
                    $q->mp_price = empty($mp_product) ? 0 : $mp_product->vk_price;
                }

                $row  = [
                    'id' => $item->id,
                    'title' => $item->title,
                    'asin' => $item->amazon_product_number,
                    'ean' => $item->ean,
                    'image' => $image,
                    'core_vk_price' => $item->core_vk_price ?? 0,
                    'mp_vk_price' => $mp_product_original->vk_price ?? 0,
                    'amazon_rating' => $item->amazon_rating ?? 0,
                    'amazon_rating_count' => $item->amazon_rating_count ?? 0,
                    'amazon_price' => $item->amazon_price,
                    'buying_choices' => $buying_choices,
                    'isLoading' => $item->loading
                ];

                return $row;

			});

		$data = [];
        $products->setCollection($product_collection);
        $productIds = $products->pluck('id')->toArray();
        $data['products'] = $products;
        $data['product_ids'] = $productIds;

        $data['columns'] = $table_column;

        $data['all_columns'] = $table_column;

        $data['languageId'] = app('App\Services\UserService')->getProductCountry($user_id);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($data['languageId']);

        $data['user_id'] = $user_id;
		return view('admin.cp_analysis.buying_choices.index', $data);
	}



    public function table_column(){
        return [
            'image'                 => ["label" => __('cp_image') , "sorting" => false],
            'title'                 => ["label" => __('Title') , "sorting" => false],
            'asin'                  => ["label" => __('ASIN') , "sorting" => false],
            'ean'                   => ["label" => __("EAN") , "sorting" => false],
            'amazon_price'          => ["label" => __('Amazon Price') , "sorting" => false],
            'drm_price'             => ["label" => __('VK Price') , "sorting" => false],
            'marketplace_price'     => ["label" => __('Marketplace Purchase Price') , "sorting" => false],
            'amazon_rating'         => ["label" => __('Rating') , "sorting" => false],
        ];
    }

    public function delete(Request $request){
        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->product_ids;
        $isCheckAll = $request->isCheckAll;
        $params = $request->params;
        if($isCheckAll == 'true'){
            $product_ids = $this->getSeletedIds($user_id, $params);
            $chunks = array_chunk($product_ids, 500);
            foreach($chunks as $chunk){
                DB::table('analysis_buying_choices')->whereIntegerInRaw('id', $chunk)->delete();
            }
        }
        else{
            DB::table('analysis_buying_choices')->where('user_id', $user_id)->whereIntegerInRaw('id', $product_ids)->delete();
        }

        return response()->json([
            'success' => true,
        ]);
    }

    public function getSeletedIds($user_id, $params){
        $products = DB::table('analysis_buying_choices')->join('analysis_products', 'analysis_buying_choices.analysis_product_id', '=', 'analysis_products.id');

        $search_by_field = $params['search_by_field_column'];
        $q = $params['q'];

        if(!empty($search_by_field) && !empty($q)){
            if($search_by_field == 'title' || $search_by_field == 'all'){
                $q = "%$q%";
            }
            if($search_by_field != 'all'){
                $products->where($search_by_field, 'LIKE', $q);
            }else{
                $products->where(function($p) use ($q) {
                    $p->where('analysis_products.ean', 'LIKE', $q);
                    $p->orWhere('analysis_products.title', 'LIKE', "%$q%");
                    $p->orWhere('analysis_products.id', 'LIKE', $q);
                });
            }
        }

        $limit = $params['limit'] ?? 20;
        $products = $products->where('analysis_buying_choices.user_id', $user_id)->orderBy('analysis_buying_choices.id','desc')->pluck('analysis_buying_choices.id')->toArray();
        return $products;
    }

}
