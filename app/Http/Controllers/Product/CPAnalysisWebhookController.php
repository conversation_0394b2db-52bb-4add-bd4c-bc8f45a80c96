<?php
namespace App\Http\Controllers\Product;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Product\AnalysisProduct;
use App\Models\Product\CPAnalysisRequest;
// use App\Models\Product\CPAnalysisResponse;
// use App\Models\Product\CPAnalysisProductsHistory;
use App\Jobs\CPAnalysisWebhook;
// use App\DrmProduct;
use App\ProductPriceApi;
use App\Services\ProductApi\Services\ProductApiInterface;
use Carbon\Carbon;
// use Illuminate\Database\Eloquent\Collection;
// use Illuminate\Support\Facades\Storage;
// use Illuminate\Support\Facades\Log;


use App\Services\ProductApi\Webhook\WebhookInterface;
use Illuminate\Support\Facades\Log;

class CPAnalysisWebhookController extends Controller
{
	public function countdownWebhook(Request $request)
	{

		$response_data = json_decode(request()->getContent(), true);
        $analysis = new CPAnalysisWebhook($response_data, 'Countdown');
        try{
			$this->dispatch($analysis);
		}catch(Exception $e){

		}

		return response()->json([
			'success' => true,
			'message' => 'Webhook updates successfully',
		]);
	}

	public function rainforestWebhook(Request $request)
	{

		$response_data = json_decode(request()->getContent(), true);
        $analysis = new CPAnalysisWebhook($response_data, 'Rainforest');
        try{
			$this->dispatch($analysis);
		}catch(Exception $e){
			
		}

		return response()->json([
			'success' => true,
			'message' => 'Webhook updates successfully',
		]);
	}

    public function googleshoppingWebhook(Request $request)
	{

		$response_data1 = json_decode(request()->getContent(), true);

        $response_data = [
            "request_info" => $response_data1["request_info"],
            "result_set" => $response_data1["result_set"],
            "collection" => $response_data1["batch"],
        ];
        $analysis = new CPAnalysisWebhook($response_data, 'Googleshopping');
        try{
			$this->dispatch($analysis);
		}catch(Exception $e){
			
		}

        return response()->json([
			'success' => true,
			'message' => 'Webhook updates successfully',
		]);
	}

	//Process webhook
	public function processWebhook(array $response_data, WebhookInterface $driver, ProductApiInterface $service, string $type)
	{
		$collection = $response_data['collection'];
		$collection_id = $collection['id'];
		$collection_name = $collection['name'];
			//id: 1975AB64
			//name: Test collection - DRM Developers

		$request_info = $response_data['request_info'];
			// success: true
			// type: collection_resultset_completed

		$result_set = $response_data['result_set'];
		$download_links = $result_set['download_links'];
		$ended_at = $result_set['ended_at'];
		$json_links = $download_links['json'];
		$json_pages = $json_links['pages']; // all_pages

		$collection_column_name = $driver->collectionColumnName();

        $db_request_id = CPAnalysisRequest::select('id')
        ->where($collection_column_name, $collection_id)
        ->value('id');

        // if(empty($db_request_id)) return;

        // if(empty($json_pages)) return;

        $column_prefix = $driver->columnPrefix();
        $source = $driver->source();

        $db_request_column = $column_prefix.'id';

		foreach($json_pages as $index => $page)
		{
			$data = file_get_contents($page);
			$res_list = json_decode($data, true);

            // $file_name = "ebay/$collection_id/$index.json";
            // _log($file_name, 'countdown.txt');
            // Storage::disk('spaces')->put($file_name, $data, 'public');

            $addPrices = [];
			foreach($res_list as $res)
			{
                $ebay_request_id = $res['id'];

				$result = $res['result'];
				// if(empty($result)) continue;

				$price = $driver->getPrice($result);
                $title = $driver->getTitle($result);
                $rating = $driver->getRating($result);
                $rating_count = $driver->getRatingCount($result);
                $product_number = $driver->getProductNumber($result);

				if($type == "Googleshopping"){
                    $request_metadata = $result['search_metadata'];
                    $request_parameters = $result['search_parameters'];
                    $gtin = $request_parameters['q'];
                }
                else{
                    $request_metadata = $result['request_metadata'];
                    $request_parameters = $result['request_parameters'];
                    $gtin = $request_parameters['gtin'];
                }
				$processed_at = $request_metadata['processed_at'];


				if(empty($gtin)) continue;

                // $gtin = str_pad($gtin, 13, '0', STR_PAD_LEFT);

                $product_data = [
					"{$column_prefix}price" => $price,
                    "{$column_prefix}rating" => $rating,
                    "{$column_prefix}rating_count" => $rating_count,
                    "{$column_prefix}product_number" => $product_number,
					"{$column_prefix}last_sync" => $processed_at,
					"{$column_prefix}request_id" => $ebay_request_id,
				];
                $res1 = AnalysisProduct::where('ean', $gtin)
				->update($product_data);
                Log::channel('command')->info($res1);

                $addPrices[] = [
                    'ean' => $gtin,
                    'source' => $source,
                    'title' => $title,
                    'price' => $price,
                    'rating' => $rating,
                    'rating_count' => $rating_count,
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now(),
                ];
			}

            ProductPriceApi::insert($addPrices);
		}

		//Update request
		CPAnalysisRequest::where($collection_column_name, $collection_id)
		->update([
			"{$column_prefix}last_sync" => $ended_at,
		]);
        $collection_att = CPAnalysisRequest::select('forced')->where($collection_column_name, $collection_id)->get();
        if($collection_att->forced == 1){
            $service = new $service;
            $service->deleteCollection($collection_id);
        }
	}


}
