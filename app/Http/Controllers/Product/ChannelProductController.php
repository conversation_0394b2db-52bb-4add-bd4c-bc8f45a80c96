<?php

namespace App\Http\Controllers\Product;

use App\CustomIndustryTemplate;
use App\DrmProduct;
use App\DropmatixProductBrand;
use App\Enums\Apps;
use App\Enums\CategoryType;
use App\Enums\Channel;
use App\Enums\ChannelProductConnectedStatus;
use App\Http\Controllers\Controller;
use App\Jobs\ChannelManager\AssignCategory;
use App\Jobs\ChannelManager\Disconnect;
use App\Jobs\ChannelManager\Export;
use App\Jobs\ChannelManager\TransferProduct;
use App\Jobs\ChannelManager\TransferProductBeta;
use App\Jobs\ChannelProductUpdateStatus;
use App\Jobs\DecathlonExportJob;
use App\Jobs\DestroyChannelProduct;
use App\Jobs\ProcessDecathlonColumnUpdateJob;
use App\Models\ChannelCategory;
use App\Models\ChannelProduct;
use App\Models\ChannelProductCategory;
use App\Models\ChannelUserCategory;
use App\Models\Product\AnalysisProduct;
use App\Models\Product\ProfitCalculation;
use App\Option;
use App\ProductPriceApi;
use App\Services\AppStoreService;
use App\Services\ChannelCategoryService;
use App\Services\ChannelProductService;
use App\Services\Keepa\Keepa;
use App\Services\Modules\Export\Ebay\Api;
use App\Services\Modules\Export\Etsy\EtsyService;
use App\Services\Modules\Export\Kaufland\Api as KauflandApi;
use App\Services\Modules\Export\Mirakl\MiraklChannelManager;
use App\Shop;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Throwable;

class ChannelProductController extends Controller
{
    private ChannelProductService $productService;

    private string $slug = 'channel-products';
    private int $module_id;

    public function __construct(ChannelProductService $productService)
    {
        $this->productService = $productService;
        $this->module_id = config('modules.list.'.$this->slug);
    }

    public function index(Request $request)
    {
        redirectToV2('/products');

        if(!$this->canAccess('view')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $currentUserId = $data['currentUserId'] = CRUDBooster::myParentId();

        $isSuperAdmin = CRUDBooster::isSuperadmin();

        if(!app(AppStoreService::class)->checkImportPayment($currentUserId)){
            return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
        }

        if(!$currentUserId){
            return redirect(CRUDBooster::adminPath('login'));
        }

        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);

        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $data['channels'] = $this->productService->getUserShops($currentUserId,$lang);
        $data['lang'] = $lang;

        $currentChannel = $data['channels']->where('id',$request->get('shop', Channel::GAMBIO))->first();
        $data['activeChannel'] = $currentChannel->channel;
        $data['shop_id'] = $currentChannel->id;

        $data['shop_mp_synced_at'] = $currentChannel->shop_mp_synced_at;

        $blocked_channels = userBlockedChannels($currentUserId, $languageId);
        if (in_array($data['activeChannel'], $blocked_channels)) {
            return redirect(CRUDBooster::adminPath('drm_products?lang='.$lang))->with(['message' => 'Sorry, Selected channel is blocked! ' . __('channel_blocked_msg'), 'message_type' => 'danger']);
        }
        $data['channelName'] = ucfirst(strtolower($currentChannel->shop_name));
        $data['page_title'] = "<img width='30px' src='".getChannelLogo($data['activeChannel'])."'> &nbsp;".ucfirst(strtolower($currentChannel->shop_name)).' '.__('Products');

        $data['withCategory'] = false;
        if(!in_array($data['activeChannel'],Channel::WITHOUT_CATEGORY)){
            // $data['categories'] = $this->productService->getShopCategories($currentChannel, $currentUserId, $lang);
            $data['withCategory'] = true;
        }

        $data['channelProducts'] = $this->productService->all(array_merge($request->all(), [
            'user_id' => !$isSuperAdmin ? $currentUserId : '',
            'channel' => $currentChannel->channel,
            'shop_id' => $currentChannel->id,
            'lang' => $lang,
//            'country_id' => $languageId
        ]));

        // foreach ($data['channels'] as $key => $types){
        //     $channel_product_id = ChannelProduct::where('channel',$types['type'])->value('id');
        //     $data['channels'][$key]['average'] = $this->averagePercentage($channel_product_id);
        // }
//        dd(ex_time());
        $data['global_rq'] = @get_option('rq_default', 'rq', $currentUserId)->option_value;
        $data['automagic_limit'] = Option::where(['option_group' => 'automagic_product_update', 'user_id' => $currentUserId])->get()->keyBy('option_key');

        $data['deluxe_or_higher'] = deluxeOrHigher($currentUserId);

        $data['universal_export_purchased'] = true;

        if($currentChannel->channel != Channel::DROPTIENDA){
            if(!$data['deluxe_or_higher']){
                $data['universal_export_purchased'] = app(AppStoreService::class)->checkAppPurchased(Apps::UNIVERSAL_EXPORT, $currentUserId);
            }
        }

        $show_cheapest_column = $has_repricing_calculation = false;
        $min_max_price_missing_count = [
            'min_missing_count' => 0,
            'max_missing_count' => 0
        ];
        if ($data['deluxe_or_higher'] && in_array($data['activeChannel'], \App\Enums\Channel::CHEAPEST_APPLICABLE_CHANNELS)) {
            $show_cheapest_column = true;

            $has_repricing_calculation = ProfitCalculation::where([
                'user_id' => $currentUserId,
                'is_repricing' => 1,
                'repricing_status' => 1,
            ])
            ->exists();

            if ($has_repricing_calculation) {
                $min_missing_count = ChannelProduct::where([
                        'user_id' => !$isSuperAdmin ? $currentUserId : '',
                        'channel' => $currentChannel->channel,
                        'shop_id' => $currentChannel->id,
                        'country_id' => $languageId
                    ])
                    ->whereNull('min_price')
                    ->count();

                $max_missing_count = ChannelProduct::where([
                        'user_id' => !$isSuperAdmin ? $currentUserId : '',
                        'channel' => $currentChannel->channel,
                        'shop_id' => $currentChannel->id,
                        'country_id' => $languageId
                    ])
                    ->whereNull('max_price')
                    ->count();

                $min_max_price_missing_count = [
                    'min_missing_count' => $min_missing_count,
                    'max_missing_count' => $max_missing_count,
                ];
            }
        }
        $data['show_cheapest_column'] = $show_cheapest_column;

        // all key are in minutes
        $repricing_intervals = [
            1    => __('Every') . ' ' . 1 . ' ' . __('Minute'),
            5    => __('Every') . ' ' . 5 . ' ' . __('Minutes'),
            15   => __('Every') . ' ' . 15 . ' ' . __('Minutes'),
            60   => __('Every') . ' ' . __('Hour'),
            180  => __('Every') . ' ' . 3 . ' ' . __('Hours'),
            360  => __('Every') . ' ' . 6 . ' ' . __('Hours'),
            720  => __('Every') . ' ' . 12 . ' ' . __('Hours'),
            1440 => __('Every') . ' ' . 24 . ' ' . __('Hours'),
        ];
        $data['repricing_intervals'] = $repricing_intervals;

        $data['blocked_channels'] = $blocked_channels;

        $data['has_repricing_calculation'] = $has_repricing_calculation;
        $data['min_max_price_missing_count'] = $min_max_price_missing_count;

        return view('channel_products.index', $data);
    }

    /*
     * Transfer DRM product to Channel
     */
    public function transfer(Request $request): JsonResponse
    {
        if(!$this->canAccess('export')){
            return response()->json(['success' => false], 401);
        }
        $user_id = CRUDBooster::myParentId();
        $channel = $request->get('channel');
        $distribution_app_purchase = $request->get('distribution_app_purchase');

        if(in_array($channel,[Channel::DROPTIENDA,Channel::AMAZON])){
            return $this->transferBeta($request);
        }
//        try {
        $fields = $request->get('selected_fields');

        $source = $request->params['search_by_source'];
        $lang = app('\App\Services\UserService')->getLang($user_id);

        $productIds = $request->get('product_ids');

        $stock_percentage = (int) $request->get('stock_percentage');

        if((int)$request->selected_all){
            $productIds = app('\App\Services\DRMProductService')->getSelectedIds($user_id,$request->params);
        }

        if($request->analysis_bulk == "true"){
            $productIds = app(\App\Http\Controllers\Product\CompetitiveAnalysisController::class)->getTransferProductIds($channel, $user_id, $source);
        }

        if( $source == 2){
            $productIds = app(\App\Http\Controllers\Product\CompetitiveAnalysisController::class)->getDRMPproductIds($user_id, $productIds);
        }

        if(in_array($channel,Channel::MP_BLACKLIST) && !in_array($user_id,Channel::MP_USER_WHITELIST[$channel])){
            $ids = array();
            foreach (array_chunk($productIds,400) as $items) {
                $ids = array_merge($ids,DrmProduct::whereIn('id',$items)->whereNull('marketplace_product_id')->pluck('id')->toArray());
            }
            $productIds = $ids;
        }

        $data = [];
        $user_default_calculation = ProfitCalculation::where(["user_id" => $user_id, 'auto_calculation' => 1])->first();

        $data['user_default_calculation'] = $user_default_calculation;
        $data['shop_id'] = $request->shop_id;
        $data['distribution_app_purchase'] = $distribution_app_purchase;
        $data['current_user_id'] = $user_id;

        foreach (array_chunk($productIds,100) as $items) {
            TransferProduct::dispatch($items,$channel, $fields, $lang, 'create', (bool)$request->force_reset, $stock_percentage, $data);
        }

        return response()->json(['success' => true, 'message' => 'Product transferred successfully.']);
//        } catch (\Exception $e) {
//            return response()->json(['success' => false, 'message' => $e->getMessage()]);
//        }
    }

    public function transferBeta(Request $request): JsonResponse
    {
        if(!$this->canAccess('export')){
            return response()->json(['success' => false], 401);
        }
        $user_id = CRUDBooster::myParentId();
        $fields = $request->get('selected_fields');

        $channel = $request->get('channel');
        $lang = app('\App\Services\UserService')->getLang($user_id);

        $distribution_app_purchase = $request->get('distribution_app_purchase');

        if((int)$request->selected_all){
            $productIds = app('\App\Services\DRMProductService')->getSelectedIds($user_id,$request->params);
        }else{
            $productIds = $request->get('product_ids');
        }

        $data['stock_percentage'] = (int) $request->get('stock_percentage');
        $user_default_calculation = ProfitCalculation::where(["user_id" => $user_id, 'auto_calculation' => 1])->first();
        $data['user_default_calculation'] = $user_default_calculation;

        $data['shop_id'] = $request->shop_id;
        $data['distribution_app_purchase'] = $distribution_app_purchase;

        foreach (array_chunk($productIds,1000) as $items) {
            TransferProductBeta::dispatch($items,$channel, $fields, $lang, true, $data);
        }
        return response()->json(['success' => true, 'message' => 'Product transferred successfully.']);
    }

    /**
     * @throws Exception
     */
    public function quickUpdate(Request $request): JsonResponse
    {
        if(!$this->canAccess('quick_edit')){
            return response()->json(['success' => false], 401);
        }

        $price = [];
        $lang  = app('\App\Services\UserService')->getLang(CRUDBooster::myParentId());

        $name   = $request->name;
        $id     = (int)$request->pk;
        // print_r($name);
        if(in_array($name, ['title', 'description', 'short_description'])){
            $value = [$lang => $request->value];
        }
        elseif(in_array($name, ['ek_price', 'vk_price', 'min_price', 'max_price', 'uvp','shipping_cost', 'rq_limit1'])){
            $value = deNumberFormatterAuto($request->value);
        }
        else{
            $value = $request->value;
        }
        // _log($id);
        // _log([$name=>$value]);
        $updated_product  = $this->productService->update($id, [$name=>$value],true);

        $min_price_error = $max_price_error = $vk_price_error = false;
        if ($name === 'min_price' && $value < $updated_product->ek_price) {
            $min_price_error = true;
        }

        if ($name === 'max_price' && $value < $updated_product->ek_price) {
            $max_price_error = true;
        }

        if ($name === 'vk_price' && (($value < $updated_product->min_price) || ($value > $updated_product->max_price))) {
            $vk_price_error = true;
        }

        if($name == "rq_limit1"){
            return response()->json(['success'=>true,
                'id' => $updated_product->id,
                'rq' => $updated_product->rq,
                'rq_limit' => $updated_product->rq_limit,
                'rq_progress' => $updated_product->RqProgress,
            ]);
        }

        $price['min_error'] = $min_price_error;
        $price['max_error'] = $max_price_error;
        $price['vk_error']  = $vk_price_error;


        \App\ProductMigration\API::syncChannelProduct($id);

        return response()->json([
            'success' => true,
            'price'   => $price,
        ]);
    }

    public function detail($id)
    {
        if(!$this->canAccess('product_detail')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $userId = CRUDBooster::myParentId();
        $data = array();

        if(!app(AppStoreService::class)->checkImportPayment($userId)){
            return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
        }

        $data['lang'] = app('App\Services\UserService')->getLang($userId);

        $isSuperAdmin = CRUDBooster::isSuperadmin();

        $data['page_title'] = __('Channel Products');
        $data['product'] = $this->productService->getById($id);

//        if(!$isSuperAdmin) {
            $data['channels'] = $this->productService->getUserShops($userId,$data['lang']);
            $currentChannel = $data['channels']->where('id',$data['product']->shop_id)->first();
            $data['activeChannel'] = $currentChannel->channel;
            $data['shop_id'] = $currentChannel->id;
//        }

        $data['withCategory'] = false;
        if(!in_array($data['activeChannel'],Channel::WITHOUT_CATEGORY)){
            $data['categories'] = $this->productService->getShopCategories($data['product']->shop_id,$data['product']->channel, $userId);
            $data['withCategory'] = true;
            $data['product_category'] = $this->productService->getProductCategoriesNew((int) $id, $data['product']->channel, $userId)->pluck('id')->toArray();
            $data['all_categories'] = $this->productService->getShopCategoriesNew($data['product']->shop_id, $data['product']->channel, $userId);
            $data['category_limit'] = Channel::CATEGORY_LIMITS[$data['product']->channel];
        }

        $data['averagePercentage'] = $this->averagePercentage($id);
        $product_url = url("admin/drm_products/detail/$id");

        if($data['activeChannel'] == 10)
        {
            $shop_url = \App\Shop::where('user_id', $userId)->where('channel', $data['activeChannel'])->value('url');
            $product_url = $shop_url ? rtrim($shop_url, '/').'/admin/view:content#action=editpage:'.$id : $shop_url;
        }

        $data['product_url'] = $product_url;

        $data['automagic_limit'] = Option::where(['option_group' => 'automagic_product_update', 'user_id' => $userId])->get()->keyBy('option_key');

        $data['dropmatix_brand'] = DropmatixProductBrand::where('id', $data['product']->brand)->first();

        $data['existing_brand'] = DropmatixProductBrand::where('user_id', $userId)->get();

        $stocks = json_decode($data['product']->stock_of_status, true) ?? ['1000' => $data['product']->stock ?? 0];

        $data['prices'] = [];
        $data['prices'] = json_decode($data['product']->price_of_status, true) ?? ['1000' => $data['product']->vk_price ?? 0];
        $states = ['1000', '1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000'];
        $data['stateNames'] = ['1500' => 'Neu Sonstige', '1750' => 'Neu Mit Fehlern', '2000' => 'Vom Hersteller', '2500' => 'Vom Verkäufer Generalüberholt', '2750' => 'Neuwertig', '3000' => 'Gebraucht', '4000' => 'Sehr Gut', '5000' => 'Gut', '6000' => 'Akzeptabel', '7000' => 'Als Ersatzteil'];
        $data['stocks'] = $stocks ?? ['1000' => $data['product']->stock];
        $data['additional_eans'] = [];
        $data['additional_eans'] = json_decode($data['product']->additional_eans, true) ?? [];
        $data['all_countries'] = DB::table('all_country')->get();
        $data['shipping_methods'] = DB::table('drm_shipping_methods')->get();
        $data['shipping_companies'] = DB::table('drm_shipping_companies')->where(['user_id' => $userId])->get();

        if($data['product']->options){
            $colors = json_decode($data['product']->options)->color;
            $data['product']->color = implode(',', $colors ?? []);
        }

        $fields = $data['product']->industry_template_data ?? [];

        if(!is_array($fields)){
            $fields = json_decode($fields,true);
        }

        $industryTemplateNameIs = "";
        $arrayHasTemplateName = false;

        if (count($fields) > 0){
            foreach ($fields as $key => $value) {
                if(in_array($key, array_keys(config('industry_template')))){
                    $industryTemplateNameIs = $key;
                    $data['all_fields'] = $value;
                    break;
                }
            }
        }
        $data['industryTemplateName'] = $industryTemplateNameIs;

        $data['previous_product_id'] = ChannelProduct::where('user_id', $userId)->where('id', '<', $id)->where('channel', $activeChannel)->orderBy('id', 'desc')->pluck('id')->take(1);
        $data['next_product_id'] = ChannelProduct::where('user_id', $userId)->where('id', '>', $id)->where('channel', $activeChannel)->orderBy('id', 'asc')->pluck('id')->take(1);
        $coreProduct = DrmProduct::where('id', $data['product']->drm_product_id)->first();
        $data['min_delivery_days'] = $coreProduct->delivery_days;

        $user_custom_ind_temp = [];
        $extra_options = '';

        $user_ind_temp = CustomIndustryTemplate::where('user_id', $userId)->pluck('name', 'id')->toArray();
        if($user_ind_temp){
            $user_custom_ind_temp = array_replace($user_custom_ind_temp, $user_ind_temp);

            foreach($user_ind_temp as $key => $value){
                $extra_options .= '<option value="'.$key.'">'.$value.'</option>';
            }
        }

        $admin_ind_temp = CustomIndustryTemplate::where('publish_to_customer', 1)->pluck('name', 'id')->toArray();
        if($admin_ind_temp){
            $user_custom_ind_temp = array_replace($user_custom_ind_temp, $admin_ind_temp);

            foreach($admin_ind_temp as $key => $value){
                $extra_options .= '<option value="'.$key.'">'.$value.'</option>';
            }
        }

        $data['custom_ind_tmp'] = $user_custom_ind_temp;
        $data['extra_custom_ind_tmp'] = $extra_options;

        if($data['product']->channel == \App\Enums\Channel::OTTO){
            $newRequest = new \Illuminate\Http\Request();
            $newRequest->replace(['product_id' => $id]);
            $otto_brands = $this->getBrandPicker($newRequest);
            $data['otto_brands'] = $otto_brands['brands'];
        }

            $data['product_url'] = "";

//        if($data['product']->channel == Channel::EBAY && $data['product']->is_connected && $id == 1170809){
//            $data['product_url'] = $this->getProductUrl($id);
//        }

        $data['is_dt_new_user'] = is_dt_user() && checkTariffEligibility($userId);

        $data['deluxe_or_higher'] = deluxeOrHigher($userId);

        $cheapest_applicable_channel = false;
        if ($data['deluxe_or_higher'] && in_array($data['product']->channel, \App\Enums\Channel::CHEAPEST_APPLICABLE_CHANNELS)) {
            $cheapest_applicable_channel = true;
        }
        $data['cheapest_applicable_channel'] = $cheapest_applicable_channel;

        $data['additional_fields'] = $this->productService->getAdditionalFields($data['product']->channel_categories[0]->category_id, $data['product']->channel,$userId);

        $mandatory_columns = [];

        if($data['additional_fields']['required_columns']){
            foreach($data['additional_fields']['required_columns'] as $c){
                $mandatory_columns[] = $c->name;
            }
        }

        $data['mandatory_columns'] = $mandatory_columns ?? [];

        $data['category_aspects'] = json_decode($data['product']->category_aspects);

        return view('channel_products.details_new', $data);

        // if($userId == 212){
        //     return view('channel_products.details_new', $data);
        // }
        // $data['existing_brand'] = DropmatixProductBrand::where('user_id', $userId)->pluck('brand_name', 'id')->toArray();
        // return view('channel_products.details', $data);
    }

    private function getProductUrl($product_id)
    {
        $metadata = json_decode(file_get_contents("http://138.197.184.210/api/v1/storekeeper?product_id=$product_id"),true);
        if(isset($metadata['store_keeper']['listing_id'])){
            return 'https://ebay.de/itm/'.$metadata['store_keeper']['listing_id'] ?? '';
        }
        return null;
    }

    public function delete($id, Request $request): RedirectResponse
    {
        if(!$this->canAccess('delete')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }
        $id = explode(",", $id);
        try {
            $this->productService->destroy($id, $request->shop, CRUDBooster::myParentId());
        } catch (Exception $e) {

        }

        try { // 2 single/multiple
            $shop = Shop::find($request->shop);

            create_agb_log(
                $shop->user_id,
                [
                    'shop_info' => ['shop_name' => $shop->shop_name, 'shop_type' => $shop->channel, 'shop_url' => $shop->url,],
                    'product_ids' => $id,
                ],
                [],
                count($id) . ' items deleted from shop ' . $shop->shop_name,
            );
        } catch (\Exception $th) {}

        return redirect()->back();
    }

    public function updateStatus(Request $request, int $id): RedirectResponse
    {
        if(!$this->canAccess('edit')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $value = $request->get('val');
        $product = $this->productService->getById($id);

        if ($product) {
            $update_status = $product->update_status;
            $update_status[$value] = 1;
            insertChangeLog($id, $value, 3);
            $lang = app('App\Services\UserService')->getLang($product->user_id);
            ChannelProductUpdateStatus::dispatch($id,$update_status,$product->drm_product_id, $product->channel, [$value], $lang, 'update');
        }

        return redirect()->back();
    }

    public function saveTag(Request $request): JsonResponse
    {
        try {
            $product_id = $request->get('id');
            $tags = explode(',', $request['tags']);
            $data['tags'] = generateTags($tags);
            $this->productService->update($product_id, $data);

            return response()->json(['success' => true, $data]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 422);
        }
    }

    public function saveImage(Request $request): JsonResponse
    {
        try {
            if ($request->hasFile('file')) {
                $imagesAll = $request->file('file');
                foreach ($imagesAll as $image) {
                    $image_type = strtolower($image->getClientOriginalExtension());
                    if ($image_type == "jpg" || $image_type == "png" || $image_type == "jpeg" || $image_type == "gif") {
                        $product = $this->productService->getById($request->product_id);
                        $image_url = uploadImage($image, 'products/' . CRUDBooster::myParentId() );
                        if($image_url && !in_array($image_url, $product->images)){
                            $data = $product->images;
                            $data[] = $image_url;
                            $this->productService->update($product->id, ['images' => array_filter($data)],true);
                        }
                    }
                }

                return response()->json(['success'=>true]);
            }
        } catch (\Exception $e) {
            return response()->json(['success'=>false],422);
        }
        return response()->json(['success'=>true]);
    }

    public function updateDetails(Request $request): JsonResponse
    {
        if(!$this->canAccess('edit')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        try{
            $data = $request->except(['import_id','_token','image_url','images','product-tags']);
            // $lang = Arr::get($data, 'lang', 'de');
            $lang = app('App\Services\UserService')->getLang(CRUDBooster::myParentId());

            if(!empty($data['title'])){
                $data['title'] = [$lang=>$data['title']];
            }
            if(!empty($data['desc'])){
                $data['description'] = [$lang=>$data['desc']];
            }

            $data['industry_template_data'] = $request->fields;

            if(isset($request->industry_template_name) && isset($request->channel) && $request->channel == Channel::DROPTIENDA){

                $industryTemplateUpdatedFields[$request->industry_template_name] = $request->fields;
                $data['industry_template_data'] = $industryTemplateUpdatedFields;

            }

            if ($request->additional_data) {
                $attr = $request->additional_data;
//                $attr['dt_variants']['color'] = implode(',',$request->additional_data['dt_variants']['color']);
//                $attr['dt_variants']['size'] = implode(',',$request->additional_data['dt_variants']['size']);
                $data['attributes'] = $attr;
            }
            $this->productService->update($request->product_id, $data , true);

            return response()->json(['success'=>true]);
        } catch(\Exception $ex){}
        return response()->json(['success'=>false],422);
    }

    public function deleteImage(Request $request): JsonResponse
    {
        try{
            $product = $this->productService->getById($request->product);
            $this->productService->update($request->product, ['images' =>  array_values(array_filter(array_diff(array_map('trim', $product->images), [$request->image]))) ],true);

            return response()->json(['success'=>true],200);
        } catch(Exception $ex){}
        return response()->json(['success'=>false],422);
    }

    public function updateImage(Request $request): JsonResponse
    {
        $id = $request->product_id;
        $images = $request->images;
        if(!is_array($images)){
            $images = preg_replace("/\r|\n/", "", $images);
            $images = array_unique(array_values(json_decode($images,true)));
        }
        $this->productService->update($id, ['images'=>array_filter($images)],true);
        return response()->json(['success'=>true]);
    }

    public function saveImageURL(Request $request): JsonResponse
    {
        if(!$this->canAccess('edit')){
            return response()->json(['success' => false], 401);
        }

        $id = $request->product_id;
        $url = $request->url;
        if(!empty($url)){
            $product = $this->productService->getById($id);
            $this->productService->update($id, ['images'=>array_filter(array_merge($product->images, [$url]))],true);

            return response()->json(['success'=>true]);
        }else{
            return response()->json(['success'=>false],422);
        }
    }

    public function assignCalculation(Request $request): JsonResponse
    {
        if(!$this->canAccess('calculation')){
            return response()->json(['success' => false], 401);
        }

        $user_id = CRUDBooster::myParentId();
        if (isset($request->is_repricing) && ($request->is_repricing == 1)) {
            $product_ids = $request->product_ids;
            if((int)$request->selected_all){
                $product_ids = $this->productService->getSelectedIds($user_id,$request->params);
            }

            $channel = $request->channel;

            if((int)$request->overwrite == 0){
                $skipped_ids = $this->productService->calculatedProducts($product_ids,$user_id,$channel);
                $product_ids = array_diff($product_ids,$skipped_ids);
            }

            $remain_credit = get_token_credit($user_id)['remain_credit'];
            if ($remain_credit <= 0) {
                return false;
            } else {
                $product_ids = array_slice($product_ids, 0, $remain_credit);
            }

            $res = $this->productService->prepareAssignCalc($product_ids,$request->calculation,$channel, 1);

            return response()->json(['success'=> $res ],$res ? 200 : 422);
        }

        $easyCalculation = $this->productService->checkEasyCalculation($request->calculation,$user_id);

        if($easyCalculation){
            $easyLimit = $this->productService->getEasyLimit($user_id,$request->calculation);
            if(!$easyLimit){
                return response()->json(['success'=> false ],402);
            }
        }

        $product_ids = $request->product_ids;
        if((int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($user_id,$request->params);
        }

        $channel = $request->channel;

        if((int)$request->overwrite == 0){
            $skipped_ids = $this->productService->calculatedProducts($product_ids,$user_id,$channel);
            $product_ids = array_diff($product_ids,$skipped_ids);
        }

        if($easyCalculation){
            $easyCalculatedProducts = $this->productService->idsByCalculation($request->calculation,$user_id);
            $newProducts = array_diff($product_ids,$easyCalculatedProducts);

            $easyLimit = $easyLimit - count($newProducts);

            if($easyLimit < 1){
                return response()->json(['success'=> false,'message' => 'You have selected more '.abs($easyLimit).' products than you plan limit. <br> <br> <strong style="color: rgb(253, 101, 0)">Please upgrade your plan !</strong>'],426);
            }
        }

        $res = $this->productService->prepareAssignCalc($product_ids,$request->calculation,$channel);

        if(checkTariffEligibility($user_id)){
            $easyCalculatedProducts = $this->productService->idsByCalculation($request->calculation,$user_id);
            $newProducts = array_diff($product_ids,$easyCalculatedProducts);

            $productCount = ChannelProduct::whereIntegerInRaw('id', $newProducts)->where('connection_status', ChannelProductConnectedStatus::CONNECTED)->count();
            $productCount = $productCount * 3;

            // app('App\Http\Controllers\tariffController')->CreditUpdate($user_id, $productCount, 'credit_deduct');

            (new \App\Services\Tariff\Credit\ChargeCredit)->charge($user_id, $productCount, \App\Services\Tariff\Credit\CreditType::PRODUCT_CALCULATION_ASSIGNED);
        }

        return response()->json(['success'=> $res ],$res ? 200 : 422);
    }

    public function checkValidity(Request $request): JsonResponse
    {
        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->product_ids;

        if( (int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($user_id, $request->params);
        }

        $response  = $this->productService->validateProducts($product_ids,$request->channel);

        return response()->json([
            'success'=> true,
            'data' => [
                'ids' => $response['valid_ids'],
                'invalid_ids' => $response['invalid_ids'],
                'total_count' => count($product_ids ?? []),
                'valid_count' => count($response['valid_ids']),
                'invalid_count' => count($response['invalid_ids']),
                // 'large_uvp' => count($valid_data['large_uvp'])
            ]
        ]);
    }

    /**
     * @throws Exception
     */
    public function export(Request $request)
    {
        if($request->channel == Channel::DROPTIENDA){
            return $this->exportBeta($request);
        }
        if(!$this->canAccess('export')){
            return response()->json(['success' => false], 401);
        }
        $currentUserId = CRUDBooster::myParentId();
        $product_ids = $request->product_ids;

        if((int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($currentUserId,$request->params);
        }

        $channel = $request->channel;
        $additional = $request->additional ?? array();

        if(in_array($channel,array_merge(Channel::CSV_CHANNELS,Channel::MIRAKL_CHANNELS))){
            foreach (array_chunk($product_ids,5000) as $ids) {
                $res = app('\App\Services\ChannelProductService')->exportToShop(
                    $ids,
                    $request->shop_id,
                    $currentUserId,
                    "create",
                    $additional
                );
            }
        }else{
            foreach (array_chunk($product_ids,300) as $ids) {
                Export::dispatch($ids,$request->shop_id,$currentUserId,"create",$additional);
            }
        }
        return $res ?? true;
    }

    /**
     * @throws \League\Csv\CannotInsertRecord
     * @throws \League\Csv\Exception
     */
    public function exportBeta(Request $request)
    {
        if(!$this->canAccess('export')){
            return response()->json(['success' => false], 401);
        }
        $currentUserId = CRUDBooster::myParentId();

        $product_ids = $request->product_ids;
        $response = NUll;
        if((int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($currentUserId,$request->params);
        }

        if($request->event == "CREATE"){
            $not_synced_categories = ChannelUserCategory::where([
                'channel' => Channel::DROPTIENDA,
                'user_id' => $currentUserId
            ])->whereNull('shop_category_id')->exists();

            if($not_synced_categories){
                app('App\Services\Modules\Export\Droptienda')->exportCategories($currentUserId,$request->shop_id);
            }

            foreach(array_chunk($product_ids,10000) as $ids){
//                if($request->channel == Channel::DROPTIENDA){
//                    ChannelProduct::whereIn('id',$ids)
//                        ->where('user_id',$currentUserId)
//                        ->update([
//                            'offer_uvp' => (bool)$request->force_uvp
//                        ]);
//                }

                $response = app('\App\Services\Modules\Export\Droptienda')->export(
                    $currentUserId,
                    $request->shop_id,
                    $ids
                );
            }
        } elseif ($request->event == "DELETE"){
            foreach (array_chunk($product_ids,10000) as $ids){
                $response = app('\App\Services\Modules\Export\Droptienda')->delete(
                    $currentUserId,
                    $request->shop_id,
                    $ids
                );
            }
        }


        return response()->json([
            'message' => $response
        ]);
    }

    /**
     * @throws Exception
     */
    public function deleteFromShop(Request $request): bool
    {
        $currentUserId = CRUDBooster::myParentId();
        $shop = Shop::find($request->shop);

        $product_ids = $request->product_ids;
        if((int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($currentUserId,$request->params);
        }

        if(in_array($shop->channel,Channel::MIRAKL_CHANNELS)){
            app('\App\Services\ChannelProductService')->deleteFromShop($product_ids,$shop->id,$currentUserId);
        }else{
            foreach (array_chunk($product_ids,100) as $items) {
                Disconnect::dispatch($items,$request->shop,$currentUserId);
            }
        }


        try { // 1 single/multiple/all delete from shop
            create_agb_log(
                $shop->user_id,
                [
                    'shop_info' => ['shop_name' => $shop->shop_name, 'shop_type' => $shop->channel, 'shop_url' => $shop->url,],
                    'product_ids' => $product_ids,
                ],
                [],
                count($product_ids) . ' items deleted from shop ' . $shop->shop_name,
            );
        } catch (\Exception $th) {}

        return true;
    }

    public function tempConnect(Request $request)
    {
        $product_id = $request->product_id;

        $temp_product                    = ChannelProduct::find($product_id);
        $temp_product->is_connected      = 1;
        $temp_product->connection_status = ChannelProductConnectedStatus::CONNECTED;
        $temp_product->save();

        return response()->json([
            'success' => true,
            'data'    => [],
        ], 200);
    }


    public function getChannelCSV(Request $request)
    {
        $token = $request->token;
        $feed = (new ChannelProductService())->generateFeed($token);
        if($feed == 'Locked') return response()->json('User is locked in DRM !',404);

        try {
            if($feed instanceof \SimpleXMLElement){
                return response($feed->asXML(), 200)->header('Content-Type', 'application/xml');
            }
            elseif ($feed instanceof Xlsx){
                header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                header('Content-Disposition: attachment; filename="'. urlencode("products.xlsx").'"');
                $feed->save('php://output');
            }
            else {
                $feed->output('products.csv');
            }
        } catch (Throwable $th) {
            return response()->json('Products not found',404);
        }
    }
    /**
     * @param Request $request
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\Routing\ResponseFactory|Response
     * @throws Exception
     */
    public function bulkChangeCategory(Request $request)
    {
        if(!$this->canAccess('categories')){
            return response('Access denied',401);
        }
        $user_id = CRUDBooster::myParentId();

        $product_ids = $request->product_ids;
        if((int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($user_id,$request->params);
        }

        $category_ids = $request->category_ids ?? array();
        if(!empty($request->custom_category) && !in_array($request->channel,Channel::MAPPING_CHANNELS))
        {
            $category = app(ChannelCategoryService::class)->updateOrCreate("",[
                'category_name' => $request->custom_category
            ],$user_id,$request->channel);

            if(isset($category['errors'])){
                $category = $category['data'];
            }
            $category_ids[] = $category->id;
        }
        $categories = array_unique($category_ids);

        foreach (array_chunk($product_ids,500) as $ids) {
            AssignCategory::dispatch($ids,$categories,$request->channel,$user_id);
        }


        return response('Success');
//        foreach ($product_ids as $productId) {
//            $this->productService->update($productId, ['category' => $categories],true);
//        }
//        die;
//        return redirect()->back();
    }

    public function getChannelCategories(int $channel)
    {
        if(!$this->canAccess('categories')){
            return response()->json(['success' => false], 401);
        }

        if (in_array($channel, Channel::MAPPING_CHANNELS)) {
            $categories = ChannelCategory::select('id','category_name as text')
                ->where(['channel' => $channel])
                // ->cacheFor(now()->addHours(24))
                // ->cacheDriver('file')
                ->get();
        } else {
            $categories = ChannelUserCategory::select('id','category_name as text')
                ->where(['channel' => $channel])
                // ->cacheFor(now()->addHours(24))
                // ->cacheDriver('file')
                ->get();
        }
        return json_encode($categories);
    }

    public function unlinkCategory(int $product_id, $category_id): RedirectResponse
    {
        if(!$this->canAccess('categories')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }
        try {
            $this->productService->unlinkCategory($product_id,$category_id);
        } catch (Throwable $th) {

        }
        return redirect()->back();
    }

    public function bulkUnlinkCategory($product_ids): RedirectResponse
    {
        if(!$this->canAccess('categories')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $product_ids = \explode(',',$product_ids);
        try {
            $this->productService->bulkClearCategory($product_ids);
        } catch (Throwable $th) {}
        return redirect()->back();
    }

    public function getProductCategories($product_ids)
    {
        $product_ids = \explode(',',$product_ids);
        $categories = $this->productService->getProductCategories($product_ids);
        return response($categories);
    }


    /**
     * @param Request $request
     * @return Response
     */
    public function createCalculation(Request $request): Response
    {
        if(!$this->canAccess('calculation')){
            return new Response(["You do not have permissions !"],401);
        }

        $user_id = CRUDBooster::myParentId();

        $validatedData = $this->validateCalculation($request);

        try {
            $calculation = $this->productService->createCalculation($validatedData,$user_id);
        } catch (Throwable $th) {
            return new Response([$th->getMessage()],400);
        }

        $data['new_cal'] = $calculation;

        $data['default_exist'] = $this->checkProfitCalHasDefaultCal($user_id);

        return new Response([
            'status' => "success",
            'data'	 => $data
        ]);
    }

    /**
     * @param Request $request
     * @return Response
     */
    public function updateCalculation(Request $request): Response
    {
        if(!$this->canAccess('calculation')){
            return new Response(["You do not have permissions !"],401);
        }

        $user_id = CRUDBooster::myParentId();
        $id = $request->calculation_id;

        $validatedData = $this->validateCalculation($request);

        try {
            $validatedData['dynamic_shipping_cost'] = $validatedData['dynamic_shipping_cost'] ?? 0;
            $validatedData['shipping_cost'] = $validatedData['dynamic_shipping_cost'] ? 0 : $validatedData['shipping_cost'];
            $calculation = $this->productService->updateCalculation($id, $validatedData, $user_id);
        } catch (Throwable $th) {
            return new Response([$th->getMessage()],400);
        }

        $data['update_cal'] = $calculation;

        $data['default_cal_exist'] = $this->checkProfitCalHasDefaultCal($user_id);

        return new Response([
            'status' => "success",
            'data'	 => $data
        ],200);
    }

    private function validateCalculation($request): array
    {
        if (!isset($request->is_repricing)) {
            if($request->uvp){
                $validatedData = [
                    'name' => "UVP ".$request->uvp_profit_range."%",
                    'uvp'  => true,
                    'uvp_profit_range' => $request->uvp_profit_range
                ];
            }elseif($request->price_on_request){
                $validatedData = [
                    'name' => "Price on Request",
                    'price_on_request'  => true
                ];
            }elseif($request->default_calculation){
                $validatedData = [
                    'shipping_cost' => $request->shipping_cost,
                    'auto_sync'     => (bool)$request->auto_sync,
                    'dynamic_shipping_cost' => $request->dynamic_shipping_cost
                ];
            }else {
                $validatedData = $request->validate([
                    'name' => ['required', 'string', 'max:255'],
                    'additional_charge' => ['required'],
                    'profit_percent' => ['required'],
                    'shipping_cost' => ['required_if:dynamic_shipping_cost,nullable'],
                    'round_scale'   => ['numeric','nullable'],
                    'dynamic_shipping_cost' => ['boolean','numeric','nullable'],
                ]);
                $validatedData['uvp'] = false;
                $validatedData['price_on_request'] = false;
            }
        } else {
            $validatedData = $request->validate([
                'name'                      => ['required', 'string', 'max:255'],
                'repricing_interval'        => ['required', 'integer', 'gt:0'],
                'repricing_price_diff'      => ['nullable', 'numeric'],
                'repricing_price_diff_type' => ['integer'],
            ]);

            $validatedData['is_repricing']              = (int) $request->is_repricing;
            $validatedData['repricing_interval']        = (int) $request->repricing_interval;
            $validatedData['repricing_price_diff']      = $request->repricing_price_diff;
            $validatedData['repricing_price_diff_type'] = (int) $request->repricing_price_diff_type;
        }

        return $validatedData;
    }

    /**
     * @param Request $request
     * @return Response
     */
    public function deleteCalculation(Request $request): Response
    {
        if(!$this->canAccess('calculation')){
            return new Response(["You do not have permissions !"],401);
        }

        $user_id = CRUDBooster::myParentId();
        $id = $request->calculation_id;
        try {
            $this->productService->deleteCalculation($id,$user_id);
        } catch (Throwable $th) {
            return new Response([$th->getMessage()],400);
        }
        return new Response([
            'success' => true,
        ],200);
    }

    /**
     * @param Request $request
     * @return Response
     */
    public function checkCalculationAssign(Request $request): Response
    {
        $user_id = CRUDBooster::myParentId();
        try {
            $count = $this->productService->checkCalculationAssign($request->calculation_id,$user_id);
        } catch (Throwable $th) {
            return new Response([$th->getMessage()],400);
        }
        return new Response([
            'success' => true,
            'data'    => [
                'count' => $count
            ]
        ],200);
    }

    public function checkCalculatedProducts(Request $request): Response
    {
        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->product_ids;
        if((int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($user_id,$request->params);
        }

        try {
            $count = $this->productService->calculatedProducts($product_ids,$user_id,$request->channel);
        } catch (Throwable $th) {
            return new Response([$th->getMessage()],400);
        }
        return new Response([
            'success' => true,
            'data'    => [
                'count' => count($count)
            ]
        ],200);
    }

    public function getCalcAssignedProducts(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $lang = app('App\Services\UserService')->getLang($user_id);
        $data['channels'] = collect(config('channel.list'));
        $data['lang'] = $lang;
        $data['products'] = $this->productService->productsByCalculation($request->calculation_id,$user_id);
        return view('channel_products.modals.calc_assigned_products',$data);
    }

    public function getProfitDetails(Request $request): string
    {
        $product = $this->productService->getById($request->id);
        $profit = $product->profit;
        $product_item_unit = $product->item_unit;
        $product_basic_price = $product->basic_price;
        $basic_price = '';
        $calculation_html = '';
        $price = 0;
        if(!empty($product_item_unit) && !empty($product_basic_price)){
            $per_unit = '';
            if($product_item_unit == 'Gram' || $product_item_unit == 'Kilogram'){
                $per_unit = 'Per Kilogram';
            }else if($product_item_unit == 'Milliliter' || $product_item_unit == 'Liter'){
                $per_unit = 'Per Liter';
            }else if($product_item_unit == 'Centimeter' || $product_item_unit == 'Meter'){
                $per_unit = 'Per Meter';
            }
            $basic_price = $product_basic_price . ' ' . $per_unit;
        }

        $basic_price_html = "";
        if(!empty($basic_price) && $product->channel == \App\Enums\Channel::DROPTIENDA){
            $basic_price_html = "Basic Price: " . $basic_price . "</br></br>";
        }

        $repricing_id = $product->repricing_id;
        if (!empty($repricing_id)) {
            $profit_margin = ProfitCalculation::find($repricing_id);

            $profit = \str_replace('%',"",$product->profit);

            $profit = (float)$profit;
            $calculation_html = "(".number_format($product->ek_price,2)." * ".$profit." / 100) = ".number_format(($product->ek_price*$profit/100),2)."<br><br>";
            $calculation_html.= number_format($product->ek_price,2)." + ".number_format(($product->ek_price*$profit/100),2)." = ".number_format($product->vk_price,2)." €";

            $repricing_html = '';

            if (in_array($product->channel, \App\Enums\Channel::CHEAPEST_APPLICABLE_CHANNELS)) {
                $analysis_product = AnalysisProduct::where([
                    'product_id' => $product->drm_product_id
                ])
                ->orWhere('product_id', $product->marketplace_product_id)
                ->select('ebay_price', 'amazon_price', 'google_price')
                ->first();

                if (!empty($analysis_product)) {
                    if ($product->channel == 4) {
                        $competitor_price = $analysis_product->ebay_price;
                    } else if ($product->channel == 5) {
                        $competitor_price = $analysis_product->amazon_price;
                    } else {
                        $competitor_price = $analysis_product->google_price;
                    }

                    $cheapest_price      = $competitor_price;
                    $cheapest_price_html = '';

                    $min_price = $product->min_price;
                    $max_price = $product->max_price;

                    if (empty($cheapest_price) || ($cheapest_price == 0.00)) {
                        $cheapest_price      = $product->max_price;
                        $cheapest_price_html = number_format($cheapest_price, 2) . ' € (Max price)';
                    } else {
                        if ($cheapest_price < $min_price) {
                            $cheapest_price      = $min_price;
                            $cheapest_price_html = number_format($cheapest_price, 2) . ' € (Min price: As competitor price less than min price.)';
                        } else if ($cheapest_price > $max_price) {
                            $cheapest_price      = $max_price;
                            $cheapest_price_html = number_format($cheapest_price, 2) . ' € (Max price: As competitor price greater than max price.)';
                        } else {
                            $repricing_price_diff = $profit_margin->repricing_price_diff;
                            if ($repricing_price_diff > 0) {
                                if ($profit_margin->repricing_price_diff_type == 0) { // amount
                                    // format: 10.00 - 1.00 = 9.00 €
                                    $cheapest_price_html = number_format($cheapest_price, 2) . ' - ' . number_format($repricing_price_diff, 2);
                                    $cheapest_price      = $cheapest_price - $repricing_price_diff;
                                    $cheapest_price_html .= ' = ' . number_format($cheapest_price, 2) . ' €';
                                } else { // percentage
                                    // format: 10.00 - (10.00 * 1.00 / 100) = 9.90 €
                                    $cheapest_price_html = number_format($cheapest_price, 2) . ' - (' . number_format($cheapest_price, 2) . ' * ' . number_format($repricing_price_diff, 2) . ' / 100)';
                                    $cheapest_price      = $cheapest_price - ($cheapest_price * ($repricing_price_diff / 100));
                                    $cheapest_price_html .= ' = ' . number_format($cheapest_price, 2) . ' €';
                                }
                            }
                        }
                    }
                    if ($cheapest_price < $min_price) {
                        $cheapest_price = $min_price;
                        $cheapest_price_html = number_format($cheapest_price, 2) . ' € (Min price: As price less than min price.)';
                    }

                    $repricing_html = "
                        Competitor price : " . number_format($competitor_price, 2) . " €" . "<br><br>
                        Price difference : " . $profit_margin->repricing_price_diff . ($profit_margin->repricing_price_diff_type ? ' %' : ' €') . "<br><br>
                        Repricer price : " . $cheapest_price_html . "<br><br><br>";
                }
            }

            $html = "
                Price source : <em>".$profit_margin->name."</em></br></br>
                ".$repricing_html."
                Profit : " . $product->profit. "<br></br>
                Calculation: " . $calculation_html . "<br></br>
                $basic_price_html";
        } else {
            $profit_margin = ProfitCalculation::find($product->calculation_id);

        if(($profit_margin->dynamic_shipping_cost && $product->shipping_cost > 0)){
            $dynamic_shipping = "(Dynamic)";
            $shipping_cost = $product->shipping_cost;
        }
        else{
            $dynamic_shipping = "";
            $shipping_cost = $profit_margin->shipping_cost;
        }

        if(!empty($profit_margin) && !$profit_margin->uvp){
            if($profit_margin->default){
                $analysis_product = AnalysisProduct::where([
                    'product_id' => $product->drm_product_id
                ])->select('ebay_price', 'amazon_price', 'google_price')->first();

                $analysis_sources = "";
                $prices = array();
                if($analysis_product){
                    $analysis_prices = $analysis_product->toArray();
                    foreach ($analysis_prices as $key => $analysis_price) {
                        $source = ucfirst(str_replace('_',' ',$key));
                        $analysis_sources.= "$source: ".number_format($analysis_price,2)." €<br>";
                    }
                    $prices = array_values(array_filter($analysis_prices));
                }

                if(!empty($prices)){
                    $price = (array_sum($prices)/count($prices)) + $shipping_cost;
                }
                $calculation_html.= $analysis_sources. "<br>
                    Average price: ".number_format($price,2)." €<br><br> Shipping cost: ".number_format($shipping_cost,2)." <br><br>";

                if ($price < $product->ek_price) {
                    $calculation_html.= "<em>[Average price is less than the purchase price!";
                    if ($product->uvp > 0) {
                        $price = $product->uvp + $shipping_cost;
                        $calculation_html.= "<br> UVP assigned] </em><br><br>
                         UVP: ".number_format($price,2)." €<br><br>";
                    } else {
                        $calculation_html.= "<br> 75% profit assigned]
                        </em><br><br>";
                        GOTO CALCULATION_SECTION;
                    }
                }

                GOTO GENERATE_HTML;
            }

            CALCULATION_SECTION:
            $calculation = $product->ek_price + $product->ek_price * (floatval($profit_margin->profit_percent) / 100);
            $additional_calculation = $shipping_cost + floatval($profit_margin->additional_charge) + $calculation;
            $round = ($profit_margin->round_scale) ? " + " . $profit_margin->round_scale . " = " . $product->vk_price : "";

            $calculation_html.= "Calculation: " .number_format($product->ek_price,2) . " + " . number_format($product->ek_price * (floatval($profit_margin->profit_percent) / 100),2) . " (" . number_format($profit_margin->profit_percent,2) . "%) = " .
                number_format($calculation,2) . " + (" . number_format($shipping_cost,2) . " + " . number_format($profit_margin->additional_charge,2) . ") = " . number_format($additional_calculation,2) . $round . "  €";
            $round_html = $profit_margin->round_scale ?: 'N/A';

            GENERATE_HTML:
            $html = "
            Calculation name : ".$profit_margin->name."
            <a href='javascript:;' onclick='
                openUpdateCalc(".json_encode($profit_margin).")
            ' style='color:#fd6500'>
            <i class='fa fa-edit'></i> Update</a></br></br>";

            if($profit_margin->default) GOTO PROFIT_SECTION;

            $html.="
            Profit percent : " . number_format($profit_margin->profit_percent,2) . "% </br></br>
            Shipping cost : " . number_format($shipping_cost,2) . " € $dynamic_shipping </br></br>
            Additional charge : " . number_format($profit_margin->additional_charge,2) . " €</br></br>
            Round scale : " . $round_html . "</br></br>";

            PROFIT_SECTION:
            $html.="
            Profit : " . $profit . "</br></br>".$calculation_html . "</br></br>
            $basic_price_html";
        }
        else{
            $profit = \str_replace('%',"",$product->profit);
            $profit = (float)$profit;
            $calculation_html = "(".number_format($product->ek_price,2)." * ".$profit." / 100) = ".number_format(($product->ek_price*$profit/100),2)."<br><br>";
            $calculation_html.= number_format($product->ek_price,2)." + ".number_format(($product->ek_price*$profit/100),2)." = ".number_format($product->vk_price,2)." €";

            if($profit_margin && $profit_margin->uvp){
                $price_source = $profit_margin->name != '' ? $profit_margin->name:"UVP";
            }else{
                $price_source = $product->price_droptienda ? "Droptienda" : "Manual";
            }

            $html = "
                Price source : <em>".$price_source."</em></br></br>
                Profit : " . $product->profit. "</br></br>
                Calculation: " . $calculation_html . "</br></br>
                $basic_price_html";
        }
        }

        return $html;
    }



    public function getFilterOptions(Request $request): string
    {
        $user_id = CRUDBooster::myParentId();

        $lang = app('App\Services\UserService')->getLang($user_id);
        return $this->productService->getFilterOptions(array_merge($request->all(), [
            'user_id' => $user_id,
            'lang' => $lang ?? "de",
        ]));
    }

    public function searchAndReplace(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $lang = app('App\Services\UserService')->getLang($user_id);

        try {
            $res = $this->productService->searchAndReplace(array_merge($request->all(), [
                'user_id' => $user_id,
                'lang' => $lang ?? "de",
            ]));
        } catch (Throwable $th) {
            //throw $th;
        }

        return response($res ?? "");
    }

    public function averagePercentage($id)
    {
        $userId = CRUDBooster::myParentId();
        if(!$userId){
            return redirect(CRUDBooster::adminPath('login'));
        }

        $languageId = app('App\Services\UserService')->getProductCountry($userId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);
        $channel_id = \App\Models\ChannelProduct::where('id', $id)->first();
        // dd($channel_id['channel']);
        $products = \App\Models\ChannelProduct::where(['user_id' => $userId, 'country_id' => $languageId, 'channel' => $channel_id['channel'] ])->get();
        // dd($products);
        $field_fill_up_count = [];

        if($products){
            foreach ($products as $product) {

                $count = 0;
                $total_fill = count(\App\Enums\Product::ALL_FIELDS);
                foreach( \App\Enums\Product::ALL_FIELDS as $fields ){
                    // dd($product->getTitleFieldAttribute(), $product->getTransDescriptionAttribute(), $product->$fields);
                    // if(!empty($product[$fields])){
                    //     $count++;
                    // }

                    if($fields == 'title'){
                        $title_field_len = strlen($product->getTitleFieldAttribute());
                        if($title_field_len > 0){
                            $count++;
                        }
                    }else if($fields == 'description'){

                        $description_field_len = strlen($product->getTransDescriptionAttribute());
                        if($description_field_len > 0){
                            $count++;
                        }
                    }else{
                        if (!empty($product->$fields)) {
                            $count++;
                        }
                    }
                }

//                dd(\App\Enums\Channel::CHRONO24);

                if ($product->channel == \App\Enums\Channel::CHRONO24 ) {
                    $total_fill = count(\App\Enums\Product::ALL_FIELDS) + count(\App\Enums\Product::EXTRA_FIELD);
                    $extra_field = \App\Enums\Product::EXTRA_FIELD;
                    if ($product->attributes) {
                        $attr = $product->attributes['additonal_data'];
                        if ($attr) {
                            foreach ($attr as $index => $value) {
                                if(!empty($value)){
                                    if (in_array($index, $extra_field)) {
                                        $count++;
                                    }
                                }
                            }
                        }
                    }
                }

                $percentage = $total_fill > 0 ? ($count * 75) / $total_fill : 0;

                $total_seo_item = 2;
                $total_seo_percentage = 20;
                $title_seo_percentage = 0;
                $short_desc_seo_percentage = 0;
                $title_len = strlen( preg_replace("/\s+/", "", strip_tags($product->getTitleFieldAttribute())) );
                $short_desc_len = strlen( preg_replace("/\s+/", "", strip_tags($product->getShortDescAttribute())) );

                if($title_len > 0){
                    if($title_len >= 56 && $title_len <= 80){
                        $title_seo_percentage = ($total_seo_percentage/$total_seo_item);
                    }else{
                        $title_seo_percentage = ($total_seo_percentage/$total_seo_item) / 2;
                    }
                }

                if($short_desc_len > 0){
                    if($short_desc_len >= 112 && $short_desc_len <= 160){
                        $short_desc_seo_percentage = ($total_seo_percentage/$total_seo_item);
                    }else{
                        $short_desc_seo_percentage = ($total_seo_percentage/$total_seo_item) / 2;
                    }
                }

                $ideal_title_length_array = $product->getIdealTitleAttribute();
                $ideal_title_percentage = 0;

                if(count($ideal_title_length_array) > 0){

                    if(count($ideal_title_length_array) >= 2){
                        $ideal_title_percentage = 5;
                    }else{
                        $ideal_title_percentage = 2;
                    }

                }

                $percentage = $percentage + $title_seo_percentage + $short_desc_seo_percentage + $ideal_title_percentage;

                $percentage = number_format((float)$percentage, 2, '.', '');
                $field_fill_up_count[] = $percentage;
                // dd($percentage);
            }

        }

        // dd($field_fill_up_count);

        $averagePercentage = ( count($field_fill_up_count) > 0 ) ? array_sum($field_fill_up_count) / count($field_fill_up_count) : 0;
        // dd($averagePercentage);
        return number_format($averagePercentage, 2, '.', '');
    }

    public function productStatus(Request $request): JsonResponse
    {
        try {
            $productIds = $request->input('product_ids', []);
            $selectedFields = $request->input('selected_fields', []);
            $activeChannelId = $request->input('channel_id', '');

            $fields = [
                'title', 'item_number', 'ean', 'description', 'images', 'ek_price',
                'vk_price', 'uvp', 'stock', 'item_weight', 'item_size', 'item_color',
                'note', 'production_year', 'brand', 'materials', 'tags', 'gender',
                'industry_template_data', 'delivery_company_id', 'drm_import_id',
                'country_id', 'variants', 'delivery_days', 'category', 'status'
            ];

            $statusArray = array_fill_keys($fields, 0);

            if (!empty($selectedFields)) {
                foreach ($selectedFields as $field) {
                    if (in_array($field, $fields)) {
                        $statusArray[$field] = 1;
                    }
                }
            }

            if (!empty($statusArray)) {
                $lang = app('App\Services\UserService')->getLang(CRUDBooster::myParentId());

                $productDrmIds = \App\Models\ChannelProduct::whereIn('id', $productIds)->pluck('drm_product_id');

                foreach ($productIds as $id) {
                    $product_drm_id = $productDrmIds[$id];

                    ChannelProductUpdateStatus::dispatch($id, $statusArray, $product_drm_id, $activeChannelId, array_keys(array_filter($statusArray)), $lang, 'update');
                }

                return response()->json(['success' => true, 'message' => 'Channel Product Status Update in Process.']);
            }

            return response()->json(['success' => false, 'message' => 'Please Select Product!!!']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }


    public function createVariant(Request $request): JsonResponse
    {
        if(!$this->canAccess('variants')){
            return response()->json(['success' => false], 401);
        }
        $user_id = CRUDBooster::myParentId();
        $this->productService->createVariant($request->product_ids,$user_id);
        return response()->json(['success' => true, 'message' => 'Variant created successfully !']);
    }

    public function showVariants(Request $request)
    {
        $data['lang'] = app('App\Services\UserService')->getLang(CRUDBooster::myParentId());
        $data['parent'] = $this->productService->getById($request->product_id);
        $data['variants'] = $data['parent']->variant_products ?? array();
        return view('channel_products.modals.show_variants',$data);
    }

    public function deleteVariant(Request $request): JsonResponse
    {
        if(!$this->canAccess('variants')){
            return response()->json(['success' => false], 401);
        }

        $user_id = CRUDBooster::myParentId();
        $this->productService->deleteVariant($request->product_id);
        return response()->json(['success' => true, 'message' => 'Variant deleted successfully !']);
    }

    public function removeFromVariant(Request $request): JsonResponse
    {
        if(!$this->canAccess('variants')){
            return response()->json(['success' => false], 401);
        }
        $this->productService->removeFromVariant($request->product_id,$request->variant_id);
        return response()->json(['success' => true, 'message' => 'Variant removed successfully !']);
    }
    public function resetVariants(Request $request): JsonResponse
    {
        if(!$this->canAccess('variants')){
            return response()->json(['success' => false], 401);
        }
        $user_id = CRUDBooster::myParentId();
        $this->productService->resetVariants($request->product_ids,$user_id);
        return response()->json(['success' => true, 'message' => 'Variants removed successfully !']);
    }

    /**
     * @throws Exception
     */
    public function refreshConnection(Request $request): JsonResponse
    {
        if(!$this->canAccess('export')){
            return response()->json(['success' => false], 401);
        }

        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->product_ids;

        if((int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($user_id,$request->params);
        }

        $this->productService->refreshConnection($product_ids,$request->shop_id,$request->channel,$user_id);
        return response()->json(['success' => true, 'message' => 'Products will be refreshed soon']);
    }

    public function getBrandPicker(Request $request)
    {
        $data['product'] = $this->productService->getById($request->product_id);
        $data['brands'] = $this->productService->getChannelBrands($data['product']->channel);
        return view('channel_products.modals.pick_brands',$data);
    }

    public function getCalculations(Request $request)
    {
        $user_id = CRUDBooster::myParentId();

        $user_all_calculation = $this->productService->getUserCalculations($user_id);
        $user_has_auto_cal = $user_all_calculation->contains('auto_calculation', 1);

        $user_purchase_calculation_app = DrmUserHasPurchasedApp($user_id, config('global.auto_calculaiton_app_id'));

        if(!$user_purchase_calculation_app && $user_has_auto_cal){
            ProfitCalculation::where(["user_id" => $user_id, 'auto_calculation' => 1])->update(['auto_calculation' => 0]);
        }

        $data['user_has_auto_cal'] = $user_has_auto_cal;

        $data['calculations'] = $user_all_calculation;

        $data['user_purchase_calculation_app'] = $user_purchase_calculation_app;

        $data['channel'] = $_GET['channel'];

        $deluxe_or_higher         = deluxeOrHigher($user_id);
        $data['deluxe_or_higher'] = $deluxe_or_higher;

        $cheapest_applicable_channel = false;
        if (in_array($_GET['channel'], \App\Enums\Channel::CHEAPEST_APPLICABLE_CHANNELS)) {
            $cheapest_applicable_channel = true;
        }
        $data['cheapest_applicable_channel'] = $cheapest_applicable_channel;

        if (!$deluxe_or_higher || !$cheapest_applicable_channel) {
            $data['calculations'] = $user_all_calculation->where('is_repricing', 0); // ignore repricing rows
        }

        $data['credit_stat'] = get_token_credit($user_id);

        // all key are in minutes
        $repricing_intervals = [
            1    => __('Every') . ' ' . 1 . ' ' . __('Minute'),
            5    => __('Every') . ' ' . 5 . ' ' . __('Minutes'),
            15   => __('Every') . ' ' . 15 . ' ' . __('Minutes'),
            60   => __('Every') . ' ' . __('Hour'),
            180  => __('Every') . ' ' . 3 . ' ' . __('Hours'),
            360  => __('Every') . ' ' . 6 . ' ' . __('Hours'),
            720  => __('Every') . ' ' . 12 . ' ' . __('Hours'),
            1440 => __('Every') . ' ' . 24 . ' ' . __('Hours'),
        ];
        $data['repricing_intervals'] = $repricing_intervals;

        return view('channel_products.modals.assign_calculation',$data);
    }

    protected function canAccess($feature): bool
    {
        return can_access($feature,$this->module_id);
    }

    public function iframeGeneration($id)
    {
        try {
            $authId = CRUDBooster::myParentId();
            $channelProduct = ChannelProduct::select('id', 'btn_bg_color', 'btn_text', 'user_id', 'calculation_percentage', 'calculation_tax', 'is_connected', 'calculation_id')->where(['user_id' => $authId, 'id' => $id])->first();
            if (empty($channelProduct->dt_url)) {
                $shop = \App\Shop::where('user_id', $authId)->where('channel', Channel::DROPTIENDA)->first();
                $url = rtrim($shop->url, '/');
                $apiUrl = "$url/api/v1/product_url";
                $payLoad = ['userToken' => $shop->username, 'userPassToken' => $shop->password, 'drm_id' => $channelProduct->id];

                $ch = curl_init();
                curl_setopt_array($ch, array(
                    CURLOPT_URL => $apiUrl,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => $payLoad,
                    CURLOPT_HTTPHEADER => array('Cookie: SameSite=None; SameSite=None;laravel_session=Cj4Z2VB64oLCYXI5o1nHAagFX1Wny1TeEUGj4OA8'),
                ));
                $server = curl_exec($ch);
                curl_close($ch);
                $json = @json_decode($server);
                if (!empty($json) && $json->success) {
                    $channelProduct->dt_url = $url . '/' . $json->data->url;
                    $channelProduct->save();
                }
            }

            $iframe = '<iframe src="' . route('iframe.load', ['id' => $id, 'user' => $authId]) . '" width="500" height="400" style="border: none!important;"></iframe>';

            try {
                $shop = isChannelExists(\App\Enums\Channel::DROPTIENDA, $authId);
                $link = $url = null;
                if (!empty($shop)) {
                    $link = rtrim($shop->url,'/');
                }
                if ($link) {
                    $channelProduct = DB::table('channel_products')->where(['user_id' => $authId, 'id' => $id])->first();
                    $url = !empty($channelProduct) ? "$link/api/v1/product/$channelProduct->id" : 'javascript:void(0)';
                }
            } catch (Exception $exception) {

            }

            $has_calculation = !empty($channelProduct->calculation_id);

            $html = view('channel_products.partials._iframe_modal', compact('channelProduct', 'iframe', 'url', 'has_calculation'))->render();
            return response()->json(['success' => true, 'data' => $html]);
        } catch (Exception $exception) {
            return response()->json(['success' => false, 'data' => null]);
        }
    }

    public function saveIframeBtnInfo(Request $request)
    {
        try {
            $authId = CRUDBooster::myParentId();
            $product = ChannelProduct::where(['user_id' => $authId, 'id' => $request->id, 'channel' => Channel::DROPTIENDA])->first();
            $product->btn_bg_color = $request->bg_color;
            $product->btn_text = $request->text;
            $product->save();

            $shop = \App\Shop::where('user_id', $authId)->where('channel', Channel::DROPTIENDA)->first();
            $url = rtrim($shop->url, '/');

            if ($product->is_connected != 1) { // if product is not connected
//                // transfer to droptienda
//                $fields = ['title', 'item_number', 'ean', 'description', 'image', 'ek_price', 'uvp', 'stock', 'item_weight', 'item_size', 'item_color', 'note', 'production_year', 'brand', 'materials', 'tags', 'gender', 'status', 'industry_template_data',
//                    // 'category'
//                ];
//                app('App\Services\ChannelProductService')->transferProduct($product->id, Channel::DROPTIENDA, $fields, $lang, 'create');
                $payLoad = $this->getProductData($product, $request->percentage, $request->tax);
                if (!empty($shop)) {
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, "$url/api/v1/products");
                    curl_setopt($ch, CURLOPT_POST, 1);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payLoad));
                    // Receive server response ...
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
                    $server = curl_exec($ch);
                    curl_close($ch);
                    $json = @json_decode($server);
                    if (!empty($json)) {
                        $product->is_connected = 1;
                        $product->dt_url = $url . '/' . $json->url;
                    }
                }
            }

            $tax = $request->tax ?? 0;
            $percentage = $request->percentage ?? 0;
            $price = $product->ek_price + $product->ek_price * ($percentage / 100);
            $price = $price + $price * ($tax / 100); // calculated vk price

            $product->vk_price = $price;
            $product->calculation_percentage = $request->percentage;
            $product->calculation_tax = $request->tax;
            $product->save();

            return response()->json(['success' => true, 'message' => 'Data saved']);
        } catch (Exception $exception) {
            return response()->json(['success' => true, 'message' => $exception->getMessage()]);
        }
    }

    public function getErrors(Request $request)
    {
        $product = $this->productService->getById($request->product_id);
        $errors = $product->error_response ?? array();
        return response()->json(['success' => true, 'errors' => $errors]);
    }

    public function getMissingFields(Request $request)
    {
        $product = $this->productService->getById($request->product_id);

//        if(in_array($product->channel, [13])){
//            $cat_id = $product->channel_categories->isEmpty() ? null : $product->channel_categories[0]->category_id;
//            $available = array();
//            $missing = array();
//            $missing_fields = $product->missing_fields ?? array();
//            if($cat_id == null){
//                $missing = [__('Category')];
//                return response()->json(['success' => true, 'missing' => $missing,'available' => $available]);
//            }
//
//            $all_fields = DB::table('channel_categories')->where('category_id', $cat_id)->where('channel', $product->channel)->first();
//            $all_fields = json_decode($all_fields->misc)->required_columns;
//            foreach($all_fields as $field){
//                if(!in_array($field->name, $missing_fields)){
//                    $available[] = $field->title;
//                }else{
//                    $missing[] = $field->title;
//                }
//            }
//            return response()->json(['success' => true, 'missing' => $missing,'available' => $available]);
//        }
        $missing_attributes = $product->missing_attributes ?? array();

        $required_fields = array_merge(config('channel.required_fields.default', []), config('channel.required_fields.' . $product->channel, []));
        if($product->channel == Channel::DROPTIENDA){
            $required_fields = array_values(array_diff((array)$required_fields, ['vk_price']));
        }
        if(in_array($product->channel, [Channel::KAUFLAND,Channel::EBAY,Channel::OTTO])){
            $category = $product->channel_categories;
            $categoryRequiredFields = [];
            if(!$category->isEmpty()){
                $additional_fields = $this->productService->getAdditionalFields($product->channel_categories[0]->category_id,$product->channel,$product->user_id);

                foreach ($additional_fields['required_columns'] as $field) {
                    if ($product->channel == Channel::EBAY) {
                        $field_name = $field['localizedAspectName'];
                    } else {
                        $field_name = $field['name'];
                    }
                    $categoryRequiredFields[] = $field_name;
                }
                $required_fields = array_merge((array)$required_fields,$categoryRequiredFields);
            }
        }

        $available = array();
        $missing = array();
        foreach ($required_fields as $required_field) {
            if(!in_array($required_field,$missing_attributes)){
                $available[] = __( ucwords(strtolower(str_replace('_', ' ', $required_field))) );
            }else{
                $missing[] = $required_field;
            }
        }

        $missing = array_unique(array_merge($missing,$missing_attributes));

        $missing = array_map(function ($item){
            return __( ucwords(strtolower(str_replace('_', ' ', $item))));
        },$missing);

        return response()->json(['success' => true, 'missing' => $missing,'available' => $available]);
    }


    public function magicAutocomplete(Request $request){
        $count_updated_product = 0;
        $automagic_product_limit = @get_option('automagic_product_limit', 'automagic_product_update', CRUDBooster::myId());
        $automagic_product_uses = @get_option('automagic_product_uses', 'automagic_product_update', CRUDBooster::myId());
        if(!$automagic_product_limit){
            $automagic_product_limit = 15; #if no limit found : default is 15
            set_option('automagic_product_limit', 'automagic_product_update', $automagic_product_limit);
        }else{
            $automagic_product_limit = $automagic_product_limit->option_value;
        }
        if(!$automagic_product_uses){
            $automagic_product_uses = 0;
        }else{
            $automagic_product_uses = $automagic_product_uses->option_value;
        }

        $userId = CRUDBooster::myParentId();
        $products = $this->productService->getUserProducts($request->product_ids, $userId);

        $product_eans = array_column($products->toArray(), 'ean');

        #if unlimited pack selected
        if($automagic_product_limit != 'unlimited'){
            if($automagic_product_uses + count($product_eans) > $automagic_product_limit){
                #show error when limit excedeed
                return [
                    'status' => 'error',
                    'error' => 'limit_exceeded',
                    'redirect' => url('admin/app_form'),
                    'app_id'   => Apps::MAGIC_PRODUCT_UPDATE,
                    'message' => 'Automagic data completion limit excedeed.'
                ];
            }
        }

        $product_param = [
            'history' => 0,
            'buybox' => 0,
            'rental' => 0,
            'stats' => 1
        ];
        $keepa = new Keepa(env('KEEPA_API_KEY'));
        $keepa_products = $keepa->code($product_eans, $product_param);

        $update_product = [];
        foreach ($products as $key => $product) {
            $updated = false;
            $keepa_product = $keepa_products->product($product->ean);
            $keepa_price = $keepa_products->latestPrice($product->ean);
            $keepa_cat_tree = $keepa_products->categoryTree($product->ean) ?? [];

            #create category from keepa category tree
            if(
                $product->channel_categories->isEmpty() &&
                !in_array($product->channel, Channel::MAPPING_CHANNELS)
            ){
                $full_path = [];
                $parent = 0;
                foreach ($keepa_cat_tree as $key => $keepa_cat) {

                    $full_path[] = $keepa_cat['name'];
                    $channelUserCat = [
                        'category_name' => $keepa_cat['name'],
                        'user_id'       => CRUDBooster::myId(),
                        'channel'       => $product->channel,
                        'level'         => $key + 1,
                        'parent'        => $parent,
                        'full_path'     => implode(' > ', $full_path)
                    ];

                    // #get category id or create
                    $parent = ChannelUserCategory::firstOrCreate(
                        [
                            'category_name' => $keepa_cat['name'],
                            'channel' => $product->channel,
                            'user_id' => CRUDBooster::myId(),
                        ],
                        $channelUserCat
                    )->id;

                    $category_type = in_array($product->channel, Channel::MAPPING_CHANNELS) ? CategoryType::API : CategoryType::USER;
                }
                ChannelProductCategory::firstOrCreate(
                    [
                        'category_id' => $parent,
                        'channel_product_id' => $product->id,
                    ],
                    [
                        'category_id' => $parent,
                        'channel_product_id' => $product->id,
                        'category_type' => $category_type,
                    ]);
                $updated = true;
            }

            $magic_fields = [
                'brand' => $product->brand,
                'item_color' => $product->item_color,
                'item_weight' => $product->item_weight,
                'item_size' => $product->item_size,
                'ek_price' => $product->ek_price,
            ];
            $empty_fields = hasEmptyValue($magic_fields);

            if(!empty($empty_fields)){
                # only send request if atleast one required field is empty
                if(\in_array('brand', $empty_fields)) $update_product['brand'] = $keepa_product->brand;
                if(\in_array('item_color', $empty_fields)) $update_product['item_color'] = $keepa_product->color;
                if(\in_array('item_weight', $empty_fields)) $update_product['item_weight'] = $keepa_product->itemWeight;
                if(\in_array('item_size', $empty_fields)) $update_product['item_size'] = $keepa_product->size;
                if(\in_array('ek_price', $empty_fields)) $update_product['ek_price'] = $keepa_price;

                $this->productService->update($product->id, $update_product, True);

                $updated = true;
            }

            if( !empty($product->industry_template_data) ){
                if(isset($keepa_product->model)){
                    $languageId = app('App\Services\UserService')->getProductCountry(CRUDBooster::myParentId());
                    $lang = app('App\Services\UserService')->getProductLanguage($languageId);

                    $industry_template_data = json_decode($product->industry_template_data);
                    if(empty($industry_template_data->juvely->model->$lang)){
                        $industry_template_data->juvely->model->$lang = $keepa_product->model;

                        $data['industry_template_data'] = json_encode($industry_template_data);
                        $this->productService->update($product->id, $data, True);
                        $updated = true;
                    }
                }
            }
            if($updated) $count_updated_product++;
        }

        $response = [
            'status' => 'warning',
            'message' => 'Nothing to update!'
        ];

        if($count_updated_product){
            $response = [
                'status' => 'success',
                'message' => "$count_updated_product Product informations updated magically!"
            ];
        }
        set_option('automagic_product_uses', 'automagic_product_update', $automagic_product_uses + $count_updated_product);
        return $response;
    }

    // New Magic Method using Credits
    public function magicAutocompleteNew(Request $request){
        $count_updated_product = 0;
        $automagic_product_limit = @get_token_credit(CRUDBooster::myParentId());

        $automagic_product_limit = $automagic_product_limit['remain_credit'] ?? 0;

        $userId = CRUDBooster::myParentId();
        $products = $this->productService->getUserProducts($request->product_ids, $userId);

        $product_eans = array_column($products->toArray(), 'ean');
        $credit_req = count($product_eans) * 5 ;

        if($credit_req > $automagic_product_limit){
            return [
                'status' => 'error',
                'error' => 'limit_exceeded',
                //'redirect' => url('admin/app_form'),
                'message' => 'Automagic data completion limit excedeed.'
            ];
        }


        $product_param = [
            'history' => 0,
            'buybox' => 0,
            'rental' => 0,
            'stats' => 1
        ];
        $keepa = new Keepa(env('KEEPA_API_KEY'));
        $keepa_products = $keepa->code($product_eans, $product_param);

        $update_product = [];
        foreach ($products as $key => $product) {
            $updated = false;
            $keepa_product = $keepa_products->product($product->ean);
            $keepa_price = $keepa_products->latestPrice($product->ean);
            $keepa_cat_tree = $keepa_products->categoryTree($product->ean) ?? [];

            #create category from keepa category tree
            if(
                $product->channel_categories->isEmpty() &&
                !in_array($product->channel, Channel::MAPPING_CHANNELS)
            ){
                $full_path = [];
                $parent = 0;
                foreach ($keepa_cat_tree as $key => $keepa_cat) {

                    $full_path[] = $keepa_cat['name'];
                    $channelUserCat = [
                        'category_name' => $keepa_cat['name'],
                        'user_id'       => CRUDBooster::myId(),
                        'channel'       => $product->channel,
                        'level'         => $key + 1,
                        'parent'        => $parent,
                        'full_path'     => implode(' > ', $full_path)
                    ];

                    // #get category id or create
                    $parent = ChannelUserCategory::firstOrCreate(
                        [
                            'category_name' => $keepa_cat['name'],
                            'channel' => $product->channel,
                            'user_id' => CRUDBooster::myId(),
                        ],
                        $channelUserCat
                    )->id;

                    $category_type = in_array($product->channel, Channel::MAPPING_CHANNELS) ? CategoryType::API : CategoryType::USER;
                }
                ChannelProductCategory::firstOrCreate(
                    [
                        'category_id' => $parent,
                        'channel_product_id' => $product->id,
                    ],
                    [
                        'category_id' => $parent,
                        'channel_product_id' => $product->id,
                        'category_type' => $category_type,
                    ]);
                $updated = true;
            }

            $magic_fields = [
                'brand' => $product->brand,
                'item_color' => $product->item_color,
                'item_weight' => $product->item_weight,
                'item_size' => $product->item_size,
                'ek_price' => $product->ek_price,
                'packaging_length'=> $product->packaging_length,
                'packaging_width'=> $product->packaging_width,
                'packaging_height'=> $product->packaging_height,
            ];
            $empty_fields = hasEmptyValue($magic_fields);

            if(!empty($empty_fields)){
                # only send request if atleast one required field is empty
                if(\in_array('brand', $empty_fields)) $update_product['brand'] = $keepa_product->brand;
                if(\in_array('item_color', $empty_fields)) $update_product['item_color'] = $keepa_product->color;
                if(\in_array('item_weight', $empty_fields)) $update_product['item_weight'] = $keepa_product->itemWeight;
                if(\in_array('item_size', $empty_fields)) $update_product['item_size'] = $keepa_product->size;
                if(\in_array('ek_price', $empty_fields)) $update_product['ek_price'] = $keepa_price;
                if(\in_array('packaging_length', $empty_fields)) $update_product['packaging_length'] = $keepa_product->packageLength;
                if(\in_array('packaging_width', $empty_fields)) $update_product['packaging_width'] = $keepa_product->packageWidth;
                if(\in_array('packaging_height', $empty_fields)) $update_product['packaging_height'] = $keepa_product->packageHeight;
                $this->productService->update($product->id, $update_product, True);

                $updated = true;
            }

            if( !empty($product->industry_template_data) ){
                if(isset($keepa_product->model)){
                    $languageId = app('App\Services\UserService')->getProductCountry(CRUDBooster::myParentId());
                    $lang = app('App\Services\UserService')->getProductLanguage($languageId);

                    $industry_template_data = json_decode($product->industry_template_data);
                    if(empty($industry_template_data->juvely->model->$lang)){
                        $industry_template_data->juvely->model->$lang = $keepa_product->model;

                        $data['industry_template_data'] = json_encode($industry_template_data);
                        $this->productService->update($product->id, $data, True);
                        $updated = true;
                    }
                }
            }
            if($updated) $count_updated_product++;
        }

        $response = [
            'status' => 'warning',
            'message' => 'Nothing to update!'
        ];

        if($count_updated_product){
            $response = [
                'status' => 'success',
                'message' => "$count_updated_product Product informations updated magically!"
            ];
        }
        charge_token($credit_req,$userId);
        //set_option('automagic_product_uses', 'automagic_product_update', $automagic_product_uses + $count_updated_product);
        return $response;
    }

    public function ebayExportPreview(Request $request)
    {
        $data['page_title'] = "eBay Export Preview";
        $user_id = CRUDBooster::myParentId();
        $data['lang'] = app('App\Services\UserService')->getLang($user_id);
        $data['products'] = array_map('makeInt',explode(',',$request->product_ids));
        $data['import_template'] = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, $user_id);
        $data['ebay_template'] = app(AppStoreService::class)->checkAppPurchased(Apps::EBAY_TEMPLATE, $user_id);

        try {
            $token = Shop::where([
                'id' => $request->shop_id,
                'user_id' => $user_id
            ])->value('username');

            $api = new Api($token);


            $data['policies'] = [
                'fulfillment' => $api->getFulfillmentPolicies(),
                'payment' => $api->getPaymentPolicies(),
                'return' => $api->getReturnPolicies(),
                'locations' => $api->getLocationKey()
            ];

//             $data['policies'] = [
//                 'fulfillment' => [],
//                 'payment' => [],
//                 'return' => [],
//                 'locations' => []
//             ];

            $data['small_business'] = DB::table('drm_invoice_setting')
                ->select('small_business')
                ->where('cms_user_id',$user_id)
                ->orderBy('id', 'desc')
                ->value('small_business');

            $data['country'] = DB::table('countries')
                ->where('id', '=', 1)->first();

            return view('admin.drm_export.ebay_export_preview',$data);
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'], $errorMessage);
        }
        return true;
    }

    public function kauflandExportPreview(Request $request)
    {
        $data['page_title'] = "Kaufland Export Preview";
        $user_id = CRUDBooster::myParentId();
        $data['lang'] = app('App\Services\UserService')->getLang($user_id);
        $data['products'] = array_map('makeInt',explode(',',$request->product_ids));

        try {
            $shop = Shop::where([
                'id' => $request->shop_id,
                'user_id' => $user_id
            ])->first();

            $api = new KauflandApi($shop);
            $data['warehouse'] = $api->getInventoryWarehouses()['data'] ?? array();

            if(empty($data['warehouse'])){
                throw new Exception("No data");
            }
            // $data['small_business'] = DB::table('drm_invoice_setting')
            // 	->select('small_business')
            // 	->where('cms_user_id',CRUDBooster::myParentId())
            // 	->first()->small_business;
            $data['tax'] = DB::table('tax_rates')
                ->where('id', '=', 4)
                ->value('charge');
            return view('admin.drm_export.kaufland_export_preview',$data);
        } catch (\Exception $e) {
            CRUDBooster::redirect($_SERVER['HTTP_REFERER'],"Invalid kaufland shop credentials. Please update your kaufland shop","warning");
        }
        return true;
    }

    public function etsyExportPreview(Request $request)
    {
        $data['page_title'] = "Etsy Export Setup";
        $user_id = CRUDBooster::myParentId();
        $data['lang'] = app('App\Services\UserService')->getLang($user_id);
        $data['products'] = array_map('makeInt',explode(',',$request->product_ids));

        try {
            $data['shipping_templates'] = app(EtsyService::class)->getShippingTemplates($user_id)['results'];

            return view('admin.drm_export.etsy_export_preview', $data);
        } catch (\Exception $e) {
            CRUDBooster::redirect(route('channel.product.index',['channel' => Channel::ETSY]), "Invalid Etsy shop credentials. Please update your Etsy shop");
        }
        return true;
    }

    public function getTitleFromAnalysis()
    {
        $analysis_title = $_REQUEST['analysis_title'];
        $analysis_created_at = $_REQUEST['analysis_created_at'];
        $ean = $_REQUEST['ean'];

        $title_from_analysis = ProductPriceApi::select('title', 'created_at')
            ->where('ean', $ean)
            ->get()
            ->unique('title')
            ->map(function($item){
                return [
                    'title' => $item->title,
                    'created_at' => date_format($item->created_at,"Y-m-d H:i:s")
                ];
            });

        if($title_from_analysis->isNotEmpty()){
            $title_from_analysis = $title_from_analysis->values();
        }else{
            $title_from_analysis = collect([
                ['title' => $analysis_title, 'created_at' => $analysis_created_at]
            ]);
        }

        // if($title_from_analysis->isEmpty()){
        //     $title_from_analysis = collect(
        //         [
        //             ['title' => $analysis_title, 'created_at' => $analysis_created_at]
        //         ]
        //         );
        // }

        return response()->json([
            'success' => true,
            'data' => $title_from_analysis
        ]);

    }

    public function replaceTitle(){

        $channel_product_id = $_REQUEST['product_id'];
        $channel_product_ean = $_REQUEST['product_ean'];
        $channel_product_channel = $_REQUEST['product_channel'];
        $channel_product_replace_title = $_REQUEST['replace_title'];

        $lang = app('\App\Services\UserService')->getLang(CRUDBooster::myParentId());
        $updated_title = [$lang => $channel_product_replace_title];

        $affectedRow = ChannelProduct::where(["id" => $channel_product_id, 'ean' => $channel_product_ean, 'channel' => $channel_product_channel])->update(["title" => $updated_title]);


        if($affectedRow){
            return response()->json([
                'success' => true,
                'message' => 'Title Replaced with Competitor Title',
            ], 200);
        }else{
            return response()->json(['success' => false], 400);
        }
    }

    public function exportWithAnalysisData(Request $request){
        // dd($request->all());
        $data = $request->all();
        return $this->productService->exportWithAnalysisData($data);
    }

    public function basicPriceProductCheck(){
        $currentUserId = CRUDBooster::myParentId();
        $channel = $_REQUEST['channel'];

        $product_ids = $_REQUEST['product_ids'];

        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);

        $_REQUEST['params']['country_id'] = $languageId ;

        if((int)$_REQUEST['selected_all']){
            $product_ids = $this->productService->getSelectedIds($currentUserId,$_REQUEST['params']);
        }

        $all_fields_fill_ids = [];
        $missing_fields_count = 0;

        ChannelProduct::select('id', 'vk_price', 'item_weight', 'item_unit')
            ->whereIn('id', $product_ids)
            ->where('channel', $channel)
            ->where('country_id', $languageId)
            ->chunk('100', function($products) use (&$all_fields_fill_ids, &$missing_fields_count){
                foreach($products as $product){

                    if( empty($product->vk_price) || empty($product->item_weight) || empty($product->item_unit) ){
                        $missing_fields_count ++;
                    }else if( !empty($product->vk_price) && !empty($product->item_weight) && !empty($product->item_unit) ){
                        $all_fields_fill_ids[] = $product->id;
                    }

                }
            });

        // dd($all_fields_fill_ids);

        return response()->json([
            'all_selected_products_count' => count($product_ids),
            'missing_fields_total_products_count' => $missing_fields_count,
            'all_field_fill_up_ids' => $all_fields_fill_ids,
        ]);

    }

    public function basicPriceCalculation(){
        // dd($_REQUEST);

        $calculation_product_ids = $_REQUEST['product_ids'];
        $channel = $_REQUEST['channel'];
        $total_selected_products_count = $_REQUEST['total_selected_products_count'];

        return $this->productService->basicPriceCalculation($calculation_product_ids, $channel, $total_selected_products_count);

    }

    public function updateDeliveryTime(){
        $product_ids = $_REQUEST['product_ids'];
        $delivery_time = $_REQUEST['delivery_time'];
        $selected_all = $_REQUEST['selected_all'];
        $params = $_REQUEST['params'];
        $shopId = $params['shop'];

        $user_id = CRUDBooster::myParentId();
        $country_id = app('App\Services\UserService')->getProductCountry($user_id);

        $params['country_id'] = $country_id;

        if((int)$selected_all){
            $product_ids = $this->productService->getSelectedIds($user_id, $params);
        }

        if(empty($delivery_time)){
            return response()->json([
                "success" => false,
                "message" => "Please Select Delivery Time !"
            ], 400);
        }

        foreach (array_chunk($product_ids,300) as $items) {

            $items = ChannelProduct::where([
                'user_id' => CRUDBooster::myParentId(),
                'shop_id' => $shopId
            ])->where('delivery_days','!=',$delivery_time)->whereIn('id', $items)->pluck('id')->toArray();

            foreach ($items as $item) {
                $this->productService->update($item,['delivery_days' => $delivery_time],true);
            }
        }

        return response()->json([
            "success" => true,
            "message" => "Delivery Time Update Successful!"
        ]);

//        return $this->productService->updateDeliveryTime($product_ids, $channel, $country_id, $delivery_time);
    }

    public function mainLevelDeliveryTimeAssign(){

        $product_ids = $_REQUEST['product_ids'];
        $channel = $_REQUEST['params']['channel'];
        $selected_all = $_REQUEST['selected_all'];
        $params = $_REQUEST['params'];

        $user_id = CRUDBooster::myParentId();
        $country_id = app('App\Services\UserService')->getProductCountry($user_id);

        $params['country_id'] = $country_id;

        if((int)$selected_all){
            $product_ids = $this->productService->getSelectedIds($user_id, $params);
        }

        $response = $this->productService->mainLevelDeliveryTimeAssign($product_ids, $channel, $country_id);

        return $response;

    }

    public function getChannelAllCategories(Request $request)
    {
        if ($request->ajax()){
            $page = $request->page;
            $sort_by = $request->sort_by;

            $terms = $request->term;
            $currentUserId = CRUDBooster::myParentId();
            $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
            $lang = app('App\Services\UserService')->getProductLanguage($languageId);

            $all_categories = $this->productService->getShopCategoriesNew(
                (int) $request->shop_id,
                (int)$request->channel,
                $currentUserId,
                $terms,
                $sort_by
            );

            $morePages=true;

            if (empty($all_categories->nextPageUrl())){
                $morePages=false;
            }

            // if(empty($sort_by)){
            //     $all_categories = $all_categories
            //         ->map(function($category){
            //             return [
            //                 'id' => $category->id,
            //                 'text' => $category->id . " - " . $category->original_full_path
            //             ];
            //         });
            // }else{

            //     $all_categories = $all_categories
            //         ->map(function($category){
            //             return [
            //                 'id' => $category->id,
            //                 'text' => $category->original_full_path
            //             ];
            //         });

            //     if($sort_by == 1){
            //         $all_categories = $all_categories
            //         ->sortBy('text')->values()
            //         ->map(function($product_cat){
            //             return [
            //                 'id' => $product_cat['id'],
            //                 'text' => $product_cat['id'] . " - " . $product_cat['text']
            //             ];
            //         });
            //     }else if($sort_by == 2){
            //         $all_categories = $all_categories
            //         ->sortByDesc('text')->values()
            //         ->map(function($product_cat){
            //             return [
            //                 'id' => $product_cat['id'],
            //                 'text' => $product_cat['id'] . " - " . $product_cat['text']
            //             ];
            //         });
            //     }

            // }


            $all_categories = $all_categories
                ->map(function($category){
                    return [
                        'id' => $category->id,
                        'text' => $category->id . " - " . $category->original_full_path
                    ];
                });

            $results = array(
                "results" => $all_categories,
                "pagination" => array(
                    "more" => $morePages
                )
            );

            return response()->json($results);
        }

    }

    public function getProductCategoriesNew($product_id)
    {
        $channel = (int) $_REQUEST['channel'];
        $user_id = CRUDBooster::myParentId();

        $categories = $this->productService->getProductCategoriesNew((int) $product_id, $channel, $user_id);

        $html = '';

        foreach($categories as $category){
            $html .= "<option value='" . $category->id . "' selected='selected'>" . $category->id . " - " . $category->full_path . "</option>";
        }

        return response($html);
    }

    public function selectAllChannelProducts()
    {
        $param = $_REQUEST['params'];
        $user_id = CRUDBooster::myParentId();

        $product_ids = $this->productService->getSelectedIds($user_id, $param);

        return response()->json([
            'success' => true,
            'data' => $product_ids
        ]);
    }

    public function allSelectedChannelProductsDelete(Request $request)
    {
        if(!$this->canAccess('delete')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $param = $_REQUEST['params'];
        $user_id = CRUDBooster::myParentId();
        $channel = (int) $request->channel;
        $selected_all = $request->select_all;
        $ids = [];

        if((int)$selected_all){
            $ids = $this->productService->getSelectedIds($user_id, $param);
        }

        try {
            foreach(array_chunk($ids, 500) as $chunkIds){
                DestroyChannelProduct::dispatch($chunkIds, $request->shop, $user_id);
            }

            try { // 3 all selected
                $shop = Shop::find($request->shop);

                create_agb_log(
                    $shop->user_id,
                    [
                        'shop_info' => ['shop_name' => $shop->shop_name, 'shop_type' => $shop->channel, 'shop_url' => $shop->url,],
                        'product_ids' => $ids,
                    ],
                    [],
                    count($ids) . ' items deleted from shop ' . $shop->shop_name,
                );
            } catch (\Exception $th) {}

            return response()->json([
                'success' => true,
                'message' => 'All products delete process started. It may take some time to delete all products.'
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 401);
        }

    }

    public function makeCalculationDefault(){

        $cal_id = (int) $_REQUEST['calculation_id'];
        $user_id = CRUDBooster::myParentId();

        $default_exist = $this->checkProfitCalHasDefaultCal($user_id);

        $user_purchased_calculation_app = DrmUserHasPurchasedApp($user_id, config('global.auto_calculaiton_app_id'));

        if($user_purchased_calculation_app){

            if(!$default_exist){

                $profit_cal = ProfitCalculation::where(["id" => $cal_id, "user_id" => $user_id ])->first();

                if(!$profit_cal->price_on_request){

                    $profit_cal->auto_calculation = true;
                    $profit_cal->save();

                    return response()->json([
                        'success' => true,
                        'message' => 'You set "'. $profit_cal->name .'" calculation as a default calculation'
                    ], 200);

                }else{

                    return response()->json([
                        'success' => false,
                        'message' => 'Do Not Try Any More'
                    ], 400);
                }

            }else{

                return response()->json([
                    'success' => false,
                    'message' => 'You already have a default calculation, if you want to change default calculaiton, first remove present calculation then make default anothe one'
                ], 400);

            }

        }else{
            return response()->json([
                'success' => false,
                'message' => 'Please Purchase App First'
            ], 400);
        }
    }

    public function removeCalculationDefault(){

        $cal_id = (int) $_REQUEST['calculation_id'];
        $user_id = CRUDBooster::myParentId();

        $profit_cal = ProfitCalculation::where(["id" => $cal_id, "user_id" => $user_id ])->first();

        $profit_cal->auto_calculation = false;
        $profit_cal->save();

        return response()->json([
            'success' => true,
            'message' => 'You have removed "'. $profit_cal->name .'" calculation from default calculation'
        ], 200);

    }

    public function checkProfitCalHasDefaultCal($user_id){

        return $this->productService->checkProfitCalHasDefaultCal($user_id);

    }

    public function eanExistsCheckNew(Request $request): JsonResponse
    {
        try{
            $additional_eans = array_filter(json_decode($request->additional_eans, true));
            $channel = $request->channel;
            $user_id = CRUDBooster::myParentId();

            $totalEan = count($additional_eans);
            $ean_exists = false;
            $message = '';

            if($totalEan){
                $main_ean = $additional_eans[$totalEan - 1];

                $ean_exists = $this->productService->checkEanExists($user_id, $channel, $main_ean);

                if($ean_exists){
                    $message = 'EAN is already exist at this channel.';
                }
            }

            return response()->json([
                'success' => true,
                'ean_exists' => $ean_exists,
                'message' => $message
            ], 200);

        } catch(\Exception $ex) {
            return response()->json([
                'success' => false,
                'message' => $ex->getMessage()
            ],422);
        }
    }

    public function updateDetailsNew(Request $request): JsonResponse
    {
        if(!$this->canAccess('edit')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        try{
            $data = $request->except(['import_id','_token','image_url','images','product-tags']);
            // $lang = Arr::get($data, 'lang', 'de');
            $user_id = CRUDBooster::myParentId();
            $lang = app('App\Services\UserService')->getLang($user_id);
            $data['category_aspects'] = json_encode($data['category_aspects']);

            if(!empty($data['title'])){
                $data['title'] = [$lang=>$data['title']];
            }
            if(!empty($data['short_description'])){
                $data['short_description'] = [$lang=>$data['short_description']];
            }
            // if(!empty($data['desc'])){
            //     $data['description'] = [$lang=>$data['desc']];
            // }

            if (!empty($data['desc'])) {
                $desc = $data['desc'];
                for($i=0; $i<strlen($desc) - 8; $i++){
                    if($desc[$i] === '[' && $desc[$i+1] === 'B' && $desc[$i+2] === 'r'&& $desc[$i+3] === 'a' && $desc[$i+4] === 'n' && $desc[$i+5] === 'd' && $desc[$i+6] === ' ' && $desc[$i+7] === '-' && $desc[$i+8] === ' '){
                        $j = 1;
                        $str = '';
                        while(true){
                            if($desc[$i+8+$j] == "]")
                                break;
                            $str .= $desc[$i+8+$j];
                            $j++;
                        }
                        $brandId = (int)$str;
                        $brand = DropmatixProductBrand::where('id', $brandId)->first();
                        if($brand->brand_logo[0]){
                            $replaceTag = "<img style='height:40px;width:50px;' src='{$brand->brand_logo[0]}'>";
                        }
                        else{
                            $replaceTag = "<b>$brand->brand_name</b>";
                        }
                        $x = "[Brand - {$brandId}]";
                        $desc = str_replace($x, $replaceTag, $desc);
                    }
                }

                $data['description'] = [$lang => $desc];
            }


            if (!empty($data['item_color'])) {
                if($data['item_color'] == 'empty'){
                    $colors = [];
                }
                else{
                    $colors = explode(',', $data['item_color']);
                }
                $data['options'] = ['color' => $colors];
            }

            $data['industry_template_data'] = $request->fields;

            $industry_template_name = $request->industry_template_name;
            $industry_temp_data = null;

            if ($industry_template_name) {

                if(in_array($industry_template_name, array_keys(config('industry_template')))){
                    $industry_template_field_list = $request->$industry_template_name;
                }else{
                    $formated_temp_name = str_replace(' ', '_', $industry_template_name);
                    $industry_template_field_list = $request->$formated_temp_name;
                }

                $tmp_field_list = [];
                foreach ($industry_template_field_list as $field_key => $field_value) {

                    $tmp_field_list[$field_key] = [
                        'de' => ($lang == 'de') ? $field_value : null,
                        'en' => ($lang == 'en') ? $field_value : null,
                    ];
                }
                $industry_temp_data[$industry_template_name] = $tmp_field_list;
            }

            if($industry_temp_data){
                $data['industry_template_data'] = json_encode($industry_temp_data, true);
            }

            if ($request->additional_data) {
                $attr = $request->additional_data;
//                $attr['dt_variants']['color'] = implode(',',$request->additional_data['dt_variants']['color']);
//                $attr['dt_variants']['size'] = implode(',',$request->additional_data['dt_variants']['size']);
                $data['attributes'] = $attr;
            }

            if($data['additional_eans'] == 'empty'){
                $data['additional_eans'] = [];
            }

            if($data['cat_changed'] == 'true'){
                $data['category_aspects'] = null;
                $cat_request = new \Illuminate\Http\Request();

                $cat_request->replace([
                    'product_ids' => [$request->product_id],
                    'channel' => $data['channel'],
                    'category_ids' => $data['category'],
                    'selected_all' => '0',
                    'params[channel]' => $data['channel']
                ]);

                $this->bulkChangeCategory($cat_request);
            }

            $this->productService->update($request->product_id, $data , true);

            if(in_array($data['channel'],Channel::MIRAKL_CHANNELS)){
                $transfer_fields = \App\Enums\Product::ALL_FIELDS;

                $except_fields = [
                    'ean',
                    'stock',
                    'ek_price',
                    'vk_price',
                    'item_number'
                ];

                foreach($except_fields as $field){
                    if (($key = array_search($field, $transfer_fields)) !== false) {
                        unset($transfer_fields[$key]);
                    }
                }

                array_push($transfer_fields, "item_unit", "shipping_cost");

                $drm_product_id = ChannelProduct::where('id', $request->product_id)->pluck('drm_product_id')->toArray();
                $data_shop['shop_id'] = $data['shop_id'];

                if(!empty($drm_product_id)){
                    TransferProduct::dispatch($drm_product_id, $data['channel'], $transfer_fields, $lang, 'update', true, 0, $data_shop);
                }
            }

            return response()->json(['success'=>true]);
        } catch(\Exception $ex){}
        return response()->json(['success'=>false, 'msg'=> $ex],422);
    }

    public function signRequest($method, $uri, $body, $timestamp, $secretKey){
        $string = implode("\n", [
            $method,
            $uri,
            $body,
            $timestamp,
        ]);

        return hash_hmac('sha256', $string, $secretKey);
    }


    public function dbMod() {

        ini_set('max_execution_time', -1);
        try{

            $field_name_conversion_array = config('kaufland_api_category_drm_name.field_names');
            $cats = DB::table('channel_categories')->where('channel', 13)->get();

            $available_fields = [
                'title',
                'item_number',
                'ean',
                'additional_eans',
                'description',
                'short_description',
                'internal_comments',
                'comment',
                'images',
                'picture',
                'ek_price',
                'vk_price',
                'min_price',
                'max_price',
                'cheapest_price',
                'stock',
                'category',
                'item_weight',
                'weight',
                'item_unit',
                'unit',
                'item_size',
                'size',
                'item_color',
                'color',
                'note',
                'production_year',
                'brand',
                'materials',
                'tags',
                'gender',
                'delivery_company',
                'delivery_days',
                'country_id',
                'attributes',
                'variants',
                'shipping_cost',
                'offer_uvp',
                'tax_included_price',
                'metadata',
                'manufacturer',
                'manufacturer_link',
                'manufacturer_id',
                'mpn',
                'region',
                'country_of_origin',
                'gross_weight',
                'net_weight',
                'product_length',
                'length',
                'product_width',
                'width',
                'product_height',
                'height',
                'dimension',
                'packaging_dimensions',
                'packaging_length',
                'packaging_width',
                'packaging_height',
            ];

            foreach($cats as $cat){
                try{
                    $baseUrl = 'https://sellerapi.kaufland.com/v2/categories/';

                    $clientKey = '486df48518aa57f2b8534832d2330cf5';
                    $secretKey = '41c0479fa19131dc25c1b563d1409e6d2c0dced01788776ad3d074846634185b';

                    $uri = $baseUrl . $cat->category_id . '/';

                    $params = [
                        'embedded' => 'required_attributes',
                        'storefront' => 'de'
                    ];

                    $uri .= '?' . http_build_query($params) . '&embedded=optional_attributes';

                    $timestamp = time();

                    $userAgent = 'test';

                    $headers = [
                        'Accept: application/json',
                        'Shop-Client-Key: ' . $clientKey,
                        'Shop-Timestamp: ' . $timestamp,
                        'Shop-Signature: ' . $this->signRequest('GET', $uri, '', $timestamp, $secretKey),
                        'User-Agent: ' . $userAgent,
                    ];

                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $uri);
                    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

                    $category = json_decode(curl_exec($ch), true);
                    curl_close($ch);

                    $required_fields = [];
                    $optional_fields = [];

                    $required_att = $category['data']['required_attributes'] ?? [];
                    $optional_att = $category['data']['optional_attributes'] ?? [];

                    foreach($required_att as $field){
                        if(!in_array($field['name'], $available_fields)){
                            $field['show'] = true;
                        }else{
                            $field['show'] = false;
                        }
                        foreach($field_name_conversion_array as $key => $value){
                            if(in_array($field['name'], $value)){
                                $field['export_name'] = $field['name'];
                                $field['name'] = $key;
                                break;
                            }else{
                                $field['export_name'] = $field['name'];
                            }
                        }

                        $required_fields[] = $field;
                    }

                    foreach($optional_att as $field){
                        if(!in_array($field['name'], $available_fields)){
                            $field['show'] = true;
                        }else{
                            $field['show'] = false;
                        }
                        foreach($field_name_conversion_array as $key => $value){
                            if(in_array($field['name'], $value)){
                                $field['export_name'] = $field['name'];
                                $field['name'] = $key;
                                break;
                            }else{
                                $field['export_name'] = $field['name'];
                            }
                        }
                        $optional_fields[] = $field;
                    }

                    $misc['required_columns'] = $required_fields;
                    $misc['optional_columns'] = $optional_fields;

                    DB::table('channel_categories')->where('id', $cat->id)->where('channel', 13)->update([
                        'misc' => json_encode($misc)
                    ]);
                } catch(Exception $e){
                    dd($e, $cat);
                }
            }

            dd(1);



            $users = ChannelProduct::select('user_id')->distinct()->get();
            foreach($users as $user){
                $products = ChannelProduct::where('user_id', $user->user_id)->get();
                foreach($products as $product){
                    if($product->item_color != null && $product->item_color != ""){
                        $color = $product->item_color;
                        $colors = preg_split("/[\s,-\/]+/", $color);
                        $options = [
                            'color' => $colors
                        ];
                        ChannelProduct::where('id', $product->id)->update([
                            'options' => json_encode($options)
                        ]);
                    }
                }
            }

            return response()->json(['success' => true, 'data' => "mod done"]);
        }
        catch(Exception $e){
            dd($e);
        }
    }

    public function syncDecathlon(Request $request){
        $user_id = CRUDBooster::myParentId();
        $product_id = $request->product_id;
        $channelManager = new MiraklChannelManager();
        $service = $channelManager($request->shop_id);
        $service->syncWithMiraklAPI([$product_id]);

        return ChannelProduct::where([
            'shop_id' => $request->shop_id,
            'id' => $product_id,
            'user_id' => $user_id
        ])->value('decathlon_status');
    }

    public function bulkSyncDecathlon(Request $request){
        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->product_ids;
        $selected_all = $request->selected_all;
        if($selected_all){
            $params = $request->params;
            $product_ids = $this->productService->getSelectedIds($user_id,$params);
        }
        foreach(array_chunk($product_ids, 100) as $chunk){
            ProcessDecathlonColumnUpdateJob::dispatch($chunk, $user_id,$request->shop_id);
        }
        return response()->json(['success' => true]);
    }

    public function syncMiraklMpStatus(Request $request){
        $user_id = CRUDBooster::myParentId();
        $shop = Shop::where([
            'user_id' => $user_id,
            'id' => $request->shop_id
        ])->first();

        $shop->shop_mp_synced_at = now();
        $shop->save();

        $product_ids = ChannelProduct::where([
            'channel' => $shop->channel,
            'shop_id' => $shop->id,
            'user_id' => $user_id
        ])->pluck('id')->toArray();

        foreach(array_chunk($product_ids, 100) as $chunk){
            ProcessDecathlonColumnUpdateJob::dispatch($chunk, $user_id,$request->shop_id);
        }
        return response()->json(['success' => true]);
    }

    public function searchProductTags(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $html = '';

        if ($request->search_by == 'tags') {
            $tags = DB::table('channel_products')
                ->where('user_id', $user_id)
                ->where('channel_products.tags', 'LIKE', '%' . $request->key . '%')
                ->select('tags')
                ->groupBy('tags')
                ->paginate(20);

            foreach ($tags as $tag) {
                $tagArray = explode(',',$tag->tags);
                if($tagArray){
                    foreach($tagArray as $item){
                        if(Str::contains($item, $request->key)){
                            $html .= '<li id="single_tag" data-tag="' . $item . '">' . $item . '</li>';
                        }
                    }
                }
            }
        }

        return $html;
    }

    public function decathlonExport(Request $request){
        $product_ids = explode(",", $request->get('product_ids'));
        $isCheckAll = $request->get('checkAll');
        $params = $request->get('params');
        $user_id = CRUDBooster::myParentId();

        if($isCheckAll){
            $product_ids = $this->productService->getSelectedIds($user_id,$params);
        }

        $job_payload = [
            'user_id' => $user_id,
            'ids' => $product_ids,
            'created_at' => now(),
            'updated_at' => now(),
            'step' => 1,
        ];

        $payload_id = DB::table('decathlon_export_requests')->insertGetId([
            'user_id' => $user_id,
            'payload' => json_encode($job_payload),
        ]);

        DecathlonExportJob::dispatch($product_ids, $user_id, $payload_id);

        return response()->json([
            'success' => true,
            'message' => __('Process running on background'),
        ]);
    }

    public function repricingToggle(Request $request): JsonResponse
    {
        try {
            $success = false;
            $message = '';
            $data    = [];

            $repricing_status = (int) $request->get('repricing_status', 0);
            $calculation_id   = (int) $request->get('calculation_id', 0);

            $update_done = ProfitCalculation::where([
                    'id'      => $calculation_id,
                    'user_id' => CRUDBooster::myParentId()
                ])
                ->update([
                    'repricing_status' => $repricing_status
                ]);

            if ($update_done) {
                $success = true;
                $message = __("Repricer updated successfully!");
            } else {
                $message = __("js_common_error_msg");
            }

            return response()->json([
                'success' => $success,
                'data'    => $data,
                'message' => $message,
            ], 200);
        } catch(\Exception $ex) {
            return response()->json([
                'success' => false,
                'message' => $ex->getMessage()
            ], 422);
        }
    }

    public function uploadCategoryAspectFile(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:jpg,png,pdf,docx|max:2048',
            'aspect' => 'required|string',
            'product_id' => 'required|numeric'
        ]);
        $aspect = str_replace('aspect_','',$request->aspect);
        $path = "channel_images/".$request->product_id.'_'.$aspect.'.'.$request->file->extension();
        Storage::disk('spaces')->put($path, file_get_contents($request->file->getRealPath()),'public');
        return response()->json(['message' => 'File uploaded successfully', 'file_url' => Storage::disk('spaces')->url($path)]);
    }
}
