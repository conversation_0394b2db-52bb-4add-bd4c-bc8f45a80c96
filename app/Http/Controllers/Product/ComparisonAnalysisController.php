<?php
namespace App\Http\Controllers\Product;


use Illuminate\Http\Request;

use App\Helper\DrmHelper;
use DateTime;

use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;
use App\Models\Product\ComparisonAnalysisProduct;
use App\MarketplaceProducts;
use App\Http\Controllers\AdminDrmImportsController;
use App\Services\ProductApi\Services\Countdown;
use App\Services\ProductApi\Services\GoogleShopping;
use App\Services\ProductApi\Services\Rainforest;

class ComparisonAnalysisController
{
    public function index(Request $request)
	{
		$user_id = CRUDBooster::myParentId();
        $my_id = CRUDBooster::myId();

        $sorting = null;
        $order_by = null;

        $sort_filter = $request->get('filter_column') ?? [];
        if(!empty($sort_filter))
        {
            $order_by = key($sort_filter);
            $sorting = $sort_filter[$order_by]['sorting'];
        }

        $search_by_field = $request->get('search_by_field_column');
        $filters = $request->all();

        $products = ComparisonAnalysisProduct::where('user_id', $user_id);

        if($order_by && $sorting){

            $products->orderBy($order_by, $sorting);
            
            // if($order_by === 'mp_stock'){
            //     $eans = $this->getMpEanSortList($user_id, $sorting);

                
            //     if(is_array($eans) && !empty($eans))
            //     {
            //         dd('sort');
            //         $placeholders = implode(',',array_fill(0, count($eans), '?'));
            //         $products->orderByRaw("field(ean,{$placeholders})", $eans);
            //     }

            //     dd($eans);

            // } else {
            //     $products->orderBy($order_by, $sorting);
            // }
        }

        if(!empty($search_by_field) && !empty($filters['q'])){
            $q = $filters['q'];
            if($search_by_field == 'title' || $search_by_field == 'all'){
                $q = "%$q%";
            }
            if($search_by_field != 'all'){
                $products->where($search_by_field, 'LIKE', $q);
            }else{
                $products->where(function($p) use ($q) {
                    $p->where('ean', 'LIKE', $q);
                    $p->orWhere('title', 'LIKE', "%$q%");
                    $p->orWhere('id', 'LIKE', $q);
                });
            }
        }

        $limit = $request->get('limit') ?? 20;
		$products = $products->orderBy('id','desc')->paginate($limit);

        $table_column = $this->table_column();

		$product_collection = $products->getCollection()
            ->map(function($item) use ($channels_list, $channel_color, $search_by_channel , $saved_column, $user_id) {

                // $mp_product = MarketplaceProducts::where('ean', $item->ean)->orderBy('ek_price', 'asc')->orderBy('stock', 'desc')->first();

                $image = json_decode($mp_product->image, true)[0];
                if(empty($image)){
                    $image = !empty($item->image) && is_array($item->image) ? $item->image[0] : asset('images/blank-product.png');
                }

                $row  = [
                    'id' => $item->id,
                    'title' => $mp_product->name ?? $item->title,
                    'ean' => $item->ean,
                    'source_ek_price' => $item->source_ek_price ?? 0,
                    'mp_ek_price' => $item->mp_ek_price ?? 0,
                    'source_stock' => $item->source_stock ?? 0,
                    'mp_stock' => $item->mp_stock ?? 0,
                    'image' => $image,
                    'mp_available' => $item->mp_stock > 0 ? true : false,
                    'updated_at' => $item->updated_at
                ];

                return $row;

			});

        $highlighting = Cache::get('comparison_highlight_'.$my_id);
        if(empty($highlighting)){
            $highlighting = 'false';
        }

		$data = [];
        $products->setCollection($product_collection);
        $productIds = $products->pluck('id')->toArray();
        $data['products'] = $products;
        $data['product_ids'] = $productIds;

        $data['columns'] = $table_column;

        $data['all_columns'] = $table_column;

        $data['highlighting'] = $highlighting;


        $data['languageId'] = app('App\Services\UserService')->getProductCountry($user_id);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($data['languageId']);

        $data['user_id'] = $user_id;
		return view('admin.cp_analysis.comparison_analysis.index', $data);
	}



    private function getMpEanSortList($user_id, $sort)
    {
        return cache()->remember("comparison_mp_stock_{$user_id}_{$sort}", now()->addMinutes(2), function() use ($user_id, $sort) {
            $stocks = [];
            ComparisonAnalysisProduct::where('user_id', $user_id)
            ->orderBy('id')
            ->select('ean')
            ->chunk(200, function($chunk) use (&$stocks, $sort) {
                $eans = $chunk->pluck('ean')->toArray();
                $res = MarketplaceProducts::whereIn('ean', $eans)
                ->where('stock', '>', 0)
                ->orderBy('stock', $sort)->pluck('stock', 'ean')
                ->toArray();
                $stocks = array_merge($stocks, $res);
            });

            if($sort === 'asc') {
                sort($stocks);
            } else {
                rsort($stocks);
            }

            $max = count($stocks) > 200 ? 200 : count($stocks);

            return array_slice($stocks, 0, 200);

        }) ?? [];
    }



    public function updateMpStock($id)
    {
        ComparisonAnalysisProduct::where('user_id', $id)
        ->whereNull('deleted_at')
        ->select('ean', 'id')
        ->chunk(200, function($chunk) use (&$stocks, $sort) {
            $eans = $chunk->pluck('ean')->toArray();
            MarketplaceProducts::whereIn('ean', $eans)
            ->orderBy('stock', 'asc')
            ->select('stock', 'ean', 'ek_price')
            ->get()
            ->each(function($item) {
                ComparisonAnalysisProduct::where('ean', $item->ean)->update([
                    'mp_stock' => $item->stock,
                    'mp_ek_price' => $item->ek_price,
                    'updated_at' => now()
                ]);
            });
        });
    }




    public function table_column(){
        return [
            'id'                    => ["label" => __("ID") , "sorting" => true],
            'title'                 => ["label" => __('cp_title') , "sorting" => true],
            'image'                 => ["label" => __('cp_image') , "sorting" => false],
            'ean'                   => ["label" => __("EAN") , "sorting" => true],
            'source_ek_price'       => ["label" => __("Source Ek Price") , "sorting" => true],
            'mp_ek_price'           => ["label" => __('Marketplace Ek Price') , "sorting" => true],
            'source_stock'          => ["label" => __('Source Stock') , "sorting" => true],
            'mp_stock'              => ["label" => __('Marketplace Stock') , "sorting" => true],
            'last_updated'          => ["label" => __('Last Updated') , "sorting" => true],
        ];
    }

    public function highlighter() {

        $my_id = CRUDBooster::myId();

        $highlighting = Cache::get('comparison_highlight_'.$my_id);
        if(empty($highlighting) || $highlighting == 'false'){
            $highlighting = 'true';
            $message = "Enabled Highlighter!";
        }
        else{
            $highlighting = 'false';
            $message = "Disabled Highlighter!";
        }
        Cache::put('comparison_highlight_'.$my_id, $highlighting);

        return response()->json([
            'success' => true,
            'message' => $message,
        ]);
    }

    public function delete(Request $request) {
        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->product_ids;
        $isCheckAll = $request->checkAll;
        if($isCheckAll == 'true'){
            ComparisonAnalysisProduct::where('user_id', $user_id)->delete();
        }
        else{
            ComparisonAnalysisProduct::where('user_id', $user_id)->whereIn('id', $product_ids)->delete();
        }

        return response()->json([
            'success' => true,
        ]);
    }

    public function import(Request $request){

        $userId = CRUDBooster::myParentId();

        $data = [];
        if ($request->get('file') && !$request->get('import')) {
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;

            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = [
                'ean',
                'source_ek_price',
                'image',
                'title',
                'source_stock',
            ];

            $labels = [
                0 => 'EAN',
                1 => __('Purchase Price'),
                2 => __('Image'),
                3 => __('Product Name'),
                4 => __('Inventory'),
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        $data['page_title'] = 'Comparison Analysis Import';
        return view('admin.cp_analysis.comparison_analysis.import', $data);
    }

    public function uploadCsv(Request $request){

        if ($request->hasFile('userfile')) {

            try{
                $request->validate([
                    'userfile' => 'required|mimetypes:text/csv,text/plain,application/csv,text/comma-separated-values,text/anytext,application/octet-stream,application/txt',
                ]);

                $file = $request->file('userfile');
                $ext = $file->getClientOriginalExtension();

                $filePath = 'uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
                Storage::makeDirectory($filePath);

                $filename = md5(Str::random(5)).'.'.$ext;
                $url_filename = '';
                if (Storage::putFileAs($filePath, $file, $filename)) {
                    $url_filename = $filePath.'/'.$filename;
                }
                $url = route('drm.competitive_analysis.comparison_import').'?file='.base64_encode($url_filename);

                return redirect($url);
            } catch(Exception $e){
                $url = route('drm.competitive_analysis.comparison_import');

                return CRUDBooster::redirect($url, "Please Upload a Valid File");
            }
        } else {
            return redirect()->back();
        }
    }

    public function done_import(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Import Products"]);
        Session::put('select_column', $request->get('select_column'));

        return view('admin.cp_analysis.comparison_analysis.import', $data);
    }

    public function do_import_chunk(Request $request)
    {
        try{
            $file_md5 = md5($request->get('file'));

            if ($request->get('file') && $request->get('resume') == 1 && $request->get('action_type') != 0) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }

                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }

            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);

            $table_columns = [
                'ean',
                'source_ek_price',
                'image',
                'title',
                'source_stock',
            ];

            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $count = 0;

            $analyseCollection = [];
            foreach ($rows as $row) {
                $count++;
                if($request->get('action_type') != 0){
                    Cache::put('success_' . $file_md5, $count);
                }
                $row = (object) $row;
                foreach ($select_column as $csvColName => $val) {

                    $colname = $table_columns[$csvColName];
                    // if ($row->$val == '') continue;

                    if($colname == 'image'){
                        $row->$val = explode(',', $row->$val);
                        $row->$val = json_encode($row->$val);
                    }
                    if($colname == 'source_ek_price'){
                        $row->$val = removeCommaPrice( $row->$val ?? 0 );
                    }

                    if($colname == 'ean'){
                        $row->$val = trim($row->$val);
                    }

                    $data[$colname] = $row->$val;
                }

                $data['user_id'] = $user_id;
                $data['deleted_at'] = null;

                try {
                    $analyseCollection[$data['ean']] = $data;
                } catch (Exception $e) {
                    $e = (string) $e;
                    Cache::put('error_' . $file_md5, $e, 500);
                }
            }

            $chunks = array_chunk($analyseCollection, 2000);
            foreach ($chunks as $chunk) {
                foreach ($chunk as $data){
                    ComparisonAnalysisProduct::updateOrInsert(
                            ['ean' => $data['ean']],
                            $data
                        );
                }
            }
            return response()->json(['status' => true, 'message' => $res['message']]);
        }catch (Exception $e) {
            return response()->json(['status' => false, 'message' => "Sorry, something went wrong"]);
        }
    }



    public function manual_import(Request $request){

        $userId = CRUDBooster::myParentId();

        $data = [];
        if ($request->get('file') && !$request->get('import')) {
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;

            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = [
                'ean',
                'source_ek_price',
                'image',
                'title',
                'source_stock',
            ];

            $labels = [
                0 => 'EAN',
                1 => __('Purchase Price'),
                2 => __('Image'),
                3 => __('Product Name'),
                4 => __('Inventory'),
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        $data['page_title'] = 'Competitive analysis import';
        return view('admin.cp_analysis.comparison_analysis.manual_import', $data);
    }

    public function upload_ean(Request $request){

        if ($request->has('ean')) {
            Cache::put('comparison_manual_import_details', []);

            $ean = $request->ean;

            if( strlen($ean) >= 12 && strlen($ean) <= 13){
                $countdown = new Countdown();
                $details = $countdown->getProductDetails($ean);
                if($details == null){
                    $rainforest = new Rainforest();
                    $details = $rainforest->getProductDetails($ean);
                    if($details == null){
                        $googleshopping = new GoogleShopping();
                        $details = $googleshopping->getProductDetails($ean);
                    }
                }
                $url = route('drm.competitive_analysis.comparison_manual_import').'?ean='.$ean;
                Cache::put('comparison_manual_import_details', $details);
                return redirect($url)->with(['details' => $details]);
            }
            else{
                return redirect()->back()->with(['len' => strlen($ean)]);
            }
        } else {
            return redirect()->back();
        }
    }

    public function insert_product(Request $request){
        $ean = $request->ean;
        $user_id = CRUDBooster::myParentId();
        $existing_id = DB::table('comparison_analysis_products')->select('id')->where('ean', $ean)->where('user_id', $user_id)->get();
        $url = route('drm.competitive_analysis.manual_import').'?ean='.$ean;
        if($existing_id[0]->id){
            $url = route('drm.competitive_analysis.manual_import').'?ean='.$ean;
            return redirect($url)->with(['success' => false]);
        }
        else{
            $url = route('drm.competitive_analysis.manual_import');
            return redirect($url)->with(['success' => true]);
        }
    }

    public function manualTransferToAnalysis(Request $request)
    {
        $request->validate([
            'details' => 'required|array|min:1',
        ],
        [
            'details.*' => 'Please select at least one product',
        ]);

        $user_id = CRUDBooster::myParentId();
        $details = $request->input('details');

        $id_exist = ComparisonAnalysisProduct::where('ean', $details[0])
        ->where('user_id', $user_id)->first();
        if($id_exist != null){
            return [
                'success' => false,
                'message' => 'Products already exported to analysis. Please import new products.',
            ];
        }

        else{
            try{
                $product = [
                    [
                        'title' => $details[1],
                        'ean' => $details[0],
                        'image' => json_encode($details[2] ?? []),
                        'source_ek_price' => $details[3],
                        'source_stock' => $details[4],
                        'user_id' => $user_id,
                        'deleted_at' => null,
                        'updated_at' => now()
                    ]
                ];

                DB::table('comparison_analysis_products')
                    ->updateOrInsert(
                        ['ean' => $product[0]['ean']],
                        $product[0]
                    );
            }catch(Exception $e){
                dd($e);
            }

        }

        $res = [
            'success' => true,
            'message' => __("Great, the product is now analyzed!"), // for us-task-687
        ];

        return response()->json($res);
    }

}
