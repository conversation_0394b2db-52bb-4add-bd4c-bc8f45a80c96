<?php
namespace App\Http\Controllers\Product;
use Illuminate\Http\Request;
use App\Models\Product\ComparisonAnalysisProduct;
use App\Shop;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;


use League\Csv\Writer;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;


use App\Services\CpExportService\CpExport;

use App\User;

use App\Notifications\DRMNotification;
use Storage;
use App\Jobs\ComparisonExportJob;

class ComparisonAnalysisExportController
{

    public function export(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $my_id = CRUDBooster::myId();
        $isCheckAll = $request->checked_all === 'true';
        $products_id = $request->product_id ? explode(',', $request->product_id) : [];
        $file_ext = $request->file_ext;
        $search_by_field_column = $request->search_by_field_column;
        $q = $request->q;

        $highlighting = Cache::get('comparison_highlight_'.$my_id);
        if(empty($highlighting)){
            $highlighting = 'false';
        }

        $job_payload = [
            'user_id' => $user_id,
            'checked_all' => $isCheckAll ? 'yes' : 'no',
            'ids' => $products_id,
            'ext' => $file_ext,
            'search_by_field_column' => $search_by_field_column,
            'q' => $q,
            'highlighting' => $highlighting,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        $payload_id = DB::table('comparison_export_requests')->insertGetId([
            'user_id' => $user_id,
            'payload' => json_encode($job_payload),
        ]);

        ComparisonExportJob::dispatch($payload_id);

        return response()->json([
            'success' => true,
            'message' => __('Process running on background'),
        ]);
    }
}
