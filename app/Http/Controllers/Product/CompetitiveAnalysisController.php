<?php
namespace App\Http\Controllers\Product;

use App\Shop;
use App\User;
use DateTime;
use Exception;
use Carbon\Carbon;
use App\DrmProduct;
use App\ChannelColor;
use App\Enums\Channel;
use League\Csv\Reader;
use App\DeliveryCompany;
use App\MpSalesAnalysis;
use App\ProductPriceApi;
use App\AnalysisCategory;
use App\Enums\CreditType;
use App\Helper\DrmHelper;
use App\Models\DrmCategory;
use Illuminate\Support\Str;
use App\ChannelPriceHistory;
use App\Jobs\DestroyProduct;
use App\MarketplaceProducts;
use Illuminate\Http\Request;
use App\DropmatixProductBrand;
use App\Models\ChannelProduct;
use App\Jobs\ProcessCPTransfer;
use App\Enums\CompetitiveSource;
use App\Jobs\CpProductUpdateJob;
use App\ProductSalesEstimateApi;
use Illuminate\Support\Facades\DB;

use App\Services\DRMProductService;



use App\Models\Marketplace\Category;

use Illuminate\Support\Facades\Cache;
use App\Models\Product\AnalysisProduct;

use App\Services\ChannelProductService;

use App\Services\Keepa\Api as KeepaAPI;
use App\Services\ProductApi\ProductApi;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use App\Jobs\ProcessCPTransferToCustomer;
use App\Models\Product\CPAnalysisRequest;
use App\Models\Product\CPGoogleTrendData;
use App\Services\ProductApi\SalesEstmnApi;
use App\Services\DropCampus\DropmatixCampus;
use App\Services\DropCampus\ExpertiseCampus;
use App\Models\Product\AnalysisBuyingChoices;
use App\Services\DropCampus\DroptiendaCampus;
use App\Jobs\ProcessTransferToCPBuyingChoices;
use App\Models\Product\CPAnalysisUserRequests;
use App\Jobs\ProcessCPToCoreTransferToCustomer;
use App\Models\Product\StoreleadDataCollection;
use App\Services\ProductApi\Services\Countdown;
use App\Services\ProductApi\Services\Rainforest;
use App\Models\Product\ComparisonAnalysisProduct;
use App\Http\Controllers\AdminDrmImportsController;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Services\ProductApi\Services\GoogleShopping;
use App\Services\ProductApi\TransferProduct; //Transfer product to API
use App\Services\Tariff\Credit\ChargeCredit;

class CompetitiveAnalysisController
{

    private array $analysisService = [
        Countdown::class,
		Rainforest::class,
        GoogleShopping::class,
	];
    private function isImportPlanActive($user_id) {
        $importPlan = DB::table('purchase_import_plans')->where('cms_user_id', $user_id)->first();
        $importPlanId = $importPlan ? $importPlan->import_plan_id : null;
        if(in_array($importPlanId, [25, 26, 27])) return true;
        $planDetails = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
        $planName = $planDetails['plan'];

        // Check if the user has an active import plan.
        return in_array($planName, ['Free', 'Trial']);
    }

    private function getProductBrands($analysis_products, $user_id) {
        $brands = $analysis_products->map(function ($item) {
            $item->brand_name = $item->brand;
            return $item;
        });

        $coreIds = $brands->unique('brand')->where('source', '!=', 2)->pluck('brand')->filter(function ($item) {
            return is_numeric($item) && is_int($item + 0);
        });

        $mpIds = $brands->unique('brand')->where('source', 2)->pluck('brand')->filter(function ($item) {
            return is_numeric($item) && is_int($item + 0);
        });

        // return Cache::remember('product_brands_' . $user_id, 5, function () use ($coreIds, $mpIds, $brands) {
            if ($coreIds->count()) {
                $coreBrands = DB::table('dropmatix_product_brands')
                    ->whereIn('id', $coreIds)
                    ->get(['id', 'brand_name']);

                foreach ($coreBrands as $brand) {
                    $brands->where('brand', $brand->id)->each(function ($cBrand) use ($brand) {
                        $cBrand->brand_name = $brand->brand_name;
                    });
                }
            }

            if ($mpIds->count()) {
                $mpBrands = DB::connection('marketplace')->table('marketplace_product_brand')
                    ->whereIn('id', $mpIds)
                    ->get(['id', 'brand_name']);

                foreach ($mpBrands as $brand) {
                    $brands->where('brand', $brand->id)->each(function ($item) use ($brand) {
                        $item->brand_name = $brand->brand_name;
                    });
                }
            }
            return $brands;
        // });
    }


	public function index(Request $request)
	{
        $user_id = CRUDBooster::myParentId();

        if(checkTariffEligibility($user_id) && //Check eligibility
            !$this->isImportPlanActive($user_id) && //Check plan status
            !CRUDBooster::isDropmatixSupport()
        ) {
            return  abort(403);
        }

        $archive_filter     = $request->get('archive')??0;
        $search_by_channel  = $request->get('search_by_channel');
        $search_by_merchant = $request->get('search_by_merchant');
        if(isset($request->search_by_category)) $search_by_merchant = $request->search_by_category;
        $search_by_brand    = $request->get('search_by_brand');
        $search_by_supplier = $request->get('search_by_supplier');
        $search_by_source   = $request->get('search_by_source');
        $search_by_amazon_seller = $request->get('search_by_amazon_seller');
        $sort_by_sale       = $request->get('sort_by_sale');
        $search_by_rating   = $request->get('search_by_rating');
        $search_by_field    = $request->get('search_by_field_column');
        $sort_filter = $request->get('filter_column', []);
        $profit_column = $_REQUEST['compare_type'] ?? 'uvp';

        $add_column_position = 11;  // channel columns add to 11th position of analysis table.

        $orderBy = null;
        $sorting = null;

        if (!empty($sortFilter)) {
            $orderBy = key($sortFilter);
            $sorting = $sortFilter[$orderBy]['sorting'];
        }

        $filters = $request->all();

        $sortable = [
            'id',                                // Product ID
            'title',                             // Product Title
            'ean',                               // EAN Code
            'brand',                             // Brand
            'supplier',                          // Supplier
            'source_name',                       // Source Name
            'category_id',                       // Category ID
            'purchase_price',                    // Purchase Price
            'vk_price',                          // VK Price
            'uvp',                               // UVP (Unverbindliche Preisempfehlung)
            'google_price',                      // Google Price
            'ebay_price',                        // eBay Price
            'amazon_price',                      // Amazon Price
            'rating',                            // Product Rating
            'min',                               // Minimum Value
            'max',                               // Maximum Value
            'cv',                                // CV (Coefficient of Variation)
            'profit',                            // Profit
            'amazon_monthly_sales_estimate',     // Amazon Monthly Sales Estimate
            'amazon_weekly_sales_estimate',      // Amazon Weekly Sales Estimate
            'amazon_weekly_sales_drop',          // Amazon Weekly Sales Drop
            'amazon_monthly_sales_drop',         // Amazon Monthly Sales Drop
            'ebay_product_sold',                 // eBay Products Sold
            'ebay_weekly_sales_drop'             // eBay Weekly Sales Drop

            // 'sales_rank_30',
            // 'sales_rank_90',
            // 'sales_rank_180'
        ];


        $data['page_title'] = 'Competitive analysis';

        Cache::forget('saved_columns_' . $user_id);
        $saved_column = Cache::rememberForever('saved_columns_' . $user_id, fn () =>
            json_decode(DB::table('drm_user_saved_columns')
            ->where('user_id', $user_id)
            ->where('table_name', 'analysis_products')
            ->value('columns'))
        ) ?? null;

        $productsQuery = AnalysisProduct::where('user_id', $user_id);

        $anaProducts = $productsQuery->select('id','brand', 'supplier', 'category_id', 'price', 'source', 'amazon_seller_name', 'amazon_other_sellers', 'check24_price', 'kaufland_price')
            ->get();
//        $analysesProducts = $anaProducts->unique('brand');

        $data['amazon_sellers'] = array();
        $brands = $this->getProductBrands($anaProducts, $user_id);

        $data['suppliers'] = array();

        foreach ($anaProducts as $product) {
            $data['vk_max_price'] = max((float)$product['price'], $data['vk_max_price']);
            $data['suppliers'] = array_merge($data['suppliers'], array_filter([$product['supplier']]));
            $data['check24_price'] = $data['check24_price'] || !is_null($product['check24_price']);
            $data['kaufland_price'] = $data['kaufland_price'] || !is_null($product['kaufland_price']);
        }
         $data['amazon_sellers'] = $anaProducts->where('archived', $archive_filter)
            ->flatMap(function ($seller) {
                $sellerArray = json_decode($seller->amazon_other_sellers, true) ?? [];

                return array_merge([$seller->amazon_seller_name], array_column($sellerArray, 'seller_name'));
            })
            ->filter()
            ->unique()
            ->values()
            ->sort(function ($a, $b) {
                return strnatcasecmp($a, $b);
            })
            ->all();

        $data['lang'] = app('App\Services\UserService')->getProductLanguage(app('App\Services\UserService')->getProductCountry($user_id));
        $channels_list = Shop::with('color:gambio_id,color')
            ->where('user_id', $user_id)
            ->where('status', 1)
            ->where('lang', $data['lang'])
            ->get(['id','channel','shop_name']);

        $new_fields = [];
        $google = $ebay = $amazon = $include_shipping_cost = false;

        if($saved_column){
            if(!is_array($saved_column)){
                $saved_column = json_decode($saved_column,true) ?? array();
            }
        }
        if (!empty($saved_column) && in_array('shipping_cost', $saved_column)) {
            $include_shipping_cost = true;
        }

        $channelNames = [];

        $query = "";
        foreach($channels_list as $channel){
            if(($channel->channel == Channel::CHECK24 && $data['check24_activated'] == 0) || ($channel->channel == Channel::KAUFLAND && $data['kaufland_activated'] == 0)){
                continue;
            }
            $pId                        = getChannelName($channel->channel)."_id";
            $channelName                = getChannelName($channel->channel)."_channel_price";
            $channelNameEk              = getChannelName($channel->channel)."_channel_price_Ek";
            $channelNames[$channelName] = getChannelName($channel->channel);
            $channelShippingCost        = getChannelName($channel->channel)."_channel_shipping_cost";
            $sortable[]                 = getChannelName($channel->channel)."_channel_price";
            // $channel_prices[] = getChannelName($channel->channel)."_channel_price";

            $query.="(SELECT(`id`) FROM channel_products WHERE `ean` = analysis_products.ean AND `user_id` = $user_id AND `channel` = $channel->channel Limit 1) AS `$pId`,";
            $query.="(SELECT(`vk_price`) FROM channel_products WHERE `ean` = analysis_products.ean AND `user_id` = $user_id AND `channel` = $channel->channel Limit 1) AS `$channelName`,";
            $query.="(SELECT(`ek_price`) FROM channel_products WHERE `ean` = analysis_products.ean AND `user_id` = $user_id AND `channel` = $channel->channel Limit 1) AS `$channelNameEk`,";
            $query.="(SELECT(`shipping_cost`) FROM channel_products WHERE `ean` = analysis_products.ean AND `user_id` = $user_id AND `channel` = $channel->channel Limit 1) AS `$channelShippingCost`,";

             // TODO:: PRODUCT_TABLE_CHANGE product_stock_table
            $query.="(SELECT(`stock`) FROM drm_products WHERE `ean` = analysis_products.ean AND `user_id` = $user_id Limit 1 ) AS `drm_stock`,";

            if($include_shipping_cost){
                ${"profit_column_query_" . $channelName}.="(SELECT(`vk_price` + `shipping_cost`) FROM channel_products WHERE `ean` = analysis_products.ean AND `user_id` = $user_id AND `channel` = $channel->channel)";
                $query.= "(SELECT(`$channelName` + `$channelShippingCost`)) AS `$channelName`,";
            }
            else{
                ${"profit_column_query_" . $channelName} = "(SELECT(`vk_price`) FROM channel_products WHERE `ean` = analysis_products.ean AND `user_id` = $user_id AND `channel` = $channel->channel)";
            }

            if($channel->channel != '201' && $channel->channel != '200') {
                if($channel->channel == '4'){
                    $ebay = true;
                }
                if($channel->channel == '5'){
                   $amazon = true;
                }
                $channel_name = getChannelName($channel->channel);
                $channel_name_with_index = $channel_name . "_" . $channel->id;
                $channel_logo = getChannelLogo($channel->channel);
                $new_fields[$channel_name_with_index] = ["label" => $channel->shop_name,"logo" => $channel_logo , "sorting" => true, "channel" => true, "id" => $channel->id];
             }
        }

        $query.= "(CASE WHEN analysis_products.source = 0 THEN 'Import' WHEN analysis_products.source = 1 THEN 'Stock Transfer' WHEN analysis_products.source = 2 THEN 'MarketPlace Transfer' WHEN analysis_products.source = 3 THEN 'Supplier' WHEN analysis_products.source = 4 THEN 'Manual Add' END) AS `source_name`,";
        $prices = "(SELECT(`ebay_price`)) AS `ebay_price`,(SELECT(`amazon_price`)) AS `amazon_price`,(SELECT(`google_price`)) AS `google_price`";
        $min_price = "(COALESCE(least(COALESCE(`google_price`, `ebay_price`, `amazon_price`, `check24_price`, `kaufland_price`), COALESCE(`ebay_price`, `amazon_price`,`google_price`, `check24_price`,`kaufland_price`), COALESCE(`amazon_price`,`google_price`, `ebay_price`, `check24_price`,`kaufland_price`), COALESCE(`check24_price`, `google_price`, `ebay_price`, `amazon_price`, `kaufland_price`), COALESCE(`kaufland_price`, `google_price`, `ebay_price`, `amazon_price`, `check24_price`)), 0)) AS `min`";
        $max_price = "(SELECT(greatest(Coalesce(`google_price`, 0), Coalesce(`ebay_price`, 0), Coalesce(`amazon_price`, 0), Coalesce(`check24_price`, 0), Coalesce(`kaufland_price`, 0)))) AS `max`";
        $cv = "(SELECT(((Coalesce(`google_price`,0) + Coalesce(`ebay_price`,0) + Coalesce(`amazon_price`,0) + Coalesce(`check24_price`, 0) + Coalesce(`kaufland_price`, 0)) / NULLIF((Coalesce(`google_price`/`google_price`, 0) + Coalesce(`ebay_price`/`ebay_price`, 0) + Coalesce(`amazon_price`/`amazon_price`, 0) + Coalesce(`check24_price`/`check24_price`, 0) + Coalesce(`kaufland_price`/`kaufland_price`, 0)), 0)))) AS `cv`";
        // $profit_column_query_min = "(COALESCE(least(COALESCE(`google_price`, `ebay_price`, `amazon_price`, `check24_price`, `kaufland_price`), COALESCE(`ebay_price`, `amazon_price`,`google_price`, `check24_price`,`kaufland_price`), COALESCE(`amazon_price`,`google_price`, `ebay_price`, `check24_price`,`kaufland_price`), COALESCE(`check24_price`, `google_price`, `ebay_price`, `amazon_price`, `kaufland_price`), COALESCE(`kaufland_price`, `google_price`, `ebay_price`, `amazon_price`, `check24_price`)), 0))";
        // $profit_column_query_max = "(SELECT(greatest(Coalesce(`google_price`, 0), Coalesce(`ebay_price`, 0), Coalesce(`amazon_price`, 0), Coalesce(`check24_price`, 0), Coalesce(`kaufland_price`, 0))))";
        // $profit_column_query_cv = "(SELECT(((Coalesce(`google_price`,0) + Coalesce(`ebay_price`,0) + Coalesce(`amazon_price`,0) + Coalesce(`check24_price`, 0) + Coalesce(`kaufland_price`, 0)) / NULLIF((Coalesce(`google_price`/`google_price`, 0) + Coalesce(`ebay_price`/`ebay_price`, 0) + Coalesce(`amazon_price`/`amazon_price`, 0) + Coalesce(`check24_price`/`check24_price`, 0) + Coalesce(`kaufland_price`/`kaufland_price`, 0)), 0))))";
        // $profit_column_query_amazon_price = "`amazon_price`";
        // $profit_column_query_ebay_price = "`ebay_price`";
        // $profit_column_query_google_price = "`google_price`";
        // $profit_column_query_uvp = "`uvp`";
        // $profit_column_query_price = "`purchase_price`";
        $shipping_cost_column = '';

        if($include_shipping_cost){
            $prices = "(SELECT(`ebay_price` + COALESCE(`ebay_shipping_cost`, 0))) AS `ebay_price`,(SELECT(`amazon_price` + COALESCE(`amazon_shipping_cost`, 0))) AS `amazon_price`,(SELECT(`google_price` + COALESCE(`google_shipping_cost`, 0))) AS `google_price`";
            $min_price = "(COALESCE(least(
                COALESCE(Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)), Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`check24_price`), Coalesce(`kaufland_price`)),
                COALESCE(Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)),Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`check24_price`), Coalesce(`kaufland_price`)),
                COALESCE(Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)), Coalesce(`check24_price`), Coalesce(`kaufland_price`)),
                COALESCE(Coalesce(`check24_price`), Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)),Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`kaufland_price`)),
                COALESCE(Coalesce(`kaufland_price`), Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)),Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`check24_price`))), 0)
            ) AS `min`";
            $max_price = "(SELECT(greatest(Coalesce(`google_price`, 0) + Coalesce(`google_shipping_cost`, 0), Coalesce(`ebay_price`, 0) + Coalesce(`ebay_shipping_cost`, 0), Coalesce(`amazon_price`, 0) + Coalesce(`amazon_shipping_cost`, 0), Coalesce(`check24_price`, 0), Coalesce(`kaufland_price`, 0)))) AS `max`";
            $cv = "(SELECT((((Coalesce(`google_price`,0) + Coalesce(`google_shipping_cost`, 0)) + (Coalesce(`ebay_price`,0) + Coalesce(`ebay_shipping_cost`, 0)) + (Coalesce(`amazon_price`,0) + Coalesce(`amazon_shipping_cost`, 0) + Coalesce(`check24_price`, 0) + Coalesce(`kaufland_price`, 0))) / NULLIF((Coalesce(`google_price`/`google_price`, 0) + Coalesce(`ebay_price`/`ebay_price`, 0) + Coalesce(`amazon_price`/`amazon_price`, 0) + Coalesce(`check24_price`/`check24_price`, 0) + Coalesce(`kaufland_price`/`kaufland_price`, 0)), 0)))) AS `cv`";

            // $profit_column_query_min = "(COALESCE(least(
            //         COALESCE(Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)), Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`check24_price`), Coalesce(`kaufland_price`)),
            //         COALESCE(Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)),Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`check24_price`), Coalesce(`kaufland_price`)),
            //         COALESCE(Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)), Coalesce(`check24_price`), Coalesce(`kaufland_price`)),
            //         COALESCE(Coalesce(`check24_price`), Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)),Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`kaufland_price`)),
            //         COALESCE(Coalesce(`kaufland_price`), Coalesce(`ebay_price`) + Coalesce(COALESCE(`ebay_shipping_cost`, 0)),Coalesce(`amazon_price`) + Coalesce(COALESCE(`amazon_shipping_cost`, 0)), Coalesce(`google_price`) + Coalesce(COALESCE(`google_shipping_cost`, 0)), Coalesce(`check24_price`))), 0)
            // )";

            // $profit_column_query_max = "(greatest(Coalesce(`google_price`, 0) + Coalesce(`google_shipping_cost`, 0), Coalesce(`ebay_price`, 0) + Coalesce(`ebay_shipping_cost`, 0), Coalesce(`amazon_price`, 0) + Coalesce(`amazon_shipping_cost`, 0), Coalesce(`check24_price`, 0), Coalesce(`kaufland_price`, 0)))";


            // $profit_column_query_cv = "(SELECT((((Coalesce(`google_price`,0) + Coalesce(`google_shipping_cost`, 0)) + (Coalesce(`ebay_price`,0) + Coalesce(`ebay_shipping_cost`, 0)) + (Coalesce(`amazon_price`,0) + Coalesce(`amazon_shipping_cost`, 0) + Coalesce(`check24_price`, 0) + Coalesce(`kaufland_price`, 0))) / NULLIF((Coalesce(`google_price`/`google_price`, 0) + Coalesce(`ebay_price`/`ebay_price`, 0) + Coalesce(`amazon_price`/`amazon_price`, 0) + Coalesce(`check24_price`/`check24_price`, 0) + Coalesce(`kaufland_price`/`kaufland_price`, 0)), 0))))";
            // $profit_column_query_amazon_price = "amazon_price + COALESCE(`amazon_shipping_cost`, 0)";
            // $profit_column_query_ebay_price = "ebay_price + COALESCE(`ebay_shipping_cost`, 0)";
            // $profit_column_query_google_price = "google_price + COALESCE(`google_shipping_cost`, 0)";

            switch ($profit_column) {
                case "google_price":
                    $shipping_cost_column = "+ COALESCE(`google_shipping_cost`, 0)";
                    break;
                case "ebay_price":
                    $shipping_cost_column = "+ COALESCE(`ebay_shipping_cost`, 0)";
                    break;
                case "amazon_price":
                    $shipping_cost_column = "+ COALESCE(`amazon_shipping_cost`, 0)";
                    break;
            }

        }
        // $products = AnalysisProduct::with('product.connected_products:drm_product_id,ean,title->de as product_title,vk_price,uvp,user_id,channel', 'analysiscategory:id,name,type,duration')
        $products = $productsQuery->select(DB::raw(" *,
            $prices,
            $min_price,
            $max_price,
            $cv,
            (SELECT(`sales_rank_30`)) AS `sales_rank_30`,
            (SELECT(`sales_rank_90`)) AS `sales_rank_90`,
            (SELECT(`sales_rank_180`)) AS `sales_rank_180`,
            (SELECT(`amazon_weekly_sales_estimate` * `amazon_price`)) AS `amazon_weekly_sales_drop`,
            (SELECT(`amazon_monthly_sales_estimate` * `amazon_price`)) AS `amazon_monthly_sales_drop`,
            (SELECT(`ebay_product_sold` * `ebay_price`)) AS `ebay_weekly_sales_drop`,
            $query
            (SELECT((((`$profit_column`$shipping_cost_column ) - NULLIF(`purchase_price`, 0)) * 100) / NULLIF(`purchase_price`, 0))) AS `profit`,
            (SELECT(`name`) FROM analysis_category WHERE(`id` = analysis_products.category_id)) AS `merchant`,
            (SELECT(`type`) FROM analysis_category WHERE(`id` = analysis_products.category_id)) AS `type`,
            (SELECT(`duration`) FROM analysis_category WHERE(`id` = analysis_products.category_id)) AS `duration`"));


        // Check if the search by channel is not empty
        if (!empty($search_by_channel)) {
            // Check if search_by_channel is 'all'
            if ($search_by_channel === 'all') {
                $products->whereHas('product', function ($p) {
                    $p->has('connected_products');
                });
            } else {
                // Apply filter based on the specific channel
                $products->whereHas('product.connected_products', function ($p) use ($search_by_channel) {
                    $p->where('channel', $search_by_channel);
                });
            }
        }


        // Apply merchant filter
        if (!empty($search_by_merchant)) {
            $products->where('category_id', $search_by_merchant);
        }

        // Apply category filter when 'select_filter' is set, 'search_by_category' is set, and merchant filter is 0
        if ($request->get('select_filter') && isset(request()->search_by_category) && $search_by_merchant == 0) {
            $products->whereNull('category_id');
        }

        // Apply brand filter
        if (!empty($search_by_brand)) {
            $products->where('brand', $search_by_brand);
        }

        // Apply supplier filter
        if (!empty($search_by_supplier)) {
            $products->where('supplier', $search_by_supplier);
        }

        // Apply source filter
        if(!empty($search_by_source) || $search_by_source == "0"){
            $products->where('source', $search_by_source);
            // if(isset(request()->search_by_category)) $products->where('category_id',request()->search_by_category);
        }

        // Apply VK price range filter
        if (!empty($filters['vk_range']) && $filters['select_filter'] == 'vk_price_section') {
            $vk_prices = explode('-', $filters['vk_range']);
            $products->whereBetween('price', [trim($vk_prices['0']), trim($vk_prices['1'])]);
        }

        // Apply Amazon Seller filter
        if (!empty($search_by_amazon_seller)) {
            $products->where(function ($builder) use ($search_by_amazon_seller) {
                $builder->whereJsonContains('amazon_other_sellers', ['seller_name' => $search_by_amazon_seller])
                    ->orWhere('amazon_seller_name', $search_by_amazon_seller);
            });
        }

        // Apply profit and competitor filters
        $compare_type = $filters['compare_type'];

        if ($filters['select_filter'] == 'margin_section' && $compare_type) {
            $competitorColumns = [
                'Check24_channel_price' => 'check24_price',
                'Kaufland_channel_price' => 'kaufland_price',
                'Ebay_channel_price' => 'ebay_price',
                'Amazon_channel_price' => 'amazon_price',
                'Droptienda_channel_price' => 'google_price',
            ];

            if (!empty($filters['range'])) {
                $profit_range = array_map('trim', explode('to', $filters['range']));

                $products->whereRaw('(((((' . ${"profit_column_query_" . $compare_type} . ' ) - NULLIF(`purchase_price`, 0)) * 100) / NULLIF(`purchase_price`, 0)) BETWEEN ? AND ?)', [
                    trim($profit_range['0']),
                    trim($profit_range['1']),
                ]);
                // $products->whereBetweenRaw('(((((' . ${"profit_column_query_" . $compare_type} . ' ) - NULLIF(`purchase_price`, 0)) * 100) / NULLIF(`purchase_price`, 0))', $profit_range);
            }

            if ($filters['non_profit'] == 'true') {
                $products->whereRaw('(((((' . ${"profit_column_query_" . $compare_type} . ' ) - NULLIF(`purchase_price`, 0)) * 100) / NULLIF(`purchase_price`, 0)) IS NULL )');
            }

            if ($filters['no_competitor'] == 'true' && array_key_exists($compare_type, $competitorColumns)) {
                $competitor_name = $competitorColumns[$compare_type];

                if ($competitor_name) {
                    $products->whereNull($competitor_name);
                }
            }
        }

        // Apply sorting by monthly sales estimate
        if(!empty($sort_by_sale)){
            $sorting = $sort_by_sale;
            $products->orderBy('amazon_monthly_sales_estimate', $sorting);
        }

        // Apply custom order by and sorting
        $order_by = ($order_by == "marchandise") ? "category_id" : $order_by;
        if ($order_by && $sorting && in_array($order_by, $sortable)) {
            $products->orderBy($order_by, $sorting);
        }

        // Apply search by field and filters
        if(!empty($search_by_field) && !empty($filters['q'])){
            $q = $filters['q'];
            if($search_by_field == 'title' || $search_by_field == 'all'){
                $q = "%$q%";
            }
            if($search_by_field != 'all'){
                $products->where($search_by_field, 'LIKE', $q);
            }else{
                $products->where(function($p) use ($q) {
                    $p->where('ean', 'LIKE', $q);
                    $p->orWhere('title', 'LIKE', "%$q%");
                    $p->orWhere('id', 'LIKE', $q);
                });
            }
        }

        // Apply search by rating filter
        if(!empty($search_by_rating)){
            $a = $search_by_rating + 1;
            $products->whereRaw("COALESCE(((Coalesce(`google_rating`,0) + Coalesce(`ebay_rating`,0) + Coalesce(`amazon_rating`,0)) /
            NULLIF((Coalesce(`google_rating`/`google_rating`, 0) + Coalesce(`ebay_rating`/`ebay_rating`, 0) + Coalesce(`amazon_rating`/`amazon_rating`, 0)), 0)), 0) >= $search_by_rating AND COALESCE(((Coalesce(`google_rating`,0) + Coalesce(`ebay_rating`,0) + Coalesce(`amazon_rating`,0)) /
            NULLIF((Coalesce(`google_rating`/`google_rating`, 0) + Coalesce(`ebay_rating`/`ebay_rating`, 0) + Coalesce(`amazon_rating`/`amazon_rating`, 0)), 0)), 0) < $a");
        }

        // Apply archived filter
		if($archive_filter && $archive_filter == 1) {
			$products->where('archived', '=', 1);
		}else {
			$products->where('archived', '<>', 1);
		}

        // Set the limit and paginate
        $limit = $request->get('limit') ?? 20;

        if(isset($_GET['test'])){
            dd($products->toSql(), $products->paginate($limit), $products->count());
        }

		$products = $products->orderBy('id','desc')->paginate($limit);

        // $channel_color = CHANNEL::COLOR;
        foreach($channels_list as $channel){
            $channel_color[$channel->id] = $channel->color;  //DB::table("channel_colors")->where('gambio_id', $channel->id)->select('gambio_id', 'color')->get();
        }

        $initial_table_column = $this->table_column();

        if($amazon == false && CRUDBooster::isSupplier() == false){
            $new_fields['amazon'] = ["label" => __('Amazon ').' '.__("Gross") , "sorting" => true, "channel" => false];
        }
        if($ebay == false && CRUDBooster::isSupplier() == false){
            $new_fields['ebay'] = ["label" => __('eBay').' '.__("Gross") , "sorting" => true, "channel" => false];
        }

        $table_column = array_merge(array_slice($initial_table_column, 0, $add_column_position), $new_fields, array_slice($initial_table_column, $add_column_position));

        $analysis_max_price = $anaProducts->max('price');

        $groupedProducts = $anaProducts->whereNotNull('category_id')->groupBy('source')->map(function ($items, $source) {
            if (in_array($source, [2,3])) {
                // Fetch data from Category model
                return Category::whereIn('id', $items->pluck('category_id')->unique())->pluck('name','id');
            } else {
                // Fetch data from DrmCategory model or any other logic
                return DrmCategory::whereIn('id', $items->pluck('category_id')->unique())->pluck('category_name as name','id');
            }
        });

        $thirtyDaysAgo = Carbon::now()->subDays(30)->toDateString();

        $productsSalesInformation = MarketplaceProducts::join('marketplace_product_sales_information', 'marketplace_product_sales_information.marketplace_product_id', '=', 'marketplace_products.id')
            ->whereIn('marketplace_products.ean', $products->getCollection()->pluck('ean')->toArray())
            ->where('marketplace_product_sales_information.created_at', '>=', $thirtyDaysAgo)
            ->select('marketplace_products.ean', 'sales_stock', 'sales_amount', 'marketplace_product_sales_information.created_at')
            ->get();

        $mp_stocks = MarketplaceProducts::select('ean','stock')
                ->whereIn('ean', $products->getCollection()->where('source',2)->pluck('ean')->toArray())
                ->orderBy('stock', 'desc')
                ->get();

		$product_collection = $products->getCollection()
            ->map(function($item) use ($channels_list, $channel_color, $brands, $productsSalesInformation, $mp_stocks, $include_shipping_cost, $groupedProducts) {
                $vk_avg = 0;
                $vk_total = 0;
                $channel_count = 0;

                foreach($channels_list as $channel){
                    $pId                = getChannelName($channel->channel)."_id";
                    $channelName        = getChannelName(($channel->channel))."_channel_price";
                    $channelNameEk      = getChannelName(($channel->channel))."_channel_price_Ek";
                    $ShippingCost       = getChannelName(($channel->channel))."_channel_shipping_cost";
                    $channelShippingCost = $item->$ShippingCost;
                    $channel_vk_price   = $item->$channelName + (($item->$channelName * 19) / 100);
                    $vk_total += $channel_vk_price;
                    $channel_count++;

                    $channel_prices[$channel->id] = ["id" => $item->$pId, "price" => $channel_vk_price, "ek_price" => $item->$channelNameEk, "shipping_cost" => $channelShippingCost, "color" => $channel_color[$channel->id]->color, "channel" => $channel->channel, 'shop_id' => $channel->id];
                    if($channel->channel == 10){
                        $dt_id = $item->$pId;
                        $dt_price = $channel_vk_price;
                        $dt_ek = $item->$channelNameEk;
                    }
                }

                if($channel_count != 0){
                    $vk_avg = $vk_total / $channel_count;
                }

                $item->google_weekly_sales_estimate = $item->google_weekly_sales_estimate ? $item->google_weekly_sales_estimate : 0;
                $item->google_monthly_sales_estimate = $item->google_monthly_sales_estimate ? $item->google_monthly_sales_estimate : 0;
                $item->ebay_weekly_sales_estimate = $item->ebay_weekly_sales_estimate ? $item->ebay_weekly_sales_estimate : 0;
                $item->ebay_monthly_sales_estimate = $item->ebay_monthly_sales_estimate ? $item->ebay_monthly_sales_estimate : 0;
                $item->amazon_weekly_sales_estimate = $item->amazon_weekly_sales_estimate ? $item->amazon_weekly_sales_estimate : 0;
                $item->amazon_monthly_sales_estimate = $item->amazon_monthly_sales_estimate ? $item->amazon_monthly_sales_estimate : 0;

                $sum = 0;
                $item->google_rating = $item->google_rating ? $item->google_rating : 0;
                $item->ebay_rating = $item->ebay_rating ? $item->ebay_rating : 0;
                $item->amazon_rating = $item->amazon_rating ? $item->amazon_rating : 0;
                $sum += $item->ebay_rating + $item->google_rating + $item->amazon_rating;
                $count = $this->checkNull($item->ebay_rating) + $this->checkNull($item->google_rating) + $this->checkNull($item->amazon_rating);
                if($count == 0)
                    $count = 1;
                $avg = $sum/$count;
                $item->rating_count = $item->ebay_rating_count + $item->amazon_rating_count + $item->google_rating_count;

                if(($item->price == 0 || $item->price == null) && $dt_price > 0){
                    $item->price = $dt_price;
                }

                if($item->source == 1){
                    $stock = $item->drm_stock;
                }elseif($item->source == 2){
                    // $mp_stock = \DB::connection('marketplace')->table('marketplace_products')
                    //     ->select('stock')
                    //     ->where('ean', $item->ean)
                    //     ->orderBy('stock', 'desc')
                    //     ->first();
                    // $stock = $mp_stock->stock;
                    $stock = $mp_stocks ? optional($mp_stocks->where('ean', $item->ean)->first())->stock : null;

                }else{
                    $stock = $item->availability;
                }

                $archived_at = new DateTime($item->archived_at ?? now());
                $archived_for = $archived_at->diff(now());
                $days = 30 - $archived_for->format('%a');
                if($days < 1){
                    $days = 1;
                }

                if($include_shipping_cost){
                    $price = $item->price + $item->drm_shipping_price;
                    $purchase_price = $item->purchase_price + $item->drm_shipping_price;
                    $shipping_icon = '<i class="fa fa-truck"></i>';
                }
                else{
                    $price = $item->price;
                    $purchase_price = $item->purchase_price;
                    $shipping_icon = '';
                }

                $sevenDaysAgo = Carbon::now()->subDays(7)->toDateString();
                // $thirtyDaysAgo = Carbon::now()->subDays(30)->toDateString();

                // $productsLast7Days = MarketplaceProducts::join('marketplace_product_sales_information','marketplace_product_sales_information.marketplace_product_id','=','marketplace_products.id')
                // ->where('marketplace_products.ean', $item->ean)
                // ->where('marketplace_product_sales_information.created_at', '>=', $sevenDaysAgo)
                // ->select('sales_stock','sales_amount')->get();

                // $productsLast30Days = MarketplaceProducts::join('marketplace_product_sales_information','marketplace_product_sales_information.marketplace_product_id','=','marketplace_products.id')
                // ->where('marketplace_products.ean', $item->ean)
                // ->where('marketplace_product_sales_information.created_at', '>=', $thirtyDaysAgo)
                // ->select('sales_stock','sales_amount')->get();

                // $productsSalesInformation = MarketplaceProducts::join('marketplace_product_sales_information','marketplace_product_sales_information.marketplace_product_id','=','marketplace_products.id')
                // ->where('marketplace_products.ean', $item->ean)
                // ->where('marketplace_product_sales_information.created_at', '>=', $thirtyDaysAgo)
                // ->select('sales_stock','sales_amount')->get();

                $productsLast7Days = $productsSalesInformation->where('ean', $item->ean)->filter(function ($product) use ($sevenDaysAgo) {
                    return $product->created_at >= $sevenDaysAgo;
                });

                $productsLast30Days = $productsSalesInformation->where('ean', $item->ean);
                if(request()->has('testscript')){
                    dump($productsSalesInformation->count()."-ean-".$item->ean."-7Ds-".
                    $this->roundUpToNearestFive($productsLast7Days->sum('sales_stock'))."-30Ds-".
                    $this->roundUpToNearestFive($productsLast7Days->sum('sales_stock'))."-30Ds-".
                    $this->roundUpToNearestFive($productsLast30Days->sum('sales_stock'))."-7DT-".
                    $productsLast7Days->sum('sales_amount')."-30DT-".
                    $productsLast30Days->sum('sales_amount'));
                }
                $category_name = $groupedProducts[$item->source][$item->category_id];

                $row  = [
                    'id' => $item->id,
                    'title' => $item->title,
                    'ean' => $item->ean,
                    'product_id' => $item->product_id,
                    'brand' => $brands->where('brand',$item->brand)->first()->brand_name,
                    'supplier' => $item->supplier,
                    'source' => $item->source,
                    'source_name' => $item->source_name,
                    'duration' => $item->duration,
                    'availability' => $stock ?? 0,
                    'merchant' => $item->merchant,
                    'type' => $item->type,
                    'ebay_id' => $item->ebay_id,
                    'amazon_id' => $item->amazon_id,
                    'google_id' => $item->google_id,
                    'ebay_price' => $item->ebay_price,
                    'ebay_shipping_cost' => $item->ebay_shipping_cost,
                    'google_price' => $item->google_price,
                    'google_shipping_cost' => $item->google_shipping_cost,
                    'amazon_price' => $item->amazon_price,
                    'amazon_shipping_cost' => $item->amazon_shipping_cost,
                    'ebay_rating' => $item->ebay_rating,
                    'google_rating' => $item->google_rating,
                    'amazon_rating' => $item->amazon_rating,
                    'ebay_weekly_sales_estimate' => $item->ebay_weekly_sales_estimate,
                    'google_weekly_sales_estimate' => $item->google_weekly_sales_estimate,
                    // 'amazon_weekly_sales_estimate' => $item->amazon_weekly_sales_estimate,
                    'ebay_monthly_sales_estimate' => $item->ebay_monthly_sales_estimate,
                    'google_monthly_sales_estimate' => $item->google_monthly_sales_estimate,
                    // 'amazon_monthly_sales_estimate' => $item->amazon_monthly_sales_estimate,
                    'sales_rank_30' => $item->sales_rank_30,
                    'sales_rank_90' => $item->sales_rank_90,
                    'sales_rank_180' => $item->sales_rank_180,
                    'rating' => $avg,
                    'rating_count' => $item->rating_count,
                    'ebay_last_sync' => $item->ebay_last_sync,
                    'amazon_last_sync' => $item->amazon_last_sync,
                    'google_last_sync' => $item->google_last_sync,
                    'image' => !empty($item->image) && is_array($item->image) ? $item->image[0] : asset('images/blank-product.png'),
                    'archived' => (bool)($item->archived == 1),
                    'vk_avg' => $vk_avg,
                    'price' => $price,
                    'uvp' => $item->uvp,
                    'purchase_price' => $purchase_price,
                    // 'color_html' => $color_html,
                    // 'channel' => $channel_type,
                    // 'channel_vk_price' => $channel_vk_price,
                    'channel_prices' => $channel_prices,
                    'min' => $item->min,
                    'cv' => $item->cv,
                    'max' => $item->max,
                    'dt_id' => $dt_id,
                    'dt_price' => $dt_price,
                    'dt_ek' => $dt_ek,
                    'profit' => $item->profit ?? 0,
                    'check24_price' => $item->check24_price,
                    'kaufland_price' => $item->kaufland_price,
                    'deleted_after' => $days,
                    'is_prime' => $item->is_prime,
                    'amazon_seller_name' => $item->amazon_seller_name,
                    'amazon_seller_link' => $item->amazon_seller_link,
                    'amazon_other_sellers' => json_decode($item->amazon_other_sellers),
                    'amazon_weekly_sales_estimate' => $item->amazon_weekly_sales_estimate,
                    'amazon_weekly_sales_drop' => $item->amazon_weekly_sales_drop,
                    'amazon_monthly_sales_estimate' => $item->amazon_monthly_sales_estimate,
                    'amazon_monthly_sales_drop' => $item->amazon_monthly_sales_drop,
                    'ebay_product_sold' => $item->ebay_product_sold,
                    'ebay_weekly_sales_drop' => $item->ebay_weekly_sales_drop,
                    'shipping_icon' => $shipping_icon,
                    'manual_update' => $item->manual_update,
                    'sales_7_days' => $item->manual_update,
                    'sales_30_days' => $item->manual_update,
                    'mp_sales_7' => $this->roundUpToNearestFive($productsLast7Days->sum('sales_stock')),
                    'mp_sales_30' => $this->roundUpToNearestFive($productsLast30Days->sum('sales_stock')),
                    'mp_sales_7_amount' => $productsLast7Days->sum('sales_amount'),
                    'mp_sales_30_amount' => $productsLast30Days->sum('sales_amount'),
                    'category_name' => $category_name
                ];
                // $row['profit'] = $this->profitCalculation($row);
                return $row;


			});

		$products->setCollection($product_collection);

        $total_product = $products->count();

        if($total_product){
            $total_ebay_product = $products->whereNotNull('ebay_price')->count();
            $total_amazon_product = $products->whereNotNull('amazon_price')->count();
            $total_google_product = $products->whereNotNull('google_price')->count();
            $data['percentage'] = [
                "ebay" => ($total_ebay_product * 100) / $total_product ?? 0,
                "amazon" => ($total_amazon_product * 100) / $total_product ?? 0,
                "google" => ($total_google_product * 100) / $total_product ?? 0
            ];
        }

        $data['saved_columns'] = $saved_column ??  array_keys($table_column);
        // $data['saved_columns'] = array_filter($data['saved_column'], fn($value) => !is_null($value) && $value !== 'null' && $value !== '');
		$data['interval_duration'] = DB::table('ca_intervals')->where('user_id',$user_id)->first();
        $data['channel_header'] = $data['interval_duration'] ? $data['interval_duration']->header_setting : 0;
        $data['brands'] = $brands->unique('brand_name');
        $data['products'] = $products;

		$data['product_ids'] = $products->pluck('id')->toArray();
		$data['archive'] = $archive_filter;
		$data['channels_list'] = $channels_list;
		$data['channel_color'] = $channel_color;
        $data['ebay'] = $ebay;
        $data['amazon'] = $amazon;

        $result = [];

        if( array_values( array_filter($data['saved_columns']) ) ){
            $result = array_intersect_key( $table_column , array_flip( array_filter($data['saved_columns']) ) );
        }

        if(empty($saved_column)){   // hide ID column by default
            unset($data['saved_columns']['0']);
            unset($result['id']);
        }

        $data['columns'] = $result;
        $data['left_fixed_col'] = $this->leftFixedColumnCount($data['saved_columns']);
        $data['right_fixed_col'] = $this->rightFixedColumnCount($data['saved_columns']);
        $data['all_columns'] = $table_column;

        $activeInterval = DB::table('cp_analysis_user_requests')->where('user_id',$user_id)->first();
        $data['analysis_category'] = AnalysisCategory::where('user_id', $user_id)->where('easy_pricing', 0)->get()->map(function($result)use($activeInterval){
            $result->isActive = ($result->id == $activeInterval->cat_id);
            return $result;
        });
//        $data['analysis_merchants'] = AnalysisProduct::with('analysiscategory:id,name,type,duration')->where('user_id', $user_id)->where('category_id','>',0)->groupBy('category_id')->get();
        // $data['brands'] = AnalysisProduct::where('user_id', $user_id)->whereNotNull('brand')->select('brand')->distinct('brand')->get();
        // $data['suppliers'] = AnalysisProduct::where('user_id', $user_id)->whereNotNull('supplier')->select('supplier')->groupBy('supplier')->get();
        $data['sources'] = $this->sources();

        $data['channels'] = config('channel.list');
        if (\crocodicstudio\crudbooster\helpers\CRUDBooster::myParentId() == 62) {
            $data['channels'][Channel::OTTO] = ['name' => 'Otto', 'type' => Channel::OTTO,];
        }

        $max_price = $products->max('max');
        $min_price = $products->where('min','>', 0)->min('min');
        $product_count = $products->count('id');
        $avg_price = ($product_count) ? $products->sum('cv') / $product_count : 0 ;
        $stock_product = $products->pluck('availability')->filter()->sum();
        $data['header'] = [
            'average' => $avg_price,
            'sv_with_best' => $stock_product * $min_price,
            'sv_with_expensive' => $stock_product * $max_price,
            'sv_with_avg' => $stock_product * $avg_price,
            'min' => $min_price,
            'max' =>  $max_price,
            'rank_30_sum' =>  $products->sum('sales_rank_30'),
            'rank_90_sum' =>  $products->sum('sales_rank_90'),
            'rank_180_sum' =>  $products->sum('sales_rank_180'),
        ];

        $data['rating'] = [
            "0 Star",
            "1 Star",
            "2 Star",
            "3 Star",
            "4 Star",
            "5 Star",
        ];

        $data['profit_filter_fields'] = $this->profitFilterFields($user_id, $channelNames);

        $data['isSupplier'] = CRUDBooster::isSupplier();

        $data['app_id'] = config('global.analysis_sales_rank_app_id');

        if(checkTariffEligibility($user_id)){
            $purchased_plan = DB::table('purchase_import_plans')->where('cms_user_id', $user_id)->first();
            if($purchased_plan->import_plan_id == 26 || $purchased_plan->import_plan_id == 27){
                $isAppPurchase = true;
                $isMpSales = true;
            }
            else{
                $plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
                if(in_array($plan['plan'], ['Free', 'Trial'])){
                    $isAppPurchase = true;
                    $isMpSales = true;
                }
                else{
                    $isAppPurchase = false;
                    $isMpSales = false;
                }
            }
        }
        else{
            $isAppPurchase = DrmUserHasPurchasedApp($user_id, $data['app_id']);
        }

        $data['isAppPurchase'] = $isAppPurchase;
        $data['isMpSales'] = $isMpSales;

        if (!CRUDBooster::isSuperadmin()) {
            // $data['shop'] = app(ChannelProductService::class)->getUserShops($user_id, $data['lang']);
            $data['shop'] = $channels_list;

            $transferable_channel = ['1','4','5','6','7','10'];
            $data['channels'] = array_values(collect($data['channels'])->whereIn('type', $data['shop']->pluck('channel'))->whereIn('type', $transferable_channel)->toArray());
        }

        // $data['vk_max'] = AnalysisProduct::where('user_id', $user_id)->max('price');
        $data['vk_max'] = $analysis_max_price;
        $data['min_profit'] = round($products->min('profit')) ?? 0;
        $data['max_profit'] = round($products->max('profit')) ?? 0;
        $data['user_id'] = $user_id;
        // $data['amazon_sellers'] = $amazon_sellers;
        $data['customer_list'] = DB::table('cms_users')
        ->where('id', '!=', $user_id)
        ->where('id_cms_privileges', '!=', 1)
        ->where('id_cms_privileges', '!=', 2)
        ->get(['id','name']);
        // $data['user_details'] = DB::table('cms_users')->where('id', $user_id)->first();

        unset($data['columns']["sales_rank_30"]);
        unset($data['columns']["sales_rank_90"]);
        unset($data['columns']["sales_rank_180"]);
        if (($key = array_search("sales_rank_30", $data['saved_columns'])) !== false) {
            unset($data['saved_columns'][$key]);
        }
        if (($key = array_search("sales_rank_90", $data['saved_columns'])) !== false) {
            unset($data['saved_columns'][$key]);
        }
        if (($key = array_search("sales_rank_180", $data['saved_columns'])) !== false) {
            unset($data['saved_columns'][$key]);
        }

        if (!$data['isAppPurchase'] && !CRUDBooster::isDropmatixSupport()) {
            unset($data['columns']["amazon_weekly_sales_estimate"]);
            unset($data['columns']["amazon_weekly_sales_drop"]);
            unset($data['columns']["amazon_monthly_sales_estimate"]);
            unset($data['columns']["amazon_monthly_sales_drop"]);
            unset($data['columns']["ebay_product_sold"]);
            unset($data['columns']["ebay_weekly_sales_drop"]);
            if (($key = array_search("amazon_weekly_sales_estimate", $data['saved_columns'])) !== false) {
                unset($data['saved_columns'][$key]);
            }
            if (($key = array_search("amazon_weekly_sales_drop", $data['saved_columns'])) !== false) {
                unset($data['saved_columns'][$key]);
            }
            if (($key = array_search("amazon_monthly_sales_estimate", $data['saved_columns'])) !== false) {
                unset($data['saved_columns'][$key]);
            }
            if (($key = array_search("amazon_monthly_sales_drop", $data['saved_columns'])) !== false) {
                unset($data['saved_columns'][$key]);
            }
            if (($key = array_search("ebay_product_sold", $data['saved_columns'])) !== false) {
                unset($data['saved_columns'][$key]);
            }
            if (($key = array_search("ebay_weekly_sales_drop", $data['saved_columns'])) !== false) {
                unset($data['saved_columns'][$key]);
            }
        }
        // return view('tmp', $data);
		return view('admin.cp_analysis.index', $data);
	}

    public function profitFilterFields($user_id, $channelNames){
        $filter_fields = [
            "uvp" => "UVP",
            "price" => __('own selling price'),
            "min" => __('Minimum price'),
            "max" => __('Maximum price'),
        ];
        foreach($channelNames as $channel => $title){
            if($channel == "check24_channel_price"){
                $filter_fields['check24_price'] = 'Check24';
            }
            elseif($channel == "kaufland_channel_price"){
                $filter_fields['kaufland_price'] = 'Kaufland';
            }
            else{
                $filter_fields[$channel] = $title;
            }
        }
        $extras = [
            // "ebay_price" => "eBay",
            // "amazon_price" => "Amazon",
            // "google_price" => "Google",
            'cv' => "ø"
        ];
        return array_merge($filter_fields, $extras);
    }

    public function sources()
    {
        return [
            '0' => 'Import' ,
            '1' => 'Stock Transfer',
            '2' => 'Marketplace Transfer',
            '3' => 'Supplier',
            '4' => 'Manual Add'
        ];
    }

    public function leftFixedColumnCount($columns)
    {
        $fixed_columns = [
            0 => 'id',
            1 => 'title',
            2 => 'brand',
            3 => 'supplier',
            4 => 'image',
            5 => 'purchase_price',
            6 => 'vk_avg',
            7 => 'uvp',
            8 => 'marchandise',
            9 => 'source',
        ];
        $result = array_intersect($columns, $fixed_columns);
        return count($result) + 1 ?? 0;
    }

    public function rightFixedColumnCount($columns)
    {
        $fixed_columns = [
            0 => 'rating',
            1 => 'min',
            2 => 'max',
            3 => 'profit',
            4 => 'availability',
            // 5 => 'sales_rank_30',
            // 6 => 'sales_rank_90',
            // 7 => 'sales_rank_180',
            // 5 => 'amazon_weekly_sales_estimate',
            // 6 => 'amazon_weekly_sales_drop',
            // 7 => 'amazon_monthly_sales_estimate',
            // 8 => 'amazon_monthly_sales_drop',
            // 9 => 'ebay_product_sold',
            // 10 => 'ebay_weekly_sales_drop'
        ];
        $result = array_intersect($columns, $fixed_columns);
        return count($result)  ?? 0;
    }


    private function profitCalculation($product)
    {
        // $price_arr = [
        //     $product['amazon_price'],
        //     $product['ebay_price'],
        //     $product['google_price']
        // ];

        $price_arr = [];
        if($product['amazon_price']){
            $price_arr[] = $product['amazon_price'];
        }
        if($product['ebay_price']){
            $price_arr[] = $product['ebay_price'];
        }
        if($product['google_price']){
            $price_arr[] = $product['google_price'];
        }

        $profit_column = $_REQUEST['compare_type'] ?? 'uvp';
        $x_val = $y_val = null;

        if ($profit_column === 'sell') {
            //$x_val = $product['product_id'] ? $product['vk_avg'] : $product['price'];
            $x_val = $product['price'] ?? 0;
        }else if($profit_column === 'min'){
            if(empty($price_arr)){
                $x_val = 0;
            }
            else{
                $x_val = min($price_arr);
            }

        }else if($profit_column === 'max'){
            if(empty($price_arr)){
                $x_val = 0;
            }
            else{
                $x_val = max($price_arr);
            }

        } else if ($profit_column === 'uvp') {
            $x_val = $product['uvp'] ?? 0;

        } else if ($profit_column === 'ebay') {
            $x_val = $product['ebay_price'] ?? 0;

        } else if ($profit_column === 'amazon') {
            $x_val = $product['amazon_price'] ?? 0;

        } else if ($profit_column === 'google') {
            $x_val = $product['google_price'] ?? 0;
        } else if ($profit_column === 'cv') {
            $x_val = $product['cv'] ?? 0;
        }

        $y_val = $product['purchase_price'];

        if($y_val)
        {
            $x_val == 0 ? $profit = 0 : $profit = $x_val - $y_val;
            return ($profit * 100) / $y_val;
        }
        return 0.00;
    }

    private function checkNull($obj){
        if($obj == null)
            return 0;
        else
            return 1;
    }

    public function priceUpdate(Request $request)
    {
        try {
            $price = $request->price;
            AnalysisProduct::where('id', $request->product_id)->update(
                [
                    $request->type => $price
                ]
            );

            if($request->type == 'purchase_price'){
                ComparisonAnalysisProduct::where('analysis_product_id', $request->product_id)->update([
                    'source_ek_price' => $price
                ]);
            }

            $class = "";
            if ($profit > 0) {
                $class = "green-profit";
            } elseif ($profit < 0) {
                $class = "red-profit";
            }
            $profit_html = "<span class=" . $class . ">" . number_format((float)$profit, 2, ',', '.') . "%</span>";

            return response()->json([
                'success' => true,
                'price' => number_format((float)$price, 2, ',', '.') . formatCurrency('EUR'),
                'base_price' => $price,
                'profit' => $profit_html,
                'message' => 'Update successfully!',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong!',
            ]);
        }

    }

    public function profilFilter($product_id, $filter){
        $product = AnalysisProduct::where('id',$product_id)
            ->select('ebay_price','amazon_price','google_price','purchase_price','price','uvp')
            ->first()
            ->toArray();
            $price_arr = [];

            if($product['amazon_price']){
                $price_arr[] = $product['amazon_price'];
            }
            if($product['ebay_price']){
                $price_arr[] = $product['ebay_price'];
            }
            if($product['google_price']){
                $price_arr[] = $product['google_price'];
            }

        $profit_column = $filter ?? 'uvp';
        $x_val = $y_val = null;

        if($profit_column === 'sell'){
            $x_val = $product['price'] ?? 0;
            $y_val = $product['purchase_price'];
        }else if($profit_column === 'min'){
            if(empty($price_arr)){
                $x_val = 0;
            }
            else{
                $x_val = min($price_arr);
            }
            $y_val = $product['purchase_price'];

        }else if($profit_column === 'max'){
            if(empty($price_arr)){
                $x_val = 0;
            }
            else{
                $x_val = max($price_arr);
            }
            $y_val = $product['purchase_price'];
        }else if($profit_column === 'uvp'){
            $x_val = $product['uvp'] ?? 0;
            $y_val = $product['purchase_price'];
        }

        if($y_val)
        {
            $profit = $x_val - $y_val;
            return ($profit * 100) / $y_val;
        }
        return 0.00;
    }

    public function checkProductExists(Request $request)
    {

        $exists_product = $transfer_product = 0;
        $product_ids = [];

        $AC_products = AnalysisProduct::where('user_id', CRUDBooster::myParentId())
            ->where('source', $request->source)
            ->where('archived', '<>', 1)
            ->select('id', 'ean', 'product_id', 'ebay_price', 'amazon_price', 'google_price');

        if($request->bulk_action == "false"){
            $AC_products->whereIn('product_id', $request->product_ids);
        }
        $AC_products = $AC_products->get();

        if ($request->channel == 4) {
            $exists_product = $AC_products->whereNotNull('ebay_price')->count('id');
            $transfer_product = $AC_products->whereNull('ebay_price')->count('id');
            $product_ids = $AC_products->whereNull('ebay_price')->pluck('product_id')->toArray();
        } elseif ($request->channel == 5) {
            $exists_product = $AC_products->whereNotNull('amazon_price')->count('id');
            $transfer_product = $AC_products->whereNull('amazon_price')->count('id');
            $product_ids = $AC_products->whereNull('amazon_price')->pluck('product_id')->toArray();
        } elseif (in_array($request->channel, [1, 6, 7, 10])) {
            $exists_product = $AC_products->where('google_price', '!=', 0.00)->count('id');
            $transfer_product = $AC_products->where('google_price', 0.00)->count('id');
            $product_ids = $AC_products->where('google_price', 0.00)->pluck('product_id')->toArray();
        }

        $message = '<p>Seller existing product';
        $message .= ($exists_product > 1) ? 's ' : ' ';
        $message.= $exists_product . '</br>';
        $message .= 'Total Transferable product';
        $message .= ($transfer_product > 1) ? 's ' : ' ';
        $message .= $transfer_product . '</p>';
        return response()->json([
            'success' => true,
            'bulk_action' => $request->bulk_action,
            'product_ids' =>  $request->product_ids,
            'message' => $message
        ]);
    }

    public function getTransferProductIds($channel, $user_id, $source)
    {

        $AC_products = AnalysisProduct::where('user_id', $user_id)
            ->where('source', $source)
            ->where('archived', '<>', 1)
            ->select('id', 'ean', 'product_id', 'ebay_price', 'amazon_price', 'google_price')
            ->get();

        if ($channel == 4) {
            $product_ids = $AC_products->whereNull('ebay_price')->pluck('product_id')->toArray();
        } elseif ($channel == 5) {
            $product_ids = $AC_products->whereNull('amazon_price')->pluck('product_id')->toArray();
        } elseif (in_array($channel, [1, 6, 7, 10])) {
            $product_ids = $AC_products->where('google_price', 0.00)->pluck('product_id')->toArray();
        }

        return $product_ids ?? [];
    }


    public function getDRMPproductIds($user_id, $marketplace_product_ids)
    {

        // TODO:: PRODUCT_TABLE_CHANGE
        $product_ids = DrmProduct::where('user_id', $user_id)
            ->whereIn('marketplace_product_id', $marketplace_product_ids)
            ->pluck('id')
            ->toArray();
        return $product_ids ?? [];
    }


    public function marketPlaceToDRM(Request $request)
    {
        try {
            $user_id = CRUDBooster::myParentId();
            $productIds = $request->product_ids;

            if ($request->bulk_action == "true") {
                $AC_products = AnalysisProduct::where('user_id', $user_id)
                    ->where('source', $request->source)
                    ->where('archived', '<>', 1)
                    ->select('id', 'ean', 'product_id', 'ebay_price', 'amazon_price', 'google_price')
                    ->get();

                if ($request->channel == 4) {
                    $productIds = $AC_products->whereNull('ebay_price')->pluck('product_id')->toArray();
                } elseif ($request->channel == 5) {
                    $productIds = $AC_products->whereNull('amazon_price')->pluck('product_id')->toArray();
                } elseif (in_array($request->channel, [1, 6, 7, 10])) {
                    $productIds = $AC_products->where('google_price', 0.00)->pluck('product_id')->toArray();
                }
            }

            // TODO:: PRODUCT_TABLE_CHANGE
            $id_exists = DrmProduct::where('user_id', $user_id)->whereIntegerInRaw('marketplace_product_id', $productIds)
                ->pluck('marketplace_product_id')
                ->toArray();
            $drm_transferable_ids = array_diff($productIds, $id_exists);

            app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferAllFilteredProductsToDrm($drm_transferable_ids);


            // TODO:: PRODUCT_TABLE_CHANGE
            $transferable_ids = DrmProduct::where('user_id', $user_id)->whereIntegerInRaw('marketplace_product_id', $productIds)
                ->pluck('id')
                ->toArray();

            return response()->json([
                'success' => count($transferable_ids) > 0,
                'product_ids' => $request->product_ids,
                'message' =>  (count($transferable_ids) > 0) ? 'Product transfer to DRM successfully!' : 'Something went wrong'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong!'
            ]);
        }
    }



    public function archive($id){
        AnalysisProduct::where('id', $id)->update(['archived' => 1]);
        $product = AnalysisProduct::select('item_number', 'ebay_id', 'google_id', 'amazon_id')->where('id', $id)->get();
        $analysisService = $this->analysisService;
        foreach($analysisService as $service){
            $analysisApi = new $service;
            $collection_id_column = $analysisApi->collectionColumnName();
            $column_prefix = $analysisApi->columnPrefix();
            $column_name = $column_prefix."id";

            $current_collection_id = DB::table('c_p_analysis_requests')->select($collection_id_column)->where('id', $product[0]->$column_name)->get();

            $request_id = $analysisApi->findRequest($current_collection_id[0]->$collection_id_column, $product[0]->item_number);
            $response = $analysisApi->deleteRequest($current_collection_id[0]->$collection_id_column, $request_id);
        }
        return redirect()->back();
    }


    public function archive_undo($id){
        AnalysisProduct::where('id', $id)->update(['archived' => 0]);
        $product = AnalysisProduct::select('ean', 'item_number', 'ebay_id', 'google_id', 'amazon_id')->where('id', $id)->get();
        $analysisService = $this->analysisService;
        $product_detail['ean'] = $product[0]->ean;
        $product_detail['item_number'] = $product[0]->item_number;
        foreach($analysisService as $service){
            $analysisApi = new $service;
            $collection_id_column = $analysisApi->collectionColumnName();
            $column_prefix = $analysisApi->columnPrefix();
            $column_name = $column_prefix."id";

            $current_collection_id = DB::table('c_p_analysis_requests')->select($collection_id_column)->where('id', $product[0]->$column_name)->get();

            $res = $analysisApi->addProducts([$product_detail], $current_collection_id[0]->$collection_id_column);
        }
        return redirect()->back();
    }


    public function update_channel_color(Request $request){
        $colors = $request->input('colors');

        $update_color = [];
        foreach ($colors as $gambio_id => $color) {
            $update_color = [
                'gambio_id' => $gambio_id,
                'color' => $color
            ];
            ChannelColor::updateOrCreate(
                ['gambio_id' => $gambio_id],
                $update_color
            );
        }

        DB::table('ca_intervals')->where('user_id', CRUDBooster::myParentId())
            ->update([
                    'header_setting' => $request->header
                ]
            );
        return response()->json([
            'status' => 'success',
            'message' => 'Shop color updated successfully!'
        ]);
    }

     public function updateInterval(Request $request){
         $request->validate([
            'duration_type'=> 'required|in:hours,minutes,oneTime',
            'cp_minutes_value'=> 'nullable|required_if:duration_type,minutes|in:15,20,25,30,60',
            'cp_daily_value'=> 'nullable|required_if:duration_type,hours|array|min:1',
        ]);


        $req = $request->only(['duration_type', 'cp_minutes_value', 'cp_daily_value']);

        $interval_data = [];

        $value = null;

        if($req['duration_type'] === 'hours')
        {
            $interval_data['value'] = array_map('intval', $req['cp_daily_value']);
            $interval_data['interval'] = 'daily';

            $value = implode(',', $interval_data['value']);
        }

        if($req['duration_type'] === 'minutes')
        {
            $value = $interval_data['value'] = (int)$req['cp_minutes_value'];
            $interval_data['interval'] = 'minutes';
        }

        if($req['duration_type'] === 'oneTime')
        {
            $interval_data['interval'] = 'oneTime';
        }

        $user_id = CRUDBooster::myParentId();

        AnalysisCategory::where(['id' => $request->category_id, 'user_id' => $user_id])->Update([
            'duration' => $value,
            'type' => $req['duration_type']
        ]);

        try{
            app(TransferProduct::class)->updateInterval($user_id, $interval_data, $request->category_id);

            DB::table('ca_intervals')->updateOrInsert([
                'user_id' => $user_id
            ],[
                'duration' => $value,
                'type' =>  $req['duration_type'],
                'created_at' =>  now(),
                'updated_at' =>  now(),
            ]);


            return response()->json([
                'success' => true,
                'message' => 'Interval updated successfully!'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }


    }

    public function loadProgressBar(){

        $user_id = CRUDBooster::myParentId();
        $products = AnalysisProduct::where('user_id', $user_id)->get();
        $total_product = $products->count();
        $total_ebay_product = $products->whereNotNull('ebay_price')->count();
        $total_amazon_product = $products->whereNotNull('amazon_price')->count();
        $total_google_product = $products->whereNotNull('google_price')->count();
        $percentage_ebay = $percentage_amazon = $percentage_google = 0;
        if($total_product > 0){
            $percentage_ebay = ($total_ebay_product * 100) / $total_product ?? 0;
            $percentage_amazon = ($total_amazon_product * 100) / $total_product ?? 0;
            $percentage_google = ($total_google_product * 100) / $total_product ?? 0;
        }
        $progress_bar = '<div class="progress" style="margin-bottom: 5px;">
                                <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar" aria-valuenow="40" aria-valuemin="0" aria-valuemax="100" style="width: '.$percentage_ebay.'%">
                                 Ebay '.round($percentage_ebay, 2).'%
                                </div>
                             </div>

                            <div class="progress" style="margin-bottom: 5px;">
                                <div class="progress-bar progress-bar-info progress-bar-striped" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" style="width: '.$percentage_amazon.'%">
                                    Amazon '.round($percentage_amazon, 2).'%
                                </div>
                            </div>

                            <div class="progress" style="margin-bottom: 5px;">
                                <div class="progress-bar progress-bar-primary progress-bar-striped" role="progressbar" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100" style="width: '.$percentage_google.'%">
                                    Google Shopping '.round($percentage_google, 2).'%
                                </div>
                            </div>';

        return response()->json([
            'success' => true,
            'html' => $progress_bar
        ]);

       }



    public function api_data(Request $request){
        $request->validate([
            'ean'=> 'required',
            'source'=> 'required|in:amazon.de,ebay.de,google.de',
        ]);

        $domain = $request->source;
        $userId = $request->user_id;
        $domain_column = (strpos($domain, 'ebay') !== false) ? 'ebay' : 'google';
        $domain_column = (strpos($domain, 'amazon') !== false) ? 'amazon' : $domain_column;
        $price_column = $domain_column.'_price';
        $rating_column = $domain_column.'_rating';
        $rating_count_column = $domain_column.'_rating_count';
        $product_number_column = $domain_column.'_product_number';
        $last_sync_column = $domain_column.'_last_sync';
        $also_bought_column = $domain_column.'_also_bought';

        $productApi = new ProductApi($domain);
        $product = $productApi->search('ean', $request->ean)->fetch();





        $price = 0;
        $rating = 0;
        $isPrime = false;

        if($product->price() || $product->offerPrice() || $product->rating() || $product->productNumber() || $product->ratingCount() || $product->title() || $product->sellerName() || $product->sellerLink() || $product->isPrime() || $product->getOtherSeller()){

            if($product->price()){
                $price = $product->price();
            }

            if($product->title()){
                $title = $product->title();
            }

            // elseif($product->offerPrice()){
            //     $price = $product->price();
            // }

            if($product->rating()){
                $rating = $product->rating();
            }

            if($product->productNumber()){
                $productNumber = $product->productNumber();
            }

            if($product->ratingCount()){
                $ratingCount = $product->ratingCount();
            }

            if($product->image()){
                $image = $product->image();
            }

            if($product->sellerName()){
                $sellerName = $product->sellerName();
            }

            if($product->sellerLink()){
                $sellerLink = $product->sellerLink();
            }

            if($product->isPrime()){
                $isPrime = $product->isPrime();
            }

            if($product->getOtherSeller()){
                $otherSeller = $product->getOtherSeller();
            }
            // $price = (float)$price / 100;

            // if($domain_column === 'amazon')
            // {
            //     $price = $product->offerPrice();
            // }

            if($product->getPeopleAlsoBought()){
                $alsoBought = $product->getPeopleAlsoBought();
            }

            if($product->getProductSold()){
                $productSold = $product->getProductSold();
            }

            $analysis_product = AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->first();

            if($domain_column == 'amazon'){
                if(strlen($productNumber) > 0){
                    DB::table('amazon_asin_collections')->updateOrInsert(
                        [
                            'ean' => $request->ean,
                            'domain' => 3
                        ],
                        [
                            'asin' => $productNumber
                        ]
                    );

                    $productSalesApi = new ProductApi($domain);
                    $estimatedData = $productSalesApi->salesEstimate($productNumber, "sales_estimation")->fetchSalesEstimationFromASIN();

                    if($estimatedData->estimatedData()){
                        $estimatedData = $estimatedData->estimatedData();
                        if($estimatedData->has_sales_estimation == true){
                            $weekly_estimate = $estimatedData->weekly_sales_estimate;
                            $monthly_estimate = $estimatedData->monthly_sales_estimate;
                            AnalysisProduct::where('ean', $request->ean)->update([
                                'amazon_weekly_sales_estimate' => $weekly_estimate,
                                'amazon_monthly_sales_estimate' => $monthly_estimate
                            ]);
                        }
                    }
                }
                AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->update([
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    'amazon_seller_name' => $sellerName,
                    'amazon_seller_link' => $sellerLink,
                    'is_prime' => $isPrime,
                    'amazon_other_sellers' => $otherSeller,
                    $also_bought_column => $alsoBought,
                    $last_sync_column => now(),
                ]);
            }

            elseif($domain_column == 'ebay') {
                AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->update([
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    $last_sync_column => now(),
                    'ebay_product_sold' => $productSold
                ]);
            }
            else{
                AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->update([
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    $last_sync_column => now()
                ]);
            }

            if($analysis_product->image == null){
                AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->update([
                    'image' => json_encode([$image])
                ]);
            }
            if($analysis_product->title == null){
                AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->update([
                    'title' => $title
                ]);
            }
            $addPrices[] = [
                'ean' => $request->ean,
                'source' => $request->source,
                'title' => $title,
                'price' => $price,
                'rating' => $rating,
                'rating_count' => $ratingCount,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
            ProductPriceApi::insert($addPrices);
        }
        else{
            AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->update([
                $last_sync_column => now(),
            ]);
        }

        $this->updateTariffBalance($userId, 1);

        $response = [
            'ean' => $request->ean,
            'source' => $request->source,
            'price' => $price ? number_format((float)$price, 2, ',', '.') . formatCurrency('EUR') : false,
            'sync_time' => now(),
        ];
        return $response;
    }

    public function sales_estm_data(Request $request){
        $request->validate([
            'ean'=> 'required',
            'source'=> 'required|in:amazon.de,ebay.de,google.de',
        ]);

        $domain = $request->source;
        $domain_column = (strpos($domain, 'ebay') !== false) ? 'ebay' : 'google';
        $domain_column = (strpos($domain, 'amazon') !== false) ? 'amazon' : $domain_column;
        $weekly_sales_column = $domain_column.'_weekly_sales_estimate';
        $monthly_sales_column = $domain_column.'_monthly_sales_estimate';
        $last_sync_column = $domain_column.'_last_sync';
        $product_number_column = $domain_column.'_product_number';
        $userId = $request->user_id;

        $product_data = ProductSalesEstimateApi::select('weekly_sales_estimate', 'monthly_sales_estimate', 'created_at')->where('ean', $request->ean)->where('source', $request->source)->orderBy('id', 'desc')->first();

        $last_sync_date = new DateTime($product_data->created_at);
        $current_date = Carbon::now();
        $interval = $last_sync_date->diff($current_date)->format('%a');

        // if($product_data->$product_number_column == null && $domain == 'amazon.de'){
        //     $productApi = new ProductApi($domain);
        //     $product = $productApi->search('ean', $request->ean)->fetch();
        //     $product_data->$product_number_column = $product->productNumber();
        //     AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->update([
        //         $product_number_column => $product_data->$product_number_column,
        //     ]);
        // }

        if($product_data->created_at == null || ($interval > 7 && ($product_data->weekly_sales_estimate != null || $product_data->monthly_sales_estimate != null))){
            $product = AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->first();
            $salesApi = new SalesEstmnApi($domain);
            $salesEstmn = $salesApi->search('ean', $product->$product_number_column)->fetch();

            if(!empty($salesEstmn->salesEstm())){
                $sales = $salesEstmn->salesEstm();
                AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->update([
                    $weekly_sales_column => $sales['weekly'],
                    $monthly_sales_column => $sales['monthly'],
                    $last_sync_column => now(),
                ]);

                ProductSalesEstimateApi::create([
                    'ean' => $request->ean,
                    'source' => $request->source,
                    'weekly_sales_estimate' => $sales['weekly'],
                    'monthly_sales_estimate' => $sales['monthly'],
                    'created_at' => now(),
                    'updated_at' => now()
                ]);

                MarketplaceProducts::where('ean', $request->ean)->update([
                    'sales_estimate' => $sales['monthly'],
                ]);
            }
            else{
                AnalysisProduct::where('ean', $request->ean)->where('user_id', $userId)->update([
                    $last_sync_column => now(),
                ]);

                ProductSalesEstimateApi::create([
                    'ean' => $request->ean,
                    'source' => $request->source,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }

            $this->updateTariffBalance($userId, 1);

            $response = [
                'ean' => $request->ean,
                'source' => $request->source,
                'sales' => $sales ? number_format((float)$sales, 2, ',', '.') . formatCurrency('EUR') : false,
                'sync_time' => now(),
            ];
            return $response;
        }
        else{
            $response = [
                'ean' => $request->ean,
                'source' => $request->source,
                'price' => false,
                'message' => 'Sales estimation is updated',
                'sync_time' => now(),
            ];
            return $response;
        }
    }

    public function trend_data(Request $request){
        // $request->validate([
        //     'ean'=> 'required',
        //     'source'=> 'required|in:amazon.de,ebay.de,google.de',
        // ]);

        $available = false;
        $search = $request->get('search');

        $domain = "trends.de";
        $userId = CRUDBooster::myParentId();

        $all_trend_history = [];

        $productApi = new ProductApi($domain);
        $trend = $productApi->searchTrend($search, "shopping")->fetchTrend();

        $trends = [];

        if($trend->trendData()){
            $trends = $trend->trendData();
        }
        $trend_data = $trends->data;
        $trend_history = [];
        if(!empty($trend_data)){
            foreach($trend_data as $data){
                $trend_history[] = [
                    'x' => date('Y-m-d', strtotime($data->date_utc)),
                    'y' => $data->values[0]->value == null ? 0 : $data->values[0]->value,
                ];
            }
            $available = true;
        }

        $all_trend_history[] = [
            'name' => 'Shopping Trend',
            'data' => $trend_history
        ];

        $productApi = new ProductApi($domain);
        $trend = $productApi->searchTrend($search, "web")->fetchTrend();

        $trends = [];

        if($trend->trendData()){
            $trends = $trend->trendData();
        }

        $trend_data = [];
        $trend_data = $trends->data;
        $trend_history = [];
        if(!empty($trend_data)){
            foreach($trend_data as $data){
                $trend_history[] = [
                    'x' => date('Y-m-d', strtotime($data->date_utc)),
                    'y' => $data->values[0]->value == null ? 0 : $data->values[0]->value,
                ];
            }
            $available = true;
        }

        $all_trend_history[] = [
            'name' => 'Web Trend',
            'data' => $trend_history
        ];

        $all_trend_history[] = $available;

        if($search != null && $search != ""){
            $this->updateTariffBalance($userId, 2);
        }

        return response()->json($all_trend_history);
    }

    public function product_trend_data(Request $request){

        $available = false;
        $search = $request->get('search');
        $interval = $request->get('interval');

        $domain = "trends.de";
        $userId = CRUDBooster::myParentId();

        $all_trend_history = [];

        $stored_trend_history = CPGoogleTrendData::where('title', $search)->first();

        if($stored_trend_history){
            if($interval == null || $interval == 'oneTime'){
                return response()->json(json_decode($stored_trend_history->trend_data));
            }
            else{
                $day_diff = Carbon::parse($stored_trend_history->updated_at)->diffInDays(now());
                if($day_diff < 1){
                    return response()->json(json_decode($stored_trend_history->trend_data));
                }
            }
        }

        $productApi = new ProductApi($domain);
        $trend = $productApi->searchTrend($search, "shopping")->fetchTrend();

        $trends = [];

        if($trend->trendData()){
            $trends = $trend->trendData();
        }

        $trend_data = [];
        $trend_data = $trends->data;
        $trend_history = [];
        if(!empty($trend_data)){
            foreach($trend_data as $data){
                $trend_history[] = [
                    'x' => date('Y-m-d', strtotime($data->date_utc)),
                    'y' => $data->values[0]->value == null ? 0 : $data->values[0]->value,
                ];
            }
            $available = true;
        }

        $all_trend_history[] = [
            'name' => 'Shopping Trend',
            'data' => $trend_history
        ];

        $all_trend_history[] = $available;

        CPGoogleTrendData::updateOrInsert(
            ['title' => $search],
            [
                'trend_data' => json_encode($all_trend_history),
                'created_at' => now(),
                'updated_at' => now()
            ]
        );

        if($search != null && $search != ""){
            $this->updateTariffBalance($userId, 2);
        }

        return response()->json($all_trend_history);
    }


    public function import(Request $request){

        $userId = CRUDBooster::myParentId();

        $data = [];
        if ($request->get('file') && !$request->get('import')) {
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;

            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = [
                'ean',
                'purchase_price',
                'uvp',
                'image',
                'title',
                'item_number',
                'brand',
                'supplier',
                'availability',
            ];

            $labels = [
                0 => 'EAN',
                1 => __('Purchase Price'),
                2 => __('UVP'),
                3 => __('Image'),
                4 => __('Product Name'),
                5 => __('Item Number'),
                6 => __('brand'),
                7 => __('Supplier'),
                8 => __('Inventory'),
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        $data['page_title'] = 'Competitive analysis import';
        return view('admin.competitive_analysis.import', $data);
    }

    public function manual_import(Request $request){

        $userId = CRUDBooster::myParentId();

        $data = [];
        if ($request->get('file') && !$request->get('import')) {
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;

            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = [
                'ean',
                'purchase_price',
                'uvp',
                'image',
                'title',
                'item_number',
                'brand',
                'supplier',
                'availability',
            ];

            $labels = [
                0 => 'EAN',
                1 => __('Purchase Price'),
                2 => __('UVP'),
                3 => __('Image'),
                4 => __('Product Name'),
                5 => __('Item Number'),
                6 => __('brand'),
                7 => __('Supplier'),
                8 => __('Inventory'),
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        $data['brands'] = DropmatixProductBrand::where('user_id', $userId)->get();

        if ( \CRUDBooster::isCustomer() || \CRUDBooster::isDropmatixSupport() ) {
            $data['suppliers'] = DeliveryCompany::where('user_id', $userId)
                                ->where('email', '<>', \App\Enums\Marketplace\Others::TRANSFER_DELIVERY_COMPANY_EMAIL)
                                ->where('name', '<>', \App\Enums\Marketplace\Others::TRANSFER_DELIVERY_COMPANY_NAME)
                                ->get();
        } else {
            $data['suppliers'] = DeliveryCompany::where('user_id', $userId)->groupBy('email')->get();
        }

        $data['page_title'] = 'Competitive analysis import';
        return view('admin.competitive_analysis.manual_import', $data);
    }


    public function do_import_chunk(Request $request)
    {
        try{
            $file_md5 = md5($request->get('file'));

            if ($request->get('file') && $request->get('resume') == 1 && $request->get('action_type') != 0) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }

                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }

            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);

            $table_columns = [
                'ean',
                'purchase_price',
                'uvp',
                'image',
                'title',
                'item_number',
                'brand',
                'supplier',
                'availability',
            ];

            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $count = 0;

            $analyseCollection = [];
            foreach ($rows as $row) {
                $count++;
                if($request->get('action_type') != 0){
                    Cache::put('success_' . $file_md5, $count);
                }
                $row = (object) $row;
                foreach ($select_column as $csvColName => $val) {

                    $colname = $table_columns[$csvColName];
                    if ($row->$val == '') continue;

                    if($colname == 'image'){
                        $row->$val = explode(',', $row->$val);
                    }
                    if($colname == 'price' || $colname == 'purchase_price' || $colname == 'uvp'){
                        $row->$val = removeCommaPrice( $row->$val );
                    }

                    if($colname == 'ean'){
                        $row->$val = trim($row->$val);
                    }

                    $data[$colname] = $row->$val;
                    $data['source'] = '0';
                    // $data['user_id'] = $user_id;
                    // $data['created_at'] = Carbon::now();
                    // $data['updated_at'] = Carbon::now();
                }

                try {
                    $analyseCollection[$data['ean']] = $data;
                } catch (Exception $e) {
                    $e = (string) $e;
                    Cache::put('error_' . $file_md5, $e, 500);
                }
            }

            $exist_ean = AnalysisProduct::whereIn('ean', array_column($analyseCollection, 'ean'))->where('user_id', $user_id)->pluck('ean')->toArray();

            $existing_products = array_filter($analyseCollection,function ($key) use ($exist_ean) {
                return in_array($key, $exist_ean);
            }, ARRAY_FILTER_USE_KEY
            );

            $product_count = count($existing_products);

            if($product_count > 0 && $request->get('action_type') == 0){

                $product_view = $this->existing_products($existing_products);

                return response()->json(['status' => false,'html' => $product_view,  'message' => __('Product Already Exists')]);
            }
            $override = ($request->get('override') == "true") ? true : false;
            $res = app(TransferProduct::class)->transferCSVToAnalysis($user_id, $analyseCollection , $override);
            return response()->json(['status' => true, 'message' => $res['message']]);
        }catch (Exception $e) {
            return response()->json(['status' => false, 'message' => "Sorry, something went wrong"]);
        }

    }

    function roundUpToNearestFive($number) {
        if ($number % 5 == 3) {
            return $number + 2;
        } elseif ($number % 5 == 2) {
            return $number + 3;
        } else {
            return ceil($number / 5) * 5;
        }
    }

    public function existing_products($existing_products){
        $html = '<table class="table">
                    <thead>
                        <tr>
                        <th scope="col">#</th>
                        <th scope="col">'.__("Image").'</th>
                        <th scope="col">EAN</th>
                        <th scope="col">'.__("Product Name").'</th>
                        <th scope="col">'.__("Purchase Price").' </th>
                        <th scope="col">'.__("UVP").' </th>
                        <th scope="col">'.__("brand").'</th>
                        <th scope="col">'.__("Supplier").'</th>
                        <th scope="col">'.__("Inventory").'</th>
                        </tr>
                    </thead>
                    <tbody>';
        $count = 1;
        foreach($existing_products as $product){
            $html.='<tr>
                                <th scope="row">'.$count++.'</th>
                                <td><img height="80" width="100" src="'.$product['image']['0'].'"></td>
                                <td>'.$product['ean'].'</td>
                                <td>'.$product['title'].'</td>
                                <td>'.number_format((float)$product['purchase_price'], 2, ',', '.').' '.formatCurrency('EUR').'</td>
                                <td>'.number_format((float)$product['uvp'], 2, ',', '.').' '.formatCurrency('EUR').'</td>
                                <td>'.$product['brand'].'</td>
                                <td>'.$product['supplier'].'</td>
                                <td>'.$product['availability'].'</td>
                            </tr>
                        ';
        }
        $html.='</tbody>
                    </table>';
        return $html;
    }

    public function uploadCsv(Request $request){

        if ($request->hasFile('userfile')) {

            try{
                $request->validate([
                    'userfile' => 'required|mimetypes:text/csv,text/plain,application/csv,text/comma-separated-values,text/anytext,application/octet-stream,application/txt',
                ]);

                $file = $request->file('userfile');
                $ext = $file->getClientOriginalExtension();

                $filePath = 'uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
                Storage::makeDirectory($filePath);

                $filename = md5(Str::random(5)).'.'.$ext;
                $url_filename = '';
                if (Storage::putFileAs($filePath, $file, $filename)) {
                    $url_filename = $filePath.'/'.$filename;
                }
                $url = route('drm.competitive_analysis.import').'?file='.base64_encode($url_filename);

                return redirect($url);
            } catch(Exception $e){
                $url = route('drm.competitive_analysis.import');

                return CRUDBooster::redirect($url, "Please Upload a Valid File");
            }
        } else {
            return redirect()->back();
        }
    }

    public function upload_ean(Request $request){

        if ($request->has('ean')) {
            Cache::put('cp_manual_import_details', []);

            $ean = $request->ean;

            if( strlen($ean) >= 12 && strlen($ean) <= 13){
                $countdown = new Countdown();
                $details = $countdown->getProductDetails($ean);
                if($details == null){
                    $rainforest = new Rainforest();
                    $details = $rainforest->getProductDetails($ean);
                    if($details == null){
                        $googleshopping = new GoogleShopping();
                        $details = $googleshopping->getProductDetails($ean);
                    }
                }
                $url = route('drm.competitive_analysis.manual_import').'?ean='.$ean;
                Cache::put('cp_manual_import_details', $details);
                return redirect($url)->with(['details' => $details]);
            }
            else{
                return redirect()->back()->with(['len' => strlen($ean)]);
            }
        } else {
            return redirect()->back();
        }
    }

    public function insert_product(Request $request){
        $ean = $request->ean;
        $user_id = CRUDBooster::myParentId();
        $existing_id = DB::table('analysis_products')->select('id')->where('ean', $ean)->where('user_id', $user_id)->get();
        $url = route('drm.competitive_analysis.manual_import').'?ean='.$ean;
        if($existing_id[0]->id){
            $url = route('drm.competitive_analysis.manual_import').'?ean='.$ean;
            return redirect($url)->with(['success' => false]);
        }
        else{
            $url = route('drm.competitive_analysis.manual_import');
            return redirect($url)->with(['success' => true]);
        }
    }


    public function done_import(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Import Products"]);
        Session::put('select_column', $request->get('select_column'));

        return view('admin.competitive_analysis.import', $data);
    }


    /**
     * @deprecated
     */
	public function transferToAnalysis(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array|min:1',
        ],
        [
            'product_ids.*' => 'Please select at least one product',
        ]);

        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->input('product_ids');

        if( (int)$request->selected_all ){
            $product_ids = app('\App\Services\DRMProductService')->getSelectedIds($user_id,$request->params);
        }

        $EANS = DrmProduct::where('user_id', $user_id)->whereIn('id', $product_ids)->pluck('ean');
        $restoring_product_ids = AnalysisProduct::where('user_id', $user_id)
            ->whereIn('ean', $EANS)
            ->pluck('id')
            ->toArray();

        $this->transferArchived($user_id, $restoring_product_ids, 0,"false", "false");

        if (\DRM::hasV9Access()) {
            $res = app(TransferProduct::class)->transferToAnalysis($user_id, $product_ids);
        } else {
            $res = app(TransferProduct::class)->transferToAnalysisV7($user_id, $product_ids);
        }

        return response()->json($res);
    }

    public function manualTransferToAnalysis(Request $request)
    {
        $request->validate([
            'details' => 'required|array|min:1',
        ],
        [
            'details.*' => 'Please select at least one product',
        ]);

        $user_id = CRUDBooster::myParentId();
        $details = $request->input('details');

        if($details[7] == 'No Supplier Selected'){
            $details[7] = null;
        }

        if($details[6] == 'No Brand Selected'){
            $details[6] = null;
        }




        $res = app(TransferProduct::class)->manualTransferToAnalysis($user_id, $details);




        return response()->json($res);
    }



















    public function mpProductTransferToAnalysis(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array|min:1',
        ],
        [
            'product_ids.*' => 'Please select at least one product',
        ]);

        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->input('product_ids');

        $res = app(TransferProduct::class)->mpProductTransferToAnalysis($user_id, $product_ids);
        return response()->json($res);
    }


    public function saveCPCategory(Request $request)
    {
        $request->validate([
            'category_name' => 'required|regex:/^\S.*$/',
        ]);
        $user_id = CRUDBooster::myParentId();

        // $collection_interval_data = [
        //     "schedule_type" => "manual",
        // ];

        // $collection_body = [
        //     "name" => $request->category_name,
        //     "enabled" => True,
        //     "priority" => "normal",
        //     "notification_as_csv" => True,
        //     "notification_as_json" => True,
        //     "include_html" => 'False'
        // ];

        // $collection = array_merge($collection_body, $collection_interval_data);

        $id = AnalysisCategory::Create(
            [
                'user_id' => $user_id,
                'name' => $request->category_name,
                'duration' => 0,
                'type' => 'oneTime'
            ])->id;

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $id,
                'category_name' => $request->category_name,
                'type' => 'oneTime',
            ],
            'message' => 'Marchandise added successfully!'
        ]);
    }

    public function updateCPCategory(Request $request)
    {
        $request->validate([
            'category_name' => 'required|regex:/^\S.*$/',
        ]);
        $user_id = CRUDBooster::myParentId();
        AnalysisCategory::where(['id' => $request->id, 'user_id' => $user_id])->Update([
            'name' => $request->category_name
        ]);

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $request->id,
                'category_name' => $request->category_name,
            ],
            'message' => 'Marchandise Update successfully!'
        ]);
    }

    public function categoryCategory($id){ // Delete Interval
        if($id){
            // Delete folowwing category delete
            DB::table('cp_transfer_request')
            ->where('user_id', CRUDBooster::myParentId())
            ->where('payload->category_id', $id)
            ->delete();

            try{
                app(TransferProduct::class)->deleteIntervals($id, CRUDBooster::myParentId());

                AnalysisCategory::where(['id' => $id, 'user_id' => CRUDBooster::myParentId()])->delete();
                return response()->json([
                    'success' => true,
                    'message' => 'Marchandise deleted successfully!'
                ]);
            }catch(Exception $e){
                return response()->json(['success' => false, 'message' => $e]);
            }

        }
        return response()->json([
            'success' => false,
            'message' => 'Something went wrong!'
        ]);
    }

    public function transferToMerchant(Request $request){
        $product_id = $request->product_id;
        $category_id = $request->category_id;
        $product_ids = (explode(",",$product_id));
        $isCheckAll = $request->isCheckAll;
        $user_id = CRUDBooster::myParentId();

        $job_payload = [
            'checked_all' => $isCheckAll,
            'ids' => $product_id,
            'action' => 'merchant',
            'category_id' => $category_id,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        DB::table('cp_transfer_request')->insert([
            'user_id' => $user_id,
            'payload' => json_encode($job_payload),
        ]);

        AnalysisProduct::where('user_id', $user_id)
        ->when($isCheckAll == 'false', function ($query) use ($product_ids) {
            $query->whereIn('id', $product_ids);
        })->update(['category_id' => $category_id]);


        if(!isLocal()){
            ProcessCPTransfer::dispatch($user_id);
        }
        return [
            'success' => true,
            'message' => 'Product transfered successfully!'
        ];
    }

    public function checkEasyPricing(Request $request)
    {
        $product_ids = $request->product_ids;
        $user_id = CRUDBooster::myParentId();
        $easy_category = AnalysisCategory::select('id')->where('user_id', $user_id)->where('easy_pricing', 1)->get();
        $exists = AnalysisProduct::where('user_id', $user_id)
            ->whereIn('id', $product_ids)
            ->get();
        if(!$easy_category->isEmpty()){
            $exists_products = $exists->where('category_id', $easy_category->id)->count('id');
            $product_ids = $exists->where('category_id', '!=', $easy_category->id)->pluck('id')->toArray();
            $transfer_product = count($product_ids);
        }

        else{
            $exists_products = $exists->where('category_id', -1)->count('id');
            $product_ids = $exists->where('category_id', '!=', -1)->pluck('id')->toArray();
            $transfer_product = count($product_ids);
        }

        $easy_status = true;
        if ($exists_products > 0) {
            $easy_status = false;
            $message = '<p>Easy Pricing Products';
            $message .= ($exists_products > 1) ? 's ' : ' ';
            $message .= $exists_products . '</br>';
            $message .= 'Transferring is not possible</br></br>';
        }
        $message .= 'Merchant Transferable product';
        $message .= ($transfer_product > 1) ? 's ' : ' ';
        $message .= $transfer_product . '</p>';

        return response()->json([
            'success' => true,
            'easy_status' => $easy_status,
            'product_ids' => $product_ids,
            'message' => $message
        ]);
    }

    /**
     * @deprecated
     */
    public function checkDRMToAnalysis(Request $request){
        $product_ids = $request->product_ids;
        $user_id = CRUDBooster::myParentId();


        if( (int)$request->select_all_status ){
            $product_ids = app('\App\Services\DRMProductService')->all(array_merge($request->params ?? [], [
                'user_id' => !CRUDBooster::isSuperadmin() ? $user_id : '',
                'lang' => $data['lang'] ?? "de",
            ]), false);
        }

        $EANS = DrmProduct::where('user_id', $user_id)->whereIn('id', $product_ids)->pluck('ean');

        $analysis_products = AnalysisProduct::where('user_id', $user_id)
            ->select('id', 'product_id','archived')
            ->whereIn('ean', $EANS)
            ->get();

        $request_products = count($product_ids);
        $exist_products = $analysis_products->where('archived', 0)->count('id');

        $transfer_product = $request_products - ( $exist_products );

        $message  = '<p>Please confirm that we may transfer the selected products to the analysis. Transferable product';
        $message .= ($transfer_product > 1) ? 's ' : ' ';
        $message .= ($transfer_product > 0) ? $transfer_product : '0';

        if( $exist_products > 0 ){
            $message .= '<br><br>Analysis already exists '.$exist_products . ' product';
            $message .= ($exist_products > 1) ? 's ' : '';
            $message .= '<br><br></p>';
        }

        return response()->json([
            'success' => true,
            'select_all_status' => $request->select_all_status,
            'product_ids' => $product_ids,
            'message' => $message
        ]);

    }

    public function checkSyncProduct(Request $request)
    {

        $product_ids = $request->product_ids;
        $user_id = CRUDBooster::myParentId();
        $analysis_sync = DB::table('cms_users')->where('id', $user_id)->value('one_one_sync');

        $analysis_products = AnalysisProduct::where('user_id', $user_id)
            ->where('synced_product', 0)
            ->where('archived', 0)
            ->select('id', 'product_id');
        if ($request->isCheckAll == 'false') {
            $analysis_products->whereIn('id', $product_ids);
        }
        $analysis_products = $analysis_products->get();


        $product_ids = $analysis_products->pluck('id')->toArray();
        $drm_product_ids = $analysis_products->pluck('product_id')->toArray();

        $product_count = 0;
        if (!empty($drm_product_ids)) {

            // TODO:: PRODUCT_TABLE_CHANGE
            $product_count = DrmProduct::where('user_id', $user_id)->whereIn('id', $drm_product_ids)->count('id');
        }

        $transfer_product = count($product_ids);
        $exits_products = count($request->product_ids) - $transfer_product;


        if ($request->isCheckAll == 'true') {
            $exits_products = AnalysisProduct::where('user_id', $user_id)
                ->where('synced_product', 1)
                ->where('archived', 0)
                ->count('id');
        }

        $sync_status = true;
        if ($analysis_sync == 1 && $exits_products > 0) {
            $sync_status = false;
            $message = '<p>1:1 Sync Stock product';
            $message .= ($exits_products > 1) ? 's ' : ' ';
            $message .= $exits_products . '</br>';
            $message .= 'Archiving is done automatically when it is deleted from the stock or when the 1:1 sync. with the stock is cancelled</br></br>';
        }
        $message .= __('We start with the archiving of the selected').' <b>'.$transfer_product.'</b> '.__('products. Please note that we completely delete your archive after 30 days. You can restore the archived products at any time for the next 30 days.');

        if ($product_count > 0) {
            $message .= '<form style="text-align: left;">
                              <input id="product_delete_id" type="checkbox" style="display:inline;width:auto;height: 13px;margin: 4px 0 0 0;">
                              <b> '. __('Would you also like to permanently delete your products in stock and in the connected channels?').'</b>
                              <br>'.__('You have').' <b> ' . $product_count . ' </b> '.__('products available in both analysis and stock. We can irrevocably delete them. Activate this checkbox to irrevocably delete the products from the warehouse. Without activation, your products will only be archived in the analysis and will be irrevocably deleted in 30 days.').
                         '</form>';
        }

        return response()->json([
            'success' => true,
            'sync_status' => $sync_status,
            'product_ids' => $product_ids,
            'message' => $message
        ]);
    }

    public function transferToArchive(Request $request){
        if(!$request->user_id){
            $user_id = CRUDBooster::myParentId();
        }
        $product_ids = $request->product_ids;
        $type = $request->type;

        $this->transferArchived($user_id, $product_ids, $type, $request->isCheckAll, $request->isDelete);
        return response()->json([
            'success' => true,
            'message' => 'Product transfered successfully!'
        ]);
    }

    public function transferArchived($user_id, $product_ids, $type, $isCheckAll, $isDelete){
        $job_payload = [
            'checked_all' => $isCheckAll,
            'ids' => $product_ids,
            'action' => 'archive',
            'type' => $type,
            'created_at' => now(),
        ];

        $payload_id = DB::table('cp_transfer_request')->insertGetId([
            'user_id' => $user_id,
            'payload' => json_encode($job_payload),
        ]);

        $drm_product_ids = [];
        if($isCheckAll == 'false'){
            $drm_product_ids = AnalysisProduct::where('user_id', $user_id)
                ->whereIn('id', $product_ids)
                ->where('synced_product', 0)
                ->pluck('product_id')
                ->toArray();

            AnalysisProduct::where('user_id', $user_id)->whereIn('id', $product_ids)->update([
                'archived' => $type,
                'archived_at' => now(),
                'ebay_last_sync' => null,
                'amazon_last_sync' => null,
                'google_last_sync' => null
            ]);
        }
        else if($isCheckAll == 'true'){
            $drm_product_ids = AnalysisProduct::where('user_id', $user_id)
                ->where('synced_product', 0)
                ->pluck('product_id')
                ->toArray();

            AnalysisProduct::where('user_id', $user_id)->update([
                'archived' => $type,
                'archived_at' => now(),
                'ebay_last_sync' => null,
                'amazon_last_sync' => null,
                'google_last_sync' => null
            ]);
        }

        if(!isLocal()){
            if($isDelete == "true"){
                DestroyProduct::dispatch($drm_product_ids, $user_id);
            }
            else{
                ProcessCPTransfer::dispatch($user_id);
            }
        }
    }


    public function supplierProductTransferToAnalysis(Request $request)
    {
        $request->validate([
            'product_ids' => 'required|array|min:1',
        ],
        [
            'product_ids.*' => 'Please select at least one product',
        ]);

        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->input('product_ids');

        $all_selected_product = MarketplaceProducts::whereIn('id', $product_ids)->where('status', 1)->pluck('id')->toArray();

        if($all_selected_product){
            $product_ids = $all_selected_product;
        }else{
            return [
                'success' => true,
                'message' => "Please Select Approved Products Only",
            ];
        }

        $res = app(TransferProduct::class)->supplierProductTransferToAnalysis($user_id, $product_ids);
        return response()->json($res);
    }

   	public function details($id){

        $userId = CRUDBooster::myParentId();

        $data['analysis_product'] = $analysis_product = AnalysisProduct::select('id', 'title', 'ean', 'price','amazon_price','ebay_price', 'google_price','image','brand','supplier', 'availability', 'category', 'category_id', 'source', 'product_id')->findOrFail($id);


        // TODO:: PRODUCT_TABLE_CHANGE
        $data['product'] = $product = \App\DrmProduct::with(['drm_categories.drm_category'])->where([
            'user_id' => $userId,
            'id'      => $analysis_product->product_id
        ])->first();




        if (empty($product)) {
            $data['product'] = $analysis_product;
        }else{
            $channel_vks = array_column($analysis_product->product->connected_products->toArray(), 'vk_price');
            if( !empty($channel_vks) ){
                $vk_avg = count($channel_vks) > 0 ? array_sum($channel_vks) / count($channel_vks) : null;
            }else{
                $vk_avg = 0;
            }
            $data['vk_avg'] = number_format($vk_avg, 2, ',', '.');
        }

        // echo '<pre>';
        // print_r($data['vk_avg'] );
        // die();

        $data['languageId'] = app('App\Services\UserService')->getProductCountry($userId);
        $data['lang'] =$lang= app('App\Services\UserService')->getProductLanguage($data['languageId']);

        $data['page_title'] = $product->title[$lang];

        $data['trans_cat'] = "category_name_" . $data['lang'];
        $data['shop'] = array();
        $results = $data['product']->connected_products ?? array();
        $data['channels'] = collect(config('channel.list'));

        $shop_prices = array();
        foreach ($results as $result) {
            $channelName = $data['channels']->where('type', $result->channel)->first();
            $channel_name = ucfirst(strtolower($channelName['name']));
            $vk_price = $result->vk_price ?? "N/A";
            $shop_prices[$channel_name] = $vk_price;
        }
        $data['shop_price'] = $shop_prices;
        $data['avg_price'] = 0;
        if (count(array_filter($shop_prices))) {
            $data['avg_price'] = count(array_filter($shop_prices)) > 0 ? array_sum(array_values($shop_prices)) / count(array_filter($shop_prices)) : null;
        }

        $data['stock'] = ($analysis_product->source == 0) ? $analysis_product->availability : $product->stock;
        $data['source'] = ($analysis_product->source == 0) ? 'Imported' : 'DRM Product';

        $merchant_name = [];

        if($analysis_product->category_id != null){
            $cat_name = AnalysisCategory::select('name', 'type', 'duration')->where('id', $analysis_product->category_id)->first();
            $merchant_name['category_name'] = $cat_name->name;
            $merchant_name['type'] = $cat_name->type;
            $merchant_name['duration'] = $cat_name->duration;
        }
        else{
            $merchant_name['category_name'] = 'default';
        }
        $data['category_name'] = $merchant_name;

        return view('admin.competitive_analysis.details', $data);
    }

    public function saveColumns(Request $request){
        $user_id = CRUDBooster::myParentId();
        DB::table('drm_user_saved_columns')
            ->updateOrInsert(
                ['user_id' => $user_id, 'table_name' => 'analysis_products'],
                [
                    'columns' => json_encode($request->saved_columns),
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        Cache::forget('saved_columns_'.$user_id);
        return back();
    }

    public function table_column(){
        if(CRUDBooster::isSupplier()){
            return [
                'id'                    => ["label" => __("ID") , "sorting" => true, "channel" => false],
                'title'                 => ["label" => __('cp_title') , "sorting" => true, "channel" => false],
                'brand'                 => ["label" => __('Brand') , "sorting" => true, "channel" => false],
                'source_name'           => ["label" => __('Source') , "sorting" => true, "channel" => false],
                'marchandise'           => ["label" => __('Marchandise') , "sorting" => true, "channel" => false],
                'image'                 => ["label" => __('cp_image') , "sorting" => false, "channel" => false],
                'ean'                   => ["label" => __("EAN") , "sorting" => true, "channel" => false],
                'purchase_price'        => ["label" => __("cp_ek").' '.__("Net") , "sorting" => true, "channel" => false],
                'vk_avg'                => ["label" => __('products.VK_Price').' '.__("Gross") , "sorting" => true, "channel" => false],
                'uvp'                   => ["label" => __('UVP').' '.__("Gross") , "sorting" => true, "channel" => false],
                'rating'                => ["label" => __('Rating') , "sorting" => true, "channel" => false],
                'amazon_monthly_sales_estimate' => ["label" => 'Total Sales', "sorting" => true, "channel" => false],
                'min'                   => ["label" => __('min price') , "sorting" => true, "channel" => false],
                'cv'                    => ["label" => 'ø' , "sorting" => true, "channel" => false],
                'max'                   => ["label" => __('max price') , "sorting" => true, "channel" => false],
                'profit'                => ["label" => __('Profit') , "sorting" => true, "channel" => false],
                ];
        }

        else{
            $cols =  [
                'id'                            => ["label" => __("ID") , "sorting" => true, "channel" => false],
                'title'                         => ["label" => __('cp_title') , "sorting" => true, "channel" => false],
                'brand'                         => ["label" => __('Brand') , "sorting" => true, "channel" => false],
                'supplier'                      => ["label" => __('Supplier') , "sorting" => true, "channel" => false],
                'source_name'                   => ["label" => __('Source') , "sorting" => true, "channel" => false],
                'marchandise'                   => ["label" => __('Marchandise') , "sorting" => true, "channel" => false],
                'image'                         => ["label" => __('cp_image') , "sorting" => false, "channel" => false],
                'ean'                           => ["label" => __("EAN") , "sorting" => true, "channel" => false],
                'purchase_price'                => ["label" => __("cp_ek").' '.__("Net") , "sorting" => true, "channel" => false],
                'vk_avg'                        => ["label" => __('products.VK_Price').' '.__("Gross") , "sorting" => true, "channel" => false],
                'uvp'                           => ["label" => __('UVP') , "sorting" => true, "channel" => false],
                'googleshopping'                => ["label" => __('Google').' '.__("Gross") , "sorting" => true, "channel" => false],
                'rating'                        => ["label" => __('Rating') , "sorting" => true, "channel" => false],
                'amazon_weekly_sales_estimate'  => ["label" => __('sales_per_week') , "sorting" => true, "channel" => false],
                'amazon_weekly_sales_drop'      => ["label" => __('turnover_per_week') , "sorting" => true, "channel" => false],
                'amazon_monthly_sales_estimate' => ["label" => __('sales_per_month') , "sorting" => true, "channel" => false],
                'amazon_monthly_sales_drop'     => ["label" => __('turnover_per_month') , "sorting" => true, "channel" => false],
                'ebay_product_sold'             => ["label" => __('total_sales') , "sorting" => true, "channel" => false],
                'ebay_weekly_sales_drop'        => ["label" => __('turnover_per_week_ebay') , "sorting" => true, "channel" => false],
                'min'                           => ["label" => __('min price') , "sorting" => true, "channel" => false],
                'cv'                            => ["label" => 'ø' , "sorting" => true, "channel" => false],
                'max'                           => ["label" => __('max price') , "sorting" => true, "channel" => false],
                'availability'                  => ["label" => __('Stock') , "sorting" => false, "channel" => false],
                'profit'                        => ["label" => __('Profit') , "sorting" => true, "channel" => false],
                // 'sales_rank_30'                 => ["label" => __('Sales Rank Drop 30 Days') , "sorting" => true, "channel" => false],
                // 'sales_rank_90'                 => ["label" => __('Sales Rank Drop 90 Days') , "sorting" => true, "channel" => false],
                // 'sales_rank_180'                => ["label" => __('Sales Rank Drop 180 Days') , "sorting" => true, "channel" => false],
                'shipping_cost'                 => ["label" => __('Shipping Costs') , "sorting" => true, "channel" => false],
                'google_trend'                  => ["label" => __('Google Trend') , "sorting" => false, "channel" => false],
                'is_prime'                      => ["label" => __('Prime Shipping') , "sorting" => false, "channel" => false],
                'seller_name'                   => ["label" => __('Seller Name') , "sorting" => false, "channel" => false],
                'other_sellers'                 => ["label" => __('Other Sellers') , "sorting" => false, "channel" => false],
                'manual_update'                 => ["label" => __('Manual Update') , "sorting" => false, "channel" => false],


            ];

            $csvIntervalPlanId = app_user_plan_id(CRUDBooster::myParentId(), config('global.csv_interval_app_id'));

            $sales_menu = in_array($csvIntervalPlanId, [26, 27]);

            if($sales_menu){
                $new_cols = [
                    'sales_7_days'                 => ["label" => __('Last 7 Days MP Sales') , "sorting" => false, "channel" => false],
                    'sales_7_days_amount'                 => ["label" => __('Last 7 Days MP Sales Amount') , "sorting" => false, "channel" => false],
                    'sales_30_days'                 => ["label" => __('Last 30 Days MP Sales') , "sorting" => false, "channel" => false],
                    'sales_30_days_amount'                 => ["label" => __('Last 30 Days MP Sales Amount') , "sorting" => false, "channel" => false],
                ];
                $cols = array_merge($cols, $new_cols);
            }

            return $cols;
        }


        // 'channels'              => ["label" => __('products.Connected_Channels') , "sorting" => false],
        // 'scoll_div'              => ["label" => "Scrollable" , "sorting" => false],
    }


    public function priceHistory($id){

        $userId = CRUDBooster::myParentId();

        // if(!isLocal() && !in_array($userId, [212, 62,61,2661, 2604,2698, 75 ])) abort(404);

        $analysis_product = AnalysisProduct::select(
            'ean',
            'product_id',
            'source',
            'amazon_last_sync',
            'ebay_last_sync',
            'google_last_sync',
            'amazon_price',
            'ebay_price',
            'google_price',
        )
        ->where('id', $id)
        ->first();

        $channel_history = [];
        $available = false;

        if($analysis_product->source == 1){



            // TODO:: PRODUCT_TABLE_CHANGE
            $productService = new DRMProductService();
            $product = $productService->findUserProduct($analysis_product->product_id, $userId);



            if($product && $product->id) {
                $channel_list = Shop::where('user_id', $userId)->with('color')->get()->keyBy('channel')->toArray();
                foreach ($product->connected_products as $key => $channel_product) {
                    if(empty($channel_list[$channel_product->channel])) continue;
                    $shop_name = $channel_list[$channel_product->channel]['shop_name'];

                    $price_list = ChannelPriceHistory::select('vk_price_after as price', 'created_at as date')
                    ->where('channel_products_id', $channel_product->id)
                    ->orderBy('created_at')->get();
                    $history = [];
                    foreach ($price_list as $key => $row) {
                        $history[] = [
                            'x' => date('Y-m-d', strtotime($row->date)),
                            'y' => round($row->price, 2),
                        ];
                    }
                    if(empty($history)) continue;
                    $color = !empty($channel_list[$channel_product->channel]['color']) ? $channel_list[$channel_product->channel]['color']['color'] : CHANNEL::COLOR[$channel_product->channel];

                    if(!empty($history))
                    {
                        $channel_history[] = [
                            'name' => $shop_name,
                            'data' => $history,
                            'color' => $color,
                            'dashArray' => 0,
                        ];
                    }
                }
            }
        }

        $productPriceHistory = ProductPriceApi::select('source', 'price', 'rating', 'created_at')
        ->where('ean', $analysis_product->ean)
        ->orderBy('created_at', 'desc')->get();

        $amazon = [];
        $ebay = [];
        $googleshopping = [];
        $googlerating = [];
        $ebayrating = [];
        $amazonrating = [];
        $amazon_sale_estimations = [];
        $ebay_sale_estimations = [];
        $google_sale_estimations = [];
        foreach ($productPriceHistory as $key => $price) {

            if(!$price->price || empty($price->price)) continue;

            $available = true;

            switch ($price->source) {
                case 'amazon.de':
                    $amazon[] = ['x' => date('Y-m-d', strtotime($price->created_at)), 'y' => round($price->price, 2) ];
                    $amazonrating[date('Y-m-d', strtotime($price->created_at))] = ['x' => date('Y-m-d', strtotime($price->created_at)), 'y' => round($price->rating, 1) ];
                    break;

                case 'ebay.de':
                    $ebay[] = ['x' => date('Y-m-d', strtotime($price->created_at)), 'y' => round($price->price, 2) ];
                    $ebayrating[date('Y-m-d', strtotime($price->created_at))] = ['x' => date('Y-m-d', strtotime($price->created_at)), 'y' => round($price->rating, 1) ];
                    break;

                case 'google.de':
                    $googleshopping[] = ['x' => date('Y-m-d', strtotime($price->created_at)), 'y' => round($price->price, 2) ];
                    $googlerating[date('Y-m-d', strtotime($price->created_at))] = ['x' => date('Y-m-d', strtotime($price->created_at)), 'y' => round($price->rating, 1) ];
                    break;

                default:
                    # code...
                    break;
            }
        }

        $productSaleEstimationHistory = ProductSalesEstimateApi::select('source', 'weekly_sales_estimate', 'created_at')
        ->where('ean', $analysis_product->ean)
        ->orderBy('created_at', 'desc')->get();

        foreach ($productSaleEstimationHistory as $key => $sale) {

            if(!$sale->weekly_sales_estimate || empty($sale->weekly_sales_estimate)) continue;

            $available = true;

            switch ($sale->source) {
                case 'amazon.de':
                    $amazon_sale_estimations[] = ['x' => date('Y-m-d', strtotime($sale->created_at)), 'y' => $sale->weekly_sales_estimate, ];
                    break;

                case 'ebay.de':
                    $ebay_sale_estimations[] = ['x' => date('Y-m-d', strtotime($sale->created_at)), 'y' => $sale->weekly_sales_estimate ];
                    break;

                case 'google.de':
                    $google_sale_estimations[] = ['x' => date('Y-m-d', strtotime($sale->created_at)), 'y' => $sale->weekly_sales_estimate ];
                    break;

                default:
                    # code...
                    break;
            }
        }

        if(empty($amazon) && $analysis_product->amazon_last_sync)
        {
            $amazon[] = ['x' => date('Y-m-d', strtotime($analysis_product->amazon_last_sync)), 'y' => round($analysis_product->amazon_price, 2) ];
            if($analysis_product->amazon_price != 0 || $analysis_product->amazon_price != null){
                $available = true;
            }
        }

        if(empty($ebay) && $analysis_product->ebay_last_sync)
        {
            $ebay[] = ['x' => date('Y-m-d', strtotime($analysis_product->ebay_last_sync)), 'y' => round($analysis_product->ebay_price, 2) ];
            if($analysis_product->ebay_price != 0 || $analysis_product->ebay_price != null){
                $available = true;
            }
        }

        if(empty($googleshopping) && $analysis_product->google_last_sync)
        {
            $googleshopping[] = ['x' => date('Y-m-d', strtotime($analysis_product->google_last_sync)), 'y' => round($analysis_product->google_price, 2) ];
            if($analysis_product->google_price != 0 || $analysis_product->google_price != null){
                $available = true;
            }
        }

        $productApi_price_history = [];

        if(!empty($amazon))
        {
            $productApi_price_history[] = [
                'name' => 'AMAZON',
                'data' => $amazon
            ];
        }

        if(!empty($ebay))
        {
            $productApi_price_history[] = [
                'name' => 'EBAY',
                'data' => $ebay
            ];
        }

        if(!empty($googleshopping))
        {
            $productApi_price_history[] = [
                'name' => 'GOOGLESHOPPING',
                'data' => $googleshopping
            ];
        }

        if(!empty($ebay_sale_estimations))
        {
            $productApi_price_history[] = [
                'name' => 'Ebay Sales',
                'data' => $ebay_sale_estimations
            ];
        }

        if(!empty($amazon_sale_estimations))
        {
            $productApi_price_history[] = [
                'name' => 'Amazon Sales',
                'data' => $amazon_sale_estimations
            ];
        }

        if(!empty($google_sale_estimations))
        {
            $productApi_price_history[] = [
                'name' => 'Google Sales',
                'data' => $google_sale_estimations
            ];
        }

        // if(!empty($googlerating) || !empty($ebayrating) || !empty($amazonrating))
        // {
        //     $rating = [];
        //     $sum = 0;
        //     $i=0;
        //     foreach($ebayrating as $ebay){
        //         $avg = 0;
        //         $sum = 0;
        //         $count = 1;
        //         $i++;
        //         // dd($googlerating[$ebay['x']]);
        //         $sum += $ebay['y'] + $googlerating[$ebay['x']]['y'] + $amazonrating[$ebay['x']]['y'];
        //         $count = $this->checkNull($ebay['y']) + $this->checkNull($googlerating[$ebay['x']]['y']) + $this->checkNull($amazonrating[$ebay['x']]['y']);
        //         if($count == 0 || $count == null)
        //             $count = 1;
        //         $avg = $sum/$count;
        //         $rating[] = ['x' => $ebay['x'], 'y' => $avg];
        //     }
        //     $productApi_price_history[] = [
        //         'name' => 'RATING',
        //         'data' => $rating
        //     ];
        // }

        $productApi_price_history[] = $available;
        $response = $productApi_price_history;

        if(!empty($channel_history)) {
            $response = \array_merge($channel_history, $productApi_price_history);
        }

        return response()->json($response);
    }

    public function recreate(){
        ini_set('max_execution_time', -1);

        AnalysisProduct::where('user_id', 2455)->update([
            'ebay_price' => null,
            'amazon_price' => null,
            'ebay_last_sync' => null,
            'amazon_last_sync' => null
        ]);
    }

    public function ratingHistory($id){

        $userId = CRUDBooster::myParentId();

        $analysis_product = AnalysisProduct::select(
            'ean',
            'product_id',
            'source',
            'amazon_last_sync',
            'ebay_last_sync',
            'google_last_sync',
            'amazon_price',
            'ebay_price',
            'google_price',
        )
        ->where('id', $id)
        ->first();

        $productRatingHistory = ProductPriceApi::select('source', 'rating', 'created_at')
        ->where('ean', $analysis_product->ean)
        ->orderBy('created_at', 'asc')->get();

        $amazon = [];
        $ebay = [];
        $googleshopping = [];
        $available = false;
        $starting_date = new DateTime($productRatingHistory[0]->created_at);
        foreach ($productRatingHistory as $key => $rating) {

            $current_date = new DateTime($rating->created_at);
            $interval = $starting_date->diff($current_date)->format('%a');

            if(!$rating->rating || empty($rating->rating) || ($interval < "7" && $interval != "0") ) continue;

            else if($interval >= "7"){
                $starting_date = new DateTime($rating->created_at);
            }

            $available = true;

            switch ($rating->source) {
                case 'amazon.de':
                    $amazon[] = ['x' => date('Y-m-d', strtotime($rating->created_at)), 'y' => round($rating->rating, 2) ];
                    break;

                case 'ebay.de':
                    $ebay[] = ['x' => date('Y-m-d', strtotime($rating->created_at)), 'y' => round($rating->rating, 2) ];
                    break;

                case 'google.de':
                    $googleshopping[] = ['x' => date('Y-m-d', strtotime($rating->created_at)), 'y' => round($rating->rating, 2) ];
                    break;

                default:
                    # code...
                    break;
            }
        }

        if(empty($amazon) && $analysis_product->amazon_last_sync)
        {
            $amazon[] = ['x' => date('Y-m-d', strtotime($analysis_product->amazon_last_sync)), 'y' => 0 ];
        }

        if(empty($ebay) && $analysis_product->ebay_last_sync)
        {
            $ebay[] = ['x' => date('Y-m-d', strtotime($analysis_product->ebay_last_sync)), 'y' => 0 ];
        }

        if(empty($googleshopping) && $analysis_product->google_last_sync)
        {
            $googleshopping[] = ['x' => date('Y-m-d', strtotime($analysis_product->google_last_sync)), 'y' => 0 ];
        }

        $productApi_rating_history = [];

        if(!empty($amazon))
        {
            $productApi_rating_history[] = [
                'name' => 'AMAZON',
                'data' => $amazon
            ];
        }

        if(!empty($ebay))
        {
            $productApi_rating_history[] = [
                'name' => 'EBAY',
                'data' => $ebay
            ];
        }

        if(!empty($googleshopping))
        {
            $productApi_rating_history[] = [
                'name' => 'GOOGLESHOPPING',
                'data' => $googleshopping
            ];
        }

        $productApi_rating_history[] = $available;

        $response = $productApi_rating_history;

        return response()->json($response);
    }

    public function insertIntoPPA() {
        ini_set('max_execution_time', '1000000');
        $products = AnalysisProduct::get();
        foreach($products as $product){
            ProductPriceApi::where('ean', $product->ean)->whereNull('title')->update([
                'title' => $product->title,
            ]);
        }
    }

    public function revertCpTables(){
        ini_set('max_execution_time', '1000000');
        $products = DB::connection('cp_backup')->table('analysis_products')->where('user_id', 2604)->get();
        foreach($products as $product){
            AnalysisProduct::where('id', $product->id)->update([
                'category_id' => $product->category_id,
            ]);
        }
    }

    public function apiPriceForAllProducts(Request $request){
        $user_id = $request->user_id;
        $isCheckAll = false;
        $product_ids = $request->products;
        if($isCheckAll == false){
            $products = AnalysisProduct::where('archived', 0)->select(DB::raw(" *,
            (SELECT(TIMESTAMPDIFF(HOUR, COALESCE(`amazon_last_sync`, COALESCE(`ebay_last_sync`, COALESCE(`google_last_sync`, 0))), now()))) as `interval`"))->get();
        }

        elseif($isCheckAll == true){
            $products = AnalysisProduct::where('archived', 0)->whereIn('id', $product_ids)->select(DB::raw(" *,
            (SELECT(TIMESTAMPDIFF(HOUR, COALESCE(`amazon_last_sync`, COALESCE(`ebay_last_sync`, COALESCE(`google_last_sync`, 0))), now()))) as `interval`"))->get();
        }

        $updatable_products1 = $products->where('interval', '>', 24)->pluck('id')->toArray();
        $updatable_products2 = $products->where('interval', null)->pluck('id')->toArray();
        $updatable_products = array_merge($updatable_products1, $updatable_products2);


        // ProcessProductPriceUpdate::dispatch($user_id, $updatable_products);

        return [
            'success' => true,
            'message' => 'Prices updated successfully!'
        ];
    }

    public function saleRankUpdate(Request $request){
        try{
            $userId = CRUDBooster::myParentId();
            $ean_array = $request->ean_array ?? [];
            $EANS = AnalysisProduct::whereIn('ean', $ean_array)
                ->where('user_id', $userId)
                ->whereNull('sales_rank_30')
                ->pluck('ean')
                ->toArray();

                if(!empty($EANS)){
                    $app_id = config('global.analysis_sales_rank_app_id');
                    $isAppPurchase = DrmUserHasPurchasedApp($userId, $app_id);
                    if ($isAppPurchase || CRUDBooster::isDropmatixSupport()) {
                        $ranks = $this->salesRank($EANS, $userId); // update sales rank
                        return response()->json([
                            'success' => true,
                            'message' => 'Get sales ranks successfully!',
                            'data' => $ranks
                        ]);

                    }
                }

                return response()->json([
                    'success' => false,
                    'message' => 'No EAN for get sales rank',
                ]);
//            dd($request->ean_array,$EANS);
        }catch (Exception $exception){

        }
    }

    public function salesRank($eanArray, $user_id)
    {
        try {
            $keepa_api = new KeepaAPI();
            $salesRank = $keepa_api->getSelesRankByEAN($eanArray);

            if ($salesRank) {
                foreach ($salesRank as $rank) {
                    AnalysisProduct::where([
                        'user_id' => $user_id,
                        'ean' => $rank['ean']
                    ])->update($rank);
                }
            }
            return $salesRank ?? [];
        } catch (Exception $exception) {

        }
    }

    public function checkMpProduct(Request $request)
    {
        $product_ids = $request->product_ids;
        $user_id = CRUDBooster::myParentId();

        $mp_products = AnalysisProduct::where('user_id', $user_id)
            ->where('source', 2)
            ->where('archived', 0)
            ->select('id', 'product_id');

        if ($request->isCheckAll == 'false') {
            $mp_products->whereIn('id', $product_ids);
        }
        $mp_products = $mp_products->get();

        $product_ids = $mp_products->pluck('id')->toArray();

        $mp_product_count = count($product_ids);

        if($mp_product_count > 0){
            $message = __('Product Available For Transferring To Stock');
        }else{
            $message = __('Only Marketplace products can be transferred to Stock');
        }


        return response()->json([
            'success' => true,
            'product_ids' => $product_ids,
            'message' => $message
        ]);
    }

    public function transferToStock(Request $request){
        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->product_ids;

        $drm_product_ids = AnalysisProduct::whereIn('id', $product_ids)->pluck('product_id')->toArray();
        $mp_products = MarketplaceProducts::whereIn('id', $drm_product_ids)->pluck('id')->toArray();

        app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferAllFilteredProductsToDrm($mp_products);

        return response()->json([
            'success' => true,
            'message' => 'Product transfered successfully!'
        ]);
    }

    public function deleteCollections(){
        ini_set('max_execution_time', '-1');
        $analysisService = $this->analysisService;
        foreach($analysisService as $service){
            $analysisApi = new $service;
            $collection_column_name = $analysisApi->collectionColumnName();
            $collection_ids = CPAnalysisRequest::whereNotNull($collection_column_name)->orderBy('id', 'desc')->limit(10000)->pluck($collection_column_name)->toArray();
            foreach($collection_ids as $id){
                $analysisApi->deleteCollection($id);
            }
        }
    }

    public function recreateCollections(){
        // abort(404);
        ini_set('max_execution_time', '-1');

        try{
            for($i = 29; $i <= 64; $i++){

                $pathString = 'storelead_csv\europa\Europa_' . $i . '.csv';

                DB::table('storelead_file_number')->insert([
                    'file' => $pathString,
                    'created_at' => now()
                ]);
                $filePath = storage_path($pathString);
                $csv = Reader::createFromPath($filePath, 'r');
                $csv->setHeaderOffset(0); // Skip the header row

                $records = $csv->getRecords();
                $dataArray = iterator_to_array($records);
                $count = 0;
                $data = [];
                foreach($dataArray as $item){
                    $est_sale = $item['estimated_monthly_sales'];
                    $est_sale = explode(" ", $est_sale)[1];
                    $est_sale = substr($est_sale, 1);
                    $item['estimated_monthly_sales'] = $est_sale;
                    $data[] = $item;
                    $count++;
                }
                $chunks = array_chunk($data, 1);  //make it 2
                foreach($chunks as $chunk){
                    try{
                        StoreleadDataCollection::updateOrInsert([
                            'domain' => $chunk[0]['domain']
                        ],$chunk[0]);
                    }catch(Exception $e){
                        DB::table('storelead_upload_log')->insert([
                            'file_name' => $pathString,
                            'data' => json_encode($chunk),
                            'error' => $e,
                            'created_at' => now()
                        ]);
                    }
                }
            }
        } catch(Exception $e){
            dd($e);
        }

        dd(1);

        ini_set('max_execution_time', '-1');

        AnalysisProduct::where('source', 2)->select('id', 'product_id')->chunk(500, function($items) {
            foreach($items as $item){
                $mp_vk = MarketplaceProducts::where('id', $item->product_id)->value('vk_price');

                AnalysisProduct::where('id', $item->id)->update([
                    'purchase_price' => $mp_vk
                ]);
            }
        });
        dd(1);
        DB::table('cms_email_templates')->insert([


            'slug' => "dropcampus_account_activation_mail",

            'subject' => "Ihr Dropcampus-Konto ist aktiviert",

            'content' => "<p>

            Lieber Benutzer [user_name],

            </p>

            Sie haben ein kostenloses Abonnement unseres Dienstes <a href='[login_link]'>Dropcampus</a>.<br><br>


            Ihr Passwort ist: <p style='font-size:x-large;font-weight:bold'>[password]</p><br><br>


            Genießen<br><br>


            Ihr Team von<br>

            [logo]"

        ]);

        dd(1);

        $this->api_data($request);
        dd("done!");

        ini_set('max_execution_time', '-1');

        try{
            AnalysisProduct::join('drm_products', 'analysis_products.product_id', '=', 'drm_products.id')
                ->where('analysis_products.source', 1)
                ->select('analysis_products.id', 'drm_products.shipping_cost')
                ->update([
                    'analysis_products.drm_shipping_price' => DB::raw('`drm_products`.`shipping_cost`')
                ]);


            AnalysisProduct::whereIn('source', [2, 3])->select('id', 'product_id')->chunk(1, function($item) {
                $mp_product = MarketplaceProducts::where('id', $item[0]->product_id)->value('shipping_cost');

                AnalysisProduct::where('id', $item[0]->id)->update([
                    'drm_shipping_price' => $mp_product
                ]);
            });
        }
        catch(Exception $e){
            dd($e);
        }
        dd("Shipping Cost Updated");

        try{
            $user_id = 3241;
            $languageId = app('App\Services\UserService')->getProductCountry($user_id);
            $lang = app('App\Services\UserService')->getProductLanguage($languageId);

            $product_ids = AnalysisProduct::where('user_id', 2455)->where('archived', '<>', 1)->whereIntegerInRaw('source', [2, 3])->pluck('product_id')->toArray();

            $chunks = array_chunk($product_ids, 500);
            foreach ($chunks as $chunk) {
                $mp_products = MarketplaceProducts::whereIn('id', $chunk)->pluck('id')->toArray();

                if(empty($mp_products)) continue;

                app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferAllFilteredProductsToDrm($mp_products, [], null, $user_id);
            }

        }
        catch(Exception $e){
            dd($e);
        }
        dd("transferred");

        try{
            AnalysisProduct::where('user_id', 3110)->chunk(500, function($rows) {
                foreach($rows as $row){
                    if($row->source == '1'){
                        $product = DrmProduct::where('ean', $row->ean)->where('user_id', 3110)->select('id', 'ek_price', 'vk_price', 'shipping_cost')->first();
                        if($product){
                            $ek_price = $product->ek_price;
                            $vk_price = $product->vk_price;

                            AnalysisProduct::where('id', $row->id)->update([
                                'price' => $vk_price,
                                'purchase_price' => $ek_price
                            ]);
                        }
                    }
                    else if($row->source == '2'){
                        $product = MarketplaceProducts::where('id', $row->product_id)->select('id', 'ek_price', 'vk_price', 'shipping_cost')->first();
                        if($product){
                            $ek_price = $product->ek_price;
                            $vk_price = $product->vk_price;

                            AnalysisProduct::where('id', $row->id)->update([
                                'price' => $vk_price,
                                'purchase_price' => $ek_price
                            ]);
                        }
                    }
                }
            });
        } catch(Exception $e){
            dd($e);
        }
        dd('done');
        // $user_ids = [166, 2698, 62, 2560];
        // AnalysisCategory::where('user_id', 166)->delete();

        // $analysisService = $this->analysisService;

        $ids = AnalysisProduct::select('user_id')->distinct()->get();
        // foreach($ids as $id){
        //     $collection_interval_data = [
        //         "schedule_type" => "manual",
        //     ];

        //     $collection_body = [
        //         "name" => "Collection - $id->user_id - " . date('Y-m-d'),
        //         "enabled" => True,
        //         "priority" => "normal",
        //         "notification_as_csv" => True,
        //         "notification_as_json" => True,
        //         "include_html" => 'False'
        //     ];

        //     $collectionBody =  array_merge($collection_body, $collection_interval_data);
        //     foreach($analysisService as $service)
        //     {
        //         $analysisApi = new $service;
        //         $collection_id = $analysisApi->createCollection($collectionBody);
        //         $collection_id_column = $analysisApi->collectionColumnName();
        //         CPAnalysisRequest::create([
        //             'user_id' => $id->user_id,
        //             $collection_id_column => $collection_id,
        //             'default' => 1,
        //         ]);
        //     }
        // }

        // $category_ids = AnalysisCategory::get();
        // foreach($category_ids as $category){
        //     foreach($analysisService as $service){
        //         $analysisApi = new $service;
        //         if($category->type == 'oneTime'){
        //             $collection_interval_data = [
        //                 "schedule_type" => "manual",
        //             ];
        //         }

        //         else if($category->type == 'hours'){
        //             $collection_interval_data = [
        //                 "schedule_type" => "daily",
        //                 "schedule_hours" => $category->duration,
        //             ];
        //         }

        //         else if($category->type == 'minutes'){
        //             $collection_interval_data = [
        //                 "schedule_type" => "minutes",
        //                 "schedule_hours" => $category->duration,
        //             ];
        //         }

        //         $collection_body = [
        //             "name" => $category->name,
        //             "enabled" => True,
        //             "priority" => "normal",
        //             "notification_as_csv" => True,
        //             "notification_as_json" => True,
        //             "include_html" => 'False'
        //         ];

        //         $collectionBody =  array_merge($collection_body, $collection_interval_data);
        //         $collection_id = $analysisApi->createCollection($collectionBody);

        //         $collection_column_name = $analysisApi->collectionColumnName();
        //         AnalysisCategory::where('id', $category->id)->update([
        //             $collection_column_name => $collection_id
        //         ]);
        //     }
        // }

        // foreach($user_ids as $user){
        //     $id = AnalysisCategory::create([
        //         'user_id' => $user,
        //         'name' => "Collection - $user - " . date('Y-m-d') . " - easy-transfer",
        //         'duration' => 8,
        //         'type' => "hours",
        //         'easy_pricing' => 1
        //     ])->id;
        //     foreach($analysisService as $service){
        //         $analysisApi = new $service;
        //         $collection_interval_data = [
        //             "schedule_type" => "daily",
        //             "schedule_hours" => 8,
        //         ];

        //         $collection_body = [
        //             "name" => "Collection - $user - " . date('Y-m-d') . " - easy-transfer",
        //             "enabled" => True,
        //             "priority" => "normal",
        //             "notification_as_csv" => True,
        //             "notification_as_json" => True,
        //             "include_html" => 'False'
        //         ];
        //         $collection_column_name = $analysisApi->collectionColumnName();
        //         $collectionBody =  array_merge($collection_body, $collection_interval_data);
        //         $collection_id = $analysisApi->createCollection($collectionBody);
        //         AnalysisCategory::where('id', $id)->update([
        //             $collection_column_name => $collection_id
        //         ]);
        //     }
        //     AnalysisProduct::where('user_id', $user)->where('category_id', '!=', null)->update([
        //         'category_id' => $id
        //     ]);
        // }
        foreach($ids as $customer_id){
            ProcessCPTransfer::dispatch($customer_id->user_id);
        }

        // $saved_columns = DB::table('drm_user_saved_columns')->where('table_name', 'analysis_products')->whereJsonContains('columns', 'Check24')->orWhereJsonContains('columns', 'Kaufland')->get();
        // foreach($saved_columns as $user){
        //     $columns = json_decode($user->columns);
        //     $length = count($columns);
        //     for($i = 0; $i < $length; $i++){
        //         if($columns[$i] == 'Kaufland' || $columns[$i] == 'Check24'){
        //             unset($columns[$i]);
        //         }
        //     }
        //     DB::table('drm_user_saved_columns')->where('id', $user->id)->update([
        //         'columns' => json_encode(array_values($columns))
        //     ]);
        // }

    }

    public function updateTariffBalance($user_id, $count){
        $status = DB::table('drm_tariff_balance')->where('user_id', $user_id)->select('use_top_up', 'balance')->first();
        if($status){
            if($status->use_top_up == 1){
                $balance = $status->balance - ($count * 0.01);
                DB::table('drm_tariff_balance')->where('user_id', $user_id)->update([
                    'balance' => $balance
                ]);
            }
        }
        return true;
    }

    public function importCheck24(Request $request){

        $userId = CRUDBooster::myParentId();

        $data = [];
        if ($request->get('file') && !$request->get('import')) {
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;

            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = [
                'ean',
                'price',
            ];

            $labels = [
                0 => __('Ean'),
                1 => __('lowest competitor price'),
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        $userChannels = \App\Shop::where('user_id', $userId)->get();
        $data['kaufland_activated'] = 0;
        $data['check24_activated'] = 0;
        foreach($userChannels as $channel){
            if($channel->channel == '13'){
                $data['kaufland_activated'] = 1;
            }
            elseif($channel->channel == '14'){
                $data['check24_activated'] = 1;
            }
        }

        $data['page_title'] = 'Competitive analysis import';
        return view('admin.cp_analysis.import_check24', $data);
    }

    public function uploadCsvCheck24(Request $request){

        if ($request->hasFile('userfile')) {

            $request->validate([
                'userfile' => 'required|mimetypes:text/csv,text/plain,application/csv,text/comma-separated-values,text/anytext,application/octet-stream,application/txt',
            ]);

            $channel_id = $request->select_channel;

            $file = $request->file('userfile');
            $ext = $file->getClientOriginalExtension();

            $filePath = 'uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
            Storage::makeDirectory($filePath);

            $filename = md5(Str::random(5)).'.'.$ext;
            $url_filename = '';
            if (Storage::putFileAs($filePath, $file, $filename)) {
                $url_filename = $filePath.'/'.$filename;
            }
            $url = route('drm.competitive_analysis.check24_import').'?channel_id='.$channel_id.'&file='.base64_encode($url_filename);

            return redirect($url);
        } else {
            return redirect()->back();
        }
    }

    public function check24DoneImport(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Import Products"]);
        Session::put('select_column', $request->get('select_column'));

        return view('admin.cp_analysis.import_check24', $data);
    }

    public function do_check24_import_chunk(Request $request)
    {
        try{
            $file_md5 = md5($request->get('file'));

            if ($request->get('file') && $request->get('resume') == 1 && $request->get('action_type') != 0) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }

                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }

            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);

            $table_columns = [
                'ean',
                'price',
            ];

            $channel = $request->channel_id;
            if($channel == '13'){
                $channel_column = 'kaufland_price';
            }
            elseif($channel = '14'){
                $channel_column = 'check24_price';
            }

            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $count = 0;

            $analyseCollection = [];
            foreach ($rows as $row) {
                $count++;
                if($request->get('action_type') != 0){
                    Cache::put('success_' . $file_md5, $count);
                }
                $row = (object) $row;
                foreach ($select_column as $csvColName => $val) {

                    $colname = $table_columns[$csvColName];

                    if($colname == 'price'){
                        $row->$val = removeCommaPrice( $row->$val ) ?? 0;
                    }

                    if($colname == 'ean'){
                        $row->$val = ltrim($row->$val);
                        $row->$val = ltrim($row->$val, '0');
                        $row->$val = str_pad($row->$val,13,"0",STR_PAD_LEFT);
                    }

                    $data[$colname] = $row->$val;
                }

                try {
                    $analyseCollection[$data['ean']] = $data;
                } catch (Exception $e) {
                    $e = (string) $e;
                    Cache::put('error_' . $file_md5, $e, 500);
                }
            }

            // $exist_ean = AnalysisProduct::whereIn('ean', array_column($analyseCollection, 'ean'))->where('user_id', $user_id)->pluck('ean')->toArray();

            // $importable_products = array_filter($analyseCollection,function ($key) use ($exist_ean) {
            //     return in_array($key, $exist_ean);
            // }, ARRAY_FILTER_USE_KEY
            // );

            // $product_count = count($importable_products);

            foreach($analyseCollection as $product){
                AnalysisProduct::where('ean', $product['ean'])->where('user_id', $user_id)->update([
                    $channel_column => $product['price']
                ]);
            }
            return response()->json(['status' => true, 'message' => $res['message']]);
        }catch (Exception $e) {
            return response()->json(['status' => false, 'message' => $e]);
        }

    }

    public function archivedAtMod() {
        try{
            $products = AnalysisProduct::whereNotNull('amazon_other_sellers')->get();
            foreach($products as $product){
                $sellers = json_decode($product->amazon_other_sellers);
                $other_sellers = [];
                if(!empty($sellers)){
                    $temp = [];
                    foreach($sellers as $seller){
                        $temp['price'] = $seller->price;
                        $temp['seller_name'] = $seller->seller_name;
                        $temp['seller_link'] = $seller->seller_link;
                        $temp['is_prime'] = $seller->is_prime;
                        array_push($other_sellers, $temp);
                    }
                }
                AnalysisProduct::where('id', $product->id)->update([
                    'amazon_other_sellers' => $other_sellers
                ]);
            }
        } catch(Exception $e) {
            dd($e);
        }
    }

    public function transferToComparison(Request $request){
        $user_id = CRUDBooster::myParentId();

        $product_ids = $request->product_ids;
        $isCheckAll = $request->isCheckAll;

        if($isCheckAll == 'true'){
            $products = AnalysisProduct::where('user_id', $user_id)->where('archived', '<>', 1)->get();
        }
        else{
            $products = AnalysisProduct::where('user_id', $user_id)->whereIn('id', $product_ids)->where('archived', '<>', 1)->get();
        }

        foreach($products as $product){
            $mp_product = MarketplaceProducts::where('ean', $product->ean)->orderBy('stock', 'asc')->select('stock', 'ek_price')->first();
            $requested_products[$product->ean] = [
                'analysis_product_id' => $product->id,
                'ean' => $product->ean,
                'title' => $product->title,
                'source_ek_price' => $product->purchase_price,
                'source_stock' => $product->availability,
                'mp_stock' => $mp_product ? $mp_product->stock : 0,
                'mp_ek_price' => $mp_product ? $mp_product->ek_price : 0,
                'image' => json_encode($product->image),
                'user_id' => $user_id
            ];
        }

        $exist_eans = ComparisonAnalysisProduct::whereIn('ean', array_column($requested_products, 'ean'))->where('user_id', $user_id)->pluck('ean')->toArray();
        $transferable_products = array_diff_key($requested_products, array_flip($exist_eans));

        $chunks = array_chunk($transferable_products, 2000);
        foreach ($chunks as $chunk) {
            ComparisonAnalysisProduct::insert($chunk);
        }
    }

    public function transferToCustomer(Request $request) {
        try{
            $user_id = CRUDBooster::myParentId();

            $product_ids = $request->product_ids;
            $isCheckAll = $request->isCheckAll;
            $params = $request->params;
            $customer_ids = $request->customer_ids;
            if($isCheckAll == 'true'){
                $product_ids = $this->getSelectedIds($user_id, $params);
            }
            foreach($customer_ids as $customer){
                $chunks = array_chunk($product_ids, 500);
                foreach ($chunks as $chunk) {
                    ProcessCPToCoreTransferToCustomer::dispatch($customer, $chunk);
                }
            }

            return response()->json([
                'success' => true
            ]);
        } catch(Exception $e){
            dd($e);
        }
    }

    public function getSelectedIds($user_id, $params){
        $archive_filter = $params['archive'];
        $search_by_field = $params['search_by_field_column'];
        $search_by_channel = $params['search_by_channel'];
        $search_by_merchant = $params['search_by_merchant'];
        $search_by_brand = $params['search_by_brand'];
        $search_by_supplier = $params['search_by_supplier'];
        $search_by_source = $params['search_by_source'];
        $sort_by_sale = $params['sort_by_sale'];
        $search_by_rating = $params['search_by_rating'];
        $search_by_amazon_seller = $params['search_by_amazon_seller'];
        $range = $params['range'];
        $select_filter = $params['select_filter'];

        $products = AnalysisProduct::where('user_id', $user_id);

        $compare_type = $params['compare_type'];
        if(!empty($range) && $select_filter == 'margin_section'){
            $profit_range = explode('to', $range);
            $products->whereRaw('(((((' . ${"profit_column_query_" . $compare_type} . ' ) - NULLIF(`purchase_price`, 0)) * 100) / NULLIF(`purchase_price`, 0)) BETWEEN '.trim($profit_range['0']).' AND '.trim($profit_range['1']).')');
        }

        if( $params['non_profit'] == 'true' && $select_filter == 'margin_section'){
            $products->whereRaw('(((((' . ${"profit_column_query_" . $compare_type} . ' ) - NULLIF(`purchase_price`, 0)) * 100) / NULLIF(`purchase_price`, 0)) IS NULL )');
        }

        if( $params['no_competitor'] == 'true' && $select_filter == 'margin_section' && in_array($params['compare_type'], ['Ebay_channel_price', 'Droptienda_channel_price', 'Amazon_channel_price', 'Check24_channel_price', 'Kaufland_channel_price'])){
            if($params['compare_type'] == "Check24_channel_price"){
                $competitor_name = 'check24_price';
            }
            else if($params['compare_type'] == "Kaufland_channel_price"){
                $competitor_name = 'kaufland_price';
            }
            else if($params['compare_type'] == "Ebay_channel_price"){
                $competitor_name = 'ebay_price';
            }
            else if($params['compare_type'] == "Amazon_channel_price"){
                $competitor_name = 'amazon_price';
            }
            else if($params['compare_type'] == "Droptienda_channel_price"){
                $competitor_name = 'google_price';
            }
            $products->where($competitor_name, null);
        }

        if(!empty($sort_by_sale)){
            $sorting = $sort_by_sale;
            $products->orderBy('amazon_monthly_sales_estimate', $sorting);
            // dd($products);
        }
        $order_by = ($order_by == "marchandise") ? "category_id" : $order_by;
        if($order_by && $sorting){
            if(in_array($order_by, $sortable)){
                $products->orderBy($order_by, $sorting);
            }
        }



        if(!empty($search_by_field) && !empty($params['q'])){
            $q = $params['q'];
            if($search_by_field == 'title' || $search_by_field == 'all'){
                $q = "%$q%";
            }
            if($search_by_field != 'all'){
                $products->where($search_by_field, 'LIKE', $q);
            }else{
                $products->where(function($p) use ($q) {
                    $p->where('ean', 'LIKE', $q);
                    $p->orWhere('title', 'LIKE', "%$q%");
                    $p->orWhere('id', 'LIKE', $q);
                });
            }
        }

        if(!empty($search_by_rating)){
            $a = $search_by_rating + 1;
            $products->whereRaw("COALESCE(((Coalesce(`google_rating`,0) + Coalesce(`ebay_rating`,0) + Coalesce(`amazon_rating`,0)) /
            NULLIF((Coalesce(`google_rating`/`google_rating`, 0) + Coalesce(`ebay_rating`/`ebay_rating`, 0) + Coalesce(`amazon_rating`/`amazon_rating`, 0)), 0)), 0) >= $search_by_rating AND COALESCE(((Coalesce(`google_rating`,0) + Coalesce(`ebay_rating`,0) + Coalesce(`amazon_rating`,0)) /
            NULLIF((Coalesce(`google_rating`/`google_rating`, 0) + Coalesce(`ebay_rating`/`ebay_rating`, 0) + Coalesce(`amazon_rating`/`amazon_rating`, 0)), 0)), 0) < $a");
        }

		if($archive_filter && $archive_filter == 1) {
			$products->where('archived', '=', 1);
		}else {
			$products->where('archived', '<>', 1);
		}

		$products = $products->pluck('id')->toArray();
        return $products;
    }

    public function transferToBuyingChoices(Request $request){
        $user_id = CRUDBooster::myParentId();

        $product_ids = $request->product_ids;
        $isCheckAll = $request->isCheckAll;
        $params = $request->params;
        if($isCheckAll == 'true'){
            $product_ids = $this->getSelectedIds($user_id, $params);
        }

        $chunks = array_chunk($product_ids, 100);

        foreach($chunks as $chunk){
            $data = [];
            foreach($chunk as $item){
                $data[] = ['analysis_product_id' => $item, 'user_id' => $user_id, 'loading' => 1];
            }
            AnalysisBuyingChoices::updateOrInsert([
                'analysis_product_id' => $item,
                'user_id' => $user_id
            ],[
                'loading' => 1
            ]);
            ProcessTransferToCPBuyingChoices::dispatch($user_id, $product_ids);
        }
    }

    public function updateInBulk(Request $request){
        $user_id = CRUDBooster::myParentId();

        $product_ids = $request->product_ids;
        $isCheckAll = $request->isCheckAll;
        $params = $request->params;
        if($isCheckAll == 'true'){
            $product_ids = $this->getSelectedIds($user_id, $params);
        }

        $remain_credit = get_token_credit($user_id)['remain_credit'];
        if($remain_credit < count($product_ids)){
            return response()->json([
                'success' => false,
                'message' => 'You do not have enough credits to update these products!! Available Creadit is: '.$remain_credit
            ]);
        }
        $chunks = array_chunk($product_ids, 100);

        foreach($chunks as $chunk){
            //dispatch
            CpProductUpdateJob::dispatch($chunk);
            // dd($chunk);
        }
    }

    public function checkChannelExistance(Request $request){
        $channels = $request->input('channels', []);
        $ean = $request->input('ean');
        $price = $request->input('price');
        $user_id = CRUDBooster::myParentId();

        $html = "<div>";

        $matchedChannelDetails = ChannelProduct::where('ean', $ean)->whereIntegerInRaw('shop_id', $channels)->leftJoin('shops', 'channel_products.shop_id', '=', 'shops.id')->select('channel_products.id', 'channel_products.ek_price', 'shops.shop_name')->get();

        if(!$matchedChannelDetails->isEmpty()){
            foreach($matchedChannelDetails as $item){
                if($item->ek_price > $price){
                    $html = $html . "<span style='color:red'>" . $item->shop_name . " - " . __('Will Cause Loss') . "</span>";
                }elseif($item->ek_price < $price){
                    $html = $html . "<span style='color:green'>" . $item->shop_name . " - " . __('Will Increase Profit') . "</span>";
                }else{
                    $html = $html . "<span style='color:grey'>" . $item->shop_name . " - " . __('Already Has The Same Price') . "</span>";
                }

                $html .= "<br>";
            }

            $html = $html . "<br><h3>" . __('Do you want to update these prices?') . "</h3><br></div>";

            $product_ids = $matchedChannelDetails->pluck('id')->toArray();

            return response()->json([
                'success' => true,
                'html' => $html,
                'ids' => $product_ids,
                'available' => true
            ], 200);
        }
        else{
            return response()->json([
                'success' => true,
                'html' => $html,
                'available' => false
            ], 200);
        }
    }
    public function dropcampusCreateTest(){
        $user = User::with('billing_detail')->find(212);
        $drmCampusService = new DropmatixCampus();
        $drmCampusService->createDropmatixCampusUser($user);
        $dtCampusService = new DroptiendaCampus();
        $dtCampusService->createDroptiendaCampusUser($user);
        $erCampusService = new ExpertiseCampus();
        $erCampusService->createExpertiseCampusUser($user);
    }

    public function dropcampusDeactivateTest(){
        $user = User::with('billing_detail')->find(212);
        $drmCampusService = new DropmatixCampus();
        $drmCampusService->deactivateDropmatixCampusUser($user);
        $dtCampusService = new DroptiendaCampus();
        $dtCampusService->deactivateDroptiendaCampusUser($user);
        $erCampusService = new ExpertiseCampus();
        $erCampusService->deactivateExpertiseCampusUser($user);
    }

    public function dropcampusReactivateTest(){
        $user = User::with('billing_detail')->find(212);
        $drmCampusService = new DropmatixCampus();
        $drmCampusService->reactivateDropmatixCampusUser($user);
        $dtCampusService = new DroptiendaCampus();
        $dtCampusService->reactivateDroptiendaCampusUser($user);
        $erCampusService = new ExpertiseCampus();
        $erCampusService->reactivateExpertiseCampusUser($user);
    }

    public function manualUpdateProduct()
    {
        $req = request();

        $remain_credit = get_token_credit($req->user_id)['remain_credit'];

        if($remain_credit < 1) return response()->json([
                'success' => false,
                'message' => 'You do not have enough credits to update this products'
            ]);

        $domains = $this->getSelectedDomains($req->user_id);

        foreach($domains as $domain){
            $this->updateProduct($req->ean,$domain);
        }

        $this->updateEKPriceQuantities($req->ean,$req->user_id);
        //   $this->updateTariffBalance($req->user_id, 1);
        $msg = 'manual analysis product update';

        $this->creditDeductionForAnalysis($req->user_id, 1, $msg);

        return response()->json([
            'success' => true,
            'message' => $msg
        ]);
    }

    private function getSelectedDomains($user_id)
    {
        $columns = $this->getSelectedColumns($user_id);

        $domainMapping = [
            'ebay' => 'ebay.de',
            'amazon' => 'amazon.de',
            'googleshopping' => 'google.de'
        ];
        return array_values(array_intersect_key($domainMapping, array_flip($columns)));
    }

    public function manualUpdateProductBulk($ean, $user_id)
    {
        $domains = $this->getSelectedDomains($user_id);

        foreach($domains as $domain){
            $this->updateProduct($ean,$domain);
        }

        $this->updateEKPriceQuantities($ean,$user_id);
        //   $this->updateTariffBalance($user_id, 1);
        $msg = 'manual analysis product update';

        $this->creditDeductionForAnalysis($user_id, 1, $msg);
    }

    private function getSelectedColumns($user_id)
    {
        $columns = DB::table('drm_user_saved_columns')->where([
            'user_id' => $user_id,
            'table_name' => 'analysis_products'
        ])->value('columns');
        return json_decode($columns,true) ?? array();
    }

    private function updateEKPriceQuantities($ean,$user_id)
    {

         // TODO:: PRODUCT_TABLE_CHANGE
        $drm_product = DB::table('drm_products')->where('ean',$ean)->where('user_id',$user_id)->first();

        if($drm_product){
            $ek_price = $drm_product->ek_price;


            DB::table('analysis_products')->where('ean',$ean)->where('user_id',$user_id)->update([
                'purchase_price' => $ek_price,
                'manual_update' => now()
            ]);
        }else{

            DB::table('analysis_products')->where('ean',$ean)->where('user_id',$user_id)->update([
                'manual_update' => now()
            ]);

        }
    }

    public function updateProduct($ean, $domain){
        $domain_column = (strpos($domain, 'ebay') !== false) ? 'ebay' : 'amazon';
        $price_column = $domain_column.'_price';
        $rating_column = $domain_column.'_rating';
        $rating_count_column = $domain_column.'_rating_count';
        $product_number_column = $domain_column.'_product_number';
        $last_sync_column = $domain_column.'_last_sync';
        $also_bought_column = $domain_column.'_also_bought';

        $productApi = new ProductApi($domain);
        $product = $productApi->search('ean', $ean)->fetch();

        $price = 0;
        $rating = 0;
        $isPrime = false;
        $productNumber = null;

        if($product->price() || $product->offerPrice() || $product->rating() || $product->productNumber() || $product->ratingCount() || $product->title() || $product->sellerName() || $product->sellerLink() || $product->isPrime() || $product->getOtherSeller()){

            if($product->price()){
                $price = $product->price();
            }

            if($product->title()){
                $title = $product->title();
            }

            // elseif($product->offerPrice()){
            //     $price = $product->price();
            // }

            if($product->rating()){
                $rating = $product->rating();
            }

            if($product->productNumber()){
                $productNumber = $product->productNumber();
            }

            if($product->ratingCount()){
                $ratingCount = $product->ratingCount();
            }

            if($product->image()){
                $image = $product->image();
            }

            if($product->sellerName()){
                $sellerName = $product->sellerName();
            }

            if($product->sellerLink()){
                $sellerLink = $product->sellerLink();
            }

            if($product->isPrime()){
                $isPrime = $product->isPrime();
            }

            if($product->getOtherSeller()){
                $otherSeller = $product->getOtherSeller();
            }
            // $price = (float)$price / 100;

            // if($domain_column === 'amazon')
            // {
            //     $price = $product->offerPrice();
            // }

            if($product->getPeopleAlsoBought()){
                $alsoBought = $product->getPeopleAlsoBought();
            }

            if($product->getProductSold()){
                $productSold = $product->getProductSold();
            }

            $analysis_product = AnalysisProduct::where('ean', $ean)->first();

            if(strlen($productNumber) > 0){
                DB::table('amazon_asin_collections')->updateOrInsert(
                    [
                        'ean' => $ean,
                        'domain' => 3
                    ],
                    [
                        'asin' => $productNumber
                    ]
                );
            }
            if($domain == 'amazon.de'){
                AnalysisProduct::where('ean', $ean)->update([
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    'amazon_seller_name' => $sellerName,
                    'amazon_seller_link' => $sellerLink,
                    'is_prime' => $isPrime,
                    'amazon_other_sellers' => $otherSeller,
                    $also_bought_column => $alsoBought,
                    $last_sync_column => now(),
                ]);
            }else{
                AnalysisProduct::where('ean', $ean)->update([
                    $rating_column => $rating,
                    $price_column => $price,
                    $rating_count_column => $ratingCount,
                    $product_number_column => $productNumber,
                    'ebay_product_sold' => $productSold,
                    $last_sync_column => now(),
                ]);
            }

            if($analysis_product->image == null){
                AnalysisProduct::where('ean', $ean)->update([
                    'image' => json_encode([$image])
                ]);
            }
            if($analysis_product->title == null){
                AnalysisProduct::where('ean', $ean)->update([
                    'title' => $title
                ]);
            }
            $addPrices[] = [
                'ean' => $ean,
                'source' => $domain,
                'title' => $title,
                'price' => $price,
                'rating' => $rating,
                'rating_count' => $ratingCount,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
            ProductPriceApi::insert($addPrices);
        }
        else{
            AnalysisProduct::where('ean', $ean)->update([
                $last_sync_column => now(),
            ]);
        }

        return $productNumber;
    }

    public function creditDeductionForAnalysis($user_id, $total_credit_deduction, $msg) {
        // $status = \App\Enums\CreditType::CREDIT_REMOVE;
        // app('App\Http\Controllers\tariffController')->CreditUpdate($user_id, $total_credit_deduction, 'credit_deduct');
        // app('App\Http\Controllers\tariffController')->drmUserCreditAdd($user_id, $total_credit_deduction, $msg, CreditType::PRODUCT_ANALYSIS, $status);

        (new ChargeCredit)->charge($user_id, $total_credit_deduction, \App\Services\Tariff\Credit\CreditType::MANUAL_ANALYSIS_PRODUCT_UPDATE);
    }


    public function getCategories()
    {
        $category = AnalysisProduct::where('user_id',CRUDBooster::myParentId())
            ->where('source',request()->selectedSource)
            ->whereNotNull('category_id');

        if(isset(request()->selectedValue))$category->where('category_id',request()->selectedValue);

        $catIds = $category->groupBy('category_id')
            ->pluck('category_id');

        if(request()->selectedSource == CompetitiveSource::Core){
            $categories = DrmCategory::whereIn('id',$catIds)
            ->limit(15)
            ->select('id','category_name','category_name_de')
            ->get()->map(function($item){
                return ['id' => $item->id, 'name' => ($item->category_name ?? $item->category_name_de)];
            })->pluck('name', 'id');

        }if(request()->selectedSource == CompetitiveSource::SupplierMp || request()->selectedSource == CompetitiveSource::MarketPlace){
            $categories = Category::whereIn('id',$catIds)
            ->limit(15)
            ->pluck('name', 'id');
        }
        // dd(request()->all(),$categories);
        return response()->json($categories);
    }

    public function searchBenachmark(){
        $user_id = CRUDBooster::myParentId();

        $data = [];
        $data['title'] = "Search Benachmark";
        $data['channels'] = collect($this->analysisService)->map(function ($item) {
            $domain = new $item;
            if($item == 'App\Services\ProductApi\Services\Rainforest') return $domain->driver();
        })->filter()->toArray();

        $data['domains'] = [
            'ae' => ['ae', 'amazon.ae', false],
            'ca' => ['ca', 'amazon.ca', false],
            'cn' => ['cn', 'amazon.cn', false],
            'jp' => ['jp', 'amazon.co.jp', false],
            'com' => ['com', 'amazon.com', false],
            'au' => ['au', 'amazon.com.au', false],
            'be' => ['be', 'amazon.com.be', false],
            'br' => ['br', 'amazon.com.br', false],
            'mx' => ['mx', 'amazon.com.mx', false],
            'tr' => ['tr', 'amazon.com.tr', false],
            'de' => ['de', 'amazon.de', false],
            'eg' => ['eg', 'amazon.eg', false],
            'es' => ['es', 'amazon.es', false],
            'fr' => ['fr', 'amazon.fr', false],
            'in' => ['in', 'amazon.in', false],
            'it' => ['it', 'amazon.it', false],
            'nl' => ['nl', 'amazon.nl', false],
            'pl' => ['pl', 'amazon.pl', false],
            'sa' => ['sa', 'amazon.sa', false],
            'se' => ['se', 'amazon.se', false],
            'sg' => ['sg', 'amazon.sg', false],
            'aca' => ['ca', 'audible.ca', false],
            'ajp' => ['co', 'audible.co.jp', false],
            'auk' => ['co', 'audible.co.uk', false],
            'acom' => ['com', 'audible.com', false],
            'aau' => ['com', 'audible.com.au', false],
            'ade' => ['de', 'audible.de', false],
            'aes' => ['es', 'audible.es', false],
            'afr' => ['fr', 'audible.fr', false],
            'ain' => ['in', 'audible.in', false],
            'ait' => ['it', 'audible.it', false],
        ];

        $data['languages'] = [
            'ar' => ['ar_SA', 'Arabic (ar_SA)',false],
            'nl' => ['nl_NL', 'Dutch (nl_NL)',false],
            'cn' => ['zh_CN', 'Chinese (zh_CN)',false],
            'en' => ['en_US', 'English (United States) (en_US)',false],
            'gb' => ['en_GB', 'English (United Kingdom) (en_GB)',false],
            'be' => ['fr_BE', 'French (Belgium) (fr_BE)',false],
            'fr' => ['fr_FR','French (fr_FR)',false],
            'de' => ['de_DE', 'German (de_DE)',false],
            'it' => ['it_IT', 'Italian (it_IT)',false],
            'jp' => ['ja_JP', 'Japanese (ja_JP)',false],
            'pl' => ['pl_PL', 'Polish (pl_PL)',false],
            'pt' => ['pt_BR','Portugueses (Brazilian) (pt_BR)',false],
            'mx' => ['pt_MX', 'Spenish (Mexican) (pt_MX)',false],
            'es' => ['es_US', 'Spenish (United States) (es_US)',false],
            'se' => ['se_SE', 'Swedish (se_SE)',false],
            'tr' => ['tr_TR', 'Turkish (tr_TR)',false],
        ];

        $data['locations'] = [
            'bl'=>'Belgium',
            'ca'=>'Canada',
            'co'=>'Colombia',
            'fr'=>'France',
            'de'=>'Germany',
            'il'=>'Israel',
            'jp'=>'Japan',
            'mx'=>'Mexico',
            'tr'=>'Turkey',
            'ae'=>'United Arab Emirates',
            'gb'=> 'United Kingdom',
        ];

        $data['plan'] = userCurrentPlan($user_id);

        $data['available_credit'] = ($data['plan']['id'] == 27) ? 'UNLIMITED' : app('App\Services\TariffService')->getAvailableCredet($user_id);

        $data['currency'] = ['aed','amd','ars','aud','awg','azn','bgn','bnd','bob','brl','bsd','bzd','cad','clp','cny','cop','crc','dop','egp','eur','gbp','ghs','gtq','hkd','hnl','huf','idr','ils','inr','jmd','jpy','kes','khr','krw','kyd','kzt','ibp','mad','mnt','mop','mur','mxn','myr','nad','ngn','nok','nzd','pab','pen','php','pln','pyg','qar','rub','sar','sek','sgd','thb','try','ttd','twd','tzs','usd','uyu','vnd','xcd','zar'];

        $data['language'] = app('App\Services\UserService')->getProductLanguage(app('App\Services\UserService')->getProductCountry($user_id));

		return view('admin.cp_analysis.index_benachmark', $data);

    }

    public function get_search_benachmark(Request $req){
        $req->validate([
            'search'=> 'required',
        ]);
        $filter = [];

        $filter['search_term'] = $req->search;
        // $filter['gtin'] = $req->ean;
        $filter['sort_by'] = $req->sort_by;
        $filter['exclude_sponsored'] = $req->exclude_sponsored;
        $filter['language'] = $req->language;
        $filter['currency'] = $req->currency;
        $filter['customer_location'] = $req->customer_location;

        $filter['category_id'] = $req->category_id;
        $filter['refinements'] = $req->refinements;
        $filter['amazon_domain'] = $req->domain;

        $channel = $req->get('channel');
        $domain = collect($this->analysisService)->filter(function ($item) use($channel){
            $domain = new $item;
            if($domain->driver() == $channel) return $domain;
        })->filter()->first();

        $domain = new $domain;
        $cacheKey = http_build_query(array_merge(['user_id' => CRUDBooster::myParentId()], $filter));

        // $domainSearchResponse = $domain->getBenchmarkResponse($filter);
        $domainSearchResponse = Cache::remember($cacheKey, Carbon::now()->addHours(5), function () use($domain, $filter){
            return $domain->getBenchmarkResponse($filter);
        });

        $responseResults = json_decode($domainSearchResponse)->search_results;

        $availableCredit = null;
        //Deduct total show credit for every successfull response
        if(count($responseResults)) {
            // app('App\Services\TariffService')->userCreditAdjust(CRUDBooster::myParentId(), count($responseResults), 11, false);
            $availableCredit = app('App\Services\TariffService')->getAvailableCredet(CRUDBooster::myParentId());
        }
        return response()->json([
            'success' => count($responseResults),
            'results' => $responseResults,
            'credit' => $availableCredit,
            'key' => $cacheKey
        ]);

    }

    public function getAdjustCredit(Request $req){
        $ammount = $req->deduct_amount;
        if(!$ammount) return false;

        // app('App\Services\TariffService')->userCreditAdjust(CRUDBooster::myParentId(), $ammount, 11, false);
        (new ChargeCredit)->charge(CRUDBooster::myParentId(), $ammount, \App\Services\Tariff\Credit\CreditType::BENCHMARK_SEARCH);

        $availableCredit = app('App\Services\TariffService')->getAvailableCredet(CRUDBooster::myParentId());
        return $availableCredit;
    }

    public function get_compare_benachmark(Request $req){
        $cacheKey = $req->key;

        $compareEan = $req->comEAN;

        $cachedData = Cache::get($cacheKey);
        $results = json_decode($cachedData)->search_results;

        $cacheItem = collect($results)->filter(function($item) use($compareEan){
            if($compareEan == $item->asin) return $item;
        })->first();

        $user_id = CRUDBooster::myParentId();
        $ean = $req->ean;

        // Retrieve AnalysisProduct
        $searchItem = AnalysisProduct::where('user_id', $user_id)
            ->where('ean', $ean)
            ->first(['title', 'uvp', 'ean', 'image', 'source', 'product_id']);


        $drmProduct = null;

        if ($searchItem) {
            // Construct query based on source

            // TODO:: PRODUCT_TABLE_CHANGE
            $productQuery = DrmProduct::with(['connected_products:drm_product_id,vk_price,connection_status,channel'])
                ->where('user_id', $user_id);

            if ($searchItem->source == 1) {
                $productQuery->where('id', $searchItem->product_id);
            } elseif ($searchItem->source == 2) {
                $productQuery->where('marketplace_product_id', $searchItem->product_id);
            }

            // Retrieve DrmProduct
            $drmProduct = $productQuery->first(['id', 'uvp']);
    }

        // If $drmProduct is not found or source is 2, retrieve MarketplaceProducts
        if (!$drmProduct || $searchItem->source == 2) {
            $drmProduct = MarketplaceProducts::find($searchItem->product_id,['id','uvp']);
        }

        // Initialize $channels
        $channels = collect();

        if ($drmProduct) {
            // Construct parent channel data
            $parentChannel = [
                'key' => 'Parent',
                'price' => $drmProduct->uvp ? number_format($drmProduct->uvp, 2, ".", ","): "N/A",
                'status' => true,
                'logo' => url('/images/branding/logo.png')
            ];

            $channels->push($parentChannel);

            if ($drmProduct->relationLoaded('connected_products')) {
                // Loop through connected_products relation
                $channelsLists = collect(config('channel.list'));

                $drmProduct->connected_products->each(function ($channel) use ($channelsLists, $channels) {
                    $channelData = $channelsLists->where('type', $channel->channel)->first();

                    $chn = [
                        'key' => ucfirst(strtolower($channelData['name'])),
                        'price' => $channel->vk_price ? number_format($channel->vk_price, 2, ".", ","): "N/A",
                        'status' => $channel->connection_status,
                        'logo' => getChannelLogo($channel->channel)
                    ];
                    $channels->push($chn);
                });
            }
        }

        $status = true;
        if(empty($cacheItem) || !$searchItem) $status = false;

        return response()->json([
            'success' => $status,
            'cache_item' => $cacheItem,
            'search_item' => $searchItem,
            'channels' => $channels
        ]);
    }


    private function getSearchResults($key, $domain = null, $filter = [], $page = 1, $pageSize = 10){
        if (!Cache::has($key)) {
            // If cache for the given key does not exist, set page to 1
            $page = 1;
        }
        $cachedData = Cache::remember($key, Carbon::now()->addHours(2), function () use($domain, $filter){
            return $domain->getBenchmarkResponse($filter);
        });
        $results = json_decode($cachedData)->search_results;

        // Calculate the start index and end index for the requested page
        $startIndex = ($page - 1) * $pageSize;
        $endIndex = $startIndex + $pageSize;

        // Extract the subset of data for the requested page
        $pagedData = array_slice($results, $startIndex, $pageSize);

        return [
            'data' => $pagedData,
            'total_records' => count($results),
            'nxt_avl' => $endIndex < count($results),
            'current_page' => $page
        ];
    }
}
