<?php
namespace App\Http\Controllers\Product;
use Illuminate\Http\Request;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\DB;


use League\Csv\Writer;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

use App\Http\Controllers\Product\CpExportColumn;

use App\Services\CpExportService\CpExport;

class CompetitiveAnalysisExportController
{
    use CpExportColumn;

    public function export(Request $request)
    {
        $req = $request->all();
		$user_id = CRUDBooster::myId();
		$file_ext = $req['file_ext'];
        $product_ids = $req['product_id'] ? explode(',', $req['product_id']) : [];
        $isCheckAll = $req['isCheckAll'] && $req['isCheckAll'] === 'true';
        $archive = $req['archive'];
        $search_by_field_column = $req['search_by_field_column'];
        $search_by_channel = $req['search_by_channel'];
        $search_by_merchant = $req['search_by_merchant'];
        $search_by_brand = $req['search_by_brand'];
        $search_by_supplier = $req['search_by_supplier'];
        $search_by_source = $req['search_by_source'];
        $sort_by_sale = $req['sort_by_sale'];
        $search_by_rating = $req['search_by_rating'];
        $q = $req['q'];

        $saved_colum = DB::table('drm_user_saved_columns')
        ->where('user_id',CRUDBooster::myId())
        ->where('table_name','analysis_products')->pluck('columns')->first();

        $saved_colum = collect($saved_colum)->map(function($item) {
            return strtolower($item);
        })
        ->first();

        $profit_column = $_REQUEST['compare_type'] ?? 'uvp';

        $job_payload = [
            'user_id' => $user_id,
            'checked_all' => $isCheckAll ? 'yes' : 'no',
            'ids' => $product_ids,
            'ext' => $file_ext,
            'archive' => $archive,
            'search_by_field_column' => $search_by_field_column,
            'search_by_channel' => $search_by_channel,
            'search_by_merchant' => $search_by_merchant,
            'search_by_brand' => $search_by_brand,
            'search_by_supplier' => $search_by_supplier,
            'search_by_source' => $search_by_source,
            'sort_by_sale' => $sort_by_sale,
            'search_by_rating' => $search_by_rating,
            'q' => $q,
            'cols' => $saved_colum,
            'profit_column' => $profit_column,
            'created_at' => now(),
            'updated_at' => now(),
            'step' => 1,
        ];

        $payload_id = DB::table('cp_export_request')->insertGetId([
            'user_id' => $user_id,
            'payload' => json_encode($job_payload),
        ]);

        app(CpExport::class)->exportStart($user_id, $product_ids, $payload_id, $isCheckAll);

        return response()->json([
            'success' => true,
            'message' => __('Process running on background'),
        ]);
    }
}
