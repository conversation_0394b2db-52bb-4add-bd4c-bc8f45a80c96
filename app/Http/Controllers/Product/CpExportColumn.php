<?php
namespace App\Http\Controllers\Product;

trait CpExportColumn
{

	private function googleShoppingColumn($r, $file_ext, $show_rating = false)
	{
		// $last_sync = $r['google_last_sync'];
		// $dt_price = ($r['dt_price'] != 0 && $r['dt_price'] != null) ? number_format((float)$r['dt_price'], 2, ',', '.') : '...';

		$price = $r['google_price'] ? number_format((float)$r['google_price'], 2, ',', '.') .'€': '...';
        return $price;

		$rating = $r['google_rating'];

        $rating = $rating ? number_format((float)$rating, 2, ',', '.') .' / 5,0': '...';

		if($file_ext != 'pdf') return $price;

		$weekly_sales_estimate = $r['google_weekly_sales_estimate'];
        $monthly_sales_estimate = $r['google_monthly_sales_estimate'];

        $weekly_sales_estimate = $weekly_sales_estimate ? number_format((float)$weekly_sales_estimate, 2, ',', '.') .'€': '...';
        $monthly_sales_estimate = $monthly_sales_estimate ? number_format((float)$monthly_sales_estimate, 2, ',', '.') .'€': '...';

		$extimation_html = "<span style='font-size:10px;'>
            Weekly Sale Estimation: $weekly_sales_estimate <br>
            Monthly Sale Estimation: $monthly_sales_estimate
        </span>";

        $rating_html = $show_rating && $last_sync ? "<span style='font-size:10px;'>Rating: $rating</span>" : '';
        return "$dt_price <br> $price <br> $extimation_html <br> $rating_html";
	}


	private function ebayColumn($r, $file_ext, $show_rating = false)
	{
		// $last_sync = $r['ebay_last_sync'];

		$price = $r['ebay_price'] ? number_format((float)$r['ebay_price'], 2, ',', '.') .'€': '...';
        return $price;
        
		$rating = $r['ebay_rating'];

        $rating = $rating ? number_format((float)$rating, 2, ',', '.') .' / 5,0': '...';

		if($file_ext != 'pdf') return $price;

		$weekly_sales_estimate = $r['ebay_weekly_sales_estimate'];
        $monthly_sales_estimate = $r['ebay_monthly_sales_estimate'];

        $weekly_sales_estimate = $weekly_sales_estimate ? number_format((float)$weekly_sales_estimate, 2, ',', '.') .'€': '...';
        $monthly_sales_estimate = $monthly_sales_estimate ? number_format((float)$monthly_sales_estimate, 2, ',', '.') .'€': '...';

		$extimation_html = "<span style='font-size:10px;'>
            Weekly Sale Estimation: $weekly_sales_estimate <br>
            Monthly Sale Estimation: $monthly_sales_estimate
        </span>";

        $rating_html = $show_rating && $last_sync ? "<span style='font-size:10px;'>Rating: $rating</span>" : '';
        return "$price <br> $extimation_html <br> $rating_html";
	}

	private function amazonColumn($r, $file_ext, $show_rating = false)
	{
		// $last_sync = $r['amazon_last_sync'];
		$price = $r['amazon_price'] ? number_format((float)$r['amazon_price'], 2, ',', '.') .'€': '...';
        return $price;

		$rating = $r['amazon_rating'];

        $rating = $rating ? number_format((float)$rating, 2, ',', '.') .' / 5,0': '...';

		if($file_ext != 'pdf') return $price;

		$weekly_sales_estimate = $r['amazon_weekly_sales_estimate'];
        $monthly_sales_estimate = $r['amazon_monthly_sales_estimate'];

        $weekly_sales_estimate = $weekly_sales_estimate ? number_format((float)$weekly_sales_estimate, 2, ',', '.') .'€': '...';
        $monthly_sales_estimate = $monthly_sales_estimate ? number_format((float)$monthly_sales_estimate, 2, ',', '.') .'€': '...';

		$extimation_html = "<span style='font-size:10px;'>
            Weekly Sale Estimation: $weekly_sales_estimate <br>
            Monthly Sale Estimation: $monthly_sales_estimate
        </span>";

        $rating_html = $show_rating && $last_sync ? "<span style='font-size:10px;'>Rating: $rating</span>" : '';
        return "$price <br> $extimation_html <br> $rating_html";
	}

	private function channelHtmlData($channel, $channel_price, $row, $show_rating = false)
    {
        // $price = $weekly_sales_estimate = $monthly_sales_estimate = $rating = $last_sync = null;
        // $has_estimation = false;

        if($channel['channel'] === '5')
        {
        	return $this->amazonColumn($row, 'pdf', $show_rating);


            // $has_estimation = true;
            // $last_sync = $row['amazon_last_sync'];
            // $l = $last_sync? '...' : '<span style="color: #008000; font-size: 30px">_</span>';

            // $price = $row['amazon_price'] ? number_format((float)$row['amazon_price'], 2, ',', '.') : $l;
            // $weekly_sales_estimate = $row['amazon_weekly_sales_estimate'];
            // $monthly_sales_estimate = $row['amazon_monthly_sales_estimate'];
            // $rating = $row['amazon_rating'];


        }elseif ($channel['channel'] === '4') {

        	return $this->ebayColumn($row, 'pdf', $show_rating);

            // $has_estimation = true;
            // $last_sync = $row['amazon_last_sync'];
            // $l = $last_sync? '...' : '<span style="color: #008000; font-size: 30px">_</span>';

            // $price = $row['ebay_price'] ? number_format((float)$row['ebay_price'], 2, ',', '.') : $l;
            // $weekly_sales_estimate = $row['ebay_weekly_sales_estimate'];
            // $monthly_sales_estimate = $row['ebay_monthly_sales_estimate'];
            // $rating = $row['ebay_rating'];
            // $last_sync = $row['ebay_last_sync'];
        }

        return $channel_price;

        // if(!$has_estimation) return $price;

        // $extimation_html = "<span>
        //     Weekly Sale Estimation: $weekly_sales_estimate <br>
        //     Monthly Sale Estimation: $monthly_sales_estimate
        // </span>";

        // $rating_html = $show_rating && $last_sync ? 'Rating: '.$rating : '';


        // return "$price <br> $extimation_html <br> $rating_html";
    }





}