<?php

namespace App\Http\Controllers\product;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Services\CustomIndustryTemplateService;
use Illuminate\Support\Facades\Validator;
use App\CustomIndustryTemplate;
use App\DrmProduct;
use App\Jobs\UserConnectedCustomIndustryTemplateDelete;
use App\Jobs\JobChainingCustomIndustryTemplate;
use App\Jobs\UserTemplateDelete;
use DB;

class CustomIndustryTemplateController extends Controller
{
    private CustomIndustryTemplateService $customIndustryTmpService;

    public function __construct(CustomIndustryTemplateService $customIndustryService)
    {
        $this->customIndustryTmpService = $customIndustryService;
    }

    public function index(Request $request)
    {
        $currentUserId = CRUDBooster::myParentId();

        $data['page_title'] = "Industry Templates";

        $data['customIndustryTmp'] = $this->customIndustryTmpService->all(array_merge($request->all(),[
            'user_id' => $currentUserId,
            "search_by_field_column" => "name"
        ]));

        $data['is_superadmin'] = CRUDBooster::isSuperadmin();

        return view('admin.custome_industry_template.index', $data);
    }

    public function getAdd() 
    {
        $data['page_title'] = __('Industry Template Create');
        $user_id = CRUDBooster::myParentId();

        $data['is_superadmin'] = CRUDBooster::isSuperadmin();

        $fetched_fields = $this->customIndustryTmpService->userFetchPreDefineFields($user_id);
        $html = '';

        if($fetched_fields){
            foreach($fetched_fields['pre_define_fields'] as $field){
                $html .= '<option value="'.$field.'">'.$field.'</option>';
            }
        }

        $data['pre_define_fields'] = $html;
        $data['fetched_fields'] = $fetched_fields;

        return view('admin.custome_industry_template.add', $data);
    }

    public function postSaveIndustryTemplate(Request $request)
    {
        $isSuperAdmin = CRUDBooster::isSuperadmin();

        $rules = [
            'industry_tmp_name' => 'required'
        ];

        if($isSuperAdmin){
            $rules['publish_to_user'] = 'nullable';
            $rules['publish_to_mp'] = 'nullable';
        }
        
        if(!empty($request->input('industry_tmp_fields'))){
            foreach($request->input('industry_tmp_fields') as $key => $value){
                foreach($value as $index => $item){
                    $rules['industry_tmp_fields.'.$key.'.'.$index] = 'required';
                    break;
                }
            }
        }
        
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            $message = $validator->errors()->all(); 
            return redirect()->back()->with(['message' => implode(', ', $message), 'message_type' => 'danger']);
        }

        $user_id = CRUDBooster::myParentId();

        if($isSuperAdmin){
            $industry_template_exist = CustomIndustryTemplate::where('name', 'LIKE', trim($request->industry_tmp_name))->exists();
        }else{
            $industry_template_exist = CustomIndustryTemplate::where(['user_id' => $user_id])
                            ->where('name', 'LIKE', trim($request->industry_tmp_name))
                            ->exists();
        }

        if($industry_template_exist){
            return redirect()->back()->with(['message' => __("Template Name Already Exists!"), 'message_type' => 'error']);
        }

        try {
            $insert_data = [
                'user_id' => $user_id, 
                'name' => trim($request->industry_tmp_name),
                'fields' => $request->industry_tmp_fields,
            ];

            if($isSuperAdmin){
                if(isset($request->publish_to_user)){
                    $insert_data['publish_to_customer'] = $request->publish_to_user;
                }
                if(isset($request->publish_to_mp)){
                    $insert_data['publish_to_mp'] = $request->publish_to_mp;
                }
            }

            $template = CustomIndustryTemplate::create($insert_data);

            if($template)
            {
                $fields = $this->formateTemplateData($template->fields ?? []);

                // OK
                $newId = DB::table('industry_templates')
                ->insertGetId([
                    'user_id' => $template->user_id,
                    'name' => $template->name,
                    'created_at' => $template->created_at,
                    'updated_at' => $template->updated_at,
                    'fields' => !empty($fields) ? json_encode($fields) : null,
                ]);

                $this->syncTemplateFieldData($newId, $fields);

                DB::table('custom_industry_templates')->where('id', $template->id)->update(['new_id' => $newId]);
            }

        } catch (\Exception $e) {
            return back()->withErrors($e->getMessage());
        }

        return redirect('/admin/custom-industry-templates/')->with(['message' => __('Industry Template Created Successfully!'), 'message_type' => 'success']);
    }


    /**
     * Generate template fields data
     */
    private function syncTemplateFieldData($newtemplateId, $fields)
    {

        if(empty($newtemplateId)) return;

        DB::table('industry_template_fields')->where('industry_template_id', $newtemplateId)->delete();


        if(empty($fields)) return;

        $data = array_map(function($item) use ($newtemplateId) {
            return [
                'industry_template_id' => $newtemplateId,
                'name' => $item['name'],
                'field' => json_encode($item),
            ];
        }, $fields);


        try {
            DB::table('industry_template_fields')->insert($data);
        } catch(\Exception $e) {}
    }


    /**
     * Formate template data
    */
    private function formateTemplateData($templateFields)
    {
        $fields = array_map(function($field) {
            return [
                'name' => $field['name'],
                'placeholder' => '',
                'required' => $field['required'],
                'max_length' => 120,
                'value' => $field['value'] ?? null,
            ];

        }, $templateFields);

        return !empty($fields) ? array_values($fields) : [];
    }

    public function getEditTemplate($id)
    {
        $data['page_title'] = __('Industry Template Edit');

        $user_id = CRUDBooster::myParentId();
        $data['custom_template'] = $this->customIndustryTmpService->getTemplateById($id);
        $data['is_superadmin'] = CRUDBooster::isSuperadmin();

        return view('admin.custome_industry_template.edit', $data);
    }

    public function postEditSaveIndustryTemplate(Request $request)
    {
        $isSuperAdmin = CRUDBooster::isSuperadmin();

        $rules = [
            'industry_tmp_name' => 'required'
        ];

        if($isSuperAdmin){
            $rules['publish_to_user'] = 'nullable';
            $rules['publish_to_mp'] = 'nullable';
        }
        
        if(!empty($request->input('industry_tmp_fields'))){
            foreach($request->input('industry_tmp_fields') as $key => $value){
                foreach($value as $index => $item){
                    $rules['industry_tmp_fields.'.$key.'.'.$index] = 'required';
                    break;
                }
            }
        }
        
        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            $message = $validator->errors()->all(); 
            return redirect()->back()->with(['message' => implode(', ', $message), 'message_type' => 'danger']);
        }

        $user_id = CRUDBooster::myParentId();

        if($isSuperAdmin){
            $industry_template_exist = CustomIndustryTemplate::where('name', 'LIKE', trim($request->industry_tmp_name))->where('id', '!=', $request->tmp_id)->exists();
        }else{
            $industry_template_exist = CustomIndustryTemplate::where(['user_id' => $user_id])
                            ->where('id', '!=', $request->tmp_id)
                            ->where('name', 'LIKE', trim($request->industry_tmp_name))
                            ->exists();
        }

        if($industry_template_exist){
            return redirect()->back()->with(['message' => __("Template Name Already Exists!"), 'message_type' => 'error']);
        }

        try {
            $update_data = [ 
                'name' => trim($request->industry_tmp_name),
                'fields' => $request->industry_tmp_fields,
            ];

            if($isSuperAdmin){
                if(isset($request->publish_to_user)){
                    $update_data['publish_to_customer'] = $request->publish_to_user;
                }else{
                    $update_data['publish_to_customer'] = 0;
                }

                if(isset($request->publish_to_mp)){
                    $update_data['publish_to_mp'] = $request->publish_to_mp;
                }else{
                    $update_data['publish_to_mp'] = 0;
                }
            }

            $newId = DB::table('custom_industry_templates')->where('id', $request->tmp_id)->value('new_id');

            if(CustomIndustryTemplate::where(['id' => $request->tmp_id, 'user_id' => $user_id])->update($update_data) && $newId)
            {
                $fields = $this->formateTemplateData($update_data['fields']);

                // OK
                DB::table('industry_templates')->where('id', $newId)->update([
                    'name' => $update_data['name'],
                    'updated_at' => now(),
                    'fields' => empty($fields) ? json_encode($fields) : null,
                ]);

                $this->syncTemplateFieldData($newId, $fields);
            }

        } catch (\Exception $e) {
            return back()->withErrors($e->getMessage());
        }

        return redirect('/admin/custom-industry-templates/')->with(['message' => __('Industry Template Updated Successfully!'), 'message_type' => 'success']);
    }

    public function postCheckTemplateProducts()
    {
        $user_id = CRUDBooster::myParentId();
        $template_name = $_REQUEST['template_name'];
        $lang = app('App\Services\UserService')->getLang($user_id);

        $product_count = $this->customIndustryTmpService->checkTemplateProducts($user_id, $lang, $template_name);
        
        return response()->json([
            'template_count' => $product_count,
        ]);
    }

    public function postUpdateTemplate(Request $request)
    {
        $form_data = $request->all();
        $user_id = CRUDBooster::myParentId();
        $template_id = $form_data['template_id'];
        $column_name = $form_data['column_name'];

        $template_name = '';
        if(isset($form_data['template_name'])){
            $template_name = $form_data['template_name'];
        }

        $template_fields = '';
        if(isset($form_data['template_fields'])){
            $template_fields = $form_data['template_fields'];
        }

        if($template_name){
            // $update_status = CustomIndustryTemplate::where(['id' => $template_id, 'user_id' => $user_id])->update(['name' => $template_name]);
            $updated = $this->customIndustryTmpService->adminTemplateUpdate($user_id, $template_id, $column_name, $template_name);

            if($updated){
                return redirect('/admin/custom-industry-templates/')->with(['message' => __('Industry Template Name Updated Successfully!'), 'message_type' => 'success']);
            }
        }
    }

    public function postCheckAdminCustomTemplateProducts()
    {
        $template_name = $_REQUEST['template_name'];
        $message = '';
        $temp_conn_status = false;

        $user_count = $this->customIndustryTmpService->checkAdminTemplateProducts($template_name);

        if($user_count > 0){
            $message .= 'You have total ' . $user_count . ' users connected with this template. <br>';
        }

        $total_count = $this->customIndustryTmpService->checkAdminTemplateMP($template_name);

        if($total_count > 0){
            if(!empty($message)){
                $message .= 'And ' . $total_count . ' products connected from Marketplace with this template. <br>';
            }else{
                $message .= 'You have total ' . $total_count . ' products connected from Marketplace with this template. <br>';
            }
        }

        if(!empty($message)){
            $message .= 'It can not be deleted.';
            $temp_conn_status = true;
        }
    
        return response()->json([
            'success' => true,
            'message' => $message,
            'status' => $temp_conn_status
        ]);
        
    }

    public function postCheckAdminTemplateProducts()
    {
        $table_name = $_REQUEST['table_name'];
        $template_name = $_REQUEST['template_name'];

        if($table_name == 'drm_products'){
            $user_count = $this->customIndustryTmpService->checkAdminTemplateProducts($template_name);

            if($user_count > 0){
                $message = 'You have total ' . $user_count . ' users connected with this template. It can not be updated.';
            }else{
                $message = 'You have total ' . $user_count . ' users connected with this template.';
            }

            return response()->json([
                'success' => true,
                'total_count' => $user_count,
                'message' => $message
            ]);
        }else if($table_name == 'marketplace_products'){
            $total_count = $this->customIndustryTmpService->checkAdminTemplateMP($template_name);

            if($total_count > 0){
                $message = 'You have total ' . $total_count . ' products connected with this template. It can not be updated.';
            }else{
                $message = 'You have total ' . $total_count . ' products connected with this template.';
            }
        
            return response()->json([
                'success' => true,
                'total_count' => $total_count,
                'message' => $message
            ]);
        }
        
    }

    public function postAdminTemplateUpdate()
    {
        $user_id = CRUDBooster::myParentId();
        $table_id = $_REQUEST['table_id'];                   
        $table_column = $_REQUEST['table_column'];         
        $value = $_REQUEST['value'];

        $updated = $this->customIndustryTmpService->adminTemplateUpdate($user_id, $table_id, $table_column, $value);
        
        if($updated){
            if($table_column == 'publish_to_customer'){
                if($value == 0){
                    $message = 'Tempate Hide from User';
                }else{
                    $message = 'Tempate Pulished to User';
                }
            }else if($table_column == 'publish_to_mp'){
                if($value == 0){
                    $message = 'Tempate Hide from MP';
                }else{
                    $message = 'Tempate Pulished to MP';
                }
            }
            
            return response()->json([
                'success' => true,
                'message' => $message
            ], 200);
        }else{
            return response()->json([
                'success' => false,
                'message' => 'Somthing Went Wrong!'
            ], 402);
        }
    }

    public function postUserConnTempDelete()
    {
        $user_id = CRUDBooster::myParentId();
        $lang = app('App\Services\UserService')->getLang($user_id);

        $temp_id = $_REQUEST['tmpl_id'];
        $temp_name = $_REQUEST['tmpl_name'];

        try{
            $conn_temp_ids = DrmProduct::where('user_id', $user_id)
            ->whereJsonLength('drm_products.title->'.$lang, '>', 0)
            ->where('drm_products.industry_template_data', 'LIKE', '%' . $temp_name . '%')->pluck('id')->toArray();
    
            $jobs = [];

            foreach (array_chunk($conn_temp_ids, 500) as $ids) {
                $jobs[] = new UserConnectedCustomIndustryTemplateDelete($ids, $user_id);
            }

            $jobs[] = new UserTemplateDelete($temp_id);

            JobChainingCustomIndustryTemplate::withChain($jobs)->dispatch();
    
            return response()->json([
                'success' => true,
                'message' => 'Deleting Process Started. Connected Template Deleting Process May Take Some Times.'
            ], 200);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function postTemplateDelete()
    {
        $temp_id = $_REQUEST['tmpl_id'];

        try{

            $newId = DB::table('custom_industry_templates')->where('id', $temp_id)->value('new_id');
            if($newId)
            {
                // OK
                DB::table('industry_templates')->where('id', $newId)->delete();
                DB::table('industry_template_fields')->where('industry_template_id', $newId)->delete();
            }

            $this->customIndustryTmpService->userTemplateDelete($temp_id);

            return response()->json([
                'success' => true,
                'message' => 'Template Deleted Successfully!'
            ], 200);

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function postAddField(Request $request)
    {
        $temp_field = [$request->industry_tmp_field];
        $user_id = CRUDBooster::myParentId();

        $pre_define_fields_exists = $this->customIndustryTmpService->userPreDefineFieldsExists($user_id);

        if(!$pre_define_fields_exists){
            $field_add = $this->customIndustryTmpService->userPreDefineFieldsAdd($user_id, $temp_field);
            return $field_add;
        }else{
            $field_update = $this->customIndustryTmpService->userPreDefineFieldsUpdate($user_id, $temp_field);
            return $field_update;
        }
    }

    public function getFetchFields()
    {
        $user_id = CRUDBooster::myParentId();
        $html = '';

        $fetched_fileds = $this->customIndustryTmpService->userFetchPreDefineFields($user_id);

        if($fetched_fileds){
            foreach($fetched_fileds['pre_define_fields'] as $field){
                $html .= '<option value="'.$field.'">'.$field.'</option>';
            }
        }

        return response()->json([
            'success' => true,
            'data' => $html
        ]);
    }

    public function getFetchFieldsToDelete()
    {
        $user_id = CRUDBooster::myParentId();
        $html = '';

        $fetched_fields = $this->customIndustryTmpService->userFetchPreDefineFields($user_id);

        if($fetched_fields){
            $html .= '<table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Fields</th>
                        <th class="pull-right">Action</th>
                    </tr>
                </thead>
                <tbody>';
                
                foreach ($fetched_fields['pre_define_fields'] as $item){
                    $html .= '<tr>
                        <td>
                            '.$item.'
                        </td>
                        <td>
                            <span class="btn btn-danger btn-xs pull-right delete_pre_define_field" data-field="'.$item.'" data-id="'.$fetched_fields['id'].'"><i class="fa fa-trash"></i></span>
                        </td>
                    </tr>';
                }
                    
            $html .= '</tbody>
                </table>';
        }else{
            $html .= '<h5>
                        No Pre Define Field Found !!!
                    </h5>';
        }

        return response()->json([
            'success' => true,
            'data' => $html
        ]);
    }

    public function postDeleteField(Request $request)
    {
        $field_id = $request->field_id;
        $pre_define_field = [$request->pre_define_field];
        $user_id = CRUDBooster::myParentId();

        $field_deleted = $this->customIndustryTmpService->userPreDefineFieldsDelete($user_id, $field_id, $pre_define_field);

        return $field_deleted;
    }
}
