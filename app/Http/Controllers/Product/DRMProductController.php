<?php

namespace App\Http\Controllers\Product;

use App\CustomEan;
use App\DeliveryCompany;
use App\DrmProduct;
use App\Jobs\ChannelManager\AutoTransfer;
use App\Models\ChannelProduct;
use App\Services\UserService;
use App\Shop;
use App\DrmSupplierCategory;
use App\Http\Controllers\AdminDrmImportsController;
use App\Http\Controllers\Controller;
use App\Models\DRMProductCategory;
use App\Services\ChannelProductService;
use App\Services\DRMProductService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Services\DRMCategoryService;
use App\Country;
use App\DrmImport;
use App\Services\ProductBundleService;
use Illuminate\Support\Facades\Storage;
use App\Enums\Apps;
use App\Enums\Channel;
use App\Services\AppStoreService;
use App\Services\Modules\Import\ImportService;
use Illuminate\Support\Str;
use Throwable;
use App\Jobs\DisconnectedProductDelete;
use App\Jobs\GenerateCsv;
use App\KeepaData;
use App\Models\DrmCategory;
use App\Models\Product\AnalysisProduct;
use App\Option;
use App\Services\Keepa\Automagic;
use App\Services\Keepa\Keepa;
use App\Services\ProductApi\TransferProduct;
use App\TrendCategories;
use Illuminate\Support\Facades\File;
use League\Csv\Writer;
use App\Models\Marketplace\MarketplaceAllowedChannel;
use App\Jobs\DestroyProduct;
use App\DropmatixProductBrand;
use App\CustomIndustryTemplate;
use App\Jobs\ProcessDRMBulkTaxTypeUpdate;
use App\Jobs\ProcessDRMBulkBrandUpdate;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Export\MarketplaceProductsExport;

class DRMProductController extends Controller
{
    private DRMProductService $productService;
    private DRMCategoryService $categoryService;
    private string $slug = 'drm_products';
    private int $module_id;
    public $channels;
    public $updated_channel;

    public function __construct(DRMProductService $productService, DRMCategoryService $categoryService)
    {
        $this->productService = $productService;
        $this->categoryService = $categoryService;
        $this->module_id = config('modules.list.'.$this->slug);
    }

    public function index(Request $request)
    {
        redirectToV2('/products');
        
        if (CRUDBooster::myPrivilegeId() == 12) {
            return redirect(CRUDBooster::adminPath('marketplace/new-store-details'));
        }

        $data = array();
        $data['bootTime'] = ex_time();
        $currentUserId = CRUDBooster::myParentId();
        $isSuperAdmin = CRUDBooster::isSuperadmin();

        if (!$currentUserId) {
            return redirect(CRUDBooster::adminPath('login'));
        }
        if(!$this->canAccess('view')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }
        if(!$isSuperAdmin){
            $user_paln = app(\App\Http\Controllers\AdminDrmImportsController::class)->importProductCheck($currentUserId);

            if (!app(AppStoreService::class)->checkImportPayment($currentUserId)) {
                if(!checkTariffEligibility($currentUserId)){
                    return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
                }else if(checkTariffEligibility($currentUserId) && $user_paln['plan'] != '500 Free Products'){
                    return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
                }
            }
        }

//        dump('Checked import plan: '.ex_time().' seconds');

        if(!deluxeOrHigher($currentUserId)){
            $data['universal_export_purchased'] = app(AppStoreService::class)->checkAppPurchased(Apps::UNIVERSAL_EXPORT, $currentUserId);
        }else{
            $data['universal_export_purchased'] = true;
        }


        $data['languageId'] = app('App\Services\UserService')->getProductCountry($currentUserId);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($data['languageId']);

        $data['page_title'] = __('products.Drm_product_title');
        // if (isLocal() || in_array(CRUDBooster::myParentId(), [212]) ) {
            $data['page_title'] = '&nbsp;';
            $data['page_icon']  = true;
        // }

        $data['trans_cat'] = "category_name_" . $data['lang'];
        $data['countries'] = Country::where(['is_active' => 1, 'status' => 1])->get();
        $data['selectedCountry'] = $data['countries']->where('id', $data['languageId'])->first();

        $data['channels'] = app(ChannelProductService::class)->getUserShops($currentUserId,$data['lang']);

        $data['products'] = $this->productService->all(array_merge($request->all(), [
            'user_id' => !CRUDBooster::isSuperadmin() ? $currentUserId : '',
            'lang' => $data['lang'] ?? "de",
        ]));



        $defaultFilter = '';
        if(!empty($_REQUEST['search_by_field_column']))
        {
            \App\Option::updateOrCreate([
                'user_id' => \CRUDBooster::myId(),
                'option_key' => 'default_product_filter',
                'option_group' => 'default_filter',
            ],
            [
                'option_value' => $_REQUEST['search_by_field_column'],
            ]);
        } else {
            $defaultFilter = \App\Option::where([
                            'user_id' => \CRUDBooster::myId(),
                            'option_key' => 'default_product_filter',
                            'option_group' => 'default_filter',
                        ])
                        ->value('option_value');
        }


        $data['defaultFilter'] = $defaultFilter;

        foreach($data['products'] as $product){
            $stocks = json_decode($product->stock_of_status, true);
            $product->stock_of_status = $stocks ?? [];
//            $product->stock = $stocks['1000'] ?? $product->stock;
        }

        $data['global_rq'] = @get_option('rq_default', 'rq', $currentUserId)->option_value;

        $data['template_imports'] = array();

        $template_purchased = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, $currentUserId);
        if ($template_purchased) {
            $data['template_imports'] = app(ImportService::class)->importsHasTemplate($currentUserId);
        }

        // $data['categories'] = $this->categoryService->getUserCategories($currentUserId, $data['languageId']);
        $data['categories'] = DrmCategory::where(['user_id' => $currentUserId , 'country_id' => $data['languageId']])->get(['id',"category_name_" . $data['lang']]);

        $data['automagic_limit'] = Option::where(['option_group' => 'automagic_product_update', 'user_id' => $currentUserId])->get()->keyBy('option_key');
        $data['token_credits'] = @get_token_credit(CRUDBooster::myParentId());

        $product_with_industry_template = DrmProduct::where('user_id', $currentUserId)->whereNotNull('industry_template_data')->get();
        $product_has_industry_template = false;

        if($product_with_industry_template){
            foreach($product_with_industry_template as $product){
                if($product->IndustryTemplateHasName()){
                    $product_has_industry_template = true;
                    break;
                }
            }
        }

        $data['analysis_sync'] = DB::table('cms_users')->where('id', $currentUserId)->value('one_one_sync');
        $data['product_has_industry_template'] = $product_has_industry_template;

        $data['stateNames'] = ['1000' => 'Neu', '1500' => 'Neu Sonstige', '1750' => 'Neu Mit Fehlern', '2000' => 'Vom Hersteller', '2500' => 'Vom Verkäufer', '2750' => 'Neuwertig', '3000' => 'Gebraucht', '4000' => 'Sehr Gut', '5000' => 'Gut', '6000' => 'Akzeptabel', '7000' => 'Als Ersatzteil'];

        $onboarding_done = true;
        if (checkTariffEligibility($currentUserId)) {
            $onboarding_done = false;

            $onboarding_info = DB::table('dashboard_onboarding')->where('user_id', $currentUserId)->first();
            if (!empty($onboarding_info)) {
                $onboarding_done = ($onboarding_info->visible == 0 ) ? true : false;
            }
        }

        $data['onboarding_done'] = $onboarding_done;

        $data['dropmatix_brands'] = DropmatixProductBrand::where('user_id', $currentUserId)->pluck('brand_name', 'id')->toArray();

        $data['user_channel'] = $this->userChannelAddition($currentUserId);

        $data['deluxe_or_higher'] = deluxeOrHigher($currentUserId);

        $show_min_max_columns = false;
        if ($data['deluxe_or_higher']) {
            $show_min_max_columns = true;
        }
        $data['show_min_max_columns'] = $show_min_max_columns;

        $data['blocked_channels'] = userBlockedChannels($currentUserId, $data['languageId']);

        return view('admin.drm_products.index', $data);
    }


    public function channelProgressJson()
    {
        $currentUserId = CRUDBooster::myParentId();
        $isSuperAdmin = CRUDBooster::isSuperadmin();

        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $shops = [];
        if (!$isSuperAdmin) {
            $shops = app(ChannelProductService::class)->getUserShops($currentUserId, $lang);
        }

        $res = [];

        if(empty($shops))
        {
            return response()->json(['success' => true, 'data' => $res]);
        }

        foreach ($shops as $shop) {
            $progress = $this->averageChannelPercentage($shop->id);
            if( $progress >= 36 && $$progress <= 65.99 ){
                $color = "orange";
            } else if( $progress >= 66 ){
                $color = "green";
            }else {
                $color = "red";
            }

            $res[] = [
                'shop' => $shop->id,
                'progress' => $progress,
                'color' => $color,
                'show' => $progress < 1 ? 'no' : 'yes',
            ];
        }

        return response()->json(['success' => true, 'data' => $res]);
    }



    public function averageChannelPercentage($shop_id)
    {
        $userId = CRUDBooster::myParentId();
        if (!$userId) {
            return redirect(CRUDBooster::adminPath('login'));
        }

        $languageId = app('App\Services\UserService')->getProductCountry($userId);
        $products = \App\Models\ChannelProduct::where(['user_id' => $userId, 'country_id' => $languageId, 'shop_id' => $shop_id])->get();

        $field_fill_up_count = [];

        if ($products) {
            foreach ($products as $product) {

                $all_fields = \App\Enums\Product::ALL_FIELDS;

                $ind_temp_field = $product->industry_template_data;

                if(!is_array($ind_temp_field)){
                    $ind_temp_field = json_decode($ind_temp_field, true);
                }

                $mandatory_ind_temp_fileds = [];

                if($ind_temp_field){

                    if( !in_array(key($ind_temp_field), array_keys(config('industry_template'))) ){
                        $associate_temp_fields = CustomIndustryTemplate::where('name', trim(key($ind_temp_field)))->value('fields');

                        if($associate_temp_fields){
                            array_walk($associate_temp_fields, function($field) use(&$mandatory_ind_temp_fileds){
                                if(isset($field['required']) && $field['required']){
                                    $mandatory_ind_temp_fileds[] = $field['name'];
                                }
                            });
                        }

                    }
                }


                if(!empty($mandatory_ind_temp_fileds)){
                    $all_fields = array_merge($all_fields, $mandatory_ind_temp_fileds);
                }

                $count = 0;
                $total_fill = count($all_fields);
                foreach ($all_fields as $fields) {
                    // dd($product, $fields);
                    // if (!empty($product[$fields])) {
                    //     $count++;
                    // }

                    if($fields == 'title'){
                        $title_field_len = strlen($product->getTitleFieldAttribute());
                        if($title_field_len > 0){
                            $count++;
                        }
                    }else if($fields == 'description'){
                        $description_field_len = strlen($product->getTransDescriptionAttribute());
                        if($description_field_len > 0){
                            $count++;
                        }
                    }else if(in_array($fields, $mandatory_ind_temp_fileds)){

                        if ($product->country_id == 2) {
                            $lang = data_get(request(), 'lang', 'en');
                        } else {
                            $lang = data_get(request(), 'lang', 'de');
                        }

                        $ind_temp_field_val = strlen($product->getIndustryTemplateFieldAttribut($fields, $lang));
                        if($ind_temp_field_val > 0){
                            $count++;
                        }

                    }else{
                        if (!empty($product->$fields)) {
                            $count++;
                        }
                    }
                }

//                dd(\App\Enums\Channel::CHRONO24);

                if ($product->channel == \App\Enums\Channel::CHRONO24) {
                    $total_fill = count($all_fields) + count(\App\Enums\Product::EXTRA_FIELD);
                    $extra_field = \App\Enums\Product::EXTRA_FIELD;
                    if ($product->attributes) {
                        $attr = $product->attributes['additonal_data'];
                        if ($attr) {
                            foreach ($attr as $index => $value) {
                                if (!empty($value)) {
                                    if (in_array($index, $extra_field)) {
                                        $count++;
                                    }
                                }
                            }
                        }
                    }
                }

                $percentage = ($count * 75) / $total_fill;

                $total_seo_item = 2;
                $total_seo_percentage = 20;
                $title_seo_percentage = 0;
                $short_desc_seo_percentage = 0;
                $title_len = strlen( preg_replace("/\s+/", "", strip_tags($product->getTitleFieldAttribute())) );
                $short_desc_len = strlen( preg_replace("/\s+/", "", strip_tags($product->getShortDescAttribute())) );

                if($title_len > 0){
                    if($title_len >= 56 && $title_len <= 80){
                        $title_seo_percentage = ($total_seo_percentage/$total_seo_item);
                    }else{
                        $title_seo_percentage = ($total_seo_percentage/$total_seo_item) / 2;
                    }
                }

                if($short_desc_len > 0){
                    if($short_desc_len >= 112 && $short_desc_len <= 160){
                        $short_desc_seo_percentage = ($total_seo_percentage/$total_seo_item);
                    }else{
                        $short_desc_seo_percentage = ($total_seo_percentage/$total_seo_item) / 2;
                    }
                }

                $ideal_title_length_array = $product->getIdealTitleAttribute();
                $ideal_title_percentage = 0;

                if(count($ideal_title_length_array) > 0){

                    if(count($ideal_title_length_array) >= 2){
                        $ideal_title_percentage = 5;
                    }else{
                        $ideal_title_percentage = 2;
                    }

                }

                $percentage = $percentage + $title_seo_percentage + $short_desc_seo_percentage + $ideal_title_percentage;

                $percentage = number_format((float)$percentage, 2, '.', '');
                $field_fill_up_count[] = $percentage;
                // dd($percentage);
            }
        }
        // dd($field_fill_up_count);

        $averagePercentage = (count($field_fill_up_count) > 0) ? array_sum($field_fill_up_count) / count($field_fill_up_count) : 0;
        // dd($averagePercentage);
        return number_format($averagePercentage, 2, '.', '');
    }



    public function indexNew(Request $request)
    {
        redirectToV2('/products');

        $data = array();
        $data['bootTime'] = ex_time();
        $currentUserId = CRUDBooster::myParentId();
        $isSuperAdmin = CRUDBooster::isSuperadmin();

        if (!$currentUserId) {
            return redirect(CRUDBooster::adminPath('login'));
        }
        if(!$this->canAccess('view')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }
        if(!$isSuperAdmin){
            if (app(AppStoreService::class)->checkImportPayment($currentUserId) == false) {
                return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
            }
        }

//        dump('Checked import plan: '.ex_time().' seconds');

//        $data['universal_export_purchased'] = app(AppStoreService::class)->checkAppPurchased(Apps::UNIVERSAL_EXPORT, $currentUserId);
        $data['languageId'] = app('App\Services\UserService')->getProductCountry($currentUserId);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($data['languageId']);
        $data['page_title'] = __('products.Drm_product_title');
        $data['trans_cat'] = "category_name_" . $data['lang'];
        $data['countries'] = Country::where(['is_active' => 1, 'status' => 1])->get();
        $data['selectedCountry'] = $data['countries']->where('id', $data['languageId'])->first();
        $data['channels'] = config('channel.list');

//        dump('Get additional data: '.ex_time(). ' seconds');

        if (!$isSuperAdmin) {
            $data['shop'] = app(ChannelProductService::class)->getUserShops($currentUserId, $data['lang']);
            $data['channels'] = array_values(collect($data['channels'])->whereIn('type', $data['shop']->pluck('channel'))->toArray());
        }


        $data['products'] = $this->productService->allNew(array_merge($request->all(), [
            'user_id' => !CRUDBooster::isSuperadmin() ? $currentUserId : '',
            'lang' => $data['lang'] ?? "de",
        ]));


        $data['global_rq'] = @get_option('rq_default', 'rq', $currentUserId)->option_value;

        $data['template_imports'] = array();
        $data['categories'] = [];

        $data['channels_list'] = Shop::where('user_id', $currentUserId)->get();
        foreach ($data['channels'] as $key => $types) {
            $data['channels'][$key]['average'] = 10;
        }

        $data['product_has_industry_template'] = false;

        return view('admin.drm_products.new.index', $data);
    }

    public function oneOneSync() {
        $user_id = CRUDBooster::myParentId();
        $sync_status = DB::table('cms_users')->select('one_one_sync')->where('id', $user_id)->first();
        if($sync_status->one_one_sync == 0){
            DB::table('cms_users')->where('id', $user_id)->update([
                'one_one_sync' => 1,
            ]);
            app(TransferProduct::class)->transferSynchronizedProductsToAnalysis(CRUDBooster::myId());
        }
        else{
            DB::table('cms_users')->where('id', $user_id)->update([
                'one_one_sync' => 0,
            ]);
            // AnalysisProduct::where(['synced_product' => 1, 'user_id' => $user_id])->delete();
            $product_eans = DrmProduct::where('user_id', CRUDBooster::myId())->pluck('ean')->toArray();
            $product_ids = AnalysisProduct::where('user_id', $user_id)->whereIn('ean', $product_eans)->where('archived', 0)->pluck('id')->toArray();
            $request = new Request([
                'product_ids' => $product_ids,
                'isCheckAll' => 'false',
                'type' => 1
            ]);

            app(CompetitiveAnalysisController::class)->transferToArchive($request);
        }
        return response()->json(['status' => true]);
    }

    public function list(Request $request)
    {
        if(!$this->canAccess('view')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $currentUserId = CRUDBooster::myParentId();
        if (app(AppStoreService::class)->checkImportPayment($currentUserId) == false) {
            return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
        }

        if (!$currentUserId) {
            return redirect(CRUDBooster::adminPath('login'));
        }

        if(!deluxeOrHigher($currentUserId)){
            $data['universal_export_purchased'] = app(AppStoreService::class)->checkAppPurchased(Apps::UNIVERSAL_EXPORT, $currentUserId);
        }else{
            $data['universal_export_purchased'] = true;
        }


        $data['languageId'] = app('App\Services\UserService')->getProductCountry($currentUserId);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($data['languageId']);

        $data['page_title'] = __('products.Drm_product_title');
        $data['trans_cat'] = "category_name_" . $data['lang'];
        $data['countries'] = Country::where(['is_active' => 1, 'status' => 1])->get();

        $data['selectedCountry'] = $data['countries']->where('id', $data['languageId'])->first();

        $data['channels'] = config('channel.list');

        if (!CRUDBooster::isSuperadmin()) {
            $data['shop'] = app(ChannelProductService::class)->getUserShops($currentUserId, $data['lang']);
            $data['channels'] = array_values(collect($data['channels'])->whereIn('type', $data['shop']->pluck('channel'))->toArray());
        }
        $data['products'] = $this->productService->all(array_merge($request->all(), [
            'user_id' => !CRUDBooster::isSuperadmin() ? $currentUserId : '',
            'lang' => $data['lang'] ?? "de",
        ]));

        $data['global_rq'] = @get_option('rq_default', 'rq', CRUDBooster::myId())->option_value;

        $data['template_imports'] = array();

        $template_purchased = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, $currentUserId);
        if ($template_purchased) {
            $data['template_imports'] = app(ImportService::class)->importsHasTemplate($currentUserId);
        }

        $data['categories'] = $this->categoryService->getUserCategories($currentUserId, $data['languageId']);
        $data['channels_list'] = Shop::where('user_id', $currentUserId)->get();
        foreach ($data['channels'] as $key => $types) {
            $channel_product_id = ChannelProduct::where('channel', $types['type'])->select('id')->first();
            $data['channels'][$key]['average'] = $this->averagePercentage($channel_product_id->id);
        }

        $data['automagic_limit'] = Option::where(['option_group' => 'automagic_product_update', 'user_id' => CRUDBooster::myParentId()])->get()->keyBy('option_key');

        $product_with_industry_template = DrmProduct::where('user_id', CRUDBooster::myParentId())->whereNotNull('industry_template_data')->get();
        $product_has_industry_template = false;

        if($product_with_industry_template){
            foreach($product_with_industry_template as $product){
                if($product->IndustryTemplateHasName()){
                    $product_has_industry_template = true;
                    break;
                }
            }
        }

        $data['product_has_industry_template'] = $product_has_industry_template;

        return view('admin.drm_products.product_list', $data);
    }


    public function searchProductTags(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        if ($request->search_by == 'tags') {
            $tags = DB::table('drm_products')
                ->where('user_id', $user_id)
                ->where('drm_products.tags', 'LIKE', '%' . $request->key . '%')
                ->select('tags')
                ->groupBy('tags')
                ->paginate(20);
        }

        $html = '';
        foreach ($tags as $tag) {
            $tagArray = explode(',',$tag->tags);
            if($tagArray){
                foreach($tagArray as $item){
                    if(Str::contains($item, $request->key)){
                        $html .= '<li id="single_tag" data-tag="' . $item . '">' . $item . '</li>';
                    }
                }
            }
        }
        return $html;
    }

    public function productList(Request $request)
    {
        $currentUserId = CRUDBooster::myParentId();
        $lang = app('App\Services\UserService')->getProductLanguage($data['languageId']);

        $trans_cat = "category_name_" . $lang;

        $template_imports = array();
        $template_purchased = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, $currentUserId);
        if ($template_purchased) {
            $template_imports = app(ImportService::class)->importsHasTemplate($currentUserId);
        }
        $global_rq = @get_option('rq_default', 'rq', $currentUserId)->option_value;
        $products = $this->productService->all(array_merge($request->all(), [
            'user_id' => !CRUDBooster::isSuperadmin() ? $currentUserId : '',
            'lang' => $lang,
        ]));

        $table_content = ' ';
        foreach ($products as $product) {
            $table_content .= '<tr class="' . $product->product_type_label . '">
                <td><input type="checkbox" class="checkbox" name="checkbox[]" value="' . $product->id . '"></td>
                 <td>' . $product->id . '</td>';
            if (in_array($product->drm_import_id, (array)$template_imports)) {
                $table_content .= '<td>
                    <span data-toggle="popover" tabindex="0" data-content="You have an active product template. Quick update is not possible. You should update the product template or update the title / description from "Edit Mode" on details page.">' . $product->getTemplate('title', 'de') . '</span>
                   </td>';
            } else {
                $table_content .= '<td class="drm_p_editable" data-name="title" data-pk="' . $product->id . '">
                    ' . $product->title['de'] . '
                </td>';
            }

            $table_content .= ' <td>' . $product->ean . '</td>';

            if ($product->fromMarketplace()) {
                $table_content .= '<td class="" data - name = "ek_price" data - pk = "' . $product->id . '" >' . number_format($product->ek_price, 2);
                if ($product->mp_offer == 1) {
                    $table_content .= '<br />
                    <a href = "javascript:;" onclick = "mpProductOffer(' . $product->id . ')" ><i class="fa fa-exclamation-triangle text-yellow" ></i ></a >';
                }
                $table_content .= '</td >';
            } else {
                $table_content .= ' <td class="drm_p_editable" data - name = "ek_price" data - pk = "' . $product->id . '" >' . number_format($product->ek_price, 2) . '</td >';
            }

            $table_content .= '<td>
                <span> ' . number_format($product->vk_avg, 2) . '</span>';
            if ($product->vk_avg > 0) {
                $table_content .= ' <span onclick = "showVkAvg(' . $product->id . ')" ><strong title = "Click to see details" style = "cursor:pointer;color:green" > <i class="fa fa-info-circle" ></i ></strong ></span >';
            }
            $table_content .= '</td>';

            if ($product->fromMarketplace()) {
                $table_content .= '<td class="" data-name="uvp" data-pk="{{ $product->id }}">{{ number_format($product->uvp,2) }}</td>';
            } else {
                $table_content .= '<td class="drm_p_editable" data - name = "uvp" data - pk = "' . $product->id . '" >' . number_format($product->uvp, 2) . '</td >';
            }

            if ($product->fromMarketplace()) {
                $table_content .= ' <td class="" data - name = "shipping_cost" data - pk = "' . $product->id . '" >' . number_format($product->shipping_cost, 2) . '</td >';
            } else {
                $table_content .= '<td class="drm_p_editable" data - name = "shipping_cost" data - pk = "' . $product->id . '" >' . number_format($product->shipping_cost, 2) . '</td >';
            }
            $table_content .= '<td style="color:#7e7373"><em>' . $product->connected_channels . '</em></td>';

            $product_rq_limit = isset($product->rq_limit) ? $product->rq_limit : $global_rq;
            if (!$product_rq_limit) $product_rq_limit = 0;

            $table_content .= '<td class="drm_p_editable1" data-name="rq_limit" data-pk="' . $product->id . '">' . $product->rq ?? 0 . '</td>
             <td>';

            $category_name = array();
            foreach ($product->drm_categories as $category) {
                $category_name[] = trim(preg_replace("/( - |>|,|;)/", '<i style="color:orange" class="fa fa-chevron-circle-right"></i>', $category->drm_category->$trans_cat));
            }
            $table_content .= implode("<br>", array_filter($category_name));

            $table_content .= '<br><span onclick="changeCategory([' . $product->id . '])" class="btn btn-xs drm-text"><i class="fa fa-pencil"></i> change</span>
            </td>
            <td>
            ';
            if ($product->first_image && !is_array($product->first_image)) {
                $table_content .= '<a href = "' . $product->first_image . '" data - lightbox = "roadtrip" target = "_blank" ><img width = "40px" height = "40px" src = "' . $product->first_image . '" ></a >';
            } else {
                $table_content .= ' <img src = "#" width = "40px" height = "40px" alt = "" >';
            }

            $table_content .= '</td>';

            if ($product->suppliers->name && !$product->fromMarketplace()) {
                $table_content .= '<td class="drm_p_editable" data - name = "stock" data - pk = "' . $product->id . '" >' . $product->stock . '</td >';
            } else {
                $table_content .= '<td class="" data - name = "stock" data - pk = "'.$product->id.'" >' . $product->stock . '</td >';
            }

            if (CRUDBooster::isSuperadmin()){
               $table_content .= ' <td >'.$product->user->name.'</td >';
            }

            if($product->marketplace_product_id){
                $table_content .= ' <td > Dropmatrix </td >';
            }else{
                $table_content .= ' <td >'.$product->suppliers->name.'</td >';
            }
            $table_content .= '<td>'.$product->stock_update.'</td>';

            $table_content .= '<td>
                    <div class="button_action" style="text-align:right">
                        <a class="btn btn-xs btn-primary btn-detail" title="@lang("Detail Data")" href="'.route('drm.product.detail', [$product->id]).'"><i class="fa fa-eye"></i></a>
                        <a class="btn btn-xs btn-orange btn-delete delete-drm-product" title="@lang("Delete")" data-url="'.route('drm.product.delete', ['id' => $product->id]).'"><i class="fa fa-trash"></i></a>

                        <a class="btn btn-xs btn-primary product-clone" title="@lang("Product Clone")" href="javascript:void(0)" data-product-id="'.$product->id.'"><i class="fa fa-copy"></i></a>

                        ';
            if ( !empty($product->industry_template_data) && $product->IndustryTemplateHasName() ){

                      $table_content .='<a class="btn btn-xs btn-info industry_template_field_show" title="@lang("Industry Template")" href="javascript:void(0)" data-product-id="'.$product->id.'"><i class="fa fa-industry"></i></a>';
                        }
            $table_content . '
                    </div>
                </td>';



        }

        $pagination_section = '<div class="col-md-8">
                               '.$products->appends(request()->except('page'))->links().'
                            </div>
                            <div class="col-md-4" style="margin:30px 0;">
                                <span class="pull-right">Total rows : '.$products->firstItem().' to
                                    '.$products->lastItem().' of '.$products->total().'</span>
                            </div>';


//        dd('fds');
        return response()->json([
            'success' => true,
            'data' => $table_content,
            'paginate' => $pagination_section
        ]);
    }

    public function detail($id)
    {
        redirectToV2('/products');

        if(!$this->canAccess('product_detail')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $userId = CRUDBooster::myParentId();
        if (app(AppStoreService::class)->checkImportPayment($userId) == false) {
            return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
        }

        $data['page_title'] = __('DRM Products Detail');
        $data['product'] = $this->productService->findUserProduct($id, $userId);
        if (empty($data['product'])) {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'), "Product not found", "danger");
        }

        $data['languageId'] = app('App\Services\UserService')->getProductCountry($userId);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($data['languageId']);

        $data['trans_cat'] = "category_name_" . $data['lang'];
        $data['shop'] = array();
        $data['countries'] = Country::where(['is_active' => 1, 'status' => 1])->get();

        $data['selectedCountry'] = $data['countries']->where('id', $data['languageId'])->first();

        $data['categories'] = $this->categoryService->getUserCategories($userId, $data['languageId']);

        $results = $data['product']->connected_products ?? array();

        $data['channels'] = collect(config('channel.list'));

        $shop_prices = array();
        $channel_logos = array();
        foreach ($results as $result) {
            $channelName = $data['channels']->where('type', $result->channel)->first();
            $channel_name = ucfirst(strtolower($channelName['name']));
            $vk_price = $result->vk_price ?? "N/A";
            // $shop_prices[$channel_name] = $vk_price;
            // $channel_logos[$channel_name] = getChannelLogo($result->channel);
            $channel_details[$channel_name]['vk_price'] = $vk_price;
            $channel_details[$channel_name]['status'] = $result->connection_status;
            $channel_details[$channel_name]['logo'] = getChannelLogo($result->channel);
        }

        // $data['shop_price'] = $shop_prices;
        $data['channel_details'] = $channel_details ?? [];
        $data['avg_price'] = 0;
        if (count(array_filter($shop_prices))) {
            $data['avg_price'] = array_sum(array_values($shop_prices)) / count(array_filter($shop_prices));
        }

        $data['automagic_limit'] = Option::where(['option_group' => 'automagic_product_update', 'user_id' => $userId])->get()->keyBy('option_key');

        $data['countries'] = \App\Country::where('id', '!=', 11)->where(['is_active' => 1, 'status' => 1])->get();
        $updated_flags = MarketplaceAllowedChannel::select('country_id')->where('product_id', $data['product']->marketplace_product_id)->get();
        $all_updated_flag = array();
        foreach($updated_flags as $updated_flag){
            $all_updated_flag[] = $updated_flag->country_id;
        }
        $data['updated_flag'] = !empty($all_updated_flag) ? $all_updated_flag : \App\Enums\DefaultSelectedCountries::COUNTRY_IDS;

        $data['shipping_methods'] = DB::table('drm_shipping_methods')->get();
        $data['shipping_companies'] = DB::table('drm_shipping_companies')->where('user_id', $userId)->orWhere('user_id', 2878)->get();

        if($data['product']->options){
            $colors = json_decode($data['product']->options)->color ?? array();
            $data['product']->color = implode(',', $colors);
        }

        $user_categories = DB::table('drm_product_categories')->select('category_id')->where('product_id', $id)->get();
        foreach($user_categories as $c){
            $data['user_categories'][] = $c->category_id;
        }
        if(empty($data['user_categories'])){
            $data['user_categories'] = [];
        }

        $data['all_countries'] = DB::table('all_country')->get();
        $data['category_name'] = "category_name_" . $data['lang'];

        // $data['dropmatix_brand'] = DropmatixProductBrand::where('id', $data['product']->brand)->first();
        $data['dropmatix_brand'] = $data['product']->productBrand;


        // $data['existing_brand'] = DropmatixProductBrand::where('user_id', $userId)->pluck('brand_name', 'id')->toArray();
        $data['existing_brand'] = [];
        $data['existing_brand'] = $data['product']->userBrand;
        $stocks = json_decode($data['product']->stock_of_status, true);
        $data['prices'] = [];
        $data['prices'] = json_decode($data['product']->price_of_status, true) ?? ['1000' => $data['product']->vk_price];
        $states = ['1000', '1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000'];
        $data['stateNames'] = ['1500' => 'Neu Sonstige', '1750' => 'Neu Mit Fehlern', '2000' => 'Vom Hersteller', '2500' => 'Vom Verkäufer Generalüberholt', '2750' => 'Neuwertig', '3000' => 'Gebraucht', '4000' => 'Sehr Gut', '5000' => 'Gut', '6000' => 'Akzeptabel', '7000' => 'Als Ersatzteil'];
//        $data['product']->stock = $stocks['1000'] ?? $data['product']->stock;
        $data['stocks'] = $stocks ?? ['1000' => $data['product']->stock];
        $data['additional_eans'] = [];
        $data['additional_eans'] = json_decode($data['product']->additional_eans, true) ?? [];

        if(!is_array($data['additional_eans'])){
            $data['additional_eans'] = json_decode($data['additional_eans'],true) ?? array();
        }

        $data['previous_product_id'] = DrmProduct::where('user_id', $userId)->where('id', '<', $id)->orderBy('id', 'desc')->pluck('id')->take(1);
        $data['next_product_id'] = DrmProduct::where('user_id', $userId)->where('id', '>', $id)->orderBy('id', 'asc')->pluck('id')->take(1);
        $data['deluxe_or_higher'] = deluxeOrHigher($userId);
        // if($userId == 212 || $userId == 2387){
        //     return view('admin.drm_products.details_new', $data);
        // }

        //for test, delete this
        $shop_prices = array();
        foreach ($results as $result) {
            $channelName = $data['channels']->where('type', $result->channel)->first();
            $channel_name = ucfirst(strtolower($channelName['name']));
            $vk_price = $result->vk_price ?? "N/A";
            $shop_prices[$channel_name] = $vk_price;
        }

        $data['shop_price'] = $shop_prices;
        // $data['existing_brand'] = $data['product']->userBrand->pluck('brand_name', 'id')->toArray();

        $user_custom_ind_temp = [];
        $extra_options = '';

        $user_ind_temp = CustomIndustryTemplate::where('user_id', $userId)->pluck('name', 'id')->toArray();
        if($user_ind_temp){
            $user_custom_ind_temp = array_replace($user_custom_ind_temp, $user_ind_temp);

            foreach($user_ind_temp as $key => $value){
                $extra_options .= '<option value="'.$key.'">'.$value.'</option>';
            }
        }

        $admin_ind_temp = CustomIndustryTemplate::where('publish_to_customer', 1)->pluck('name', 'id')->toArray();
        if($admin_ind_temp){
            $user_custom_ind_temp = array_replace($user_custom_ind_temp, $admin_ind_temp);

            foreach($admin_ind_temp as $key => $value){
                $extra_options .= '<option value="'.$key.'">'.$value.'</option>';
            }
        }

        $data['custom_ind_tmp'] = $user_custom_ind_temp;
        $data['extra_custom_ind_tmp'] = $extra_options;
        $data['token_credits'] = @get_token_credit(CRUDBooster::myParentId());
       //dd($data['token_credits']->credit);

        $data['is_dt_new_user'] = is_dt_user() && checkTariffEligibility($userId);

        return view('admin.drm_products.details_new', $data);

    }

    public function getLanguage(): string
    {
        $currentUserId = CRUDBooster::myId();
        return app(UserService::class)->getProductLanguage($currentUserId,$_GET);
    }

    public function getCountry(): int
    {
        $currentUserId = CRUDBooster::myId();
        return app(UserService::class)->getProductCountry($currentUserId);
    }

    public function quickUpdate(Request $request): JsonResponse
    {
        if(!$this->canAccess('quick_update')){
            return response()->json(['success' => false]);
        }

        $price      = [];
        $languageId = app('App\Services\UserService')->getProductCountry(CRUDBooster::myParentId());
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $name = $request->name;
        $id = (int)$request->pk;

        if(in_array($name, ['title', 'description', 'short_description'])){
            $value = [$lang => $request->value];
        }
        elseif(in_array($name, ['ek_price', 'vk_price', 'min_price', 'max_price', 'uvp','shipping_cost', 'rq_limit1'])){
            $value = deNumberFormatterAuto($request->value);
        }
        else{
            $value = $request->value;
        }

        $res = $this->productService->update($id, [$name => $value], $lang, 'manual');

        if($name == 'stock'){
            $stock_of_status = DrmProduct::where('id', $id)->value('stock_of_status');
            $stock_of_status = json_decode($stock_of_status, true);
            $stock_of_status['1000'] = $value;
            $this->productService->update($id, ['stock_of_status' => json_encode($stock_of_status)], $lang, 'manual');
        }

        $min_price_error = $max_price_error = false;
        if ($name === 'min_price' && $value < $res->ek_price) {
            $min_price_error = true;
        }

        if ($name === 'max_price' && $value < $res->ek_price) {
            $max_price_error = true;
        }

        $price['min_error'] = $min_price_error;
        $price['max_error'] = $max_price_error;

        return response()->json([
            'success' => true,
            'data'    => $res,
            'price'   => $price,
        ]);
    }

    public function update(Request $request)
    {
        if(!$this->canAccess('quick_update')){
            return response()->json(['success' => false]);
        }

        $languageId = app('App\Services\UserService')->getProductCountry(CRUDBooster::myParentId());
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $details = $request->input('description');

        $id = (int)$request->pk;

        if(in_array($name, ['title', 'description', 'short_description'])){
            $value = [$lang => $request->value];
            $value2 = [$lang => 'description'];
        }
        elseif(in_array($name, ['ek_price', 'vk_price','uvp','shipping_cost', 'rq_limit1'])){
            $value = deNumberFormatterAuto($request->value);
        }
        else{
            $value = $request->value;
        }

        $res = $this->productService->update(
            $id,
                [
                    'title' => [$lang => $request->title],
                    'description' => [$lang => $request->description],
                    'short_description' => [$lang => $request->short_description],
                    'ek_price' => deNumberFormatterAuto($request->ek_price),
                    'vk_price' => deNumberFormatterAuto($request->vk_price),
                    'uvp' => deNumberFormatterAuto($request->uvp),
                    'shipping_cost' => deNumberFormatterAuto($request->shipping_cost),
                    // 'rq_limit' => deNumberFormatterAuto($request->rq_limit),
                ],
            $lang, 'manual');

        return response()->json(['success' => true, 'data' => $res]);
    }

    public function delete($id)
    {
        if(!$this->canAccess('delete')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $ids = explode(",", $id);

        $languageId = app('App\Services\UserService')->getProductCountry(CRUDBooster::myParentId());
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        try {
            $this->productService->destroy($ids, CRUDBooster::myParentId(),$lang,$languageId);
        } catch (Exception $e) {

        }

        try { // single/multiple selected
            \App\AgbLogs::create([
                'user_id' => CRUDBooster::myParentId(),
                'agb' => [
                    'product_ids' => $ids,
                ],
                'message' => count($ids) . ' items deleted from main label.',
                'ip_address' => getIpAddress(),
            ]);
        } catch (\Exception $th) {}

        return redirect()->back();
    }

    public function deleteProducts($ids, $user_id){
        try {
            $this->productService->destroy($ids, $user_id);
        } catch (Exception $e) {

        }
    }

    public function getBundleModal(Request $request)
    {
        $currentUserId = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $data = $this->productService->getBundleData(
            $request->product_ids,
            $currentUserId,
            $lang,
            $languageId,
        );
        return view('admin.drm_products.bundle_modal', $data);
    }

    public function createProductBundle(Request $request)
    {
        if(!$this->canAccess('product_detail')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $user_id = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);


        $response = app(ProductBundleService::class)->createBundle($request->all(), $user_id, $lang);

        if (isset($response['id'])) {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'), "Product bundle created successfully", "success");
        } else {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'), $response['message']);
        }
    }

    public function saveImage(Request $request): JsonResponse
    {
        $currentUserId = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        try {
            if ($request->hasFile('file')) {
                $imagesAll = $request->file('file');
                foreach ($imagesAll as $image) {
                    $image_type = strtolower($image->getClientOriginalExtension());
                    if ($image_type == "jpg" || $image_type == "png" || $image_type == "jpeg" || $image_type == "gif") {
                        $product = $this->productService->getById($request->product_id);
                        $image_url = uploadImage($image, 'drm_products/' . $currentUserId);
                        if ($image_url && !in_array($image_url, $product->image)) {
                            $data = $product->image;
                            $data[] = $image_url;
                            $this->productService->update($product->id, ['image' => array_filter($data)], $lang, "manual");
                            DB::table('drm_product_image')->insert([
                                'drm_product_id' => $request->product_id,
                                'image_url' => $image_url,
                                'image_title' => $request->title,
                                'image_description' => $request->description
                            ]);
                        }
                    }
                }
            }
        } catch (Exception $e) {
            return response()->json(['success' => false], 422);
        }
        return response()->json(['success' => true]);
    }

    public function deleteImage(Request $request): JsonResponse
    {
        if(!$this->canAccess('edit')){
            return response()->json(['success' => false], 401);
        }
        $currentUserId = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        try {
            $product = $this->productService->getById($request->product);
            $this->productService->update($request->product, ['image' => array_values(array_filter(array_diff($product->image, [$request->image])))], $lang, "manual");

            return response()->json(['success' => true]);
        } catch (Exception $ex) {
            return response()->json(['success' => false], 422);
        }
    }

    public function updateImage(Request $request): JsonResponse
    {
        if(!$this->canAccess('edit')){
            return response()->json(['success' => false], 401);
        }
        $currentUserId = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $id = $request->product_id;
        $images = $request->images;
        if (!is_array($images)) {
            $images = preg_replace("/\r|\n/", "", $images);
            $images = array_unique(array_values(json_decode($images, true)));
        }
        $this->productService->update($id, ['image' => array_filter($images)], $lang, "manual");
        return response()->json(['success' => true]);
    }

    public function updateDetails(Request $request): JsonResponse
    {
        if(!$this->canAccess('edit')){
            return response()->json(['success' => false], 401);
        }
        try {
            $data = $request->except(['import_id', '_token', 'image_url', 'imagens', 'images', 'product-tags']);
            // $lang = Arr::get($data, 'lang', 'de');
            $currentUserId = CRUDBooster::myParentId();
            $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
            $lang = app('App\Services\UserService')->getProductLanguage($languageId);

            if (!empty($data['title'])) {
                $data['title'] = [$lang => $data['title']];
            }
            if (!empty($data['desc'])) {
                $desc = $data['desc'];
                for($i=0; $i<strlen($desc) - 8; $i++){
                    if($desc[$i] === '[' && $desc[$i+1] === 'B' && $desc[$i+2] === 'r'&& $desc[$i+3] === 'a' && $desc[$i+4] === 'n' && $desc[$i+5] === 'd' && $desc[$i+6] === ' ' && $desc[$i+7] === '-' && $desc[$i+8] === ' '){
                        $j = 1;
                        $str = '';
                        while(true){
                            if($desc[$i+8+$j] == "]")
                                break;
                            $str .= $desc[$i+8+$j];
                            $j++;
                        }
                        $brandId = (int)$str;
                        $brand = DropmatixProductBrand::where('id', $brandId)->first();
                        if($brand->brand_logo[0]){
                            $replaceTag = "<img style='height:40px;width:50px;' src='{$brand->brand_logo[0]}'>";
                        }
                        else{
                            $replaceTag = "<b>$brand->brand_name</b>";
                        }
                        $x = "[Brand - {$brandId}]";
                        $desc = str_replace($x, $replaceTag, $desc);
                    }
                }

                $data['description'] = [$lang => $desc];
            }

            if (!empty($data['short_description'])) {
                $data['short_description'] = [$lang => $data['short_description']];
            }

            if (!empty($data['item_color'])) {
                if($data['item_color'] == 'empty'){
                    $colors = [];
                }
                else{
                    $colors = explode(',', $data['item_color']);
                }
                $data['options'] = ['color' => $colors];
            }

            // $industryTemplateUpdatedFields = [];

            // if(isset($request->industry_template_name)){

            //     $industryTemplateUpdatedFields[$request->industry_template_name] = $request->fields;

            // }

            // if(!empty($industryTemplateUpdatedFields)){

            //     $data['industry_template_data'] = $industryTemplateUpdatedFields;

            // }

            $industry_template_name = $request->industry_template;
            $industry_temp_data = null;

            if ($industry_template_name) {

                if(in_array($industry_template_name, array_keys(config('industry_template')))){
                $industry_template_field_list = $request->$industry_template_name;
                }else{
                    $formated_temp_name = str_replace(' ', '_', $industry_template_name);
                    $industry_template_field_list = $request->$formated_temp_name;
                }

                $tmp_field_list = [];
                foreach ($industry_template_field_list as $field_key => $field_value) {

                    $tmp_field_list[$field_key] = [
                        'de' => ($lang == 'de') ? $field_value : null,
                        'en' => ($lang == 'en') ? $field_value : null,
                    ];
                }
                $industry_temp_data[$industry_template_name] = $tmp_field_list;
            }

            if($industry_temp_data){
                $data['industry_template_data'] = json_encode($industry_temp_data, true);
            }

            if($data['additional_eans'] == 'empty'){
                $data['additional_eans'] = [];
            }

            $this->productService->update($request->product_id, $data, $lang, 'manual');

            return response()->json(['success' => true]);
        } catch (Exception $ex) {
            return response()->json(['success' => false], 422);
        }
    }

    public function create()
    {
        if(!$this->canAccess('add')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }
        $user_id = CRUDBooster::myParentId();
        $import_controller = new AdminDrmImportsController;
        $data = $import_controller->importProductCheck($user_id);


        if (CRUDBooster::myPrivilegeId() == '3') {
            if ($data['blocked'] != "" || $data['product_amount'] <= 0) {
                return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
            }
        }


        $data = [];
        $data['page_title'] = __('products.Add_Manual_Product');
        // logic
        // $data['suppliers'] = DeliveryCompany::where('user_id', $user_id)->get();

        // updated logic
        if ( \CrudBooster::isCustomer() ) {
            $data['suppliers'] = DeliveryCompany::where('user_id', $user_id)
                                ->where('email', '<>', \App\Enums\Marketplace\Others::TRANSFER_DELIVERY_COMPANY_EMAIL)
                                ->where('name', '<>', \App\Enums\Marketplace\Others::TRANSFER_DELIVERY_COMPANY_NAME)
                                ->get();
        } else {
            $data['suppliers'] = DeliveryCompany::where('user_id', $user_id)->unique('email')->get();
        }
        $data['countries'] = Country::all();
        $data['categories'] = DrmSupplierCategory::where('user_id', $user_id)->get();


        $data['languageId'] = app('App\Services\UserService')->getProductCountry($user_id);
        $data['lang_short_code'] = app('App\Services\UserService')->getProductLanguage($data['languageId']);

        $data['category_name'] = "category_name_" . $data['lang_short_code'];

        $data['user_categories'] = $this->categoryService->getUserCategories($user_id, $data['languageId']);
        $data['selectedCountry'] = $data['countries']->where('id', $data['languageId'])->first();
        // dd($data['lang'], $data['languageId'], $data['user_categories']->take(2));

        $data['dropmatix_brands'] = DropmatixProductBrand::where('user_id', $user_id)->pluck('brand_name', 'id')->toArray();

        $extra_industry_temp_options = '';

        $user_ind_temp = CustomIndustryTemplate::where('user_id', $user_id)->pluck('name', 'id')->toArray();
        if($user_ind_temp){
            foreach($user_ind_temp as $key => $value){
                $extra_industry_temp_options .= '<option value="'.$key.'">'.$value.'</option>';
            }
        }

        $admin_ind_temp = CustomIndustryTemplate::where('publish_to_customer', 1)->pluck('name', 'id')->toArray();
        if($admin_ind_temp){
            foreach($admin_ind_temp as $key => $value){
                $extra_industry_temp_options .= '<option value="'.$key.'">'.$value.'</option>';
            }
        }

        $data['extra_custom_ind_tmp'] = $extra_industry_temp_options;
        $data['deluxe_or_higher']     = deluxeOrHigher($user_id);
        return view('admin.drm_products.add', $data);
    }

    public function store(Request $request)
    {
        redirectToV2('/products');

        $supplier = DeliveryCompany::where('id', $request->suplier)->first(); // delivery_companies

        if(! ( $this->canAccess('add') || $this->canAccess('edit') ) ){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $ean = validateEan($request['ean']);
        $product_exist = DrmProduct::where(['user_id' => CRUDBooster::myParentId(), "ean" => $ean])->count();
        $ean_exist = CustomEan::where(['user_id' => CRUDBooster::myParentId(), "ean" => $ean])->count();
        if ($product_exist || $ean_exist) {
            $ean = false;
        }
        if (!$ean) {
            return [
                'status' => 'error',
                'message' => 'Invalid EAN'
            ];

        }

        $user = CRUDBooster::myParentId();
        $import_controller = new AdminDrmImportsController;
        $import_controller->setImportTrial($user);

        $languageId = app('App\Services\UserService')->getProductCountry($user);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $industry_template_name = $request['industry_template'];
        $industry_temp_data = null;

        if ($industry_template_name) {

            if(in_array($industry_template_name, array_keys(config('industry_template')))){
            $industry_template_field_list = $request[$industry_template_name];
            }else{
                $industry_template_name = CustomIndustryTemplate::where('id', $industry_template_name)->value('name');
                $formated_temp_name = str_replace(' ', '_', $industry_template_name);
                $industry_template_field_list = $request->$formated_temp_name;
            }

            $tmp_field_list = [];
            foreach ($industry_template_field_list as $field_key => $field_value) {

                $tmp_field_list[$field_key] = [
                    'de' => ($lang == 'de') ? $field_value : null,
                    'en' => ($lang == 'en') ? $field_value : null,
                ];
            }
            $industry_temp_data[$industry_template_name] = $tmp_field_list;
        }

        $product_unit = null;

        if(isset($request->item_unit)){
            $product_unit = $request->item_unit;
        }

        $id = $request['id'] ?? null;
        $product = DrmProduct::findOrNew($id);
        $product->title = [$lang => $request['name']];
        $product->description = [$lang => $request['description']];
        $product->item_number = $request['item_number'];
        $product->ean = str_pad($request['ean'], 13, '0', STR_PAD_LEFT);
        $product->image = $request->image;
        $product->ek_price = $request['ek_price'];
        $product->min_price = $request['min_price'] ?? null;
        $product->max_price = $request['max_price'] ?? null;
        $product->uvp = $request['product_uvp'] ?? 0;
        $product->stock = $request['stock'];
        $product->user_id = $user;
        $product->item_size = $request['product_size'];
        $product->brand = $request['brand'];
        $product->item_weight = $request['item_weight'];
        $product->item_unit = $product_unit;
        $product->item_color = $request['item_color'];
        $product->materials = $request['materials'];
        $product->production_year = $request['production_year'];
        $product->note = $request['note'];
        $product->delivery_company_id = $request['suplier'];
        $product->country_id = $languageId;
        $product->status = $request['status'];
        $product->gender = $request['gender'];
        $product->delivery_days = $request['delivery_days'];
        $product->shipping_cost = deNumberFormatterAuto($request['shipping_cost']);
        $product->update_status = makeUpdateStatusJson();
        $product->tax_type = (int)$request['tax_type'];

        if($industry_temp_data){
            $product->industry_template_data = json_encode($industry_temp_data, true);
        }

        $product->save();

        \App\ProductMigration\API::syncProduct($product->id);

        $one_one_sync = DB::table('cms_users')->where('id', $user)->value('one_one_sync');

        if($one_one_sync == 1){
            app(TransferProduct::class)->transferSynchronizedProductsToAnalysis($user);
        }

        if (!empty($request['category'])) {
            DRMProductCategory::where([
                'product_id' => $product->id,
            ])->delete();

            foreach ($request['category'] as $categoryId) {
                DRMProductCategory::create([
                    'product_id' => $product->id,
                    'country_id' => $product->country_id,
                    'category_id' => $categoryId,
                ]);
            }
        }

        // CREATE manual feed from here.
        $file_name = $supplier->name." manual products";
        $feed_exists = DrmImport::where([
            'user_id' => CRUDBooster::myId(),
            'csv_file_name' => $file_name,
        ])->first();

        if($feed_exists){
            $drm_id = $feed_exists->id;
            GenerateCsv::dispatch($drm_id, $feed_exists->csv_file_path,$user);
        }else{
            $csv_file_path = "public/csv_files/".Str::random(40).'.csv';
            $drm_id = DrmImport::create([
                'user_id'=> CRUDBooster::myId(),
                'delivery_company_id'=> $supplier->id,
                'csv_file_name'=> $file_name,
                'type'=> 'file',
                'csv_file_path' => $csv_file_path,
                'country_id'=> $product->country_id,
                'delimiter' => ',',
                "delivery_time"=> $product->delivery_days,
                'import_finished'=> 1,
                'money_format' => 1
            ])->id;
            GenerateCsv::dispatch($drm_id, $csv_file_path,$user);
        }
        $product->drm_import_id = $drm_id;
        $product->save();

        if(professionalOrHigher($user)){
            AutoTransfer::dispatch([$product->id],$user,$lang);
        }

        $automagic = new Automagic(CRUDBooster::myParentId());
        $empty_field_count = $automagic->countEmptyFields( $product->id );

        if($empty_field_count > 0){
            $response = [
                'status' => 'success',
                'message' =>  __('Product added successfully'),
                'action' => __("Do you want to automagically fillup"),
                'product_id' => $product->id
            ];

        }else{
            $response = [
                'status' => 'success',
                'message' => "Product added successfully?"
            ];
        }

        return $response;
    }

    public function automagicFillup(Request $request){
        if(!$request->product_id) return false;

        $automagic = new Automagic(CRUDBooster::myId());
        $response = $automagic->setLabel('drm')->magicAutocomplete( $request->product_id );

        switch ($response->status) {
            case 'limit_excedeed':
                return [
                    'status' => 'warning',
                    'message' => 'Your automagic limit excedeed!'
                ];
                break;
            case 'no_empty_field':
                return [
                    'status' => 'success',
                    'message' => 'No empty field found!'
                ];
                break;
            case 'updated':
                return [
                    'status' => 'success',
                    'message' => 'Product empty field updated successfully!'
                ];
                break;

            default:
                return [
                    'status' => 'warning',
                    'message' => 'Nothing updated!'
                ];
                break;
        }
    }

    public function storeImageURL(Request $request): JsonResponse
    {
        try {
            $url = $request->url;

            $path = 'drm_products/' . CRUDBooster::myParentId();

            $extension = pathinfo(parse_url($url, PHP_URL_PATH), PATHINFO_EXTENSION);
            $randomStr = substr(str_shuffle(str_repeat('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', mt_rand(1, 5))), 1, 40);

            if (!empty($extension)) {
                $fileName = $path . '/' . $randomStr . "." . $extension;
            } else {
                $fileName = $path . '/' . $randomStr;

            }
            $fileContent = file_get_contents($url);

            Storage::disk('spaces')->put($fileName, $fileContent, 'public');
            if (Storage::disk('spaces')->exists($fileName)) {
                $images_url = Storage::disk('spaces')->url($fileName);
            } else {
                $images_url = null;
            }

            if (!empty($images_url)) {
                return response()->json([
                    'name' => $images_url,
                    'serverFileName' => $randomStr,
                    'success' => true,
                    'message' => 'Image upload successfully!',
                ]);
            } else {
                return response()->json(['success' => false, 'message' => 'Image not uploaded!'], 422);
            }
        } catch (Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 422);
        }

    }

    public function saveImageURL(Request $request): JsonResponse
    {
        if(!( $this->canAccess('add') || $this->canAccess('edit') ) ){
            return response()->json(['success' => false], 401);
        }

        $currentUserId = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $id = $request->product_id;
        $url = $request->url;
        if (!empty($url)) {
            $product = $this->productService->getById($id);
            $this->productService->update($id, ['image' => array_filter(array_merge($product->image, [$url]))], $lang, "manual");

            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false], 422);
        }
    }

    public function saveTmpImage(Request $request)
    {

        if (!CRUDBooster::isSuperadmin()) {
            // 'save_temp_image'
            if (CRUDBooster::isSubUser() && (!sub_account_can('add') && !sub_account_can('all_modules', 122))) {
                return 0;
            }
        }

        try {
            if ($request->hasFile('file')) {
                $imagesAll = $request->file('file');
                $image_type = strtolower($imagesAll->getClientOriginalExtension());
                $imgTypes = ['jpg', 'png', 'jpeg', 'gif'];
                if (in_array($image_type, $imgTypes)) {
                    $images_url = uploadImage($imagesAll, 'drm_products/' . CRUDBooster::myParentId());
                    return response()->json([
                        'name' => $images_url,
                        'success' => true,
                        'message' => 'File upload success!',
                    ]);
                }
            }
        } catch (Exception $e) {
            return response()->json(['success' => false], 422);
        }
        return response()->json(['success' => false], 422);
    }

    public function delTmpImage(Request $request): JsonResponse
    {
        $path = str_replace("https://drm-file.fra1.digitaloceanspaces.com/", "/", $request->file_name);
        if ($request->file_name && Storage::disk('spaces')->exists('/' . $path)) {
            Storage::disk('spaces')->delete('/' . $path);

            return response()->json(['success' => true, 'name' => $request->file_name, 'message' => 'File deleted success!']);
        }

        return response()->json(['success' => false, 'name' => $request->file_name, 'message' => 'File deleted failed!']);
    }

    public function getCloneModal(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($languageId);

        $data['product'] = $this->productService->findUserProduct($request->product_id, $user_id);
        $data['ean_module'] = app(AppStoreService::class)->checkAppPurchased(Apps::EAN_MANAGER, $user_id);
        return view('admin.drm_products.clone_modal', $data);
    }

     public function getAnalysisModal(Request $request)
    {
         $userId = CRUDBooster::myParentId();

        // if(!isLocal() && $userId != 212) abort(404);
        $data['analysis_product'] = $analysis_product = DB::table('analysis_products')->select('id', 'title', 'ean', 'price','amazon_price','ebay_price' ,'image','brand', 'availability', 'category', 'source', 'product_id')->where('ean', $request->ean)->first();

        $data['product'] = $product = \App\DrmProduct::with(['drm_categories.drm_category'])->where([
            'user_id' => $userId,
            'id'      => $analysis_product->product_id
        ])->first();

        if (empty($product)) {
            // not a drm product
            $data['product'] = $analysis_product;
        }else{
            $channel_vks = (!empty($analysis_product->product->connected_products)) ? array_column($analysis_product->product->connected_products->toArray(), 'vk_price') : array();
            if( !empty($channel_vks) ){
                $vk_avg = array_sum($channel_vks) / count($channel_vks);
            }else{
                $vk_avg = 0;
            }
            $data['vk_avg'] = number_format($vk_avg, 2, ',', '.');
        }

        $data['shop'] = array();
        $results = $data['product']->connected_products ?? array();
        $data['channels'] = collect(config('channel.list'));

        $shop_prices = array();
        foreach ($results as $result) {
            $channelName = $data['channels']->where('type', $result->channel)->first();
            $channel_name = ucfirst(strtolower($channelName['name']));
            $vk_price = $result->vk_price ?? "N/A";
            $shop_prices[$channel_name] = $vk_price;
        }
        $data['shop_price'] = $shop_prices;
        $data['avg_price'] = 0;
        if (count(array_filter($shop_prices))) {
            $data['avg_price'] = array_sum(array_values($shop_prices)) / count(array_filter($shop_prices));
        }

        // dd($data['analysis_product']->title);

        return view('admin.drm_products.analysis_modal', $data);
    }


    public function cloneProduct(Request $request)
    {
        if(!$this->canAccess('clone')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $user_id = CRUDBooster::myParentId();

        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $product = $this->productService->findUserProduct($request->product_id, $user_id);

        $clone_ean = $request->clone_ean_module;
        $ean_input = $request->ean;
        $ean = null;

        if ($clone_ean) {
            $select_ean = DB::table('custom_eans')->where(['user_id' => $product->user_id, "used" => 0])->first();
            if ($select_ean) {
                $ean = $select_ean->ean;
            }
        } elseif (validateEan($ean_input)) {
            $ean = validateEan($ean_input);
            $product_exist = DB::table('drm_products')->where(['user_id' => $product->user_id, "ean" => $ean])->count();
            $ean_exist = DB::table('custom_eans')->where(['user_id' => $product->user_id, "ean" => $ean])->count();
            if ($product_exist || $ean_exist) {
                $ean = false;
            }
        }

        if ($ean) {
            $data = [
                "title" => [$lang => $request->title],
                "description" => [$lang => $request->description],
                "item_number" => $request->item_number,
                "item_size" => $request->item_size,
                "item_color" => $request->item_color,
                "brand" => $request->brand,
                "item_weight" => $request->item_weight,
                "gender" => $request->gender,
                "status" => $request->status,
                "delivery_days" => $request->delivery_days,
                "note" => $request->note,
                "production_year" => $request->production_year,
                "materials" => $request->materials,
                "ek_price" => $request->ek_price,
                "uvp" => $request->uvp,
                "stock" => $request->stock,
                "user_id" => $product->user_id,
                "delivery_company_id" => $product->delivery_company_id,
                "country_id" => $product->country_id,
                "image" => $product->image,
                "ean" => $ean,
                'cloned_from' => $request->product_id
            ];

            $id = DrmProduct::create($data)->id;

            $categories = $product->drm_categories->pluck('category_id')->toArray();
            $insert_categories = [];
            foreach ($categories as $category) {
                $insert_categories[] = [
                    'category_id' => $category,
                    'product_id' => $id
                ];
            }
            DB::table('drm_product_categories')->insert($insert_categories);

            // return redirect(CRUDBooster::adminPath('drm_products/detail_new/' . $id));
            return redirect(CRUDBooster::adminPath('drm_products/'));
        } else {
            // CRUDBooster::redirect(CRUDBooster::adminPath('drm_products/detail_new/' . $request->product_id), "Invalid EAN", "danger");
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_products/'), "Invalid EAN", "danger");
        }
        return redirect(CRUDBooster::adminPath('drm_products'));
    }


    public function getFilterOptions(Request $request): string
    {
        $user_id = CRUDBooster::myParentId();

        $country_id = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($country_id);

        return $this->productService->getFilterOptions(array_merge($request->all(), [
            'user_id' => $user_id,
            'lang' => $lang ?? "de",
            'country_id' => $country_id
        ]));
    }

    public function searchAndReplace(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        try {
            $res = $this->productService->searchAndReplace(array_merge($request->all(), [
                'user_id' => $user_id,
                'lang' => $lang ?? "de",
            ]));
        } catch (Throwable $th) {
            //throw $th;
        }

        return response($res ?? "");
    }

    public function getTagProducts(Request $request): JsonResponse
    {
        $user_id = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($languageId);

        $data['products'] = dropFunnelCount($request->tag, $request->user_id, 'data', $data['lang']);
        if ($data['products']->isNotEmpty()) {
            $html = view('admin.drm_products.partials.drop_funnel_products', $data)->render();
            return response()->json(['success' => true, 'data' => $html]);
        }
        return response()->json(['success' => false, 'data' => '<p>No Product Found</p>']);
    }

    public function drmProductStatus()
    {
        try {
            $productIds = $_REQUEST['product_ids'];
            $selectedFields = $_REQUEST['selected_fields'];
            // dd($productIds, $selectedFields);

            $fields = [
                'title',
                'description',
                'image',
                'ek_price',
                'stock',
                'item_weight',
                'item_size',
                'item_color',
                'production_year',
                'brand',
                'materials',
                'gender',
                'category',
                'status'
            ];

            if ($productIds) {
                $statusArray = [];
                foreach ($fields as $field) {
                    if ($selectedFields) {
                        if (in_array($field, $selectedFields)) {
                            $statusArray[$field] = 1;
                        } else {
                            $statusArray[$field] = 0;
                        }
                    } else {
                        $statusArray[$field] = 0;
                    }

                }
                // dd("end of loop", $statusArray, json_encode($statusArray));
            }

            if (!empty($statusArray)) {
                \App\DrmProduct::whereIn('id', $productIds)->update(['update_status' => $statusArray]);
                return response()->json(['success' => true, 'message' => 'Product Status Updated Successfully.']);
            }
            return response()->json(['success' => false, 'message' => 'Please Select Product!!!']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function changeCountry(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        app(UserService::class)->changeCountry($request->country, $user_id);
        return redirect(route('drm.product.index'));
    }

    public function assignCategory(Request $request)
    {
        if(!$this->canAccess('categories')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $currentUserId = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $custom_category = $request->custom_category;
        $product_ids = json_decode($request->product_ids);
        $category_ids = $request->category ?? [];

        $this->productService->assignCategory($product_ids, $category_ids, $currentUserId, $lang,
            $custom_category ? [
                'country_id' => $languageId,
                'category_name' => $custom_category
            ] : array());

        $products = DrmProduct::whereIn('id', $product_ids)->get();
        foreach ( $products as $product ) {
            $update_status = json_decode($product->update_status,1 );
            $update_status['category'] = 0;
            $product->update([
                'update_status' => $update_status,
            ]);
        }

        return \redirect()->back();
    }


    public function averagePercentage($id)
    {
        $userId = CRUDBooster::myParentId();
        if (!$userId) {
            return redirect(CRUDBooster::adminPath('login'));
        }

        $languageId = app('App\Services\UserService')->getProductCountry($userId);
        $channel_id = \App\Models\ChannelProduct::where('id', $id)->first();
        // dd($channel_id['channel']);
        $products = \App\Models\ChannelProduct::where(['user_id' => $userId, 'country_id' => $languageId, 'channel' => $channel_id['channel']])->get();
        // dd($products);
        $field_fill_up_count = [];

        if ($products) {
            foreach ($products as $product) {

                $count = 0;
                $total_fill = count(\App\Enums\Product::ALL_FIELDS);
                foreach (\App\Enums\Product::ALL_FIELDS as $fields) {
                    // dd($product, $fields);
                    if (!empty($product[$fields])) {
                        $count++;
                    }
                }

//                dd(\App\Enums\Channel::CHRONO24);

                if ($product->channel == \App\Enums\Channel::CHRONO24) {
                    $total_fill = count(\App\Enums\Product::ALL_FIELDS) + count(\App\Enums\Product::EXTRA_FIELD);
                    $extra_field = \App\Enums\Product::EXTRA_FIELD;
                    if ($product->attributes) {
                        $attr = $product->attributes['additonal_data'];
                        if ($attr) {
                            foreach ($attr as $index => $value) {
                                if (!empty($value)) {
                                    if (in_array($index, $extra_field)) {
                                        $count++;
                                    }
                                }
                            }
                        }
                    }
                }


                $percentage = ($count * 100) / $total_fill;
                $percentage = number_format((float)$percentage, 2, '.', '');
                $field_fill_up_count[] = $percentage;
                // dd($percentage);
            }
        }
        // dd($field_fill_up_count);

        $averagePercentage = (count($field_fill_up_count) > 0) ? array_sum($field_fill_up_count) / count($field_fill_up_count) : 0;
        // dd($averagePercentage);
        return number_format($averagePercentage, 2, '.', '');
    }

    public function addDeliveryCompany(Request $request): JsonResponse
    {
        $data = [];
        $data['user_id'] = CRUDBooster::myParentId();
        $data['name'] = $request->supplier_name;
        $data['address'] = $request->supplier_address;
        $data['email'] = $request->email;
        $data['zip'] = $request->supplier_zip;
        $data['state'] = $request->supplier_state;
        $data['contact_name'] = $request->supplier_contact_name;
        $data['url'] = $request->website;
        $data['country_id'] = $request->supplier_country;
        $data['category_id'] = $request->category;
        $data['phone'] = $request->phone;
        $data['customer_number'] = $request->customer_number;
        $data['created_at'] = date('Y-m-d H:i:s');
        //dd($data);
        $id = DB::table('delivery_companies')->insertGetId($data);

        //Clear account activity step
        \App\Services\CheckListProgress\Checklist::cache_key_clear(3, \CRUDBooster::myParentId());
        return response()->json([
            'status' => "SUCCESS",
            'code' => 200,
            "message" => "Successfully Created !",
            'data' => [
                'name' => $request->supplier_name,
                'id' => $id
            ]
        ]);
    }

    public function checkExistingChannel(Request $request): JsonResponse
    {
        $user_id = CRUDBooster::myParentId();
        $channel = $request->channel;
        if($channel == Channel::DROPTIENDA)
        {
            return response()->json([
                'product_count' => 0,
            ]);
        }
        $product_ids = $request->product_ids;
        $source = $request->params['search_by_source'];

        if((int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($user_id, $request->params);
        }

        if($request->analysis_bulk == "true"){
            $product_ids = app(\App\Http\Controllers\Product\CompetitiveAnalysisController::class)->getTransferProductIds($channel, $user_id, $source);
        }

        if( $source == 2){
            $product_ids = app(\App\Http\Controllers\Product\CompetitiveAnalysisController::class)->getDRMPproductIds($user_id, $product_ids);
        }

        $products = ChannelProduct::where([
            'user_id' => $user_id,
            'channel' => $channel,
            'shop_id' => $request->shop
        ])
        ->whereIn('drm_product_id', $product_ids)
        ->count();

        return response()->json([
            'product_count' => $products,
        ]);
    }

    protected function canAccess($feature): bool
    {
        return can_access($feature,$this->module_id);
    }

    public function getDisconnectedProducts(){

        $user_id = CRUDBooster::myParentId();
        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        $all_disconnected_product = DrmProduct::where('user_id', CRUDBooster::myParentId())
        ->whereJsonLength('drm_products.title->'.$lang, '>', 0)
        ->whereNotExists(function ($query){
            $query->select(DB::raw(1))
                ->from('channel_products')
                ->whereRaw('channel_products.drm_product_id = drm_products.id');
        })
        ->pluck('id')
        ->toArray();

        if($all_disconnected_product){
            return response()->json([
                'success' => true,
                'data' => $all_disconnected_product
            ]);
        }else{
            return response()->json([
                'success' => false,
                'message' => 'There is no disconnected products !'
            ]);
        }
    }

    public function bulkDelete(Request $request){

        if(!$this->canAccess('bulk_delete')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $ids = json_decode($request->getContent());

        try {
            foreach(array_chunk($ids, 20) as $chunkIds){
                DisconnectedProductDelete::dispatch($chunkIds, CRUDBooster::myParentId());
            }

            try { // disconnected items
                \App\AgbLogs::create([
                    'user_id' => CRUDBooster::myParentId(),
                    'agb' => [
                        'product_ids' => $ids,
                    ],
                    'message' => count($ids) . ' disconnected items deleted from main label.',
                    'ip_address' => getIpAddress(),
                ]);
            } catch (\Exception $th) {}

            return response()->json([
                'success' => true,
                'message' => 'All disconnected products deleted !'
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 401);
        }

    }

    public function getIndustryTemplateFields()
    {

        if(isset($_REQUEST['product_ids']) && !empty($_REQUEST['product_ids'])){
            $product_ids = $_REQUEST['product_ids'];
            $all_industry_template = config('industry_template');
            $template_name = [];

            foreach($product_ids as $product_id){
                $item = DrmProduct::where('id', $product_id)->value('industry_template_data');

                if($item && $all_industry_template){

                    $item = json_decode($item, true) ?? array();

                    foreach($item as $key => $value){

                        if(!in_array($key, $template_name)){
                            $template_name[] = $key;
                        }

                    }

                    // $item = array_keys(json_decode($item, true));

                    // foreach($all_industry_template as $key => $template){
                    //     $result = array_diff($item, array_keys($template));
                    //     if(empty($result) && !in_array($key, $template_name)){
                    //         $template_name[] = $key;
                    //     }
                    // }
                }
            }

            $html = '';

            if($template_name){

                foreach($template_name as $name){

                    if($name == "supplements"){
                        $html .= '<br> <div class="col-sm-12"> <div class="row"> <div class="col-sm-3"> <strong>'. ucfirst('lebensmittel/supplements') .'</strong></div> <br>';
                    }else{
                        $html .= '<br> <div class="col-sm-12"> <div class="row"> <div class="col-sm-3"> <strong>'. ucfirst($name) .'</strong></div> <br>';
                    }

                    $template_keys = array_keys(config('industry_template.'.$name));

                    // if($name == "juvely"){
                        foreach($template_keys as $value){
                            $html .= '<div class="col-sm-3">
                                        <input type="checkbox" id="industry_template_transfer_field_'. $value .'" class="industry_field_checkbox_'. $name .'" name="selected_columns[]" value="'.$value.'"> '. __('industry_template.'.$value). '
                                    </div>';
                        }
                    // }else{
                    //     foreach($template_keys as $value){
                    //         $html .= '<div class="col-sm-3">
                    //                     <input type="checkbox" id="industry_template_transfer_field_'. $value .'" class="industry_field_checkbox_'. $name .'" name="selected_columns[]" value="'.$value.'"> '. ucfirst(str_replace('_',' ',$value)). '
                    //                 </div>';
                    //     }
                    // }

                    $html .= '</div> </div>';

                }

                return response()->json([
                    'success' => true,
                    'html' => $html,
                    'template_name' => implode(', ', $template_name),
                    'message' => 'Template with fields retrun',
                ], 200);

            }else{
                return response()->json([
                    'success' => true,
                    'html' => $html,
                    'message' => 'No Template Found !',
                ], 200);
            }

        }else{
            return response()->json([
                'success' => false,
                'message' => 'No Product Selected !'
            ], 403);
        }
    }


    /**
     * #TODO: Rewrite with automagic service class
     */
    public function magicAutocomplete(Request $request){
        $count_updated_product = 0;
        $automagic_product_limit = @get_option('automagic_product_limit', 'automagic_product_update', CRUDBooster::myParentId());
        $automagic_product_uses = @get_option('automagic_product_uses', 'automagic_product_update', CRUDBooster::myParentId());
        if(!$automagic_product_limit){
            $automagic_product_limit = 15;
            set_option('automagic_product_limit', 'automagic_product_update', $automagic_product_limit);
        }else{
            $automagic_product_limit = $automagic_product_limit->option_value;
        }
        if(!$automagic_product_uses){
            $automagic_product_uses = 0;
        }else{
            $automagic_product_uses = $automagic_product_uses->option_value;
        }

        $userId = CRUDBooster::myParentId();
        $products = $this->productService->findUserProductsWithCat($request->product_ids, $userId)->get();

        $product_eans = array_column($products->toArray(), 'ean');

        #if unlimited pack selected
        if($automagic_product_limit != 'unlimited'){

            if($automagic_product_uses + count($product_eans) > $automagic_product_limit){
                #show error when limit excedeed
                return [
                    'status' => 'error',
                    'error' => 'limit_exceeded',
                    'redirect' => url('admin/app_form'),
                    'app_id'   => Apps::MAGIC_PRODUCT_UPDATE,
                    'message' => 'Automagic data completion limit excedeed.'
                ];
            }
        }

        $product_param = [
            'history' => 0,
            'buybox' => 0,
            'rental' => 0,
            'stats' => 1
        ];
        $keepa = new Keepa(env('KEEPA_API_KEY'));
        $keepa_products = $keepa->code($product_eans, $product_param);

        foreach ($products as $key => $product) {

            $updated = false;
            $drm_cat_id = false;
            $keepa_product = $keepa_products->product($product->ean);
            //dd( $keepa_product);

            $keepa_price = $keepa_products->latestPrice($product->ean);
            $keepa_cat_tree = $keepa_products->categoryTree($product->ean) ?? [];

            $update_product = [];

            $last_keepa_category_id = end($keepa_cat_tree)['catId'] ?? false;

            if($last_keepa_category_id && $product->drm_categories->isEmpty()){
                $cat_name = implode( "/", array_column((array)$keepa_cat_tree, 'name')  );
                # product dont have category need autocomplete

                $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                $lang = app('App\Services\UserService')->getProductLanguage($country_id);
                $drmCategory = [
                    'id' => $last_keepa_category_id,
                    'category_name_'.$lang => $cat_name,
                    'user_id' => CRUDBooster::myId(),
                    'country_id' => $country_id,
                ];

                #get category id or create
                $drm_cat_id = DrmCategory::firstOrCreate(
                    ['id' => $last_keepa_category_id],
                    $drmCategory
                )->id;

                #assign category to product
                DRMProductCategory::create([
                    'product_id' => $product->id,
                    'country_id' => $product->country_id,
                    'category_id' => $drm_cat_id,
                ]);
                $updated = true;
            }

            $magic_fields = [
                'brand' => $product->brand,
                'item_color' => $product->item_color,
                'item_weight' => $product->item_weight,
                'item_size' => $product->item_size,
                'ek_price' => $product->ek_price,
                'packaging_length'=> $product->packaging_length,
                'packaging_width'=> $product->packaging_width,
                'packaging_height'=> $product->packaging_height,
            ];
            $empty_fields = hasEmptyValue($magic_fields);

            $languageId = app('App\Services\UserService')->getProductCountry(CRUDBooster::myParentId());
            $lang = app('App\Services\UserService')->getProductLanguage($languageId);

            //dd($keepa_product->packageHeight,$empty_fields);

            if(!empty($empty_fields)){
                # only send request if atleast one required field is empty
                if(\in_array('brand', $empty_fields)) $update_product['brand'] = $keepa_product->brand;
                if(\in_array('item_color', $empty_fields)) $update_product['item_color'] = $keepa_product->color;
                if(\in_array('item_weight', $empty_fields)) $update_product['item_weight'] = $keepa_product->itemWeight;
                if(\in_array('item_size', $empty_fields)) $update_product['item_size'] = $keepa_product->size;
                if(\in_array('ek_price', $empty_fields)) $update_product['ek_price'] = $keepa_price;
                if(\in_array('packaging_length', $empty_fields)) $update_product['packaging_length'] = $keepa_product->packageLength;
                if(\in_array('packaging_width', $empty_fields)) $update_product['packaging_width'] = $keepa_product->packageWidth;
                if(\in_array('packaging_height', $empty_fields)) $update_product['packaging_height'] = $keepa_product->packageHeight;
                //dd($update_product['packaging_height']);
                $this->productService->update($product->id, $update_product, $lang, 'manual');

                # dont count if nothing updated. above function dont return if product updated or not
                $updated = true;
            }
            $industry_template_data = json_decode($product->industry_template_data);

            if( !empty($industry_template_data) ){
                if(isset($keepa_product->model)){
                    if(empty($industry_template_data->juvely->model->$lang)){
                        $industry_template_data->juvely->model->$lang = $keepa_product->model;
                        $data['industry_template_data'] = json_encode($industry_template_data);
                        $this->productService->update($product->id, $data, $lang, 'manual');
                        $updated = true;
                    }
                }
            }
            if($updated) $count_updated_product++;
        }

        $response = [
            'status' => 'warning',
            'message' => 'Nothing to update!'
        ];

        if($count_updated_product){
            $response = [
                'status' => 'success',
                'message' => "$count_updated_product Product informations updated magically!"
            ];
        }
        set_option('automagic_product_uses', 'automagic_product_update', $automagic_product_uses + $count_updated_product);
        return $response;
    }

    /**
     * #TODO: Automagic with now credits
     */

    public function magicAutocompleteNew(Request $request){

        $count_updated_product = 0;
        $automagic_product_limit = @get_token_credit(CRUDBooster::myParentId());

        $automagic_product_limit = $automagic_product_limit['remain_credit'] ?? 0;

        $userId = CRUDBooster::myParentId();
        $products = $this->productService->findUserProductsWithCat($request->product_ids, $userId)->get();


        $product_eans = array_column($products->toArray(), 'ean');
        $credit_req = count($product_eans) * 5 ;

        if($credit_req > $automagic_product_limit){
            return [
                'status' => 'error',
                'error' => 'limit_exceeded',
                //'redirect' => url('admin/app_form'),
                'message' => 'Automagic data completion limit excedeed.'
            ];
        }
        //dd('prod',$credit_req);

        #if unlimited pack selected
        // if($automagic_product_limit != 'unlimited'){

        //     if($automagic_product_uses + count($product_eans) > $automagic_product_limit){
        //         #show error when limit excedeed
        //         return [
        //             'status' => 'error',
        //             'error' => 'limit_exceeded',
        //             'redirect' => url('admin/app_form'),
        //             'app_id'   => Apps::MAGIC_PRODUCT_UPDATE,
        //             'message' => 'Automagic data completion limit excedeed.'
        //         ];
        //     }
        // }

        $product_param = [
            'history' => 0,
            'buybox' => 0,
            'rental' => 0,
            'stats' => 1
        ];
        $keepa = new Keepa(env('KEEPA_API_KEY'));
        $keepa_products = $keepa->code($product_eans, $product_param);

        foreach ($products as $key => $product) {

            $updated = false;
            $drm_cat_id = false;
            $keepa_product = $keepa_products->product($product->ean);
            //dd( $keepa_product);

            $keepa_price = $keepa_products->latestPrice($product->ean);
            $keepa_cat_tree = $keepa_products->categoryTree($product->ean) ?? [];

            $update_product = [];

            $last_keepa_category_id = end($keepa_cat_tree)['catId'] ?? false;

            if($last_keepa_category_id && $product->drm_categories->isEmpty()){
                $cat_name = implode( "/", array_column((array)$keepa_cat_tree, 'name')  );
                # product dont have category need autocomplete

                $country_id = app('App\Services\UserService')->getProductCountry($product->supplier_id);
                $lang = app('App\Services\UserService')->getProductLanguage($country_id);
                $drmCategory = [
                    'id' => $last_keepa_category_id,
                    'category_name_'.$lang => $cat_name,
                    'user_id' => CRUDBooster::myId(),
                    'country_id' => $country_id,
                ];

                #get category id or create
                $drm_cat_id = DrmCategory::firstOrCreate(
                    ['id' => $last_keepa_category_id],
                    $drmCategory
                )->id;

                #assign category to product
                DRMProductCategory::create([
                    'product_id' => $product->id,
                    'country_id' => $product->country_id,
                    'category_id' => $drm_cat_id,
                ]);
                $updated = true;
            }

            $magic_fields = [
                'brand' => $product->brand,
                'item_color' => $product->item_color,
                'item_weight' => $product->item_weight,
                'item_size' => $product->item_size,
                'ek_price' => $product->ek_price,
                'packaging_length'=> $product->packaging_length,
                'packaging_width'=> $product->packaging_width,
                'packaging_height'=> $product->packaging_height,
            ];
            $empty_fields = hasEmptyValue($magic_fields);

            $languageId = app('App\Services\UserService')->getProductCountry(CRUDBooster::myParentId());
            $lang = app('App\Services\UserService')->getProductLanguage($languageId);

            //dd($keepa_product->packageHeight,$empty_fields);

            if(!empty($empty_fields)){
                # only send request if atleast one required field is empty
                if(\in_array('brand', $empty_fields)) $update_product['brand'] = $keepa_product->brand;
                if(\in_array('item_color', $empty_fields)) $update_product['item_color'] = $keepa_product->color;
                if(\in_array('item_weight', $empty_fields)) $update_product['item_weight'] = $keepa_product->itemWeight;
                if(\in_array('item_size', $empty_fields)) $update_product['item_size'] = $keepa_product->size;
                if(\in_array('ek_price', $empty_fields)) $update_product['ek_price'] = $keepa_price;
                if(\in_array('packaging_length', $empty_fields)) $update_product['packaging_length'] = $keepa_product->packageLength;
                if(\in_array('packaging_width', $empty_fields)) $update_product['packaging_width'] = $keepa_product->packageWidth;
                if(\in_array('packaging_height', $empty_fields)) $update_product['packaging_height'] = $keepa_product->packageHeight;
                //dd($update_product['packaging_height']);
                $this->productService->update($product->id, $update_product, $lang, 'manual');

                # dont count if nothing updated. above function dont return if product updated or not
                $updated = true;
            }
            $industry_template_data = json_decode($product->industry_template_data);

            if( !empty($industry_template_data) ){
                if(isset($keepa_product->model)){
                    if(empty($industry_template_data->juvely->model->$lang)){
                        $industry_template_data->juvely->model->$lang = $keepa_product->model;
                        $data['industry_template_data'] = json_encode($industry_template_data);
                        $this->productService->update($product->id, $data, $lang, 'manual');
                        $updated = true;
                    }
                }
            }
            if($updated) $count_updated_product++;
        }

        $response = [
            'status' => 'warning',
            'message' => 'Nothing to update!'
        ];

        if($count_updated_product){
            $response = [
                'status' => 'success',
                'message' => "$count_updated_product Product informations updated magically!"
            ];
        }
        charge_token($credit_req,$userId);
        //set_option('automagic_product_uses', 'automagic_product_update', $automagic_product_uses + $count_updated_product);
        return $response;
    }


    // /**
    //  * Command: Keepa:fetchKeepaProducts
    //  * Download json respose from keepa to category spesific folder
    //  * Folder Path: "keepa_products/".date('Y-m')."/<keepa_category_id>
    //  * @param Request keepa_cat_idx
    //  * <url>/admin/drm_products/fetch_keepa_products?keepa_cat_idx=17
    //  */
    // public function fetchKeepaProducts(Request $req){

    //     $asin_chunk_size = 100; #max size keepa supports is 100
    //     $product_param = [
    //         'stats' => 1,
    //         'rating' => 1,
    //     ];

    //     $keepa_category = KeepaCategory::find($req->keepa_cat_idx);

    //     # chunk product asin's to keepa limit for parsing information
    //     $asin_chunks = array_chunk($keepa_category->asin_list, $asin_chunk_size);


    //     foreach ($asin_chunks as $key => $parsable_list) {
    //         # fetche keepa products by asin list
    //         $keepa = new Keepa(env('KEEPA_API_KEY'));
    //         $keepa_products = $keepa->asin($parsable_list, $product_param);
    //         if(!$keepa_products->responseJSON()) continue;

    //         #store json data to storate
    //         $json_path = "keepa_products/".date('Y-m')."/$keepa_category->category_id/$key.json";
    //         Storage::disk('spaces')->put($json_path, $keepa_products->responseJSON(), 'public');

    //         $exists_products = KeepaProduct::whereIn('asin', $keepa_category->asin_list)->pluck('asin')->toArray();

    //         $insert = [];
    //         foreach ($parsable_list as $chunk_idx => $asin) {

    //             $keepa_product = $keepa_products->product($asin);

    //             if(empty($keepa_product->asin)) continue;

    //             if(\in_array($keepa_product->asin, $exists_products)){
    //                 $update = [
    //                     'category_id' => $keepa_category->category_id,
    //                     'asin' => $keepa_product->asin,
    //                     'ean' => json_encode($keepa_product->eanList),
    //                     'title' => $keepa_product->title,
    //                     'images' => $keepa_product->imagesCSV,
    //                 ];
    //                 KeepaProduct::where(['asin' => $keepa_product->asin])->update($update);
    //             }else{
    //                 $insert[] = [
    //                     'category_id' => $keepa_category->category_id,
    //                     'asin' => $keepa_product->asin,
    //                     'ean' => json_encode($keepa_product->eanList),
    //                     'title' => $keepa_product->title,
    //                     'images' => $keepa_product->imagesCSV,
    //                 ];
    //             }
    //         }
    //         #insert each category chunks once
    //         KeepaProduct::insert($insert);
    //     }
    // }

    // /**
    //  * Generate monthly report csv from json
    //  * @param keepa_cat_id
    //  * <url>/admin/drm_products/generate_trend_report?keepa_cat_id=355007011
    //  */
    // function generateTrendReport(Request $request){

    //     if(!$request->keepa_cat_id) return false;
    //     $json_path = "keepa_products/".date('Y-m')."/$request->keepa_cat_id/";

    //     $files = Storage::disk('spaces')->allFiles($json_path, 'public');

    //     foreach ($files as $key => $file) {
    //         $response = file_get_contents(Storage::disk('spaces')->url($file)) ;

    //         $keepa = new Keepa(env('KEEPA_API_KEY'));
    //         $keepa_response = $keepa->setResponse($response);

    //         $product_asins = $keepa_response->getASINList();

    //         $csv_header = [
    //             'Title',
    //             'Country',
    //             'Images',
    //             'ASIN',
    //             'Brand',
    //             'Sales Rank (current)',
    //             'Sales Rank (DROP 30)',
    //             'Rating',
    //             'Review Count',
    //             'Amazon (current)',
    //             'Amazon (AVG 90)',
    //             'New (current)',
    //             'New (AVG 90)',
    //         ];

    //         $csv_array = [];
    //         if(!$product_asins) continue;
    //         foreach ($product_asins as $key => $asin) {
    //             $keepa_product = $keepa_response->select($asin);

    //             $csv_array[] = [
    //                 $keepa_product->title(),
    //                 $keepa_product->country(),
    //                 implode(',', $keepa_product->images()),
    //                 $asin,
    //                 $keepa_product->brand(),
    //                 $keepa_product->stats('current', 'sales'),
    //                 $keepa_product->stats('salesRankDrops30'),
    //                 $keepa_product->stats('current', 'rating'),
    //                 $keepa_product->stats('current', 'count_reviews'),
    //                 $keepa_product->stats('current', 'amazon'),
    //                 $keepa_product->stats('avg90', 'amazon'),
    //                 $keepa_product->stats('current', 'new'),
    //                 $keepa_product->stats('avg90', 'new'),
    //             ];
    //         }

    //         #generate csv
    //         $csv = Writer::createFromString();
    //         $csv->insertOne($csv_header);
    //         $csv->insertAll($csv_array);
    //         $csv_data = makeUtf8($csv->getContent());

    //         $report_file_name = 'monthly-report.csv';
    //         Storage::disk('spaces')->put($json_path.$report_file_name, $csv_data, 'public');

    //         echo Storage::disk('spaces')->url($json_path.$report_file_name);

    //     }
    // }

    // /**
    //  * Fetch Best Seller ASIN list (30 days avg) for a Keepa Category
    //  * This list will be used to generate monthy csv report
    //  */
    // public function fetchAvgKeepaCategories(){

    //     $app_ids = TrendCategories::select('drm_app_id', 'keepa_cat_id')->get();
    //     foreach ($app_ids->toArray() as $key => $app) {
    //         $category_id = $app['keepa_cat_id'];
    //         $keepa = new Keepa(env('KEEPA_API_KEY'));
    //         $asin_list = $keepa->setCategory($category_id)->bestSellers(30)->asinList(0, 5000);

    //         if(!$asin_list) continue;
    //         $keepa_data = [
    //             'category_id' => $category_id,
    //             'asin_list' => $asin_list,
    //             'range' => '30'
    //         ];
    //         $keepa_filter = [
    //             'category_id' => $category_id,
    //             'range' => '30'
    //         ];
    //         KeepaCategory::updateOrCreate($keepa_filter, $keepa_data);
    //     }
    // }

    /**
     * Fetch current best seller asin list from keepa to database
     * This later used to populate keepa_products table
     *
     * data_type => ( product | category )
     *      category: top product asin id by category
     *      product: product information (without csv history and stats)
     *          for product type return data can be configured by $product_param
     *
     */
    // public function fetchKeepaCategories(){
    //     $app_ids = TrendCategories::select('drm_app_id', 'keepa_cat_id')->get();

    //     foreach ($app_ids->toArray() as $key => $app) {
    //         $category_id = $app['keepa_cat_id'];
    //         $keepa = new Keepa(env('KEEPA_API_KEY'));
    //         $asin_list = $keepa->setCategory($category_id)->bestSellers()->asinList(0, 5000);

    //         if(!$asin_list) continue;
    //         $keepa_data = [
    //             'category_id' => $category_id,
    //             'asin_list' => $asin_list,
    //         ];
    //         $keepa_filter = [
    //             'category_id' => $category_id,
    //             'range' => '0'
    //         ];
    //         KeepaCategory::updateOrCreate($keepa_filter, $keepa_data);
    //     }
    // }

    // /**
    //  * Trend Importer
    //  * Transfer Products from market place to users drm channel
    //  */
    // public function trendImporter(Request $request){

    //     $app_ids = TrendCategories::select('drm_app_id')->pluck('drm_app_id');
    //     $purchased_list = PurchasedApp::with('trend_categories:keepa_cat_id,drm_app_id')
    //                         ->whereIn('app_id', $app_ids)
    //                         // ->where('cms_user_id', $user_id)
    //                         ->get();

    //     $user_id = $request->get('user_id') ?? CRUDBooster::myId();

    //     $plans = config('global.trend_importer_app_plans');

    //     $matched_products = [];
    //     foreach ($purchased_list as $key => $purchased) {

    //         if( !isset($plans[$purchased->plan_id]) ) continue;
    //         $package_limit = $plans[$purchased->plan_id];

    //         $keepa_category = $purchased->trend_categories['keepa_cat_id'];
    //         $products = KeepaProduct::select('category_id', 'ean')
    //         ->whereNotNull('ean')
    //         ->where('ean', '!=', 'null')
    //         ->where('category_id', $keepa_category)
    //         ->get()->toArray();

    //         $ean_list = array_column($products, 'ean');
    //         $ean_list = array_merge(...$ean_list);
    //         if(!$ean_list) return false;
    //         $marketplace_products = MarketplaceProducts::select('id', 'name')
    //         ->whereIn('ean', $ean_list)
    //         ->take($package_limit)
    //         ->get();

    //         #eliminate diplicate entries
    //         foreach ($marketplace_products as $key => $product) {
    //             $matched_products[$product->id] = $product->name;
    //         }
    //     }
    //     $data_override = [
    //         'product_type' => 'automatic_product',
    //         'user_id' => $user_id
    //     ];
    //     foreach ($matched_products as $product_id => $name) {
    //         app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)
    //         ->transferProductToDrm($product_id, $data_override);
    //     }
    // }


    public function getConnectedShops(Request $request)
    {
        $product = DrmProduct::find($request->product_id);
        return $product->connected_channels;
    }


    public function checkTransferMarketplace(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $exists = 0;

        if($user_id == 62)
        {
            return response([
                'exists' => 0,
                'total'  => 10,
                'total_not_allow_products'  => 0
            ]);
        }
        $product_ids = $request->product_ids;
        if((int)$request->selected_all){
            $product_ids = $this->productService->getSelectedIds($user_id,$request->params);
        }

        if(in_array($request->channel,Channel::MP_BLACKLIST) && !in_array($user_id,Channel::MP_USER_WHITELIST[$request->channel])){
            $exists = DrmProduct::where([
                'user_id' => $user_id,
            ])->whereIn('id',$product_ids)->whereNotNull('marketplace_product_id')->count();
        }

        $languageId = app('App\Services\UserService')->getProductCountry($user_id);

        $total_not_allow_product = 0;

        // DrmProduct::select('marketplace_product_id')->where([
        //     'user_id' => $user_id,
        //     'country_id' => $languageId
        // ])
        // ->whereIn('id',$product_ids)
        // ->whereNotNull('marketplace_product_id')
        // ->chunk(100, function($products) use (&$total_not_allow_product, $request){
        //     foreach($products as $product){

        //         $allowed_product = true;

        //         $allowed_product = app('\App\Services\ChannelProductService')->isAllowed($product->marketplace_product_id, $request->channel, $request->params["lang"]);

        //         if(!$allowed_product){
        //             $total_not_allow_product ++;
        //         }

        //     }
        // });

        if($product_ids){

            foreach (array_chunk($product_ids, 200) as $ids){

                $check_not_allowed_product = DrmProduct::select('marketplace_product_id')->where([
                    'user_id' => $user_id,
                    'country_id' => $languageId
                ])->whereIn('id',$ids)->whereNotNull('marketplace_product_id')->get();

                if($check_not_allowed_product){

                    foreach($check_not_allowed_product as $product){

                        $allowed_product = true;

                        if(!empty($product->marketplace_product_id)){
                            $allowed_product = app('\App\Services\ChannelProductService')->isAllowed($product->marketplace_product_id, $request->channel, $request->params["lang"]);
                        }

                        if(!$allowed_product){
                            $total_not_allow_product ++;
                        }
                    }

                }

            }

        }

        // dd($total_not_allow_product);

        return response([
            'exists' => $exists,
            'total'  => count($product_ids),
            'total_not_allow_products'  => $total_not_allow_product
        ]);
    }

    public function getIndustryTemplateModal(){
        $product_id = $_REQUEST['product_id'];

        $product_industry_template = DrmProduct::where(['user_id' => CRUDBooster::myParentId(), 'id' => $product_id])->first();
        $decoded_industry_template = json_decode($product_industry_template->industry_template_data, true) ?? [];

        if($decoded_industry_template && $product_industry_template->IndustryTemplateHasName()){
            foreach($decoded_industry_template as $key => $template_name){

                if ($key == "supplements"){
                    $template_key = ucfirst('lebensmittel/supplements');
                }else{
                    $template_key = ucfirst($key);
                }

                if(in_array($template_key, array_keys(config('industry_template')))){
                    $selected_product_template[$template_key] = array_map( function($value){
                        return __('industry_template.'.$value);
                    }, array_keys($template_name) );
                }else{
                    $selected_product_template[$template_key] = array_map( function($value){
                        return $value;
                    }, array_keys($template_name) );
                }

                return response()->json(['success' => true, 'data' => $selected_product_template], 200);
            }
        }

        return response()->json([ 'success' => false, 'message' => 'Industry Template Fetching Error !' ], 401);
    }

    public function getIndustryTemplateField(){
        $selected_template_name = $_REQUEST['selected_template_name'];

        if( in_array($selected_template_name, array_keys(config('industry_template'))) ){
        $selected_template_field_name = config("industry_template.".$selected_template_name);
        $selected_template_field_name = array_keys($selected_template_field_name);
        }else{
            $custom_temp = CustomIndustryTemplate::where('id', $selected_template_name)->select('name', 'fields')->first();
            $selected_template_field_name = $custom_temp->fields;
        }

        $input_field = '';
        foreach($selected_template_field_name as $name){

            if( in_array($selected_template_name, array_keys(config('industry_template'))) ){
            $trans_field_name = __('industry_template.'.$name);

            $input_field .= '
            <div class="form-group">
                <div class="col-md-3"></div>
                <div class="col-md-7 inputGroupContainer">
                    <div class="input-group">
                        <span class="input-group-addon">
                            <i class="fa fa-list-alt"></i>
                        </span>
                        <input name="' . $selected_template_name . '[' . $name . ']' . '" placeholder="' . $trans_field_name . '" class="form-control" type="text">
                    </div>
                </div>
            </div>
            ';
            }else{
                $trans_field_name = $name['name'];

                $field_required = '';
                if(isset($name['required']) && $name['required']){
                    $field_required = 'required';
                }

                if (!array_key_exists("value", $name)){
                    $input_field .= '
                    <div class="form-group">
                        <div class="col-md-3"></div>
                        <div class="col-md-7 inputGroupContainer">
                            <label class="control-label">'.$trans_field_name.'</label>
                            <div class="input-group">
                                <span class="input-group-addon">
                                    <i class="fa fa-list-alt"></i>
                                </span>
                                <input name="' . $custom_temp->name . '[' . $trans_field_name . ']' . '" placeholder="' . $trans_field_name . '" class="form-control" type="text" '.$field_required.'>
                            </div>
                        </div>
                    </div>
                    ';
                }else{
                    $all_options = explode(",", $name['value']);
                    $all_options = array_filter($all_options);
                    $option = '';

                    if(!empty($all_options)){
                        array_walk($all_options, function($item) use(&$option){
                            $option .= '<option value="'.$item.'">'.$item.'</option>';
                        });
                    }

                    if($option){
                        $input_field .= '
                        <div class="form-group">
                            <div class="col-md-3"></div>
                            <div class="col-md-7 inputGroupContainer">
                                <label class="control-label">'.$trans_field_name.'</label>
                                <div class="input-group">
                                    <span class="input-group-addon">
                                        <i class="fa fa-list-alt"></i>
                                    </span>
                                    <select class="form-control" name="' . $custom_temp->name . '[' . $trans_field_name . ']' . '" '.$field_required.'>'.$option.'</select>
                                </div>
                            </div>
                        </div>
                        ';
                    }
                }
            }
        }

        return response()->json(['success' => true, 'data' => $input_field], 200);
    }

    public function getIndustryTemplateFieldForDetailPage(){
        $selected_template_name = $_REQUEST['selected_template_name'];

        if( in_array($selected_template_name, array_keys(config('industry_template'))) ){
        $selected_template_field_name = config("industry_template.".$selected_template_name);
        $selected_template_field_name = array_keys($selected_template_field_name);
        }else{
            $custom_temp = CustomIndustryTemplate::where('id', $selected_template_name)->select('name', 'fields')->first();
            $selected_template_field_name = $custom_temp->fields;
        }

        $input_field = '';
        foreach($selected_template_field_name as $name){

            if( in_array($selected_template_name, array_keys(config('industry_template'))) ){
            $trans_field_name = __('industry_template.'.$name);
            $input_field .= '
            <div class="row" style="padding-bottom: 15px;">
                <label for="goodsName" class="col-sm-1 control-label" style="width:200px">' . $trans_field_name . '</label>
                <div class="col-sm-8">
                    <input id="' . $selected_template_name . '[' . $name . ']' . '" placeholder="' . $trans_field_name . '" class="form-control" type="text">
                </div>
            </div>
            ';
            }else{
                $trans_field_name = $name['name'];

                $field_required = '';
                if(isset($name['required']) && $name['required']){
                    $field_required = 'required';
                }

                if (!array_key_exists("value", $name)){
                    $input_field .= '
                    <div class="row" style="padding-bottom: 15px;">
                        <label for="goodsName" class="col-sm-1 control-label" style="width:200px">' . $trans_field_name . '</label>
                        <div class="col-sm-8">
                            <input id="' . $custom_temp->name . '[' . $trans_field_name . ']' . '" placeholder="' . $trans_field_name . '" class="form-control" type="text" '.$field_required.'>
                        </div>
                    </div>
                    ';
                }else{
                    $all_options = explode(",", $name['value']);
                    $all_options = array_filter($all_options);
                    $option = '';

                    if(!empty($all_options)){
                        array_walk($all_options, function($item) use(&$option){
                            $option .= '<option value="'.$item.'">'.$item.'</option>';
                        });
                    }

                    if($option){
                        $input_field .= '
                        <div class="row" style="padding-bottom: 15px;">
                            <label for="goodsName" class="col-sm-1 control-label" style="width:200px">' . $trans_field_name . '</label>
                            <div class="col-sm-8">
                            <select class="form-control" id="' . $custom_temp->name . '[' . $trans_field_name . ']' . '" '.$field_required.'>'.$option.'</select>
                            </div>
                        </div>
                        ';
                    }
                }
            }
        }

        if( !in_array($selected_template_name, array_keys(config('industry_template'))) ){
            $temp_fileds_array = [];
            foreach($selected_template_field_name as $fields){
                if(array_key_exists("value", $fields) && !empty($fields['value'])){
                    $temp_fileds_array[] = $fields['name'];
                }else if(!array_key_exists("value", $fields)){
                    $temp_fileds_array[] = $fields['name'];
                }
            }

            $selected_template_field_name = $temp_fileds_array;
        }

        return response()->json(['success' => true, 'data' => $input_field, 'field' => $selected_template_field_name], 200);
    }

    public function selectedCountry(){
        $country = request()->country;
        $product_id = request()->product_id;
        $updated_channels = MarketplaceAllowedChannel::where('country_id', $country)
                                                    ->where('product_id', $product_id)
                                                    ->first();
        $updated_product = MarketplaceAllowedChannel::where('product_id', $product_id)
                                                    ->first();

        $channel_id = $updated_channels->channel_id;
        $channels = collect(config('channel.list'));
        $disable_channel = \App\Enums\Channel::MP_BLACKLIST;
        if(empty($updated_product) && empty($channel_id[0]) && $country == 1){
           $active_country = 'de';
        }
        return view('marketplace.product.drm_product_flag',compact('channel_id','channels','disable_channel','active_country'));
    }

    public function updateStockPercentage(){

        $product_id = (int) $_REQUEST['product_id'];
        $channel = (int) $_REQUEST['channel'];
        $lang = $_REQUEST['lang'];
        $stock_percent = (int) $_REQUEST['stock_percent'];

        $response = app('\App\Services\ChannelProductService')->transferProduct(
            $product_id,
            $channel,
            [],
            $lang,
            "update",
            false,
            $stock_percent,
            ['shop_id' => '']
        );

        if($response){
            return response()->json(["success" => true, "message" => "Stock Update Successful!"]);
        }else{
            return response()->json(["success" => false, "message" => "Something Went Wrong"], 400);
        }
    }

    public function checkStockDistributionAppPurchase(){

        $channel_app_id = (int) $_REQUEST['channel_app_id'];
        $user_id = CRUDBooster::myParentId();
        $status = CheckPurchesedPlanAccess($user_id,[26,27]) ?: CheckPurchesedAppAccess($user_id,[$channel_app_id]);
        return response()->json( ["success" => $status] );

    }

    public function selectAllProducts(){
        $param = $_REQUEST['params'];
        $user_id = CRUDBooster::myParentId();

        $product_ids = $this->productService->getSelectedIds($user_id, $param);

        return response()->json([
            'success' => true,
            'data' => $product_ids
        ]);
    }

    public function selectAllProductsDelete()
    {
        if(!$this->canAccess('delete')){
            CRUDBooster::redirect(CRUDBooster::adminPath(), "You do not have permission to access this area !");
        }

        $ids = json_decode($_REQUEST['product_ids']);

        $languageId = app('App\Services\UserService')->getProductCountry(CRUDBooster::myParentId());
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        try {
            foreach(array_chunk($ids, 500) as $chunkIds){
                DestroyProduct::dispatch($chunkIds, CRUDBooster::myParentId(),$lang,$languageId);
            }

            try { // selected all
                \App\AgbLogs::create([
                    'user_id' => CRUDBooster::myParentId(),
                    'agb' => [
                        'product_ids' => $ids,
                    ],
                    'message' => count($ids) . ' items deleted from main label.',
                    'ip_address' => getIpAddress(),
                ]);
            } catch (\Exception $th) {}

            return response()->json([
                'success' => true,
                'message' => 'All products delete process started. It may take some time to delete all products.'
            ]);
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 401);
        }

    }

    public function addShippingCompany(Request $request) {
        $user_id = CRUDBooster::myParentId();
        DB::table('drm_shipping_companies')->updateOrInsert([
            'user_id' => $user_id,
            'shipping_company' => $request->company_name
        ]);
        return response()->json([
            'success' => true,
            'message' => 'Shipping Company Added Successfully'
        ]);
    }

    public function shippingMethodSeeder(){
        $methods = ["Self collection", "Parcel shipping", "Shipping company shipping"];
        foreach($methods as $method){
            DB::table('drm_shipping_methods')->insert(
                [
                    'shipping_method' => $method
                ]
            );
        }

        $companies = ["DHL", "Hermes", "UPS", "DPD", "GLS", "FedEx/TNT", "Schenker", "Other"];
        foreach($companies as $company){
            DB::table('drm_shipping_companies')->insert(
                [
                    'user_id' => 2878,
                    'shipping_method' => $company
                ]
            );
        }
        return "succcessfull";
    }

    public function countryListSeeder() {
        $country_list = [
            "AF" => "Afghanistan",
            "AL"=> "Albania",
            "DZ"=> "Algeria",
            "AS"=> "American Samoa",
            "AD"=> "Andorra",
            "AO"=> "Angola",
            "AI"=> "Anguilla",
            "AQ"=> "Antarctica",
            "AG"=> "Antigua and Barbuda",
            "AR"=> "Argentina",
            "AM"=> "Armenia",
            "AW"=> "Aruba",
            "AU"=> "Australia",
            "AT"=> "Austria",
            "AZ"=> "Azerbaijan",
            "BS"=> "Bahamas (the)",
            "BH"=> "Bahrain",
            "BD"=> "Bangladesh",
            "BB"=> "Barbados",
            "BY"=> "Belarus",
            "BE"=> "Belgium",
            "BZ"=> "Belize",
            "BJ"=> "Benin",
            "BM"=> "Bermuda",
            "BT"=> "Bhutan",
            "BO"=> "Bolivia (Plurinational State of)",
            "BQ"=> "Bonaire, Sint Eustatius and Saba",
            "BA"=> "Bosnia and Herzegovina",
            "BW"=> "Botswana",
            "BV"=> "Bouvet Island",
            "BR"=> "Brazil",
            "IO"=> "British Indian Ocean Territory (the)",
            "BN"=> "Brunei Darussalam",
            "BG"=> "Bulgaria",
            "BF"=> "Burkina Faso",
            "BI"=> "Burundi",
            "CV"=> "Cabo Verde",
            "KH"=> "Cambodia",
            "CM"=> "Cameroon",
            "CA"=> "Canada",
            "KY"=> "Cayman Islands (the)",
            "CF"=> "Central African Republic (the)",
            "TD"=> "Chad",
            "CL"=> "Chile",
            "CN"=> "China",
            "CX"=> "Christmas Island",
            "CC"=> "Cocos (Keeling) Islands (the)",
            "CO"=> "Colombia",
            "KM"=> "Comoros (the)",
            "CD"=> "Congo (the Democratic Republic of the)",
            "CG"=> "Congo (the)",
            "CK"=> "Cook Islands (the)",
            "CR"=> "Costa Rica",
            "HR"=> "Croatia",
            "CU"=> "Cuba",
            "CW"=> "Curaçao",
            "CY"=> "Cyprus",
            "CZ"=> "Czechia",
            "CI"=> "Côte d'Ivoire",
            "DK"=> "Denmark",
            "DJ"=> "Djibouti",
            "DM"=> "Dominica",
            "DO"=> "Dominican Republic (the)",
            "EC"=> "Ecuador",
            "EG"=> "Egypt",
            "SV"=> "El Salvador",
            "GQ"=> "Equatorial Guinea",
            "ER"=> "Eritrea",
            "EE"=> "Estonia",
            "SZ"=> "Eswatini",
            "ET"=> "Ethiopia",
            "FK"=> "Falkland Islands (the) [Malvinas]",
            "FO"=> "Faroe Islands (the)",
            "FJ"=> "Fiji",
            "FI"=> "Finland",
            "FR"=> "France",
            "GF"=> "French Guiana",
            "PF"=> "French Polynesia",
            "TF"=> "French Southern Territories (the)",
            "GA"=> "Gabon",
            "GM"=> "Gambia (the)",
            "GE"=> "Georgia",
            "DE"=> "Germany",
            "GH"=> "Ghana",
            "GI"=> "Gibraltar",
            "GR"=> "Greece",
            "GL"=> "Greenland",
            "GD"=> "Grenada",
            "GP"=> "Guadeloupe",
            "GU"=> "Guam",
            "GT"=> "Guatemala",
            "GG"=> "Guernsey",
            "GN"=> "Guinea",
            "GW"=> "Guinea-Bissau",
            "GY"=> "Guyana",
            "HT"=> "Haiti",
            "HM"=> "Heard Island and McDonald Islands",
            "VA"=> "Holy See (the)",
            "HN"=> "Honduras",
            "HK"=> "Hong Kong",
            "HU"=> "Hungary",
            "IS"=> "Iceland",
            "IN"=> "India",
            "ID"=> "Indonesia",
            "IR"=> "Iran (Islamic Republic of)",
            "IQ"=> "Iraq",
            "IE"=> "Ireland",
            "IM"=> "Isle of Man",
            "IL"=> "Israel",
            "IT"=> "Italy",
            "JM"=> "Jamaica",
            "JP"=> "Japan",
            "JE"=> "Jersey",
            "JO"=> "Jordan",
            "KZ"=> "Kazakhstan",
            "KE"=> "Kenya",
            "KI"=> "Kiribati",
            "KP"=> "Korea (the Democratic People's Republic of)",
            "KR"=> "Korea (the Republic of)",
            "KW"=> "Kuwait",
            "KG"=> "Kyrgyzstan",
            "LA"=> "Lao People's Democratic Republic (the)",
            "LV"=> "Latvia",
            "LB"=> "Lebanon",
            "LS"=> "Lesotho",
            "LR"=> "Liberia",
            "LY"=> "Libya",
            "LI"=> "Liechtenstein",
            "LT"=> "Lithuania",
            "LU"=> "Luxembourg",
            "MO"=> "Macao",
            "MG"=> "Madagascar",
            "MW"=> "Malawi",
            "MY"=> "Malaysia",
            "MV"=> "Maldives",
            "ML"=> "Mali",
            "MT"=> "Malta",
            "MH"=> "Marshall Islands (the)",
            "MQ"=> "Martinique",
            "MR"=> "Mauritania",
            "MU"=> "Mauritius",
            "YT"=> "Mayotte",
            "MX"=> "Mexico",
            "FM"=> "Micronesia (Federated States of)",
            "MD"=> "Moldova (the Republic of)",
            "MC"=> "Monaco",
            "MN"=> "Mongolia",
            "ME"=> "Montenegro",
            "MS"=> "Montserrat",
            "MA"=> "Morocco",
            "MZ"=> "Mozambique",
            "MM"=> "Myanmar",
            "NA"=> "Namibia",
            "NR"=> "Nauru",
            "NP"=> "Nepal",
            "NL"=> "Netherlands (the)",
            "NC"=> "New Caledonia",
            "NZ"=> "New Zealand",
            "NI"=> "Nicaragua",
            "NE"=> "Niger (the)",
            "NG"=> "Nigeria",
            "NU"=> "Niue",
            "NF"=> "Norfolk Island",
            "MP"=> "Northern Mariana Islands (the)",
            "NO"=> "Norway",
            "OM"=> "Oman",
            "PK"=> "Pakistan",
            "PW"=> "Palau",
            "PS"=> "Palestine, State of",
            "PA"=> "Panama",
            "PG"=> "Papua New Guinea",
            "PY"=> "Paraguay",
            "PE"=> "Peru",
            "PH"=> "Philippines (the)",
            "PN"=> "Pitcairn",
            "PL"=> "Poland",
            "PT"=> "Portugal",
            "PR"=> "Puerto Rico",
            "QA"=> "Qatar",
            "MK"=> "Republic of North Macedonia",
            "RO"=> "Romania",
            "RU"=> "Russian Federation (the)",
            "RW"=> "Rwanda",
            "RE"=> "Réunion",
            "BL"=> "Saint Barthélemy",
            "SH"=> "Saint Helena, Ascension and Tristan da Cunha",
            "KN"=> "Saint Kitts and Nevis",
            "LC"=> "Saint Lucia",
            "MF"=> "Saint Martin (French part)",
            "PM"=> "Saint Pierre and Miquelon",
            "VC"=> "Saint Vincent and the Grenadines",
            "WS"=> "Samoa",
            "SM"=> "San Marino",
            "ST"=> "Sao Tome and Principe",
            "SA"=> "Saudi Arabia",
            "SN"=> "Senegal",
            "RS"=> "Serbia",
            "SC"=> "Seychelles",
            "SL"=> "Sierra Leone",
            "SG"=> "Singapore",
            "SX"=> "Sint Maarten (Dutch part)",
            "SK"=> "Slovakia",
            "SI"=> "Slovenia",
            "SB"=> "Solomon Islands",
            "SO"=> "Somalia",
            "ZA"=> "South Africa",
            "GS"=> "South Georgia and the South Sandwich Islands",
            "SS"=> "South Sudan",
            "ES"=> "Spain",
            "LK"=> "Sri Lanka",
            "SD"=> "Sudan (the)",
            "SR"=> "Suriname",
            "SJ"=> "Svalbard and Jan Mayen",
            "SE"=> "Sweden",
            "CH"=> "Switzerland",
            "SY"=> "Syrian Arab Republic",
            "TW"=> "Taiwan",
            "TJ"=> "Tajikistan",
            "TZ"=> "Tanzania, United Republic of",
            "TH"=> "Thailand",
            "TL"=> "Timor-Leste",
            "TG"=> "Togo",
            "TK"=> "Tokelau",
            "TO"=> "Tonga",
            "TT"=> "Trinidad and Tobago",
            "TN"=> "Tunisia",
            "TR"=> "Turkey",
            "TM"=> "Turkmenistan",
            "TC"=> "Turks and Caicos Islands (the)",
            "TV"=> "Tuvalu",
            "UG"=> "Uganda",
            "UA"=> "Ukraine",
            "AE"=> "United Arab Emirates (the)",
            "GB"=> "United Kingdom of Great Britain and Northern Ireland (the)",
            "UM"=> "United States Minor Outlying Islands (the)",
            "US"=> "United States of America (the)",
            "UY"=> "Uruguay",
            "UZ"=> "Uzbekistan",
            "VU"=> "Vanuatu",
            "VE"=> "Venezuela (Bolivarian Republic of)",
            "VN"=> "Viet Nam",
            "VG"=> "Virgin Islands (British)",
            "VI"=> "Virgin Islands (U.S.)",
            "WF"=> "Wallis and Futuna",
            "EH"=> "Western Sahara",
            "YE"=> "Yemen",
            "ZM"=> "Zambia",
            "ZW"=> "Zimbabwe",
            "AX"=> "Åland Islands"
        ];
        foreach($country_list as $key => $value){
            DB::table('all_country')->insert([
                'country' => $value,
                'country_code' => $key
            ]);
        }
    }

    public function dbMod() {

        ini_set('max_execution_time', -1);
        try{
            $products = DrmProduct::where('user_id', 2947)->get();
            foreach($products as $product){
                $stock = [];
                $price = [];
                if($product->status == 1000 || !in_array($product->status, ['1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000']) || $product->status == null){
                    $stock[1000] = ($product->stock ?? 0);
                }
                elseif($product->status != 1000 && in_array($product->status, ['1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000'])){
                    $stock[1000] = 0;
                    $stock[$product->status] = ($product->stock ?? 0);
                }
                if($product->status == 1000 || !in_array($product->status, ['1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000']) || $product->status == null){
                    $price[1000] = ($product->vk_price ?? 0);
                }
                elseif($product->status != 1000 && in_array($product->status, ['1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000'])){
                    $price[1000] = 0;
                    $price[$product->status] = ($product->vk_price ?? 0);
                }
                DrmProduct::where('id', $product->id)->update([
                    'stock_of_status' => json_encode($stock),
                    'price_of_status' => json_encode($price)
                ]);

                if($product->item_color != null && $product->item_color != ""){
                    $color = $product->item_color;
                    $colors = preg_split("/[\s,-\/]+/", $color);
                    $options = [
                        'color' => $colors
                    ];
                    DrmProduct::where('id', $product->id)->update([
                        'options' => json_encode($options)
                    ]);
                }
            }

            // $ids = DrmProduct::select('user_id')->distinct()->get();
            // foreach($ids as $user){
            //     if($user->user_id != 212 && $user->user_id != 2387){
            //         $products = DrmProduct::where('user_id', $user->user_id)->get();
            //         foreach($products as $product){
            //             $stock = [];
            //             $price = [];
            //             if($product->status == 1000 || !in_array($product->status, ['1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000']) || $product->status == null){
            //                 $stock[1000] = ($product->stock ?? 0);
            //             }
            //             elseif($product->status != 1000 && in_array($product->status, ['1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000'])){
            //                 $stock[1000] = 0;
            //                 $stock[$product->status] = ($product->stock ?? 0);
            //             }
            //             if($product->status == 1000 || !in_array($product->status, ['1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000']) || $product->status == null){
            //                 $price[1000] = ($product->vk_price ?? 0);
            //             }
            //             elseif($product->status != 1000 && in_array($product->status, ['1500', '1750', '2000', '2500', '2750', '3000', '4000', '5000', '6000', '7000'])){
            //                 $price[1000] = 0;
            //                 $price[$product->status] = ($product->vk_price ?? 0);
            //             }
            //             DrmProduct::where('id', $product->id)->update([
            //                 'stock_of_status' => json_encode($stock),
            //                 'price_of_status' => json_encode($price)
            //             ]);

            //             if($product->item_color != null && $product->item_color != ""){
            //                 $color = $product->item_color;
            //                 $colors = preg_split("/[\s,-\/]+/", $color);
            //                 $options = [
            //                     'color' => $colors
            //                 ];
            //                 DrmProduct::where('id', $product->id)->update([
            //                     'options' => json_encode($options)
            //                 ]);
            //             }
            //         }
            //     }
            // }



            // $methods = ["Self collection", "Parcel shipping", "Shipping company shipping"];
            // foreach($methods as $method){
            //     DB::table('drm_shipping_methods')->insert(
            //         [
            //             'shipping_method' => $method
            //         ]
            //     );
            // }

            // $companies = ["DHL", "Hermes", "UPS", "DPD", "GLS", "FedEx/TNT", "Schenker", "Other"];
            // foreach($companies as $company){
            //     DB::table('drm_shipping_companies')->insert(
            //         [
            //             'user_id' => 2878,
            //             'shipping_company' => $company
            //         ]
            //     );
            // }


            // $country_list = [
            //     "AF" => "Afghanistan",
            //     "AL"=> "Albania",
            //     "DZ"=> "Algeria",
            //     "AS"=> "American Samoa",
            //     "AD"=> "Andorra",
            //     "AO"=> "Angola",
            //     "AI"=> "Anguilla",
            //     "AQ"=> "Antarctica",
            //     "AG"=> "Antigua and Barbuda",
            //     "AR"=> "Argentina",
            //     "AM"=> "Armenia",
            //     "AW"=> "Aruba",
            //     "AU"=> "Australia",
            //     "AT"=> "Austria",
            //     "AZ"=> "Azerbaijan",
            //     "BS"=> "Bahamas (the)",
            //     "BH"=> "Bahrain",
            //     "BD"=> "Bangladesh",
            //     "BB"=> "Barbados",
            //     "BY"=> "Belarus",
            //     "BE"=> "Belgium",
            //     "BZ"=> "Belize",
            //     "BJ"=> "Benin",
            //     "BM"=> "Bermuda",
            //     "BT"=> "Bhutan",
            //     "BO"=> "Bolivia (Plurinational State of)",
            //     "BQ"=> "Bonaire, Sint Eustatius and Saba",
            //     "BA"=> "Bosnia and Herzegovina",
            //     "BW"=> "Botswana",
            //     "BV"=> "Bouvet Island",
            //     "BR"=> "Brazil",
            //     "IO"=> "British Indian Ocean Territory (the)",
            //     "BN"=> "Brunei Darussalam",
            //     "BG"=> "Bulgaria",
            //     "BF"=> "Burkina Faso",
            //     "BI"=> "Burundi",
            //     "CV"=> "Cabo Verde",
            //     "KH"=> "Cambodia",
            //     "CM"=> "Cameroon",
            //     "CA"=> "Canada",
            //     "KY"=> "Cayman Islands (the)",
            //     "CF"=> "Central African Republic (the)",
            //     "TD"=> "Chad",
            //     "CL"=> "Chile",
            //     "CN"=> "China",
            //     "CX"=> "Christmas Island",
            //     "CC"=> "Cocos (Keeling) Islands (the)",
            //     "CO"=> "Colombia",
            //     "KM"=> "Comoros (the)",
            //     "CD"=> "Congo (the Democratic Republic of the)",
            //     "CG"=> "Congo (the)",
            //     "CK"=> "Cook Islands (the)",
            //     "CR"=> "Costa Rica",
            //     "HR"=> "Croatia",
            //     "CU"=> "Cuba",
            //     "CW"=> "Curaçao",
            //     "CY"=> "Cyprus",
            //     "CZ"=> "Czechia",
            //     "CI"=> "Côte d'Ivoire",
            //     "DK"=> "Denmark",
            //     "DJ"=> "Djibouti",
            //     "DM"=> "Dominica",
            //     "DO"=> "Dominican Republic (the)",
            //     "EC"=> "Ecuador",
            //     "EG"=> "Egypt",
            //     "SV"=> "El Salvador",
            //     "GQ"=> "Equatorial Guinea",
            //     "ER"=> "Eritrea",
            //     "EE"=> "Estonia",
            //     "SZ"=> "Eswatini",
            //     "ET"=> "Ethiopia",
            //     "FK"=> "Falkland Islands (the) [Malvinas]",
            //     "FO"=> "Faroe Islands (the)",
            //     "FJ"=> "Fiji",
            //     "FI"=> "Finland",
            //     "FR"=> "France",
            //     "GF"=> "French Guiana",
            //     "PF"=> "French Polynesia",
            //     "TF"=> "French Southern Territories (the)",
            //     "GA"=> "Gabon",
            //     "GM"=> "Gambia (the)",
            //     "GE"=> "Georgia",
            //     "DE"=> "Germany",
            //     "GH"=> "Ghana",
            //     "GI"=> "Gibraltar",
            //     "GR"=> "Greece",
            //     "GL"=> "Greenland",
            //     "GD"=> "Grenada",
            //     "GP"=> "Guadeloupe",
            //     "GU"=> "Guam",
            //     "GT"=> "Guatemala",
            //     "GG"=> "Guernsey",
            //     "GN"=> "Guinea",
            //     "GW"=> "Guinea-Bissau",
            //     "GY"=> "Guyana",
            //     "HT"=> "Haiti",
            //     "HM"=> "Heard Island and McDonald Islands",
            //     "VA"=> "Holy See (the)",
            //     "HN"=> "Honduras",
            //     "HK"=> "Hong Kong",
            //     "HU"=> "Hungary",
            //     "IS"=> "Iceland",
            //     "IN"=> "India",
            //     "ID"=> "Indonesia",
            //     "IR"=> "Iran (Islamic Republic of)",
            //     "IQ"=> "Iraq",
            //     "IE"=> "Ireland",
            //     "IM"=> "Isle of Man",
            //     "IL"=> "Israel",
            //     "IT"=> "Italy",
            //     "JM"=> "Jamaica",
            //     "JP"=> "Japan",
            //     "JE"=> "Jersey",
            //     "JO"=> "Jordan",
            //     "KZ"=> "Kazakhstan",
            //     "KE"=> "Kenya",
            //     "KI"=> "Kiribati",
            //     "KP"=> "Korea (the Democratic People's Republic of)",
            //     "KR"=> "Korea (the Republic of)",
            //     "KW"=> "Kuwait",
            //     "KG"=> "Kyrgyzstan",
            //     "LA"=> "Lao People's Democratic Republic (the)",
            //     "LV"=> "Latvia",
            //     "LB"=> "Lebanon",
            //     "LS"=> "Lesotho",
            //     "LR"=> "Liberia",
            //     "LY"=> "Libya",
            //     "LI"=> "Liechtenstein",
            //     "LT"=> "Lithuania",
            //     "LU"=> "Luxembourg",
            //     "MO"=> "Macao",
            //     "MG"=> "Madagascar",
            //     "MW"=> "Malawi",
            //     "MY"=> "Malaysia",
            //     "MV"=> "Maldives",
            //     "ML"=> "Mali",
            //     "MT"=> "Malta",
            //     "MH"=> "Marshall Islands (the)",
            //     "MQ"=> "Martinique",
            //     "MR"=> "Mauritania",
            //     "MU"=> "Mauritius",
            //     "YT"=> "Mayotte",
            //     "MX"=> "Mexico",
            //     "FM"=> "Micronesia (Federated States of)",
            //     "MD"=> "Moldova (the Republic of)",
            //     "MC"=> "Monaco",
            //     "MN"=> "Mongolia",
            //     "ME"=> "Montenegro",
            //     "MS"=> "Montserrat",
            //     "MA"=> "Morocco",
            //     "MZ"=> "Mozambique",
            //     "MM"=> "Myanmar",
            //     "NA"=> "Namibia",
            //     "NR"=> "Nauru",
            //     "NP"=> "Nepal",
            //     "NL"=> "Netherlands (the)",
            //     "NC"=> "New Caledonia",
            //     "NZ"=> "New Zealand",
            //     "NI"=> "Nicaragua",
            //     "NE"=> "Niger (the)",
            //     "NG"=> "Nigeria",
            //     "NU"=> "Niue",
            //     "NF"=> "Norfolk Island",
            //     "MP"=> "Northern Mariana Islands (the)",
            //     "NO"=> "Norway",
            //     "OM"=> "Oman",
            //     "PK"=> "Pakistan",
            //     "PW"=> "Palau",
            //     "PS"=> "Palestine, State of",
            //     "PA"=> "Panama",
            //     "PG"=> "Papua New Guinea",
            //     "PY"=> "Paraguay",
            //     "PE"=> "Peru",
            //     "PH"=> "Philippines (the)",
            //     "PN"=> "Pitcairn",
            //     "PL"=> "Poland",
            //     "PT"=> "Portugal",
            //     "PR"=> "Puerto Rico",
            //     "QA"=> "Qatar",
            //     "MK"=> "Republic of North Macedonia",
            //     "RO"=> "Romania",
            //     "RU"=> "Russian Federation (the)",
            //     "RW"=> "Rwanda",
            //     "RE"=> "Réunion",
            //     "BL"=> "Saint Barthélemy",
            //     "SH"=> "Saint Helena, Ascension and Tristan da Cunha",
            //     "KN"=> "Saint Kitts and Nevis",
            //     "LC"=> "Saint Lucia",
            //     "MF"=> "Saint Martin (French part)",
            //     "PM"=> "Saint Pierre and Miquelon",
            //     "VC"=> "Saint Vincent and the Grenadines",
            //     "WS"=> "Samoa",
            //     "SM"=> "San Marino",
            //     "ST"=> "Sao Tome and Principe",
            //     "SA"=> "Saudi Arabia",
            //     "SN"=> "Senegal",
            //     "RS"=> "Serbia",
            //     "SC"=> "Seychelles",
            //     "SL"=> "Sierra Leone",
            //     "SG"=> "Singapore",
            //     "SX"=> "Sint Maarten (Dutch part)",
            //     "SK"=> "Slovakia",
            //     "SI"=> "Slovenia",
            //     "SB"=> "Solomon Islands",
            //     "SO"=> "Somalia",
            //     "ZA"=> "South Africa",
            //     "GS"=> "South Georgia and the South Sandwich Islands",
            //     "SS"=> "South Sudan",
            //     "ES"=> "Spain",
            //     "LK"=> "Sri Lanka",
            //     "SD"=> "Sudan (the)",
            //     "SR"=> "Suriname",
            //     "SJ"=> "Svalbard and Jan Mayen",
            //     "SE"=> "Sweden",
            //     "CH"=> "Switzerland",
            //     "SY"=> "Syrian Arab Republic",
            //     "TW"=> "Taiwan",
            //     "TJ"=> "Tajikistan",
            //     "TZ"=> "Tanzania, United Republic of",
            //     "TH"=> "Thailand",
            //     "TL"=> "Timor-Leste",
            //     "TG"=> "Togo",
            //     "TK"=> "Tokelau",
            //     "TO"=> "Tonga",
            //     "TT"=> "Trinidad and Tobago",
            //     "TN"=> "Tunisia",
            //     "TR"=> "Turkey",
            //     "TM"=> "Turkmenistan",
            //     "TC"=> "Turks and Caicos Islands (the)",
            //     "TV"=> "Tuvalu",
            //     "UG"=> "Uganda",
            //     "UA"=> "Ukraine",
            //     "AE"=> "United Arab Emirates (the)",
            //     "GB"=> "United Kingdom of Great Britain and Northern Ireland (the)",
            //     "UM"=> "United States Minor Outlying Islands (the)",
            //     "US"=> "United States of America (the)",
            //     "UY"=> "Uruguay",
            //     "UZ"=> "Uzbekistan",
            //     "VU"=> "Vanuatu",
            //     "VE"=> "Venezuela (Bolivarian Republic of)",
            //     "VN"=> "Viet Nam",
            //     "VG"=> "Virgin Islands (British)",
            //     "VI"=> "Virgin Islands (U.S.)",
            //     "WF"=> "Wallis and Futuna",
            //     "EH"=> "Western Sahara",
            //     "YE"=> "Yemen",
            //     "ZM"=> "Zambia",
            //     "ZW"=> "Zimbabwe",
            //     "AX"=> "Åland Islands"
            // ];
            // foreach($country_list as $key => $value){
            //     DB::table('all_country')->insert([
            //         'country' => $value,
            //         'country_code' => $key
            //     ]);
            // }





            // DB::table('cms_email_templates')->insert([

            //     'name' => "DRM Product Stock Shortage Mail",

            //     'slug' => "drm_product_stock_shortage_email_template",

            //     'subject' => "Warnung vor Lagerknappheit",

            //     'content' => "<p>

            //     Lieber Benutzer [user_name],

            //     </p>

            //     Der Lagerbestand einiger Produkte in Ihrem Geschäft hat das Mindestlagerlimit erreicht..<br>

            //     Bitte ergreifen Sie die notwendigen Schritte.<br><br>


            //     Hier ist ein Überblick über diese Probleme:<br>


            //     [product_table]<br><br>



            //     Für Rückfragen stehen wir Ihnen jederzeit gerne zur Verfügung.<br><br>


            //     Mit freundlichen Grüßen<br><br>


            //     Ihr Team von<br>

            //     [logo]"

            // ]);
            return response()->json(['success' => true, 'data' => "mod done"]);
        }
        catch(Exception $e){
            dd($e);
        }
    }

    public function stockDBMod() {
        ini_set('max_execution_time', -1);
        try{
            // DB::table('drm_products')->orderBy('id')->whereNotNull('net_weight')->chunk(1000, function($rows) {
            //     foreach($rows as $row){
            //         DB::table('drm_products')->where('id', $row->id)->update([
            //             'item_weight' => $row->net_weight
            //         ]);
            //     }
            // });
            // DB::table('channel_products')->orderBy('id')->whereNotNull('net_weight')->chunk(1000, function($rows) {
            //     foreach($rows as $row){
            //         DB::table('channel_products')->where('id', $row->id)->update([
            //             'item_weight' => $row->net_weight
            //         ]);
            //     }
            // });
            // \App\Models\Export\UniversalExport::create([
            //     'user_id' => 212,
            //     'default' => true,
            //     'dt_feed' => true
            // ]);
            // DB::table('google_merchant_reports')->truncate();
            $user_ids = [2134, 1813];

            $new_widgets1 = [
                "x" => "0",
                "y" => "58",
                "w" => "6",
                "h" => "4",
                "id" => "cus_38",
                "hidden" => false,
            ];

            $new_widgets2 = [
                "x" => "6",
                "y" => "58",
                "w" => "6",
                "h" => "4",
                "id" => "cus_39",
                "hidden" => false,
            ];

            foreach($user_ids as $user_id){
                $user_grid = DB::table('user_dashboards')->where('user_id', $user_id)->value('grids');

                if($user_grid){
                    $user_grid = json_decode($user_grid, true);

                    array_push($user_grid, $new_widgets1);
                    array_push($user_grid, $new_widgets2);

                    $user_grid = json_encode($user_grid, true);
                    DB::table('user_dashboards')->where('user_id', $user_id)->update(['grids' => $user_grid]);
                }
            }
            dd("OK");
        }catch(Exception $e){
            dd($e);
        }
    }

    public function saveTag(Request $request): JsonResponse
    {
        try {
            $product_id = $request->get('id');
            $tags = explode(',', $request['tags']);
            $data['tags'] = generateTags($tags);
            $languageId = app('App\Services\UserService')->getProductCountry($currentUserId);
            $lang = app('App\Services\UserService')->getProductLanguage($languageId);
            $this->productService->update($product_id, $data, $lang, 'manual');

            return response()->json(['success' => true, $data]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 422);
        }
    }

    public function bulkTaxRateUpdate(Request $request) {
        try{
            $user_id = CRUDBooster::myParentId();
            $product_ids = $request->product_ids;
            $checked_all = $request->checked_all;
            $tax_type = $request->tax_type;
            $params = $request->params;
            if($checked_all == 1){
                $product_ids = $this->productService->getSelectedIds($user_id, $params);
            }
            ProcessDRMBulkTaxTypeUpdate::dispatch($user_id, $product_ids, $tax_type);
            return response()->json(['success' => true]);
        }
        catch(Exception $e){
            return response()->json(['success' => false]);
        }
    }

    public function bulkBrandUpdate(Request $request) {
        try{
            $user_id = CRUDBooster::myParentId();
            $product_ids = $request->product_ids;
            $checked_all = $request->checked_all;
            $brand = $request->brand;
            $params = $request->params;
            if($checked_all == 1){
                $product_ids = $this->productService->getSelectedIds($user_id, $params);
            }
            ProcessDRMBulkBrandUpdate::dispatch($user_id, $product_ids, $brand);
            return response()->json(['success' => true]);
        }
        catch(Exception $e){
            return response()->json(['success' => false]);
        }
    }

    public function userChannelAddition($user_id): array
    {
        $channel_add_info = [];
        $user_import_plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
        $user_channel_added = userTotalChannels($user_id);
        $user_channel_access = DB::table('purchase_import_plans')->where('cms_user_id', $user_id)->value('total_channel');
        $is_new_dt_user = is_dt_user() && checkTariffEligibility($user_id);

        if(checkTariffEligibility($user_id)){

            if($is_new_dt_user){
                $channel_add_info['total_channel'] = 1;
            }else{
                if(in_array($user_import_plan['plan'], ['Free', 'Trial'])){
                    $channel_add_info['total_channel'] = 9999999999; // Unlimited Channel can Add
                }else if(in_array($user_import_plan['plan'], ['500 Free Products'])){
                    $channel_add_info['total_channel'] = $user_channel_added;
                }else{

                    // $except_user_id = [3288, 3299, 3217, 3307, 3253, 3241, 3337, 3379];

                    $channel_add_info['total_channel'] = $user_channel_access;

                    // if(in_array($user_id, $except_user_id)){
                    //     $channel_add_info['total_channel'] = 5;
                    // }
                }
            }

        }else{
            $channel_add_info['total_channel'] = 9999999999; // Unlimited Channel can Add
        }

        $channel_add_info['channel_added'] = $user_channel_added;
        $channel_add_info['channel_add_left'] = $channel_add_info['total_channel'] - $user_channel_added;

        return $channel_add_info;
    }

    function bulkProductDeleteCheck(){
        $userId = CRUDBooster::myId();
        $req = request();
        return DB::table('drm_products')
                    ->whereIn('id',json_decode($req->product_ids))
                    ->where('user_id',$userId)
                    ->where('ek_price','')
                    ->whereJsonLength('stock_of_status','>',1)
                    ->count();

    }

    public function minMaxExport(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $product_ids = explode(',', $request->product_ids);

        $size = 500;
        $products = [];

        $productIds = array_chunk($product_ids, $size);

        foreach ($productIds as $key => $ids){
            $products[] = DB::table('drm_products')
            ->select('ean', 'ek_price', 'shipping_cost', 'min_price', 'max_price')
            ->whereIn('id', $ids)
            ->get()
            ->toArray();
        }
        $product_file = [];

        foreach ($products as $key => $product) {
            $product_file  = array_merge($product_file, $product);
        }

        // Modified headings
        $headings = [
            'EAN',
            'EK Preis',
            'Freight',
            'Min Preis',
            'Max Preis'
        ];

        return Excel::download(new MarketplaceProductsExport($product_file, $headings, true), 'min_max_price_list.csv');
    }
}
