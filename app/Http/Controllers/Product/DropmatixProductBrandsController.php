<?php

namespace App\Http\Controllers\Product;

use App\DrmProduct;
use App\Enums\Channel;
use App\Http\Controllers\Controller;
use App\Jobs\ChannelManager\CopyWEEENumber;
use App\Jobs\ChannelManager\TransferProduct;
use App\Models\ChannelBrand;
use App\Models\ChannelProduct;
use App\Shop;
use Illuminate\Http\Request;
use App\Services\DropmatixProductBrandsService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Request as req;
use DB;
use App\DropmatixProductBrand;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Str;
use App\Http\Controllers\AdminDrmImportsController;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;

class DropmatixProductBrandsController extends Controller
{
    private DropmatixProductBrandsService $productBrandService;

    public function __construct(DropmatixProductBrandsService $productBrandService)
    {
        $this->productBrandService = $productBrandService;
    }

    public function index(Request $request)
    {
        $currentUserId = CRUDBooster::myParentId();

        $data['page_title'] = "Drm Product Brands";

        $data['productBrands'] = $this->productBrandService->all(array_merge($request->all(),[
            'user_id' => !CRUDBooster::isSuperadmin() ? $currentUserId : '',
            "search_by_field_column" => "brand_name"
        ]));

       $data['hasOttoChannel'] = Shop::where([
           'user_id' => $currentUserId,
           'channel' => Channel::OTTO
       ])->exists();

        return view('dropmatix_product_brands.index', $data);
    }

    public function getAdd()
    {
        $countries = DB::table('tax_rates')->orderBy('country')->get();
        
        return view('dropmatix_product_brands.add_brand',compact('countries'));
    }

    public function saveBrandLogo(Request $request)
    {
        if (!CRUDBooster::isSuperadmin()) {
            // 'save_temp_image'
            if (CRUDBooster::isSubUser() && (!sub_account_can('add') && !sub_account_can('all_modules', 122))) {
                return 0;
            }
        }

        try {
            if ($request->hasFile('file')) {
                $imagesAll = $request->file('file');
                $image_type = strtolower($imagesAll->getClientOriginalExtension());
                $imgTypes = ['jpg', 'png', 'jpeg', 'gif'];

                if (in_array($image_type, $imgTypes)) {
                    $images_url = uploadImage($imagesAll, 'dropmatix_brand_logos/' . CRUDBooster::myParentId());

                    return response()->json([
                        'name' => $images_url,
                        'success' => true,
                        'message' => 'File upload success!',
                    ]);
                }
            }
        } catch (Exception $e) {
            return response()->json(['success' => false], 422);
        }

        return response()->json(['success' => false], 422);
    }

    public function deleteBrandLogo(Request $request)
    {
        $path = str_replace("https://drm-file.fra1.digitaloceanspaces.com/", "/", $request->file_name);

        if(isLocal()){
            $path = str_replace("https://drm-team.fra1.digitaloceanspaces.com/", "/", $request->file_name);
        }

        if ($request->file_name && Storage::disk('spaces')->exists('/' . $path)) {
            Storage::disk('spaces')->delete('/' . $path);

            return response()->json(['success' => true, 'name' => $request->file_name, 'message' => 'File deleted success!']);
        }

        return response()->json(['success' => false, 'name' => $request->file_name, 'message' => 'File deleted failed!']);
    }

    public function postAddBrand(Request $request)
    {
        // dd($request->all());

        $validator = Validator::make($request->all(), [
            'brand_name' => 'required',
            'image' => 'nullable',
            'contact_person' => 'nullable',
            'email' => 'nullable',
            'street_and_house_number' => 'nullable',
            'postcode' => 'nullable',
            'city' => 'nullable',
            'country' => 'nullable',
        ]);

        if ($validator->fails()) {
            $message = $validator->errors()->all();
            return redirect()->back()->with(['message' => implode(', ', $message), 'message_type' => 'danger']);
        }

        $brand_logo = null;

        if(isset($request->image) && !empty($request->image)){
            $brand_logo = $request->image;
        }

        $brand_name_exist = DropmatixProductBrand::where(['user_id' => CRUDBooster::myParentId()])
                        ->where('brand_name', 'LIKE', trim($request->brand_name))
                        ->exists();

        if($brand_name_exist){
            return redirect()->back()->with(['message' => "Brand Name Already Exists!", 'message_type' => 'error']);
        }

        DropmatixProductBrand::create([
            'user_id' => CRUDBooster::myParentId(),
            'brand_name' => trim($request->brand_name),
            'brand_logo' => $brand_logo,
            'contact_person' => $request->contact_person,
            'email' => $request->email,
            'street_and_house_number' => $request->street_and_house_number,
            'postcode' => $request->postcode,
            'city' => $request->city,
            'country' => $request->country,
        ]);

        return redirect('/admin/brands/')->with(['message' => 'Brand Added Successfully!', 'message_type' => 'success']);

    }

    public function getEditBrand($id)
    {
        $data['product_brand'] = DropmatixProductBrand::find($id);
        $data['countries'] = DB::table('tax_rates')->orderBy('country')->get();

        return view('dropmatix_product_brands.edit_brand', $data);

    }

    public function removeLogo(Request $request){
        $path = str_replace("https://drm-file.fra1.digitaloceanspaces.com/", "/", $request->image_url);

        if(isLocal()){
            $path = str_replace("https://drm-team.fra1.digitaloceanspaces.com/", "/", $request->image_url);
        }

        // dd($request->image_url, $path, $request->all());

        if ($request->image_url && Storage::disk('spaces')->exists('/' . $path)) {
            Storage::disk('spaces')->delete('/' . $path);

            $product_brand = DropmatixProductBrand::find($request->brand_id);
            $updated_url = array_filter(array_diff($product_brand->brand_logo, [$request->image_url]));
            $product_brand->brand_logo = $updated_url;
            $product_brand->save();
        }


        return response()->json(['success' => true, 'name' => $request->file_name, 'message' => 'File deleted success!']);
    }

    public function updateLogo(Request $request){

        if (!CRUDBooster::isSuperadmin()) {
            if (CRUDBooster::isSubUser() && (!sub_account_can('add') && !sub_account_can('all_modules', 122))) {
                return 0;
            }
        }

        try {
            if ($request->hasFile('file')) {
                $imagesAll = $request->file('file');
                $image_type = strtolower($imagesAll->getClientOriginalExtension());
                $imgTypes = ['jpg', 'png', 'jpeg', 'gif'];

                if (in_array($image_type, $imgTypes)) {
                    $images_url = uploadImage($imagesAll, 'dropmatix_brand_logos/' . CRUDBooster::myParentId());

                    DropmatixProductBrand::where('id', $request->brand_id)->update(['brand_logo' => json_encode([$images_url])]);

                    return response()->json([
                        'name' => $images_url,
                        'success' => true,
                        'message' => 'File upload success!',
                    ]);
                }
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false], 422);
        }

        return response()->json(['success' => false], 422);

    }

    public function updateBrand(Request $request){

        $validator = Validator::make($request->all(), [
            'brand_name' => 'required',
            'image' => 'nullable',
            'contact_person' => 'nullable',
            'email' => 'nullable',
            'street_and_house_number' => 'nullable',
            'postcode' => 'nullable',
            'city' => 'nullable',
            'country' => 'nullable',
        ]);

        if ($validator->fails()) {
            $message = $validator->errors()->all();
            return redirect()->back()->with(['message' => implode(', ', $message), 'message_type' => 'danger']);
        }

        $image_url = [];
        if(isset($request->image)){
            $image_url = json_encode($request->image);
        }

        try{
            $brand_name_exist = DropmatixProductBrand::where(['user_id' => $request->user_id])
                                ->where('brand_name', 'LIKE', trim($request->brand_name))
                                ->where('id', '!=', $request->brand_id)
                                ->exists();

            if($brand_name_exist){

                return redirect()->back()->with(['message' => "Brand Name Already Exists!", 'message_type' => 'error']);

            }else{

                DropmatixProductBrand::where(['id' => $request->brand_id, 'user_id' => $request->user_id])
                    ->update([
                        'brand_name' => $request->brand_name,
                        'brand_logo' => $image_url,
                        'contact_person' => $request->contact_person,
                        'email' => $request->email,
                        'street_and_house_number' => $request->street_and_house_number,
                        'postcode' => $request->postcode,
                        'city' => $request->city,
                        'country' => $request->country,
                    ]);

                return redirect('/admin/brands/')->with(['message' => 'Brand Updated Successfully!', 'message_type' => 'success']);
            }


        }catch (\Exception $e) {
            return redirect()->back()->with(['message' => $e->getMessage(), 'message_type' => 'error']);
        }

    }

    public function deleteBrands()
    {
        $user_id = CRUDBooster::myParentId();
        $brand_ids = $_REQUEST['brand_ids'];
        $params = $_REQUEST['params'];
        $selected_all = (int) $_REQUEST['select_all'];

        if($selected_all){
            $brand_ids = $this->productBrandService->getSelectedIds($user_id, $params);
        }

        foreach(array_chunk($brand_ids, 500) as $chunk){
            $result = $this->productBrandService->destroy($chunk, $user_id);
        }

        return $result;
    }

    public function deleteSingleBrand()
    {
        $id = [(int)$_REQUEST['brand_id']];

        $user_id = CRUDBooster::myParentId();

        $result = $this->productBrandService->destroy($id, $user_id);

        return $result;
    }

    public function getImportBrands()
    {
        // dd("oeieiusfdhs 9uu tttt", \Route::getCurrentRoute()->getActionName());
        // $this->cbLoader();
        $data['page_menu'] = \Route::getCurrentRoute()->getActionName();
        $data['page_title'] = __('customer.Import_tittle');

        if (req::get('file') && !req::get('import')) {
            // dd("fff");
            $file = base64_decode(req::get('file'));
            $file = storage_path('app/' . $file);
            // $rows = Excel::load($file, function ($reader) {
            // })->get();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            // dd($type);
            $rows = app('App\Http\Controllers\AdminDrmImportsController')->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;
            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }
            $table_columns = ['brand_name', 'brand_logo'];
            $labels = array_map(function ($str) {
                return ucwords(str_replace("_", " ", $str));
            }, $table_columns);
            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        return view('dropmatix_product_brands.import_brands', $data);
    }

    public function uploadBrandCsv()
    {
        if (req::hasFile('userfile')) {
            $file = req::file('userfile');
            $ext = $file->getClientOriginalExtension();

            $validator = Validator::make([
                'extension' => $ext,
            ], [
                'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV,txt,TXT,XML,xml',
            ]);

            if ($validator->fails()) {
                $message = $validator->errors()->all();

                return redirect()->back()->with(['message' => implode('<br/>', $message), 'message_type' => 'warning']);
            }

            //Create Directory Monthly
            $filePath = 'brand_uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
            Storage::makeDirectory($filePath);

            //Move file to storage
            $filename = md5(Str::random(5)).'.'.$ext;
            $url_filename = '';
            if (Storage::putFileAs($filePath, $file, $filename)) {
                $url_filename = $filePath.'/'.$filename;
            }
            $url = route('dropmatix.product.brand.importBrands').'?file='.base64_encode($url_filename);

            return redirect($url);
        } else {
            return redirect()->back();
        }
    }

    public function brandImportDone(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Import Products"]);
        Session::put('select_column', $request->get('select_column'));

        return view('dropmatix_product_brands.import_brands', $data);
    }

    public function doImportChunk(Request $request)
    {
        try{
            $file_md5 = md5($request->get('file'));

            if ($request->get('file') && $request->get('resume') == 1) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }

                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }

            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);

            $table_columns = [
                'brand_name',
                'brand_logo'
            ];

            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $count = 0;

            $existing_brands = DropmatixProductBrand::where('user_id',  $user_id)->pluck('brand_name')->toArray();
            $new_brand = [];

            try {
                foreach ($rows as $row) {

                    $count++;
                    Cache::put('success_' . $file_md5, $count);

                    // $row = (object) $row;

                    $tmp_data = [];

                    foreach ($table_columns as $key => $value) {

                        // if ($row->$select_column[$key] == '') continue;

                        if($value == 'brand_name'){
                            $tmp_data[$value] = trim( $row[$select_column[$key]] );
                        }else if($value == 'brand_logo'){
                            $tmp_data[$value] = !empty($select_column[$key]) ? json_encode([$row[$select_column[$key]]]) : null;
                        }

                    }

                    $tmp_data['user_id'] = $user_id;
                    $tmp_data['created_at'] = Carbon::now()->toDateTimeString();
                    $tmp_data['updated_at'] = Carbon::now()->toDateTimeString();

                    $new_brand[] = $tmp_data;
                }

                $brands_unique_name = array_unique(array_map('strtolower', array_column($new_brand, 'brand_name')));

                $new_brand = array_intersect_key($new_brand, $brands_unique_name);

                $new_brands = array_filter($new_brand, function ($value) use ($existing_brands) {
                    return !in_array(strtolower($value['brand_name']), array_map('strtolower', $existing_brands));

                });

                $new_brands = array_values($new_brands);

                // dd($new_brands);

                if($new_brands){
                    collect($new_brands)
                    ->chunk(500)
                    ->each(function($brand_chunk){
                        $brand_chunk = $brand_chunk->toArray();
                        DropmatixProductBrand::insert($brand_chunk);
                    });
                }

                return response()->json(['status' => true, 'message' => "Brand Imported !!!"]);
            }catch(\Exception $e){
                $e = (string) $e;
                Cache::put('error_' . $file_md5, $e, 500);
                return response()->json(['status' => false, 'message' => $e]);
            }
        }catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => "Sorry, something went wrong"]);
        }

    }

    public function getUpdateBrands()
    {
        // dd("oeieiusfdhs 9uu tttt", \Route::getCurrentRoute()->getActionName());
        $data['page_title'] = __('Update Brands');

        if (req::get('file') && !req::get('import')) {
            // dd("fff");
            $file = base64_decode(req::get('file'));
            $file = storage_path('app/' . $file);
            // $rows = Excel::load($file, function ($reader) {
            // })->get();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            // dd($type);
            $rows = app('App\Http\Controllers\AdminDrmImportsController')->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;
            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }
            $table_columns = ['brand_name', 'brand_logo'];
            $labels = array_map(function ($str) {
                return ucwords(str_replace("_", " ", $str));
            }, $table_columns);
            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        return view('dropmatix_product_brands.update_brands', $data);
    }

    public function uploadUpdateBrandCsv()
    {
        if (req::hasFile('userfile')) {
            $file = req::file('userfile');
            $ext = $file->getClientOriginalExtension();

            $validator = Validator::make([
                'extension' => $ext,
            ], [
                'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV,txt,TXT,XML,xml',
            ]);

            if ($validator->fails()) {
                $message = $validator->errors()->all();

                return redirect()->back()->with(['message' => implode('<br/>', $message), 'message_type' => 'warning']);
            }

            //Create Directory Monthly
            $filePath = 'brand_uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
            Storage::makeDirectory($filePath);

            //Move file to storage
            $filename = md5(Str::random(5)).'.'.$ext;
            $url_filename = '';
            if (Storage::putFileAs($filePath, $file, $filename)) {
                $url_filename = $filePath.'/'.$filename;
            }
            $url = route('dropmatix.product.brand.updateBrands').'?file='.base64_encode($url_filename);

            return redirect($url);
        } else {
            return redirect()->back();
        }
    }

    public function brandUpdateDone(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Update Brands"]);
        Session::put('select_column', $request->get('select_column'));

        return view('dropmatix_product_brands.update_brands', $data);
    }

    public function doUpdateChunk(Request $request)
    {
        try{
            $file_md5 = md5($request->get('file'));

            if ($request->get('file') && $request->get('resume') == 1) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }

                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }

            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);

            $table_columns = [
                'brand_name',
                'brand_logo'
            ];

            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $count = 0;

            $existing_brands = DropmatixProductBrand::where('user_id',  $user_id)->select('id', 'brand_name')->get();

            $new_brands = [];
            $update_data = [];

            try {
                foreach ($rows as $row) {

                    $count++;
                    Cache::put('success_' . $file_md5, $count);

                    // $row = (object) $row;

                    $tmp_data = [];
                    $tmp_update_data = [];

                    foreach ($table_columns as $key => $value) {

                        // if ($row->$select_column[$key] == '') continue;

                        if($value == 'brand_name'){
                            $tmp_data[$value] = trim( $row[$select_column[$key]] );
                        }else if($value == 'brand_logo'){
                            $tmp_data[$value] = !empty($select_column[$key]) ? json_encode([$row[$select_column[$key]]]) : null;
                        }

                    }

                    $existing_brands->filter(function($item) use ($tmp_data, &$tmp_update_data){
                        $cmp_res = strcasecmp($item->brand_name, $tmp_data['brand_name']);
                        if( $cmp_res == 0 ){
                            $tmp_update_data['id'] = $item->id;
                        }
                    });

                    if( !empty($tmp_update_data) ){

                        $update_data[$tmp_update_data['id']] = $tmp_data['brand_logo'];

                    }else{

                        $tmp_data['user_id'] = $user_id;
                        $tmp_data['created_at'] = Carbon::now()->toDateTimeString();
                        $tmp_data['updated_at'] = Carbon::now()->toDateTimeString();

                        $new_brands[] = $tmp_data;

                    }

                }

                $brands_unique_name = array_unique(array_column($new_brands, 'brand_name'));
                $new_brands = array_intersect_key($new_brands, $brands_unique_name);

                // $new_brands = array_filter($new_brand, function ($value) use ($existing_brands_name) {
                //     return !in_array($value['brand_name'], $existing_brands_name);
                // });

                // $new_brands = array_values($new_brands);

                // dd($new_brand);

                if($update_data){

                    collect($update_data)
                    ->chunk(200)
                    ->each(function($brand_update_chunk) use($user_id){
                        $brand_update_chunk = $brand_update_chunk->toArray();

                        $cases = [];
                        $ids = [];
                        $params = [];

                        foreach ($brand_update_chunk as $key => $value) {
                            $cases[] = "WHEN {$key} then ?";
                            $params[] = $value;
                            $ids[] = $key;
                        }

                        $ids = implode(',', $ids);
                        $cases = implode(' ', $cases);

                        // if (!empty($ids)) {
                            \DB::update("UPDATE dropmatix_product_brands SET `brand_logo` = CASE `id` {$cases} END WHERE `id` in ({$ids}) AND `user_id` = {$user_id}", $params);
                        // }

                    });

                }

                if($new_brands){
                    collect($new_brands)
                    ->chunk(500)
                    ->each(function($brand_chunk){
                        $brand_chunk = $brand_chunk->toArray();
                        DropmatixProductBrand::insert($brand_chunk);
                    });
                }

                return response()->json(['status' => true, 'message' => "Brand Imported !!!"]);
            }catch(\Exception $e){
                $e = (string) $e;
                Cache::put('error_' . $file_md5, $e, 500);
                return response()->json(['status' => false, 'message' => $e]);
            }
        }catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => "Sorry, something went wrong"]);
        }

    }

    //check product's alternative stock inside the brand
    public function checkBrandsAlternativeStock(){

        $req = request();
        $userId = CRUDBooster::myId();
        if($req->type == 'single'){
            $products = DB::table('drm_products')
                    ->where('brand',$req->brandId)
                    ->where('user_id',$userId)
                    ->whereJsonLength('stock_of_status','>',1)
                    ->count();
        }else{
            $products = DB::table('drm_products')
                    ->whereIn('brand',$req->brandIds)
                    ->where('user_id',$userId)
                    ->whereJsonLength('stock_of_status','>',1)
                    ->count();

        }

        return $products;
    }

    public function getChannelBrands(Request $request)
    {
        $input = $request->search;
        $query = ChannelBrand::where([
            'channel' => Channel::OTTO
        ]);

        if (!is_null($input) && $input != 'undefined') {
            $query->where('name', 'LIKE', '%' . $input . '%');
        }

        $brands = $query->get();

        $transformedResults = $brands->map(function ($item) {
            return [
                'id'   => $item->brand_id,
                'text' => $item->name,
            ];
        });

        return ['results' => $transformedResults->toArray()];
    }

    public function confirmBrandMapping(Request $request)
    {
        $userId = CRUDBooster::myParentId();
        $brandId = (int)$request->brandId;
        $ottoBrandId = (string)$request->ottoBrandId;

        $ottoBrand = ChannelBrand::where([
            'channel' => Channel::OTTO,
            'brand_id' => $ottoBrandId
        ])->first();

        DropmatixProductBrand::where([
            'user_id' => $userId,
            'id' => $brandId
        ])->update(['otto_brand_id' => $ottoBrand->id,'otto_brand_name' => $ottoBrand->name]);


        $products = DrmProduct::select('drm_products.id','channel_products.shop_id')
            ->join('channel_products','drm_products.id','=','channel_products.drm_product_id')
            ->where([
                'drm_products.user_id' => $userId,
                'channel' => Channel::OTTO,
                'drm_products.brand' => $brandId
            ])->get()->groupBy('shop_id');


        foreach ($products as $shopId => $product) {
            TransferProduct::dispatch($product->pluck('id')->toArray(), Channel::OTTO, ['brand'], 'de', 'update', true, 0, ['shop_id' => $shopId]);
        }

        return response()->json(['message' => 'Brand mapped successfully']);
    }

    public function updateWeeeNumber(Request $request)
    {
        $id = $request->pk;
        $value = $request->value;

        $productBrand = DropmatixProductBrand::where([
            'user_id' => CRUDBooster::myParentId(),
            'id' => $id
        ])->first();

        if($productBrand){

            $productBrand->weee_number = $value;
            $productBrand->save();

            $channelProducts = ChannelProduct::where([
                'user_id' => CRUDBooster::myParentId(),
                'channel' => Channel::OTTO,
                'brand' => $productBrand->otto_brand_id
            ])->pluck('id')->toArray();

            CopyWEEENumber::dispatch($channelProducts);
        }


        return response()->json(['message' => 'WEEE number updated successfully']);
    }
}
