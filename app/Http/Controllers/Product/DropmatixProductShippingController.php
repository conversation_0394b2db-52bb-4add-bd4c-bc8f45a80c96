<?php

namespace App\Http\Controllers\product;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Country;
use App\ShippingGroup;
use App\Services\DropmatixProductShippingService;

class DropmatixProductShippingController extends Controller
{
    private DropmatixProductShippingService $shippingSettingService;

    public function __construct(DropmatixProductShippingService $shippingSettingService)
    {
        $this->shippingSettingService = $shippingSettingService;
    }

    public function index(Request $request)
    {
        $currentUserId = CRUDBooster::myParentId();

        $data['page_title'] = "Shipping Groups";

        $data['shippingGroup'] = $this->shippingSettingService->all(array_merge($request->all(),[
            'user_id' => !CRUDBooster::isSuperadmin() ? $currentUserId : '',
            "search_by_field_column" => "shipping_group_name"
        ]));

        return view('admin.shipping_setting.index', $data);
    }

    public function getAdd() 
    {
        $data['page_title'] = __('Shipping Settings Add');
        
        $data['countries'] = Country::where('is_active', 1)->pluck('name', 'id')->toArray();

        return view('admin.shipping_setting.add_shipping_setting', $data);
    }

    public function postAddShippingSetting(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'group_name' => 'required',
            'country' => 'required',
            'shipping_cost' => 'required',
            'shipping_discount' => 'nullable',
            'max_shipping_cost' => 'nullable',
            'free_shipping_amount' => 'nullable',
        ]);

        if ($validator->fails()) {
            $message = $validator->errors()->all(); 
            return redirect()->back()->with(['message' => implode(', ', $message), 'message_type' => 'danger']);
        }

        $shipping_group_exist = ShippingGroup::where(['user_id' => CRUDBooster::myParentId()])
                        ->where('shipping_group_name', 'LIKE', trim($request->group_name))
                        ->exists();

        if($shipping_group_exist){
            return redirect()->back()->with(['message' => __("Shipping Group Already Exists!"), 'message_type' => 'error']);
        }

        ShippingGroup::create([
            'user_id' => CRUDBooster::myParentId(), 
            'shipping_group_name' => trim($request->group_name),
            'shipping_country' => $request->country,
            'shipping_cost' => $request->shipping_cost,
            'shipping_discount' => $request->shipping_discount,
            'shipping_max_cost' => $request->max_shipping_cost,
            'shipping_free_amount' => $request->free_shipping_amount
        ]);

        return redirect('/admin/shipping-settings/')->with(['message' => __('Shipping Group Added Successfully!'), 'message_type' => 'success']);
    }

    public function getEditGroup($id){
        $data['page_title'] = __('Shipping Settings Edit');

        $data['shipping_group'] = ShippingGroup::find($id);
        $data['save_shipping_country'] = $data['shipping_group']->shipping_country ?? [];
        
        $data['countries'] = Country::where('is_active', 1)->pluck('name', 'id')->toArray();

        $data['update_submit_url'] = route('dropmatix.product.shipping.postEditGroup');

        return view('admin.shipping_setting.add_shipping_setting', $data);
    }

    public function postEditGroup(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'group_name' => 'required',
            'country' => 'required',
            'shipping_cost' => 'required',
            'shipping_discount' => 'nullable',
            'max_shipping_cost' => 'nullable',
            'free_shipping_amount' => 'nullable',
        ]);

        if ($validator->fails()) {
            $message = $validator->errors()->all(); 
            return redirect()->back()->with(['message' => implode(', ', $message), 'message_type' => 'danger']);
        }

        $user_id = CRUDBooster::myParentId();

        $shipping_group_exist = ShippingGroup::where(['user_id' => $user_id])
                        ->where('id', '!=', $request->edit_id)
                        ->where('shipping_group_name', 'LIKE', trim($request->group_name))
                        ->exists();

        if($shipping_group_exist){
            return redirect()->back()->with(['message' => __("Shipping Group Already Exists!"), 'message_type' => 'error']);
        }

        $update_data = [
            'shipping_group_name' => trim($request->group_name),
            'shipping_country' => $request->country,
            'shipping_cost' => $request->shipping_cost,
            'shipping_discount' => $request->shipping_discount,
            'shipping_max_cost' => $request->max_shipping_cost,
            'shipping_free_amount' => $request->free_shipping_amount
        ];

        ShippingGroup::where('id', $request->edit_id)
        ->where('user_id', $user_id)
        ->update($update_data);

        return redirect('/admin/shipping-settings/')->with(['message' => __('Shipping Group Updated Successfully!'), 'message_type' => 'success']);

    }

    public function deleteSingleShippingGroup()
    {
        $id = [(int)$_REQUEST['shipping_group_id']];

        $result = $this->shippingSettingService->destroy($id);
        
        return $result;
    }

}