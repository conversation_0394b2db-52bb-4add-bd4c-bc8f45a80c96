<?php
namespace App\Http\Controllers\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Redirect;
use App\Services\ProductApi\Services\GoogleMerchant;
use App\Models\Product\GoogleMerchantReport;
use App\Models\Product\GoogleMerchantHistory;
use App\Jobs\ProcessGoogleMerchantUpdateJob;

class GoogleShoppingMerchantController
{
    public function index(Request $request) {
        $user_id = CRUDBooster::myParentId();
        $exists = \App\Models\Export\UniversalExport::where([
            'user_id' => $user_id,
            'default' => true,
            'dt_feed' => true
        ])->exists();
        $allowed_users = [2134, 1813, 2698, 2873, 2911, 2872];
        if(!in_array($user_id, $allowed_users) || !$exists){
          abort(404);
        }
        $authCode = DB::table('google_oauth')->select('google_access_token')->where('user_id', $user_id)->first();
        if(!$authCode->google_access_token){
          $loggedCode = DB::table('google_oauth')->select('google_access_token', 'google_refresh_token')->where('user_id', 2134)->first();
          DB::table('google_oauth')->insert([
            'user_id' => $user_id,
            'google_access_token' => $loggedCode->google_access_token,
            'google_refresh_token' => $loggedCode->google_refresh_token
          ]);
          // $url = $this->getAuthUrl();
          // return Redirect::to($url);
        }
        

        $cv = "(SELECT(((Coalesce(`google_price`,0) + Coalesce(`ebay_price`,0) + Coalesce(`amazon_price`,0) + Coalesce(`check24_price`, 0) + Coalesce(`kaufland_price`, 0)) / NULLIF((Coalesce(`google_price`/`google_price`, 0) + Coalesce(`ebay_price`/`ebay_price`, 0) + Coalesce(`amazon_price`/`amazon_price`, 0) + Coalesce(`check24_price`/`check24_price`, 0) + Coalesce(`kaufland_price`/`kaufland_price`, 0)), 0))) FROM analysis_products WHERE `ean` = google_merchant_reports.ean AND `user_id` = $user_id) AS `cv`";
        $channel_price = "(SELECT(`vk_price`) FROM channel_products WHERE `ean` = google_merchant_reports.ean AND `user_id` = $user_id AND `channel` = 10) AS `channel_price`";
        $google_cheapest_price = "(SELECT(`google_price`) FROM analysis_products WHERE `ean` = google_merchant_reports.ean AND `user_id` = $user_id) AS `google_cheapest_price`";
        $google_rating = "(SELECT(Coalesce(`google_rating`, 0)) FROM analysis_products WHERE `ean` = google_merchant_reports.ean AND `user_id` = $user_id) AS `google_rating`";
        $google_rating_count = "(SELECT(Coalesce(`google_rating_count`, 0)) FROM analysis_products WHERE `ean` = google_merchant_reports.ean AND `user_id` = $user_id) AS `google_rating_count`";
        $products = GoogleMerchantReport::where('user_id', $user_id)->select(DB::raw(" *, $cv, $channel_price, $google_cheapest_price, $google_rating, $google_rating_count"));

        $search_by_field = $request->get('search_by_field_column');
        $filters = $request->all();

        if(!empty($search_by_field) && !empty($filters['q'])){
          $q = $filters['q'];
          if($search_by_field == 'title' || $search_by_field == 'all'){
              $q = "%$q%";
          }
          if($search_by_field != 'all'){
              $products->where($search_by_field, 'LIKE', $q);
          }else{
              $products->where(function($p) use ($q) {
                  $p->where('ean', 'LIKE', $q);
                  $p->orWhere('title', 'LIKE', "%$q%");
                  $p->orWhere('id', 'LIKE', $q);
              });
          }
      }

      $order_by = null;

      $sort_filter = $request->get('filter_column') ?? [];
      if(!empty($sort_filter))
      {
          $order_by = key($sort_filter);
          $sorting = $sort_filter[$order_by]['sorting'];
      }

      if(!$order_by && !$sorting){
        $order_by = 'id';
        $sorting = 'desc';
      }

      if($order_by && $sorting){
        $products->orderBy($order_by, $sorting);
      }

      $limit = $request->get('limit') ?? 20;

      $data['products'] = $products->paginate($limit);

      $historyLimit = $request->get('history_limit') ?? 90;

      $merchantHistory = GoogleMerchantHistory::where('user_id', $user_id)->orderBy('id', 'desc')->first();

      $clickHistory = array_slice(array_reverse(json_decode($merchantHistory->history)[0]->data ?? []), 0, $historyLimit);
      $impressionHistory = array_slice(array_reverse(json_decode($merchantHistory->history)[1]->data ?? []), 0, $historyLimit);
      $ctrHistory = array_slice(array_reverse(json_decode($merchantHistory->history)[2]->data ?? []), 0, $historyLimit);
      $totalClick = 0;
      $totalCtr = 0;
      $totalImp = 0;

      foreach($clickHistory as $item){
        $totalClick += $item->y;
      }
      foreach($impressionHistory as $item){
        $totalImp += $item->y;
      }
      foreach($ctrHistory as $item){
        $totalCtr += $item->y;
      }

      $avgCtr = number_format((float)$totalCtr, 1, ',', '.');
      $historyData = [];
      $historyData[] = [
        'name' => "Klicks <br> In Total <br> <span style='font-size: 20px'>" . $totalClick . "</span>",
        'data' => array_slice(array_reverse(json_decode($merchantHistory->history)[0]->data ?? []), 0, $historyLimit)
      ];
      $historyData[] = [
        'name' => "Impressionen <br> In Total <br> <span style='font-size: 20px'>" . $totalImp . "</span>",
        'data' => array_slice(array_reverse(json_decode($merchantHistory->history)[1]->data ?? []), 0, $historyLimit)
      ];
      $historyData[] = [
        'name' => "CTR <br> Average <br> <span style='font-size: 20px'>" . $avgCtr . "%</span>",
        'data' => array_slice(array_reverse(json_decode($merchantHistory->history)[2]->data ?? []), 0, $historyLimit)
      ];

      $merchantHistory->history = json_encode($historyData);

      $data['merchant_history'] = $merchantHistory->history;

      $bestSellerProducts = GoogleMerchantReport::where('user_id', $user_id)->orderBy('clicks', 'desc')->limit(10)->get();

      $bestSellerClicksRecords = [];
      $bestSellerCTRRecords = [];

      $totalClick = 0;
      $totalCtr = 0;
      $totalProduct = (count($bestSellerProducts) > 0) ? count($bestSellerProducts) : 1;

      foreach($bestSellerProducts as $item){
          $bestSellerClicksRecords[] = ['x' => $item->ean, 'y' => $item->clicks ?? 0 ];
          $bestSellerCTRRecords[] = ['x' => $item->ean, 'y' => $item->ctr ?? 0 ];
          $totalClick += ($item->clicks ?? 0);
          $totalCtr += ($item->ctr ?? 0);
      }

      $avgClicks = $totalClick / $totalProduct;
      $avgCtr = round(($totalCtr / $totalProduct), 6);

      $bestSellerData = [];

      $bestSellerData[] = [
        'name' => "Klicks <br> Average <br> <span style='font-size:20px'>" . $avgClicks . "</span>",
        'data' => $bestSellerClicksRecords
      ];
      $bestSellerData[] = [
        'name' => "CTR <br> Average <br> <span style='font-size:20px'>" . $avgCtr . "</span>",
        'data' => $bestSellerCTRRecords
      ];

      $data['best_seller_data'] = json_encode($bestSellerData);

      $data['columns'] = $this->table_column();
      return view('admin.cp_analysis.google_shopping_merchant.index', $data);
    }

    public function table_column(){
      return [
          'id'                    => ["label" => __('ID') , "sorting" => true],
          'offer_id'              => ["label" => __('Google Offer ID') , "sorting" => true],
          'title'                 => ["label" => __('Title') , "sorting" => true],
          'ean'                   => ["label" => 'EAN' , "sorting" => true],
          'brand'                 => ["label" => __('Brand') , "sorting" => true],
          'channel_price'         => ["label" => __('price') , "sorting" => true],
          'google_cheapest_price' => ["label" => __('Google Price') , "sorting" => true],
          'cv'                    => ["label" => 'ø' , "sorting" => true],
          'google_rating'         => ["label" => 'Google Rating' , "sorting" => true],
          'clicks'                => ["label" => __("clicks") , "sorting" => true],
          'impressions'           => ["label" => __('impression') , "sorting" => true],
          'ctr'                   => ["label" => __('CTR') , "sorting" => true],
      ];
  }

  private function getClient():\Google_Client
  {
      $configJson = base_path().'/config.json';

      $applicationName = 'DropMatix';

      $client = new \Google_Client();
      $client->setApplicationName($applicationName);
      $client->setAuthConfig($configJson);
      $client->setAccessType('offline');
      $client->setApprovalPrompt ('force');
      $client->setScopes(
          [
              "https://www.googleapis.com/auth/content"
          ]
      );
      $client->setIncludeGrantedScopes(true);

      return $client;
  }

  public function getAuthUrl()
  {
      $client = $this->getClient();

      $authUrl = $client->createAuthUrl();

      return $authUrl;
  }

  public function postLogin(Request $request)
  {
      $authCode = urldecode($request->input('code'));

      $client = $this->getClient();

      $accessToken = $client->fetchAccessTokenWithAuthCode($authCode);

      $client->setAccessToken(json_encode($accessToken));

      DB::table('google_oauth')->where('user_id', CRUDBooster::myParentId())->updateOrInsert([
        'user_id' => CRUDBooster::myParentId(),
      ],[
        'google_access_token' => $accessToken['access_token'],
        'google_refresh_token' => $accessToken['refresh_token']
      ]);

      return view('admin.cp_analysis.google_shopping_merchant.merchand_id_form');
  }

  public function updateMerchantId()
  {
      return view('admin.cp_analysis.google_shopping_merchant.merchand_id_form');
  }

  public function saveMerchantId(Request $request){
    try{
        $merchantId = $request->get('merchant_id');
        DB::table('google_oauth')->where('user_id', CRUDBooster::myParentId())->update([
          'merchant_id' => $merchantId
        ]);

        $GoogleMerchant = new GoogleMerchant();
        $GoogleMerchant->getUserProducts();

        return Redirect::to(route('drm.google_shopping.google.shopping.merchant'));
    } catch (Exception $e) {
          return response()->json([
              'success' => false,
              'message' => 'Something went wrong!'
          ]);
      }
  }
  
  public function googleContentHistory(Request $request) {

      $user_id = $request->get('user_id') ?? CRUDBooster::myParentId();

      $allowed_users = [2134, 1813, 2698, 2873, 2911, 2872];
      if(!in_array($user_id, $allowed_users)){
        $historyData = [];
        $historyData[] = [
          'name' => 'Click',
          'data' => []
        ];
        $historyData[] = [
          'name' => 'Impression',
          'data' => []
        ];
        $historyData[] = [
          'name' => 'CTR',
          'data' => []
        ];
        $data = json_encode($historyData);
        return $data;
      }

      $historyLimit = $request->get('google_history_limit') ?? 90;

      $merchantHistory = GoogleMerchantHistory::where('user_id', $user_id)->orderBy('id', 'desc')->first();

      $clickHistory = array_slice(array_reverse(json_decode($merchantHistory->history)[0]->data), 0, $historyLimit);
      $impressionHistory = array_slice(array_reverse(json_decode($merchantHistory->history)[1]->data), 0, $historyLimit);
      $ctrHistory = array_slice(array_reverse(json_decode($merchantHistory->history)[2]->data), 0, $historyLimit);
      $totalClick = 0;
      $totalCtr = 0;
      $totalImp = 0;

      foreach($clickHistory as $item){
        $totalClick += $item->y;
      }
      foreach($impressionHistory as $item){
        $totalImp += $item->y;
      }
      foreach($ctrHistory as $item){
        $totalCtr += $item->y;
      }

      $avgCtr = number_format((float)$totalCtr, 1, ',', '.');
      $historyData = [];
      $historyData[] = [
        'name' => "Klicks <br> In Total <br> <span style='font-size: 20px'>" . $totalClick . "</span>",
        'data' => array_slice(array_reverse(json_decode($merchantHistory->history)[0]->data), 0, $historyLimit)
      ];
      $historyData[] = [
        'name' => "Impressionen <br> In Total <br> <span style='font-size: 20px'>" . $totalImp . "</span>",
        'data' => array_slice(array_reverse(json_decode($merchantHistory->history)[1]->data), 0, $historyLimit)
      ];
      $historyData[] = [
        'name' => "CTR <br> Average <br> <span style='font-size: 20px'>" . $avgCtr . "%</span>",
        'data' => array_slice(array_reverse(json_decode($merchantHistory->history)[2]->data), 0, $historyLimit)
      ];

      $merchantHistory->history = json_encode($historyData);

      $data = $merchantHistory->history;
      return $data ?? "";
  }

  public function googleBestSellerHistory(Request $request) {

      $user_id = $request->get('user_id') ?? CRUDBooster::myParentId();

      $exists = \App\Models\Export\UniversalExport::where([
          'user_id' => $user_id,
          'default' => true,
          'dt_feed' => true
      ])->exists();

      $allowed_users = [2134, 1813, 2698, 2873, 2911, 2872];
      if(!in_array($user_id, $allowed_users) || !$exists){
        $bestSellerData = [];

        $bestSellerData[] = [
          'name' => 'Klicks',
          'data' => []
        ];
        $bestSellerData[] = [
          'name' => 'CTR',
          'data' => []
        ];

        $data = json_encode($bestSellerData);
        return $data;
      }

      $bestSellerProducts = GoogleMerchantReport::where('user_id', $user_id)->orderBy('clicks', 'desc')->limit(10)->get();

      $bestSellerClicksRecords = [];
      $bestSellerCTRRecords = [];

      $totalClick = 0;
      $totalCtr = 0;
      $totalProduct = (count($bestSellerProducts) > 0) ? count($bestSellerProducts) : 1;

      foreach($bestSellerProducts as $item){
          $bestSellerClicksRecords[] = ['x' => $item->ean, 'y' => $item->clicks ?? 0 ];
          $bestSellerCTRRecords[] = ['x' => $item->ean, 'y' => $item->ctr ?? 0 ];
          $totalClick += ($item->clicks ?? 0);
          $totalCtr += ($item->ctr ?? 0);
      }

      $avgClicks = $totalClick / $totalProduct;
      $avgCtr = round(($totalCtr / $totalProduct), 6);

      $bestSellerData = [];

      $bestSellerData[] = [
        'name' => "Klicks <br> Average <br> <span style='font-size:20px'>" . $avgClicks . "</span>",
        'data' => $bestSellerClicksRecords
      ];
      $bestSellerData[] = [
        'name' => "CTR <br> Average <br> <span style='font-size:20px'>" . $avgCtr . "</span>",
        'data' => $bestSellerCTRRecords
      ];

      $data = json_encode($bestSellerData);
      // return response()->json(['records' => $data, 'avg_clicks' => $avgClicks, 'avg_ctr' => $avgCtr]);
      return $data;
  }

  public function manualUpdate(){
    DB::table('google_oauth')->get()->each(function ($item) {
        ProcessGoogleMerchantUpdateJob::dispatch($item->id, $item->user_id);
    });
  }

}