<?php

namespace App\Http\Controllers\Product;

use App\DrmImport;
use App\Enums\ImportFields;
use App\Enums\ImportSourceType;
use App\FtpCredential;
use App\Http\Controllers\Controller;
use App\Models\Import\FileSource;
use App\Services\ProductImportService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use DB;
use App\Services\Modules\Import\ImportService;
use Illuminate\Support\LazyCollection;
use App\TmpDrmProduct;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Redis;
use PhpOffice\PhpSpreadsheet\IOFactory;
use App\Enums\Tax;
use Session;
use App\User;
use App\DrmImportTemplate;
use App\IndustryTemplate;
use App\Services\DateTime\DateTime;
use App\Http\Controllers\AdminManualImportTarrifController;
use Carbon\Carbon;
use App\Services\AppStoreService;
use App\Enums\Apps;
use App\Helper\AppStore;
use App\Enums\Product;
use Request as req;
use DataTables as DataTables;
use App\DeliveryCompany;
use App\DrmProductField;
use App\Services\Keepa\Automagic;
use App\DrmProduct;
use App\Models\DRMProductCategory;
use App\TrackAutomagic;
use App\Jobs\DrmFeedAutomagic;
use App\MarketplaceProducts;
use App\Jobs\Supplier\DeleteImportFeed;
class ImportController extends Controller
{
    private ProductImportService $service;

    public $count = 0;
    public $invalid = 0;
    public $settings;
    public $ean_field;
    public $ean;
    public $existing_products = [];
    public $insertArray = [];

    public function __construct(ProductImportService $service)
    {
        $this->service = $service;
    }

    public function index()
    {
        redirectToV2('/products/import');

        $data['ftp_app_banner'] = False;
        $check_importing = $this->importProductCheck();
        // $import_details=AppStore::ShowImportLimitBanner(CRUDBooster::myId());
        // dd($check_importing, drmTotalProduct(CRUDBooster::myParentId()), (500 > 500) ? true : false);

        if (CRUDBooster::myPrivilegeId() == '3') {
            if (($check_importing['blocked'] != '') or ($check_importing['product_amount'] <= 0)) {
                return redirect()->route('import_paymet')->with('msg', 'you Need To Purchase Plan For Importing Product');
            }
        }

        $data = [];
        $data['import_new_product'] = "https://player.vimeo.com/video/393321002";
        $data['import_video'] = drm_video_url(42);
        $data['product_sync'] = "https://player.vimeo.com/video/391571545";
        $data['import_product'] = $check_importing;

        $data['template_purchased'] = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, CRUDBooster::myParentId());
        if (CRUDBooster::myId() == null) {
            return redirect(CRUDBooster::adminPath());
        }
        if (isset($_REQUEST['id'])) {
            $id = $_REQUEST['id'];
            if ($id != null) {
                $drm = User::find(CRUDBooster::myParentId())->drm_imports()->where('id', $id)->first();

                $country = DB::table('countries')->where('id', $drm->country_id)->first();
                $data['drm_categories'] = [];
                if ($country != null) {
                    $data['drm_categories'] = DB::table('drm_category')->select('category_name_' . $country->language_shortcode . ' as category_name', 'id')->where('user_id', $drm->user_id)->where('country_id', $drm->country_id)->get();
                }

                //  $data['price_categories'] = DB::table('price_category')->where('user_id',CRUDBooster::myParentId())->get();
                $category_ids = json_decode($drm->category_ids);

                $data['product_categories'] = [];
                if (is_array($category_ids)) {
                    $data['product_categories'] = DB::table('drm_category')
                        ->select('drm_category.category_name_' . $country->language_shortcode . ' as category_name', 'drm_category.id')
                        ->whereIn('id', $category_ids)
                        ->get();
                }

                if ($data['product_categories'] == null) {
                    $data['product_categories'] = [];
                }
                if (isset($_GET['tab'])) {
                    $drm->current_step = $_GET['tab'];
                }

                if($drm->current_step == 'upload_csv'){
                    $view = "admin.drm_import.new.upload_file";
                    $data['ftp_app_banner'] = True;

                    $user_industry_temp_field = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->first();

                    $data['user_industry_temp_field'] = $user_industry_temp_field;

                    $data['has_industry_temp'] = false;

                    if($user_industry_temp_field){
                        $data['has_industry_temp'] = true;
                    }
                }

                if ($drm->current_step == 'fields') {
                    $sources = FileSource::where([
                        'drm_import_id' => $drm->id,
                    ])->get();

                    // if( (isLocal() && !in_array(CRUDBooster::myParentId(), [179, 212])) || (in_array(CRUDBooster::myParentId(), [62, 98])) ){
                        $fields = array();
                        $csv_fields = array();
                        $demo_data = array();
                        $data['options'] = array();

                        foreach ($sources as $source) {
                            $data['fields'][$source->file_name] = removeNulls($source->fields);
                            $data['csv_fields'][$source->file_name] = removeNulls($source->header);
                            $data['demo_data'][$source->file_name] = $source->demo_data;

                            foreach ($data['fields'][$source->file_name] as $field) {
                                $option = '';
                                foreach ($data['csv_fields'][$source->file_name] as $header) {
                                    $option.= "<option value='$header'>$header</option>";
                                }
                                $data['options'][$field] = $option;
                            }

                            if($source->industry_temp_fields){
                                $data['industry_temp_fields'][$source->file_name] = $source->industry_temp_fields;

                                foreach ($data['industry_temp_fields'][$source->file_name] as $temp_field) {
                                    $temp_option = '';
                                    foreach ($data['csv_fields'][$source->file_name] as $header) {
                                        $temp_option.= "<option value='$header'>$header</option>";
                                    }
                                    $data['options'][$temp_field] = $temp_option;
                                }
                            }
                        }
                    // }

                    $data['import_video'] = drm_video_url(44);
                    $data['image_backup_premium'] = AppStore::ActiveFeature('Image-Backup');

                    $data['settings'] = DB::table('drm_import_settings')->where('drm_import_id', $drm->id)->first();
                    $data['country'] = $country;

                    $view = "admin.drm_import.new.field_mapping";
                }

                elseif ($drm->current_step == 'template') {
                    $data['import_video'] = drm_video_url(45);

                    $fields = $drm->drm_product_fields->toArray();

                    $data['title_fields'] = Product::TEMPLATE_FIELDS;
                    unset($data['title_fields']['description']);
                    $data['desc_fields'] = Product::TEMPLATE_FIELDS;
                    $image_fields = explode('|', $fields['image']);
                    foreach ($image_fields as $key => $value) {
                        $i = $key + 1;
                        $data['desc_fields'][] = 'image_' . $i;
                    }

                    $data['template'] = $drm->drm_import_template;

                    // if(isLocal() || in_array(CRUDBooster::myParentId(), [212])){
                        $industry_template_on_product_template = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->first();
                        $industry_template_field_name = [];

                        if($industry_template_on_product_template){
                            $industry_template_field_name = array_keys($industry_template_on_product_template->fields);
                        }

                        if($industry_template_field_name){
                            $data['title_fields'] = array_merge($data['title_fields'], $industry_template_field_name);
                            $data['desc_fields'] = array_merge($data['desc_fields'], $industry_template_field_name);
                        }
                    // }

                    $view = "admin.drm_import.new.import_product_template";
                }

                elseif ($drm->current_step == 'products') {
                    $data['import_video'] = drm_video_url(46);
                    $data['total'] = TmpDrmProduct::where('drm_import_id', $id)->count();

                    $data['ean_missing'] = TmpDrmProduct::where('drm_import_id', $id)->where(function ($q) {
                        return $q->where('ean', null)->orWhere('ean', "");
                    })->count();
                    $data['fixable_ean'] = TmpDrmProduct::where('drm_import_id', $id)->whereRaw('LENGTH(ean) IN (11,12)')->count();

                    $data['ean_module_purchased'] = AppStore::ActiveFeature('GTIN-Archiv');

                    if (CRUDBooster::myParentId() == 98) {
                        $data['ean_module_purchased'] = true;
                    }
                    $view = "admin.drm_import.new.temp_product_list";
                }

                elseif ($drm->current_step == 'search_and_replace') {
                    $data['import_video'] = drm_video_url(48);
                    $data['filtered_ids'] = $this->getFilteredIds($id);
                    $view = "admin.drm_import.search_and_replace";
                }
                //-- endif
                elseif ($drm->current_step == 'market_place') {

                    $template = DB::table('drm_product_fields')->select('industry_template')->where('drm_import_id', $drm->id)->first();
                    if($template){
                        $template = (array) json_decode($template->industry_template);
                        $template_name = array_key_first($template);
                        $marketplace_cat_id = templateMarketplaceCategory($template_name);

                        if(isset($template_name)){
                            $imported_products = DrmProduct::select('ean')->where('user_id', $id)->pluck('ean');
                            $data['suggested_products'] = MarketplaceProducts::whereNotIn('ean', $imported_products)
                                                        ->where('category_id', $marketplace_cat_id)->limit(15)->get();
                        }
                    }
                    $view = "admin.drm_import.new.market_place";
                }
                elseif ($drm->current_step == 'filter') {
                    $data['filter'] = DB::table('drm_import_filter')->where('drm_import_id', $id)->first();
                    $data['import_video'] = drm_video_url(47);
                    $data['filtered_ids'] = $this->getFilteredIds($id);

                    $data['magicable'] = TmpDrmProduct::select('brand', 'item_color', 'item_weight', 'item_size', 'ek_price')
                    ->where('drm_import_id', $id)
                    ->where(function($q) {
                        $q->whereNotNull('brand')->orWhere('brand','<>','');
                        $q->whereNotNull('item_color')->orWhere('item_color','<>','');
                        $q->whereNotNull('item_weight')->orWhere('item_weight','<>','');
                        $q->whereNotNull('item_size')->orWhere('item_size','<>','');
                        $q->whereNotNull('ek_price')->orWhere('ek_price','<>','');
                    })->count();


                    $automagic = new Automagic(CRUDBooster::myId());
                    $data['magic_left'] = $automagic->magicAvailable();

                    $view = "admin.drm_import.new.filter_v2";
                }
                $data['drm'] = $drm;

                if ($drm->import_finished == 1) {
                    return redirect('/admin/drm_imports/new/feed-list');
                } else {
                    $data['errors'] = DB::table('drm_import_errors')->where('drm_import_id', $id)->limit(10)->get();
                    $select_option = json_decode($drm->csv_headers);
                    $data['select_option'] = removeNulls($select_option);
                    if (view()->exists($view)) {
                        return view($view, $data);
                    } else {
                        return redirect()->back();
                    }
                }
            } else {
                return redirect('/admin/drm_imports/new/feed-list');
            }
        } else {
            $data['ftp_app_banner'] = True;

            $data['import_video'] = drm_video_url(43);

            // if((isLocal() || in_array(CRUDBooster::myParentId(), [212]))){
                $data['user_industry_template'] = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->first();
            // }
            // if( (isLocal() && !in_array(CRUDBooster::myParentId(), [179, 2455, 212])) || (in_array(CRUDBooster::myParentId(), [62, 98])) ){

                return view('admin.drm_import.new.initialize', $data);
            // }
            // return view('admin.drm_import.upload_csv', $data);
        }
    }

    public function importProductCheck($user_id = null)
    {
        if ($user_id == null) {
            $user_id = CRUDBooster::myParentId();
        }
        $trial = $this->checkImportTrial($user_id);
        $check_trial = DB::table('app_trials')->where(['user_id' => $user_id, 'app_id' => 0])->count();
        $manual_tarrif = new AdminManualImportTarrifController;
        $assigned_amount = $manual_tarrif->checkManualTarrif($user_id);
        $total_products = drmTotalProduct($user_id);
        $plan = $this->checkImportPlan($user_id);
        $plan_total = $assigned_amount['amount'] + $plan['amount'];

        //Plan amonunt
        $plan_limit = $plan['amount'] ?? 0;

        if ($plan_total) {
            $check_trial = true;
        }

        if ($check_trial && $plan['import_plan_payment_discount'] != 100) {
            if ($plan['amount'] != 0 && $plan['amount'] >= $total_products) {
                $data['product_amount'] = $plan['amount'] - $total_products;
                $data['blocked'] = '';
                $data['limit'] = '';
                $data['plan'] = "Purchased";
                $data['days'] = $plan['days'];
                $data['plan_id'] = $plan['id'];
                $data['plan_total'] = $plan_total;
                $data['plan_limit'] = $plan_limit;

            } elseif ($assigned_amount['amount'] != 0 && $assigned_amount['amount'] >= $total_products) {
                $data['product_amount'] = $assigned_amount['amount'] - $total_products;
                $data['blocked'] = '';
                $data['limit'] = '';
                $data['plan'] = "Assigned";
                $data['days'] = $assigned_amount['days'];
                $data['plan_total'] = $assigned_amount['amount'];
                $data['plan_limit'] = $plan_limit;
            } elseif ($trial > 0) {
                $data['product_amount'] = 1;
                $data['blocked'] = '';
                $data['limit'] = 'Unlimited';
                $data['plan'] = "Trial";
                $data['days'] = $trial;
            } else {

                // if(isLocal()){
                $data['product_amount'] = 0;
                $data['blocked'] = 'blocked';
                $data['limit'] = '';
                $data['plan_total'] = $plan_total;
                $data['plan_limit'] = $plan_limit;
                // }


                if ($assigned_amount['amount']) {
                    $plan_total = (int)$assigned_amount['amount'];
                    $data['plan'] = "Assigned";
                    $data['days'] = $assigned_amount['days'];
                } elseif ($plan['amount']) {
                    $plan_total = (int)$plan['amount'];
                    $data['plan'] = "Purchased";
                    $data['days'] = $plan['days'];
                    $data['plan_id'] = $plan['id'];
                } else {
                    // $data['plan'] = "Expired";
                    $plan_total = 0;
                    $data['days'] = 0;

                    // if(isLocal()){
                    $data['total_product'] = $total_products;
                    $data['blocked'] = '';
                    $data['product_amount'] = 500 - $total_products;
                    $data['plan'] = "500 Free Products";
                    $data['plan_total'] = 500;
                    $data['plan_limit'] = $plan_limit;
                    // }
                }
                // if(isLocal()){
                // 	// $data['plan_total'] = $plan_total;
                // }else{
                // 	$data['product_amount'] = 0;
                // 	$data['blocked']='blocked';
                // 	$data['limit']='';
                // 	$data['plan_total'] = $plan_total;
                // }
            }
        } else {
            $data['product_amount'] = 1;
            $data['blocked'] = '';
            $data['limit'] = 'Unlimited';
            $data['plan'] = "none";
            $data['days'] = 0;
            $data['plan_total'] = 0;
            $data['plan_limit'] = $plan_limit;
            $data['import_plan_discount'] = $plan['import_plan_payment_discount'];
        }

        if (empty($check_trial) && empty($plan) && $total_products <= 500) {
            $data['product_amount'] = 500 - $total_products;
            $data['blocked'] = '';
            $data['limit'] = 500;
            $data['plan'] = "Free";
            $data['days'] = null;
            $data['plan_total'] = $assigned_amount['amount'] + 500;
            $data['plan_limit'] = $plan_limit;
            $data['free_500'] = true;
        }

        if (!empty($plan['is_unlimited']) && $plan['is_unlimited']) {
            $data['product_amount'] = $plan['amount'];
            $data['blocked'] = '';
            $data['limit'] = 'Unlimited';
            $data['plan'] = "Unlimited";
            $data['is_unlimited'] = true;
            $data['days'] = $plan['days'];
            $data['plan_total'] = $plan['amount'];
            $data['plan_limit'] = $plan_limit;
            $data['import_plan_discount'] = $plan['import_plan_payment_discount'];
        }

        return $data;
    }

    public function checkImportTrial($user_id)
    {
        $remain_days = 0;
        $trial = DB::table('app_trials')
            ->select('trial_days', 'start_date')
            ->where(['user_id' => $user_id, 'app_id' => 0])->first();
        if ($trial) {
            $remain_days = DateTime::getTrialRemaining($trial->start_date, $trial->trial_days);
        }
        return $remain_days;
    }

    public function checkImportPlan($user_id)
    {
        $date_diff = 0;
        $plan = DB::table('purchase_import_plans')
            ->where('cms_user_id', $user_id)
            ->first();

        $import_plan_discount = DB::table('import_plan_get_discounts')
            ->where('user_id', $user_id)
            ->where('end_date', '>=', Carbon::now()->toDateTimeString())
            ->where('status', 1)
            ->first();

        if ($plan) {
            $end_date = $plan->end_date;
            $date_diff = DateTime::getRemainDays(date('Y-m-d'), $end_date);
        }
        if ($date_diff > 0) {
            $data['amount'] = (int)$plan->product_amount_import;
            $data['is_unlimited'] = $plan->import_plan_id == 23;
            $data['days'] = $date_diff;

            if ($import_plan_discount) {
                $data['import_plan_payment_discount'] = $plan->import_plan_percentage_discount;
            } else {
                $data['import_plan_payment_discount'] = 0;
            }

            $data['id'] = $plan->id;

            return $data;
        } else {
            return false;
        }
    }

    public function postSaveImportNew(\Illuminate\Http\Request $request)
    {
        redirectToV2('/products/import');
        
        $message = ['text' => 'Initializing...', 'percent' => '10'];
        sentProgress($message, 'import');
        $user_id = CRUDBooster::myParentId();

        if ($request->company_user_type == 1) {
            $company_id = DeliveryCompany::create([
                'user_id' => $user_id,
                'name' => $request->company_name,
                'address' => $request->company_address,
                'phone' => $request->company_contactnumber,
                'state' => $request->company_state,
                'country_id' => $request->company_country,
                'zip' => $request->company_zip,
                'email' => $request->company_email,
                'contact_name' => $request->company_contactname,
            ])->id;
        } else {
            $company_id = $request->company_user;
        }

        $message = ['text' => 'Saving information...', 'percent' => '100'];
        sentProgress($message, 'import');

        $drm_id = DrmImport::create([
            'user_id' => $user_id,
            'delivery_company_id' => $company_id,
            'csv_file_name' => $request->csv_filename,
            'country_id' => $request->item_country,
            'current_step' => 'upload_csv'
        ])->id;

        $selected_industry_template_name = $_REQUEST['industry_templates'];
        $industry_template_checkbox = $_REQUEST['industry_template_checkbox'];

        if (!empty($selected_industry_template_name) && !isset($industry_template_checkbox)) {

            $selected_template_fields = 'industry_template.'.strtolower($selected_industry_template_name);

            $new_template_insert = IndustryTemplate::updateOrCreate(
                ['user_id' => $user_id],
                ['name' => $selected_industry_template_name, 'fields' => config($selected_template_fields), 'status' => true]
            );

        }else if( (isset($industry_template_checkbox) && $industry_template_checkbox == 'on') && (empty($selected_industry_template_name) || !empty($selected_industry_template_name)) ){

            $template_exits = IndustryTemplate::where('user_id',$user_id)->exists();

            if($template_exits){
                IndustryTemplate::where('user_id',$user_id)->update(['status' => false]);
            }

        }

        Session::put('unfinished_' . $user_id, $drm_id);
        Session::save();

        // return (int)$drm_id;

        return redirect('/admin/drm_imports/new?id='.(int)$drm_id);

        // return redirect(CRUDBooster::mainPath('import?id='.(int)$drm_id));
    }

    public function postSelectedTemplateFields(){
        $selected_template_name = $_REQUEST['selected_template_name'];

        $selected_template_field_name = config("industry_template.".$selected_template_name);
        $selected_template_field_name = array_keys($selected_template_field_name);

        $popover_content = '<div id="template_field_list"> <ul class="list-group">';
        foreach($selected_template_field_name as $value){
            $trans_field_name = __('industry_template.'.$value);
            $popover_content .= '<li class="list-group-item">' . $trans_field_name . '</li>';
        }
        $popover_content .= '</ul> </div>';

        return response()->json(['success' => true, 'popover_content' => $popover_content], 200);
    }

    public function getFileList(Request $request)
    {
        $sources = FileSource::where([
            'drm_import_id' => $request->import_id,
        ])->get();

        $user_industry_temp_field = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->first();
        $industry_temp_fields_label = [];

        if($user_industry_temp_field){

            $industry_temp_fields = array_keys($user_industry_temp_field->fields);

            foreach($industry_temp_fields as $field){
                $industry_temp_fields_label[$field] = __('industry_template.'.$field);
            }

        }

        return response(['sources' => $sources,'field_labels' => ImportFields::ALL, 'industry_template_fields_label' => $industry_temp_fields_label]);
    }

    public function storeFile(Request $request)
    {
        $user_id = CRUDBooster::myParentId();

        $import = DrmImport::where([
            'user_id' => $user_id,
            'id' => $request->drm_import_id
        ])->first();

        $file_exist = FileSource::where('drm_import_id', $request->drm_import_id)->count();

        // && !in_array(CRUDBooster::myParentId(), [98])

        if($file_exist >= 1){

            if( isLocal() ){
                $multiple_file_sources_app_id = config('global.multiple_file_sources_app_id_local');
            }else{
                $multiple_file_sources_app_id = config('global.multiple_file_sources_app_id_live');
            }

            $check_multiple_sources_app_purchased = app('App\Services\AppStoreService')->checkAppPurchased($multiple_file_sources_app_id, CRUDBooster::myParentId());

            if(!$check_multiple_sources_app_purchased){

                return response([
                    'status' => false,
                    'multiple_sources_app_purchased' => false
                ]);

            }
        }

        $csv_data = array();
        if ($request->source_type == ImportSourceType::URL) {
            $source_url = trim($request->csv_link);
            $exists = FileSource::where([
                'drm_import_id' => $import->id,
                'source_config->source_url' => $source_url
            ])->exists();
            if (!$exists) {
                sentProgress(['text' => 'Getting Data From : <i>' . substr($source_url, 0, 40) . '...</i>', 'percent' => '50'], 'import');
                $csv_data = getRemoteFile($source_url);
            } else {
                return response([
                    'status' => false,
                    'exists' => true
                ]);
            }
        } elseif ($request->source_type == ImportSourceType::FILE && $request->csv_file) {
            $file_type = pathinfo($request->csv_file->getClientOriginalName(), PATHINFO_EXTENSION);
            $content = $request->csv_file->getContent();
            $csv_data = [
                'data' => $content,
                'ext' => $file_type
            ];
        } elseif ($request->source_type == ImportSourceType::FTP) {
            sentProgress(['text' => 'Getting Data From FTP...', 'percent' => '50'], 'import');
            $credential = FtpCredential::where([
                'user_id' => $user_id
            ])->first();
            $source_url = 'ftp://' . $credential->user_name . ':' . $credential->password . '@' . $credential->host_name . '/' . $request->file_path;
            $csv_data = getRemoteFile($source_url);
        }

        $valid = false;
        if ($csv_data['data']) {
            $valid_data = $this->service->validateFile($csv_data, $request->delimiter);

            if ($valid_data['feed_data'] && $valid_data['valid']) {
                $valid = true;
                $redis_key = $this->service->storeToRedis($valid_data['feed_data']);
                $file_path = 'import_files/' . $import->id . '/' . Str::random() . '.' . $csv_data['ext'];

                Storage::disk('dropmatix')->put($file_path, $csv_data['data'], 'public');

                $industry_template_fields = null;

                if(isset($request->industry_fields) && $request->industry_fields){
                    $industry_template_fields = $request->industry_fields;
                }

                FileSource::updateOrCreate([
                    'drm_import_id' => $import->id,
                    'source_config' => [
                        'redis_key' => $redis_key,
                        'cloud_path' => $file_path,
                        'source_url' => $source_url ?? null
                    ],
                    'delimiter' => $request->delimiter,
                    'source_type' => $request->source_type,
                    'demo_data' => $valid_data['feed_data'][1] ?? array(),
                    'header' => $valid_data['feed_data'][0] ?? array(),
                    'file_name' => $request->file_name,
                    'fields' => $request->fields,
                    'industry_temp_fields' => $industry_template_fields
                ]);
            }
        }

        return response(['status' => $valid]);
    }

    public function removeSource(Request $request)
    {
        $this->service->removeSource($request->import_id, $request->source_id);
        return response(['success' => true]);
    }

    public function processSources(Request $request)
    {
        $used_fields = Arr::flatten(FileSource::where([
            'drm_import_id' => $request->drm_import_id
        ])->pluck('fields')->toArray());

        $mandatory_field_missing = [];

        $mandatory_field = [
            'title',
            'item_number',
            'ean',
            'description',
            'category',
            'image',
            'ek_price',
            'stock',
        ];

        if($used_fields){
            foreach($mandatory_field as $mandatory_field_value){
                if(!in_array($mandatory_field_value, $used_fields)){
                    $mandatory_field_missing[$mandatory_field_value] = ImportFields::ALL[$mandatory_field_value];
                }
            }
        }else{
            return response()->json([
                'mandatory_missing_fields' => true,
                'missing_fields' => $mandatory_field,
                'missing_fields_count' => count($mandatory_field)
            ]);
        }

        if($mandatory_field_missing){
            return response()->json([
                'mandatory_missing_fields' => true,
                'missing_fields' => $mandatory_field_missing,
                'missing_fields_count' => count($mandatory_field_missing)
            ]);
        }

        sentProgress(['text' => 'Processing files...', 'percent' => '70'], 'import');
        $user_id = CRUDBooster::myParentId();
        $import = DrmImport::where([
            'id' => $request->drm_import_id,
            'user_id' => $user_id
        ])->first();
        $files = $import->files;

        $import->current_step = 'fields';
        $import->save();
        return success_response();
    }

    public function postSaveFieldsNew()
    {
        $message = ['text' => 'Initializing Fields', 'percent' => '10'];
        sentProgress($message, 'import');

        $image_backup = req::input('image_backup');

        $manual_category = req::input('custom_category');
        $import_id = req::input('drm_import_id');
        $drm_category = req::input('category_from_drm');

        $delivery_days = req::input('delivery_time');
        if ($delivery_days == null) {
            $delivery_days = req::input('delivery_time_field');
        }
        $drm = DrmImport::find($import_id);

        $country = DB::table('countries')->where('id', $drm->country_id)->first();
        DB::table('drm_category')->where('drm_import_id', $import_id)->delete();
        DB::table('drm_import_categories')->where('drm_import_id', $import_id)->delete();
        DB::table('drm_product_fields')->where('drm_import_id', $import_id)->delete();

        $image_separator = req::input('image_separator');
        if (req::input('image_separator') == null || req::input('image_separator') == "") {
            $image_separator = " ";
        }
        if (req::input('select_stock') != "file") {
            $drm->unlimited_quantity = 1;
            if (req::input('select_stock') == "manual") {
                $drm->fixed_stock = (int)req::input('manual_stock');
            } else {
                $drm->fixed_stock = 10000;
            }
        }
        if ($image_backup) {
            $drm->image_backup = 1;
        }

        $drm->image_prefix = req::input('prefix');
        $drm->image_suffix = req::input('suffix');
        $drm->image_separator = $image_separator;
        $drm->item_number_prefix = req::input('item_number_prefix');
        $drm->image_validation = req::input('image_validation');
        $drm->money_format = req::input('money_format');

        if (req::input('fallback') != null) {
            $drm->fallback = req::input('fallback');
        }


        DB::table('drm_import_settings')->updateOrInsert(
            ['drm_import_id' => $import_id],
            [
                'quantity_surcharge' => drm_convert_european_to_decimal(req::input('quantity_surcharge'), $drm->money_format) ?? 0,
                'discount' => drm_convert_european_to_decimal(req::input('discount'), $drm->money_format) ?? 0,
                'discount_type' => req::input('discount_type') ?? "%",
                'description_prefix' => req::input('description_prefix'),
                'description_suffix' => req::input('description_suffix'),
            ]
        );


        // if($custom_category != null && $drm_category != null){
        // 	$category_from_drm = DB::table('drm_category')->where('id',$drm_category)->first();
        // 	if($category_from_drm!=null){
        // 		$category_from_drm = (array)$category_from_drm;
        // 		$category_from_drm = $category_from_drm["category_name"."_".$country->language_shortcode];
        // 	}
        // 	$allCategory = $category_from_drm . "|" . $custom_category;
        // }elseif($drm_category != null){
        // 	$drm->existing_category = $drm_category;
        // 	$category_from_drm = DB::table('drm_category')->where('id',$drm_category)->first();
        // 	   if($category_from_drm!=null){
        // 		   $category_from_drm = (array)$category_from_drm;
        // 		   $category_from_drm = $category_from_drm["category_name"."_".$country->language_shortcode];
        // 		   }
        // 	$allCategory = $category_from_drm;
        // }elseif($custom_category != null){
        //    $allCategory = $custom_category;
        // }

        // if($category_from_drm!=null){
        // 	$drm->custom_category = 1;
        // }

        if ($drm_category != null) {
            $drm->existing_category = $drm_category;
            $custom_category = DB::table('drm_category')->where('id', $drm_category)->first();
            if ($custom_category != null) {
                $custom_category = (array)$custom_category;
                $custom_category = $custom_category["category_name" . "_" . $country->language_shortcode];
            }
        }

        if ($custom_category != null) {
            $drm->custom_category = 1;
        }

        $message = ['text' => 'Processing Fields', 'percent' => '20'];
        sentProgress($message, 'import');
        $images = req::input('image');

        foreach ($images as $key => $value) {
            if ($key == 0) {
                $image = $value;
            } else {
                $image = $image . "|" . $value;
            }
        }

        //   ($custom_category!=null)?$category=$custom_category:$category=req::input('category');

        if ($manual_category != null) {
            $drm->manual_category = $manual_category;
            $drm->custom_category = 1;
        }

        $categories = (array)req::input('category');

        if ($categories) {
            foreach ($categories as $key => $value) {
                if ($key == 0) {
                    $category = $value;
                } else {
                    $category = $category . "|" . $value;
                }
            }
        } else {
            $category = "";
        }

        $tax = (float)req::input('manual_tax') ? [Tax::ORIGINAL => $country->tax_rate, Tax::REDUCED => $country->reduced_tax_rate,][(float)req::input('manual_tax')] ?? 0 : req::input('tax');


        // Industry Template

        $industry_template = [];

        if(isset($_REQUEST["industry_template_name"])){

            $template_name = 'industry_template.'.strtolower($_REQUEST["industry_template_name"]);
            $template_property = config($template_name);

            foreach($template_property as $key => $value){
                $industry_template_property[$key] = req::input($key);
            }

            $industry_template[strtolower($_REQUEST["industry_template_name"])] = $industry_template_property;

        }

        // End Industry Template

        if(req::input('shipping_cost_manual')){
            $shipping_cost = drm_convert_european_to_decimal(req::input('shipping_cost_manual'), $drm->money_format);
        }else{
            $shipping_cost = req::input('shipping_cost');
        }

        (req::input('item_number') != null) ? $item_number = req::input('item_number') : $item_number = req::input('ean');
        $message = ['text' => 'Saving Fields', 'percent' => '50'];
        sentProgress($message, 'import');

        $drm_products = [
            'drm_import_id' => $import_id,
            'name' => req::input('name'),
            'item_number' => $item_number,
            'item_weight' => req::input('item_weight'),
            'item_size' => req::input('item_size'),
            'item_color' => req::input('item_color'),
            'production_year' => req::input('production_year'),
            'brand' => req::input('brand'),
            'materials' => req::input('materials'),
            'ean' => req::input('ean'),
            'description' => req::input('description'),
            'short_description' => req::input('short_description'),
            'image' => $image,
            'ek_price' => req::input('ek_price'),
            'vk_price' => req::input('vk_price'),
            'stock' => req::input('stock'),
            'category' => $category,
            'gender' => req::input('gender'),
            'status' => req::input('status'),
            'delivery_days' => $delivery_days,
            'shipping_cost' => $shipping_cost,
            'uvp' => req::input('uvp'),
            'dropfunnel_tags_manual' => req::input('dropfunnel_tags_manual'),
            'dropfunnel_tags' => implode('|', req::input('dropfunnel_tags') ?? []),
            'tax' => $tax,
            'offer_options' => req::input('offer_options')
        ];


        if(!empty($industry_template)){
            $drm_products['industry_template'] = json_encode($industry_template);
        }
        DrmProductField::create($drm_products);

        $fields = DB::table('drm_product_fields')->where('drm_import_id', $import_id)->first();
        $message = ['text' => 'Creating Categories', 'percent' => '70'];
        sentProgress($message, 'import');

        $template_purchased = app(AppStoreService::class)->checkAppPurchased(Apps::PRODUCT_TEMPLATE, CRUDBooster::myParentId());
        $drm->current_step = 'template';
        $drm->save();
        $message = ['text' => 'Mapping Completed', 'percent' => '100'];
        sentProgress($message, 'import');
        return "template";
    }

    public function getCheckFormatNew()
    {
        $drm = DrmImport::find(req::input('id'));
        $return = true;

        if($drm->files){
            foreach($drm->files as $file){
                if(in_array('ek_price', $file->fields)){

                    $headers = $file->header;
                    $demo = $file->demo_data;
                    $demo_data = array_combine($headers, $demo);
                    $demo_data = removeNullKeys($demo_data);
                    $request = req::input();

                    $format = $request['money_format'];
                    $ek_price = $demo_data[$request['ek_price']];
                    $mod_ek = drm_convert_european_to_decimal($ek_price, $format);

                    if ($ek_price != 0 && $mod_ek == 0) {
                        $return = false;
                    }

                    break;
                }
            }
        }

        return json_encode(['status' => $return]);
    }

    public function getDrmCategoriesNew()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $id = $_REQUEST['id'];
        $drm = DrmImport::find($id);
        $country = DB::table('countries')->where('id', $drm->country_id)->first();
        $data['drm_categories'] = [];
        if ($country != null) {
            $drm_categories = DB::table('drm_category')
                ->select('category_name_' . $country->language_shortcode . ' as category_name', 'id')
                ->where('user_id', $drm->user_id)
                ->where('country_id', $drm->country_id)
                ->whereNotNull('category_name_' . $country->language_shortcode)
                ->get();
        }
        $html .= "<option value=''>Please select category</option>";
        foreach ($drm_categories as $key => $value) {

            $html .= "<option value='$value->id'>$value->category_name</option>";
        }
        return $html;
    }

    public function getTemplatePreviewNew()
    {
        $import_id = $_REQUEST['id'];
        $tempate = $_REQUEST['template'];
        $user_id = CRUDBooster::myParentId();
        $tempProduct = \Cache::remember('temp_product_' . $user_id, 05.0, function () use ($import_id) {
            $drm = DrmImport::find($import_id);
            $headers = json_decode($drm->csv_headers, true);
            $demo = json_decode($drm->demo_data, true);
            $demo_data = array_combine($headers, $demo);
            $data = removeNullKeys($demo_data);

            $fields = $drm->drm_product_fields;
            $image_headers = explode('|', $fields->image);
            $image_separator = $drm->image_separator;
            $image_array = array();

            foreach ($image_headers as $header) {
                $csv_images = explode($image_separator, $data[$header]);
                $image_array = array_merge($image_array, $csv_images);
            }

            $demo_product = [
                'drm_import_id' => $import_id,
                'title' => $data[$fields->name],
                'item_number' => strip_tags($data[$fields->item_number]),
                'item_weight' => strip_tags($data[$fields->item_weight]),
                'item_size' => strip_tags($data[$fields->item_size]),
                'item_color' => strip_tags($data[$fields->item_color]),
                'production_year' => strip_tags($data[$fields->production_year]),
                'brand' => strip_tags($data[$fields->brand]),
                'materials' => strip_tags($data[$fields->materials]),
                'ean' => strip_tags($data[$fields->ean]),
                'TransDescription' => $data[$fields->description],
                'image' => json_encode($image_array),
                'stock' => strip_tags($data[$fields->stock]),
                'category' => $category,
                'status' => $fields->status,
                'gender' => strip_tags($data[$fields->gender]),
            ];

            $preview_field = json_decode($fields->industry_template, true);

            if($preview_field){
                foreach($preview_field as $fields){
                    $filterd_field = array_filter($fields);
                    $industry_template_value_array = [];

                    foreach($filterd_field as $key => $value){
                        $industry_template_value_array[$key] = $data[$value];
                    }

                    if($industry_template_value_array){
                        $demo_product = array_merge($demo_product, $industry_template_value_array);
                    }
                }
            }

            $product = (object)$demo_product;
            return $product;
        });

        $input = $tempate;
        $template = $this->generateTemplate($tempProduct, null, $input);
        return $template;
    }

    public function getAvailableFields(Request $request)
    {
        $used_fields = Arr::flatten(FileSource::where([
            'drm_import_id' => $request->import_id
        ])->pluck('fields')->toArray());

        $unused_fields = array_diff(array_keys(ImportFields::ALL),$used_fields);
        $available_fields = [];

        foreach($unused_fields as $field){
            $available_fields[$field] = ImportFields::ALL[$field];
        }

        $mandatory_field = [
            'title',
            'item_number',
            'ean',
            'description',
            'category',
            'image',
            'ek_price',
            'stock',
        ];

        foreach($available_fields as $available_field_key => $available_field_value){
            if(in_array($available_field_key, $mandatory_field)){
                $available_fields[$available_field_key] = $available_field_value . " *";
            }
        }

        return response($available_fields);
    }

    public function getDemoData(Request $request)
    {
        $import = DrmImport::find($request->id);

        $data = array();

        foreach ($import->files as $source)
        {
            $data['fields'][$source->file_name] = removeNulls($source->fields);
            $data['csv_fields'][$source->file_name] = removeNulls($source->header);
            // $data['demo_data'][$source->file_name] = array_combine(removeNulls($source->header), $source->demo_data);
            $demo_all_csv[$source->file_name] = array_combine($source->header, $source->demo_data);
        }
        $data['request'] = $request->all();
        $data['demo_data'] = [];
        $demo_data_request = array_filter($request->all());

        foreach($demo_data_request as $demo_data_key => $demo_data_value){
            if( in_array($demo_data_key, array_keys(ImportFields::ALL)) ){

                if($demo_data_key == "category" || $demo_data_key == "image"){

                    foreach($data['fields'] as $field_csv_name => $field_value){

                        if(in_array($demo_data_key, $field_value)){         // Finding CSV Name for Selected Field

                            foreach($data['request'][$demo_data_key] as $cat_value){
                                if(!in_array($cat_value, $data['demo_data'])){
                                    $data['demo_data'][$cat_value] = $demo_all_csv[$field_csv_name][$cat_value];  // Storing information from multiple csv to signle array
                                }
                            }

                        }
                    }

                }else{
                    foreach($data['fields'] as $csv_name => $csv_value){
                        if(in_array($demo_data_key, $csv_value)){        // Finding CSV Name for Selected Field
                            $data['demo_data'][$demo_data_value] = $demo_all_csv[$csv_name][$demo_data_value];    // Storing information from multiple csv to signle array
                        }
                    }
                }

            }
        }

        return view('admin.drm_import.new.partials.demo_data_table',$data);
    }

    public function postImportTempProducts($import_id = null, $return_url = null)
    {

        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $this->count = 0;
        $message = ['text' => 'Initializing Import'];
        sentProgress($message, 'import');
        if ($import_id == null) {
            $import_id = $_REQUEST['drm_import_id'];
        }

        $user_id = CRUDBooster::myParentId();

        $drm = DrmImport::with('files')->where('id', $import_id)->first();

        $fallback = $_REQUEST['fallback'];
        $import_update['fallback'] = $fallback;
        if ($return_url == null) {
            $import_update['current_step'] = 'products';
            $return_url == $_REQUEST['return_url'];
        }

        $fields = DB::table('drm_product_fields')->where('drm_import_id', $import_id)->first();
        $this->setEanField($drm, $fields);

        DB::table('drm_imports')->where('id', $import_id)->update($import_update);
        $this->existing_products = $this->getExistingProducts($user_id, $import_id);

        $this->settings = DB::table('drm_import_settings')->where('drm_import_id', $import_id)->first();

        // $this->import_service = new ImportService($drm);


        $message = ['text' => 'Getting Data From Sheet'];
        sentProgress($message, 'import');

        $imported_csv_files = $drm->files;

        if($imported_csv_files){

            $csv_data = [];

            foreach($imported_csv_files as $import_csv_file){

                $file_url = $import_csv_file->source_config['cloud_path'];
                $type = pathinfo($file_url, PATHINFO_EXTENSION);
                $csv_delimiter = $import_csv_file->delimiter;
                $csv_redis_key = $import_csv_file->source_config['redis_key'];

                if ($type == 'xml') {
                    $csv_data = $this->xmlToArray($file_url);
                } else {
                    $single_csv_data = $this->getCsv($file_url, $type, $csv_delimiter, $csv_redis_key);

                    $selected_field_from_csv = $import_csv_file->fields;
                    $selected_industry_temp_field_from_csv = $import_csv_file->industry_temp_fields;

                    if($single_csv_data){
                        $csv_header_store = [];
                        $industry_temp_csv_header_store = [];

                        foreach($single_csv_data as $single_csv_data_key => $value){   //CSV Data

                            if($single_csv_data_key == 0){

                                if($selected_field_from_csv){   //User Selected field for CSV

                                    foreach($selected_field_from_csv as $selected_field_key => $selected_field_value){

                                        if($selected_field_value == 'title'){

                                            if( ((array)$fields)["name"] ){
                                                $csv_header_index = array_search(((array)$fields)["name"], $value);  // Matching field with CSV and Mapping field, finding index
                                                $csv_header_store[$csv_header_index] = ( (array)$fields )["name"];   // Store Matching field with CSV and Mapping field value
                                            }

                                        }else if($selected_field_value == 'category' || $selected_field_value == 'image'){

                                            if( ((array)$fields)[$selected_field_value] ){

                                                $multiple_selected_field = ((array)$fields)[$selected_field_value];
                                                $multiple_selected_field = explode("|", $multiple_selected_field);

                                                foreach($multiple_selected_field as $single_field_value){

                                                    $csv_header_index = array_search($single_field_value, $value);

                                                    if(!in_array($single_field_value, $csv_header_store)){
                                                        $csv_header_store[$csv_header_index] = $single_field_value;
                                                    }

                                                }

                                            }

                                        }else{
                                            if( ((array)$fields)[$selected_field_value] ){
                                                $csv_header_index = array_search(((array)$fields)[$selected_field_value], $value);  // Matching field with CSV and Mapping field, finding index
                                                $csv_header_store[$csv_header_index] = ( (array)$fields )[$selected_field_value];  // Store Matching field with CSV and Mapping field value
                                            }
                                        }

                                    }

                                    if(empty($csv_data[$single_csv_data_key])){
                                        $csv_data[$single_csv_data_key] = array_values($csv_header_store);
                                    }else{
                                        foreach($csv_header_store as $header_value){
                                            if(!in_array($header_value, $csv_data[$single_csv_data_key])){
                                                $csv_data[$single_csv_data_key][] = $header_value;
                                            }
                                        }
                                    }
                                }

                                if($selected_industry_temp_field_from_csv){   //User Selected Industry Template field for CSV

                                    foreach($selected_industry_temp_field_from_csv as $selected_industry_temp_field_key => $selected_industry_temp_field_value){

                                        if($selected_industry_temp_field_value == 'title'){

                                            if( ((array)$fields)["name"] ){
                                                $industry_temp_csv_header_index = array_search(((array)$fields)["name"], $value);  // Store Matching field with CSV and Mapping field value
                                                $industry_temp_csv_header_store[$industry_temp_csv_header_index] = ( (array)$fields )["name"];  // Store Matching field with CSV and Mapping field value
                                            }

                                        }else{

                                            $industry_temp_data = json_decode( ((array)$fields)['industry_template'], true );

                                            foreach($industry_temp_data as $industry_temp_data_key => $industry_temp_data_value){

                                                if( $industry_temp_data_value[$selected_industry_temp_field_value] ){

                                                    $industry_temp_csv_header_index = array_search($industry_temp_data_value[$selected_industry_temp_field_value], $value);

                                                    $industry_temp_csv_header_store[$industry_temp_csv_header_index] = $industry_temp_data_value[$selected_industry_temp_field_value];
                                                }

                                            }

                                        }

                                    }

                                    if(empty($csv_data[$single_csv_data_key])){
                                        $csv_data[$single_csv_data_key] = array_values($industry_temp_csv_header_store);
                                    }else{
                                        foreach($industry_temp_csv_header_store as $header_value){
                                            if(!in_array($header_value, $csv_data[$single_csv_data_key])){
                                                $csv_data[$single_csv_data_key][] = $header_value;
                                            }
                                        }
                                    }

                                }
                            }else{
                                $last_element_key = array_key_last($csv_header_store);

                                foreach($csv_header_store as $csv_array_keys => $csv_array_value){
                                    $header_index_csv_data = array_search($csv_array_value, $csv_data[0]);

                                    if( $header_index_csv_data > 0 && ($last_element_key == $csv_array_keys) ){   // For header and content same positioning

                                        $array_index_search = $header_index_csv_data;
                                        $array_index_search = $array_index_search - 1;

                                        for($array_index_search; $array_index_search >= 0; $array_index_search--){
                                            if( empty($csv_data[$single_csv_data_key][$array_index_search]) ){
                                                $csv_data[$single_csv_data_key][$array_index_search] = null;
                                            }
                                        }

                                    }
                                    $csv_data[$single_csv_data_key][$header_index_csv_data] = $value[$csv_array_keys];
                                }

                                ksort($csv_data[$single_csv_data_key]);

                                if($industry_temp_csv_header_store){

                                    $last_element_industry_temp_key = array_key_last($industry_temp_csv_header_store);

                                    foreach($industry_temp_csv_header_store as $csv_array_industry_temp_keys => $csv_array_industry_temp_value){
                                        $industry_temp_header_index_csv_data = array_search($csv_array_industry_temp_value, $csv_data[0]);

                                        if( $industry_temp_header_index_csv_data > 0 && ($last_element_industry_temp_key == $csv_array_industry_temp_keys) ){   // For header and content same positioning

                                            $industry_temp_array_index_search = $industry_temp_header_index_csv_data;
                                            $industry_temp_array_index_search = $industry_temp_array_index_search - 1;

                                            for($industry_temp_array_index_search; $industry_temp_array_index_search >= 0; $industry_temp_array_index_search--){
                                                if( empty($csv_data[$single_csv_data_key][$industry_temp_array_index_search]) ){
                                                    $csv_data[$single_csv_data_key][$industry_temp_array_index_search] = null;
                                                }
                                            }

                                        }
                                        $csv_data[$single_csv_data_key][$industry_temp_header_index_csv_data] = $value[$csv_array_industry_temp_keys];
                                    }

                                    ksort($csv_data[$single_csv_data_key]);

                                }
                            }
                        }
                    }
                }
            }
        }

        DB::table('tmp_drm_products')->where('drm_import_id', $import_id)->delete();
        DB::table('drm_import_errors')->where('drm_import_id', $import_id)->delete();

        if ($drm->image_backup) {
            Storage::disk('spaces')->deleteDirectory('import/backup/images/' . $drm->id);
        }

        $message = ['text' => 'Starting Import'];
        // event(new progressEvent($message));
        sentProgress($message, 'import');

        $key = array_map('trim', $csv_data[0]);
        $key = array_map('removeDots', $key);
        $key = makeArrayUtf8(makeArrayUtf8($key));
        $key_count = count($key);
        unset($csv_data[0]);
        $total = count($csv_data);

        $country = DB::table('countries')->where('id', $drm->country_id)->first();

        LazyCollection::make(function () use ($csv_data, $key_count) {
            foreach ($csv_data as $line) {
                if (count($line) == $key_count && !containsOnlyNull($line)) {
                    yield makeArrayUtf8(makeArrayUtf8($line));
                }
            }
        })
            ->chunk(200)
            ->each(function ($lines) use ($import_id, $fields, $drm, $user_id, $total, $message, $key,$country) {
                $this->insertArray = null;
                array_walk($lines, function ($chunks) use ($drm, $fields, $key,$country) {
                    array_walk($chunks, function ($chunk) use ($drm, $fields, $key,$country) {
                        $data = array_combine($key, $chunk);
                        $data = makeArrayUtf8(makeArrayUtf8($data));

                        $valid = $this->drmErrorReport($data, $fields, $drm, $country);
                        if ($valid['valid']) {
                            $this->insertArray[] = $this->insertSingleProduct($drm, $data, $fields, $country);
                        } else {
                            $this->invalid++;
                        }

                        $this->count++;
                    });
                });
                if (is_array($this->insertArray)) {
                    $last = end($this->insertArray);
                    $name = $last['name'];
                    TmpDrmProduct::insert($this->insertArray);
                }

                $message = ['total' => $total, 'count' => $this->count, 'percent' => round(($this->count / $total) * 100, 2), 'invalid' => $this->invalid, 'name' => $name];
                sentProgress($message, 'import');
            });

        $message = ['text' => 'Creating Categories', 'percent' => '100'];
        sentProgress($message, 'import');

        $categories = TmpDrmProduct::where('drm_import_id', $import_id)->pluck('category')->unique()->toArray();
        $categories = array_map('json_decode', $categories);
        $categories = removeNulls(array_unique(Arr::flatten($categories)));


        $trans_cat = "category_name_" . $country->language_shortcode;
        $drm_categories = [];
        if (\Schema::hasColumn('drm_category', $trans_cat)) {
            $categories_cache = $this->getCategoryCache($drm->user_id);
            $drm_categories = $categories_cache->pluck($trans_cat)->toArray();
            if (is_array($drm_categories)) {
                $drm_categories = removeNulls($drm_categories);
            }
        }

        $all_imported_categories = array();
        foreach ($categories as $category) {
            if (!in_array($category, $drm_categories)) {
                DB::table('drm_category')
                    ->insert([
                        'drm_import_id' => $id,
                        $trans_cat => $category,
                        'country_id' => $drm->country_id,
                        'user_id' => $drm->user_id,
                    ]);
            }
            $all_imported_categories[] = $category;
        }

        $inserted_categories = DB::table('drm_category')->whereIn($trans_cat, $all_imported_categories)->where('user_id', $drm->user_id)->pluck('id')->toArray();
        $inserted_categories = json_encode($inserted_categories);

        DB::table('drm_imports')->where('id', $drm->id)->update(['category_ids' => $inserted_categories]);
        return true;
    }

    public function setEanField($drm, $csv_fields)
    {
        if ($drm->ean_field == 1) {
            $this->ean_field = $csv_fields->ean;
            $this->ean = "ean";
        } else {
            $this->ean_field = $csv_fields->item_number;
            $this->ean = "item_number";
        }
    }

    public function getExistingProducts($user_id, $import_id)
    {
        return Cache::remember('existing_products_' . $user_id, 05.0, function () use ($user_id, $import_id) {
            return DB::table('drm_products')->where('user_id', $user_id)->whereNull('deleted_at')->where('drm_import_id', '<>', $import_id)->pluck("ean")->toArray();
        });
    }

    public function xmlToArray($path)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $xml_file = file_get_contents_utf8($path);
        $xml = simplexml_load_string($xml_file);
        $array = $this->simpleXmlToArray($xml->product);

        foreach ($xml->product as $key => $value) {
            $array = $this->simpleXmlToArray($value);
            $final_array[] = $array;
            // yield $array;
        }
        return $final_array;
    }

    function simpleXmlToArray($xmlObject)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        ini_set('memory_limit', -1);
        $array = [];
        foreach ($xmlObject->children() as $node) {
            $array[$node->getName()] = is_array($node) ? simplexml_to_array($node) : (string)$node;
        }
        return $array;
    }

    public function getCsv($file_url, $type, $csv_delimiter, $csv_redis_key)
    {
        $file_url = $file_url;
        $type = pathinfo($file_url, PATHINFO_EXTENSION);
        try {
            $redis = Redis::connection();
            $json = $redis->get($csv_redis_key);
            if ($json) {
                $csv_data = json_decode($json);
            }
        } catch (\Predis\Connection\ConnectionException $e) {
        }
        if (!$csv_data) {
            $csv_data = $this->csvToArrayModified($file_url, $type, $csv_delimiter);

            try {
                $redis = Redis::connection();
                $redis->set($csv_redis_key, json_encode($csv_data));
            } catch (\Predis\Connection\ConnectionException $e) {

            }
        }
        return $csv_data;
    }

    public function csvToArrayModified($path, $type, $delimiter, $cloud = true)
    {
        $rand = Str::random(40);
        if ($cloud == true) {
            $path = Storage::disk('dropmatix')->url($path);
            file_put_contents($rand . '.' . $type, fopen($path, 'r'));
            $localpath = $rand . '.' . $type;
        } else {
            $localpath = $path;
        }

        if ($type == 'csv' || $type == 'txt') {
            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
            // $reader->setInputEncoding('UTF-8');
            if ($delimiter != 'auto') {
                $reader->setDelimiter($delimiter);
            }
            $spreadsheet = $reader->load($localpath);
        } else {
            $spreadsheet = IOFactory::load($localpath);
        }
        if ($cloud) {
            unlink($localpath);
        }
        return $spreadsheet->getActiveSheet()->toArray();
    }

    public function drmErrorReport($data, $field, $drm, $country): array
    {
        $valid = true;

        if ((trim($data[$field->ean]) == null || trim($data[$field->ean]) == '') && (trim($data[$field->item_number]) == null || trim($data[$field->item_number]) == '')) {
            $valid = false;
        }

        if (trim($data[$field->name]) == null || trim($data[$field->name]) == '') {
            $valid = false;
        }

        if (trim($data[$field->description]) == null || trim($data[$field->description]) == '') {
            $valid = false;
        }

        $categories = json_decode($this->service->generateCategory($data, $field->category)) ?? [];

        if (count($categories) == 0 && trim($field->category) != null && !$drm->custom_category && !$drm->existing_category) {
            $valid = false;
        }

        if (drm_convert_european_to_decimal(trim($data[$field->ek_price]), $drm->money_format) == 0) {
            $valid = false;
        }

        if (trim($data[$field->description]) == null || trim($data[$field->description]) == '') {
            $valid = false;
        }


        $tax = is_numeric($field->tax) ? (float)$field->tax : (float)$data[$field->tax];
        if(!in_array($tax,[$country->tax_rate,$country->reduced_tax_rate])){
            $valid = false;
        }

        $report['valid'] = $valid;

        return $report;
    }

    public function insertSingleProduct($drm, $data, $fields,$country): array
    {
        $duplicate_ean = 0;
        if (in_array($this->validateEan($data[$fields->ean]), $this->existing_products)) {
            $duplicate_ean = 1;
        }
        $image_headers = explode('|', $fields->image);
        $image_separator = $drm->image_separator;
        $final_array = array();
        foreach ($image_headers as $header) {
            $csv_images = explode($image_separator, $data[$header]);
            $final_array = array_merge($final_array, $csv_images);
        }
        $json_image = $this->getImageJson($final_array, $drm);
        $array_img = json_decode($json_image);
        if (count($array_img) < 1) {
            $json_image = json_encode(array("https://drm-file.fra1.digitaloceanspaces.com/public/images/icons/drm-logo.png"));
        } else {
            $json_image = json_encode($array_img);
        }

        $tag_fields = explode('|', $fields->dropfunnel_tags) ?? [];
        $tags = explode(',', $fields->dropfunnel_tags_manual) ?? [];

        foreach ($tag_fields as $tag_field) {
            $tags[] = $data[$tag_field];
        }

        $tags = generateTags($tags);
        if ($drm->item_number_prefix != null) {
            $item_number = $drm->item_number_prefix . "" . strip_tags($data[$fields->item_number]);
        } else {
            $item_number = strip_tags($data[$fields->item_number]);
        }

        $ean = $this->validateEan($data[$fields->ean]);

        if ($drm->unlimited_quantity) {
            $stock = $drm->fixed_stock;
        } else {
            $stock = strip_tags($data[$fields->stock]);
        }

        if (is_numeric($fields->delivery_days)) {
            $delivery_days = (int)$fields->delivery_days;
        } else {
            $delivery_days = (int)strip_tags($data[$fields->delivery_days]);
        }


        if ($item_number == null || $item_number == "") {
            $item_number = $ean;
        }

        $ek_price = drm_convert_european_to_decimal($data[$fields->ek_price], $drm->money_format);
        $price = $this->service->calculatePurchasePrice($ek_price, $this->settings);
        $description = $this->service->generateDescription($data[$fields->description], $this->settings);

        $shipping_cost = is_numeric($fields->shipping_cost) ? $fields->shipping_cost : drm_convert_european_to_decimal($data[$fields->shipping_cost], $drm->money_format);

        if ($drm->custom_category == 1) {
            $merge_category = [];
            $drm_existing_category = $drm->existing_category;
            $fetch_category_from_drm = DB::table('drm_category')->where('id', $drm_existing_category)->first();

            if ($fetch_category_from_drm != null) {
                $fetch_category_from_drm = (array)$fetch_category_from_drm;
                $fetch_category_from_drm = $fetch_category_from_drm["category_name" . "_" . $country->language_shortcode];
            }

            if ($fetch_category_from_drm != null) {
                $merge_category[] = $fetch_category_from_drm;
            }

            if ($drm->manual_category != null) {
                $drm_manual_category = $drm->manual_category;
                $merge_category[] = $drm_manual_category;
            }

            $drm_n_manual_category = json_encode($merge_category);
            $field_category = $this->service->generateCategory($data, $fields->category);

            $check_field_category = array_filter(json_decode($field_category, true));
            if (empty($check_field_category)) {
                $category = json_encode(json_decode($drm_n_manual_category, true));
            } else {
                $category = json_encode(array_merge(json_decode($drm_n_manual_category, true), json_decode($field_category, true)));
            }

        } else {
            $category = $this->service->generateCategory($data, $fields->category);
        }

        // Industry Template

        $industry_template_fields = json_decode($fields->industry_template, true);

        $industry_template_data = [];

        if ($country->language_shortcode == 'de' && !empty($industry_template_fields)) {

            foreach($industry_template_fields as $key => $field){

                foreach($field as $property => $value){

                    $industry_template_data_property[$property] = ['de' => !empty($data[$value]) ? $data[$value] : null, 'en' => null];
                }

                $industry_template_data[$key] = $industry_template_data_property;
            }

        } else if ($country->language_shortcode == 'en' && !empty($industry_template_fields)) {

            foreach($industry_template_fields as $key => $field){

                foreach($field as $property => $value){

                    $industry_template_data_property[$property] = ['en' => !empty($data[$value]) ? $data[$value] : null, 'de' => null];

                }

                $industry_template_data[$key] = $industry_template_data_property;
            }

        }

        // End Industry Template

        $tax = is_numeric($fields->tax) ? $fields->tax : $data[$fields->tax];
        $tax_type = array_search($tax,[
            Tax::ORIGINAL => $country->tax_rate,
            Tax::REDUCED => $country->reduced_tax_rate
        ]);

        $factory = [
            'drm_import_id' => $drm->id,
            'name' => $data[$fields->name],
            'item_number' => $item_number,
            'item_weight' => strip_tags($data[$fields->item_weight]),
            'item_size' => strip_tags($data[$fields->item_size]),
            'item_color' => strip_tags($data[$fields->item_color]),
            'production_year' => strip_tags($data[$fields->production_year]),
            'brand' => strip_tags($data[$fields->brand]),
            'materials' => strip_tags($data[$fields->materials]),
            'ean' => $ean,
            'description' => $description,
            'short_description' => $data[$fields->short_description],
            'image' => $json_image,
            'original_ek' => $ek_price,
            'ek_price' => $price,
            'shipping_cost' => $shipping_cost ?? 0,
            'vk_price' => 0,
            'uvp' => drm_convert_european_to_decimal($data[$fields->uvp], $drm->money_format),
            'stock' => $stock,
            'category' => $category,
            'duplicate_ean' => $duplicate_ean,
            'update_enabled' => '1',
            'status' => $fields->status,
            'gender' => strip_tags($data[$fields->gender]),
            'user_id' => $drm->user_id,
            'delivery_company_id' => $drm->delivery_company_id,
            'country_id' => $drm->country_id,
            'delivery_days' => $delivery_days,
            'tags' => $tags,
            'tax_type'  => $tax_type,
            'offer_options' => $data[$fields->offer_options],
        ];

        if(!empty($industry_template_data)){
            $factory['industry_template_data'] = json_encode($industry_template_data);
            $factory['tax'] = $tax;
        }else{
            $factory['tax'] = $tax_type ? $tax : false;
        }

        return $factory;
    }

    public function getImageJson($array, $drm)
    {
        $final_img = array();
        $prefix = $drm->image_prefix;
        $suffix = $drm->image_suffix;
        foreach ($array as $key => $value) {
            $img = str_replace(' ', '', $value);
            if ($img != null) {
                if (filter_var($img, FILTER_VALIDATE_URL) === FALSE && $prefix != null) {
                    $value = $this->importImagePrefix($prefix, $img);
                }

                if ($suffix != null) {
                    $value = $this->importImageSuffix($suffix, $value);
                }

                if ($drm->image_validation) {
                    $image_exists = checkImageUrl($value);
                } else {
                    $image_exists = true;
                }

                if ($image_exists) {
                    $final_img[] = $value;
                }
            }
        }
        return json_encode($final_img);
    }

    public function importImagePrefix($prefix, $image): string
    {
        if (substr($prefix, -1) == '/') {
            $image = $prefix . $image;
        } else {
            $image = $prefix . '/' . $image;
        }
        return $image;
    }

    public function importImageSuffix($suffix, $image): string
    {
        return $image . $suffix;
    }

    public function getCategoryCache($user_id)
    {
        $categories = Cache::remember('Categories_' . $user_id, 05.0, function () use ($user_id) {
            $db_categories = DB::table('drm_category')->where('user_id', $user_id)->cursor();
            $all_categories = collect($db_categories->all());
            return $all_categories;
        });
        return $categories;
    }

    public function validateEan($ean)
    {
        $ean_original = strip_tags($ean);
        $sanitize = explode('.', $ean_original);
        $ean_original = $sanitize[0];
        $ean = trim($ean_original);
        $int_ean = (int)$ean;

        if (CRUDBooster::myId() == 485) {
            if (strlen($ean) > 6 && strlen($ean) < 14 && $int_ean) {
                return $ean;
            } else {
                return null;
            }
        }

        if (strlen($ean) > 7 && strlen($ean) < 14 && $int_ean) {
            return $ean;
        } else {
            return null;
        }
    }

    public function postSaveNewProductTemplate()
    {
        $desc_template = $_REQUEST['desc_template'];
        $title_template = $_REQUEST['title_template'];
        $import_id = $_REQUEST['drm_import_id'];
        $import = DrmImport::find($import_id);

        $data = [];

        if (!empty($desc_template) && !empty($title_template)) {

            $data = [
                'drm_import_id' => $import_id,
                'description' => str_replace('<p><br></p>', '', $desc_template),
                'title' => str_replace('<p><br></p>', '', $title_template)
            ];

        } elseif (!empty($title_template)) {

            $data = [
                'drm_import_id' => $import_id,
                'description' => str_replace('<p><br></p>', '', "<p>#DESCRIPTION#<br></p>"),
                'title' => str_replace('<p><br></p>', '', $title_template)
            ];

        } elseif (!empty($desc_template)) {

            $data = [
                'drm_import_id' => $import_id,
                'description' => str_replace('<p><br></p>', '', $desc_template),
                'title' => str_replace('<p><br></p>', '', "<p>#TITLE#<br></p>")
            ];

        } else {

            $this->postImportTempProducts($import_id);
            return redirect('admin/drm_imports/import?id=' . $import_id);

        }

        DrmImportTemplate::updateOrCreate(['drm_import_id' => $import_id], $data);
        $fields = $import->drm_product_fields;
        if (isset($_REQUEST['update'])) {
            if ($_REQUEST['update'] == '1') {
                return redirect('admin/drm_imports');
            }
        } else {
            $import->current_step = 'products';
            $import->save();
            $this->postImportTempProducts($import_id);
            return redirect('admin/drm_imports/import?id=' . $import_id);
        }

    }

    public function getIndustryTempAvailableFields(Request $request)
    {
        $user_industry_temp_field = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->first();

        if($user_industry_temp_field){
            $used_industry_temp_fields = Arr::flatten(FileSource::where([
                'drm_import_id' => $request->import_id
            ])->pluck('industry_temp_fields')->toArray());

            $unused_industry_temp_fields = array_diff(array_keys($user_industry_temp_field->fields), $used_industry_temp_fields);

            $available_fields = [];

            if($unused_industry_temp_fields){
                foreach($unused_industry_temp_fields as $field){
                    $available_fields[$field] = __('industry_template.'.$field);
                }
            }

            return response($available_fields);
        }

    }

    public function getFileData(Request $request)
    {
        $selected_file_type = $request->file_type;
        $drm_import_id = $request->import_id;
        $file_link = '';
        $input_file = '';
        $ftp_file_path = '';
        $user_id = CRUDBooster::myParentId();

        if(isset($request->file_link)){
            $file_link = $request->file_link;
        }

        if(isset($request->csv_file) && $request->hasFile('csv_file')){
            $input_file = $request->csv_file;
        }

        if(isset($request->ftp_file_path)){
            $ftp_file_path = $request->ftp_file_path;
        }

        $csv_data = [];

        if ($selected_file_type == ImportSourceType::URL && !empty($file_link) ) {
            $source_url = trim($file_link);

            $csv_data = getRemoteFile($source_url);
            // $csv_data['File Type'] = $selected_file_type;

        } elseif ($selected_file_type == ImportSourceType::FILE && !empty($input_file) ) {
            $file_type = pathinfo($input_file->getClientOriginalName(), PATHINFO_EXTENSION);
            $content = $input_file->getContent();
            $csv_data = [
                'data' => $content,
                'ext' => $file_type
            ];

            // $csv_data['File Type'] = $selected_file_type;

        } elseif ($selected_file_type == ImportSourceType::FTP && !empty($ftp_file_path)) {
            $credential = FtpCredential::where([
                'user_id' => $user_id
            ])->first();
            $source_url = 'ftp://' . $credential->user_name . ':' . $credential->password . '@' . $credential->host_name . '/' . $ftp_file_path;
            $csv_data = getRemoteFile($source_url);
        }

        $valid = false;
        if ($csv_data['data']) {
            $valid_data = $this->service->validateFile($csv_data, $request->delimiter);

            if ($valid_data['feed_data'] && $valid_data['valid']) {
                $valid = true;
                $file_to_preview_header = [];
                $file_to_preview_content = [];

                if(count($valid_data['feed_data']) >= 3){
                    $file_to_preview = array_slice($valid_data['feed_data'], 0, 2, true);

                    $file_to_preview_header = $file_to_preview[0];

                    unset($file_to_preview[0]);
                    $file_to_preview = array_values($file_to_preview);

                    $file_to_preview_content = $file_to_preview;

                    return response()->json([ 'success' => $valid, 'file_header' => $file_to_preview_header, 'file_content' => $file_to_preview_content ], 200);
                }

                $file_to_preview_header = $valid_data['feed_data'][0];

                unset($valid_data['feed_data'][0]);
                $valid_data['feed_data'] = array_values($valid_data['feed_data']);

                $file_to_preview_content = $valid_data['feed_data'];

                return response()->json([ 'success' => $valid, 'file_header' => $file_to_preview_header, 'file_content' => $file_to_preview_content ], 200);
            }
        }

        return response()->json(['success' => $valid, 'data' => "Sorry!! No Data Found"], 400);
    }

    public function getUploadedFilePreview(Request $request){

        $id = $request->id;
        $import_id = $request->import_id;

        $preview_data = FileSource::where(['id' => $id, 'drm_import_id' => $import_id])->select('header', 'demo_data')->first();

        return response()->json([ 'success' => true, 'file_header' => $preview_data->header, 'file_content' => $preview_data->demo_data ], 200);

    }

    public function uploadUpdate(Request $request)
    {
        $id = $request->id;
        $import_id = $request->import_id;
        $save_fields = $request->fields;
        $industry_fields = $request->industry_fields;

        $available_fields = [];
        $available_industry_fields = [];

        if($save_fields){
            $save_fields = explode(',', $save_fields);

            $used_fields = Arr::flatten(FileSource::where([
                'drm_import_id' => $import_id
            ])->pluck('fields')->toArray());

            $unused_fields = array_diff(array_keys(ImportFields::ALL),$used_fields);

            foreach($unused_fields as $field){
                $available_fields[$field] = ImportFields::ALL[$field];
            }

            foreach($save_fields as $field_value){
                $available_fields[$field_value] = ImportFields::ALL[$field_value];
            }

            $mandatory_field = [
                'title',
                'item_number',
                'ean',
                'description',
                'category',
                'image',
                'ek_price',
                'stock',
            ];

            foreach($available_fields as $available_field_key => $available_field_value){
                if(in_array($available_field_key, $mandatory_field)){
                    $available_fields[$available_field_key] = $available_field_value . " *";
                }
            }

        }

        $user_industry_temp_field = IndustryTemplate::where('user_id', CRUDBooster::myParentId())->first();

        if($user_industry_temp_field){
            $used_industry_temp_fields = Arr::flatten(FileSource::where([
                'drm_import_id' => $import_id
            ])->pluck('industry_temp_fields')->toArray());

            $unused_industry_temp_fields = array_diff(array_keys($user_industry_temp_field->fields), $used_industry_temp_fields);

            if($unused_industry_temp_fields){
                foreach($unused_industry_temp_fields as $field){
                    $available_industry_fields[$field] = __('industry_template.'.$field);
                }
            }

        }


        if($industry_fields){
            $industry_fields = explode(',', $industry_fields);

            foreach($industry_fields as $field){
                $available_industry_fields[$field] = __('industry_template.'.$field);
            }
        }

        return response()->json(['id' => $id, 'import_id' => $import_id, 'mandatory_fields' => $available_fields, 'industry_fields' => $available_industry_fields]);
    }

    public function storedFileUpdate(Request $request)
    {
        $validated_data = $request->validate([
            'update_file_name'=> 'required|string',
            'update_fields'=> 'required|array',
            'update_industry_fields'=> 'nullable|array',
        ]);

        $id = $request->id;
        $import_id = $request->import_id;

        $update_file_name = $request->update_file_name;
        $update_fields = $request->update_fields;
        $update_industry_fields = null;

        if(isset($request->update_industry_fields)){
            $update_industry_fields = $request->update_industry_fields;
        }

        FileSource::where(['id' => $id, 'drm_import_id' => $import_id])
        ->update([
            'file_name' => $update_file_name,
            'fields' => $update_fields,
            'industry_temp_fields' => $update_industry_fields
        ]);

        return response()->json(['success' => true, 'message' => 'File Updated!']);
    }

    public function getImportErrors()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $id = $_REQUEST['id'];
        $errors = DB::table('drm_import_errors')->where('drm_import_id', $id)->get();
        return view('admin.drm_import.new.drm_import_errors', compact('errors'));
    }

    function postSetTab()
    {
        $tab = req::input('tab');
        $id = req::input('id');
        DB::table('drm_imports')->where('id', $id)->update(['current_step' => $tab]);
        req::session()->put('curr_tab', $tab);
        return redirect('admin/drm_imports/import?id=' . $id . '&tab=' . $tab);
    }

    function getSetTab()
    {
        $tab = $_REQUEST['tab'];
        $id = $_REQUEST['id'];
        DB::table('drm_imports')->where('id', $id)->update(['current_step' => $tab]);
        return redirect('admin/drm_imports/new?id=' . $id);
    }

    public function postFixEanNew($id){
        $fixable = TmpDrmProduct::where('drm_import_id', $id)->whereRaw('LENGTH(ean) IN (11,12)');
        $fix_count = $fixable->count();

        $fixable->each(function($product){
            $fixed_ean = sprintf("%013d", $product->ean);
            $product->ean = $fixed_ean;
            $product->save();
        });

        return [
            'status' => 'success',
            'message' => $fix_count . ' EAN updated successfully!'
        ];
    }

    public function getTmpDrmProductNew()
    {
        return TmpDrmProduct::find(req::input('id'));
    }

    public function getTmpDrmProductsNew()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $id = $_REQUEST['id'];
        $filter = DB::table('drm_import_filter')->where('drm_import_id', $id)->first();

        $excluded = $this->getFilteredIds($id);

        // if(isLocal()){
        if (!json_decode($filter->filter)) {
            $products = TmpDrmProduct::where('drm_import_id', $id);
        } else {
            if (!$filter->blacklist) {
                $products = TmpDrmProduct::whereIn('id', $excluded);
            } else {
                $products = TmpDrmProduct::where('drm_import_id', $id)->whereNotIn('id', $excluded);
            }
        }

        $products = $products->get() ?? [];
        // }
        // else{
        // 	$products = TmpDrmProduct::where('drm_import_id',$id)->get();
        // }

        $all_products = [];
        foreach ($products as $key => $value) {
            $image = '';
            $value->image = implode(',', $value->image);
            $all_products[] = $value;
        }
        return DataTables::of($all_products)->make(true);
    }

    public function getFilteredIds($import_id)
    {
        $filter_json = DB::table('drm_import_filter')->where('drm_import_id', $import_id)->first();
        $filters = json_decode($filter_json->filter, true);
        $stock_opertaor = $filters['stock_operator'];
        $products = TmpDrmProduct::where('drm_import_id', $import_id);

        if (!$filters) {
            return [];
        } else {
            $products = $products->where(function ($query) use ($filters, $stock_opertaor) {
                if (isset($filters['price_more_than'])) {
                    $query->orWhere('ek_price', '>', (float)$filters['price_more_than']);
                }

                if (isset($filters['price_below'])) {
                    $query->orWhere('ek_price', '<', (float)$filters['price_below']);
                }

                if (isset($filters['ean'])) {
                    foreach ($filters['ean'] as $ean) {
                        $query->orWhere('ean', $ean);
                    }
                }

                if (isset($filters['category'])) {
                    foreach ($filters['category'] as $category) {
                        $query->whereJsonContains('category', $category, "or");
                    }
                }

                if (isset($filters['stock'])) {
                    // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){
                    foreach ($filters['stock'] as $key => $value) {

                        if ($stock_opertaor[$key] == '=') {
                            $query->orWhere('stock', (int)$value);
                        }

                        if ($stock_opertaor[$key] == '<') {
                            $check_greater_than_operator = array_search(">", $stock_opertaor);
                            if(!empty($check_greater_than_operator)){
                                $query->orWhere(function($find_stock) use ($value, $filters, $check_greater_than_operator){
                                    $find_stock->where([
                                        [ 'stock', '<', (int)$value ],
                                        [ 'stock', '>', (int)$filters['stock'][$check_greater_than_operator] ]
                                    ]);
                                });
                                break;
                            }else{
                                $query->orWhere('stock', '<', (int)$value);
                            }
                        }

                        if ($stock_opertaor[$key] == '>') {
                            $check_less_than_operator = array_search("<", $stock_opertaor);

                            if(!empty($check_less_than_operator)){
                                $query->orWhere(function($find_stock) use ($value, $filters, $check_less_than_operator){
                                    $find_stock->where([
                                        [ 'stock', '>', (int)$value ],
                                        [ 'stock', '<', (int)$filters['stock'][$check_less_than_operator] ]
                                    ]);
                                });
                                break;
                            }else{
                                $query->orWhere('stock', '>', (int)$value);
                            }

                        }

                    }
                    // }else{
                    //     foreach ($filters['stock'] as $key => $value) {
                    //         if ($stock_opertaor[$key] == '=') {
                    //             $query->orWhere('stock', (int)$value);
                    //         }
                    //         if ($stock_opertaor[$key] == '<') {
                    //             $query->orWhere('stock', '<', (int)$value);
                    //         }
                    //         if ($stock_opertaor[$key] == '>') {
                    //             $query->orWhere('stock', '>', (int)$value);
                    //         }
                    //     }
                    // }
                }

                if (isset($filters['brand'])) {
                    foreach ($filters['brand'] as $brand) {
                        $query->orWhere('brand', $brand);
                    }
                }

                if (isset($filters['gender'])) {
                    foreach ($filters['gender'] as $gender) {
                        $query->orWhere('gender', $gender);
                    }
                }

                if (isset($filters['materials'])) {
                    foreach ($filters['materials'] as $material) {
                        $query->orWhere('materials', $material);
                    }
                }

                if (isset($filters['status'])) {
                    foreach ($filters['status'] as $status) {
                        $query->orWhere('status', $status);
                    }
                }
                return $query;
            });
        }
        return $products->pluck('id')->toArray();
    }

    public function getDeleteTmp()
    {
        $id = $_REQUEST['id'];
        $import_id = $_REQUEST['import_id'];
        TmpDrmProduct::where(['id' => $id, 'drm_import_id' => $import_id])->delete();
        return response()->json([
            'status' => 'SUCCESS',
            'code' => 200
        ]);
    }

    public function postTmpDrmProductUpdate()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $product = TmpDrmProduct::find(req::input('id'));
        $product->name = req::input('name');
        $product->item_number = req::input('item_number');
        $product->description = req::input('description');
        $product->ek_price = req::input('ek_price');
        $product->vk_price = req::input('vk_price');
        // $product->vat=req::input('vat');
        $product->stock = req::input('stock');
        $product->save();
        return "success";
    }

    public function postSetOverwrite()
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $import = DrmImport::findOrFail(req::input('id'));
        $import->overwrite = req::input('overwrite');
        $import->save();
        return "success";
    }

    public function getAssignEan()
    {
        $import_id = $_REQUEST['import_id'];
        $user_id = CRUDBooster::myId();
        $query = DB::table('tmp_drm_products')->where('drm_import_id', $import_id);
        $query = $query->where(function ($query) {
            $query->whereNull('ean')
                ->orWhere('ean', "");
        });
        $products = $query->get();
        $count = count($products);
        $ean_query = DB::table('custom_eans')->where('user_id', $user_id)->where('used', 0)->limit($count)->cursor();
        $eans = $ean_query->toArray();
        $ean_count = count($eans);
        $limit_products = $products->take($ean_count);
        foreach ($limit_products as $key => $product) {
            $ean = $eans[$key]->ean;
            DB::table('tmp_drm_products')->where('id', $product->id)->update(['ean' => $ean]);
            // DB::table('custom_eans')->where('ean',$ean)->where('user_id',$user_id)->update(['used' => 1,'product_id' => $product->id]);
        }
        $ean_ids = $ean_query->pluck('id')->toArray();
        $product_ids = $limit_products->pluck('id')->toArray();
        //
        foreach (array_chunk($ean_ids, 500) as $chunk) {
            DB::table('custom_eans')->whereIn('id', $chunk)->update(['used' => 1]);
        }

        foreach (array_chunk($product_ids, 500) as $chunk) {
            DB::table('tmp_drm_products')->whereIn('id', $chunk)->update(['ean_field' => 0]);
        }

        return true;
    }

    public function postCsvImportToDatabaseNew()
    {
        ini_set('memory_limit', -1);
        ini_set('max_execution_time', '0'); // for infinite time of execution
        $import_id = req::input('drm_import_id');
        $enable_automagic = req::input('automagic');
        DB::table('drm_import_settings')->updateOrInsert(
            ['drm_import_id' => $import_id],
            [
                'automagic' => $enable_automagic ?? 0,
            ]
        );

        $import_check = $this->importProductCheck();
        $drm = DrmImport::find($import_id);

        $fields = DB::table('drm_product_fields')->where('drm_import_id', $import_id)->first();
        $this->setEanField($drm, $fields);

        // if(isLocal()){
        $filter = DB::table('drm_import_filter')->where('drm_import_id', $import_id)->first();

        $excluded = array();
        $blacklist = 1;
        if (json_decode($filter->filter)) {
            $excluded = $this->getFilteredIds($import_id);
            $blacklist = $filter->blacklist;
        }

        if ($blacklist) {
            $data = TmpDrmProduct::where('drm_import_id', $import_id)->whereNotIn('id', $excluded)->distinct('ean')->get();
        } else {
            $data = TmpDrmProduct::where('drm_import_id', $import_id)->whereIn('id', $excluded)->distinct('ean')->get();
        }
        // }

        // else{
        // 	$excluded = $this->getFilteredIds($import_id);
        // 	$data = TmpDrmProduct::where('drm_import_id',$import_id)->whereNotIn('id',$excluded)->distinct('ean')->get();
        // }


        $total = count($data);
        $country = DB::table('countries')->where('id', $drm->country_id)->first();

        $trans_cat = "category_name_" . $country->language_shortcode;
        $lang = $country->language_shortcode;
        // $Import_filter = DB::table('drm_import_filter')->where('drm_import_id',$import_id)->first();
        // $filter = json_decode($Import_filter->filter, true);
        // DrmProduct::where('drm_import_id',$import_id)->delete();
        $category_ids = json_decode($drm->category_ids);
        $imported_categories = DB::table('drm_category')->select('id', $trans_cat)->whereIn('id', $category_ids)->cursor();
        $imported_categories_all = collect($imported_categories->all());

        $update_status = makeUpdateStatusJson();

        $chunk_size = 100;
        $count = 0;
        $trial_checked = 0;

        $used_magic = 0;
        foreach ($data->chunk($chunk_size) as $chunks) {
            $magic_product_ids = [];

            $productCategories = array();
            foreach ($chunks as $record) {
                if (CRUDBooster::myPrivilegeId() == '3') {
                    if (($import_check['limit'] == '') and ($count >= $import_check['product_amount'])) break;
                }

                if (filter_var($record->stock, FILTER_VALIDATE_INT) === false && (int)$record->stock == 0) {
                    $this->invalid++;
                    continue;
                }
                $record->stock = (int)$record->stock;

                if ($record->stock < 0) {
                    $record->stock = 0;
                }

                if ($this->checkValid($record)) {
                    $record = $record->toArray();

                    $record['title'] = [$lang => $record['name']];
                    $record['description'] = [$lang => $record['description']];
                    $record['short_description'] = [$lang => $record['short_description']];

                    $categories = json_decode($record['category'], true) ?? [];

                    $product_category = array();
                    foreach ($categories as $cat) {
                        $tags_array[] = $cat;
                        $category = $imported_categories_all->where($trans_cat, $cat)->first();

                        if ($category != null) {
                            $category = (array)$category;
                            $category_id = $category['id'];
                        } else {
                            continue;
                        }

                        if ($category_id != null) {
                            $product_category[] = [
                                'category_id' => $category_id,
                                'country_id' => $record['country_id']
                            ];
                        }
                    }

                    unset($record['category']);
                    unset($record['id']);
                    unset($record['name']);
                    // unset($record['description']);
                    // unset($record['short_description']);

                    $record['original_images'] = $record['image'];

                    if ($record['duplicate_ean'] == 1) {
                        if ($drm->overwrite == 1) {
                            // unset($record['drm_import_id']);
                            $ean = $record["ean"];
                            unset($record["ean"]);
                            unset($record['duplicate_ean']);

                            $duplicate_product = DrmProduct::where([
                                'user_id' => $drm->user_id,
                                'ean' => $ean,
                            ])->where('drm_import_id', '<>', $drm->id)->first();

                            if ($duplicate_product != null) {
                                $duplicate_product->fill($record);
                                $duplicate_product->save();

                                DB::table('drm_product_categories')->where('product_id', $duplicate_product->id)->delete();
                                foreach ($product_category as $prod_cat) {
                                    if (is_array($prod_cat)) {
                                        $prod_cat['product_id'] = $duplicate_product->id;
                                        $productCategories[] = $prod_cat;
                                    }
                                }

                            }
                        }
                        continue;
                    }
                    $record['update_status'] = $update_status;

                    $p_id = DrmProduct::updateOrCreate([
                        'user_id' => $record['user_id'],
                        'ean' => $record['ean']
                    ], $record)->id;

                    $magic_product_ids[] = $p_id; #this will be dispatched for automagic;

                    DRMProductCategory::where('product_id', $p_id)->delete();

                    if ($trial_checked == 0) {
                        $this->setImportTrial($drm->user_id);
                        $trial_checked = 1;
                    }

                    $value['product_id'] = $p_id;
                    $insertValues[] = $value;
                    foreach ($product_category as $prod_cat) {
                        if (is_array($prod_cat)) {
                            $prod_cat['product_id'] = $p_id;
                            $productCategories[] = $prod_cat;
                        }
                    }
                    $count++;
                } else {
                    $this->invalid++;
                }

            }

            /**
             * Dispatch csv imported products for automagic
             * chunk of 100 products
             */
            $automagic = new Automagic(CRUDBooster::myId());
            $magic_left = $automagic->magicAvailable($used_magic);
            if( ($magic_left > 0 || $magic_left == 'unlimited') && $enable_automagic ){
                TrackAutomagic::create([
                    'user_id' => CRUDBooster::myId(),
                    'import_id' => $import_id,
                    'product_ids' => json_encode($magic_product_ids),
                ]);
            }

            $used_magic += count($magic_product_ids);


            if (count($productCategories)) {
                DB::table('drm_product_categories')->insert($productCategories);
            }

            $message = ['total' => $total, 'count' => $count, 'invalid' => $this->invalid, 'finalize' => 1, 'percent' => number_format(($count / $total) * 100, 2), 'name' => $value['title']];
            sentProgress($message, 'import');
        }

        $automagics = TrackAutomagic::select('product_ids', 'id')->where('import_id', $import_id)->get();

        foreach ($automagics as $key => $track_automagic) {
            DrmFeedAutomagic::dispatch(CRUDBooster::myId(), json_decode($track_automagic->product_ids), $track_automagic->id)->onQueue('feed_automagic');
        }

        $suggested_products = null;
        $template = DB::table('drm_product_fields')->select('industry_template')->where('drm_import_id', $drm->id)->first();
        if($template){
            $template = (array) json_decode($template->industry_template);
            $template_name = array_key_first($template);
            $marketplace_cat_id = templateMarketplaceCategory($template_name);

            if(isset($template_name)){
                $imported_products = DrmProduct::select('ean')->where('user_id', CRUDBooster::myId())->pluck('ean');
                $suggested_products = MarketplaceProducts::whereNotIn('ean', $imported_products)
                                            ->where('category_id', $marketplace_cat_id)->limit(15)->get();
            }
        }

        $drm->import_finished = 0;
        $drm->current_step = 'market_place';

        if($suggested_products){
            $drm->import_finished = 1;
        }
        Session::remove('unfinished_' . $drm->user_id);
        DB::table('drm_import_errors')->where('drm_import_id', $import_id)->delete();
        if ($drm->save()) {
            //Clear account activity step
            \App\Services\CheckListProgress\Checklist::cache_key_clear(4, $drm->user_id);
        }

        if ($drm->image_backup) {
            app(ImportService::class)->backupImages($import_id, $drm->user_id);
        }
        $drm->tmp_drm_products()->delete();
        // $message=DB::table('notification_trigger')->where('hook','DRMImport')->where('status',0)->first();
        // if($message){
        // $message_title = $e->getMessage();
        // if (isHookRemainOnSidebar('DRMImport') && isLocal()) {
        // 	User::find(Crudbooster::myId())->notify(new DRMTelegramNotification($message_title, 'DRMImport','#'));
        // }else{
        // User::find(Crudbooster::myId())->notify(new DRMNotification($message_title, 'DRMImport','#'));
        // }
        // }

        return "translate";
    }

    public function checkValid($record)
    {
        ($record->ean_field == 1) ? $ean_field = "ean" : $ean_field = "item_number";
        if ($record->$ean_field == null || $record->$ean_field == "") {
            return false;
        } else {
            return true;
        }
    }

    public function postSearchAndReplaceNew()
    {
        $drm_id = req::input('drm_import_id');
        $keyword = makeUtf8(req::input('keyword'));
        $replace_with = makeUtf8(req::input('replace_with'));
        $queries = DB::select(DB::raw("select CONCAT(
				'UPDATE tmp_drm_products SET ',
				column_name,
				' = REPLACE(',COLUMN_NAME,',''$keyword'',''$replace_with'') where drm_import_id = $drm_id;') AS query
				from information_schema.columns
				where table_name = 'tmp_drm_products'"));
        $queries = collect($queries);
        $queries = $queries->unique();
        $filtered = $queries->filter(function ($item) {
            if (strpos($item->query, "SET id") || strpos($item->query, "SET drm_import_id")
                || strpos($item->query, "SET drm_import_id") || strpos($item->query, "SET country_id")
                || strpos($item->query, "SET user_id") || strpos($item->query, "SET delivery_company_id")
                || strpos($item->query, "SET duplicate_ean") || strpos($item->query, "SET created_at")
                || strpos($item->query, "SET updated_at") || strpos($item->query, "SET update_enabled")
                || strpos($item->query, "SET language_id")
            ) {
                return false;
            } else {
                return true;
            }
        });
        foreach ($filtered as $query) {
            $res = DB::statement($query->query);
        }
        return "search_and_replace";
    }

    public function getExcludedProductsNew()
    {
        $id = $_REQUEST['id'];
        $ids = $this->getFilteredIds($id);

        $filter = DB::table('drm_import_filter')->where('drm_import_id', $id)->first();
        // if(isLocal()){
        // 	$products = TmpDrmProduct::where('drm_import_id',$id)->get();
        // }
        // else{
        if (!json_decode($filter->filter)) {
            $products = TmpDrmProduct::where('drm_import_id', $id);
        } else {
            if (!$filter->blacklist) {
                $products = TmpDrmProduct::whereIn('id', $ids);
            } else {
                $products = TmpDrmProduct::where('drm_import_id', $id)->whereNotIn('id', $ids);
            }
        }
        // }
        return DataTables::of($products)
            ->make(true);
    }

    public function postFilterByDataFetchNew()
    {
        $drm_import_id = $_REQUEST['drm_import_id'];
        $filter_by = $_REQUEST['filterValue'];

        $distinctColoumn = DB::table('tmp_drm_products')
            ->where('drm_import_id', $drm_import_id)
            ->where($filter_by, '<>', '')
            ->select($filter_by)
            ->distinct($filter_by)
            ->pluck($filter_by)
            ->toArray();

        if ($distinctColoumn) {
            foreach ($distinctColoumn as $key => $value) {
                // $selected = $filter_input == $key ? "selected" : "";

                $html .= "<option value='" . $value . "'" . $selected . " >" . $value . "</option>";
            }
        }

        return $html;

    }

    public function postFilterStatusNew()
    {
        $status = $_REQUEST['status'];
        $import_id = $_REQUEST['drm_import_id'];

        $filter = DB::table('drm_import_filter')->where(['drm_import_id' => $import_id])->first();

        DB::table('drm_import_filter')->updateOrInsert(
            ['drm_import_id' => $import_id],
            ['filter' => $filter->filter, 'blacklist' => (int)$status]
        );

    }

    public function postRemoveFilterNew()
    {
        $import_id = $_REQUEST['drm_import_id'];
        $field = $_REQUEST['field'];
        $value = $_REQUEST['value'];
        $stock_operator = $_REQUEST['operator'];

        $filter = DB::table('drm_import_filter')
            ->select('drm_import_filter.*')
            ->join('drm_imports', 'drm_imports.id', '=', 'drm_import_filter.drm_import_id')
            ->where(['drm_import_id' => $import_id, 'drm_imports.user_id' => CRUDBooster::myParentId()])->first();

        $filters = json_decode($filter->filter ?? '[]', true);

        if (($field == "ean" || $field == "category" || $field == "brand" || $field == "gender" || $field == "materials" || $field == "status") && is_array($filters[$field])) {
            if (($key = array_search($value, $filters[$field])) !== false) {
                unset($filters[$field][$key]);
                if (!count($filters[$field])) {
                    unset($filters[$field]);
                }
            }
        }

        if ($field == "price_below") {
            unset($filters['price_below']);
        }

        if ($field == "price_more_than") {
            unset($filters['price_more_than']);
        }

        if ($field == "stock") {
            if (($key = array_search($stock_operator, $filters['stock_operator'])) !== false) {
                unset($filters['stock'][$key]);
                unset($filters['stock_operator'][$key]);

                // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){
                if($filters['stock']){
                    $filters['stock'] = array_values($filters['stock']);
                }
                if($filters['stock_operator']){
                    $filters['stock_operator'] = array_values($filters['stock_operator']);
                }
                // }

                if (!count($filters['stock'])) {
                    unset($filters['stock']);
                    unset($filters['stock_operator']);
                }
            }
        }
        $json = json_encode($filters);

        DB::table('drm_import_filter')->where('id', $filter->id)->update(['filter' => $json]);
        return response()->json(['status' => "UPDATED"], 200);
    }

    public function postFilterNew()
    {
        $id = $_REQUEST['drm_import_id'];
        $field = $_REQUEST['field'];
        $value = $_REQUEST['value'];

        $filter = DB::table('drm_import_filter')->where('drm_import_id', $id)->first();
        $filter = json_decode($filter->filter, true);

        if ($field != 'price_below' && $field != 'price_more_than') {
            if (is_array($filter[$field])) {
                if (in_array($value, $filter[$field])) {
                    return response('Filter already exists!', 422);
                }

                // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){

                if($_REQUEST['operator'] == '='){
                    foreach($filter['stock_operator'] as $operator){
                        if($operator == '<' || $operator == '>'){
                            return response('Please Remove "<", ">" Filter First to Filter With "=" Operator.', 422);
                        }
                    }
                }else if($_REQUEST['operator'] == '<' || $_REQUEST['operator'] == '>'){
                    foreach($filter['stock_operator'] as $operator){
                        if($operator == '='){
                        return response('Please Remove "=" Filter First to Filter With "<", ">" Operator.', 422);
                        }
                    }
                }

                $operator_exist = false;
                if( $field == 'stock' && in_array($_REQUEST['operator'], $filter['stock_operator']) ){
                    $operator_exist = true;
                    $matched_value_index = array_search($_REQUEST['operator'], $filter['stock_operator']);
                    $filter['stock'][$matched_value_index] = $value;
                }
                // }

            }

            // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){
            if( $field == 'stock' && !$operator_exist ){
                $filter[$field][] = $value;
            }else if($field != 'stock'){
                $filter[$field][] = $value;
            }
            // }else{
            //     $filter[$field][] = $value;
            // }
        } else {
            $filter[$field] = $value;
        }

        // if(isLocal() || in_array(CRUDBooster::myParentId(), [179, 212, 62])){
        if ($field == 'stock') {
            if( is_array($filter['stock_operator']) && !in_array($_REQUEST['operator'], $filter['stock_operator']) ){
                $filter["stock_operator"][] = $_REQUEST['operator'];
            }else if(!is_array($filter['stock_operator'])){
                $filter["stock_operator"][] = $_REQUEST['operator'];
            }
        }
        // }else{
        //     if ($field == 'stock') {
        //         $filter["stock_operator"][] = $_REQUEST['operator'];
        //     }
        // }

        $json = json_encode($filter);

        DB::table('drm_import_filter')->updateOrInsert(
            ['drm_import_id' => $id],
            ['filter' => $json]
        );
    }

    public function postFinishImportNew()
    {
        $import_id = req::input('import_id');

        DrmImport::where(['id' => $import_id])->update([
            'import_finished' => '1'
        ]);
        return [
            'status' => 'success',
            'message' => 'Import finished successfully!'
        ];
    }

    public function getFeedList(Request $request)
    {
        redirectToV2('/suppliers/feeds');

        $currentUserId = CRUDBooster::myParentId();

        if(!$currentUserId){
            return redirect(CRUDBooster::adminPath('login'));
        }

        $data['page_title'] = "New Feed List";

        $all_imports_data = DrmImport::where('user_id', $currentUserId)
                                ->with('supplier', 'country', 'manual_update_mapping')
                                ->withCount('total_product')
                                ->where('import_finished', 1)
                                ->where('csv_file_path', '=', '');

        if( isset($request->filter_select) && !empty($request->filter_select) ){
            $all_imports_data = $all_imports_data->where('delivery_company_id', $request->filter_select);
        }

        if( isset($request->search_by_field_column) && !empty($request->search_by_field_column) ){

            if( isset($request->q) && !empty($request->q) ){
                $all_imports_data = $all_imports_data->where($request->search_by_field_column, 'LIKE', '%' . trim($request->q) . '%');
            }
        }

        $all_imports_data = $all_imports_data->orderBy('id', 'desc');

        if( isset($request->limit) && !empty($request->limit) ){
            $all_imports_data = $all_imports_data->paginate($request->limit);
        }else{
            $all_imports_data = $all_imports_data->paginate($all_imports_data->count());
        }


        $data['user_all_imports'] = $all_imports_data;

        $data['user_supplier'] = DeliveryCompany::where('user_id', $currentUserId)->select('id', 'name')->get();

        return view('admin.drm_import.new.feed_list', $data);
    }

    public function getCustomizeFeed(int $import_id)
    {
        $data = array();
        $data['drm'] = DrmImport::find($import_id);
        return view('admin.drm_import.after_import.customize_feed',$data);
    }

    public function postNewFeedAllowNew()
    {
        $id = $_REQUEST['id'];
        $action = (int)$_REQUEST['action'];
        DB::table('drm_imports')->where('id', $id)->update(['allow_new' => $action]);
    }

    public function setImportTrial($user_id)
    {
        $check_trial = DB::table('app_trials')->where(['user_id' => $user_id, 'app_id' => 0])->count();
        $check_product = DB::table('drm_products')->where('user_id', $user_id)->first();
        if (!$check_trial && $check_product != null) {
            $date = date("Y-m-d");
            DB::table('app_trials')->insert([
                'user_id' => $user_id,
                'app_id' => 0,
                'trial_days' => 14,
                'start_date' => $date
            ]);
        }
    }

    public function postFetchImportedFileNew()
    {

        $imported_id = $_REQUEST['import_id'];

        $imported_csv = FileSource::where('drm_import_id', $imported_id)->select('source_config', 'file_name')->get();
        $imported_csv_file_detail = [];
        $html_table = '
            <table class="table">
            <thead>
                <tr>
                <th>SL</th>
                <th>File Name</th>
                <th>Action</th>
                </tr>
            </thead>

            <tbody>
        ';

        if($imported_csv){
            foreach($imported_csv as $csv_key => $csv_detail){
                $html_table .= '
                <tr>
                <td>'. ($csv_key + 1).'</td>
                <td>'.$csv_detail->file_name.'</td>
                <td><a class="btn btn-xs btn-primary" href="'.Storage::disk('dropmatix')->url($csv_detail->source_config['cloud_path']) . "?download=1".'" target="_blank" title="Download File"><i class="fa fa-download"></i> Download</a></td>
                </tr>';
            }
        }

        $html_table .= '
        </tbody>
        </table>
        ';

        return response()->json(["success" => true, "data" => $html_table]);

    }

    public function getDisableConfirmationNew()
	{
		$id = $_REQUEST['id'];
		$total = DB::table('drm_products')->where('drm_import_id', $id)->count();

		return "<center><h4 style='color:tomato'><strong>" . $total . "</strong> ".__('Products will be Deleted.')." <br /> ".__('Are you sure ?')."</h4></center>";
	}

    public function getDeleteImportNew($id)
    {
        DeleteImportFeed::dispatch($id,CRUDBooster::myParentId());
        CRUDBooster::redirect(route('drm.import.feed.list.new'), __('Import feed deletion process started. Your data will be deleted very soon.'), "success");
    }

    public function getUploadedFilePreview2(Request $request){

        $id = $request->id;
        $import_id = $request->import_id;

        $preview_data = FileSource::where(['id' => $id, 'drm_import_id' => $import_id])->select('header', 'demo_data')->first();

        return response()->json([ 'success' => true, 'file_header' => $preview_data->header, 'file_content' => $preview_data->demo_data ], 200);

    }
}
