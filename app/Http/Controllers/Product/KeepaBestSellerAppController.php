<?php
namespace App\Http\Controllers\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Helper\DrmHelper;
use App\KeepaBestSellerCategories;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use App\Http\Controllers\AdminDrmImportsController;
use Illuminate\Support\Facades\Session;
use Exception;
use Illuminate\Support\Facades\Cache;
use App\Models\Product\KeepaCSVProduct;
use App\AppStore;
use App\MarketplaceProducts;
use App\Jobs\ProcessKeepaCSVProductsColumnUpdateJob;
use App\Services\ProductApi\TransferProduct;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\LazyCollection;

class KeepaBestSellerAppController
{
    public function index(Request $request) {
        $data['categories'] = KeepaBestSellerCategories::get();
        return view('admin.cp_analysis.keepa_best_seller.index', $data);
    }

    public function productView(Request $request){
        $user_id = CRUDBooster::myParentId();
        $my_id = CRUDBooster::myId();

        $interval = $request->get('interval') ?? 30;
        $category = $request->get('category') ?? 61;

        $data['category_transfer'] = DB::table('mp_auto_transfer_selection')->where('user_id', $user_id)->where('category_id', $category)->where('interval', $interval)->first();
        
        $products = KeepaCSVProduct::where('interval', $interval)->where('category_id', $category)->with('mpProducts');

        $search_by_field = $request->get('search_by_field_column');
        $filters = $request->all();

        if(!empty($search_by_field) && !empty($filters['q'])){
          $q = $filters['q'];
          if($search_by_field == 'title' || $search_by_field == 'all'){
              $q = "%$q%";
          }
          if($search_by_field != 'all'){
              $products->where($search_by_field, 'LIKE', $q);
          }else{
              $products->where(function($p) use ($q) {
                  $p->where('ean', 'LIKE', $q);
                  $p->orWhere('asin', 'LIKE', $q);
                  $p->orWhere('title', 'LIKE', "%$q%");
                  $p->orWhere('id', 'LIKE', $q);
              });
          }
        }

        $order_by = null;

        $sort_filter = $request->get('filter_column') ?? [];
        if(!empty($sort_filter))
        {
            $order_by = key($sort_filter);
            $sorting = $sort_filter[$order_by]['sorting'];
        }

        $mp_only = $request->get('mp_only') ?? 0;
        if($mp_only == 1){
            $products->where('mp_available', 1);
        }

        if(!$order_by && !$sorting){
            $order_by = 'id';
            $sorting = 'asc';
        }

        if($order_by && $sorting){
            if($order_by == 'sales_rank')
                $order_by = 'id';
            $products->orderBy($order_by, $sorting);
        }

        $limit = $request->get('limit') ?? 20;

        $data['products'] = $products->paginate($limit);
        $data['total'] = $data['products']->total();

        $category_access = DB::connection('marketplace')->table('marketplace_user_accesses')->where('user_id', $user_id)->first();
        $access_enabled = 0;
        $cat_array = [];
        if($category_access){
            $cat_array = json_decode($category_access->accessable_categories);
            if($cat_array){
                foreach($cat_array as $cat){
                    if($cat == $category){
                        $access_enabled = 1;
                        break;
                    }
                }
            }
        }
        $data['category_access'] = $access_enabled;
        $data['available_mp_categories'] = $cat_array;

        $columns = $this->table_column();
        $data['all_columns'] = $columns;

        $saved_column_collection = DB::table('drm_user_saved_columns')
                            ->where('user_id', $my_id)
                            ->where('table_name','keepa_csv_products')->pluck('columns')->first();
        $saved_column = json_decode($saved_column_collection) ?? array_keys($data['all_columns']);
        $data['saved_columns'] = $saved_column;
        foreach($columns as $field => $$colname){
            if(!in_array($field, $saved_column)){
                unset($columns[$field]);
            }
        }
        $data['columns'] = $columns;
        $data['user_id'] = $user_id;
        
        return view('admin.cp_analysis.keepa_best_seller.product_view.index', $data);
    }

    public function table_column(){
        return [
            'id'                    => ["label" => __('ID') , "sorting" => true],
            'ean'                   => ["label" => 'EAN' , "sorting" => true],
            'asin'                  => ["label" => 'ASIN' , "sorting" => true],
            'image'                 => ["label" => __('Image') , "sorting" => false],
            'title'                 => ["label" => __('Title') , "sorting" => true],
            'price_lowest'          => ["label" => __('Minimum price') , "sorting" => true],
            'price'                 => ["label" => __('Price') , "sorting" => true],
            'price_highest'         => ["label" => __('Maximum price') , "sorting" => true],
            'rating'                => ["label" => __('Rating') , "sorting" => true],
            'rating_count'          => ["label" => __('rating_count') , "sorting" => true],
            'buybox_seller'         => ["label" => __('buybox_seller') , "sorting" => true],
            'sales_rank'            => ["label" => __('sales_rank') , "sorting" => true],
            'sales_rank_drop'       => ["label" => __('sales_rank_drops') , "sorting" => true],
            'category'              => ["label" => __('Category') , "sorting" => true],
            'manufacturer'          => ["label" => __('manufacturer') , "sorting" => true],
        ];
    }

    public function import(Request $request){

        $userId = CRUDBooster::myParentId();

        $data = [];
        if ($request->get('file') && !$request->get('import')) {
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;

            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = [
                'ean',
                'asin',
                'image',
                'title',
                'price_lowest',
                'price',
                'price_highest',
                'rating',
                'rating_count',
                'buybox_seller',
                'sales_rank',
                'sales_rank_drop',
                'amazon_url',
                'category',
                'manufacturer'
            ];

            $labels = [
                0 => 'EAN',
                1 => __('ASIN'),
                2 => __('Image'),
                3 => __('Title'),
                4 => __('Minimum price'),
                5 => __('Price'),
                6 => __('Maximum price'),
                7 => __('Rating'),
                8 => __('rating_count'),
                9 => __('buybox_seller'),
                10 => __('sales_rank'),
                11 => __('sales_rank_drops'),
                12 => __('Amazon URL'),
                13 => __('Category'),
                14 => __('manufacturer')
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        $data['categories'] = KeepaBestSellerCategories::get();
        $data['page_title'] = 'Comparison Analysis Import';
        return view('admin.cp_analysis.keepa_best_seller.admin_csv_upload', $data);
    }

    public function uploadCsv(Request $request){

        if ($request->hasFile('userfile')) {

            try{
                // $request->validate([
                //     'userfile' => 'required|mimetypes:text/csv,text/plain,application/csv,text/comma-separated-values,text/anytext,application/octet-stream,application/txt',
                // ]);

                $extensions = array("xls","xlsx","csv");
                $file = $request->file('userfile');
                $ext = $file->getClientOriginalExtension();
                if(!in_array($ext, $extensions)){
                    $url = route('drm.keepa_bestseller.import');

                    return CRUDBooster::redirect($url, "Please Upload a Valid File");
                } 

                $filePath = 'uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
                Storage::makeDirectory($filePath);

                $filename = md5(Str::random(5)).'.'.$ext;
                $url_filename = '';
                if (Storage::putFileAs($filePath, $file, $filename)) {
                    $url_filename = $filePath.'/'.$filename;
                }
                $url = route('drm.keepa_bestseller.import').'?file='.base64_encode($url_filename).'&category='.$request->get('category-select').'&interval='.$request->get('interval-select');

                return redirect($url);
            } catch(Exception $e){
                $url = route('drm.keepa_bestseller.import');

                return CRUDBooster::redirect($url, "Please Upload a Valid File");
            }
        } else {
            return redirect()->back();
        }
    }

    public function done_import(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Import Products"]);
        Session::put('select_column', $request->get('select_column'));

        return view('admin.cp_analysis.keepa_best_seller.admin_csv_upload', $data);
    }

    public function do_import_chunk(Request $request)
    {
        try{
            $file_md5 = md5($request->get('file'));
            $selected_category = $request->get('category');
            $selected_interval = $request->get('interval');

            if ($request->get('file') && $request->get('resume') == 1 && $request->get('action_type') != 0) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }

                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }

            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);

            $table_columns = [
                'ean',
                'asin',
                'image',
                'title',
                'price_lowest',
                'price',
                'price_highest',
                'rating',
                'rating_count',
                'buybox_seller',
                'sales_rank',
                'sales_rank_drop',
                'amazon_url',
                'category',
                'manufacturer'
            ];

            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $this->csvReader($file, $type, 'auto', false);

            $count = 0;

            $categoryProducts = [];
            foreach ($rows as $row) {
                $count++;
                if($request->get('action_type') != 0){
                    Cache::put('success_' . $file_md5, $count);
                }
                $row = (object) $row;
                foreach ($select_column as $csvColName => $val) {

                    $colname = $table_columns[$csvColName];

                    if($colname == 'ean'){
                        $ean_array = explode(',', $row->$val);

                        $row->$val = trim($row->$val);
                    }

                    else if($colname == 'sales_rank'){
                        $rank_array = explode('|', $row->$val);

                        $row->$val = trim($rank_array[0]);
                    }
                    else if($colname == 'price_lowest' || $colname == 'price' || $colname == 'price_highest'){
                        $row->$val = str_replace('€', '', $row->$val);
                        $row->$val = removeCommaPrice( $row->$val );
                    }

                    $data[$colname] = $row->$val;
                }

                $mp_available = MarketplaceProducts::where('ean', $data['ean'])->exists();

                if($mp_available){
                    $data['mp_available'] = 1;
                }
                else{
                    $data['mp_available'] = 0;
                }

                $data['category_id'] = $selected_category;
                $data['interval'] = $selected_interval;
                $data['deleted_at'] = null;

                try {
                    foreach($ean_array as $ean){
                        if(trim($ean) != ""){
                            $data['ean'] = trim($ean);
                            $categoryProducts[$data['ean']] = $data;
                        }
                    }
                } catch (Exception $e) {
                    $e = (string) $e;
                    Cache::put('error_' . $file_md5, $e, 500);
                }
            }

            KeepaCSVProduct::where('category_id', $selected_category)->where('interval', $selected_interval)->delete();

            $chunks = array_chunk($categoryProducts, 2000);
            foreach ($chunks as $chunk) {
                foreach ($chunk as $data){
                    KeepaCSVProduct::create(
                        $data
                    );
                }
            }

            $this->mpAutoTransferProcess($selected_category, $selected_interval);

            ProcessKeepaCSVProductsColumnUpdateJob::dispatch();
        
            return response()->json(['status' => true, 'message' => $res['message']]);
        }catch (Exception $e) {
            return response()->json(['status' => false, 'message' => "Sorry, something went wrong"]);
        }
    }

    public function activateAutoTransfer(Request $request){
        $user_id = CRUDBooster::myParentId();

        $category_access = DB::connection('marketplace')->table('marketplace_user_accesses')->where('user_id', $user_id)->first();
        $access_enabled = 0;
        if($category_access){
            $cat_array = json_decode($category_access->accessable_categories);
            if($cat_array){
                foreach($cat_array as $cat){
                    if($cat == $category){
                        $access_enabled = 1;
                        break;
                    }
                }
            }
        }

        if($category_access == 0){
            return response()->json([
                'status'      => true,
                'message'     => 'Please Enable Access To The Marketplace Category!',
            ]);
        }

        $category_id = $request->get('category_id');
        $interval = $request->get('interval');
        $activation = $request->get('activation');
        $exists = DB::table('mp_auto_transfer_selection')->where('user_id', $user_id)->where('category_id', $category_id)->where('interval', $interval)->first();
        if($exists){
            DB::table('mp_auto_transfer_selection')->where('id', $exists->id)->update([
                'auto_transfer' => $activation
            ]);
        }
        else{
            DB::table('mp_auto_transfer_selection')->insert([
                'user_id' => $user_id,
                'category_id' => $category_id,
                'interval' => $interval,
                'auto_transfer' => $activation
            ]);
        }

        $this->doMPAutoTransfer($category_id, $interval, $user_id);

        return response()->json([
            'status'      => true,
            'message'     => 'Updated Successfully!',
        ]);
    }

    public function mpAutoTransferProcess($category_id, $interval) {
        $enabled_users = DB::table('mp_auto_transfer_selection')->where('category_id', $category_id)->where('interval', $interval)->where('auto_transfer', 1)->get();
        foreach($enabled_users as $user){
            $this->doMPAutoTransfer($category_id, $interval, $user->user_id);
        }
        return true;
    }

    public function doMPAutoTransfer($category_id, $interval, $user_id) {
        KeepaCSVProduct::where('category_id', $category_id)->where('interval', $interval)->orderBy('id')->with('mpProducts')->chunk(5, function($rows) {
            $productIds = $rows->whereNotNull('mpProducts.id')->pluck('mpProducts.id')->toArray();
            app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)
                            ->transferAllFilteredProductsToDrm($productIds, [], null, $user_id);
        });
        return true;
    }

    public function mpAutoTransfer() {
        DB::statement('CREATE TABLE keepa_best_seller_categories LIKE app_stores');
        DB::statement('INSERT IGNORE INTO keepa_best_seller_categories SELECT * FROM app_stores WHERE `id` > 60 AND `id` < 91');
        AppStore::where('id', '>', 60)->where('id', '<', 91)->delete();
    }

    public function saveColumns(Request $request){
        $user_id = CRUDBooster::myId();
        DB::table('drm_user_saved_columns')
            ->updateOrInsert(
                ['user_id' => $user_id, 'table_name' => 'keepa_csv_products'],
                [
                    'columns' => json_encode($request->saved_columns),
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        return back();
    }

    public function transferToAnalysis(Request $request){
        abort(404);

        
        $user_id = CRUDBooster::myParentId();

        $checked_all = $request->get('checked_all');
        $product_ids = $request->get('product_ids');
        if($checked_all == 'true'){
            $params = $request->get('params');
            $product_ids = $this->getSelectedIds($params);
        }
        $chunks = array_chunk($product_ids, 500);
        foreach ($chunks as $chunk) {
            app(TransferProduct::class)->keepaProductsTransferToAnalysis($user_id, $chunk);
        }

        return response()->json([
            'status' => true,
            'message' => 'Product transfer successfully!'
        ]);
    }

    public function getSelectedIds($params){
        $ids = KeepaCSVProduct::where('interval', $params['interval'])->where('category_id', $params['category']);
        if($params['mp_only'] == 1){
            $ids->where('mp_available', 1);
        }

        $search_by_field = $params['search_by_field_column'];

        if(!empty($search_by_field) && !empty($filters['q'])){
          $q = $params['q'];
          if($search_by_field == 'title' || $search_by_field == 'all'){
              $q = "%$q%";
          }
          if($search_by_field != 'all'){
              $products->where($search_by_field, 'LIKE', $q);
          }else{
              $products->where(function($p) use ($q) {
                  $p->where('ean', 'LIKE', $q);
                  $p->orWhere('asin', 'LIKE', $q);
                  $p->orWhere('title', 'LIKE', "%$q%");
                  $p->orWhere('id', 'LIKE', $q);
              });
          }
        }
        return $ids->pluck('id')->toArray();
    }

    public function keepaMpAvailableColumnManualUpdate() {
        ProcessKeepaCSVProductsColumnUpdateJob::dispatchNow();
    }

    public function categoryNameUpdateInDB() {
        KeepaBestSellerCategories::where('id', 72)->update([
            'app_name_de' => "Elektro-Großgeräte"
        ]);
        KeepaBestSellerCategories::where('id', 82)->update([
            'app_name_de' => "Lebensmittel & Getränke"
        ]);
    }

    public function csvReader($csv, $type, $delimiter, $deleteFile = true)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $paths = explode(';', $csv);
        $key = null;
        $key_count = 0;
        $array = array();
        $rand = Str::random(40);
        foreach ($paths as $path) {
            if ($deleteFile) {
                $path = Storage::disk('spaces')->url($path);
                $file_type = pathinfo($path, PATHINFO_EXTENSION);
                $file = file_get_contents($path);
                file_put_contents($rand . '.' . $file_type, $file);
                $localpath = $rand . '.' . $file_type;
            } else {
                $localpath = $path;
            }
            if ($type == 'csv' || $type == 'txt') {
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');

                if ($delimiter != 'auto') {
                    $reader->setDelimiter($delimiter);
                }
                $spreadsheet = $reader->load($localpath);
            } else {
                $spreadsheet = IOFactory::load($localpath);
            }

            // Convert all cell values to string
            $spreadsheet->getActiveSheet()
                ->getStyle('A1:'.$spreadsheet->getActiveSheet()->getHighestDataColumn().$spreadsheet->getActiveSheet()->getHighestDataRow())
                ->getNumberFormat()
                ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);

            $spreadsheetArray = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
            $collection = LazyCollection::make($spreadsheetArray);

            if ($key == null) {
                $key = array_map('trim', $collection->first());
                $key_count = count($key);
            }
            $key = array_map('removeDots', $key);
            $collection = $collection->except(1);
            foreach ($collection as $row) {
                if (count($row) == $key_count && !containsOnlyNull($row)) {
                    $array[] = array_combine($key, $row);
                }

            }

            if (!pathIsUrl($path) && $deleteFile) {
                unlink($localpath);
            }
        }
        return $array;
    }
    
    public function transferSelectedToStock(Request $request){
        try{
            $user_id = CRUDBooster::myParentId();

            $checked_all = $request->get('checked_all');
            $product_ids = $request->get('product_ids');
            if($checked_all == 'true'){
                $params = $request->get('params');
                $product_ids = $this->getSelectedIds($params);
            }
            $chunks = array_chunk($product_ids, 500);
            foreach ($chunks as $chunk) {
                $products = KeepaCSVProduct::whereIn('id', $chunk)->get();
                $mp_ids = $products->whereNotNull('mpProducts.id')->pluck('mpProducts.id')->toArray();
                app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)
                                ->transferAllFilteredProductsToDrm($mp_ids, [], null, $user_id);
            }

            return response()->json([
                'status' => true,
                'message' => 'Available products transferred successfully!'
            ]);
        } catch(Exception $e){
            dd($e);
            return response()->json([
                'status' => false,
                'message' => 'Something Went Wrong!'
            ]);
        }
    }
}