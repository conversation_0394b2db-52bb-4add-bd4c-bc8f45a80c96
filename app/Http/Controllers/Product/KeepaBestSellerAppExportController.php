<?php
namespace App\Http\Controllers\Product;
use Illuminate\Http\Request;
use App\Models\Product\ComparisonAnalysisProduct;
use App\Shop;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;


use League\Csv\Writer;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

use App\User;

use App\Notifications\DRMNotification;
use Storage;
use App\Jobs\KeepaBestSellerExportJob;

class KeepaBestSellerAppExportController
{

    public function export(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $my_id = CRUDBooster::myId();
        $isCheckAll = $request->isCheckAll === 'true';
        $products_id = $request->product_id ? explode(',', $request->product_id) : [];
        $file_ext = $request->file_ext;
        $search_by_field_column = $request->search_by_field_column;
        $q = $request->q;
        $category = $request->category;
        $interval = $request->interval;
        $mp_only = $request->mp_only;

        $saved_column_collection = DB::table('drm_user_saved_columns')
                            ->where('user_id', $my_id)
                            ->where('table_name','keepa_csv_products')->pluck('columns')->first();

        $saved_column_collection = collect($saved_column_collection)->map(function($item) {
                return strtolower($item);
            })
            ->first();

        $job_payload = [
            'user_id' => $user_id,
            'checked_all' => $isCheckAll,
            'ids' => $products_id,
            'ext' => $file_ext,
            'search_by_field_column' => $search_by_field_column,
            'q' => $q,
            'category' => $category,
            'interval' => $interval,
            'mp_only' => $mp_only,
            'cols' => $saved_column_collection,
            'created_at' => now(),
            'updated_at' => now(),
        ];

        $payload_id = DB::table('keepa_bestseller_export')->insertGetId([
            'user_id' => $user_id,
            'payload' => json_encode($job_payload),
        ]);

        KeepaBestSellerExportJob::dispatchNow($payload_id);

        return response()->json([
            'success' => true,
            'message' => __('Process running on background'),
        ]);
    }
}
