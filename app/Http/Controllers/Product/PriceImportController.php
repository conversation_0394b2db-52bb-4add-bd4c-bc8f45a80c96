<?php

namespace App\Http\Controllers\Product;

use App\Http\Controllers\AdminDrmImportsController;
use App\Http\Controllers\Controller;
use App\Models\ChannelProduct;
use App\Services\ChannelProductService;
use App\Services\PriceImportService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Predis\Connection\ConnectionException;
use Illuminate\Support\Facades\Validator;

class PriceImportController extends Controller
{
    private PriceImportService $service;

    public function __construct(PriceImportService $service)
    {
        $this->service = $service;
    }
    
    public function uploadFile(Request $request)
    {
        $file = $request->file;
        $extension = pathinfo($file->getClientOriginalName(), PATHINFO_EXTENSION);
        $rand = Str::random(40);
        $file_name = 'csv/' . $rand . '.' . $extension;
        file_put_contents($file_name, file_get_contents($file->getRealPath()));
        $headers = $this->getHeaders($file_name, $request->delimiter, $extension);
        return response(['headers' => $headers]);
    }

    public function getHeaders($file_name, $delimiter, $extension): array
    {
        try {
            if ($extension == 'csv' || $extension == 'txt') {
                $reader = IOFactory::createReader('Csv');
                if ($delimiter != 'auto') {
                    $reader->setDelimiter($delimiter);
                }
                $spreadsheet = $reader->load($file_name);
            } else {
                $spreadsheet = IOFactory::load($file_name);
            }
            $spreadsheet = $spreadsheet->getActiveSheet()->toArray();

            unlink($file_name);

            $total = count($spreadsheet);

            $collection = array();
            for ($i = 0; $i <= 5; $i++) {
                if ($total >= $i + 1) {
                    $collection[$i] = $spreadsheet[$i];
                }
            }

            if (app(AdminDrmImportsController::class)->validateFile($collection)) {
                $headers = makeArrayUtf8(makeArrayUtf8($spreadsheet[0]));
                try {
                    $redis = Redis::connection();
                    $redis->set('price_import_csv_' . CRUDBooster::myParentId(), json_encode($spreadsheet));
                } catch (ConnectionException $e) {
                }
                return array_map('removeDots', $headers);
            }

            return array();

        } catch (\Exception $e) {
            return array();
        }
    }

    public function getSpreadsheet(): \Illuminate\Support\Collection
    {
        $redis = Redis::connection();
        $json = $redis->get('price_import_csv_' . CRUDBooster::myParentId());
        $sheet = $json ? json_decode($json,true) : array();
        return collect($this->combine($sheet));
    }

    public function combine($array): array
    {
        $key = $array[0];
        unset($array[0]);

        array_walk($array, function (&$item) use($key){
            if(!containsOnlyNull($item)){
                $item = array_combine($key, $item);
                $item = makeArrayUtf8(makeArrayUtf8($item));
            }
            else {
                $item = null;
            }
        });
        return array_filter($array);
    }

    public function submitFields(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $product_ids = $request->product_ids;

        if($request->selected_all == 1){
            $product_ids = app(ChannelProductService::class)->getSelectedIds($user_id,$request->params);
        }

        $products = ChannelProduct::find($product_ids);
        $sheet = $this->getSpreadsheet();

        foreach ($products as $product)
        {
            $sheet_product = $sheet->where($request->ean_field,$product->ean)->first();
            if($sheet_product){
                $product->vk_price = $sheet_product[$request->price_field] ?? $product->vk_price;
            }
        }

        return view('channel_products.modals.import_price_review',['products' => $products]);
    }

    /**
     * @throws \Exception
     */
    public function submitPrices(Request $request): \Illuminate\Http\RedirectResponse
    {
        $prices = $request->vk_price;
        $user_id = CRUDBooster::myParentId();

        foreach ($prices as $key => $price)
        {
            $channel_product = ChannelProduct::where([
                'user_id' => $user_id,
                'id' => $key
            ])->first();

            if($channel_product)
            {
                $vk_price = deNumberFormatterAuto($price);
                if($vk_price){
                    app(ChannelProductService::class)->update($key,['vk_price' => $vk_price],true);
                }
            }
        }

        return redirect()->back();
    }

    public function minMaxUploadFile(Request $request)
    {
        $data = [];
        $data['headers'] = '';

        if ($request->hasFile('file')) {
            $file = $request->file('file');
            $ext = $file->getClientOriginalExtension();

            $validator = Validator::make([
                'extension' => $ext,
            ], [
                'extension' => 'in:xls,xlsx,csv,XLS,XLSX,CSV',
            ]);

            if ($validator->fails()) {
                $message = $validator->errors()->all();

                // todo
                return redirect()->back()->with(['message' => implode('<br/>', $message), 'message_type' => 'warning']);
            }

            //Create Directory Monthly
            $filePath = 'min_max_price_uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
            Storage::makeDirectory($filePath);

            //Move file to storage
            $filename = md5(Str::random(5)).'.'.$ext;
            $url_filename = '';
            if (Storage::putFileAs($filePath, $file, $filename)) {
                $url_filename = $filePath.'/'.$filename;

                $file = $url_filename;
                $file = storage_path('app/' . $file);
                $type = pathinfo($file, PATHINFO_EXTENSION);
                $rows = app('App\Http\Controllers\AdminDrmImportsController')->csvToArray($file, $type, 'auto', false);
                
                $mapping_columns = [];
                if (is_array($rows) && !empty($rows)) {
                    foreach ($rows[0] as $row_key => $row_val) {
                        // if ($row_key != 'EK Preis') 
                        if (!(strpos(strtolower($row_key), 'ek') !== false) && !(strpos(strtolower($row_key), 'freight') !== false)) { // ignore ek preis, freight 
                            $mapping_columns[] = $row_key;
                        }
                    }
                }
                $data['headers'] = $mapping_columns;
            }

            $data['url_filename'] = $url_filename;

            return response()->json([
                'success' => true,
                'data'    => $data,
            ], 200);
        } else {
            return redirect()->back();
        }
    }
    
    public function submitMinMaxFields(Request $request)
    {
        try{
            $status  = false;
            $data    = [];
            $message = [];
            
            $user_id    = CRUDBooster::myParentId();
            $shop_id    = $request->params['shop'];
            $channel_id = $request->channel_id;

            $file = $request->url_filename;
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);
            $rows = (new AdminDrmImportsController)->csvToArray($file, $type, 'auto', false);

            $data['import_statistics'] = '';
            try {
                if (count($rows) == 0) {
                    $message[] = __("There are no products in this file! Please check again.");
                } else {
                    $valid_products = $invalid_eans = $inserted_products = $not_in_channel = [];

                    foreach ($rows as $index => $row) {
                        $row_valid     = true;

                        $ean_value = $row[$request->min_max_ean_field];
                        $min_value = $row[$request->min_price_field];
                        $max_value = $row[$request->max_price_field];

                        if (empty($ean_value) ) {
                            $row_valid = false;
                            if (!isset($message['ean_empty'])) {
                                $message['ean_empty'] = __("EAN field is empty in some products.");
                            }
                        } else if (strlen($ean_value) != 13) {
                            $row_valid = false;
                            if (!isset($message['ean_invalid'])) {
                                $message['ean_invalid'] = __("EAN field is wrongly formatted in some products.");
                            }
                        }

                        if (empty($min_value)) {
                            $row_valid = false;
                            if (!isset($message['min_empty'])) {
                                $message['min_empty'] = __("Min price is empty in some products.");
                            }
                        } else if (!is_numeric($min_value)) {
                            $row_valid = false;
                            if (!isset($message['min_invalid'])) {
                                $message['min_invalid'] = __("Min price is wrongly formatted in some products.");
                            }
                        }

                        if (empty($max_value)) {
                            $row_valid = false;
                            if (!isset($message['max_empty'])) {
                                $message['max_empty'] = __("Max price is empty in some products.");
                            }
                        } else if (!empty($max_value) && !is_numeric($max_value)) {
                            $row_valid = false;
                            if (!isset($message['max_invalid'])) {
                                $message['max_invalid'] = __("Max price is wrongly formatted in some products.");
                            }
                        }
        
                        if ($row_valid) {
                            $valid_products[] = [
                                'ean'       => $ean_value,
                                'min_price' => $min_value,
                                'max_price' => $max_value,
                            ];
                        } else {
                            if (!empty($ean_value)) {
                                $invalid_eans[]   = $ean_value;
                            }
                        }
                    }
    
                    if (empty($valid_products)) {
                        $message[] = __("No product is updated. All products are either empty or wrongly formatted.");
                    } else {
                        $status = true;
                        if (!empty($invalid_eans)) {
                            $message['eans_invalid'] = '<br>' . __("Following products are failed to import: ") . '<br>' . Str::limit(join(' | ', $invalid_eans), 200);
                        }

                        collect($valid_products)
                        ->chunk(500)
                        ->each(function($product_chunk) use($user_id, $shop_id, $channel_id, &$inserted_products, &$not_in_channel) {
                            $product_chunk = $product_chunk->toArray();
    
                            foreach($product_chunk as $index => $product_data) {
                                $trimmed_ean = trim($product_data['ean']);
                                $inserted = ChannelProduct::where([
                                    'ean'     => $trimmed_ean,
                                    'user_id' => $user_id,
                                    'shop_id' => $shop_id,
                                    'channel' => $channel_id,
                                ])
                                ->update([
                                    'min_price' => deNumberFormatterAuto($product_data['min_price']),
                                    'max_price' => deNumberFormatterAuto($product_data['max_price']),
                                ]);
                               
                                if ($inserted) {
                                    $inserted_products[] = $trimmed_ean;
                                } else {
                                    $not_in_channel[]    = $trimmed_ean;
                                }
                            }
                        });
                    }

                    $import_statistics = [
                        __('Imported: ') . count($inserted_products),
                        __('Invalid: ') . count($invalid_eans),
                        __('Not in Channel: ') . count($not_in_channel),
                    ];
    
                    $data['import_statistics'] = __('File Products: ') . count($rows) . '<br>' . join(', ', $import_statistics);
                }

                return response()->json([
                    'status'  => $status, 
                    'data'    => $data, 
                    'message' => $message
                ]);
            } catch(\Exception $e) {
                $e = (string) $e;
                return response()->json(['status' => false, 'message' => $e]);
            }
         }catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => __("Sorry, something went wrong")]);
        }
    }
}
