<?php

namespace App\Http\Controllers\Product;

use App\Enums\Apps;
use App\Enums\Channel;
use App\Http\Controllers\Controller;
use App\Jobs\UniversalExport\SyncDtFeed;
use App\Jobs\UniversalExport\SyncFeed;
use App\Models\ChannelProduct;
use App\Models\Export\UniversalExport;
use App\Services\AppStoreService;
use App\Services\UniversalExportService;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UniversalExportController extends Controller
{
    private UniversalExportService $service;

    private int $module_id;

    public function __construct(UniversalExportService $service)
    {
        $this->service = $service;
        $this->module_id = config('modules.list.drm_products');
    }

    public function create(Request $request): JsonResponse
    {
        if(!$this->canAccess('universal_export')){
            return response()->json(['success' => false, 'message' => 'You do not have permissions !','close' => true]);
        }

        $user_id = CRUDBooster::myParentId();
        $product_ids = explode(',',$request->product_ids);

        if((int)$request->selected_all){
            if($request->shop_id){
                $product_ids = app('App\Services\ChannelProductService')->getSelectedIds($user_id,json_decode($request->params,true));
            }else{
                $product_ids = app('App\Services\DRMProductService')->getSelectedIds($user_id,json_decode($request->params,true));
            }
        }

        $form_data = $request->all();
        unset($form_data['product_ids']);

        $lang= app('App\Services\UserService')->getLang($user_id);

        $res = $this->service->create($user_id, array_merge(['lang' => $lang,'product_ids' => $product_ids],$form_data));
        return response()->json(['success' => $res['success'], 'deluxeOrHigher' => $res['deluxeOrHigher'], 'message' => $res['message'],'limit' => $res['limit'], 'close' => $res['success']]);

    }

    public function dtCreate(Request $request): JsonResponse
    {
        if(!$this->canAccess('universal_export')){
            return response()->json(['success' => false, 'message' => 'You do not have permissions !','close' => true]);
        }
        $user_id = CRUDBooster::myParentId();

        $product_ids = explode(',',$request->product_ids);

        if((int)$request->selected_all){
            $product_ids = app('App\Services\ChannelProductService')->getSelectedIds($user_id,json_decode($request->params,true));
        }
        $form_data = $request->all();
        unset($form_data['product_ids']);

        $lang= app('App\Services\UserService')->getLang($user_id);
        $ids = $this->service->checkConnectedProducts($user_id,$product_ids);

        if(empty($ids))
        {
            return response()->json(['success' => false, 'message' => 'No products are connected !','close' => true]);
        }
//        $exists = $this->service->getDtFeedByName($user_id,$request->feed_name);
//        if(!$exists){
            $this->service->dtCreate($user_id, array_merge(['lang' => $lang,'product_ids' => $ids],$form_data));
            return response()->json(['success' => true, 'message' => __('Products exported as CSV successfully'), 'close' => true]);
//        }
//        else{
//            return response()->json(['success' => false, 'message' => 'Feed name already exists !', 'close' => false]);
//        }
    }

    public function update(Request $request)
    {
        if(!$this->canAccess('universal_export')){
            return redirect()->back()->withErrors(['You do not have permissions !']);
        }
        $user_id = CRUDBooster::myParentId();
//        $exists = $this->service->getFeedByName($user_id,$request->feed_name);
//        if(!$exists){
            $this->service->update($user_id, $request->all());
            return redirect()->back()->withSuccess(['Feed information updated successfully']);
//        }
//        else{
//            return redirect()->back()->withError(['Feed Name Already exists !']);
//        }
    }

    public function dtUpdate(Request $request)
    {
        if(!$this->canAccess('universal_export')){
            return redirect()->back()->withErrors(['You do not have permissions !']);
        }
        $user_id = CRUDBooster::myParentId();
//        $exists = $this->service->getDtFeedByName($user_id,$request->feed_name);
//        if(!$exists){
            $this->service->dtUpdate($user_id, $request->all());
            return redirect()->back()->withSuccess(['Feed information updated successfully']);
//        }
//        else{
//            return redirect()->back()->withError(['Feed Name Already exists !']);
//        }
    }

    public function getFeeds(Request $request): JsonResponse
    {
        $user_id = CRUDBooster::myParentId();
        $lang= app('App\Services\UserService')->getLang($user_id);
        $results = $this->service->getFeeds($user_id,$lang,$request->shop);
        return response()->json(['success' => true, 'data' => $results]);
    }

    public function getDtFeeds(Request $request): JsonResponse
    {
        $user_id = CRUDBooster::myParentId();
        $lang= app('App\Services\UserService')->getLang($user_id);
        $results = $this->service->getDtFeeds($user_id,$request->shop,$lang);
        return response()->json(['success' => true, 'data' => $results]);
    }

    public function getFeed(Request $request): JsonResponse
    {
        $user_id = CRUDBooster::myParentId();
        $result = $this->service->getFeed($user_id, $request->feed_id);
        if($result->feed_url){
            $result->feed_url = Storage::disk('spaces')->url($result->feed_url);
        }
        return response()->json(['success' => true, 'data' => $result]);
    }

    public function getDtFeed(Request $request): JsonResponse
    {
        $user_id = CRUDBooster::myParentId();
        if((int)$request->copyFromGoogle){
            $result = $this->service->getGoogleTemplate();
        }else{
            $result = $this->service->getDtFeed($user_id, $request->feed_id);
        }

        if($result->feed_url){
            $result->feed_url = Storage::disk('spaces')->url($result->feed_url);
        }
        return response()->json(['success' => true, 'data' => $result]);
    }

    public function checkConnected(Request $request): JsonResponse
    {
        $user_id = CRUDBooster::myParentId();
        $ids = $this->service->checkConnectedProducts($user_id,$request->product_ids);
        return response()->json([
            'success' => !empty($ids),
            'message' => empty($ids)?'No products are connected !':count($ids)." Products selected"
        ]);
    }

    public function index(Request $request)
    {
        if(!$this->canAccess('universal_export')){
            return redirect(route('drm.product.index'))->withErrors(['You do not have permissions !']);
        }

        $data['page_title'] = __("Universal Export");
        $user_id = CRUDBooster::myParentId();

        if(!deluxeOrHigher($user_id)){
            $purchased = app(AppStoreService::class)->checkAppPurchased(Apps::UNIVERSAL_EXPORT,$user_id);
        }else{
            $purchased = true;
        }

        if($purchased){
            $data['interval'] = DRM_Inverval_App_Minute($user_id, config('global.csv_interval_app_id'));
            $lang = app('App\Services\UserService')->getLang($user_id);
            $data['feeds'] = $this->service->getFeeds($user_id,$lang,$request->shop);
            return view('admin.drm_products.universal_export.universal_export', $data);
        }
        else{
            if($request->shop){
                $returnUrl = route('channel.product.index',['shop' => $request->shop]);
            }else{
                $returnUrl = route('drm.product.index');
            }
            return redirect($returnUrl)->withErrors(['Please purchase the app !']);
        }
    }

    public function dtIndex(Request $request)
    {
        if(!$this->canAccess('universal_export')){
            return redirect(route('drm.product.index'))->withErrors(['You do not have permissions !']);
        }
        $data['page_title'] = __("Droptienda feed export");
        $data['shop_id'] = $request->shop;
        $user_id = CRUDBooster::myParentId();
        $lang= app('App\Services\UserService')->getLang($user_id);
        $data['feeds'] = $this->service->getDtFeeds($user_id,$request->shop,$lang);
        $data['automatic_access'] = app(AppStoreService::class)->checkAppPurchased(Apps::DT_AUTO_EXPORT, $user_id);
        $data['interval'] = DRM_Inverval_App_Minute($user_id, config('global.csv_interval_app_id'));
        $data['connected_products'] = ChannelProduct::where([
            'user_id' => $user_id,
            'channel' => Channel::DROPTIENDA,
            'is_connected' => true
        ])->count();
        return view('channel_products.dt_universal_export.universal_export', $data);
    }

    public function delete($id)
    {
        if(!$this->canAccess('universal_export')){
            return redirect(route('universal.export.index'))->withErrors([__('You do not have permissions !')]);
        }
        $user_id = CRUDBooster::myParentId();
        try {
            $this->service->delete($user_id,$id);
        }catch (\Exception $e){}
        return redirect(route('universal.export.index'))->withSuccess([__('Feed deleted successfully')]);
    }

    public function dtDelete($id)
    {
        if(!$this->canAccess('universal_export')){
            return redirect(route('universal.export.dt.index'))->withErrors([__('You do not have permissions !')]);
        }
        $user_id = CRUDBooster::myParentId();
        try {
            $res = $this->service->dtDelete($user_id,$id);
        }catch (\Exception $e){}
        if($res){
            return redirect($_SERVER['HTTP_REFERER'])->withSuccess([__('Feed deleted successfully')]);
        }else{
            return redirect($_SERVER['HTTP_REFERER'])->withErrors([__('You cannot delete this feed !')]);
        }

    }

    public function syncFeed(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        SyncFeed::dispatchNow($user_id,$request->feed_id);
    }

    public function syncDtFeed(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        SyncDtFeed::dispatchNow($user_id,$request->feed_id);
//        SyncDtFeed::dispatch($user_id,$request->feed_id)->onQueue('universal_exports');
    }

    public function setAutoUpdate(Request $request)
    {
        $user_id = CRUDBooster::myParentId();
        $automatic_access = app(AppStoreService::class)->checkAppPurchased(Apps::DT_AUTO_EXPORT, $user_id);

        if(!$automatic_access){
            return response("",402);
        }
        UniversalExport::where([
            'id' => $request->feed_id,
            'user_id' => $user_id,
            'dt_feed' => true,
        ])->update(['is_automatic_feed' => (int)$request->trigger]);
        return response("",204);
    }


    public function getPurchasedPlan()
    {
        $user_id = CRUDBooster::myParentId();
        $limit = $this->service->getLimit($user_id);
        dd($limit);
    }

    protected function canAccess($feature): bool
    {
        return can_access($feature,$this->module_id);
    }
}
