<?php

namespace App\Http\Controllers;

use Google\Cloud\Translate\V2\TranslateClient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\User;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Mail\AppPurchaseConfirmation;
use Illuminate\Support\Facades\Mail;
use App\DrmProduct;
use App\Models\DrmCategory;
use App\Services\DRM\DRMService;

class ProductTranslateController extends Controller
{
    public function getLanguage()
    {
        $product_id = request()->product_id;
        $lang = request()->lang;
        $results = DB::table('countries')->where('language_shortcode', '!=', $lang)->get();
        return view('app_store.language_modal', compact('results', 'lang', 'product_id'));
    }

    public function getImportLanguage()
    {
        $import_id = request()->import_id;
        $import = DB::table('drm_imports')->where('id', $import_id)->first();
        $lang = DB::table('countries')->where('id', $import->country_id)->first()->language_shortcode;
        $product_id = DB::table('drm_products')->where('drm_import_id', $import_id)->pluck('id')->toArray();
        $results = DB::table('countries')->where('language_shortcode', '!=', $lang)->get();
        return view('app_store.language_modal', compact('results', 'lang', 'product_id'));
    }

    public function getProduct(Request $request)
    {
        $free_transfer = ['de']; # list of free transfer language
        $product_id = $request->product_id;
        $language_id = $request->language_id;
        $source = $request->lang;
        $lang = $source;
        $user = User::find(CRUDBooster::myParentId());
        $results = DrmProduct::find($request->product_id);

        $languages = DB::table('countries')->whereIn('id', $language_id)->get();
        $all_lang = $languages->pluck('language_shortcode')->toArray(); // Frre all

        $free_lang_codes = [];
        if( in_array($source, $free_transfer) ){
            foreach ($languages as $key => $objLang) {
                if( in_array($objLang->language_shortcode, $free_transfer) ){
                    $free_lang_codes[] = $objLang->language_shortcode;
                    unset($languages[$key]);
                }
            }
        }

        // FREE TRANSLATION
        if(\CRUDBooster::myParentId() == 2698)
        {
            $free_lang_codes = $all_lang;
            $languages = [];
        }

        if(!count($languages)){
            # no paid translate required
            foreach ($free_lang_codes as $key => $free_target_lang) {
                $this->appTranslateEngine($product_id, $source, $free_target_lang, true);
            }
            return [
                'action' => 'swal',
                'status' => 'success',
                'message' => __('We are translating your languages.')
            ];
        }

        $title = '';
        $cat = '';
        $siz = '';
        $weight = '';
        $brand = '';
        $material = '';
        $production_year = '';
        $des = '';
        $color = '';
        $total_text = '';
        $total_text_with_cat = '';

        $category_name = [];

        foreach ($results as $key => $result) {
            $title .= $result->title[$lang];
            $des .= $result->description[$lang];
            // $cat .="Test Category";
            foreach ($result->drm_categories as $category){
                $category_name[] = $category->drm_category->{'category_name_'.$lang};
            }
            // $color .=$result->item_color;
            // $siz .=$result->item_size;
            // $weight .=$result->item_weight;
            // $brand .=$result->brand;
            // $material .=$result->materials;
            // $production_year .=$result->production_year;
        }
        $category_name = array_unique($category_name);
        $cat = implode(' ', $category_name);

        $total_text .= $title . ' ' . $des . ' ' . $color . ' ' . $production_year . ' ' . $material . ' ' . $brand . ' ' . $weight . ' ' . $siz . ' ';
        $total_text_with_cat .= $title . ' ' . $des . ' ' . $color . ' ' . $production_year . ' ' . $material . ' ' . $brand . ' ' . $weight . ' ' . $siz . ' ' . $cat . ' ';

        $total_language = count($request->language_id);
        $count = str_word_count($total_text);
        $count_with_cat = str_word_count($total_text_with_cat);

        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }

        if(checkTariffEligibility($user->id) || in_array($user->id, config('global.free_product_translate_users') ?? [])){
            return view('app_store.text_modal_v1_new_users', compact('results', 'count', 'count_with_cat', 'total_language', 'language_id', 'product_id', 'languages', 'source', 'user', 'term', 'lang', 'free_lang_codes'));
        }

        $hasPaypal = app(DRMService::class)->paypalCheck(User::DROPMATIX_ACCOUNT_ID);

        return view('app_store.text_modal_v1', compact('results', 'count', 'count_with_cat', 'total_language', 'language_id', 'product_id', 'languages', 'source', 'user', 'term', 'lang', 'free_lang_codes', 'hasPaypal'));
    }


    //Translate product SCA -> after payment hook
    public function productTranslationSCA($purchase_data)
    {
        // operation  start
        //DB::beginTransaction();
        try {
            $user = User::with('billing_detail')->find($purchase_data['user_id']);
            if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);

            $languageIds = explode(',', $purchase_data['language_id']);
            $languageId = array_map('intval', array_map('trim', $languageIds));

            $productIds = explode(',', $purchase_data['product_id']);
            $productId = array_map('intval', array_map('trim', $productIds));

            //Source language
            $countryName = DB::table('countries')->where('language_shortcode', '=', $purchase_data['source'])->select('name')->first();
            $countryNameShow = $countryName->name;

            $translateProductID = $purchase_data['product_id']; //Product id string
            $translateProductLangID = array();

            foreach ($languageId as $langID) {
                $toTranslateLangName = DB::table('countries')->where('id', $langID)->select('name')->first();
                $translateProductLangID[] = $toTranslateLangName->name;
            }

            $translateProductLangIDshow = implode(",", $translateProductLangID);

            //Erros
            $error_count = 0;

            $languages = DB::table('countries')->whereIn('id', $languageId)->get();
            foreach ($languages as $key => $value) {
                try {
                    $translate_cat = $purchase_data['translate_category'] == 'yes';
                    $this->appTranslateEngine($productId, $purchase_data['source'], $value->language_shortcode, $translate_cat);
                } catch (\Exception $ex) {
                    $error_count++;
                }
            }

            //if($user->id <= config('global.greater_user_id')){
            if(!checkTariffEligibility($user->id)){

                //Increment single pay coupon usages
                if (isset($purchase_data['coupon']) && $purchase_data['coupon']) {
                    DB::table('coupons')->where('coupon_id', $purchase_data['coupon'])->increment('single_pay');
                }

                $discount = $purchase_data['discount'] ?? 0;
                $total = $purchase_data['total'] ?? 0;
                $sub_total = $purchase_data['sub_total'] ?? 0;

                $payment_intend_id = $purchase_data['intend_id'] ?? null;
                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($total * $taxShow) / 100;
                $order_info = [
                    'user_id' => 2455,  //STRIPE_CLIENT
                    'cms_client'  => $user->id,
                    'order_date'    => date('Y-m-d H:i:s'),
                    'total' => round(($total), 2),
                    'sub_total' => round($sub_total, 2),
                    'discount' => round($discount, 2),
                    'discount_type' => 'fixed',
                    'total_tax' => 0,
                    'payment_type'  => "Stripe Card",
                    'status'    => "paid",
                    'currency'  => "EUR",
                    'adjustment'    => 0,
                    'insert_type'   => \App\Enums\InsertType::TRANSLATE,
                    'shop_id'       => 8,
                    'order_id_api'  => $purchase_data['id'],
                    'intend_id' => $payment_intend_id,
                ];

                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'Product Translate ID  ' . preg_replace('/\,/', ', ', $translateProductID) . '.');
                $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT', "Product Translate from $countryNameShow to $translateProductLangIDshow.");
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($sub_total, 2);
                $cart_item['tax'] = $taxShow;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($sub_total, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);

                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $user->id);

                try {
                    $postdata = [
                        'app_name' => 'Product Translate',
                        'price' => $total,
                        'subject' => 'Product Translate confirmation By DRM',
                    ];

                    // if(isLocal() || in_array($user->id, [212, 2592])){
                        $emails = json_decode($user->billing_detail->billing_emails, true) ?? [ $user->billing_detail->email ];
                        foreach ($emails as $key => $email){
                            app('drm.mailer')->getMailer()->to($email)->send(new AppPurchaseConfirmation($postdata));
                        }
                    // }else{
                    // app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new AppPurchaseConfirmation($postdata));
                    // }
                } catch (\Exception $ee) {

                }
            }

            $info_message = ($error_count > 0) ? 'Unfortunately only ' . $error_count . ' product are not translated properly dut to your content.' : 'Successfully Translated Product';
            // operation  end

            //DB::commit();    // Commiting  ==> There is no problem whatsoever

            return ['success' => true, 'message' => $info_message, 'return_url' => CRUDBooster::adminPath('drm_products')];
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong
            return ['success' => false, 'message' => $e->getMessage(), 'return_url' => CRUDBooster::adminPath('drm_products')];
        }
    }


    //Google translator engine
    // public function appTranslateEngine()
    public function appTranslateEngine($product_ids, $source, $target_lang, $cat_translate = false)
    {
        // $product_ids = ['2612810', '2612809'];
        // $source = 'de';
        // $target_lang = 'et';

        $products = DrmProduct::whereIn('id', $product_ids)->get();

        $translate = new TranslateClient(['key' => 'AIzaSyCXmhbZQ5pBqXjKgAKwYQVoXF7FHZEE9FA']);
        $word_count = 0;
        $categories = [];

        foreach ($products as $product) {

            foreach ($product->drm_categories as $category){
                #unnique categores;
                $categories[$category->drm_category->id] = $category->drm_category->{'category_name_'.$source};
            }

            $title_arr  = $product->title ?? [];
            $description_arr = $product->description ?? [];

            $title = $title_arr[$source] ?? '';
            $description = $description_arr[$source] ?? '';


            if(!empty($title_arr[$target_lang])) continue;

            //Sleep after limit over
            if ($word_count > 95000) {
                sleep(10); //Prevoius 100
                $word_count = 0;
            }

            //Translate products
            $results = $translate->translateBatch([$title, $description], ['source' => $source, 'target' => $target_lang]);

            //Output
            $title_arr[$target_lang] = $results[0]['text'];
            $description_arr[$target_lang] = $results[1]['text'];

            //count translated word
            $word_count += (str_word_count($title) + str_word_count($description));

            //Update product data
            $product->update(['title' => $title_arr, 'description' => $description_arr]);
            \App\ProductMigration\API::syncProduct($product->id);
        }

        # Translate Category Strings
        $product_categories = DrmCategory::whereIn('id', \array_keys($categories))->get();
        foreach ($product_categories as $key => $cat) {
            $translated_string = $translate->translateBatch([$cat->{'category_name_'.$source}], ['source' => $source, 'target' => $target_lang]);
            $cat->update([
                "category_name_$target_lang" => $translated_string[0]['text']
            ]);
        }
    }




    // jahidulhasanzahid
    public function sofort_translate()
    {
        // dd(request());
        $req = json_decode(request()->data);
        // dd($req);
        // dd(\request()->get());
        $lang_id = json_decode($req->language_id);
        $productNew = json_decode($req->product_id);
        $translateProductID = implode(",", $productNew);
        $translateProductLangID = array();
        foreach ($lang_id as $langID) {
            $toTranslateLangName = DB::table('countries')->where('id', $langID)->select('name')->first();
            $translateProductLangID[] = $toTranslateLangName->name;
        }

        $translateProductLangIDshow = implode(",", $translateProductLangID);
        // dd($translateProductLangIDshow);
        $price = $req->price * 100;
        $payment_source = request()->source;
        // dd($payment_source);
        \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));


        // dd($charge);
        try {
            $charge = \Stripe\Charge::create([
                'amount' => $price,
                'currency' => 'eur',
                'source' => $payment_source,
            ]);
            if ($charge->status != "pending") {
                // return CRUDBooster::redirect(url("https://drm_v7.test/admin/drm_products"),"Sorry, the payment you made is failed","info");
                CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'), "Sorry, the payment you made is failed!", "info");
            }
        } catch (\Exception $e) {
            return "Sorry, the payment you made is failed";
        }

        // dd($product_id);

        $countryName = DB::table('countries')->where('language_shortcode', '=', $req->source)->select('name')->first();
        $countryNameShow = $countryName->name;
        $languages = DB::table('countries')->whereIn('id', $lang_id)->get();

        foreach ($languages as $key => $value) {

            $this->appTranslate($productNew, $req->source, $value->language_shortcode);
        }

        // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
        //   ->join('countries','countries.id','=','billing_details.country_id')
        //   ->first();

        //   $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";

        $taxShow = config('global.tax_for_invoice');
        $price = $req->price . '00';
        $total_tax = ($price * $taxShow) / 100;
        $order_info = [
            'user_id' => 98,
            'cms_client'  => CRUDBooster::myId(),
            'order_date'    => date('Y-m-d H:i:s'),
            'total' => round(($price), 2),
            'sub_total' => round($price - $total_tax, 2),
            'total_tax' => round($total_tax, 2),
            'payment_type'  => "Sofort",
            'status'    => "paid",
            'currency'  => "EUR",
            'adjustment'    => 0,
            'insert_type'   => 3,
            'shop_id'       => 8,
            'order_id_api'  => $charge->id,
        ];

        $carts = [];
        $cart_item = [];
        $cart_item['id'] = 1;
        $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'Product Translate ID  ' . $translateProductID . '.');
        $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT', "Product Translate from $countryNameShow to $translateProductLangIDshow.");
        $cart_item['qty'] = 1;
        $cart_item['rate'] = round($price, 2);
        $cart_item['tax'] = $taxShow;
        $cart_item['product_discount'] = 0;
        $cart_item['amount'] = round($price, 2);
        $carts[] = $cart_item;
        $order_info['cart'] = json_encode($carts);

        app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info);


        // return CRUDBooster::redirect(url("https://drm_v7.test/admin/drm_products"),"Successfully Translated Product","success");
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'), "Successfully Translated Product", "success");
    }

    public function store_translate_product_sofort()
    {

        if ($_POST['price'] < 1.00) {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'), "Amount must convert to at least 1 euro.", "warning");
        } else {
            $user = User::where('id', CRUDBooster::myId())->first();
            if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);
            \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));

            $source = \Stripe\Source::create([
                "type" => "sofort",
                "amount" => $_POST['price'] * 100,
                "currency" => "eur",
                "redirect" => [
                    "return_url" => CRUDBooster::adminPath('') . "/sofort_translate?data=" . json_encode(request()->post()),
                ],
                "sofort" => [
                    "country" => "DE",
                ],
                "owner" => [
                    "email" => $user->email,
                    "name" => $user->name,
                ]
            ]);

            return redirect($source['redirect']['url']);
        }
    }


    public function giropay_translate()
    {
        // dd(request());
        $req = json_decode(request()->data);
        // dd($req);
        // dd(\request()->get());
        $lang_id = json_decode($req->language_id);
        $productNew = json_decode($req->product_id);

        $translateProductID = implode(",", $productNew);
        $translateProductLangID = array();
        foreach ($lang_id as $langID) {
            $toTranslateLangName = DB::table('countries')->where('id', $langID)->select('name')->first();
            $translateProductLangID[] = $toTranslateLangName->name;
        }

        $translateProductLangIDshow = implode(",", $translateProductLangID);
        // dd($translateProductLangIDshow);

        $price = $req->price * 100;
        $payment_source = request()->source;
        // dd($payment_source);
        \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));


        // dd($charge);
        try {
            $charge = \Stripe\Charge::create([
                'amount' => $price,
                'currency' => 'eur',
                'source' => $payment_source,

            ]);
            if ($charge->status != "succeeded") {
                // return CRUDBooster::redirect(url("https://drm_v7.test/admin/drm_products"),"Sorry, the payment you made is failed","info");
                CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'), "Sorry, the payment you made is failed!", "info");
            }
        } catch (\Exception $e) {
            return "Sorry, the payment you made is failed";
        }

        // dd($product_id);

        $countryName = DB::table('countries')->where('language_shortcode', '=', $req->source)->select('name')->first();
        $countryNameShow = $countryName->name;
        $languages = DB::table('countries')->whereIn('id', $lang_id)->get();

        foreach ($languages as $key => $value) {

            $this->appTranslate($productNew, $req->source, $value->language_shortcode);
        }

        // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
        //   ->join('countries','countries.id','=','billing_details.country_id')
        //   ->first();

        //   $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";

        $taxShow = config('global.tax_for_invoice');
        $price = $req->price . '00';
        $total_tax = ($price * $taxShow) / 100;
        $order_info = [
            'user_id' => 98,
            'cms_client'  => CRUDBooster::myId(),
            'order_date'    => date('Y-m-d H:i:s'),
            'total' => round(($price), 2),
            'sub_total' => round($price - $total_tax, 2),
            'total_tax' => round($total_tax, 2),
            'payment_type'  => "Giropay",
            'status'    => "paid",
            'currency'  => "EUR",
            'adjustment'    => 0,
            'insert_type'   => 3,
            'shop_id'       => 8,
            'order_id_api'  => $charge->id,
        ];

        $carts = [];
        $cart_item = [];
        $cart_item['id'] = 1;
        $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'Product Translate ID  ' . $translateProductID . '.');
        $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT', "Product Translate from $countryNameShow to $translateProductLangIDshow.");
        $cart_item['qty'] = 1;
        $cart_item['rate'] = round($price, 2);
        $cart_item['tax'] = $taxShow;
        $cart_item['product_discount'] = 0;
        $cart_item['amount'] = round($price, 2);
        $carts[] = $cart_item;
        $order_info['cart'] = json_encode($carts);

        app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info);

        // return CRUDBooster::redirect(url("https://drm_v7.test/admin/drm_products"),"Successfully Translated Product","success");
        CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'), "Successfully Translated Product", "success");
    }


    public function store_translate_product_giropay()
    {
        if ($_POST['price'] < 1.00) {
            CRUDBooster::redirect(CRUDBooster::adminPath('drm_products'), "Amount must convert to at least 1 euro.", "warning");
        } else {
            $user = User::where('id', CRUDBooster::myId())->first();
            if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);
            \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));

            $source = \Stripe\Source::create([
                "type" => "giropay",
                "amount" => $_POST['price'] * 100,
                "currency" => "eur",
                "redirect" => [
                    "return_url" => CRUDBooster::adminPath('') . "/giropay_appointment?data=" . json_encode(request()->post()),
                ],
                "owner" => [
                    "email" => $user->email,
                    "name" => $user->name,
                ]
            ]);
            return redirect($source['redirect']['url']);
        }
    }
    // jahidulhasanzaid

    public function store_translate_product(Request $request)
    {
        \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
        $user = User::with('billing_detail')->find(CRUDBooster::myId());
        $countryName = DB::table('countries')->where('language_shortcode', '=', $request->source)->select('name')->first();
        $countryNameShow = $countryName->name;

        $translateProductID = implode(",", $request->product_id);
        $translateProductLangID = array();
        foreach ($request->language_id as $langID) {
            $toTranslateLangName = DB::table('countries')->where('id', $langID)->select('name')->first();
            $translateProductLangID[] = $toTranslateLangName->name;
        }

        $translateProductLangIDshow = implode(",", $translateProductLangID);
        // dd($translateProductLangIDshow);

        if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);


        try {
            // operation  start
            //DB::beginTransaction();


            //Coupon
            $coupon = null;
            if (isset($_POST['coupon_id']) && $_POST['coupon_id']) {
                $coupon = \Stripe\Coupon::retrieve($_POST['coupon_id'], []);
            }

            $discount = 0;
            $total =  $request->price;
            $sub_price = $total;
            $coupon_valid = false;

            if ($coupon) {
                if ($coupon->valid) {
                    $coupon_valid = true;
                    $discount = ($coupon->amount_off) ? ($coupon->amount_off / 100) : (($sub_price * $coupon->percent_off) / 100);
                    $discount = ($discount > $sub_price) ? $sub_price : $discount;
                    $total = $total - $discount;
                } else {
                    throw new \Exception("Invalid Coupon code. Please try again!");
                }
            }

            $charge = \Stripe\Charge::create([
                'amount' => $total * 100,
                'currency' => 'eur',
                'description' => 'Product Translated ',
                'source' => $request->stripeToken
            ]);

            //Errors
            $errors_id = [];
            $success_id = [];

            if ($charge['status'] == "succeeded") {
                $languages = DB::table('countries')->whereIn('id', $request->language_id)->get();
                foreach ($languages as $key => $value) {
                    try {
                        $this->appTranslate($request->product_id, $request->source, $value->language_shortcode);
                        $success_id[] = $value->id;
                    } catch (\Exception $etx) {
                        $errors_id[] = $value->id;
                    }
                }

                //Update single pay coupon
                if ($coupon_valid) {
                    DB::table('coupons')->where('coupon_id', $coupon->id)->increment('single_pay');
                }

                $billingDetails = DB::table('billing_details')->where('user_id', CRUDBooster::myId())
                    ->join('countries', 'countries.id', '=', 'billing_details.country_id')
                    ->first();

                $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";

                $taxShow = config('global.tax_for_invoice');
                $price = $request->price . '00';
                $total_tax = ($price * $taxShow) / 100;
                $order_info = [
                    'user_id' => 98,
                    'cms_client'  => CRUDBooster::myId(),
                    'order_date'    => date('Y-m-d H:i:s'),
                    'total' => round(($total), 2),
                    'sub_total' => round($sub_price, 2),
                    'discount' => round($discount, 2),
                    'discount_type' => 'fixed',
                    'total_tax' => 0,
                    'payment_type'  => "Stripe Card",
                    'status'    => "paid",
                    'currency'  => "EUR",
                    'adjustment'    => 0,
                    'insert_type'   => 3,
                    'shop_id'       => 8,
                    'order_id_api'  => $charge->id,
                ];

                $carts = [];
                $cart_item = [];
                $cart_item['id'] = 1;
                $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT', 'Product Translate ID  ' . $translateProductID . '.');
                $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT', "Product Translate from $countryNameShow to $translateProductLangIDshow.");
                $cart_item['qty'] = 1;
                $cart_item['rate'] = round($price, 2);
                $cart_item['tax'] = $taxShow;
                $cart_item['product_discount'] = 0;
                $cart_item['amount'] = round($price, 2);
                $carts[] = $cart_item;
                $order_info['cart'] = json_encode($carts);

                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info);

                $postdata = [
                    'app_name' => 'Product Translate',
                    'price' => $total,
                    'subject' => 'Product Translate confirmation By DRM',
                ];
                app('drm.mailer')->getMailer()->to($user->billing_detail->email)->send(new AppPurchaseConfirmation($postdata));
            }

            // operation  end
            $info_message = ($errors_id) ? 'Unfortunately only those' . implode(', ', $errors_id) . ' producs are not translated properly dut to your content.' : 'Successfully Translated Product';

            //DB::commit();    // Commiting  ==> There is no problem whatsoever
            return response(['status' => true, 'message' => $info_message]);
        } catch (\Exception $e) {
            //DB::rollBack();   // rollbacking  ==> Something went wrong
            return response(['status' => false, 'message' => $e->getMessage()]);
        }
    }


    public function appTranslate($productNew, $source, $user_lang)
    {

        $language = DB::table('countries')->where('language_shortcode', $source)->first();
        $table = "drm_translation_" . $source;

        $products = DrmProduct::find($productNew);

        $translate = new TranslateClient(['key' => 'AIzaSyCXmhbZQ5pBqXjKgAKwYQVoXF7FHZEE9FA']);
        $charCount = 0;
        $charCount += $_POST['count'];

        foreach ($products as $product) {

            $names        = "";
            $descriptions = "";
            $name        = ($product->name == '') ? "" : $product->name;
            $description = ($product->description == '') ? "" : $product->description;
            $item_weight = ($product->item_weight == '') ? "" : $product->item_weight;
            $item_size = ($product->item_size == '') ? "" : $product->item_size;
            $item_color = ($product->item_color == '') ? "" : $product->item_color;
            $brand = ($product->brand == '') ? "" : $product->brand;
            $production_year = ($product->production_year == '') ? "" : $product->production_year;
            $material = ($product->materials == '') ? "" : $product->materials;

            if ($charCount > 95000) {
                sleep(100);
                $charCount = 0;
            }
            try {
                $results = $translate->translateBatch([$name, $description, $item_weight, $item_size, $item_color, $brand, $production_year, $material], ['source' => $source, 'target' => $user_lang]);

                $names = $results[0]['text'];
                $descriptions = $results[1]['text'];
                $item_weights = $results[2]['text'];
                $item_sizes = $results[3]['text'];
                $item_colors = $results[4]['text'];
                $brands = $results[5]['text'];
                $production_years = $results[6]['text'];
                $materials = $results[7]['text'];
            } catch (Exception $e) {

                dd($e);
            } finally {

                if ($names == '' or $descriptions == '' or $item_weights == '' or $item_size == '' or $item_colors == '' or $brands == '' or $production_years == '' or $materials == '') {
                    sleep(2);
                    $results = $translate->translateBatch([$name, $description, $item_weight, $item_size, $item_color, $brand, $production_year, $material], ['source' => $source, 'target' => $user_lang]);

                    $names = $results[0]['text'];
                    $descriptions = $results[1]['text'];
                    $item_weights = $results[2]['text'];
                    $item_sizes = $results[3]['text'];
                    $item_colors = $results[4]['text'];
                    $brands = $results[5]['text'];
                    $production_years = $results[6]['text'];
                    $materials = $results[7]['text'];
                }

                $title = $product->title;
                $title[$user_lang] = strip_tags($names);
                $description = $product->description;
                $description[$user_lang] = strip_tags($descriptions);


                // $rows['item_weight'] = strip_tags($item_weights);
                // $rows['item_size'] = strip_tags($item_sizes);
                // $rows['item_color'] = strip_tags($item_colors);
                // $rows['brand'] = strip_tags($brands);
                // $rows['production_year'] = strip_tags($production_years);
                // $rows['materials'] = strip_tags($materials);
                // $rows['source_id'] = $language->id ;
                // $rows['ean'] = $product->ean;

                $product->title = $title;

                $product->description = $description;
                $product->save();

                $category = DB::table('drm_products')->join('drm_product_categories', 'drm_product_categories.product_id', '=', 'drm_products.id')
                    ->join('drm_category', 'drm_category.id', '=', 'drm_product_categories.category_id')
                    ->select('drm_category.*', 'drm_products.ean')
                    ->where('drm_products.id', $product->product_id)->get();
                $column = 'category_name_' . $user_lang;
                foreach ($category as $value) {
                    $trans_category = 'category_name_' . $source; //lang = de/en/fr
                    $category = $value->category_name ?: $value->$trans_category;
                    $cat = ($category == '') ? "" : $category;
                    $data = $translate->translateBatch([$cat], ['source' => $source, 'target' => $user_lang]);
                    $category_names = $data[0]['text'];
                    DB::table('drm_category')->where('id', $value->id)->update([$column => $category_names]);
                }
            }
        }
    }


    public function update_calculation()
    {
        $product_id = request()->product_id;
        $results = DrmProduct::find(request()->product_id);
        $lang = request()->source;
        $title = '';
        $cat = '';
        $siz = '';
        $weight = '';
        $brand = '';
        $material = '';
        $production_year = '';
        $des = '';
        $color = '';
        $total_text = '';

        $category_name = [];

        foreach ($results as $key => $result) {
            $title .= $result->title[$lang];
            $des .= $result->description[$lang];
            // $color .=$result->item_color;
            // $siz .=$result->item_size;
            // $weight .=$result->item_weight;
            // $brand .=$result->brand;
            // $material .=$result->materials;
            // $production_year .=$result->production_year;
            foreach ($result->drm_categories as $category){
                $category_name[] = $category->drm_category->{'category_name_'.$lang};
            }

            $category_name = array_unique($category_name);
            $cat = implode(' ', $category_name);
        }
        $total_text .= $title . ' ' . $des; // .' '. $color.' '.$production_year.' '.$material.' '.$brand.' '.$weight.' ' .$siz.' '.$cat;

        $total_text_with_cat = $total_text . ' ' . $cat;
        $count_with_cat = str_word_count($total_text_with_cat);
        $total_with_cat = $count_with_cat * request()->language;
        $price_with_cat = $total_with_cat / 200;

        $count = str_word_count($total_text);
        $total = $count * request()->language;

        $price = $total / 200;

        $price_text = number_format((float)$price, 2, ',', '.');
        $price_with_cat_text = number_format((float)$price_with_cat, 2, ',', '.');
        return response([
            'total' => $total,
            'total_with_cat' => $total_with_cat,
            'price' => $price,
            'price_with_cat' => $price_with_cat,
            'price_text' => $price_text,
            'price_with_cat_text' => $price_with_cat_text,
        ]);
    }

    public function read_moore()
    {
        $lang = request()->source;
        $results = DrmProduct::find(request()->product_id);
        return view('app_store.read_moore', compact('results', 'lang'));
    }

    //Translating using tokens for new users
    public function startTranslationNew(Request $request) {
        $product_id = $request->product_id;
        $user_id = $request->user_id;
        if(count($product_id) > 0){
            $data['user_id'] = $user_id;
            $data['language_id'] = implode(',', $request->language_id);
            $data['product_id'] = implode(',', $request->product_id);
            $data['source'] = $request->source;
            $data['translate_category'] = $request->cat_translation == 'true' ? 'yes' : 'no';

            if($request->cat_translation == 'true'){
                $count = $request->price_with_cat;
            }
            else{
                $count = $request->price;
            }


            if(in_array($user_id, config('global.free_product_translate_users') ?? []))
            {
                $this->productTranslationSCA($data);
                return response()->json([
                    'success' => true,
                    'message' => __('translation_done')
                ]);
            }

            $type = \App\Enums\CreditType::PRODUCT_TRANSLATION;
            $tariff_available = app('App\Http\Controllers\tariffController')->creditReductionCheck($user_id, $count, 0.01, $type);

            if($tariff_available == 0){
                return response()->json([
                    'success' => false,
                    'message' => __('update_plan_for_translation').' This action needs '.$count.' credits to be completed.',
                ]);
            }
            elseif($tariff_available == 1){
                $this->productTranslationSCA($data);
                $charge_credit = app('App\Http\Controllers\tariffController')->creditCharge($user_id, $count, 0.01, $type);
                return response()->json([
                    'success' => true,
                    'message' => __('translation_done')
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Something Went Wrong',
            ]);
        }
        return response()->json([
            'success' => true,
            'message' => __('translation_done')
        ]);
    }
}
