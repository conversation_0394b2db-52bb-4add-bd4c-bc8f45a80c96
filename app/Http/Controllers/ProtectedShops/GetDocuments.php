<?php
// SED -> ll

namespace App\Http\Controllers\ProtectedShops;

use Illuminate\Support\Facades\Storage;

use App\Shop;
use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;

class GetDocuments
{
    private $documentPath;
    private $error = null;

    public function __construct($request)
    {
        $content_formats = config('protectedShops.content_formats');
        $partnerId = config('protectedShops.partnerId');
        $base_url = config('protectedShops.base_url');
        $_format = config('protectedShops._format');
        $locale = config('protectedShops.locale');

        $shopId = $request['protected_shops_id'];
        $token = $request['access_token'];

        $api_url = $base_url . "v2.0/$locale/partners/$partnerId/shops/$shopId/documents/format/$_format";

        $header = [
            "Accept: application/json",
            "Content-Type: application/json",
            "Authorization: $token"
        ];

        $ch = curl_init($api_url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if (!isset($api_response['error'])) {

            // $documents = $api_response['content']['documents'][0]['type']; // usually this is "Impressum"
            $api_documents = $api_response['content']['documents'];

            $data = [];
            $files = [];
            $documents = [];
            $file_paths = [];
            $file_paths = [];
            
            // Downloading all documents format
            foreach ($api_documents as $document) {
                foreach ($content_formats as $content_format) {
                    $docType = $document['type'];
                    $url = 'https://drm.network/shop-document/'.$shopId.'/'.$docType.'/'.$content_format.'/stream';
                    $file_paths[$content_format] = $url;
                }

                $documents[$document['type']] = $file_paths;
                $file_paths = [];
            }

            if (!isset($api_response['error'])) {
                $data['time'] = now();
                $data['status'] = 'active';
                $data['documents'] = $documents; 

                $res = Shop::where([
                    'id' => $request['shop_id'],
                    'user_id' => CRUDBooster::myParentId()
                ])->update([
                    'protected_shop' => $data
                ]);

                if ($res) {
                    $this->documentPath = $documents['AGB']['pdf'];
                }
                else {
                    $this->error = [
                        'error_code' => 404, 
                        'error' => "User, shop or documents not found!", 
                        'description_title' => "While storing the documents, server encountered the following error:",
                        'error_description' => "User or shop not found!"
                    ];
                }
            }
            else {
                $this->error = [
                    'error_code' => $httpcode, 
                    'error' => $api_response['error'], 
                    'description_title' => "While fetching the documents, server encountered the following error:",
                    'error_description' => $api_response['error_description']
                ];
            }
        } 
        else {
            $this->error = [
                'error_code' => $httpcode, 
                'error' => $api_response['error'], 
                'description_title' => "While fetching the documents, server encountered the following error:",
                'error_description' => $api_response['error_description']
            ];
        }
    }

    public function getDocumentPath()
    {
        return $this->documentPath;
    }

    public function hasError()
    {
        return $this->error;
    }
}
// SED -> ll