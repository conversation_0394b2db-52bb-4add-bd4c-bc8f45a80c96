<?php
// SED -> ll

namespace App\Http\Controllers\ProtectedShops;

class GetQuestions
{
    private $sections;
    private $error = null;

    public function __construct($protected_shops_credentials)
    {
        $partnerId = config('protectedShops.partnerId');
        $base_url = config('protectedShops.base_url');
        $_format = config('protectedShops._format');
        $locale = config('protectedShops.locale');

        $shopId = $protected_shops_credentials['protected_shops_id'];
        $token = $protected_shops_credentials['access_token'];

        $api_url = $base_url . "v2.0/$locale/partners/$partnerId/shops/$shopId/questionary/format/$_format";

        $header = [
            "Accept: application/json",
            "Content-Type: application/json",
            "Authorization: $token"
        ];

        $ch = curl_init($api_url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if (!isset($api_response['error'])) {
            $this->sections = $api_response['content']['documents'];
        }
        else {
            $this->error = [
                'error_code' => $httpcode, 
                'error' => $api_response['error'], 
                'description_title' => "While fetching the questions, server encountered the following error:",
                'error_description' => $api_response['error_description']
            ];
        }
    }

    // To access sections from another class
    public function getSections()
    {
        return $this->sections;
    }

    public function hasError()
    {
        return $this->error;
    }
}
// SED -> ll