<?php
// SED -> ll

namespace App\Http\Controllers\ProtectedShops;

use App\Shop;
use crocodicstudio\crudbooster\helpers\CRUDBooster;

class GetShopId
{
    private $error = null;
    private $protected_shops_id;

    public function __construct($access_token, $shop_id)
    {
        $shop = Shop::where([
            'id' => $shop_id
        ])
        ->select('id', 'channel', 'protected_shop')
        ->first();



        if(empty($shop))
        {
            $this->error = [
                'error_code' => 400, 
                'error' => '', 
                'description_title' => "While creating a shop, server encountered the following error:",
                'error_description' => '',
            ];
        }


        if(!empty($shop->protected_shop))
        {
            $protected_shop = $shop->protected_shop;
            // if($protected_shop['status'] == 'active')
            // {
            //     $this->error = [
            //         'error_code' => 400, 
            //         'error' => '', 
            //         'description_title' => "While creating a shop, server encountered the following error:",
            //         'error_description' => '',
            //     ];
            // }

            if($protected_shop['id']) {
                $this->protected_shops_id = $protected_shop['id'];
                return;
            }
        }

        $shop_type = $shop->channel;


        $module = config('protectedShops.modules.' . $shop_type);
        $partnerId = config('protectedShops.partnerId');
        $base_url = config('protectedShops.base_url');
        $_format = config('protectedShops._format');
        $locale = config('protectedShops.locale');

        $shop_name = "drm_" . $module;

        $api_url = $base_url . "v2.0/$locale/partners/$partnerId/shops/format/$_format";

        $header = [
            "Accept: application/json",
            "Content-Type: application/json",
            "Authorization: $access_token"
        ];

        $body = [
            "shop" => [
                "title" => $shop_name,
                "module" => $module,
                "partnerId" => $partnerId
            ]
        ];
        $body = json_encode($body);

        $ch = curl_init($api_url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        /////////////////////////////////////////////////////
        // Dummy data
        // $api_response = [];
        // $api_response['shopId'] = "711F4F05CE59F9AF15665648054EA3BA";
        ////////////////////////////////////////////////////

        if (!isset($api_response['error'])) {
            $this->protected_shops_id = $api_response['shopId'];
            $shop->update(['protected_shop' => ['id' => $this->protected_shops_id ]]);
        } 
        else {
            $this->error = [
                'error_code' => $httpcode, 
                'error' => $api_response['error'], 
                'description_title' => "While creating a shop, server encountered the following error:",
                'error_description' => $api_response['error_description']
            ];
        }
    }

    public function hasError()
    {
        return $this->error;
    }

    public function getProtectedShopsId()
    {
        return $this->protected_shops_id;
    }
}
// SED -> ll