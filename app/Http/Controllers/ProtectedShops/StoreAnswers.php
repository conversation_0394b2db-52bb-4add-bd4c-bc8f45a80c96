<?php
// SED -> ll

namespace App\Http\Controllers\ProtectedShops;

class StoreAnswers
{
    private $isAnsValid;
    private $error = null;
    
    private function formatAns($request)
    {
        $conditions = [];
        $variables = [];

        $data = $request->all();
        unset($data['_token']);

        foreach (array_keys($data) as $key) {
            // Removing null values
            if ($data[$key] == null) {
                unset($data[$key]);
            }
            // For radio ques
            else if ($key[0] == 'Q') {
                $conditions[$data[$key]] = 1;
            }
            // For yesno & checkbox ques
            else if ($key[0] == 'c') {
                $conditions[$key] = (int) $data[$key];
            }
            // For variable fields
            else {
                $variables[$key] = $data[$key];
            }
        }

        $tmp = [];
        $tmp['conditions'] = $conditions;
        $tmp['variables'] = $variables;

        $answers['answers'] = $tmp;

        return $answers;
    }

    public function __construct($request)
    {
        $answers = $this->formatAns($request);

        $partnerId = config('protectedShops.partnerId');
        $base_url = config('protectedShops.base_url');
        $_format = config('protectedShops._format');
        $locale = config('protectedShops.locale');
        
        $shopId = $request['protected_shops_id'];
        $token = $request['access_token'];

        $api_url = $base_url . "v2.0/$locale/partners/$partnerId/shops/$shopId/answers/format/$_format";

        $header = [
            "Accept: application/json",
            "Content-Type: application/json",
            "Authorization: $token"
        ];

        $body = json_encode($answers);

        $ch = curl_init($api_url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if (!isset($api_response['error'])) {
            $this->isAnsValid = $api_response["answersStatus"]["valid"];
        }
        else {
            $this->error = [
                'error_code' => $httpcode, 
                'error' => $api_response['error'], 
                'description_title' => "While storing the answers, server encountered the following error:",
                'error_description' => $api_response['error_description']
            ];
        }
    }

    public function getValidity()
    {
        return $this->isAnsValid;
    }

    public function hasError()
    {
        return $this->error;
    }
}
// SED -> ll