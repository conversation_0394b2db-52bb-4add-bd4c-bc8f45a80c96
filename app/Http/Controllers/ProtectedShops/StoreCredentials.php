<?php
// SED -> ll

namespace App\Http\Controllers\ProtectedShops;

class StoreCredentials
{
    private $error = null;
    private $access_token;
    private $protected_shops_id;

    public function __construct($shop_id)
    {
        $base_url = config('protectedShops.base_url');
        $api_url = $base_url . 'oauth/v2/token';

        $body = [
            'client_id' => env('PROTECTED_SHOPS_CLIENT_ID'),
            'client_secret' => env('PROTECTED_SHOPS_CLIENT_SECRET'),
            'grant_type' => config('protectedShops.grant_type')
        ];
        $body = json_encode($body);

        $ch = curl_init($api_url);

        curl_setopt_array($ch, array(
            CURLOPT_TIMEOUT => 0,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_URL => $api_url,
            CURLOPT_POSTFIELDS => $body,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_HTTPHEADER => array('Content-Type: application/json')
        ));

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        ///////////////////////////////////////////////////////////
        // Dummy data
        // $api_response = [];
        // $api_response['token_type'] = "Bearer";
        // $api_response['access_token'] = "ZGI3YTA2ZTQwNDFiOGFjMGM3M2Y4OGQ3OGY2MjRhZjQ3MTI3NzIzYWUyYzM1YTgyYjMyM2UzMTEyNzE1NjcxYQ";
        //////////////////////////////////////////////////////////

        if (!isset($api_response['error'])) {
            $this->access_token = ucfirst($api_response['token_type']) . " " . $api_response['access_token'];
            
            $obj = new GetShopId($this->access_token, $shop_id);
            $this->error = $obj->hasError();
            $this->protected_shops_id = $obj->getProtectedShopsId();
        }
        else {
            $this->error = [
                'error_code' => $httpcode, 
                'error' => $api_response['error'], 
                'description_title' => "While authenticating, server encountered the following error:",
                'error_description' => $api_response['error_description']
            ];
        }
    }

    public function hasError()
    {
        return $this->error;
    }

    public function getAccessToken()
    {
        return $this->access_token;
    }

    public function getProtectedShopsId()
    {
        return $this->protected_shops_id;
    }
}
// SED -> ll