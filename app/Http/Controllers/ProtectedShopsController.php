<?php
// SED -> ll

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Http\Controllers\ProtectedShops\GetDocuments;
use App\Http\Controllers\ProtectedShops\StoreAnswers;
use App\Http\Controllers\ProtectedShops\GetQuestions;
use App\Http\Controllers\ProtectedShops\StoreCredentials;
use Illuminate\Support\Facades\Storage;
use App\Shop;

class ProtectedShopsController extends Controller
{
    private $contentFormats = ['pdf', 'html', 'html-snippet', 'text'];

    public function getQuestions(Request $request, $id, $shop)
    {
        $partnerId = config('protectedShops.partnerId');
        $url = "https://api.protectedshops.de/v2.0/de/partners/{$partnerId}/shops/{$id}/questionary/format/json";

        $token = $request->header('authToken');
        $header = [
            "Accept: application/json",
            "Content-Type: application/json",
            "Authorization: {$token}"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        return $api_response;
    }


    public function answerQuestion(Request $request, $id, $shop)
    {
        $partnerId = config('protectedShops.partnerId');
        $url = "https://api.protectedshops.de/v2.0/de/partners/{$partnerId}/shops/{$id}/answers/format/json";
        $body = json_encode(['answers' => $request->answers]);

        $token = $request->header('authToken');
        $header = [
            "Accept: application/json",
            "Content-Type: application/json",
            "Authorization: {$token}"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        return $api_response;
    }

    public function index()
    {
        try {
            $shop = request()->shop_id;

            $channelId = \DB::table('shops')->where('id', $shop)->value('channel');

            if(in_array($channelId, \App\Enums\Channel::MIRAKL_CHANNELS))
            {
                return response()->json([
                    'success' => true,
                    'custom_doc' => true,
                    'channel' => $channelId,
                    'shop_id' => $shop,
                    'url' => \CRUDBooster::adminPath('shop_setting/save-document/'.$shop),
                ]);
            }

            $credential = new StoreCredentials($shop);
            $error = $credential->hasError();

            if($error)
            {
                throw new \Exception($error['error_description']);
            }

            return response()->json([
                'success' => true,
                'protected_shop_id' => $credential->getProtectedShopsId(),
                'access_token'  => $credential->getAccessToken(),
                'shop_id' => $shop,
                'droptienda' => Shop::where('id', $shop)->value('channel') == 10 ? 'yes' : 'no',
            ]);
        } catch(\Exception $e)
        {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 401);
        }


        $obj = new StoreCredentials(request()->shop_id);
        $error = $obj->hasError();

        $protected_shops_credentials = [
            'access_token' => $obj->getAccessToken(),
            'protected_shops_id' => $obj->getProtectedShopsId()
        ];

        if ($error) {
            return response()->json([
                'view' => view('protectedShops.index')->render(),
                'protected_shops_credentials' => "",
                'html' => view('protectedShops.error', $error)->render()
            ]);
        }
        else {
            return response([
                'view' => view('protectedShops.index')->render(),
                'html' => $this->showQues(false, $protected_shops_credentials)->render(),
                'protected_shops_credentials' => $protected_shops_credentials
            ]);
        }
    }


    // store files
    public function storeFiles(Request $request, $id, $shop)
    {
        $partnerId = config('protectedShops.partnerId');
        $url = "https://api.protectedshops.de/v2.0/de/partners/{$partnerId}/shops/{$id}/documents/format/json";

        $token = $request->header('authToken');
        $header = [
            "Accept: application/json",
            "Content-Type: application/json",
            "Authorization: {$token}"
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if (!isset($api_response['error'])) {
            $api_documents = $api_response['content']['documents'];

            $files = [];
            foreach ($api_documents as $document) {
                $docType = $document['type'];
                $files[$docType] = $this->storeFilesCloud($id, $docType, $header);
            }

            $payload = [
                'time' => now(),
                'status' => 'active',
                'documents' => $files,
                'id' => $id,
            ];

            Shop::where([
                'id' => $shop,
            ])->update([
                'protected_shop' => $payload
            ]);

            $this->legalTaxtApi($shop,$payload);

            return ['success' => true];
        }

        return ['success' => false];
    }

    private function legalTaxtApi($shop_id,$payload){

        $shop_info = Shop::where('id',$shop_id)->first();
        try{
            if(substr($shop_info->url, -1) != '/'){
                $shop_info->url .= '/';
            }
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $shop_info->url.'api/v1/drm-to-dt-legal-texts-transfer',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => array('data' => json_encode($payload)),
                CURLOPT_HTTPHEADER => array(
                    'userPassToken: '.$shop_info->password,
                    'userToken: '.$shop_info->username
                ),
            ));

            $response = curl_exec($curl);
            curl_close($curl);
        }catch (Shop $e) {
            return false;
        }
    }


    // Update document
    public function refreshProtectedShopsFile()
    {
        $shops = Shop::whereNotNull('protected_shop')
        ->whereNotNull('protected_shop->id')
        ->get()
        ->each(function($shop) {
            $this->fetchContentToStorage($shop);
        });
    }

    // Transfer text
    private function fetchContentToStorage($shop)
    {

        $item = $shop->protected_shop;
        if(empty($item['id'])) return;

        $id = $item['id'];


        $partnerId = config('protectedShops.partnerId');
        $url = "https://api.protectedshops.de/v2.0/de/partners/{$partnerId}/shops/{$id}/documents/format/json";

        $header = [
            "Accept: application/json",
            "Content-Type: application/json",
        ];

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $api_response = json_decode(curl_exec($ch), true);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if (!isset($api_response['error'])) {
            $api_documents = $api_response['content']['documents'];

            $files = [];
            foreach ($api_documents as $document) {
                $docType = $document['type'];
                $files[$docType] = $this->storeFilesCloud($id, $docType, $header);
            }

            $item['documents'] = $files;
            Shop::where(['id' => $shop->id])->update(['protected_shop' => $item]);
        }
    }

    // Store files
    private function storeFilesCloud($id, $docType, $header): array
    {
        $partnerId = config('protectedShops.partnerId');
        $files = [];
        foreach ($this->contentFormats as $contentFormat) {
            $url = 'https://drm.network/shop-document/'.$id.'/'.$docType.'/'.$contentFormat.'/stream';
            $files[$contentFormat] = $url;
        }

        return $files;
    }


    public function showQues($isAnsValid = false, $protected_shops_credentials)
    {
        // Sections, Questions & Variables
        $SQV = new GetQuestions($protected_shops_credentials);
        $error = $SQV->hasError();

        if ($error) return view('protectedShops.error', $error);
        else {
            $sections = $SQV->getSections();
            return view('protectedShops.questions', compact('sections', 'isAnsValid'));
        }
    }

    function storeAns(Request $request)
    {
        // Store the answers in the server
        $obj = new StoreAnswers($request);
        $error = $obj->hasError();

        $protected_shops_credentials = [
            'access_token' => $request['access_token'],
            'protected_shops_id' => $request['protected_shops_id']
        ];

        if ($error) return view('protectedShops.error', $error);
        else {
            $isAnsValid = $obj->getValidity();
            return $this->showQues($isAnsValid, $protected_shops_credentials);
        }
    }

    public function store(Request $request)
    {
        $documents = new GetDocuments($request);
        $error = $documents->hasError();

        if ($error) return view('protectedShops.error', $error);
        else {
            $documentPath = $documents->getDocumentPath();
            return view('protectedShops.document', compact('documentPath'));
        }
    }
}
// SED -> ll
