<?php

namespace App\Http\Controllers;

use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\AgbLogs;
use App\Traits\ProjectShare;
use App\Models\Marketplace\MarketplaceParentCategory;
use App\Notifications\DRMNotification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use App\Mail\DRMSEndMail;
use Illuminate\Support\Facades\Mail;
use App\TrakerTimeSpent;
use Illuminate\Support\Facades\Log;
use App\DeliveryCompany;
use App\AppointmentHistory;
use DRM;
use Exception;
use App\Option;
use App\Http\Controllers\tariffController;
use App\Jobs\Marketplace\MarketplaceProductTransferToDrm;
use App\MarketplaceProducts;
use App\Services\Tariff\Credit\RefillCredit;

class RegistrationController extends Controller
{
    use ProjectShare;

    private $lang = 'de';
    private $trans = [];

    public function __construct(Request $request)
    {
        $this->lang = $request->header('lang', 'de');
        app()->setLocale($this->lang);
        $this->trans = $this->lang == 'de' ? $this->locale_de : $this->locale_en;
    }

    private $locale_de = [
        'account_created_successfully' => 'Bitte prüfe dein Postfach. Nach Eingabe deines Sicherheitscode leiten wir dich zu deinem Account weitern. Im Anschluss kannst du dich mit deinen selbst vergebenen Zugangsdaten jederzeit anmelden.',
        'failed_to_create_account' => 'Konto konnte nicht erstellt werden',
        'invalid_data' => 'Ungültige Dateneingabe',
        'mail_2fa_error' => 'Konto erfolgreich erstellt! Aber 2FA-Code wird nicht gesendet. Bitte versuchen Sie sich anzumelden!',
        'invalid_code' => 'Es tut uns leid! Der eingegebene E-Mail-Bestätigungscode stimmt nicht überein',
        'invalid_action' => 'Ungültige Aktion',
        'account_done' => 'Konto wurde erfolgreich erstellt!',
        'sent_2fa' => 'Bestätigungscode gesendet!',
    ];

    private $locale_en = [
        'account_created_successfully' => 'Account created successfully!',
        'failed_to_create_account' => 'Failed to create account!',
        'invalid_data' => 'Invalid data input',
        'mail_2fa_error' => 'Account created successfully! But 2FA code not sent. Please try to login!',
        'invalid_code' => 'Sorry! The email verification code you have entered does not match',
        'invalid_action' => 'Invalid action',
        'account_done' => 'Account is created successfully!',
        'sent_2fa' => 'Verification code sent!',
    ];

    public function index() {
        return view('iframe.sup_reg-form');
    }

    public function showIframe() {
        return '<iframe src="'. route('qogita.form') .'" title="Account creation" width="700px" height="700px" style="border: none!important;"></iframe>';
    }

    public function store(Request $request) {}

    public function register(Request $request) {

        $vali_rules = [
            'email' => 'required|email|unique:cms_users',
            'password'  => 'required|confirmed|min:8',
            'first_name' => 'required|min:2|max:40|string',
            'last_name' => 'required|min:2|max:40|string',
            'mp_category' => 'required|array|min:1|max:5',
            "company" => 'nullable|max:100|string',
            "country" => 'required',
            "city" => 'required',
            "zip" => 'required',
            "street" => 'required',
            'phone' => 'required',
            "vat_number" => 'nullable|max:40|string',
            'dropfunnel_tag' => 'sometimes|nullable',
            'tag' => 'sometimes|nullable',
            'tags' => 'sometimes|nullable',
        ];

        if (isset($request->aqua)) {
            unset($vali_rules['mp_category']);
        }

        $validator = Validator::make($request->all(), $vali_rules,
        [
            'email.unique' => __('email_exists'),
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error_messages'         => $validator->errors()->all(),
                'errors' => $validator->errors()->messages(),
                'message' => $this->trans['invalid_data'],
            ], 422);
        }

        // Email validate
        try {
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($request->email);
        } catch (Exception $e) {
            return response()->json([
                'error_messages' => [$e->getMessage()],
                'errors' => [
                    'email' => [$e->getMessage()],
                ],
                'message' => $this->trans['invalid_data'],
            ], 422);
        }

        // Phone validate
        try {
            if(isset($request->phone) && !empty($request->phone))
            {
                $phone = $request->phone;
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
            }
        } catch (Exception $e) {
            return response()->json([
                'error_messages' => [$e->getMessage()],
                'errors' => [
                    'phone' => [$e->getMessage()],
                ],
                'message' => $this->trans['invalid_data'],
            ], 422);
        }

        try {
            $vat_number = $request->vat_number;
            if(!empty($vat_number) && $request->country != 83)
            {
                $vat_checker = \DRM::checkTaxNumber($vat_number);
                if(!$vat_checker['success']) throw new \Exception('Invalid VAT number. '.$vat_checker['message']);
            }

            // User data
            $firstName = $request->first_name;
            $lastName = $request->last_name;
            $password = Hash::make($request->password);
            $email = $request->email;

            // Billing Data
            $company_name = $request->company;
            $country_id = $request->country;

            $referred_by = null;
            if (!empty($request->referred_by)){
                $referred_by = DB::table('cms_users')->where('id', $request->referred_by)->value('id');
            }

            // Create user
            $user = User::create([
                'name'     => trim("{$firstName} {$lastName}"),
                'email'    => $email,
                'password' => $password,
                'id_cms_privileges' => 3,
                'status' => null,
                'referrer_id' => $referred_by,
                'ref_id' => Str::random(25),
                'two_factor_code' => rand(100999, 999999),
                'two_factor_expires_at' => now()->addMinutes(10),
            ]);

            $userCreated = ($user && $user->id);
            if(!$userCreated) throw new Exception($this->trans['failed_to_create_account']);

            $billing = $user->billing_detail()->create([
                'country_id' => $country_id,
                'company_name' => $company_name,
                'email' => $user->email,
                'vat_id' => $vat_number,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'last_updated_at' => now(),
                'city' => $request->city,
                'zip' => $request->zip,
                'address' => $request->street,
                'phone' => $request->phone,
            ]);

            // Agb Logfile
            try {
                AgbLogs::create([
                    'user_id' => $user->id,
                    'message' => $user->name . ' Account Created!',
                    'agb' => json_encode(['agb' => config('agb.user_registration')]),
                    'ip_address' => getIpAddress(),
                ]);
            } catch (Exception $th) {}
            // End Agb Logfile

            // Tag Insert
            $tag = 'Anmeldung Dropmatix';
            if($request->has('dropfunnel_tag') && !empty($request->dropfunnel_tag))
            {
                $tag = $request->dropfunnel_tag;
            }

            $mp_category = [];
            $tags = [];
            if(isset($request->mp_category) && !empty($request->mp_category) && is_array($request->mp_category))
            {
               $tags = MarketplaceParentCategory::whereIntegerInRaw('id', $request->mp_category)->pluck('name')->toArray();
               $mp_category = $request->mp_category ?? [];
            }

            $userTag = $request->has('tag') && !empty($request->tag) ? $request->tag : 'MP registration';

            $userTagArr = explode('tag:', $userTag);
            if (count($userTagArr) > 1) {
                $userTag = 'MP registration';

                unset($userTagArr[0]);
                foreach ($userTagArr as $singleUserTag) {
                    $tags[] = trim($singleUserTag);
                }
            } else {
                $this->tagInsertToCustomer($user->id, $tag);
            }

            $tags[] = $userTag;

            $userTags = $request->has('tags') && !empty($request->tags) ? $request->tags : [];
            foreach ($userTags as $singleUserTag) {
                $tags[] = $singleUserTag;
            }

            $tags = array_filter($tags);
            if(!empty($tags))
            {
                if(!in_array('dt_user', $tags))
                {
                    $tags[] = 'drm_user';
                    $userTag = 'drm_user';
                } else {
                    $this->tagInsertToCustomer($user->id, 'dt_user', 28, 2439);
                    $userTag = 'dt_user';
                }

               $this->tagInsertToCustomer($user->id, $tags, 28, 2455);
               $this->assignUserTag($user->id, $userTag);
            }
            // Tag Insert end

            $groupId = in_array('dt_user', $tags) ? 2 : 1;

            // User group
            $this->assignUserGroup($user->id, $groupId);

            // Access category
            Option::create([
                'option_key' => 'sign_in_mp_category',
                'option_group' => 'sign_in_mp_category',
                'user_id' => $user->id,
                'option_value' => json_encode($mp_category),
            ]);
            // Mp category access end


            if(isset($request->group) && !empty($request->group))
            {
                Option::create([
                    'option_key' => 'sign_in_from_group',
                    'option_group' => 'sign_in_from_group',
                    'user_id' => $user->id,
                    'option_value' => $request->group,
                ]);
            }

            if(isset($request->aqua))
            {
                Option::create([
                    'option_key' => 'sign_in_from_aqua',
                    'option_group' => 'sign_in_from_aqua',
                    'user_id' => $user->id,
                    'option_value' => 1,
                ]);

               $this->assignUserTag($user->id, 'aqua');
            }

            //After login event create
            try {
                TrakerTimeSpent::create([
                    'user_id' => $user->id,
                    'href' => request()->url(),
                    'seconds' => 1,
                ]);
            } catch (Exception $exception) {
                Log::channel('command')->error('Register Traker Time insert Error:- ' . $exception->getMessage());
            }
            // Login event end


            try {
                $this->sendTwoFaEmail($user);
            } catch(Exception $e) {
                return response()->json([
                    'message' => $this->trans['mail_2fa_error'] .' '. $e->getMessage(),
                    'success' => true,
                    'type' => 'warning',
                    'id' => $user->id,
                ]);
            }


            // Send notification
            try {
                if(in_array('dt_user', $tags))
                {
                    $message = "New Droptienda user registered. {$user->name} - {$user->email}";
                    User::whereIn('id', [2455, 2439])->get()
                    ->each(function($userNotify) use ($message){
                        $userNotify->notify(new DRMNotification($message, 'DT_USER', '#'));
                    });
                }
            } catch(Exception $e) {}

            // Do accounting
            app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

            // Create test order
            $this->createTestOrder($user->id);

            return response()->json([
                'success' => true,
                'message' => $this->trans['account_created_successfully'],
                'id' => $user->id,
            ]);

        } catch (Exception $emn) {
            return response()->json([
                'success' => false,
                'message' => $emn->getMessage(),
            ], 400);
        }
    }



    // Supplier registration
    public function registerSupplier(Request $request)
    {


        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:cms_users',
            'phone' => 'required',
            'password'  => 'required|confirmed|min:8',
            'first_name' => 'required|min:2|max:40|string',
            'last_name' => 'required|min:2|max:40|string',
            "company" => 'nullable|max:100|string',
            "country" => 'required',
            "city" => 'required',
            "zip" => 'required',
            "street" => 'required',
            "vat_number" => 'nullable|max:40|string',
            'dropfunnel_tag' => 'sometimes|nullable',
            'tag' => 'sometimes|nullable',
            'account_type' => 'nullable',
        ],
        [
            'email.unique' => __('email_exists'),
        ]);

        $valid_slugs = ['relavida'];

        if ($validator->fails()) {
            return response()->json([
                'error_messages'         => $validator->errors()->all(),
                'errors' => $validator->errors()->messages(),
                'message' => $this->trans['invalid_data'],
            ], 422);
        }

        $slug = $request->slug ?? null;
        // Slug validate
        if(!empty($slug) && !in_array($slug, $valid_slugs)) {
            return response()->json([
                'error_messages' => [$this->trans['invalid_data']],
                'errors' => [
                    'email' => [$this->trans['invalid_data']],
                ],
                'message' => $this->trans['invalid_data'],
            ], 422);
        }


        try {
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($request->email);
        } catch (Exception $e) {
            return response()->json([
                'error_messages' => [$e->getMessage()],
                'errors' => [
                    'email' => [$e->getMessage()],
                ],
                'message' => $this->trans['invalid_data'],
            ], 422);
        }

        try {
            if(isset($request->phone) && !empty($request->phone))
            {
                $phone = $request->phone;
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($phone);
            }
        } catch (Exception $e) {
            return response()->json([
                'error_messages' => [$e->getMessage()],
                'errors' => [
                    'phone' => [$e->getMessage()],
                ],
                'message' => $this->trans['invalid_data'],
            ], 422);
        }

        try {

            // Vat checker
            $vat_number = $request->vat_number;
            if(!empty($vat_number) && $request->country != 83)
            {
                $vat_checker = \DRM::checkTaxNumber($vat_number);
                if(!$vat_checker['success']) throw new \Exception('Invalid VAT number. '.$vat_checker['message']);
            }

            // User data
            $firstName = $request->first_name;
            $lastName = $request->last_name;
            $password = Hash::make($request->password);
            $email = $request->email;

            // Billing Data
            $company_name = $request->company;
            $country_id = $request->country;

            $referred_by = null;
            if (!empty($request->referred_by)){
                $referred_by = DB::table('cms_users')->where('id', $request->referred_by)->value('id');
            }

            // Create user
            $user = User::create([
                'name'     => trim("{$firstName} {$lastName}"),
                'email'    => $email,
                'password' => $password,
                'id_cms_privileges' => 4,
                'status' => null,
                'referrer_id' => $referred_by,
                'ref_id' => Str::random(25),
                'two_factor_code' => rand(100999, 999999),
                'two_factor_expires_at' => now()->addMinutes(10),
                'user_theme' => $slug,
            ]);

            $userCreated = ($user && $user->id);
            if(!$userCreated) throw new Exception($this->trans['failed_to_create_account']);

            // Relavida user type
            if($slug === 'relavida')
            {
                DB::table('relavida_user_types')->insert([
                    'user_id' => $user->id,
                    'type' => $request->account_type,
                ]);
            }

            // Billing
            $user->billing_detail()->create([
                'country_id' => $country_id,
                'company_name' => $company_name,
                'email' => $user->email,
                'vat_id' => $vat_number,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'last_updated_at' => now(),
                'city' => $request->city,
                'zip' => $request->zip,
                'address' => $request->street,
                'phone' => $request->phone,
            ]);

            //Delivery company data
            $deliveryCompanyData = [
                'name'      => $user->name,
                'zip'       => $request->zip,
                'country_id'=> $country_id,
                'phone'     => $request->phone,
                'address'   => $request->street,
                'contact_name'  => $user->name,
                'note'          => 'Marketplace Supplier',
                'is_marketplace_supplier'   => 1,
                'supplier_id'   => $user->id,
                'state'         => $request->city,
            ];

            //Save delivery company data
            DeliveryCompany::updateOrCreate([
                'user_id' => \App\Enums\Apps::DROPMATIX_ID,
                'email'     => $user->email
            ], $deliveryCompanyData);


            // Agb Logfile
            try {
                AgbLogs::create([
                    'user_id' => $user->id,
                    'message' => $user->name . ' Account Created!',
                    'agb' => json_encode(['agb' => config('agb.user_registration')]),
                    'ip_address' => getIpAddress(),
                ]);
            } catch (Exception $th) {}
            // End Agb Logfile

            //Add tag to customer profile
            if($slug === 'relavida')
            {
                $this->tagInsertToCustomer($user->id, 'relavidaneuerhaendler', 4, 2455);
            } else {
                $this->tagInsertToCustomer($user->id, 'Anmeldung als Lieferant', 4, 2455);
                app(\App\Http\Controllers\RegistrationController::class)->assignUserTag($user->id, 'Anmeldung als Lieferant');
            }

            // Group assign
            $groupId = $slug === 'relavida' ? 3 : 4;
            app(\App\Http\Controllers\RegistrationController::class)->assignUserGroup($user->id, $groupId);

            //After login event create
            try {
                TrakerTimeSpent::create([
                    'user_id' => $user->id,
                    'href' => request()->url(),
                    'seconds' => 1,
                ]);
            } catch (Exception $exception) {
                Log::channel('command')->error('Register Traker Time insert Error:- ' . $exception->getMessage());
            }
            // Login event end

            try {
                $this->sendTwoFaEmail($user);
            } catch(Exception $e) {
                return response()->json([
                    'message' => $this->trans['mail_2fa_error'] .' '. $e->getMessage(),
                    'success' => true,
                    'type' => 'warning',
                    'id' => $user->id,
                ]);
            }

            // Do accounting
            app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

            // Create test order
            $this->createTestOrder($user->id);

            return response()->json([
                'success' => true,
                'message' => $this->trans['account_created_successfully'],
                'id' => $user->id,
            ]);

        } catch (Exception $emn) {
            return response()->json([
                'success' => false,
                'message' => $emn->getMessage(),
            ], 400);
        }
    }

    // Verify account
    public function verify(Request $request)
    {
        $request->validate([
            'user' => 'required',
            'code' => 'required|min:6',
        ],[
            'user' => 'Invalid action',
        ]);

        try {

            $user = User::where('id', $request->input('user'))
            ->where('two_factor_code', $request->input('code'))
            ->where('two_factor_expires_at', '>=', now())
            ->first();
            if(empty($user) || empty($user->id)) throw new Exception($this->trans['invalid_code']);

            if(!empty($user->email_verified_at)) throw new Exception($this->trans['invalid_action']);

            // Active account
            $token = rand(100999, 999999);
            DB::table('cms_users')->where('id', $user->id)
            ->update([
                'status' => 'Active',
                'email_verified_at' => now(),
                'two_factor_code' => $token,
                'two_factor_expires_at' => now()->addMinutes(10),
            ]);

            $isAqua = \App\Option::where([
                'option_key' => 'sign_in_from_aqua',
                'option_group' => 'sign_in_from_aqua',
                'user_id' => $user->id,
            ])->value('option_value');

            if (!empty($isAqua)) {
                $tranferable_ids = MarketplaceProducts::where('status', 1)
                    ->where('supplier_id', 4163)->where('brand', 12607)
                    ->pluck('id')->toArray();

                foreach( array_chunk($tranferable_ids, 500) as $tranferable_id ){
                    dispatch(new MarketplaceProductTransferToDrm($tranferable_id, $user->id, null, true))->onQueue('mp_product_trans'); //->onQueue('keepa');
                }
            }

            // Appointment initial data
            DB::table('takeappointment')
            ->insert([
                'user_id' => $user->id,
                'payment_date_for' => 1,
                'payment_date_remaining' => 1,
                'free_date' => 1,
            ]);

            AppointmentHistory::insert([
                'user_id' => $user->id,
                'previous' => 0,
                'current' => 1,
                'type' => 2,
                'mentor_type' => 2,
                'message' => 'Checklist progress bar appointment.',
                'created_at' => now(),
                'updated_at' => now()
            ]);
            // Appointment initial data end


            // Create server
            try {
                $this->runDtPipeline($user);
            } catch(Exception $em) {}

            try {
                $tags = [
                    'user_name' => $user->name,
                    'user_email' => $user->email,
                    'password_confirmation' => '*******'
                ];
                if($user->user_theme && $user->user_theme === 'relavida')
                {
                    $mail_data = app(\App\Http\Controllers\EmailVerificationController::class)->welcomeMailData($tags, 'relavida');
                    // Relavida SMTP
                    if( DB::table('user_mail_configurations')->where('user_id', 2817)->where('active', 1)->exists())
                    {
                        app('drm.mailer')->getMailer(2817)->to($user->email)->send(new DRMSEndMail($mail_data));
                    } else {
                        app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));
                    }
                }
            } catch(Exception $em) {}

            // Referrer Bonus Credit
            if($user->referrer_id && ($user->referrer_id == 2592) && $user->id_cms_privileges == 4){
                $tariffController = new tariffController();
                $status = \App\Enums\CreditType::CREDIT_ADD;

                // app('App\Http\Controllers\tariffController')->CreditUpdate($user->referrer_id, 250, 'credit_add');
                // app('App\Http\Controllers\tariffController')->drmUserCreditAdd($user->referrer_id, 250, 'Supplier Registration Bonus', 5, $status);

                (new RefillCredit)->refillBonusCredit($user->referrer_id, 250, \App\Services\Tariff\Credit\CreditType::SUPPLIER_REGISTRATION_BONUS);
            }

            $is_drm_user = app('App\Services\UserService')->findUserGroup($user->id, 1);

            // Trial Period Start During Registration for New User
            if (checkTariffEligibility($user->id) && $user->id_cms_privileges == 3) {
                $date = date("Y-m-d");
                DB::table('app_trials')->insert([
                    'user_id' => $user->id,
                    'app_id' => 0,
                    'trial_days' => !empty($isAqua) ? 365 : 14,
                    'start_date' => $date
                ]);

                // During trial period "50" credit will get user
                if($is_drm_user){
                    $credit_type = \App\Enums\CreditType::TRIAL_CREDIT;
                    $credit_amount = config('global.trial_credit_amount');
                    $status = \App\Enums\CreditType::CREDIT_ADD;

                    // app('App\Http\Controllers\tariffController')->CreditUpdate($user->id, $credit_amount, 'credit_add');
                    // app('App\Http\Controllers\tariffController')->drmUserCreditAdd($user->id, $credit_amount, 'Trial Credit', $credit_type, $status);

                    (new RefillCredit)->refillTariffCredit($user->id, $credit_amount, \App\Services\Tariff\Credit\CreditType::TRIAL_CREDIT);
                }
                // During trial period "50" credit will get user END

                app('App\Http\Controllers\AdminPipelineController')->registrationTimeDealCreation($user);
            }

            if ($is_drm_user) {
                $user_with_billing = User::with('billing_detail')->find($user->id);
                resolve(\App\Services\DropCampus\DropmatixCampus::class)->createDropmatixCampusUser($user_with_billing);
            }

            updateUserTopInfos($user->id, [	
                'import_plan_id' => 0, 
                'import_end_date' => \Carbon\Carbon::now()->addDays(14),
            ]);

            return response()->json([
                'success' => true,
                'message' => $this->trans['account_done'],
                'token' => $this->generateRedirectToken($user, $token),
            ]);
        } catch(Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    // Resend code
    public function verifyResend(Request $request)
    {
        try {
            $userId = $request->input('user');

            if(empty($userId)) throw new Exception($this->trans['invalid_action']);

            $user = User::where('id', $userId)->select('id', 'name', 'email', 'two_factor_code', 'two_factor_expires_at', 'email_verified_at')->first();
            if (empty($user) || empty($user->id)) throw new Exception($this->trans['invalid_action']);
            if(!empty($user->email_verified_at)) throw new Exception($this->trans['invalid_action']);

            User::where('id', $userId)
            ->update([
                'two_factor_code' => rand(100999, 999999),
                'two_factor_expires_at' => now()->addMinutes(10),
            ]);


            $user = User::where('id', $userId)->select('id', 'name', 'email', 'two_factor_code', 'two_factor_expires_at', 'email_verified_at', 'user_theme')->first();
            $this->sendTwoFaEmail($user);
            return response()->json([
                'success' => true,
                'message' => $this->trans['sent_2fa'],
                'user' => $user->id,
            ]);
        } catch(Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'user' => $user ? $user->id : null,
            ], 400);
        }
    }


    // send 2 Factor Email
    private function sendTwoFaEmail(User $user)
    {
        $tags = [
            'user_name' => $user->name,
            'user_email' => $user->email,
            'two_factor_code' => $user->two_factor_code
        ];

        if($user->user_theme && $user->user_theme === 'relavida')
        {
            $mail_data = app(\App\Http\Controllers\EmailVerificationController::class)->twoFactorMailData($tags, 'relavida');
            // Relavida SMTP
            if( DB::table('user_mail_configurations')->where('user_id', 2817)->where('active', 1)->exists())
            {
                app('drm.mailer')->getMailer(2817)->to($user->email)->send(new DRMSEndMail($mail_data));
            } else {
                app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));
            }

        } else {
            $slug = 'two_factor_varification';
            $lang = getUserSavedLang($user->email);
            $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
            app('drm.mailer')->getMailer()->to($user->email)->send(new DRMSEndMail($mail_data));
        }
    }


    //Redirect token
    private function generateRedirectToken(User $user, $token): string
    {
        $payload = json_encode(['id' => $user->id, 'key' => $token, 'email' => $user->email]);
        $token = DRM::stringEncryption($payload);
        return base64_encode($token);
    }


    public function assignUserGroup($userId, $groupId = 1)
    {
        \App\User::where('id', $userId)
            ->get()
            ->each(function($user) use ($groupId) {
                $user->groups()->sync([$groupId]);
            });
    }


    public function assignUserTag($userId, $tags)
    {
        if(!is_array($tags))
        {
            $tags = [$tags];
        }

        if(!is_array($tags)) return;

        $tags = array_unique($tags);

        foreach($tags as $tag)
        {
            DB::table('user_tags')->insert([
                'user_id' => $userId,
                'tag' => $tag,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }

    // DT sales pipeline
    public function runDtPipeline($user)
    {
        try {

            $exist = DB::table('user_tags')->where('user_id', $user->id)->where('tag', 'dt_user')->exists();
            if(!$exist) return;

            if(DB::table('user_tags')->where('user_id', $user->id)->where('tag', 'dt_newinstall')->exists()) return;

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => 'https://mgmt.droptienda.eu/api/v4/projects/7/trigger/pipeline',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => [
                    'token' => config('app.dt_pipeline_token'),
                    'ref' => 'main',
                    'variables[action]' => 'create',
                    'variables[email]' => $user->email,
                    'variables[cust_name]' => $user->name,
                ],
            ]);

            $response = curl_exec($curl);
            curl_close($curl);
            $res = json_decode($response, true) ?? [];

            if( !empty($res) && isset($res['id']) && isset($res['status']) && $res['status'] === 'created')
            {
                // create new tag
                $this->tagInsertToCustomer($user->id, 'dt_newinstall', 28, 2439);

                if(DB::table('shops')->where('user_id', $user->id)->where('channel', 10)->where('category', 8)->exists())
                {
                    $this->tagInsertToCustomer($user->id, 'test phase active', 28, 2439);
                }

                $this->assignUserTag($user->id, 'dt_newinstall');
            } else {
                throw new Exception("DT pipeline execution failed. User ID: {$user->id}, Name: {$user->name}, Email: {$user->email}. Error: {$response}");
            }
        } catch(Exception $e) {

            $message = $e->getMessage();
            $url = $user ? url('admin/users/detail/'.$user->id) : '#';

            User::whereIn('id', [2455, 2439])->get()
            ->each(function($userNotify) use ($message, $url){
                $userNotify->notify(new DRMNotification($message, 'DT_USER', $url));
            });
        }
    }


    // Create test order
    private function createTestOrder($user_id)
    {
        try {
            \App\Services\Customer\CustomerHelper::insertInitialTestOrder($user_id);
        } catch (Exception $e) {}
    }
}
