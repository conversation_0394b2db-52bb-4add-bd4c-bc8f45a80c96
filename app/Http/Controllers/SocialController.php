<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Hash;
use Validator, Redirect, Response, File;
use Socialite;
use App\User;
use Illuminate\Support\Facades\Session;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\DB;
use Request;
use App\Mail\DRMSEndMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use App\Traits\ProjectShare;
use ServiceKey;
use Illuminate\Support\Facades\Auth;

class SocialController extends Controller
{

    use ProjectShare;


    public function redirect($provider)
    {
        return Socialite::driver($provider)->redirect();
    }

    public function callback($provider)
    {


        try {
            $getInfo = Socialite::driver($provider)->stateless()->user();

            $users = User::where('email', $getInfo->email)->first();

            if ($users != null) {
                $this->userlogin($users);
                return redirect(CRUDBooster::adminPath());
            }

            if ($getInfo->email == null) {
                return redirect()->action('AdminDrmProjectsController@sign_in_get')->with('error_msg', 'Your Email is Empty!');
            }
            $password = Str::random(6);
            $usersxds = $this->createUser($getInfo, $provider, $password);

            // $postdata=[

            //     'name'=>$getInfo->name,
            //     'email'=>$getInfo->email,
            //     'password_confirmation'=>$password,

            // ];
            // app('drm.mailer')->getMailer()->to($getInfo->email)->send(new NewCustomers($postdata));

            // $tags = [
            //     'user_name' => $getInfo->name,
            //     'user_email' => $getInfo->email,
            //     'password_confirmation' => $password
            // ];
            // $slug = 'welcome_email';
            // $lang = getUserSavedLang($getInfo->email);
            // $mail_data = DRMParseMailTemplate($tags, $slug, $lang);
            // app('drm.mailer')->getMailer()->to($getInfo->email)->send(new DRMSEndMail($mail_data));


            $users = User::where('email', $getInfo->email)->first();
            $this->userlogin($users);
            return redirect(CRUDBooster::adminPath());

        } catch (\Exception $e) {
            return redirect()->action('AdminDrmProjectsController@sign_in_get')->with('error_msg', 'Something Went Wrong !');
        }

    }

    function createUser($user, $provider, $password)
    {

        $userValueInsert = DB::table('cms_users')->insertGetId([
            'name' => $user->name,
            'email' => $user->email,
            'remember_token' => $user->token,
            'id_cms_privileges' => 3,
            'provider' => $provider,
            'password' => Hash::make($password),
            'provider_id' => $user->id,
            'status' => 'Active',
            'created_at' => date('Y-m-d H:i:s')
        ]);

        $insert_appoinment_data = DB::table('takeappointment')->insert(
            ['user_id' => $userValueInsert, 'payment_date_for' => 1, 'payment_date_remaining' => 1]
        );

        return $userValueInsert;
    }


    public function userlogin($user)
    {
        $users = DB::table('cms_users')->where('id', $user->id)->first();
        if ($users->status) {

            // Login user
            authUserLogin($users->id);


            $priv = DB::table("cms_privileges")->where("id", $users->id_cms_privileges)->first();
            $roles = DB::table('cms_privileges_roles')->where('id_cms_privileges', $users->id_cms_privileges)->join('cms_moduls', 'cms_moduls.id', '=', 'id_cms_moduls')->select('cms_moduls.name', 'cms_moduls.path', 'is_visible', 'is_create', 'is_read', 'is_edit', 'is_delete')->get();
            $photo = ($users->photo) ? user_photo($users->photo) : asset('vendor/crudbooster/avatar.jpg');
            Session::put('admin_is_superadmin', $priv->is_superadmin);
            Session::put('admin_id', $users->id); // TODO:: USER_LOGIN
            Session::put('admin_is_superadmin', $priv->is_superadmin);
            Session::put('admin_is_developer', $priv->is_dev);
            Session::put('admin_name', $users->name);
            Session::put('admin_photo', $photo);
            Session::put('admin_privileges_roles', $roles);
            Session::put("admin_privileges", $users->id_cms_privileges);
            Session::put('admin_privileges_name', $priv->name);
            Session::put('marketplace_access', $users->marketplace_access);
            Session::put('admin_lock', 0);
            Session::put('theme_color', $priv->theme_color);
            Session::put("appname", CRUDBooster::getSetting('appname'));
            CRUDBooster::insertLog(trans("crudbooster.log_login", ['email' => $users->email, 'ip' => Request::server('REMOTE_ADDR')]));

            $isDtUser = DB::table('user_group_relations')
            ->where('user_id', CRUDBooster::myParentId())
            ->where('group_id', 2)
            ->exists();
            Session::put('is_dt_user', $isDtUser);

            $importPlanRecord = DB::table('purchase_import_plans')->where('cms_user_id', CRUDBooster::myParentId())->exists();
            Session::put('has_import_plan_record', $importPlanRecord);

            $cb_hook_session = new \App\Http\Controllers\CBHook;
            $cb_hook_session->afterLogin();
            return true;
        } else {
            return redirect(drm_login_url($user))->with('message', 'Your account has been suspended. Please contact our <NAME_EMAIL> to get a new activation.');
        }

    }

    public function getLoginFrame()
    {
        return view('iframe.login_full');
    }

    public function getLoginFrameLite()
    {
        return view('iframe.login');
    }

    public function getRegFrameLite()
    {
        $data['white'] = isset($_REQUEST['white']);
        return view('iframe.registration', $data);
    }

    public function getRegFrame()
    {
        $data['white'] = isset($_REQUEST['white']);
        return view('iframe.registration_full', $data);
    }

    public function getCustomerRegistrationScript(){

        $theme = $_GET['theme'] ?? '#fd6500';
        $color = $_GET['color'] ?? '#fff';
        $prefix = $_GET['prefix'] ?? '';

        if(ctype_xdigit($color) && strlen($color))
        {
            $color = '#'.$color;
        }

        $external = $_GET['external'] ?? 'no';

        $data = [
            'prefix' => $prefix,
            'theme' => $theme,
            'color' => $color,
            'external' => $external,
        ];


        return response()->view('customer_registration_script', $data)
            ->header('Content-Type', 'application/javascript');
    }
}
