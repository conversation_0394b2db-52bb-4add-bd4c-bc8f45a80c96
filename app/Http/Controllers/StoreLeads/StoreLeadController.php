<?php
namespace App\Http\Controllers\StoreLeads;

use Illuminate\Http\Request;
use DB;
use CRUDBooster;
use Illuminate\Support\Facades\Storage;
use League\Csv\Writer;
use Illuminate\Support\Facades\Cache;

// use PhpOffice\PhpSpreadsheet\Spreadsheet;
// use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

use App\Services\DRM\CurrencyApi;
use Exception;
use App\Services\WebshopAnalysisServices\WebshopAnalysisServices;
use App\Http\Controllers\AdminDrmImportsController;
use Illuminate\Support\Facades\Session;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Illuminate\Support\LazyCollection;
use App\Helper\DrmHelper;
use Illuminate\Support\Str;
use App\Models\Product\WebshopAnalysisReport;
use App\Models\Product\WebshopAnalysisReportData;
use App\Jobs\ProcessWebshopAnalysisReportJob;
use App\DrmUserCredit;

class StoreLeadController
{

	private WebshopAnalysisServices $webshopService;

	private $title = 'Webshop Analysis';

	const APP_ID = 93;

	private $appData;

	public function __construct(WebshopAnalysisServices $webshopService)
    {
        $this->webshopService = $webshopService;
    }

	public function index(Request $request)
	{
		if(!CRUDBooster::isDropmatixSupport()){
			$app = $this->permissionCheck();
		}

		$user_id = CRUDBooster::myParentId();

        $data = [];
        $data['limit'] = $limit = ($request->limit) ? $request->limit : 20;

		$query_limit = $app['query_limit'];
		$total_query = $this->userTotalQuery($user_id);
		$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);

		$used = $this->queryUsedInfo($total_query, $query_limit);
		$data['used_label'] = $used['used_label'];

        $result = DB::table('webshop_archives')
        	->where('user_id', '=', $user_id)
        	->select(
        		'id',
        		'name',
				'platform',
				'country_code',
				'location',
				'employee_count',
				'shipping_carriers',
				'platform_rank',
				'rank',
				'state',
				'created_date',
				'estimated_sales',
				'eur_estimated_sales',
        	);

    	$cols = [
    	    'id',
    		'name',
			'platform',
			'country_code',
			'location',
			'employee_count',
			'shipping_carriers',
			'platform_rank',
			'rank',
			'state',
			'created_at',
			'estimated_sales',
		];

		$sorted = false;

		//Sorting
		if(!empty($request->filter_column)){
			foreach($request->filter_column as $k => $col)
			{
				if(in_array($k, $cols))
				{
					$sort = $col['sorting'] ?? 'asc';

					if($k == 'created_at')
					{
						$result->orderByRaw('CAST(created_date AS datetime) '.$sort);
					}else {
						$result->orderBy($k, $sort);
					}

					$sorted = true;
				}
			}
		}

        // searching
        if($request->q){
            $q = trim($request->q);
            $result->where(function($r) use ($q) {
            	$r->where('name', 'like', '%'.$q.'%');
            });
        }

        if(!$sorted)
        {
        	$result->orderby('id', 'DESC');
        }


        $data['result'] = $result->paginate($limit);
        $data['page_title'] = '<i class="fa fa-glass"></i> ' . $this->title;

        $data['index_statistic'] = [];
        $data['columns'] = $this->colData();
        $data['button_selected'] = [];
        $data['button_filter'] = false;
        $data['button_bulk_action'] = false;
        $data['button_table_action'] = true;
        $data['mainPath'] = trim(route('drm.storelead::index'), '/');
        
        return view("admin.cp_analysis.storelead.index", $data);
	}

	public function show(Request $request)
	{
		if(!CRUDBooster::isDropmatixSupport()){
			$app = $this->permissionCheck();
		}

		$data = [];
		$data['page_title'] = $this->title;

		$app = $this->appData();
		$user_id = CRUDBooster::myParentId();
		$data['query_limit'] = $query_limit = $app['query_limit'];
		$data['total_query'] = $total_query = $this->userTotalQuery($user_id);
		if($query_limit > 0)
		{
			$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);
		}

		$used = $this->queryUsedInfo($total_query, $query_limit);
		if($used['limit_over'])
		{
			CRUDBooster::redirect(route('drm.storelead::index'), 'Your webshop analysis limit excedeed.!', 'warning');
		}

		$data['used_label'] = $used['used_label'];

		return view("admin.cp_analysis.storelead.show", $data);
	}

    //Domain compare result
    public function domainCompareResult(Request $request)
    {	
    	try{

	    	$data = [];
			$app = $this->appData();
			$user_id = CRUDBooster::myParentId();
			$query_limit = $app['query_limit'];
			$total_query = $this->userTotalQuery($user_id);

			$used = $this->queryUsedInfo($total_query, $query_limit);
			if($used['limit_over'])
			{
				throw new Exception('Your webshop analysis limit excedeed.!');
			}

			$has_vs = $request->vs && strlen($request->vs) > 3;

			$data['domain'] = $request->domain ? $this->webshopService->getQueryResult($request->domain, 'url', $user_id) : [];

			$data['vs'] = $has_vs ? $this->webshopService->getQueryResult($request->vs, 'url', $user_id) : [];

			if(empty($data['domain']) && !empty($request->seller))
			{
				$data['domain'] = $this->webshopService->getQueryResult($request->seller, 'name', $user_id);
			}

			$total_query = $this->userTotalQuery($user_id);
			if($query_limit > 0)
			{
				$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);
			}

			$used = $this->queryUsedInfo($total_query, $query_limit);

			$view_name = $has_vs ? "admin.cp_analysis.storelead.ajax.compare" : "admin.cp_analysis.storelead.ajax.single";
			$html = view($view_name, $data)->render();

			return response()->json([
				'success' => true,
				'html' => $html,
				'query_limit' => $query_limit,
				'total_query' => $total_query,
				'used_percentage' => $data['used_percentage'],
				'used_label' => $used['used_label'],
			]);

    	}catch(Exception $e)
    	{
			dd($e);
    		return response()->json([
    			'success' => false,
    			'message' => $e->getMessage(),
    		]);
    	}
    }

	public function showArchiveResult($id)
	{
		$data = [];
		$data['page_title'] = $this->title;
		$user_id = CRUDBooster::myParentId();

		$app = $this->appData();
		$query_limit = $app['query_limit'];
		$total_query = $this->userTotalQuery($user_id);
		$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);

		$used = $this->queryUsedInfo($total_query, $query_limit);
		$data['used_label'] = $used['used_label'];


		$row = DB::table('webshop_archives')
		->where('user_id', '=', $user_id)
		->where('id', '=', $id)
		->select(
			'name',
			'platform',
			'country_code',
			'location',
			'employee_count',
			'shipping_carriers',
			'platform_rank',
			'rank',
			'state',
			'alexa_rank',
			'cc_centrality',
			'cc_rank',
			'title',
			'description',
			'contact_info',
			'technologies',
			'screenshot',
			'created_date AS created_at',
			'id',
			'estimated_sales',
			'eur_estimated_sales',
			'theme',
		)
		->first();

		if(empty($row))
		{
			CRUDBooster::redirect(route('drm.storelead::index'), 'Invalid access!', 'warning');
		}

		$res = (array)$row;
		$res['contact_info'] = $res['contact_info'] ? json_decode($res['contact_info'], true) : [];
		$res['technologies'] = $res['technologies'] ? json_decode($res['technologies'], true) : [];
		$res['theme'] 		 = $res['theme'] ? json_decode($res['theme'], true) : [];

		$data['domain'] = $res;

		return view("admin.cp_analysis.storelead.archive", $data);
	}


    //Columns data
    private function colData()
    {
        $col = [];
        $col[] = ['label' => 'ID', "name" => 'id'];
        $col[] = ['label' => __('Merchant Name'), "name" => 'name'];
        $col[] = ['label' => __('Estimated_Sales_(monthly)'), "name" => 'estimated_sales'];
        $col[] = ['label' => __('Platform'), "name" => 'platform'];
        $col[] = ['label' => __('Country'), "name" => 'country_code'];
        $col[] = ['label' => __('Company Location'), "name" => 'location'];
        $col[] = ['label' => __('Employees'), "name" => 'employee_count'];
        $col[] = ['label' => __('Shipping Carriers'), "name" => 'shipping_carriers'];
        $col[] = ['label' => __('Store Rank'), "name" => 'rank'];
        $col[] = ['label' => 'Platform Rank', "name" => 'platform_rank'];
        $col[] = ['label' => __('status'), "name" => 'state'];
        $col[] = ['label' => __('Created'), "name" => 'created_at'];
        return $col;
    }


    private function permissionCheck()
    {
    	
        $user_id = CRUDBooster::myId();

    	if(CRUDBooster::isSupportAccount())
    	{
    		return CRUDBooster::redirectBack("Sorry, You don't have access!");
    	}

        $tariffEligibility = checkTariffEligibility($user_id);

        $csvIntervalPlanId = app_user_plan_id($user_id, config('global.webshop_analysis_app_id'));

        if(CRUDBooster::isDropmatixSupport()){
            return [
                'status' => true,
                'query_limit' => 10000
            ];
        }elseif($tariffEligibility && $csvIntervalPlanId >= 26){
            // $user_credit = DrmUserCredit::where('user_id', $user_id)->value('credit');
            $user_credit = (new \App\Services\Tariff\Credit\CreditService())->remainingCredit();
            return [
                'status' => true,
                'query_limit' => $user_credit
            ];
        }elseif(!$tariffEligibility && $csvIntervalPlanId){
            $app = $this->appData();
    	    if(!isset($app['deny'])) return $app;
        }

    	abort(404);
    }

    //User total query
    private function userTotalQuery($user_id)
    {
    	return DB::table('user_webshop_analysis')
    	->where('user_id', '=', $user_id)
    	->count();
    }

    //used percentage
    private function queryUsedPercentage($total_query, $query_limit)
    {
    	if($total_query >= $query_limit) return 100;

    	$percentage = (100 / $query_limit) * $total_query;
    	return $percentage > 100 ? 100 : $percentage;
    }


    private function queryUsedInfo($total_query, $query_limit)
    {
    	if($query_limit == -1) return [];

    	$data = [];

    	if($total_query >= $query_limit)
    	{
    		$total_query = $query_limit;
    	}

    	$data['used_label'] = 'Used: '.$total_query.'/'.$query_limit;
    	$data['limit_over'] = false;
    	return $data;
    }

	/*==================================
    ============ App data ==============
    ===================================*/
    //app data
    private function appData()
    {
    	if(!is_null($this->appData) && !CRUDBooster::isDropmatixSupport()) return $this->appData;

        $tariffEligibility = checkTariffEligibility(CRUDBooster::myParentId());

        if($tariffEligibility)
    	    $this->appData = $this->appDataDB();
        else
            $this->appData = $this->appDataDBOld();
        
    	return $this->appData;
    }

    //App data db
    private function appDataDBOld()
    {
    	$user_id = CRUDBooster::myParentId();
		$data = resolve('App\Services\DRM\AppHelper')->getUserApps($user_id);
		if(empty($data)) return ['deny' => true];

		$app = collect($data['all_apps'])->firstWhere('id', self::APP_ID);
		if(empty($app)) return ['deny' => true];

		if($app->plan_id)
		{
			return $this->planAccessLimit($app->plan_id);

		}else if($app->is_trial)
		{
			return [
				'query_limit' => 25,
				'archive_limit' => 0,
				'is_trial' => $app->is_trial,
				'remaining_days' => $app->remaining_days,
			];

		}else if($app->is_free || CRUDBooster::isDropmatixSupport())
		{
			return ['query_limit' => -1, 'archive_limit' => -1];
		}

		return ['query_limit' => 0, 'archive_limit' => 0, 'deny' => true];
    }

    //App data db
    private function appDataDB()
    {
    	$user_id = CRUDBooster::myParentId();

        $plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
        if($plan['plan'] == 'Trial' || $plan['plan'] == 'Free' || CRUDBooster::isDropmatixSupport()){
            return [
				'query_limit' => -1,
				'archive_limit' => -1,
				'is_trial' => true,
			];
        }
        elseif($plan['plan'] == 'Purchased'){
            return [
				'query_limit' => $plan['plan_limit'],
				'is_trial' => false,
			];
        }
		
		return ['query_limit' => 0, 'archive_limit' => 0, 'deny' => true];
    }

    //access limit
    private function planAccessLimit($plan_id)
    {
    	if($plan_id == 53 || $plan_id == 49){
    		return ['query_limit' => 250, 'archive_limit' => 25];
    	}elseif($plan_id == 54 || $plan_id == 50) {
    		return ['query_limit' => 1000, 'archive_limit' => 100];
    	}elseif($plan_id == 55 || $plan_id == 51) {
    		return ['query_limit' => 5000, 'archive_limit' => 500];
    	}elseif($plan_id == 56 || $plan_id == 52) {
    		return ['query_limit' => -1, 'archive_limit' => 2000];
    	}

    	return ['query_limit' => 0, 'archive_limit' => 0];
    }

	public function import(Request $request){

        $userId = CRUDBooster::myParentId();

        $data = [];
        if ($request->get('file') && !$request->get('import')) {
            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $type = pathinfo($file, PATHINFO_EXTENSION);

            $import = new AdminDrmImportsController;
            $rows = $import->csvToArray($file, $type, 'auto', false);

            $countRows = ($rows) ? count($rows) : 0;

            Session::put('total_data_import', $countRows);

            $data_import_column = [];
            foreach ($rows as $value) {
                $a = [];
                foreach ($value as $k => $v) {
                    $a[] = $k;
                }
                if ($a && count($a)) {
                    $data_import_column = $a;
                }
                break;
            }

            $table_columns = [
                'query',
            ];

            $labels = [
                0 => __('URL/Seller')
            ];

            $data['table_columns'] = $table_columns;
            $data['data_import_column'] = $data_import_column;
            $data['labels'] = $labels;
        }

        $data['page_title'] = 'Store Lead Search Import';
        return view("admin.cp_analysis.storelead.import-query-file", $data);
    }

    public function uploadCsv(Request $request){

        if ($request->hasFile('userfile')) {

            try{

                $extensions = array("xls","xlsx","csv");
                $file = $request->file('userfile');
                $ext = $file->getClientOriginalExtension();
                if(!in_array($ext, $extensions)){
                    $url = route("drm.storelead::import");

                    return CRUDBooster::redirect($url, "Please Upload a Valid File");
                } 

                $filePath = 'uploads/'.CRUDBooster::myParentId().'/'.date('Y-m');
                Storage::makeDirectory($filePath);

                $filename = md5(Str::random(5)).'.'.$ext;
                $url_filename = '';
                if (Storage::putFileAs($filePath, $file, $filename)) {
                    $url_filename = $filePath.'/'.$filename;
                }
				$report_name = $request->get('report_name');
                $url = route("drm.storelead::import").'?file='.base64_encode($url_filename). '&report_name=' . $report_name;

                return redirect($url);
            } catch(Exception $e){
                $url = route("admin.cp_analysis.storelead.import-query-file");

                return CRUDBooster::redirect($url, "Please Upload a Valid File");
            }
        } else {
            return redirect()->back();
        }
    }

    public function done_import(Request $request){
        $data['page_title'] = trans('crudbooster.import_page_title', ['module' => "Import Products"]);
        Session::put('select_column', $request->get('select_column'));

        return view('admin.cp_analysis.storelead.import-query-file', $data);
    }

    public function do_import_chunk(Request $request)
    {
        try{
            $file_md5 = md5($request->get('file'));

            if ($request->get('file') && $request->get('action_type') != 0) {
                $total = Session::get('total_data_import');
                $prog = $total > 0 ? intval(Cache::get('success_' . $file_md5)) / $total * 100 : 0;
                $prog = round($prog, 2);
                if ($prog >= 100) {
                    Cache::forget('success_' . $file_md5);
                }

                return response()->json(['progress' => $prog, 'last_error' => Cache::get('error_' . $file_md5)]);
            }

            $select_column = Session::get('select_column');
            $select_column = array_filter($select_column);

            $table_columns = [
                'query'
            ];

            $file = base64_decode($request->get('file'));
            $file = storage_path('app/' . $file);

            $user_id = CRUDBooster::myParentId();
            $type = pathinfo($file, PATHINFO_EXTENSION);
            $import = new AdminDrmImportsController;
            $rows = $this->csvReader($file, $type, 'auto', false);

            $count = 0;

            $queryList = [];

			$report_name = $request->get('amp;report_name');

			$report = WebshopAnalysisReport::create([
				'user_id' => $user_id,
				'report_name' => $report_name
			]);

            foreach ($rows as $row) {
                $count++;
				
                if($request->get('action_type') != 0){
                    Cache::put('success_' . $file_md5, $count);
                }
                $row = (object) $row;
                foreach ($select_column as $csvColName => $val) {

                    $colname = $table_columns[$csvColName];

                    $data[$colname] = $row->$val;

					WebshopAnalysisReportData::updateOrCreate([
                        'query' => $row->$val,
						'report_id' => $report->id
						],
						[]
                    );
                }

				$data['report_id'] = $report->id;

                $queryList[] = $data;

            }

			ProcessWebshopAnalysisReportJob::dispatch($report->id, $user_id);
        
            return response()->json(['status' => true, 'message' => $res['message']]);
        }catch (Exception $e) {
			dd($e);
            return response()->json(['status' => false, 'message' => "Sorry, something went wrong"]);
        }
    }

	public function csvReader($csv, $type, $delimiter, $deleteFile = true)
    {
        ini_set('max_execution_time', '0'); // for infinite time of execution
        ini_set('memory_limit', -1);
        $paths = explode(';', $csv);
        $key = null;
        $key_count = 0;
        $array = array();
        $rand = Str::random(40);
        foreach ($paths as $path) {
            if ($deleteFile) {
                $path = Storage::disk('spaces')->url($path);
                $file_type = pathinfo($path, PATHINFO_EXTENSION);
                $file = file_get_contents($path);
                file_put_contents($rand . '.' . $file_type, $file);
                $localpath = $rand . '.' . $file_type;
            } else {
                $localpath = $path;
            }
            if ($type == 'csv' || $type == 'txt') {
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                $reader->setInputEncoding('UTF-8');

                if ($delimiter != 'auto') {
                    $reader->setDelimiter($delimiter);
                }
                $spreadsheet = $reader->load($localpath);
            } else {
                $spreadsheet = IOFactory::load($localpath);
            }

            // Convert all cell values to string
            $spreadsheet->getActiveSheet()
                ->getStyle('A1:'.$spreadsheet->getActiveSheet()->getHighestDataColumn().$spreadsheet->getActiveSheet()->getHighestDataRow())
                ->getNumberFormat()
                ->setFormatCode(\PhpOffice\PhpSpreadsheet\Style\NumberFormat::FORMAT_TEXT);

            $spreadsheetArray = $spreadsheet->getActiveSheet()->toArray(null, true, true, true);
            $collection = LazyCollection::make($spreadsheetArray);

            if ($key == null) {
                $key = array_map('trim', $collection->first());
                $key_count = count($key);
            }
            $key = array_map('removeDots', $key);
            $collection = $collection->except(1);
            foreach ($collection as $row) {
                if (count($row) == $key_count && !containsOnlyNull($row)) {
                    $array[] = array_combine($key, $row);
                }

            }

            if (!pathIsUrl($path) && $deleteFile) {
                unlink($localpath);
            }
        }
        return $array;
    }

	public function report_list(Request $request){

		$user_id = CRUDBooster::myParentId();

        $sorting = null;
        $order_by = null;

        $sort_filter = $request->get('filter_column') ?? [];
        if(!empty($sort_filter))
        {
            $order_by = key($sort_filter);
            $sorting = $sort_filter[$order_by]['sorting'];
        }

        $search_by_field = $request->get('search_by_field_column');
        $filters = $request->all();

        $reports = WebshopAnalysisReport::where('user_id', $user_id);

        if($order_by && $sorting){
            $reports->orderBy($order_by, $sorting);
        }

        if(!empty($search_by_field) && !empty($filters['q'])){
            $q = $filters['q'];
            $q = "%$q%";

            $reports->where(function($p) use ($q) {
				$p->where('report_name', 'LIKE', $q);
			});
        }

        $limit = $request->get('limit') ?? 20;
		$reports = $reports->orderBy('id','desc')->paginate($limit);

        $table_column = [
            'id'                    => ["label" => __("ID") , "sorting" => true],
            'report_name'           => ["label" => __('Report Name') , "sorting" => true],
            'Status'                => ["label" => __('Status') , "sorting" => false],
        ];

		$report_collection = $reports->getCollection()
            ->map(function($item) use ($channels_list, $channel_color, $search_by_channel , $saved_column, $user_id) {

                $row  = [
                    'id' => $item->id,
                    'report_name' => $item->report_name ?? "Report #" . $item->id,
                    'status' => $item->completed ? "Complete" : "In Progress",
                ];

                return $row;

			});

		$data = [];
        $reports->setCollection($report_collection);
        $reportIds = $reports->pluck('id')->toArray();
        $data['products'] = $reports;
        $data['product_ids'] = $reportIds;

        $data['columns'] = $table_column;

        $data['all_columns'] = $table_column;

        $data['languageId'] = app('App\Services\UserService')->getProductCountry($user_id);
        $data['lang'] = app('App\Services\UserService')->getProductLanguage($data['languageId']);

        $data['user_id'] = $user_id;
		return view('admin.cp_analysis.storelead.report-index', $data);

	}

	public function delete_report( Request $request){
		$user_id = CRUDBooster::myParentId();
        $report_ids = $request->report_ids;
        $isCheckAll = $request->checkAll;
        if($isCheckAll == 'true'){
            WebshopAnalysisReport::where('user_id', $user_id)->delete();
        }
        else{
            WebshopAnalysisReport::where('user_id', $user_id)->whereIn('id', $report_ids)->delete();
        }

        return response()->json([
            'success' => true,
        ]);
	}

	public function report_list_data(Request $request){
		$user_id = CRUDBooster::myParentId();
		$report_id = $request->get('report_id');

		$products = WebshopAnalysisReportData::where('report_id', $report_id);

		$filters = $request->all();

		if(!empty($filters['q'])){
            $q = $filters['q'];
            $q = "%$q%";

            $products->where(function($p) use ($q) {
				$p->where('query', 'LIKE', $q);
			});
        }

		$limit = $request->get('limit') ?? 20;
		$products = $products->orderBy('id','desc')->paginate($limit);

		$product_collection = $products->getCollection()
            ->map(function($item) {

                $row  = $this->webshopService->formatData($item, true);
				$row['query'] = $item->query;

                return $row;

			});

		$data = [];
        $products->setCollection($product_collection);
		$data['products'] = $products;

		return view('admin.cp_analysis.storelead.report_data.report-index', $data);
	}

    public function getSearchResults(Request $request){
        $search_term = $request->get('search');

        $search_results = $this->webshopService->getSearchResult($search_term);
        
        return response()->json([
            'success' => true,
            'results' => $search_results
        ]);
    }

    public function getDomainScreenShot(Request $request){
        try{
            $domain = $request->get('domain');

            $ss = $this->webshopService->domainImage($domain);

            return response()->json([
                'success' => true,
                'ss' => $ss
            ]);
        }catch(Exception $e){
            return response()->json([
                'success' => false,
            ]);
        }
    }

    //Table action
    public function actionSelected(Request $request)
    {
    	$id_selected = $request->input('checkbox');
    	$button_name = $request->input('button_name');

    	if(empty($id_selected))
    	{
    		CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans("crudbooster.alert_select_a_data"), 'warning');
    	}

    	try {
            if ($button_name == 'csv_download') {
            	return $this->csvDownload($id_selected);
            } elseif ($button_name == 'delete') {
            	return $this->webshopService->deleteArchived($id_selected);
            }
        } catch (Exception $e) {
            return redirect()->back()->with(['message_type' => 'error', 'message' => $e->getMessage()]);
        }

    	return redirect()->back();
    }


    //Download CSV
    private function csvDownload($item_ids)
    {
        $file = new \SplTempFileObject();
        $file->setFlags(\SplFileObject::READ_CSV);
        $file->setCsvControl(',');
        $csv = Writer::createFromFileObject($file);

        $rows = DB::table('webshop_archives')
        	->where('user_id', '=', CRUDBooster::myParentId())
        	->whereIntegerInRaw('id', $item_ids)
        	->select(
        		'name',
				'platform',
				'country_code',
				'location',
				'employee_count',
				'shipping_carriers',
				'platform_rank',
				'rank',
				'state',
				'created_date',
				'eur_estimated_sales',
				'screenshot',
        	)
        	->get();

        $header = ['Merchant Name', "Estimated Sales", "Platform", "Country", "Company Location", "Employees", "Shipping Carriers", "Store Rank", "Platform Rank", 'Status', 'Created', 'Screenshot'];
        $csv->insertOne($header);
        foreach($rows as $row)
        {
        	$csv_row = [];
        	$csv_row[] = $row->name;
        	$csv_row[] = $row->eur_estimated_sales;
        	$csv_row[] = $row->platform;
        	$csv_row[] = $row->country_code;
        	$csv_row[] = $row->location;
        	$csv_row[] = $row->employee_count;
        	$csv_row[] = $row->shipping_carriers;
        	$csv_row[] = $row->rank;
        	$csv_row[] = $row->platform_rank;
        	$csv_row[] = $row->state;
        	$csv_row[] = $row->created_date;
        	$csv_row[] = $row->screenshot;
        	$csv->insertOne($csv_row);
        }
        $csv->output('webshoop_data.csv');
        die;
    }

    

}