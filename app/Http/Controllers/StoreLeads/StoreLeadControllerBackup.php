<?php
namespace App\Http\Controllers\StoreLeads;

use Illuminate\Http\Request;
use DB;
use CRUDBooster;
use Illuminate\Support\Facades\Storage;
use League\Csv\Writer;
use Illuminate\Support\Facades\Cache;

// use PhpOffice\PhpSpreadsheet\Spreadsheet;
// use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

use App\Services\DRM\CurrencyApi;
use Exception;

class StoreLeadControllerBackup
{
	private $title = 'Webshop Analysis';

	const APP_ID = 93;

	private $appData;

	public function index(Request $request)
	{
		if(!CRUDBooster::isDropmatixSupport()){
			$app = $this->permissionCheck();
		}

		$user_id = CRUDBooster::myParentId();

        $data = [];
        $data['limit'] = $limit = ($request->limit) ? $request->limit : 20;

		$query_limit = $app['query_limit'];
		$total_query = $this->userTotalQuery($user_id);
		$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);

		$used = $this->queryUsedInfo($total_query, $query_limit);
		$data['used_label'] = $used['used_label'];

        $result = DB::table('webshop_archives')
        	->where('user_id', '=', $user_id)
        	->select(
        		'id',
        		'name',
				'platform',
				'country_code',
				'location',
				'employee_count',
				'shipping_carriers',
				'platform_rank',
				'rank',
				'state',
				'created_date',
				'estimated_sales',
				'eur_estimated_sales',
        	);

    	$cols = [
    	    'id',
    		'name',
			'platform',
			'country_code',
			'location',
			'employee_count',
			'shipping_carriers',
			'platform_rank',
			'rank',
			'state',
			'created_at',
			'estimated_sales',
		];

		$sorted = false;

		//Sorting
		if(!empty($request->filter_column)){
			foreach($request->filter_column as $k => $col)
			{
				if(in_array($k, $cols))
				{
					$sort = $col['sorting'] ?? 'asc';

					if($k == 'created_at')
					{
						$result->orderByRaw('CAST(created_date AS datetime) '.$sort);
					}else {
						$result->orderBy($k, $sort);
					}

					$sorted = true;
				}
			}
		}

        // searching
        if($request->q){
            $q = trim($request->q);
            $result->where(function($r) use ($q) {
            	$r->where('name', 'like', '%'.$q.'%');
            });
        }

        if(!$sorted)
        {
        	$result->orderby('id', 'DESC');
        }


        $data['result'] = $result->paginate($limit);
        $data['page_title'] = '<i class="fa fa-glass"></i> ' . $this->title;

        $data['index_statistic'] = [];
        $data['columns'] = $this->colData();
        $data['button_selected'] = [];
        $data['button_filter'] = false;
        $data['button_bulk_action'] = false;
        $data['button_table_action'] = true;
        $data['mainPath'] = trim(route('drm.storelead::index'), '/');
        
        return view("admin.cp_analysis.storelead.index", $data);
	}

	public function showArchiveResult($id)
	{
		$data = [];
		$data['page_title'] = $this->title;
		$user_id = CRUDBooster::myParentId();

		$app = $this->appData();
		$query_limit = $app['query_limit'];
		$total_query = $this->userTotalQuery($user_id);
		$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);

		$used = $this->queryUsedInfo($total_query, $query_limit);
		$data['used_label'] = $used['used_label'];


		$row = DB::table('webshop_archives')
		->where('user_id', '=', $user_id)
		->where('id', '=', $id)
		->select(
			'name',
			'platform',
			'country_code',
			'location',
			'employee_count',
			'shipping_carriers',
			'platform_rank',
			'rank',
			'state',
			'alexa_rank',
			'cc_centrality',
			'cc_rank',
			'title',
			'description',
			'contact_info',
			'technologies',
			'screenshot',
			'created_date AS created_at',
			'id',
			'estimated_sales',
			'eur_estimated_sales',
			'theme',
		)
		->first();

		if(empty($row))
		{
			CRUDBooster::redirect(route('drm.storelead::index'), 'Invalid access!', 'warning');
		}

		$res = (array)$row;
		$res['contact_info'] = $res['contact_info'] ? json_decode($res['contact_info'], true) : [];
		$res['technologies'] = $res['technologies'] ? json_decode($res['technologies'], true) : [];
		$res['theme'] 		 = $res['theme'] ? json_decode($res['theme'], true) : [];

		$data['domain'] = $res;

		return view("admin.cp_analysis.storelead.archive", $data);
	}

	public function show(Request $request)
	{
		if(!CRUDBooster::isDropmatixSupport()){
			$app = $this->permissionCheck();
		}

		$data = [];
		$data['page_title'] = $this->title;

		$app = $this->appData();
		$user_id = CRUDBooster::myParentId();
		$data['query_limit'] = $query_limit = $app['query_limit'];
		$data['total_query'] = $total_query = $this->userTotalQuery($user_id);
		if($query_limit > 0)
		{
			$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);
		}

		$used = $this->queryUsedInfo($total_query, $query_limit);
		if($used['limit_over'])
		{
			CRUDBooster::redirect(route('drm.storelead::index'), 'Your webshop analysis limit excedeed.!', 'warning');
		}

		$data['used_label'] = $used['used_label'];

		return view("admin.cp_analysis.storelead.show", $data);
	}

	public function compare(Request $request)
	{
		if(!CRUDBooster::isDropmatixSupport()){
			$app = $this->permissionCheck();
		}

		$data = [];
		$data['page_title'] = $this->title;

		$app = $this->appData();
		$user_id = CRUDBooster::myParentId();
		$data['query_limit'] = $query_limit = $app['query_limit'];
		$data['total_query'] = $total_query = $this->userTotalQuery($user_id);
		if($query_limit > 0)
		{
			$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);
		}

		$used = $this->queryUsedInfo($total_query, $query_limit);
		if($used['limit_over'])
		{
			CRUDBooster::redirect(route('drm.storelead::index'), 'Your webshop analysis limit excedeed.!', 'warning');
		}
		$data['used_label'] = $used['used_label'];

		return view("admin.cp_analysis.storelead.compare", $data);
	}

	private function currencyFormat($num) {
	    if($num > 999 && $num < 1000000){
	        return $this->priceRange([($num/1000), 'K']); // convert to K for number from > 1000 < 1 million 
	    }else if($num > 1000000){
	        return $this->priceRange([($num/1000000), 'M']); // convert to M for number from > 1 million 
	    }else if($num < 900){
	        return $this->priceRange([$num, '']); // if value < 1000, nothing to do
	    }
	}

	private function priceRange($data) {
		$amount = $data[0];
		$k = $data[1];
		$range = 5;
		$amount = $amount >= 100 ? 100 : $amount;
		$low = ($amount / $range);
		$c = (int)$low;
		$c = ($amount % $range === 0)? $c : (++$c);

		$h = $c * $range;
		$l = $h - $range;
		$l = $l < 1 ? 1 : $l;
		return '$'.$l.$k.'-'.'$'.$h.$k;
	}


	private function apiData($url)
	{
		$host = $this->hostNameFromDomain($url);
		$domains = $this->hostVarients($host);
		$token = env('STORELEAD_TOKEN');

		foreach($domains as $domain)
		{
			$archive = $this->dbArchiveResult($domain);
			if($archive) {
				$this->insertUserRecordByArchiveId($archive['id']);
				return $archive;
			}

			$curl = curl_init();
			curl_setopt_array($curl, array(
			  CURLOPT_URL => 'https://storeleads.app/json/api/v1/all/domain/'.$domain,
			  CURLOPT_RETURNTRANSFER => true,
			  CURLOPT_ENCODING => '',
			  CURLOPT_MAXREDIRS => 10,
			  CURLOPT_TIMEOUT => 0,
			  CURLOPT_FOLLOWLOCATION => true,
			  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
			  CURLOPT_CUSTOMREQUEST => 'GET',
			  CURLOPT_HTTPHEADER => array(
			    'Accept: application/json',
			    'Content-Type: application/json',
			    'Authorization: Bearer '.$token,
			  ),
			));

			$response = curl_exec($curl);

			curl_close($curl);
			$res = json_decode($response, true);

			$data = [];
			if(!empty($res) && !empty($res['domain']))
			{
				$data = $res['domain'];
				$domain_name = $data['name'];
				$data['screenshot'] = $this->domainImage($domain_name);
				$data['categories'] = $data['categories'] ? implode(' > ', $data['categories']) : null;
				// $data['annual_sales'] = $data['estimated_sales'] ? $this->currencyFormat($data['estimated_sales'] * 12) : null;
				$data['shipping_carriers'] = $data['shipping_carriers'] ? implode(', ', $data['shipping_carriers']) : null;
				$data['created_date'] = $data['created_at'];
				$data['eur_estimated_sales'] = $this->eurSalesEstimate($data['estimated_sales']);

				//Replace microweber
				if( $data['platform'] && strtolower($data['platform']) == 'microweber' )
				{
					$data['platform'] = 'droptienda';
				}

				$this->insertDomainToArchive($data, $res);
				$this->insertUserRecordByDomainName($domain_name);
			}

			return $data;
		}
	}

	//Domain varients
	private function hostVarients($host)
	{
		$domains = [$host];
		$domains[] = str_starts_with($host, 'www.') ? str_replace('www.', '', $host) : 'www.'.$host;

		return $domains;
	}


	//DB archive result
	private function dbArchiveResult($domain)
	{
		$host = $this->hostNameFromDomain($domain);
		if(empty($host)) return;
		$date = now()->subDays(4);

		$rows = DB::table('webshop_analysis')
		->where('name', 'LIKE', $host)
		->where('created_at', '>=', $date);

		//Full domain search
		if(!str_starts_with($host, 'www.')){
			$full_host = 'www.'.$host;
			$rows->orWhere('name', 'LIKE', $full_host);
		}

		$rows = $rows->select(
			'name',
			'platform',
			'country_code',
			'location',
			'employee_count',
			'shipping_carriers',
			'platform_rank',
			'rank',
			'state',
			'alexa_rank',
			'cc_centrality',
			'cc_rank',
			'title',
			'description',
			'contact_info',
			'technologies',
			'screenshot',
			'created_date AS created_at',
			'id',
			'estimated_sales',
			'eur_estimated_sales',
			'theme',
		)
		->get();

		$res = null;
		if($rows)
		{
			$res = $rows->count() > 1 ? $rows->firstWhere('name', $host) : $rows->first();
		}

		if($res) {
			$res = (array)$res;
			$res['contact_info'] = $res['contact_info'] ? json_decode($res['contact_info'], true) : [];
			$res['technologies'] = $res['technologies'] ? json_decode($res['technologies'], true) : [];
			$res['theme'] 		 = $res['theme'] ? json_decode($res['theme'], true) : [];
			return $res;
		}
	}


	//parse host from domain
	private function hostNameFromDomain($domain)
	{	
		$domain = trim($domain);
		if(!str_starts_with($domain, 'http'))
		{
			$domain = 'http://'.$domain;
		}

		return parse_url($domain, PHP_URL_HOST);
	}

	private function insertDomainToArchive($data, $res)
	{
		$columns = [
			'name',
			'platform',
			'country_code',
			'location',
			'employee_count',
			'shipping_carriers',
			'platform_rank',
			'rank',
			'state',
			'alexa_rank',
			'cc_centrality',
			'cc_rank',
			'title',
			'description',
			'contact_info',
			'technologies',
			'screenshot',
			'created_date',
			'estimated_sales',
			'eur_estimated_sales',
			'theme',
		];

		$row = collect($data)->only($columns)->toArray();

		$row['created_at'] = now();
		$row['updated_at'] = now();

		if($row['contact_info'])
		{
			$row['contact_info'] = json_encode($row['contact_info']);
		}

		if($row['technologies'])
		{
			$row['technologies'] = json_encode($row['technologies']);
		}

		$row['response'] = json_encode($res);

		if($row['theme'])
		{
			$row['theme'] = json_encode($row['theme']);
		}

		DB::table('webshop_analysis')->updateOrInsert([
			'name' => $row['name'],
		], $row);
	}

	//Insert user record by domain name
	private function insertUserRecordByDomainName($domain_name)
	{
		$archive_id = DB::table('webshop_analysis')->where('name', $domain_name)->value('id');
		if($archive_id)
		{
			$this->insertUserRecordByArchiveId($archive_id);
		}
	}

	//Insert user record by archive id
	private function insertUserRecordByArchiveId($archive_id)
	{
		$user_id = CRUDBooster::myParentId();
		
		DB::table('user_webshop_analysis')->insert([
			'webshop_analysis_id' => $archive_id,
			'user_id' => $user_id,
			'created_at' => now(),
			'updated_at' => now(),
		]);

		$this->insertToUserArchive($user_id, $archive_id);
	}


	//Delete archive items
	private function insertToUserArchive($user_id, $archive_id)
	{
		$app = $this->appData();
		$archiveLimit = $app['archive_limit'];

		if($archiveLimit == 0)
		{
			DB::table('webshop_archives')->where('user_id', '=', $user_id)->delete();
			return;
		}


		$exists_on_archive = DB::table('webshop_archives')
		->where('user_id', '=', $user_id)
		->where('webshop_analysis_id', '=', $archive_id)
		->exists();

		if(!$exists_on_archive)
		{
			$domain = DB::table('webshop_analysis')->where('id', '=', $archive_id)->first();
			if($domain)
			{
				$domain_data = collect($domain)->except(['id', 'created_at', 'updated_at'])->toArray();
				$domain_data['created_at'] = $domain_data['updated_at'] = now();
				$domain_data['user_id'] = $user_id; 
				$domain_data['webshop_analysis_id'] = $archive_id;
				DB::table('webshop_archives')->insert($domain_data);
			}
		}

		if($archiveLimit == -1) return;

		usleep(200);
		$prev_id =  DB::table('webshop_archives')
        ->orderBy('id', 'DESC')
        ->where('user_id', '=', $user_id)
        ->offset($archiveLimit)
        ->limit(1)
        ->value('id');

	    if($prev_id)
	    {
	        DB::table('webshop_archives')
	        ->where('user_id', '=', $user_id)
	        ->where('id', '<=', $prev_id)
	        ->delete();
	    }
	}

	public function domainImage($domain_name)
    {
    	$url = 'http://'.$domain_name;
    	$screen_shot_name = 'webshop_'.$domain_name;
        $image_name = 'tmp/' . $screen_shot_name . '.png';
        $cloud_path = 'webshop_screenshots/' . $screen_shot_name . '.png';

        try {

            if (empty($url)) throw new \Exception('Empty url');
            $chrome_app = env('CHROME_URL', null);
            if (empty($chrome_app)) throw new \Exception('No app specified!');
            $chrome = new \App\Services\PHPChrome\Chrome($url, $chrome_app);

            $chrome->setArgument('--enable-viewport', true);
            $chrome->setArgument('--enable-headless', true);
            $chrome->setArgument('--enable-incognito', true);
            $chrome->setArgument('--virtual-time-budget', 30000);

        	$chrome->setWindowSize(1024, 768);
            $image_url = $chrome->getScreenShot(public_path($image_name));

            if (is_null($image_url) || empty($image_url)) throw new \Exception('Failed');

            $image = file_get_contents($image_url);
            @unlink($image_url);
            Storage::disk('spaces')->put($cloud_path, $image, 'public');

            return Storage::disk('spaces')->url($cloud_path);

        } catch (\Exception $e) {}
    }

    //Domain single result
    public function domainSingle(Request $request)
    {
    	try {

    		$app = $this->appData();
			$user_id = CRUDBooster::myParentId();
			$query_limit = $app['query_limit'];

	    	$data = [];

	    	$total_query = $this->userTotalQuery($user_id);
			$used = $this->queryUsedInfo($total_query, $query_limit);

			if($used['limit_over'])
			{
				throw new Exception('Your webshop analysis limit excedeed.!');
			}

			$data['domain'] = $request->domain ? $this->apiData($request->domain, ) : [];

			$total_query = $this->userTotalQuery($user_id);
			if($query_limit > 0)
			{
				$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);
			}

			$used = $this->queryUsedInfo($total_query, $query_limit);

			$html = view("admin.cp_analysis.storelead.ajax.single", $data)->render();

			return response()->json([
				'success' => true,
				'html' => $html,
				'query_limit' => $query_limit,
				'total_query' => $total_query,
				'used_percentage' => $data['used_percentage'],
				'used_label' => $used['used_label'],
			]);

    	}catch(Exception $e) {
    		return response()->json([
    			'success' => false,
    			'message' => $e->getMessage(),
    		]);
    	}
    }

    //Domain compare result
    public function domainCompareResult(Request $request)
    {	
    	try{

	    	$data = [];
			$app = $this->appData();
			$user_id = CRUDBooster::myParentId();
			$query_limit = $app['query_limit'];
			$total_query = $this->userTotalQuery($user_id);

			$used = $this->queryUsedInfo($total_query, $query_limit);
			if($used['limit_over'])
			{
				throw new Exception('Your webshop analysis limit excedeed.!');
			}

			$has_vs = $request->vs && strlen($request->vs) > 3;

			$data['domain'] = $request->domain ? $this->apiData($request->domain) : [];
			$data['vs'] = $has_vs ? $this->apiData($request->vs) : [];

			if(empty($data['domain']) && !empty($request->seller))
			{
				$data['domain'] = $this->apiDataQuery($request->seller);
			}

			$total_query = $this->userTotalQuery($user_id);
			if($query_limit > 0)
			{
				$data['used_percentage'] = $this->queryUsedPercentage($total_query, $query_limit);
			}

			$used = $this->queryUsedInfo($total_query, $query_limit);

			$view_name = $has_vs ? "admin.cp_analysis.storelead.ajax.compare" : "admin.cp_analysis.storelead.ajax.single";
			$html = view($view_name, $data)->render();

			return response()->json([
				'success' => true,
				'html' => $html,
				'query_limit' => $query_limit,
				'total_query' => $total_query,
				'used_percentage' => $data['used_percentage'],
				'used_label' => $used['used_label'],
			]);

    	}catch(Exception $e)
    	{
    		return response()->json([
    			'success' => false,
    			'message' => $e->getMessage(),
    		]);
    	}
    }


    //Columns data
    private function colData()
    {
        $col = [];
        $col[] = ['label' => 'ID', "name" => 'id'];
        $col[] = ['label' => __('Merchant Name'), "name" => 'name'];
        $col[] = ['label' => __('Estimated_Sales_(monthly)'), "name" => 'estimated_sales'];
        $col[] = ['label' => __('Platform'), "name" => 'platform'];
        $col[] = ['label' => __('Country'), "name" => 'country_code'];
        $col[] = ['label' => 'Company Location', "name" => 'location'];
        $col[] = ['label' =>__('Employees'), "name" => 'employee_count'];
        $col[] = ['label' => __('Shipping Carriers'), "name" => 'shipping_carriers'];
        $col[] = ['label' => __('Store Rank'), "name" => 'rank'];
        $col[] = ['label' => 'Platform Rank', "name" => 'platform_rank'];
        $col[] = ['label' => 'Status', "name" => 'state'];
        $col[] = ['label' => __('Created'), "name" => 'created_at'];
        return $col;
    }


    private function permissionCheck()
    {
    	$app = $this->appData();
    	if(!isset($app['deny'])) return $app;
    	if(CRUDBooster::isSupportAccount())
    	{
    		return CRUDBooster::redirectBack("Sorry, You don't have access!");
    	}
    	abort(404);
    }




    //Table action
    public function actionSelected(Request $request)
    {
    	$id_selected = $request->input('checkbox');
    	$button_name = $request->input('button_name');

    	if(empty($id_selected))
    	{
    		CRUDBooster::redirect($_SERVER['HTTP_REFERER'], trans("crudbooster.alert_select_a_data"), 'warning');
    	}

    	try {
            if ($button_name == 'csv_download') {
            	return $this->csvDownload($id_selected);
            }
        } catch (Exception $e) {
            return redirect()->back()->with(['message_type' => 'error', 'message' => $e->getMessage()]);
        }

    	return redirect()->back();
    }


    //Download CSV
    private function csvDownload($item_ids)
    {
        $file = new \SplTempFileObject();
        $file->setFlags(\SplFileObject::READ_CSV);
        $file->setCsvControl(',');
        $csv = Writer::createFromFileObject($file);

        $rows = DB::table('webshop_archives')
        	->where('user_id', '=', CRUDBooster::myParentId())
        	->whereIntegerInRaw('id', $item_ids)
        	->select(
        		'name',
				'platform',
				'country_code',
				'location',
				'employee_count',
				'shipping_carriers',
				'platform_rank',
				'rank',
				'state',
				'created_date',
				'eur_estimated_sales',
				'screenshot',
        	)
        	->get();

        $header = ['Merchant Name', "Estimated Sales", "Platform", "Country", "Company Location", "Employees", "Shipping Carriers", "Store Rank", "Platform Rank", 'Status', 'Created', 'Screenshot'];
        $csv->insertOne($header);
        foreach($rows as $row)
        {
        	$csv_row = [];
        	$csv_row[] = $row->name;
        	$csv_row[] = $row->eur_estimated_sales;
        	$csv_row[] = $row->platform;
        	$csv_row[] = $row->country_code;
        	$csv_row[] = $row->location;
        	$csv_row[] = $row->employee_count;
        	$csv_row[] = $row->shipping_carriers;
        	$csv_row[] = $row->rank;
        	$csv_row[] = $row->platform_rank;
        	$csv_row[] = $row->state;
        	$csv_row[] = $row->created_date;
        	$csv_row[] = $row->screenshot;
        	$csv->insertOne($csv_row);
        }
        $csv->output('webshoop_data.csv');
        die;
    }


    //Currency convert
    private function currencyRate() {
    	try{
    		return app(CurrencyApi::class)->convertToEUR(1);
    	}catch(\Exception $e){}
    }

    //EUR sales estimate
    private function eurSalesEstimate($amount)
    {
    	$rate = $this->currencyRate();
    	if(is_null($amount) || is_null($rate)) return;
    	$amount = $amount / 100;
    	return $amount * $rate;
    }

    //User total query
    private function userTotalQuery($user_id)
    {
    	return DB::table('user_webshop_analysis')
    	->where('user_id', '=', $user_id)
    	->count();
    }

    //used percentage
    private function queryUsedPercentage($total_query, $query_limit)
    {
    	if($total_query >= $query_limit) return 100;

    	$percentage = (100 / $query_limit) * $total_query;
    	return $percentage > 100 ? 100 : $percentage;
    }


    private function queryUsedInfo($total_query, $query_limit)
    {
    	if($query_limit == -1) return [];

    	$data = [];

    	if($total_query >= $query_limit)
    	{
    		$total_query = $query_limit;
    	}

    	$data['used_label'] = 'Used: '.$total_query.'/'.$query_limit;
    	$data['limit_over'] = $total_query >= $query_limit;
    	return $data;
    }

    



    /*==================================
    ============ App data ==============
    ===================================*/
    //app data
    private function appData()
    {
    	if(!is_null($this->appData) && !CRUDBooster::isDropmatixSupport()) return $this->appData;

    	$this->appData = $this->appDataDB();

    	return $this->appData;
    }

    //App data db
    private function appDataDB()
    {
    	$user_id = CRUDBooster::myParentId();
		$data = resolve('App\Services\DRM\AppHelper')->getUserApps($user_id);
		if(empty($data)) return ['deny' => true];

		$app = collect($data['all_apps'])->firstWhere('id', self::APP_ID);
		if(empty($app)) return ['deny' => true];

		if($app->plan_id)
		{
			return $this->planAccessLimit($app->plan_id);

		}else if($app->is_trial)
		{
			return [
				'query_limit' => 25,
				'archive_limit' => 0,
				'is_trial' => $app->is_trial,
				'remaining_days' => $app->remaining_days,
			];

		}else if($app->is_free || CRUDBooster::isDropmatixSupport())
		{
			return ['query_limit' => -1, 'archive_limit' => -1];
		}

		return ['query_limit' => 0, 'archive_limit' => 0, 'deny' => true];
    }


    //access limit
    private function planAccessLimit($plan_id)
    {
    	if($plan_id == 53 || $plan_id == 49){
    		return ['query_limit' => 250, 'archive_limit' => 25];
    	}elseif($plan_id == 54 || $plan_id == 50) {
    		return ['query_limit' => 1000, 'archive_limit' => 100];
    	}elseif($plan_id == 55 || $plan_id == 51) {
    		return ['query_limit' => 5000, 'archive_limit' => 500];
    	}elseif($plan_id == 56 || $plan_id == 52) {
    		return ['query_limit' => -1, 'archive_limit' => 2000];
    	}

    	return ['query_limit' => 0, 'archive_limit' => 0];
    }

    private function apiDataQuery($seller)
	{
		$token = env('STORELEAD_TOKEN');

		// $archive = $this->dbArchiveResult($domain);
		// if($archive) {
		// 	$this->insertUserRecordByArchiveId($archive['id']);
		// 	return $archive;
		// }

		$curl = curl_init();
		curl_setopt_array($curl, array(
		  CURLOPT_URL => 'https://storeleads.app/json/api/v1/all/domain?q='.$seller,
		  CURLOPT_RETURNTRANSFER => true,
		  CURLOPT_ENCODING => '',
		  CURLOPT_MAXREDIRS => 10,
		  CURLOPT_TIMEOUT => 0,
		  CURLOPT_FOLLOWLOCATION => true,
		  CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		  CURLOPT_CUSTOMREQUEST => 'GET',
		  CURLOPT_HTTPHEADER => array(
		    'Accept: application/json',
		    'Content-Type: application/json',
		    'Authorization: Bearer '.$token,
		  ),
		));

		$response = curl_exec($curl);

		curl_close($curl);
		$payload = json_decode($response, true);


		$data = [];
		if(!empty($payload) && !empty($payload['domains']) && $payload['domains'][0])
		{
			$data = $payload['domains'][0];
			// $data = $res['domain'];
			$domain_name = $data['name'];
			$data['screenshot'] = ''; //$this->domainImage($domain_name);
			$data['categories'] = $data['categories'] ? implode(' > ', $data['categories']) : null;
			// $data['annual_sales'] = $data['estimated_sales'] ? $this->currencyFormat($data['estimated_sales'] * 12) : null;
			$data['shipping_carriers'] = $data['shipping_carriers'] ? implode(', ', $data['shipping_carriers']) : null;
			$data['created_date'] = $data['created_at'];
			$data['eur_estimated_sales'] = $this->eurSalesEstimate($data['estimated_sales']);

			//Replace microweber
			if( $data['platform'] && strtolower($data['platform']) == 'microweber' )
			{
				$data['platform'] = 'droptienda';
			}

			return $data;
			// $this->insertDomainToArchive($data, $res);
			// $this->insertUserRecordByDomainName($domain_name);
		}
	}
}