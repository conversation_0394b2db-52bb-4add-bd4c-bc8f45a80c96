<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use CRUDBooster;
use DB;
use DRM;

class StripeKeyController extends Controller
{
	public function index()
	{
		redirectToV2('/accounting');

		$this->keyChangePermission();
		$data = [];
		$data['page_title'] = "Stripe tokens";
		$slug = isPatrickSpecial() ? 'stripe_key_'.CRUDBooster::myId() : 'stripe_key_'.CRUDBooster::myParentId();

		$data['token'] = DB::table('stripe_keys')->where('slug', $slug)->select('public_key', 'secret_key', 'id')->first();
        $data['public_key_org'] = DRM::stringEncryption($data['token']->public_key , true);
        $data['secret_key_org'] = DRM::stringEncryption($data['token']->secret_key , true);
	    return view('stripeToken.index', $data);
	}

	public function save(Request $request)
	{
		redirectToV2('/accounting');

		$this->keyChangePermission();

		$request->validate([
			'public_key' => ['required', 'max:125', function ($attribute, $value, $fail) {
				if ( (strpos($value, '=') !== false) || (strpos($value, '/') !== false) || (strpos($value, '+') !== false) ) {
					$fail('Sorry, you can not set encrypted public key');
				}
            }],
            'secret_key' => ['required', 'max:125', function ($attribute, $value, $fail) {
				if ( (strpos($value, '=') !== false) || (strpos($value, '/') !== false) || (strpos($value, '+') !== false) ) {
					$fail('Sorry, you can not set encrypted private key');
				}
            }]
        ]);

        try{
			//initialize value
			$slug = isPatrickSpecial() ? 'stripe_key_'.CRUDBooster::myId() : 'stripe_key_'.CRUDBooster::myParentId();
	        $id = DB::table('stripe_keys')->where('slug', $slug)->value('id');
	        $public_key = trim($request->public_key);
	        $secret_key = trim($request->secret_key);

	        $stripe = new \Stripe\StripeClient([
			  "api_key" => $secret_key,
			  "stripe_version" => "2020-08-27"
			]);
			$stripe->customers->all(['limit' => 0]);

			$public_key = \DRM::stringEncryption($public_key);
			$secret_key = \DRM::stringEncryption($secret_key);

			$err = null;

	        //Insert or update value
	        if(empty($id)){
	        	$created = DB::table('stripe_keys')->insert([
	        		'user_id' => isPatrickSpecial() ? CRUDBooster::myId() : CRUDBooster::myParentId(),
	        		'slug' => $slug,
	        		'public_key' => $public_key,
	        		'secret_key' => $secret_key,
	        	]);
	        	$err = $created? null : 'Token failed to save. Please try again!';
	        }else{
	        	$changed = DB::table('stripe_keys')->where('id', $id)->update([
	        		'public_key' => $public_key,
	        		'secret_key' => $secret_key,
	        	]);

	        	$err = $changed? null : 'Nothing changed!';
	        }

	        //Forget cache
	        Cache::forget($slug);

	        if($err){
	        	throw new \Exception($err);
	        }

        	return response()->json([
        		'success' => true,
        		'message' => 'Token saved successfully!'
        	], 200);

        }catch(\Exception $e){
        	return response()->json([
        		'success' => false,
        		'message' => $e->getMessage()
        	], 400);
        }
	}

	//Delete token
	public function delete($id){

		redirectToV2('/accounting');
		
		$this->keyChangePermission();
		try{
			$delete = DB::table('stripe_keys')->where('id', '=', $id)->where('user_id', isPatrickSpecial() ? CRUDBooster::myId() : CRUDBooster::myParentId())->delete();
			if($delete){
				$slug = isPatrickSpecial() ? 'stripe_key_'.CRUDBooster::myId() : 'stripe_key_'.CRUDBooster::myParentId();
				//Forget cache 
	        	Cache::forget($slug);

	        	return response()->json([
	        		'success' => true,
	        		'message' => 'Token delete successfully!'
	        	], 200);
			}
			throw new \Exception('Something went wrong!');
		}catch(\Exception $e){
        	return response()->json([
        		'success' => false,
        		'message' => $e->getMessage()
        	], 400);
        }
	}

	private function keyChangePermission(){
		// if(!in_array(CRUDBooster::myParentId(), [98, 2454, 2455, 2439, 212, 2494])){
		// 	abort(404);
		// }
	}
}
