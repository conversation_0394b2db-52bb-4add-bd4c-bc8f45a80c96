<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use App\Notifications\DRMNotification;
use App\User;
use App\StripePayment;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

use App\Services\Payment\ServiceList;
use App\Services\Payment\PurchasePayloadFormat;
use App\Services\Stripe\Latest\StripeService;
use App\Services\Stripe\Latest\InvoicePaymentCallback;
use App\Services\Stripe\Latest\PaymentCallback;

use Exception;

class StripePaymentController extends Controller
{
	use ServiceList;


	private StripeService $stripeService;

    public function __construct(StripeService $stripeService){
    	$this->stripeService = $stripeService;
    }


    public function payment(){
    	return view('stripe.stripe_payment');
    }

    //Get Public key
    public function getPublicKey(){
		try {

    		return response()->json(['publishableKey' => $this->stripeService->getPublicKey()]);

    	} catch(\Exception $e){
    		return response()->json(['error' => ['message' => $e->getMessage()]]);
    	}
    }

    //1st step Create customer
    public function createCustomer(Request $request){
    	try{

    		$validator = Validator::make($request->all(), [
	            'email' => 'required|exists:cms_users,email',
	        ]);

	        if ($validator->fails()) {
	            throw new \Exception('Invalid Customer!');
	        }

    		$customerId = $this->stripeService->createCustomer(['first_name' => $request->name, 'email' => $request->email]);
			return response()->json(['id' => $customerId]);

    	}catch(\Exception $e){
    		return response()->json(['error' => ['message' => $e->getMessage()]]);
    	}
    }

    //Create subscription
	public function createSubscription(Request $request){
		try{
			$request->validate([
	            'metadata' => 'required|json',
	            'purchase_type' => ['required', Rule::in(['plan', 'import', 'dt', 'dtlicense', 'mp_category'])],
	            'planId' => 'required',
	            'paymentMethodId' => 'required|starts_with:pm_',
	            'customerId' => 'required|starts_with:cus_',
	            'coupon_id' => 'nullable|exists:coupons,coupon_id',
			  	'profit_share_id' => ['nullable', function ($attribute, $value, $fail) {
						$exist = DB::table('import_plan_get_discounts')->where('id', $value)->where('status', '=', 0)->exists();
						if (!$exist) {
							$fail('Invalid Profit share option');
						}
					}]
	        	]);

			$metadata = (isset($request->metadata) && ($request->metadata) )? json_decode($request->metadata, true) : [];
			
			// DT Tariff
			if($metadata['purchase_item_type'] === 'dtlicense')
			{
				$metadata['purchase_item_type'] = 'dt_tariff';
			}

			$validator = Validator::make($metadata, [
	            'user_id' => 'required|exists:cms_users,id',
	            'purchase_item_type' => ['required', Rule::in($this->serviceNameList())],
	            'item_id' => 'nullable|required_if:purchase_item_type,app,dt',
	            'plan_id' => 'required',
	        ]);

	        if ($validator->fails()) {
	            throw new \Exception('Invalid action. Please try again after refreshing this page!');
	        }

			$payload = [
	        	'service' => $metadata['purchase_item_type'],
	        	'id' => $metadata['item_id'] ?? null,
	        	'plan_id' => $metadata['plan_id'],
	        	'coupon_id' => $request->input('coupon_id'),
	        	'user_id' => $metadata['user_id'],
	        ];

	       	if($payload['service'] === 'import' && !empty($request->profit_share_id))
	        {
	        	$payload['profit_share_id'] = $request->profit_share_id;
	        }

	        $purchasePayload = [
                'customerId' => $request->input('customerId'),
                'paymentMethodId' => $request->input('paymentMethodId'),
			];


			//Coupon
            if (isset($payload['coupon_id']) && $payload['coupon_id']) {
                $payload['coupon'] = $this->stripeService->retrieveCoupon($payload['coupon_id']);
            }

            $purchaseItem = [];
            $serviceName = $payload['service'];
            if (isset($this->services[$serviceName])) {
                $purchaseItem = resolve($this->services[$serviceName])->getItem($payload);
            }

            $purchasePayload['payload'] = resolve(PurchasePayloadFormat::class)($purchaseItem);

            return $this->stripeService->createSubscription($purchasePayload);

		}catch(\Exception $e){
			return response()->json(['error' => ['message' => $e->getMessage()]]);
		}
	}

	public function createPaymentIntend(Request $request){
		try{

			$request->validate([
	            'metadata' => 'required|json',
	            'payment_id' => 'nullable|starts_with:pm_',
	            'customerId' => 'required|starts_with:cus_',
	            'coupon_id' => 'nullable|exists:coupons,coupon_id',
	            'profit_share_id' => ['nullable', function ($attribute, $value, $fail) {
					$exist = DB::table('import_plan_get_discounts')->where('id', $value)->where('status', '=', 0)->exists();
					if (!$exist) {
						$fail('Invalid Profit share option');
					}
				}]
	        ]);

			$metadata = (isset($request->metadata) && ($request->metadata) )? json_decode($request->metadata, true) : [];

			// DT Tariff
			if($metadata['purchase_item_type'] === 'dtlicense')
			{
				$metadata['purchase_item_type'] = 'dt_tariff';
			}

	        $validator = Validator::make($metadata, [
	            'user_id' => 'required|exists:cms_users,id',
	            'purchase_item_type' => ['required', Rule::in($this->serviceNameList())],
	            'item_id' => 'nullable|required_if:purchase_item_type,app,marketplace_product,monthly_paywall,drm_subscription,invoice_payment,quiz,dt,shipcloud_payment',
	            'plan_id' => 'nullable|required_if:purchase_item_type,appointment,plan,import,dt',

	            'source' => 'nullable|required_if:purchase_item_type,translate',
	            'language_id' => 'nullable|required_if:purchase_item_type,translate',
	            'product_id' => 'nullable|required_if:purchase_item_type,translate',
	        ]);

	        if ($validator->fails()) {
	            throw new \Exception($validator->errors()->first().' Please try again after refreshing this page!');
	        }

	        $payload = [
	        	'service' => $metadata['purchase_item_type'],
	        	'id' => $metadata['item_id'] ?? null,
	        	'plan_id' => $metadata['plan_id'],
	        	'coupon_id' => $request->input('coupon_id'),
	        	'user_id' => $metadata['user_id'],
	        ];


	        if($payload['service'] === 'translate')
	        {

	        	if(empty($request->price)) throw new \Exception("Invalid price");

	        	$payload['price'] = $request->price;
	        	$payload['translate_category'] = $metadata['translate_category'] ?? null;
	        	$payload['source'] = $metadata['source'];
	        	$payload['language_id'] = $metadata['language_id'];
	        	$payload['product_id'] = $metadata['product_id'];

	        }else if($payload['service'] === 'import' && !empty($request->profit_share_id)) {

	        	$payload['profit_share_id'] = $request->profit_share_id;

	        }else if(in_array($payload['service'], ['buy_product', 'buy_pre_product', 'mp_cart_checkout'])) {

	        	if(empty($request->price)) throw new \Exception("Invalid price");

	        	$payload['price'] = $request->price;
	        	$payload['id'] = $metadata['order_id'] ?? null;
				$payload['item_id'] = $metadata['item_id'] ?? null;	
	        }

	        $purchasePayload = [
                'customerId' => $request->input('customerId'),
                'paymentMethodId' => $request->input('payment_id'),
                'paymentMethod' => isset($request->payment_id) && $request->payment_id ? 'card' : $request->payment_option ?? 'eps',
			];

			//Coupon
            if (isset($payload['coupon_id']) && $payload['coupon_id']) {
                $coupon = $this->stripeService->retrieveCoupon($payload['coupon_id']);
                $payload['coupon'] = $coupon;
            }

            $purchaseItem = [];
            $serviceName = $payload['service'];
            if (isset($this->services[$serviceName])) {
                $purchaseItem = resolve($this->services[$serviceName])->getItem($payload);
            }

            $purchasePayload['payload'] = resolve(PurchasePayloadFormat::class)($purchaseItem);
            return $this->stripeService->createPaymentIntend($purchasePayload);

		}catch(\Exception $e){
			return response()->json(['error' => ['message' => $e->getMessage()]]);
		}
	}



	//Retry Invoice
	public function retryInvoice( Request $request){
	    try {
	        $payment_method = $this->stripeService->stripe()->paymentMethods->retrieve(
	            $request->paymentMethodId
	        );
	        $payment_method->attach([
	            'customer' => $request->customerId,
	        ]);
	    } catch (Exception $e) {
	        return response()->json(['error' => $e->getMessage()]);
	    }

	    // // Set the default payment method on the customer
	    // $this->stripeService->stripe()->customers->update($request->customerId, [
	    //     'invoice_settings' => [
	    //         'default_payment_method' => $request->paymentMethodId,
	    //     ],
	    // ]);

	    $invoice = $this->stripeService->stripe()->invoices->retrieve($request->invoiceId, [
	        'expand' => ['payment_intent'],
	    ]);
	    return response()->json($invoice);
	}



public function retrieveUpcomingInvoice( Request $request){

    $body = json_decode($request->getBody());
    $stripe = $this->stripeService->stripe();

    $subscription = $stripe->subscriptions->retrieve($body->subscriptionId);

    $invoice = $stripe->invoices->upcoming([
        "customer" => $body->customerId,
        "subscription_prorate" => true,
        "subscription" => $body->subscriptionId,
        "subscription_items" => [
            [
                'id' => $subscription->items->data[0]->id,
                'deleted' => true,
            ],
            [
                'price' => getenv($body->newPriceId),
                'deleted' => false,
            ],
        ],
    ]);

    return response()->json($invoice);
}

public function cancelSubscription(Request $request){

    $body = json_decode($request->getBody());
    $stripe = $this->stripeService->stripe();

    $subscription = $stripe->subscriptions->retrieve($body->subscriptionId);
    $subscription->delete();

    return response()->json($subscription);
}

public function updateSubscription(Request $request){
    $body = json_decode($request->getBody());
    $stripe = $this->stripeService->stripe();

    $subscription = $stripe->subscriptions->retrieve($body->subscriptionId);

    $updatedSubscription = $stripe->subscriptions->update(
        $body->subscriptionId,
        [
            'items' => [
                [
                    'id' => $subscription->items->data[0]->id,
                    'price' => getenv($body->newPriceId),
                ],
            ],
        ]
    );

    return response()->json($updatedSubscription);
}

public function updateSubscriptionPaymentMethod(Request $request){
	$user_id = $request->input('user_id');
	$subscription_id = $request->input('subscriptionId');
	$payment_method_id = $request->input('paymentMethodId');

	try { 
		$stripe = $this->stripeService->stripe();

		$updatedSubscription = $stripe->subscriptions->update(
			$subscription_id,
			[
				'default_payment_method' => $payment_method_id,
			]
		);

		if (!empty($updatedSubscription->id)) {
			\App\Option::updateOrCreate([
                'option_key' => 'import_plan_payment_method',
                'option_group' => 'import_plan_payment_method',
                'user_id' => $user_id,
            ],
            [
                'option_value' => $payment_method_id,
            ]);
		}

		return response()->json([
			'success' => true,
			'message' => __('Subscription updated successfully!'),
		], 200);
	} catch (\Exception $ex) {
		return response()->json([
			'success' => false,
			'message' => $ex->getMessage()
		], 422);
	}
}

public function retrieveCustomerPaymentMethod(Request $request){
    $stripe = $this->stripeService->stripe();
    $paymentMethod = $stripe->paymentMethods->retrieve($request->paymentMethodId);
    return response()->json($paymentMethod);
}

//Receive data from webhook
public function stripeWebhookSCA(){

	$response_data = json_decode(request()->getContent(), true);

	if($response_data){

		Log::channel('uscreen')->info($response_data); //Log response

		$data = $response_data['data']['object'];
		$webhook_type = $response_data['type'];

		$type = $data['object']; // invoice, payment_intent

		$payment = $purchase_data = [];
		$payment['object_type'] = $type;

		$purchase_data['id'] = $object_id = $data['id'];

		$payment['created'] = $data['created'];
		$payment['status'] = $payment_status = $data['status'];

		$payment['webhook_response'] = (array)$data;
		$payment['is_stripe_response'] = 1;
		$payment['notes'] = 'Response from webhook!';


		if($this->isNewVersion($object_id)) return;
	}
}



//Stripe redirect url callback - Event
public function stripeRedirect(Request $request, $key){

	try{

        //Payment intend ID
        if ($intentId = $request->query('payment_intent')) {
            return $this->_paymentCallback($intentId);
        }

        //Invoice ID
        if ($invoiceId = $request->query('invoice_id')) {
            return $this->_subscriptionCallback($invoiceId);
        }

        throw new Exception('Invalid payment!');

	}catch(\Exception $e) {
		User::find(User::DEV_ACCOUNT_ID)->notify(new DRMNotification('Stripe redirect payment failed. Error: '.$e->getMessage(), 'STRIPE_INTEND_REDIRECT_ERROR', '#'));
	    if(request()->ajax()){
			return response()->json([
				'success' => false,
				'message' => $e->getMessage()
			]);
	    }else{
	    	CRUDBooster::redirect(CRUDBooster::adminPath(),"Purchase failed. ERROR: ".$e->getMessage(),"error");
	    }
	}
}



private function _paymentCallback(string $intentId)
{
    //Retrive payment intend
    $paymentIntent = $this->stripeService->stripe()->paymentIntents->retrieve(
        $intentId,
        []
    );

    if ($paymentIntent && $paymentIntent instanceof \Stripe\PaymentIntent) {
        return app(PaymentCallback::class)($paymentIntent);
    }

    throw new Exception('Invalid payment!');
}

/**
 * Payment subscription callback
 */
public function _subscriptionCallback(string $invoiceId)
{
    //Retrive invoice
    $invoice = $this->stripeService->stripe()->invoices->retrieve(
        $invoiceId,
        []
    );

    if ($invoice && $invoice instanceof \Stripe\Invoice) {
        return app(InvoicePaymentCallback::class)($invoice);
    }

    throw new Exception('Invalid payment!');
}

//Active payment service after successfull payment
public function stripeSubscriptionPaymentAction(Request $request, $key){
	try{

		$data = (object)$request->subscription;
		$line_item = (object)$data->latest_invoice;

        //Invoice ID
        if ($line_item && $line_item->id) {
            return $this->_subscriptionCallback($line_item->id);
        }

        throw new Exception('Invalid payment!');
	}catch(\Exception $e) {
		User::find(User::DEV_ACCOUNT_ID)->notify(new DRMNotification('Stripe redirect payment failed. Error: '.$e->getMessage(), 'STRIPE_INTEND_REDIRECT_ERROR', '#'));
	    if(request()->ajax()){
			return response()->json([
				'success' => false,
				'message' => $e->getMessage()
			]);
	    }else{
	    	CRUDBooster::redirect(CRUDBooster::adminPath(),"Purchase failed. ERROR: ".$e->getMessage(),"error");
	    }
	}
}


	//Refund payment
	public function stripeWebhookRefunded(){

		$response_data = json_decode(request()->getContent(), true);

		if($response_data){
			$data = $response_data['data']['object'];
			return app(\App\Services\Order\RefundPayment::class)->refundWebhook($data);
		}
	}


	private function isNewVersion($object_id)
	{
		return StripePayment::where('object_id', $object_id)->where('version', 1)->exists();
	}

	public function retrievePaymentIntentInfo(string $intentId = '')
	{
		//Retrive payment intend
		$paymentIntentInfo = $this->stripeService->stripe()->paymentIntents->retrieve(
			$intentId,
			[]
		);

		return $paymentIntentInfo;
	}

	public function retrievePaymentMethodInfo(string $payment_method_id = '')
	{
		$paymentMethodInfo = $this->stripeService->stripe()->paymentMethods->retrieve($payment_method_id);

		return $paymentMethodInfo;
	}
}
