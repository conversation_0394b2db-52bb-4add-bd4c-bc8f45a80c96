<?php

namespace App\Http\Controllers\Supplier;

use App\DeliveryCompany;
use App\Helper\Encoding;
use App\Jobs\Supplier\DeleteSupplier;
use App\Models\Import\Supplier;
use App\Services\SupplierService;
use App\SupplierLog;
use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class SupplierController extends \App\Http\Controllers\Controller
{
    private SupplierService $service;

    public function __construct(SupplierService $service)
    {
        $this->service = $service;
    }

    public function index()
    {
        return view('suppliers.index');
    }


    public function detail(int $id)
    {
        $path = public_path() . "/supplier/form_fields.json";
        $data["fields"] = json_decode(file_get_contents($path), true);
        $user_id = CRUDBooster::myParentId();
        $data['page_title'] = __("Supplier Details");
        $data['id'] = $id;
        $data["countries"] = DB::table("countries")->select('id','name')->get();
        $data['category'] = DB::table('drm_supplier_categories')->where('user_id', $user_id)->get();
//        $data['supplier'] = $this->service->getById($id,$user_id);
        $query = Supplier::where('id', $id);
        if (!CRUDBooster::isSuperadmin() && !CRUDBooster::isDropMatrix() ) {
            $query->where('user_id', $user_id);
        } else if ( CRUDBooster::isDropMatrix() ) {
            $query->where(function($q) use ($user_id) {
                $q->where('user_id', $user_id)->orWhere('is_marketplace_supplier', true);
            });
        }
        $data['supplier'] = $query->first();
        $data['logistics'] = json_decode($data['supplier']->logistics , true);
        $data['contracts'] = json_decode($data['supplier']->contract , true);
        $data['bank_details'] = json_decode($data['supplier']->bank_details , true);



        $db_countries = DB::table('countries')->select('name', 'id', 'country_shortcut as code')->get();
        $countries = $db_countries->keyBy('name')->toArray();
        $countriesJson = json_encode($countries);
        $countries = $db_countries->keyBy('id')->toArray();
        $countriesRevJson = json_encode($countries);

        $data['countriesJson'] = $countriesJson;
        $data['countriesRevJson'] = $countriesRevJson;

        return view('suppliers.details.main',$data);
    }

    public function updateSupplier(Request $request){

        $skipVatCheck = isset($request->country_id) && intval($request->country_id) === 83;

        $validator = Validator::make($_REQUEST, [
            'name' => 'required',
            'email' => 'required | email',
            'address' => 'required',
            'country_id' => 'required',
            'zip' => 'required',
            'state' => 'required',
//            'category' => 'required',
            'phone' => 'required',
            // 'headquarter_number' => 'required',
            'vat_number' => ['nullable', function ($attribute, $value, $fail) use ($skipVatCheck) {
                $vat_checker = \DRM::checkTaxNumber($value);
                if(!$vat_checker['success'] && !$skipVatCheck){
                    $fail('The '.$attribute.' is invalid. '.$vat_checker['message']);
                }
            }],
            'director' => 'required',
            'url' => 'nullable | url',
            'linkin_profile' => 'nullable | url',
        ],
        [
            'headquarter_opening.required' => __('The headquarter opening time required.'),
            // 'supplier_number.required' => 'The supplier / vendor number required.',
            'country_id.required' => __('The country is required.'),
            'director.required' => __('Management is required.')
        ]
        );

        if ($validator->fails())
        {
            return response()->json(['errors'=>$validator->errors()->all()]);
        }

        try{
            $user_id = CRUDBooster::myParentId();
            DB::table('delivery_companies')->where('id', $request->id)->where('user_id', $user_id)->update([
                'name' => $request->name,
                'address' => $request->address,
                'zip' => $request->zip,
                'state' => $request->state,
                'email' => $request->email,
                'order_email' => $request->email,
                'category_id' => $request->category,
                'country_id' => $request->country_id,
                'customer_number' => $request->customer_number,
                // 'headquarter_number' => $request->headquarter_phone,
                'headquarter_opening_time' => $request->headquarter_opening,
                'contact_name' => $request->director,
                'phone' => $request->phone,
                'vat_number' => $request->vat_number,
                'order_value' => $request->order_value,
                'url' => $request->url,
                'linkin_profile' => $request->linkin_profile,
            ]);

            return response()->json([
                'success' => true,
                'message' => __('General Information Updated!')
            ]);

        }catch (Exception $exception){
            return response()->json([
                'success' => false,
                'message' => __('Something Went Wrong') . " !"
            ]);
        }
    }

    public function contactDepartment(Request $request){
      try{
        $fields = [
          'title' => $request->name,
          'input_fields' => [
            'name' => 'Surname',
            'email' => 'E-mail',
            'phone' => 'Phone',
            'opening_time' => 'Accessibility / opening times',
            'linkin_profile' => 'Linkin Profile'
          ]
        ];

        DB::table('contact_department_details')->insert([
          'supplier_id' => $request->id,
          'fields' => json_encode($fields),
        ]);

        return response()->json([
          'success' => true,
          'fields' => $fields,
          'message' =>'Add department successfully!'
        ]);

      }catch(Exception $exception){
        return response()->json([
          'success' => false,
          'message' => $exception->getMessage()
        ]);
      }
    }

    public function contactDepartmentLoad($id){

      try{
        $path = public_path() . "/supplier/form_fields.json";
        $default_fields = json_decode(file_get_contents($path), true);

        $user_fields = DB::table('contact_department_details')->where('supplier_id', $id)->select('fields')->get();
        foreach ($user_fields as $user_field) {
          array_push($default_fields , json_decode($user_field->fields,true));
        }

          $contact_info = DB::table('delivery_companies')->where('id', $id)->value('contact_info');
          $contact_value = json_decode($contact_info,true);

         $html = '<input type = "hidden" value="'.$id.'" name="id">';
         $index = 0;
         foreach ($default_fields as $field) {
           $html.='  <p><b>'.__($field['title']).'</b></p><hr>
                <div class="form-group">
                    <label for="email" class="col-sm-3 control-label">'.__($field['input_fields']['name']).'</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" placeholder="" name="'.$field['input_fields']['name'].'[]" value="'.$contact_value['Surname'][$index].'">
                        <input type="hidden" class="form-control" placeholder="" name="title[]" value="'.$field['title'].'">
                    </div>
                </div>
                <div class="form-group">
                    <label for="email" class="col-sm-3 control-label">'.__($field['input_fields']['email']).'</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" placeholder="" name="'.$field['input_fields']['email'].'[]"  value="'.$contact_value['E-mail'][$index].'">
                    </div>
                </div>
                <div class="form-group">
                    <label for="email" class="col-sm-3 control-label">'.__($field['input_fields']['phone']).'</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" placeholder="" name="'.$field['input_fields']['phone'].'[]" value="'.$contact_value['Phone'][$index].'">
                    </div>
                </div>
                <div class="form-group">
                    <label for="email" class="col-sm-3 control-label">'.__($field['input_fields']['opening_time']).'</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" placeholder="" name="'.$field['input_fields']['opening_time'].'[]" value="'.$contact_value['Accessibility_/_opening_times'][$index].'">
                    </div>
                </div>
                <div class="form-group">
                    <label for="email" class="col-sm-3 control-label">'.__($field['input_fields']['linkin_profile']).'</label>
                    <div class="col-sm-9">
                        <input type="text" class="form-control" placeholder="" name="'.$field['input_fields']['linkin_profile'].'[]" value="'.$contact_value['Linkin_Profile'][$index].'" value="'.$contact_value['Linkin_Profile'][$index].'">
                    </div>
                </div>';
                if(!empty($field['input_fields']['return_address'])){
                  $html.='<div class="form-group">
                          <label for="email" class="col-sm-3 control-label">'.__($field['input_fields']['return_address']).'</label>
                          <div class="col-sm-9">
                              <input type="text" class="form-control" placeholder="" name="'.$field['input_fields']['return_address'].'[]" value="'.$contact_value['Return_Address']['0'].'">
                          </div>
                      </div>
                  <br><br>';
                }
            $index++;
         }
         return response()->json([
           'success' => true,
           'html' => $html
         ]);
      }catch(Exception $exception){
        return response()->json([
          'success' => false,
          'html' => ' '
        ]);
      }
    }

    public function contactSave(Request $request){
      try{
          DB::table('delivery_companies')->where('id', $request->id)->update([
              'contact_info' => json_encode($request->all())
          ]);

          return response()->json([
              'success' => true,
              'message' =>'Contact save successfully!'
          ]);

      }catch (Exception $exception){
          return response()->json([
              'success' => false,
              'message' => $exception->getMessage()
          ]);
      }
    }

    public function logisticsSave(Request $request){
      try{
          DB::table('delivery_companies')->where('id', $request->id)->update([
              'logistics' => json_encode($request->all())
          ]);

          DB::table('delivery_companies')->where('id', $request->id)->update([
              'dropshipping' => $request->direct_delivery
          ]);

          return response()->json([
              'success' => true,
              'value' => $request->direct_delivery,
              'message' =>'Logistics save successfully!'
          ]);

      }catch (Exception $exception){
          return response()->json([
              'success' => false,
              'message' => 'Something went wrong'
          ]);
      }
    }

    public function contractSave(Request $request){

        try{
            DB::table('delivery_companies')->where('id', $request->id)->update([
                'contract' => json_encode($request->all())
            ]);

            DB::table('delivery_companies')->where('id', $request->id)->update([
                'dropshipping' => $request->direct_delivery
            ]);

            return response()->json([
                'success' => true,
                'value' => $request->direct_delivery,
                'message' =>'Contract save successfully!'
            ]);

        }catch (Exception $exception){
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong'
            ]);
        }
    }

    public function bankDetailsSave(Request $request){

        $request->validate([
            'iban' => ['sometimes', function ($attribute, $value, $fail) {
                try {
                    app(\App\Services\UiValidation\UiValidation::class)->validateIBAN($value);
                } catch (\Exception $e) {
                    $fail($e->getMessage());
                }
            }],
        ]);

        try{
            DB::table('delivery_companies')->where('id', $request->id)->update([
                'bank_details' => json_encode($request->all())
            ]);

            // Do accounting
            app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

            return response()->json([
                'success' => true,
                'message' =>'Bank details save successfully!'
            ]);

        }catch (Exception $exception){
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong'
            ]);
        }
    }

    public function delete(int $id)
    {
        $user_id = CRUDBooster::myParentId();
        DeleteSupplier::dispatch($id,$user_id);
        CRUDBooster::redirect($_SERVER['HTTP_REFERER'], "Supplier deletion process started. Your data will be deleted very soon.", "success");
    }

    public function getLogs(Request $request): JsonResponse
    {
        try {
            $supplier_id = $_REQUEST['supplier_id'];
            $supplier_logs = SupplierLog::where('supplier_id', $supplier_id)->orderBy('created_at', 'desc')->get()->groupBy(function ($date) {
                return Carbon::parse($date->created_at)->format('d-M-y');
            });

            if ($supplier_logs) {
                $data['logs'] = $supplier_logs;
                return response()->json([
                    'success' => true,
                    'log' => view('admin.drm_delivery_companies.log.index', $data)->render(),
                ]);
            } else {
                throw new Exception('Supplier has no log data!');
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function addComment(Request $request): JsonResponse
    {
        $validator = Validator::make($_REQUEST, [
            'comment_text' => 'required',
            'order_email' => 'required | email',
            ],
            [
                'comment_text.required' => __('The comment box is required'),
                'order_email.required' => __('E-Mail-Address is required'),
            ]);

        if ($validator->fails())
        {
            return response()->json(['errors'=>$validator->errors()->all()]);
        }

        try {
            $supplier_id = $request->supplier_id;

            $comment_text = $request->comment_text;
            $supplier = DeliveryCompany::where('id', $supplier_id)->first();
            if ($supplier) {
                $supplier->logs()->create([
                    'type' => 2,
                    'data' => [],
                    'message' => $comment_text,
                ]);

                $data['logs'] = SupplierLog::where('supplier_id', $supplier_id)->orderBy('created_at', 'desc')->get()->groupBy(function ($date) {
                    return Carbon::parse($date->created_at)->format('d-M-y');
                });
                return response()->json([
                    'success' => true,
                    'log' => view('admin.drm_delivery_companies.log.index', $data)->render(),
                ]);

            } else {
                throw new Exception('Invalid Supplier!');
            }

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function emailUpdate(Request $request){
        $validator = Validator::make($_REQUEST, [
            'order_email' => 'required | email',
        ],
        [
            'order_email.required' => __('E-Mail-Address is required'),
        ]);

        if ($validator->fails())
        {
            return response()->json(['errors'=>$validator->errors()->all()]);
        }

        try {
            $supplier_id = $request->supplier_id;

            $supplier = DeliveryCompany::where('id', $supplier_id)->first();
            if ($supplier) {
                $supplier->order_email = $request->order_email;
                $supplier->save();

                return response()->json([
                    'success' => true,
                    'message' => __('Email Update successfully') . "!",
                ]);

            } else {
                throw new Exception( __('Invalid Supplier') . "!" );
            }

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function uploadDocuments(Request $request): JsonResponse
    {
        if ($_FILES['profile_picture']) {
            $ext = end(explode(".", $_FILES['profile_picture']['name']));
            $name = 'pp' . time() . '.' . $ext;

            $file = \Request::file('profile_picture');
            $path = $file->storeAs('public/delivery_companies/profile_picture', $name, ['visibility' => 'public', 'disk' => 'spaces']);

            $path = Storage::disk('spaces')->url($path);

            DB::table('delivery_companies')->where('id', $_POST['id'])->update(['profile_picture' => $path]);


        } else if ($_FILES['upload_doc']) {
            $docs = json_decode(DB::table('delivery_companies')->where('id', $_POST['id'])->first()->doc, true);
            if (!$docs) {
                $count = 1;
            } else {
                $count = count($docs) + 1;
            }

            if ($count > 5) {
                return response()->json('File is maxed out.', 500);
            }

            if ($docs) {
                foreach ($docs as $doc_file_name) {
                    $decoded_url = urldecode($doc_file_name);

                    $database_file_name = pathinfo($decoded_url, PATHINFO_FILENAME);
                    $uploaded_file_name = preg_replace('/\s+/', '_', Encoding::toUTF8(array_shift(explode(".", $_FILES['upload_doc']['name']))));

                    if (mb_convert_case($database_file_name, MB_CASE_UPPER, "UTF-8") == mb_convert_case($uploaded_file_name, MB_CASE_UPPER, "UTF-8")) {
                        return response()->json('File name already exists !!!', 422);
                    }
                }
            }

            $name = preg_replace('/\s+/', '_', Encoding::toUTF8($_FILES['upload_doc']['name']));

            $file = \Request::file('upload_doc');
            $docs['doc' . $count] = $file->storeAs('public/delivery_companies/doc', $name, ['visibility' => 'public', 'disk' => 'spaces']);
            $docs['doc' . $count] = Storage::disk('spaces')->url($docs['doc' . $count]);

            $path = $docs['doc' . $count];
            DB::table('delivery_companies')->where('id', $_POST['id'])->update([
                'doc' => json_encode($docs),
            ]);
        }else if($_FILES['cont_document']){
            $ext = end(explode(".", $_FILES['cont_document']['name']));
            $name = 'pp' . time() . '.' . $ext;

            $file = \Request::file('cont_document');
            $path = $file->storeAs('public/delivery_companies/contract_document', $name, ['visibility' => 'public', 'disk' => 'spaces']);

            $path = Storage::disk('spaces')->url($path);
        }
        $data['file'] = $path;
        $data['label'] = $name;
        $data['type'] = array_key_first($_FILES);
        $data['count'] = $count;
        $data['docs'] = $docs;
        return response()->json($data);
    }

    public function updateNote(Request $request): JsonResponse
    {
        $this->service->update($request->id,['note' => $request->note]);
        return response()->json($request->all());
    }

    public function addCategory(Request $request){
        try{
            $category_id = DB::table('drm_supplier_categories')->insertGetId([
                'user_id' => CRUDBooster::myParentId(),
                'category_name' => $request->name,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'html' => '<option value="'.$category_id.'" selected>'.$request->name.'</option>',
                'message' =>'Add category successfully!'
            ]);

        }catch(Exception $exception){
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage()
            ]);
        }
    }
}
