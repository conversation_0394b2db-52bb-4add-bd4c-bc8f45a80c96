<?php

namespace App\Http\Controllers\Support;

use App\Exceptions\DropmatixException;
use App\Http\Controllers\Controller;
use App\Services\Support\AnswerService;
use App\Services\Support\SupportService;
use App\Services\Support\WidgetData;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ChatController extends Controller
{
    private $supportService;
    private $widgetData;
    private $answerService;


    public function __construct(
        SupportService $supportService,
        WidgetData $widgetData,
        AnswerService $answerService
    ) {
        $this->supportService = $supportService;
        $this->widgetData = $widgetData;
        $this->answerService = $answerService;
    }

    /**
     * Create or find service thread
     *
     * @throws DropmatixException
     */
    public function serviceThread(Request $request, int $serviceId)
    {
        $serviceType = $request->input('service', '');
        if (empty($serviceType)) {
            throw new DropmatixException(__('Invalid action'));
        }

        $chat = $this->supportService->findOrCreateThread($serviceId, $serviceType);

        return response()->json(['id' => $chat->id]);
    }

    /**
     * Get thread data
     *
     * @throws DropmatixException
     */
    public function openThread(Request $request, $threadId): JsonResponse
    {
        $chat = $this->supportService->openThread($threadId);

        $body = view('support.body', ['messages' => $chat['messages'], 'threadId' => $threadId])->render();
        $header = !empty($chat['order']) ? view('support.order-header', ['order' => $chat['order']])->render() : view('support.order-header', ['mp_product' => $chat['mp_product']])->render();
        $answers = $chat['isSystem'] ? \App\Models\Support\ChatAnswer::select('question', 'answer', 'id')->get()->toArray(): [];

        return response()->json([
            'header' => $header,
            'body' => $body,
            'answers' => $answers,
            'isSystem' => $chat['isSystem'],
            'order_id' => !empty($chat['order']) && $chat['order']['id'] ? $chat['order']['id'] : null,
            'threadId' => $threadId,
        ]);
    }

    /**
     * Pull thread
     *
     * @throws DropmatixException
     */
    public function pullThread(Request $request, $threadId): JsonResponse
    {
        $body = view('support.body', ['messages' => $this->supportService->pullThread($threadId), 'threadId' => $threadId])->render();
        return response()->json([
            'body' => $body,
        ]);
    }

    /**
     * Reply message
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'chat_id' => 'required',
            'message' => 'nullable|max:5000',
            'message_id' => 'nullable|numeric',
            'files' => 'nullable',
            'files.*' => 'mimetypes:image/jpeg,image/png,application/pdf|max:5000',
        ], [], ['files.*' => 'Attachment']);

        return response()->json($this->supportService->sendMessage($request, (int) $request->input('chat_id')));

    }

    /**
     * Widget data
     *
     * @throws DropmatixException
     */
    public function widgetData(Request $request)
    {
        return response()->json($this->widgetData->__invoke($request));
    }

    /**
     * Update chat status
     *
     * @throws DropmatixException
     */
    public function updateStatus(Request $request, $threadId)
    {
        return response()->json($this->supportService->updateStatus($threadId, $request->input('archive') === 'yes'));
    }

    /**
     * Get all answers
     */
    public function getAnswers(Request $request)
    {
        return response()->json($this->answerService->getAll());
    }

    /**
     * Create answer
     *
     * @throws DropmatixException
     */
    public function createAnswer(Request $request)
    {
        $payload = $request->validate([
            'question' => 'required|min:2|max:250',
            'answer' => 'required|min:2|max:5000',
            'id' => 'nullable',
        ]);


        $answerId = isset($payload['id']) ? $payload['id'] : null;


        if($answerId)
        {
            return response()->json($this->answerService->update($payload, (int) $answerId));
        }
        


        return response()->json($this->answerService->create($payload));
    }

    /**
     * Remove answer
     *
     * @throws DropmatixException
     */
    public function removeAnswer(Request $request, $answerId)
    {
        return response()->json($this->answerService->remove((int) $answerId));
    }
}
