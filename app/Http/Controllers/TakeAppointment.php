<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\User;
use App\AppointmentHistory;
use App\NewOrder;
use CRUDBooster;
use Carbon\Carbon;
use DB;
use ServiceKey;

class TakeAppointment extends Controller
{
    public function index(){
        $user = User::find(CRUDBooster::myParentId());
        $type = request()->type;
        //User term
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy) ? $privacy->page_content : '';
        $user_data = '<div id="customer_data_term"></div>';
        if ($user->billing_detail) {
            $billing = $user->billing_detail;
            $user_data = '<div id="customer_data_term">' . $billing->company_name . '<br>' . $billing->address . '<br>' . $billing->zip . ' ' . $billing->city . '<br>' . $billing->country->name . '</div>';
        }
        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }

        if($type == 'manager'){
            $manager_id = app('\App\Http\Controllers\AdminSubAccountController')->userAppointmentManagerId($user->id);
            $plan_config_name = 'appointment.key_account';
        }else{
            $manager_id = 98;
            $plan_config_name = 'appointment.daily';
        }
        $manager = User::find($manager_id, ['photo', 'name']);

//        $plan_config_name = $manager_id == 98 ? 'appointment.daily' : 'appointment.key_account';
        $plans = config($plan_config_name, []);

        $manager_data = [
            'photo' => $manager_id == 98? asset('images/fabian.jpg') : user_photo($manager->photo),
            'name' => $manager->name,
            'type' => ($type) ? $type : 'mentoring',
        ];

       // if ( app()->environment('development') ) { //stripeTest
            return view('admin.drm_appointment.take_appointment_v1', compact('user', 'term', 'manager_data', 'plans'));
       //  } else {
            // return view('admin.drm_appointment.take_appointment');
        // }
    }

    public function appointmentPrice(Request $request){
        try{
            $plan = $request->appointment_plan;
            if($request->type == 'manager'){
                $plan_config_name = 'appointment.key_account';
            }else{
                $plan_config_name = 'appointment.daily';
            }
//            $manager_id = app('\App\Http\Controllers\AdminSubAccountController')->userAppointmentManagerId(CRUDBooster::myParentId());
//            $plan_config_name = $manager_id == 98 ? 'appointment.daily' : 'appointment.key_account';

            $plans = config($plan_config_name, []);
            $plan_data = collect($plans)->firstWhere('day', '=', $plan);
            if(!empty($plan_data)){
                return response()->json([
                    'success' => true,
                    'day' => $plan_data['day'],
                    'price' => $plan_data['amount']
                ]);
            }else{
                throw new \Exception('Invalid appointment plan!');
            }

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function appointmentSCA($purchase_data) {
        //DB::beginTransaction();
        try {

            $user = User::with('billing_detail')->find($purchase_data['user_id']);
            if (is_null($user->term_accept)) $user->update(['term_accept' => \Carbon\Carbon::now()->toDateTimeString()]);
            $is_tax_free = $user->billing_detail->country_id == 83;

            $intend_id = $purchase_data['id'];
            if(NewOrder::where(['order_id_api' => $intend_id, 'cms_user_id' => 2455, 'shop_id' => 8])->exists()) throw new \Exception('Already purchased!');  //STRIPE_CLIENT

            $appointment = DB::table('takeappointment')->where('user_id', $user->id)->first();
            if($appointment == null){
                throw new \Exception('You are not accessible to use this feature, Please Contact with DRM Customer Care!');
            }else{

                //Increment single pay coupon usages
                if (isset($purchase_data['coupon']) && $purchase_data['coupon']) {
                    DB::table('coupons')->where('coupon_id', $purchase_data['coupon'])->increment('single_pay');
                }

                $discount = $purchase_data['discount']?? 0;
                $total = $purchase_data['total']?? 0;
                $sub_total = $purchase_data['sub_total']?? 0;

                $day = $purchase_data['appointment_id'];

                $paymentAmount = $appointment->payment_amount;
                $userID = $appointment->user_id;
                $totalDay = $appointment->payment_date_for;
                $totalDayDue = $appointment->payment_date_remaining;

                $mentoring_plan_config_name = 'appointment.daily';
                $plans = config($mentoring_plan_config_name, []);
                $mentoring_plan_ids =  [];
                foreach ($plans as $plan) {
                    array_push($mentoring_plan_ids,$plan['day']);
                }




                // start add history
                if(in_array($day,$mentoring_plan_ids)){

                    $appointment_history = AppointmentHistory::where('user_id',$userID)->orderby('id','desc')->first();
                    if($appointment_history){
                        $previous = ($appointment_history->current)? $appointment_history->current : 0;
                    }else{
                        $remaining_date = DB::table('takeappointment')
                            ->where('user_id',$userID)
                            ->first();
                        $previous = ($remaining_date->payment_date_remaining)? $remaining_date->payment_date_remaining : 0;
                    }

                    AppointmentHistory::insert([
                        'user_id' => $userID,
                        'previous' => $previous,
                        'current' => $previous+$day,
                        'type' => 3, // 3 for purchase date
                        'message' => 'User puchsase '.iconv('UTF-8', 'ASCII//TRANSLIT','Mentor Appointment for '.$day.' Dates'),
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                    // end add history

                    $go = DB::table('takeappointment')->where('user_id',$userID)->update([
                        'payment_id' => $intend_id,
                        'payment_amount' => $paymentAmount + $total,
                        'payment_date' => date('Y-m-d'),
                        'payment_date_for' => $totalDay + $day,
                        'payment_date_remaining' => $totalDayDue + $day
                    ]);
                }else{
                    $totalDayDue = $appointment->manager_date;
                     DB::table('takeappointment')->where('user_id',$userID)->update([
                        'payment_id' => $intend_id,
                        'payment_amount' => $paymentAmount + $total,
                        'payment_date' => date('Y-m-d'),
                        'payment_date_for' => $totalDay + $day,
                        'manager_date' => $totalDayDue + $day
                    ]);

                     //history add
                    $appointment_history = AppointmentHistory::where('user_id',$userID)->where('mentor_type', 1)->orderby('id','desc')->first();
                    if($appointment_history){
                        $previous = ($appointment_history->current)? $appointment_history->current : 0;
                    }else{
                        $remaining_date = DB::table('takeappointment')
                            ->where('user_id',$userID)
                            ->first();
                        $previous = ($remaining_date->manager_date)? $remaining_date->manager_date : 0;
                    }

                    AppointmentHistory::insert([
                        'user_id' => $userID,
                        'previous' => $previous,
                        'current' => $previous+$day,
                        'type' => 3, // 3 for purchase date
                        'mentor_type' => 1, // 1 for manager
                        'message' => 'User puchsase '.iconv('UTF-8', 'ASCII//TRANSLIT','Mentor Appointment for '.$day.' Dates'),
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }

                $payment_intend_id = $purchase_data['intend_id'] ?? null;
                $vat_number = $purchase_data['vat_number'] ?? null;

                $taxShow = !empty($vat_number) || $is_tax_free ? 0 : 21;
                $total_tax = ($total * $taxShow) /100;
                $order_info = [
                    'user_id' => 2455,  //STRIPE_CLIENT
                    'cms_client'  => $userID,
                    'order_date'    => date('Y-m-d H:i:s'),
                    'total' => round(($total), 2),
                    'sub_total' => round($sub_total, 2),
                    'discount' => round($discount, 2),
                    'discount_type' => 'fixed',
                    'total_tax' => 0,
                    'payment_type'  => 'Stripe',
                    'status'    => "paid",
                    'currency'  => "EUR",
                    'adjustment'    => 0,
                    'insert_type'   => \App\Enums\InsertType::APPOINTMENT,
                    'shop_id'       => 8,
                    'order_id_api'  => $intend_id,
                    'intend_id' => $payment_intend_id,
                    'vat_number' => $vat_number,
                ];

                  $carts = [];
                  $cart_item = [];
                  $cart_item['id'] = 1;
                  $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT','Mentor Appointment for '.$day.' Dates');
                  $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT',"Mentor Appointment");
                  $cart_item['qty'] = 1;
                  $cart_item['rate'] = round($sub_total,2);
                  $cart_item['tax'] =  $taxShow;
                  $cart_item['product_discount'] = 0;
                  $cart_item['amount'] = round($sub_total,2);
                  $carts[] = $cart_item;
                  $order_info['cart'] = json_encode($carts);


                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info, $userID);


            }

            //DB::commit();    // Commiting  ==> There is no problem whatsoever
             return ['success' => true, 'message' => 'Successfully Active Mentor Appointment Plan!'];
        }catch(\Exception $e){
            //DB::rollBack();   // rollbacking  ==> Something went wrong
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }


        public function sofort_appointment(){

            $req=json_decode(request()->data);
            $select_type = $req->select_type;
            $payment_source=request()->source;
            if($select_type == 'fiveday'){
                $price = 445.00;
                $day = 5;
            }
            elseif($select_type == 'tenday'){
                $price = 890.00;
                $day = 10;
            }
            elseif($select_type == 'twentyfiveday'){
                $price = 2225.00;
                $day = 25;
            }
            elseif($select_type == 'fiftyday'){
                $price = 4450.00;
                $day = 50;
            }
            else{
                $price = 0.00;
                $day = 0;
            }
            \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
            try{
            $charge = \Stripe\Charge::create([
                'amount' => $price*100,
                'currency' => 'eur',
                'source' => $payment_source,
            ]);

            if($charge->status != "succeeded"){
                // return CRUDBooster::redirect(url("https://drm_v7.test/admin"),"Sorry, the payment you made is failed","info");
                CRUDBooster::redirect(CRUDBooster::adminPath(),"Sorry, the payment you made is failed!","info");
            }
            }catch(\Exception $e){
            return "Sorry, the payment you made is failed";
            }

            $charge_id = $charge->id;
            $payment_type = "Sofort";
            $this->MentorAppointmentUpdate($price,$day,$charge_id,$payment_type);

            // return CRUDBooster::redirect(url("https://drm_v7.test/admin"),"Successfully Active Mentor Appointment Plan.","success");
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-plan'),"Successfully Active Mentor Appointment Plan.","success");

        }

        public function AppointmentSofort(Request $request){
          $type = $_POST['select_type'];
          $user=User::where('id',CRUDBooster::myId())->first();

                if($type == 'fiveday'){
                    $price = 445.00;
                    $day = 5;
                }
                elseif($type == 'tenday'){
                    $price = 890.00;
                    $day = 10;
                }
                elseif($type == 'twentyfiveday'){
                    $price = 2225.00;
                    $day = 25;
                }
                elseif($type == 'fiftyday'){
                    $price = 4450.00;
                    $day = 50;
                }
                else{
                    $price = 0.00;
                    $day = 0;
                }

                \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
                $source = \Stripe\Source::create([
                    "type" => "sofort",
                    "amount" => $price*100,
                    "currency" => "eur",
                    "redirect" => [
                    "return_url" => CRUDBooster::adminPath('')."/sofort_appointment?data=".json_encode(request()->post()),
                    ],
                    "sofort" => [
                    "country" => "DE",
                    ],
                    "owner" => [
                    "email" => $user->email,
                    "name" => $user->name,
                    ]
                ]);
                return redirect($source['redirect']['url']);
        }



        public function giropay_appointment(){

            $req=json_decode(request()->data);
            $select_type = $req->select_type;
            $payment_source=request()->source;
            if($select_type == 'fiveday'){
                $price = 445.00;
                $day = 5;
            }
            elseif($select_type == 'tenday'){
                $price = 890.00;
                $day = 10;
            }
            elseif($select_type == 'twentyfiveday'){
                $price = 2225.00;
                $day = 25;
            }
            elseif($select_type == 'fiftyday'){
                $price = 4450.00;
                $day = 50;
            }
            else{
                $price = 0.00;
                $day = 0;
            }
            \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
            try{
            $charge = \Stripe\Charge::create([
                'amount' => $price*100,
                'currency' => 'eur',
                'source' => $payment_source,
            ]);
            if($charge->status != "succeeded"){
                // return CRUDBooster::redirect(url("https://drm_v7.test/admin"),"Sorry, the payment you made is failed","info");
                CRUDBooster::redirect(CRUDBooster::adminPath(),"Sorry, the payment you made is failed!","info");
            }
            }catch(\Exception $e){
            return "Sorry, the payment you made is failed";
            }

            $charge_id = $charge->id;
            $payment_type = "Giropay";
            $this->MentorAppointmentUpdate($price,$day,$charge_id,$payment_type);

            // return CRUDBooster::redirect(url("https://drm_v7.test/admin"),"Successfully Active Mentor Appointment Plan.","success");
            CRUDBooster::redirect(CRUDBooster::adminPath('appointment-plan'),"Successfully Active Mentor Appointment Plan.","success");

        }

        public function AppointmentGiropay(Request $request){
            // dd($request->all());
          $type = $_POST['select_type'];
          $user=User::where('id',CRUDBooster::myId())->first();

                if($type == 'fiveday'){
                    $price = 445.00;
                    $day = 5;
                }
                elseif($type == 'tenday'){
                    $price = 890.00;
                    $day = 10;
                }
                elseif($type == 'twentyfiveday'){
                    $price = 2225.00;
                    $day = 25;
                }
                elseif($type == 'fiftyday'){
                    $price = 4450.00;
                    $day = 50;
                }
                else{
                    $price = 0.00;
                    $day = 0;
                }

                \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));

                $source = \Stripe\Source::create([
                    "type" => "giropay",
                    "amount" => $price*100,
                    "currency" => "eur",
                    "redirect" => [
                        "return_url" => CRUDBooster::adminPath('')."/giropay_appointment?data=".json_encode(request()->post()),
                    ],
                    "owner" => [
                    "email" => $user->email,
                    "name" => $user->name,
                    ]
                ]);

                return redirect($source['redirect']['url']);
        }

        public function MentorAppointmentUpdate($price,$day,$charge_id,$payment_type){

            // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
            // ->join('countries','countries.id','=','billing_details.country_id')
            // ->first();

            // $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";


             $appointment = DB::table('takeappointment')->where('user_id',CRUDBooster::myId())->first();
             if($appointment == null){
                return CRUDBooster::redirectBack("You are not accessible to use this feature, Please Contact with DRM Customer Care!", "warning");
            }
            else{
                    $paymentAmount = $appointment->payment_amount;
                    $userID = $appointment->user_id;
                    $totalDay = $appointment->payment_date_for;
                    $totalDayDue = $appointment->payment_date_remaining;

                    $go = DB::table('takeappointment')->where('user_id',$userID)->update([
                        'payment_id' => $charge_id,
                        'payment_amount' => $paymentAmount + $price,
                        'payment_date' => date('Y-m-d'),
                        'payment_date_for' => $totalDay + $day,
                        'payment_date_remaining' => $totalDayDue + $day
                    ]);



                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($price * $taxShow) /100;
                $order_info = [
                    'user_id' => 98,
                    'cms_client'  => $userID,
                    'order_date'    => date('Y-m-d H:i:s'),
                    'total' => round(($price),2),
                    'sub_total' => round($price-$total_tax,2),
                    'total_tax' => round($total_tax,2),
                    'payment_type'  => $payment_type,
                    'status'    => "paid",
                    'currency'  => "EUR",
                    'adjustment'    => 0,
                    'insert_type'   => 3,
                    'shop_id'       => 8,
                    'order_id_api'  => $charge_id,
                ];

                  $carts = [];
                  $cart_item = [];
                  $cart_item['id'] = 1;
                  $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT','Mentor Appointment for '.$day.' Dates');
                  $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT',"Mentor Appointment");
                  $cart_item['qty'] = 1;
                  $cart_item['rate'] = round($price,2);
                  $cart_item['tax'] = $taxShow;
                  $cart_item['product_discount'] = 0;
                  $cart_item['amount'] = round($price,2);
                  $carts[] = $cart_item;
                  $order_info['cart'] = json_encode($carts);

                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info);
            }
        }


    public function Appointment(Request $request){
        // dd($request->all());

    // $billingDetails = DB::table('billing_details')->where('user_id',CRUDBooster::myId())
    // ->join('countries','countries.id','=','billing_details.country_id')
    // ->first();

    // $detailsInformationForBilling = "<b>Company Name: $billingDetails->company_name</b></br><p>Address: $billingDetails->address,$billingDetails->city,$billingDetails->zip,$billingDetails->name</p><p>Contact Information:</p><p>E-mail: $billingDetails->email</p><p>Phone: $billingDetails->phone</p>";

      $type = $_POST['select_type'];
      $user=User::where('id',CRUDBooster::myId())->first();
      $appointment = DB::table('takeappointment')->where('user_id',CRUDBooster::myId())->get();
      if($appointment[0] == null){
            return CRUDBooster::redirectBack("You are not accessible to use this feature, Please Contact with DRM Customer Care!", "warning");
      }
      else{
            $paymentAmount = $appointment[0]->payment_amount;
            $userID = $appointment[0]->user_id;
            $totalDay = $appointment[0]->payment_date_for;
            $totalDayDue = $appointment[0]->payment_date_remaining;

            if($type == 'fiveday'){
                $price = 445.00;
                $day = 5;
            }
            elseif($type == 'tenday'){
                $price = 890.00;
                $day = 10;
            }
            elseif($type == 'twentyfiveday'){
                $price = 2225.00;
                $day = 25;
            }
            elseif($type == 'fiftyday'){
                $price = 4450.00;
                $day = 50;
            }
            else{
                $price = 0.00;
                $day = 0;
            }


              \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));

              $charge = \Stripe\Charge::create([
                'amount' => $price * 100,
                'currency' => 'eur',
                'description' => 'Mentor Appointment Package Active by "'. $user->name.'", & Plan Active for '. $day.' Dates',
                'source' => $_POST['stripeToken']
              ]);


                if($charge->status != "succeeded"){
                    return CRUDBooster::redirectBack("Something wrong with you payment!", "success");
                }
                else{

                DB::table('takeappointment')->where('user_id',$userID)->update([
                    'payment_id' => $charge->id,
                    'payment_amount' => $paymentAmount + $price,
                    'payment_date' => date('Y-m-d'),
                    'payment_date_for' => $totalDay + $day,
                    'payment_date_remaining' => $totalDayDue + $day
                ]);
                $taxShow = config('global.tax_for_invoice');
                $total_tax = ($price * $taxShow) /100;
                $order_info = [
                    'user_id' => 98,
                    'cms_client'  => $userID,
                    'order_date'    => date('Y-m-d H:i:s'),
                    'total' => round(($price),2),
                    'sub_total' => round($price-$total_tax,2),
                    'total_tax' => round($total_tax,2),
                    'payment_type'  => 'Stripe Card',
                    'status'    => $charge->status,
                    'currency'  => "EUR",
                    'adjustment'    => 0,
                    'insert_type'   => 3,
                    'shop_id'       => 8,
                    'order_id_api'  => $charge->id,

                ];

                  $carts = [];
                  $cart_item = [];
                  $cart_item['id'] = 1;
                  $cart_item['product_name'] = iconv('UTF-8', 'ASCII//TRANSLIT','Mentor Appointment for '.$day.' Dates');
                  $cart_item['description'] =  iconv('UTF-8', 'ASCII//TRANSLIT',"Mentor Appointment");
                  $cart_item['qty'] = 1;
                  $cart_item['rate'] = round($price,2);
                  $cart_item['tax'] = $taxShow;
                  $cart_item['product_discount'] = 0;
                  $cart_item['amount'] = round($price,2);
                  $carts[] = $cart_item;
                  $order_info['cart'] = json_encode($carts);

                app('App\Http\Controllers\AdminDrmAllOrdersController')->insertStripeOrder($order_info);

                // return CRUDBooster::redirectBack("Your Mentor Appointment package is Confirm. Now you can get an appointment with mentor.!", "success");
                CRUDBooster::redirect(CRUDBooster::adminPath('appointment-plan'),"Your Mentor Appointment package is Confirm. Now you can get an appointment with mentor.!","success");
            }
        }
    }


}
