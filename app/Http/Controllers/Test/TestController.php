<?php
namespace App\Http\Controllers\Test;

use App\AnalysisCategory;
use App\DrmImport;
use App\DrmProduct;
use App\Enums\CategoryType;
use App\Enums\Channel;
use App\Enums\ChannelProductConnectedStatus;
use App\Enums\DroptiendaSyncEvent;
use App\Enums\DroptiendaSyncType;
use App\Http\Controllers\Controller;
use App\Jobs\ChannelManager\ChangeChannelProductConnectionStatus;
use App\Jobs\GenerateCsv;
use App\Jobs\InvoiceArchiveJob;
use App\MarketplaceProducts;
use App\Models\ChannelCategory;
use App\Models\ChannelProduct;
use App\Models\ChannelProductCategory;
use App\Models\ChannelUserCategory;
use App\Models\DRMProductCategory;
use App\Models\DroptiendaSyncHistory;
use App\Models\Import\Supplier;
use App\Models\Log;
use App\Models\Marketplace\Category;
use App\Models\Marketplace\Product;
use App\Models\Marketplace\StockSyncReport;
use App\Models\Product\AnalysisProduct;
use App\Models\Product\CPAnalysisRequest;
use App\Models\Product\CPAnalysisUserRequests;
use App\NewCustomer;
use App\NewOrder;
use App\Services\ChannelProductService;
use App\Services\DRMProductService;
use App\Services\Modules\Export\Colizey;
use App\Services\Modules\Export\Ebay\Api;
use App\Services\Modules\Export\Mirakl\Home24;
use App\Services\Modules\Export\Otto;
use App\Services\ProductApi\Services\Countdown;
use App\Services\ProductApi\Services\GoogleShopping;
use App\Services\ProductApi\Services\Rainforest;
use App\Shop;
use App\Traits\EmailMarketings;
use App\Traits\InvoiceNumber;
use App\Traits\Marketplace\InternelApi;
use App\User;
use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use League\Csv\CannotInsertRecord;
use League\Csv\Reader;
use League\Csv\Writer;


class TestController extends Controller
{
    use InvoiceNumber, InternelApi, EmailMarketings;

    //copy daily proforma to new account
    public function cloneDailyProforma(){
        DB::table('new_orders')->where('cms_user_id', 98)->whereNull('deleted_at')
            ->where('invoice_number', -1)
            ->get()
            ->each(function($order){
                $this->insertCloneOrder($order);
            });
    }

    //insert clone order
    private function insertCloneOrder($order){
        $customer = DB::table('new_customers')->where('id', $order->drm_customer_id)->first();
        $customer_email = $customer->email;

        $user_id = 2455;

        $customer_id = DB::table('new_customers')->where('user_id', $user_id)->where('email', $customer_email)->value('id');
        if(empty($customer_id)){
            $customer_data = collect($customer)->except('id', 'email', 'user_id')->toArray();
            $new_cus = DB::table('new_customers')->updateOrInsert([
                'user_id' => $user_id,
                'email' => $customer_email
            ], $customer_data);

            if($new_cus){
                $customer_id = DB::table('new_customers')->where('user_id', $user_id)->where('email', $customer_email)->value('id');
            }
        }

        //Format order data
        $order_data = collect($order)->except('id', 'cms_user_id', 'drm_customer_id', 'order_id_api')->toArray();
        $order_data['drm_customer_id'] = $customer_id;

        $order_id_api = 'dclone_'.$order->id;
        $exist = DB::table('new_orders')->where('cms_user_id', $user_id)->where('order_id_api', $order_id_api)->exists();

        if(!$exist){
            DB::table('new_orders')->updateOrInsert([
                'cms_user_id' => $user_id,
                'order_id_api' => $order_id_api
            ], $order_data);
        }
    }


    public function insertVODprogressTag(){
        $user_id = 2454;
        $customers = DB::table('new_customers')
            ->join('dropfunnel_customer_tags', 'dropfunnel_customer_tags.customer_id', '=', 'new_customers.id')
            ->where('dropfunnel_customer_tags.insert_type', '=', 3)
            ->where('new_customers.user_id', '=', $user_id)
            ->selectRaw('COUNT(*) as count, new_customers.id as customer_id')
            ->groupBy('new_customers.id')
            ->orderBy('count')
            ->pluck('customer_id')
            ->toArray();

        $c = 0;
        foreach ($customers as $customer_id) {
            \DRM::insertVodWatchProgressTag($customer_id, $user_id);
            $c++;
        }


        dd($c, $customers);
    }

    //transfer customer profiles
    public function userProfileClone(){
        return 0;

        $vod_customers = DB::table('new_customers')->where('user_id', '=', 98)->whereNotNull('cc_user_id')->get();

        dump('Customer cloning start');
        foreach ($vod_customers as $customer) {
            //Format customer data
            $customer_data = $this->format_customer_data($customer);

            //Users
            $users = [2454, 2455, 2439];

            //Insert customer
            foreach ($users as $key => $target_user_id) {

                $check_customer = ["user_id" => $target_user_id, "email" => $customer->email];

                //Get customer id
                $customer_id = DB::table('new_customers')->where($check_customer)->value('id');
                if(empty($customer_id)){
                    //Merge data
                    $cus_data = array_merge($check_customer, $customer_data);
                    DB::table('new_customers')->insertGetId($cus_data);
                }else{
                    DB::table('new_customers')->where('id', '=', $customer_id)->update(['cc_user_id' => $customer->cc_user_id]);
                }
            }
            //Insert customer end
        }
        dump('End customer cloning!');

        $vod_customersd = DB::table('new_customers')->where('user_id', '=', 98)->whereNotNull('cc_user_id')->count();
        $vod_customers1 = DB::table('new_customers')->where('user_id', '=', 2454)->whereNotNull('cc_user_id')->count();
        $vod_customers2 = DB::table('new_customers')->where('user_id', '=', 2455)->whereNotNull('cc_user_id')->count();
        $vod_customers3 = DB::table('new_customers')->where('user_id', '=', 2439)->whereNotNull('cc_user_id')->count();
        dd($vod_customersd, $vod_customers1, $vod_customers2, $vod_customers3);
    }

    //update user time
    function updateCmsUserTime(){
        DB::table('cms_users')->whereNull('updated_at')->update(['updated_at' => now()]);
        $data = DB::table('traker_time_spents')
            ->selectRaw("user_id, max(created_at) as created_at")
            ->orderBy('created_at', 'desc')
            ->groupBy('user_id')
            ->get()->each(function($item){
                DB::table('cms_users')->where('id', $item->user_id)->update(['updated_at' => $item->created_at]);
            });
    }

    public function inactiveAction(){
        if(!isLocal()) abort(404);

        $check = new \App\Services\DeactiveUser\CheckUser();
        dd($check->inactiveAction());
    }



    //Clone daily account to DropCampus
    public function cloneDailyToCampus()
    {
        return 0;
        $target_user_id = 2454;
        $order_cus_ids = DB::table('new_orders')
            ->where('new_orders.insert_type', '=', 2)
            ->whereNotNull('drm_customer_id')
            ->select('drm_customer_id')
            ->groupBy('drm_customer_id')
            ->pluck('drm_customer_id')
            ->toArray();

        $ids = DB::table('dropfunnel_customer_tags')
            ->join('dropfunnel_tags', function ($join) {
                $join->on('dropfunnel_customer_tags.tag_id', '=', 'dropfunnel_tags.id')
                    ->whereIntegerInRaw('dropfunnel_customer_tags.insert_type', [3, 11])
                    ->where('dropfunnel_tags.user_id', '=', 98);
            })
            ->join('new_customers', 'new_customers.id', 'dropfunnel_customer_tags.customer_id')
            ->select('dropfunnel_customer_tags.customer_id as customer_id')
            ->groupBy('customer_id')
            ->pluck('customer_id')
            ->merge($order_cus_ids)
            ->unique()
            ->toArray();

        $vod_customers = DB::table('new_customers')->where('user_id', '=', 98)->where(function($cus) use($ids){
            $cus->whereIntegerInRaw('id', $ids)->orWhere('insert_type', '=', 2);
        })->get();

        $all_ids = collect($vod_customers)->pluck('id')->toArray();

        //Customer cloning start
        dump('Start customer cloning');
        foreach ($vod_customers as $customer) {
            break;
            $customer_tags = DB::table('dropfunnel_customer_tags')
                ->join('dropfunnel_tags', 'dropfunnel_tags.id', '=', 'dropfunnel_customer_tags.tag_id')
                ->where('dropfunnel_customer_tags.customer_id', '=', $customer->id)
                ->whereIntegerInRaw('dropfunnel_customer_tags.insert_type', [3, 11])
                ->select('dropfunnel_customer_tags.*', 'dropfunnel_tags.tag')
                ->get()
                ->map(function($tag){
                    return [
                        "insert_type" => $tag->insert_type,
                        "score" => $tag->score,
                        "history" => $tag->history,
                        "created_at" => $tag->created_at,
                        "updated_at" => $tag->updated_at,
                        "deleted_at" => $tag->deleted_at,
                        "tag" => $tag->tag,
                    ];
                })
                ->toArray();

            //Format customer data
            $check_customer = [
                "user_id" => $target_user_id,
                "email" => $customer->email,
            ];

            $customer_data = $this->format_customer_data($customer);

            //Merge data
            $cus_data = array_merge($check_customer, $customer_data);

            //Get customer id
            $customer_id = DB::table('new_customers')->where($check_customer)->value('id');
            if(empty($customer_id)){
                $customer_id = DB::table('new_customers')->insertGetId($cus_data);
            }

            //Tag clone section
            if(!empty($customer_tags) && $customer_id)
            {
                foreach ($customer_tags as $tag) {

                    //Step-1
                    $check_tag = [
                        "user_id" => $target_user_id,
                        "tag" => $tag['tag']
                    ];

                    $tag_time = [
                        "created_at" => $tag['created_at'],
                        "updated_at" => $tag['updated_at'],
                    ];

                    //Merge data
                    $tag_data = array_merge($check_tag, $tag_time);

                    //Get tag id
                    $tag_id = DB::table('dropfunnel_tags')->where($check_tag)->value('id');
                    if(empty($tag_id)){
                        $tag_id = DB::table('dropfunnel_tags')->insertGetId($tag_data);
                    }


                    //Step-2
                    $check_customer_tag = [
                        'customer_id' => $customer_id,
                        'tag_id' => $tag_id
                    ];

                    $customer_tag_data = [
                        "insert_type"   =>  $tag['insert_type'],
                        "score"         =>  $tag['score'],
                        "history"       =>  $tag['history'],
                        "created_at"    =>  $tag['created_at'],
                        "updated_at"    =>  $tag['updated_at'],
                        "deleted_at"    =>  $tag['deleted_at'],
                    ];

                    //Merge data
                    $tag_data_cus = array_merge($check_customer_tag, $customer_tag_data);

                    //Get customer tag id
                    $cus_tag_id = DB::table('dropfunnel_customer_tags')->where($check_customer_tag)->value('id');
                    if(empty($cus_tag_id)){
                        $cus_tag_id = DB::table('dropfunnel_customer_tags')->insertGetId($tag_data_cus);
                    }
                }
            }
        }
        dump('End customer cloning');
        //Customer cloning foreach end

        //Order cloning
        $vod_credits = DB::table('new_orders')
            ->join('new_customers', 'new_customers.id', '=', 'new_orders.drm_customer_id')
            ->where('new_orders.insert_type', '=', 2)
            ->where('new_orders.cms_user_id', '=', 98)
            ->whereIntegerInRaw('new_orders.drm_customer_id', $all_ids)
            ->where('credit_number', '<>', 0)
            ->whereNull('new_orders.deleted_at')
            ->orderByRaw('CAST(new_orders.order_date AS datetime) asc')
            ->select('new_orders.id', 'new_orders.order_id_api')
            ->get()->map(function($item){
                return [
                    'invoice_id' => (int)str_replace('inv_id_', '', $item->order_id_api),
                    'credit_id' => $item->id,
                ];
            });

        $credit_ref_ids = collect($vod_credits)->pluck('invoice_id')->toArray();
        $credit_ids = collect($vod_credits)->pluck('credit_id')->toArray();

        $vod_orders = DB::table('new_orders')
            ->join('new_customers', 'new_customers.id', '=', 'new_orders.drm_customer_id')
            ->where('new_orders.insert_type', '=', 2)
            ->whereIntegerInRaw('new_orders.drm_customer_id', $all_ids)
            ->where('new_orders.credit_number', 0)
            ->whereNull('new_orders.deleted_at')
            ->orderByRaw('CAST(new_orders.order_date AS datetime) asc')
            ->select('new_orders.*', 'new_customers.email')
            ->get();

        dump('Start order cloning');
        $inv = 1;
        $cnm = 1;
        foreach ($vod_orders as $order) {
            $new_order_id = $this->order_data_insert($order, $target_user_id, $inv);
            $inv++;
            if(in_array($order->id, $credit_ref_ids))
            {
                $this->credit_data_insert($new_order_id, $order->id, $cnm);
                $cnm++;
            }
        }
        dump('End order cloning');
    }
    //Clone daily end


    // prepare order data
    private function order_data_insert($order, $target_user_id, $invoice_number)
    {
        //Invoice number
        $inv_pattern = self::generateInvoiceString($invoice_number, $target_user_id);

        $check_inv = [
            'order_id_api' => $order->order_id_api,
            'cms_user_id' => $target_user_id
        ];

        $inv_data = $this->format_order_data($order);
        $inv_data['inv_pattern'] = $inv_pattern;
        $inv_data['invoice_number'] = $invoice_number;

        //Customer
        $customer_id = DB::table('new_customers')->where('user_id', $target_user_id)->where('email', $order->email)->value('id');
        $inv_data['drm_customer_id'] = $customer_id;

        //Merge data
        $order_data = array_merge($check_inv, $inv_data);

        //Get order id
        $order_id = DB::table('new_orders')->where($check_inv)->value('id');
        if(empty($order_id)){
            $order_id = DB::table('new_orders')->insertGetId($order_data);
            try{
                $this->generateOrderhistory($order_id);
            }catch(Exception $e){}
        }

        return $order_id;
    }

    //Credit data insert
    private function credit_data_insert($new_order_id, $order_id, $credit_number)
    {
        $order = DB::table('new_orders')->where('id', $new_order_id)->first();
        $credit = DB::table('new_orders')->where('cms_user_id', 98)->where('order_id_api', 'inv_id_'.$order_id)->first();

        $credit_data = collect($credit)
            ->except(['id', 'order_id_api', 'cms_user_id', 'drm_customer_id', 'invoice_number', 'credit_number', 'inv_pattern', 'credit_ref', 'order_history'])
            ->toArray();

        $check['order_id_api']  = $order_id_api = 'inv_id_'.$order->id;
        $check['cms_user_id']  = $cms_user_id = $order->cms_user_id;

        $credit_data['invoice_number']  = $order->invoice_number;
        $credit_data['inv_pattern']  = $order->inv_pattern;
        $credit_data['drm_customer_id']  = $order->drm_customer_id;
        $credit_data['credit_number']   = $credit_number;

        //Merge data
        $credit_data = array_merge($check, $credit_data);

        //Get order id
        $credit_id = DB::table('new_orders')->where($check)->value('id');
        if(empty($credit_id)){
            $credit_id = DB::table('new_orders')->insertGetId($credit_data);
            DB::table('new_orders')->where('id', $new_order_id)->update(['credit_ref' => $credit_id]);
            try{
                $this->generateOrderhistory($credit_id);
            }catch(Exception $e){}
        }
        return $credit_id;
    }

    //Format customer data
    private function format_customer_data($customer)
    {
        return [
            "full_name" => $customer->full_name,
            "company_name" => $customer->company_name,
            "vat_number" => $customer->vat_number,
            "phone" => $customer->phone,
            "website" => $customer->website,
            "currency" => $customer->currency,
            "default_language" => $customer->default_language,
            "address" => $customer->address,
            "city" => $customer->city,
            "state" => $customer->state,
            "zip_code" => $customer->zip_code,
            "country" => $customer->country,
            "note" => $customer->note,
            "billing" => $customer->billing,
            "shipping" => $customer->shipping,
            "insert_type" => $customer->insert_type,
            "status" => $customer->status,
            "created_at" => $customer->created_at,
            "updated_at" => $customer->updated_at,
            "cc_user_id" => $customer->cc_user_id
        ];
    }


    //Format order data
    private function format_order_data($order)
    {
        return collect($order)->except(['id', 'cms_user_id', 'drm_customer_id', 'invoice_number', 'credit_number', 'inv_pattern', 'credit_ref', 'email', 'order_history'])->toArray();
    }


    //Generate order history
    private function generateOrderhistory($order_id){

        $order = NewOrder::find($order_id);
        if(empty($order) || empty($order->id)) return false;

        $insert_type_name = getInsertTypeName($order->insert_type);
        $shop_channel_name = null;

        $history = $order->order_history ?? [];

        $shop_id = $order->shop_id;
        $shop_type = ($shop_id)? Shop::find($shop_id, ['channel'])->channel : null;
        if($shop_type){
            $shop_channel_name = drm_shop_type_name($shop_type);
        }

        $insert_message = '';
        if($insert_type_name){
            $insert_message = $insert_type_name.' ';
        }

        $insert_message .= (in_array($order->insert_type, [1, 2, 7]))? 'Order sycronized' : 'Order inserted';

        $insert_message .= ($shop_channel_name)? ' from '.$shop_channel_name.'.' : '.';
        $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->created_at) ), 'action_by' => null, 'message'=> $insert_message];

        if($order->credit_number == 0){
            $invoice_create_message = 'Invoice number '.inv_number_string($order->invoice_number, $order->inv_pattern).' created.';
            $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->created_at) ), 'action_by' => null, 'message'=> $invoice_create_message];
        }else{
            $credit_create_message = 'Credit number '.$order->credit_number.' created.';
            $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->created_at) ), 'action_by' => null, 'message'=> $credit_create_message];
        }

        if($order->mail_sent){
            $email_send_message = 'Invoice number '.inv_number_string($order->invoice_number, $order->inv_pattern).' has been sent to customer by email.';
            $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->mail_sent) ), 'action_by' => null, 'message'=> $email_send_message];
        }

        if($order->supplier_time){
            $order_placed_message = 'Order placed successfully!';
            $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->supplier_time) ), 'action_by' => null, 'message'=> $order_placed_message];
        }

        if($order->package_number){
            $order_tracking_message = 'Order tracking successfully. Tracking number: '.$order->package_number;
            $history[] = ['status' => $order->status, 'time' => date('Y-m-d H:i:s', strtotime($order->supplier_time) ), 'action_by' => null, 'message'=> $order_tracking_message];
        }

        // STORE_ORDER_STATUS_ON_DATABASE
        $order->update(['order_history' => $history]);
    }

    //Order cloning end




    public function moveTranslations()
    {
        try {
            $products = DrmProduct::whereIn('user_id',[61,212])->get();
            // $products = DrmProduct::where('user_id',98)->get();
            foreach($products->chunk(500) as $chunk){
                //   $translations_de = DB::table('drm_translation_de')
                //   ->select('product_id','title','description')
                //   ->whereIn('product_id',$chunk->pluck('id')->toArray() ?? array())->get();


                //   $translations_en = DB::table('drm_translation_en')
                //   ->select('product_id','title','description')
                //   ->whereIn('product_id',$chunk->pluck('id')->toArray() ?? array())->get();


                foreach($chunk as $product)
                {
                    // $title = array();
                    // $description = array();

                    // $translation_de = $translations_de->where('product_id',$product->id)->first();
                    // $translation_en = $translations_en->where('product_id',$product->id)->first();

                    // if(!empty($translation_de)){
                    //   $title['de'] = $translation_de->title;
                    //   $description['de'] = $translation_de->description;
                    // }

                    // if(!empty($translation_en)){
                    //   $title['en'] = $translation_en->title;
                    //   $description['en'] = $translation_en->description;
                    // }


                    $product->image = collect($product->image)->pluck('src')->toArray();
                    $product->save();


                    // $product->title = $title;
                    // $product->description = $description;

                }
            }
        } catch (\Throwable $th) {}
    }

    public function countTrans()
    {
        $this->fixApiCategories();
        dd('ok');
        $channel_categories = ChannelProductCategory::get();
        foreach ($channel_categories as $category)
        {
            if($category->category_type == 'App\Models\ChannelUserCategory'){
                $category->category_type = CategoryType::USER;
            }
            else {
                $category->category_type = CategoryType::API;
            }
            $category->save();
        }
    }

    public function transferCategory($channel,$user_id,$category,$drm_category_id)
    {
        return ChannelUserCategory::updateOrCreate([
            'drm_category_id' => $drm_category_id,
            'category_name' => $category,
            'channel' => $channel,
            'user_id' => $user_id
        ], []);
    }

    public function transferMappingProduct($product, $channel, $shop_id, $lang = 'de')
    {
        $image = collect($product->image)->pluck('src')->toArray();

        $shop_price = DB::table('drm_product_shop_prices')->where(['drm_product_id' => $product->id ,'shop_id' => $shop_id])->first();

        $channelProduct = ChannelProduct::updateOrCreate([
            'user_id' => $product->user_id,
            'drm_product_id' => $product->id,
            'channel' => $channel,
        ],
            [
                'title' => [$lang => $product->hasTemplate() ? $product->getTemplate('title',$lang) : $product->title[$lang]],
                'description' => [$lang => $product->hasTemplate() ? $product->getTemplate('description',$lang) : $product->description[$lang]],
                'item_number' => $product->item_number,
                'ean' => $product->ean,
                'ek_price' => $product->ek_price,
                'vk_price' => $shop_price ? $shop_price->vk_price : $product->vk_price,
                'stock' => $product->stock,
                'item_weight' => $product->item_weight,
                'item_size' => $product->item_size,
                'item_color' => $product->item_color,
                'note' => $product->note,
                'production_year' => $product->production_year,
                'brand' => $product->brand,
                'materials' => $product->materials,
                'tags' => $product->tags,
                'gender' => $product->gender,
                'delivery_days' => $product->delivery_days,
                'delivery_company_id' => $product->delivery_company_id,
                'drm_import_id' => $product->drm_import_id,
                'country_id' => $product->country_id,
                'status' => $product->status,
                'images' => $image,
                'is_connected' => 1,
                'update_status' => [
                    'title' => 1,
                    'description' => 1,
                    'item_number' => 1,
                    'ek_price' => 1,
                    'stock' => 1,
                    'item_weight' => 1,
                    'item_size' => 1,
                    'item_color' => 1,
                    'note' => 1,
                    'production_year' => 1,
                    'brand' => 1,
                    'materials' => 1,
                    'tags' => 1,
                    'gender' => 1,
                    'delivery_days' => 1,
                    'status' => 1,
                    'image' => 1,
                    'category' => 0,
                ]
            ]);

        $category = DB::table('drm_product_categories')->where('product_id',$product->id)->first();

        $category_yatego = DB::table('drm_category_mapping')->where(['drm_category_id' => $category->category_id, 'shop_id' => $shop_id])->first();

        if($category_yatego){
            ChannelProductCategory::updateOrCreate([
                'channel_product_id' => $channelProduct->id,
                'category_id' => $category_yatego->mapping_id,
                'category_type' => \App\Enums\CategoryType::API
            ], []);
        }

        return true;
    }

    public function ottoRecoverCategory()
    {
        $products = ChannelProduct::where([
            'user_id' => 61,
            'channel' => 12
        ])->pluck('id')->toArray();

        $categories = DB::connection('backup')
            ->table('channel_product_categories')
            ->whereIn('category_id', [56941])
            ->whereIn('channel_product_id',$products);
    }

    public function generateCsv()
    {
        $products = ChannelProduct::where(['channel' => 10,'user_id' => 2560])->cursor();

        $header = [
            'title',
            'drm_ref_id',
            'description',
            'qty',
            'ek_price',
            'price',
            'price_on_request',
            'ean',
            'sku',
            'content_type',
            'content_body',
            'subtype',
            'tags',
            'images',
            'categories',
            'is_active',
            'color',
            'size',
            'weight',
            'materials',
            'brand',
            'production_year',
            'status',
            'note',
            'gender',
            'delivery_company_id',
            'offer_options',
            'handling_time',
            'tax_type'
        ];
        $writer = Writer::createFromString();
        $writer->insertOne($header);

        foreach($products as $product)
        {
//            $categories = $this->getCategories($product);

            $data = [
                'title'                 => $product->getTitle('de'),
                'drm_ref_id'            => $product->id,
                'description'           => $product->short_description['de'] ?? '',
                'qty'                   => $product->stock,
                'ek_price'              => $product->ek_price,
                'price'                 => $product->vk_price,
                'price_on_request'      => 0,
                'ean'                   => $product->ean,
                'sku'                   => $product->item_number,
                'content_type'          => 'product',
                'content_body'          => $product->getDescription('de') ?? '',
                'subtype'               => 'product',
                'tags'                  => $product->tags,
                'images'                => json_encode($product->images),
//                'categories'            => json_encode($categories),
//                'categories'            => $categories,
                'is_active'             => 1,
                'color'                 => $product->item_color, //varchar
                'size'                  => $product->item_size, //varchar
                'weight'                => $product->item_weight,
                'materials'             => $product->materials,
                'brand'                 => $product->brand,
                'production_year'       => $product->production_year,
                'status'                => $product->status,
                'note'                  => $product->note,
                'gender'                => $product->gender,
                'delivery_company_id'   => $product->delivery_company_id,
                'offer_options'         => json_encode($product->offer_options),
                'handling_time' => json_encode([
                    'min' => (int)$product->delivery_days,
                    'max' => (int)$product->delivery_days + 2
                ]),
                'tax_type' => "FULL"
            ];
            $writer->insertOne($data);
        }
        $writer->output('wohlauf.csv');
    }

    public function generateDecathlonCsv()
    {
        debug_log();
        $headers = [
            "Kategorie",
            "Artikelnummer",
            "Hauptbild",
            "EAN-Codes",
            "Marke",
            "Geschlecht",
            "Farbe",
            "Sportart",
            "Produkttyp",
            "ohne Größe",
            "Sportart",
            "HÖHENMESSER",
            "Schuhgröße unisex",
            "POSITION"
        ];

        $products = ChannelProduct::where([
            'user_id' => 62
        ])->limit(50)->get();

        $writer = Writer::createFromString();
        $writer->setDelimiter(';');
        $writer->insertOne($headers);
        $csvData = array();
        foreach ($products as $product) {
            $csvData[] = [
                "sku" => $product->item_number,
                "product-id" => $product->ean,
                "product-id-type" => "EAN",
                "description" => $product->description['de'],
                "internal-description" => $product->description['de'],
                "price" => $product->ek_price,
                "price-additional-info" => "My price Additional innformations",
                "quantity" => $product->stock,
                "min-quantity-alert" => $product->stock - 1,
                "state" => 11,
                "available-start-date" => "2022-08-09T10:45:53+01",
                "available-end-date" => "2022-08-20T10:45:53+01",
                "discount-price" => $product->uvp,
                "discount-start-date" => "2022-08-20T10:45:53+01",
                "discount-end-date" => "2022-08-21T10:45:53+01",
                "discount-ranges" => "",
                "allow-quote-requests" => "true",
                "leadtime-to-ship" => 3,
                "min-order-quantity" => 1,
                "max-order-quantity" => 2,
                "package-quantity" => 1,
                "update-delete" => "UPDATE",
                "price-ranges" => "",
                "ecotax" => "",
                "gift-wrap" => "",
                "min-quantity-ordered" => "",
                "eco-contributions" => ""
            ];
        }
        $writer->insertAll($csvData);
        $writer->output('decathlon.csv');
    }

    private function removeEmoji($string){
        $symbols = "\x{1F100}-\x{1F1FF}" // Enclosed Alphanumeric Supplement
            ."\x{1F300}-\x{1F5FF}" // Miscellaneous Symbols and Pictographs
            ."\x{1F600}-\x{1F64F}" //Emoticons
            ."\x{1F680}-\x{1F6FF}" // Transport And Map Symbols
            ."\x{1F900}-\x{1F9FF}" // Supplemental Symbols and Pictographs
            ."\x{2600}-\x{26FF}" // Miscellaneous Symbols
            ."\x{2700}-\x{27BF}"; // Dingbats

        return preg_replace('/['. $symbols . ']+/u', '', $string);
    }

    public function is_valid_ean($ean) {
        // Check that the code is either 8, 12, or 13 digits long
        if (!preg_match('/^[0-9]{8}$|^[0-9]{12}$|^[0-9]{13}$/', $ean)) {
            return false;
        }
        return !preg_match('/\D/', $ean);
    }

    public function dtShops()
    {
        $data = array();
        $data['shops'] = Shop::where([
            'channel' => 10,
        ])
//            ->whereNotNull('shop_version')
//            ->where(function ($query) {
//                $query->where('url', 'not like', '%localhost%')
//                    ->where('url', 'not like', '%127.0.0.1%')
//                    ->where('url', 'not like', '%droptienda.rocks%');
//            })
            ->orderBy('shop_version','desc')
            ->get();
        return view('test.dtShops',$data);
    }

    public function deleteShop(Request $request)
    {
//        if(CRUDBooster::isSuperadmin()){
//            $shop = Shop::find($request->shop);
//
//            app(ChannelProductService::class)->deleteChannelProducts($shop->id, $shop->user_id);
//            app(ChannelProductService::class)->deleteChannelCategories($shop->id, $shop->user_id);
//
//            app(Droptienda::class)->disconnectShop($shop->toArray());
//            $shop->delete();
//            create_agb_log(
//                $shop->user_id,
//                [
//                    'agb' => config('agb.channel_add'),
//                    'shop_info' => ['shop_name' => $shop->shop_name, 'shop_type' => $shop->channel, 'shop_url' => $shop->url,],
//                ], [],
//                $shop->shop_name . ' Shop Deleted!'
//            );
//        }
        return redirect()->back();
    }


// Function to generate a random EAN
    public function generateEAN()
    {
        $ean = '9'; // Start with 9 to ensure it's a valid EAN-13
        for ($i = 1; $i <= 12; $i++) {
            $ean .= mt_rand(0, 9);
        }
        return $ean;
    }


// Function to generate a random price
    public function generatePrice()
    {
        return number_format(mt_rand(100, 99999) / 100, 2);
    }


    /**
     * @throws Exception
     */
    public function test(Request $request)
    {
        $shop = Shop::find(1002);

        (new Home24($shop->id, $shop->user_id))->export([
            1982202
        ]);
    }

    public function fetchOffers()
    {
        try {
            $endpoint = 'https://novaclub-prod.mirakl.net/api/offers/export';
            $client = new Client(['headers' => $this->getHeader()]);
            $res = $client->get($endpoint)->getBody()->getContents();
            return $this->generateOffersArray($res);
        } catch(\Exception $exception) {
            return new \ArrayIterator();
        }
    }

    protected function getHeader(): array
    {
        return [
            'Authorization' => '58603e27-fee1-4c68-b046-d098ffbbf2cd',
            'Accept' => 'application/json',
            'Content-Type' => 'application/json'
        ];
    }

    /**
     * @throws Exception
     */
    protected function generateOffersArray($offers)
    {
        $reader = Reader::createFromString($offers);
        $reader->setDelimiter(';');
        $reader->setHeaderOffset(0);
        return $reader->getRecords();
    }


    private function countProducts()
    {
        $products = DrmProduct::whereNotNull('manufacturer_id')->count();
        dd($products);
    }

    public function lastErrorReport()
    {
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.colizey.fr/merchant/skus/latest/download?format=csv ',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => array(
                'x-apikey: 8f4526318fb54690a44010d328934f89',
                'content-type: application/json'
            ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        return $response;
    }


    public function testTransfer()
    {
        $allFields = \App\Enums\Product::ALL_FIELDS;
        $allFields[] = 'category';
        app(ChannelProductService::class)->transferProduct(52728843,Channel::SPRINTER,$allFields,'es','create',true,0,[
            'shop_id' => 1048
        ]);
    }
    public function getDtCategories($product_categories)
    {
        $categories = array();
        foreach ($product_categories as $cat) {
            $categories = makeUtf8($cat->category->full_path);
        }
        return $categories;
    }

    private function getStockData($feed, $product)
    {
        $stock = $product->stock;
        if ($stock <= 0) {
            $stock = 'out_of_stock';
        } elseif ($stock > 0) {
            $stock = 'in_stock';
        }
        return $stock;
    }

    private function getStatusData(int $status): string
    {
        return \App\Enums\Product::STATUS[$status] ?? "New";
    }

    private function getTitleDescriptionData($feed, $value, $key = 'title'): string
    {
        $value = !$feed->desc_html ? strip_tags($value) : $value;
        if($key == 'description' && (int)$feed->description_limit > 0){
            $value = substr($value,0,$feed->description_limit);
        }
        return $value;
    }


    private function formatPrice($feed,$price): string
    {
        return number_format($price, 2, '.', '').' EUR';
    }

    public function getHandlingTime($handling_time,$max,$label): string
    {
        $max = $handling_time + $max;
        return trim("$handling_time - $max $label");
    }

    public function getDTPrice($product,$settings): string
    {
        $price = $product->vk_price;
        $offer_price = false;
        if($product->price_on_request == 0 && $product->offer_uvp){
            $price = $product->uvp;
            $offer_price = true;
        }
        elseif($product->price_on_request){
            return 'Auf Anfrage';
        }

        if(!$offer_price){
            if($settings['tax_type'] == 'percent'){
                $price = $price + ($price * $settings['tax_rate'] / 100);
            }
            else{
                $price = $price + $settings['tax_rate'];
            }
        }

        if($settings['round_amount']){
            $prices = explode('.', $price);
            if ($prices[1]) {
                $price = $prices[0].".".$settings['round_amount'];
            }
        }
        return $price;
    }


    public function getPriceSettings($shop): array
    {
        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => $this->getApiUrl($shop->url, 'api/v1/tax_and_round_amount'),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => array('userToken' => $shop->username, 'userPassToken' => $shop->password),
        ));

        $response = curl_exec($curl);

        if(!empty($response)){
            $response = json_decode($response,true);
        }

        return [
            'round_amount' => $response['data']['rount_amount'] ?? 0,
            'tax_rate'     => $response['data']['tax']['rate'] ?? 0,
            'tax_type'     => $response['data']['tax']['type'] ?? 'percent',
        ];
    }

    public function getApiUrl($shop_url, $url): string
    {
        if (substr($shop_url, -1) == '/') {
            $url = $shop_url . $url;
        } else {
            $url = $shop_url . '/' . $url;
        }
        return $url;
    }

    public function getBrandLogo($logo)
    {
        if($logo){
            $brandLogo = json_decode($logo, true);
            return $brandLogo[0];
        }else{
            return '';
        }
    }

    private function getProductUrl($metadata,$custom_shop_url,$shop_url)
    {
        $meta_url = $metadata['url'];
        if($custom_shop_url && $meta_url){
            return str_replace(trim($shop_url,'/'),trim($custom_shop_url,'/'),$meta_url);
        }
        else{
            return $metadata['url'] ?? "";
        }
    }

    private function getImageData($feed, $images,$metadata = array(),$custom_shop_url = '', $shop_url = ''): string
    {
        if(isset($metadata['images'])){
            $images = $metadata['images'];
            if(!is_array($images)){
                $images = json_decode($images,true);
            }

            $images = array_reverse($images);

            if($custom_shop_url){
                $images = array_map(function ($item) use($custom_shop_url,$shop_url){
                    return str_replace(trim($shop_url,'/'),trim($custom_shop_url,'/'),$item);
                },$images);
            }
        }
        if ($feed->image_limit && is_array($images)) {
            $images = array_splice($images, 0, $feed->image_limit);
        }
        return implode(';', $images ?? []);
    }



    public function addOptionalAspects()
    {
        $categories = ChannelCategory::where([
            'channel' => Channel::EBAY,
            'status' => 3
        ])->orderBy('category_id','DESC')->chunk(500, function($cats) {
            $service = new Api();

            foreach ($cats as $category) {
                $aspects = $service->getCategoryAspects($category->category_id);

                if($aspects){
                    $required_aspects = $aspects->where('aspectConstraint.aspectRequired',true);
                    $optional_aspects = $aspects->where('aspectConstraint.aspectRequired',false);

                    $misc = $category->misc;
                    $misc['required_aspects'] = json_decode(json_encode($required_aspects),true);
                    $misc['optional_aspects'] = json_decode(json_encode($optional_aspects),true);

                    $category->misc = $misc;
                    $category->status = 1;

                    $category->save();
                }

            }

        });
    }

    public function updateShopVersion(Request $request)
    {
        if($request->latest){
            $shops = Shop::where('channel',Channel::DROPTIENDA)
                ->where('shop_version','!=',$request->latest)->get();
        }else {
            $shops = Shop::where('channel',Channel::DROPTIENDA)->get();
        }
        foreach ($shops as $shop) {
            try {
                $url = trim($shop->url,'/').'/api/v1/shop_version';
                dump($url);
                $shop_info = json_decode(file_get_contents($url),true);

                if(isset($shop_info['version'])){
                    if($shop->shop_version!=$shop_info['version']){
                        $shop->shop_version = $shop_info['version'];
                        $shop->save();
                    }
                }
            }catch (Exception $e){
//                    dump('Error:'.$url);
            }
        }


        return "Shop version updated successfully!";
    }


    public function get50kProducts(Request $request)
    {
        $query = ChannelProduct::query();
        $products = $query->orderBy('id')->offset($request->offset ?? 0)->limit($request->limit ?? 50000)->cursor();
        $writer = Writer::createFromString();

        $header = array(
            "ean",
            "item_number",
            "title",
            "description",
            "image",
            "ek_price",
            "stock",
            "item_weight",
            "item_size",
            "item_color",
            "category",
            "tags",
            "status",
            "gender"
        );

        $writer->insertOne($header);

        foreach ($products as $product) {
            if(!is_array($product->title)){
                $product->title = json_decode($product->title,true)??array();
            }
            if(!is_array($product->description)){
                $product->description = json_decode($product->description,true)??array();
            }

            $title = $product->title['de'] ?? "";
            $description = $product->description['de'] ?? "";

            // $category = $category = app(Lengow::class)->getCategories($product,'de');

            $data = [
                "ean" => $product->ean,
                "item_number" => $product->item_number,
                "title" => $title,
                "description" => $description,
                "image" => implode(',',$product->images),
                "ek_price" => $product->ek_price,
                "stock" => $product->stock,
                "item_weight" => $product->item_weight,
                "item_size" => $product->item_size,
                "item_color" => $product->item_color,
                "category" => "default",
                "tags" => $product->tags,
                "status" => $product->status,
                "gender" => $product->gender,
            ];
            $writer->insertOne($data);
        }
        $writer->output('import.csv');
    }


    /**
     * @throws CannotInsertRecord
     */
    public function mpBrandCsv()
    {
        $categories = Category::whereIn('parent_id',[1,10,12,28])->pluck('id')->toArray();
        $brands = array_filter(Product::whereIn('category_id',$categories)->groupBy('brand')->pluck('brand')->toArray());

        $writer = Writer::createFromString();

        $writer->insertOne([
            'Brand Name'
        ]);

        foreach($brands as $brand){
            $writer->insertOne([
                'Brand Name' => $brand
            ]);
        }

        $writer->output('brands.csv');
    }


    public function syncEbayCategory()
    {
//        $ebay_api = new EbayApi();
//        $access = $ebay_api->getAppToken();
//
//        $client = new Client();
//        $response  = $client->get("https://api.ebay.com/commerce/taxonomy/v1/category_tree/77",  [
//            'headers' => [
//                'Accept' => 'application/json',
//                'Content-Type' => 'application/json',
//                'Authorization' => 'Bearer ' . $access
//            ]
//        ]);
//        file_put_contents('ebay_categories.json',$response->getBody()->getContents());
//        dd('ok');
//        $content = json_decode($response->getBody()->getContents())->rootCategoryNode->childCategoryTreeNodes;
        $json = json_decode(file_get_contents('ebay_categories.json'))->rootCategoryNode->childCategoryTreeNodes;
        foreach ($json as $key => $level_1)
        {
            foreach ($level_1->childCategoryTreeNodes as $level_2)
            {
                $parent_name_1 = $level_1->category->categoryName;
                $parent_1 = $level_1->category->categoryId;
                if($level_2->leafCategoryTreeNode){
                    $insert_data = [
                        'category_id' => $level_2->category->categoryId,
                        'category_name' => $parent_name_1." > ".$level_2->category->categoryName,
                        'parent' => $parent_1
                    ];
                    DB::table('channel_categories_temp')->insert($insert_data);
                }

                if (is_array($level_2->childCategoryTreeNodes))
                {
                    foreach ($level_2->childCategoryTreeNodes as $level_3)
                    {
                        $parent_name_2 = $parent_name_1 . " > " . $level_2->category->categoryName;
                        $parent_2 = $level_2->category->categoryId;

                        if($level_3->leafCategoryTreeNode){
                            $insert_data = [
                                'category_id' => $level_3->category->categoryId,
                                'category_name' => $parent_name_2." > ".$level_3->category->categoryName,
                                'parent' => $parent_2
                            ];
                            DB::table('channel_categories_temp')->insert($insert_data);
                        }

                        if (is_array($level_3->childCategoryTreeNodes))
                        {
                            foreach ($level_3->childCategoryTreeNodes as $level_4)
                            {
                                $parent_name_3 = $parent_name_2 . " > " . $level_3->category->categoryName;
                                $parent_3 = $level_3->category->categoryId;
                                if($level_4->leafCategoryTreeNode){
                                    $insert_data = [
                                        'category_id' => $level_4->category->categoryId,
                                        'category_name' => $parent_name_3." > ".$level_4->category->categoryName,
                                        'parent' => $parent_3
                                    ];
                                    DB::table('channel_categories_temp')->insert($insert_data);
                                }
                                if (is_array($level_4->childCategoryTreeNodes))
                                {
                                    foreach ($level_4->childCategoryTreeNodes as $level_5)
                                    {
                                        $parent_name_4 = $parent_name_3 . " > " . $level_4->category->categoryName;
                                        $parent_4 = $level_4->category->categoryId;

                                        if ($level_5->leafCategoryTreeNode)
                                        {
                                            $insert_data = [
                                                'category_id' => $level_5->category->categoryId,
                                                'category_name' => $parent_name_4 . " > " . $level_5->category->categoryName,
                                                'parent' => $parent_4
                                            ];
                                            DB::table('channel_categories_temp')->insert($insert_data);
                                        }

                                        if (is_array($level_5->childCategoryTreeNodes))
                                        {
                                            foreach ($level_5->childCategoryTreeNodes as $level_6)
                                            {
                                                $parent_name_5 = $parent_name_4 . " > " . $level_5->category->categoryName;
                                                $parent_5 = $level_5->category->categoryId;

                                                if ($level_6->leafCategoryTreeNode)
                                                {
                                                    $insert_data = [
                                                        'category_id' => $level_6->category->categoryId,
                                                        'category_name' => $parent_name_5 . " > " . $level_6->category->categoryName,
                                                        'parent' => $parent_5
                                                    ];
                                                    DB::table('channel_categories_temp')->insert($insert_data);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    public function syncMpProducts()
    {
//        DrmProduct::whereNotNull('marketplace_product_id')
//            ->select(
//                'drm_products.stock as drm_stock',
//                'drm_products.ek_price as drm_price',
//                'drm_products.user_id',
//                'drm_products.id as drm_product_id',
//                'drm_products.marketplace_product_id',
//            )
//            ->chunk(1000, function ($chunk) {
//                $mpProducts = Product::whereIn('id', $chunk->pluck('marketplace_product_id')->toArray())
//                    ->select(
//                        'id',
//                        'stock as marketplace_stock',
//                        'ek_price as marketplace_price'
//                    )->get();
//
//                $data = $chunk->map(function ($item) use ($mpProducts){
//                    $item = $item->toArray();
//                    $mpProduct = $mpProducts->where('id',$item['marketplace_product_id'])->first();
//                    if(!$mpProduct){
//                        app(DRMProductService::class)->destroy([$item['drm_product_id']],$item['user_id']);
//                    }else{
//                        if($item['marketplace_stock']){
//                            $item['marketplace_stock'] = $mpProduct->marketplace_stock;
//                            $item['marketplace_price'] = $mpProduct->marketplace_price;
//                        }
//                    }
//
//                    return $item;
//                });
//
//                dd($data, $mpProducts);
//            });

        DrmProduct::whereNotNull('marketplace_product_id')
            ->select(
                'drm_products.stock as drm_stock',
                'drm_products.ek_price as drm_price',
                'drm_products.user_id',
                'drm_products.id as drm_product_id',
                'drm_products.marketplace_product_id',
            )
            ->chunk(1000, function ($chunk) {
                $mpProducts = Product::whereIn('id', $chunk->pluck('marketplace_product_id')->toArray())
                    ->select(
                        'id',
                        'stock as marketplace_stock',
                        'ek_price as marketplace_price'
                    )->get();

                $data = $chunk->map(function ($item) use ($mpProducts){
                    $item = $item->toArray();
                    $mpProduct = $mpProducts->where('id',$item['marketplace_product_id'])->first();
                    $item['marketplace_stock'] = $mpProduct->marketplace_stock;
                    $item['marketplace_price'] = $mpProduct->marketplace_price;
//                    if(!$mpProduct){
//                        app(DRMProductService::class)->destroy([$item['drm_product_id']],$item['user_id']);
//                    }else{
//                        $item['marketplace_stock'] = $mpProduct->marketplace_stock;
//                        $item['marketplace_price'] = $mpProduct->marketplace_price;
//                    }
                    return $item;
                })->all();

                StockSyncReport::insert($data);
            });
    }
    /**
     * @throws Exception
     */
    public function copyDrmCategories($new_product_id, $product_categories, $to)
    {
        foreach ($product_categories as $product_category)
        {
            $category = $product_category->drm_category;
            $new_category = app('App\Services\DRMCategoryService')->updateOrCreate('',[
                'category_name_de' => $category->category_name_de,
                'country_id' => 1,
                'user_id' => $to
            ],$to,1,'de');

            if(isset($new_category['errors'])){
                $new_category = $new_category['data'];
            }

            $new_category_id = $new_category->id;
            $data = $product_category->toArray();

            unset($data['id']);
            unset($data['drm_category']);
            $data['product_id'] = $new_product_id;
            $data['category_id'] = $new_category_id;

            DRMProductCategory::create($data);
        }
    }

    public function getCategories($product): array
    {
        return [];
        $categories = array();
        foreach ($product->channel_categories as $channel_category) {
            $category = $channel_category->category;
            $name = $category->full_path;
            $paths[] = ['id' => $category->id,'category_name' => $category->category_name];
            if ($name != "" && $name != null) {
                $categories[] = [
                    'drm_ref_id' => $category->id,
                    'category_name'   => $name,
                    'category_tree' => array_reverse(array_merge($paths,$this->getParentCategory($category)))
                ];
            }
        }
        return array_filter($categories);
    }

    public function getParentCategory($category): array
    {
        $paths = array();
        if($category->parent_category){
            $parent_category = $category->parent_category;
            $paths[] = ['id' => $parent_category->id,'category_name' => $parent_category->category_name];
            $paths = array_merge($paths,$this->getParentCategory($parent_category));
        }
        return $paths;
    }

    public function getNotSyncedProducts($user_id)
    {
        $channelProducts = ChannelProduct::where([
            'user_id' => $user_id
        ])->where('drm_product_id','<>',0)->cursor();

        $notSynced = array();

        foreach ($channelProducts as $channelProduct) {
            $drmProduct = DrmProduct::where([
                'id' => $channelProduct->drm_product_id,
                'user_id' => $channelProduct->user_id
            ])->first();

            if($drmProduct)
            {
                $update_status = $channelProduct->update_status;
                if($drmProduct->stock!=$channelProduct->stock && $update_status['stock'])
                {
                    $notSynced[] = $channelProduct;
                }
            }

        }

        dd($notSynced);
    }

    public function autoAssignCalculation(Request $request)
    {
        $products = ChannelProduct::where([
            'user_id' => $request->user_id,
            'channel' => 10
        ])->pluck('id')->toArray();

        app('App\Services\ChannelProductService')->assignCalc($products,$request->calculation_id,10);

    }

    public function getChildCategories($category_id,$user_id)
    {
        $childs = ChannelUserCategory::where([
            'user_id' => $user_id,
            'parent'  => $category_id
        ])->pluck('id')->toArray();

        foreach ($childs as $child) {
            $this->getChildCategories($child->id,2560);
        }
    }


    public function fixUpdateStatus()
    {
        $products = DrmProduct::where('update_status','NOT LIKE','%uvp%')->where('user_id',96)->get();

        foreach ($products->chunk(500) as $chunk)
        {
            foreach ($chunk as $product)
            {
                $update_status = json_decode($product->update_status,true);
                $update_status['uvp'] = 1;
                $product->update_status = json_encode($update_status);
                $product->save();
            }
        }

    }


    public function transferProducts()
    {
        $users = DB::connection('backup')->table('channel_products')->pluck('user_id')->unique()->toArray();

        foreach ($users as $user) {
            $backup = DB::connection('backup')->table('channel_products')
                ->where([
                    'user_id' => $user
                ])->count();

            $current = DB::table('channel_products')
                ->where([
                    'user_id' => $user
                ])->count();

            dump("User: $user Previously: $backup Currently: $current");
        }
    }

    public function replaceDeToEn()
    {
        $channel_products = ChannelProduct::where(['user_id' => 2266,'country_id' => 1])->select('id', 'title', 'description')->get();

        if($channel_products){
            foreach($channel_products as $value){
                if($value->title['en']){
                    $title = ['de' => $value->title['en']];
                    ChannelProduct::where('id', $value->id)->update(['title' => $title]);
                }
                if($value->description['en']){
                    $description = ['de' => $value->description['en']];
                    ChannelProduct::where('id', $value->id)->update(['description' => $description]);
                }
            }
        }
    }

    public function multipleItemNumberDelete(){
        $duplicateItemNumberRecords = DrmProduct::selectRaw("count('id') as total, item_number")->where('drm_import_id', 1664)->groupBy('item_number')->havingRaw('total > 1')->get();

        if($duplicateItemNumberRecords)
        {
            foreach($duplicateItemNumberRecords as $record)
            {
                $allItems = DrmProduct::where('drm_import_id', 1664)->select('id', 'item_number')->where('item_number', $record->item_number)->orderBy('id', 'desc')->take($record->total - 1)->pluck('id')->toArray();
                app(DRMProductService::class)->destroy($allItems,52);
            }
        }
        // dd("All duplicate Product deleted.");
    }

    public function fixParentCategories()
    {
        $categories = ChannelUserCategory::where('parent',0)
            ->groupBy('category_name','user_id')
            ->havingRaw("COUNT(category_name) > 1")
            ->get();

        foreach ($categories as $category)
        {
            $duplicates = ChannelUserCategory::where([
                'user_id' => $category->user_id,
                'parent'  => 0,
                'category_name' => $category->category_name
            ])->where('id','<>',$category->id)->get();

            foreach ($duplicates as $duplicate)
            {
                ChannelUserCategory::where('parent',$duplicate->id)->update(['parent' => $category->id]);
                ChannelProductCategory::where('category_id',$duplicate->id)->update(['category_id' => $category->id]);
                $duplicate->delete();
            }
        }
    }

    public function fixCategories()
    {
        $this->fixWohulf();
        die();
        $categories = ChannelUserCategory::where([
            'channel'   => 10
        ])->get();

        foreach ($categories->chunk(500) as $chunk)
        {
            foreach($chunk as $category)
            {
                if (preg_match("/( - |>)/", $category->full_path)) {
                    $paths = array_map('trim',explode('>',$category->full_path));
                    $level = 1;
                    foreach ($paths as $key => $path)
                    {
                        if($path == $category->category_name)
                        {
                            $level = $key+1;
                        }
                    }
                    $category->level = $level;
                    $category->save();
                }
            }
        }
    }

    public function fixFullPath($user_id,$channel)
    {
        $channelCategories = ChannelUserCategory::where([
            'user_id' => $user_id,
            'channel' => $channel
        ])->get();

        foreach ($channelCategories as $channelCategory) {
            $channelCategory->full_path = $channelCategory->full_path_generated;
            $channelCategory->save();
        }
    }

    public function syncDTCategory($uuid)
    {
        $categories = ChannelUserCategory::where([
            'channel'   => 10,
            'user_id'   => $uuid,
            'parent'    => 0
        ])->get();


        foreach ($categories as $category)
        {
            DroptiendaSyncHistory::create([
                'sync_type' => DroptiendaSyncType::CATEGORY,
                'sync_event' => $category->shop_category_id ? DroptiendaSyncEvent::UPDATE : DroptiendaSyncEvent::CREATE,
                'model_id' => $category->id,
                'user_id' => $category->user_id,
            ]);
        }
    }

    public function autoTransferCategories($uuid)
    {

    }

    public function restoreTwoFa()
    {
        $users = User::all();
        foreach ($users as $user)
        {
            $two_fa = DB::connection('backup')
                ->table('cms_users')
                ->where('id',$user->id)
                ->value('two_fa_status');

            $user->two_fa_status = $two_fa;
            $user->save();
        }
    }

    public function syncConnectionStatus()
    {
        $products = ChannelProduct::where([
            'connection_status' => 4
        ])->cursor();

        foreach ($products as $product) {
            ChangeChannelProductConnectionStatus::dispatch($product->id);
        }
    }

    public function addMissingFeeds()
    {
        $users = DB::table('cms_users')->whereIn('id',[2591,2565])->get();

        foreach ($users as $user)
        {
            $suppliers = Supplier::where([
                'user_id' => $user->id
            ])->get();

            foreach ($suppliers as $supplier) {
                $file_name = $supplier->name." manual products";
                $feed_exists = DrmImport::where([
                    'user_id' => $user->id,
                    'delivery_company_id' => $supplier->id,
                    'csv_file_name' => $file_name,
                ])->first();

                $first_product = DrmProduct::where([
                    'delivery_company_id'=> $supplier->id,
                    'user_id'           => $user->id,
                    'drm_import_id'     => null
                ])->first();

                if(!$feed_exists){
                    if($first_product)
                    {
                        $csv_file_path = "public/csv_files/".Str::random(40).'.csv';
                        $drm_id = DrmImport::create([
                            'user_id'=> $user->id,
                            'delivery_company_id'=> $supplier->id,
                            'csv_file_name'     => $file_name,
                            'type'              => 'file',
                            'csv_file_path'     => $csv_file_path,
                            'country_id'        => $first_product->country_id,
                            'delimiter'         => ',',
                            'import_finished'   => 1,
                            'money_format'      => 1
                        ])->id;

                        DrmProduct::where([
                            'delivery_company_id'=> $supplier->id,
                            'user_id'           => $user->id,
                            'drm_import_id'     => null
                        ])->update(['drm_import_id' => $drm_id]);

                        GenerateCsv::dispatch($drm_id, $csv_file_path,$user->id);
                    }
                }
                else{
                    DrmProduct::where([
                        'delivery_company_id'=> $supplier->id,
                        'user_id'           => $user->id,
                        'drm_import_id'     => null
                    ])->update(['drm_import_id' => $feed_exists->id]);
                    GenerateCsv::dispatch($feed_exists->id, $feed_exists->csv_file_path,$user->id);
                }
            }
        }
    }


    public function removeDuplicates($shopId){
        $duplicateCategories = ChannelUserCategory::select('id', 'category_name', 'shop_id', 'parent')
            ->groupBy('category_name', 'shop_id', 'parent')
            ->where('shop_id',$shopId)
            ->havingRaw('COUNT(*) > 1')
            ->get();

        foreach ($duplicateCategories as $category) {
            $duplicates = ChannelUserCategory::where([
                'category_name' => $category->category_name,
                'shop_id' => $category->shop_id,
                'parent' => $category->parent,
            ])->get();

            $firstCategory = $duplicates->first();

            foreach ($duplicates as $duplicate) {
                if ($duplicate->id !== $firstCategory->id) {
                    ChannelUserCategory::where([
                        'parent' => $duplicate->id,
                    ])->update(['parent' => $firstCategory->id]);

                    ChannelProductCategory::where([
                        'category_id' =>  $duplicate->id,
                        'category_type' => CategoryType::USER
                    ])
                    ->update(['category_id' => $firstCategory->id]);

                    $duplicate->delete();
                }
            }
        }
    }

    public function duplicateChannelProductRemove(){
        $user_droptienda_product = ChannelProduct::where([
            'user_id'=> 2693,
            'channel' => 10
        ])
            ->pluck('ean')
            ->toArray();

        $total_number_of_ean = array_count_values($user_droptienda_product);
        // dd($total_number_of_ean);

        foreach($total_number_of_ean as $ean => $total_number){
            if($total_number > 1){
                $duplicate_product_id = ChannelProduct::where([
                    'user_id'=> 2693,
                    'channel' => 10,
                    'ean' => $ean
                ])
                    ->orderBy('id', 'desc')
                    ->pluck('id')
                    ->take($total_number - 1)
                    ->toArray();

                ChannelProduct::whereIn('id', $duplicate_product_id)->delete();
            }
        }
        // dd(array_count_values($user_droptienda_product), count(array_keys($user_droptienda_product, 4260184631452)), count(array_keys($user_droptienda_product, 4260184614561)), count(array_keys($user_droptienda_product, 4260184614035)));
    }

    public function fixMissingUpdateStatus(int $user_id = 62)
    {
        $update_array = [
            'ek_price',
            'uvp',
            'stock',
            'item_weight',
            'item_size',
            'item_color',
            'note',
            'production_year',
            'brand',
            'materials',
            'gender',
            'industry_template_data',
            'status',
            'delivery_days',
            'title',
            'description'
        ];
        $products = ChannelProduct::where(['user_id' => $user_id])->cursor();
        foreach ($products as $product){
            $drmProduct = DrmProduct::where(['id' => $product->drm_product_id])->first();
            $status = $product->update_status;
            $updates = array();
            foreach ($update_array as $key) {
                if($drmProduct->$key != $product->$key){
                    $status[$key] = 0;
                }
                else{
                    $status[$key] = 1;
                    $updates[] = $key;
                }
            }
            if($drmProduct->image != $product->images){
                $status['images'] = 0;
            }else{
                $status['images'] = 1;
                $updates[] = 'images';
            }
            $product->update_status = $status;
            $product->save();
            $lang = app('App\Services\UserService')->getLang($user_id);

            if(count($updates)){
                $channelProductService = new ChannelProductService;
                $channelProductService->transferProduct($drmProduct->id, $product->channel, $updates, $lang);
            }
        }
    }


    public function fixCustomerNames(){
        $customers = NewCustomer::where(['full_name' => 'Droptienda Staging'])->cursor();

        $count = 0;
        foreach ($customers as $customer) {
            $billing = json_decode($customer->billing,true);
            $customer->full_name = $billing['name'] ?? null;
            $customer->save();
            echo "\rFixed ".$count." Customers";
            $count++;
        }
    }

    /**
     * @throws Exception
     */
    public function refreshAllProducts($user_id)
    {
        $products = ChannelProduct::where([
            'user_id' => $user_id,
            'connection_status' => 1,
            'channel' => 10
        ])->pluck('id')->toArray();

        app('App\Services\ChannelProductService')->refreshConnection($products,10,$user_id);

    }

    public function deleteAllCategories($user_id)
    {
        $categories = ChannelUserCategory::where([
            'user_id' => $user_id,
            'channel' => 10
        ])->get();

        foreach ($categories as $category)
        {
            DroptiendaSyncHistory::create([
                'sync_type' => DroptiendaSyncType::CATEGORY,
                'sync_event' => DroptiendaSyncEvent::DELETE,
                'model_id' => $category->id,
                'user_id' => $user_id,
            ]);
        }
    }


    public function mpReport()
    {
        $report = StockSyncReport::where('fixed', 0)
            ->where(function ($q) {
                $q->whereRaw('marketplace_price != drm_price')
                    ->orWhereRaw('marketplace_stock != drm_stock');
            })->count();
        dd($report);
    }

    public function mpSync()
    {
        Artisan::call("mp:sync");
    }

    public function getDebugLogs(Request $request)
    {
        $logs = Log::where('module',$request->flag)->get();

        foreach ($logs as $log) {
            echo $log->created_at.": ".$log->message."<br>";
        }
    }

    public function removeDuplicateProducts($user_id){
        $products = DrmProduct::where([
            'user_id' => $user_id
        ])->withCount('connected_products')->get()->groupBy('ean');

        $toBeDeleted = array();
        foreach ($products as $key => $product) {
            if(count($product) > 1){
                $toBeStay = $product->first();
                ChannelProduct::where([
                    'user_id' => $user_id,
                    'ean'     => $toBeStay->ean
                ])->update(['drm_product_id' => $toBeStay->id]);
                $notConnected = $product->where('id','!=',$toBeStay->id)->first();
                if($notConnected){
                    $toBeDeleted[] = $notConnected->id;
                }
            }
        }
        app('App\Services\DRMProductService')->destroy($toBeDeleted,$user_id);
    }

    public function exportToShop(){
        $user_id = CRUDBooster::myParentId();
        app('App\Services\Modules\Export\CHECK24')->export($user_id);

    }

    public function brandTransfer()
    {
        $allUsersId = DB::table('drm_products')
            ->distinct()
            ->pluck('user_id')
            ->toArray();

        // $allUsersId = [212];

        foreach($allUsersId as $user_id){

            // $existing_brands = DB::table('dropmatix_product_brands')
            // ->where('user_id',  $user_id)
            // ->pluck('brand_name')
            // ->toArray();

            DB::table('drm_products')
                ->where('user_id', $user_id)
                ->select('user_id', 'brand')
                ->orderBy('created_at')
                ->chunk(200, function($data) use($user_id){
                    $data = $data->toArray();

                    $existing_brands = DB::table('dropmatix_product_brands')
                        ->where('user_id',  $user_id)
                        ->pluck('brand_name')
                        ->toArray();

                    // $brands_unique_name = array_unique(array_column($data, 'brand'));
                    $brands_unique_name = array_unique(array_map('strtolower', array_column($data, 'brand')));
                    $data = array_intersect_key($data, $brands_unique_name);

                    $data = array_filter($data, function ($value) use ($existing_brands) {
                        return !in_array(strtolower($value->brand), array_map('strtolower', $existing_brands));
                    });

                    $data = array_values($data);

                    $insertData = [];

                    array_walk($data, function ($item) use (&$insertData) {
                        if($item->brand && !is_numeric($item->brand)){
                            $tempData = [];
                            $tempData['user_id'] = $item->user_id;
                            $tempData['brand_name'] = $item->brand;
                            $tempData['created_at'] = Carbon::now()->toDateTimeString();
                            $tempData['updated_at'] = Carbon::now()->toDateTimeString();

                            $insertData[] = $tempData;
                        }

                    });

                    if($insertData){
                        // foreach(array_chunk($insertData, 500) as $chunkData){
                        DB::table('dropmatix_product_brands')->insert($insertData);
                        // }
                    }
                });
        }

        dd('Done');

    }

    public function brandUpdate()
    {
        $allUsersId = DB::table('drm_products')
            ->distinct()
            ->pluck('user_id')
            ->toArray();

        // $allUsersId = [212];

        foreach($allUsersId as $user_id){

            $existing_brands = DB::table('dropmatix_product_brands')
                ->where('user_id',  $user_id)
                ->select('id', 'brand_name')
                ->get();

            DrmProduct::where('user_id', $user_id)
                ->select('id', 'user_id', 'brand')
                ->orderBy('created_at')
                ->chunk(50, function($data) use($existing_brands){

                    $data->each(function ($item) use ($existing_brands) {
                        if($item->brand && !is_numeric($item->brand)){
                            $brand_info = $existing_brands->where('brand_name', $item->brand)->first();

                            if($brand_info){
                                $item->brand = $brand_info->id;
                                $item->save();
                            }
                        }
                    });

                });
        }

        dd("Updated");

    }

    public function channelBrandUpdate()
    {
        $allUsersId = DB::table('channel_products')
            ->distinct()
            ->pluck('user_id')
            ->toArray();

        // $allUsersId = [212];
        $count = 0;

        foreach($allUsersId as $user_id){

            $existing_brands = DB::table('dropmatix_product_brands')
                ->where('user_id',  $user_id)
                ->select('id', 'brand_name')
                ->get();

            if($existing_brands){

                DB::table('channel_products')->where('user_id', $user_id)
                    ->select('id', 'user_id', 'brand')
                    ->whereNotNull('brand')
                    ->whereRaw('CAST(brand as SIGNED) = 0')
                    ->orderBy('created_at')
                    ->chunk(500, function($data) use($existing_brands, &$count){

                        foreach($data as $item){
                            if(!empty($item->brand) && !is_numeric($item->brand)){
                                $brand_info = $existing_brands->where('brand_name', $item->brand)->first();

                                if($brand_info){
                                    // $item->brand = $brand_info->id;
                                    // $item->save();

                                    DB::table('channel_products')
                                        ->where(['id' => $item->id,'user_id' => $item->user_id])
                                        ->update(['brand' => $brand_info->id]);
                                }

                                echo "\r". ++$count;
                            }
                        }

                    });
            }

        }

        dd("Channel Brand Updated");

    }

    public function marketplaceBrandTransfer(){

        $allUsersId = DB::connection('marketplace')->table('marketplace_products')
            ->distinct()
            ->pluck('delivery_company_id')
            // ->pluck('supplier_id')
            ->toArray();

        foreach($allUsersId as $user_id){

            DB::connection('marketplace')->table('marketplace_products')
                ->where('delivery_company_id', $user_id)
                ->select('delivery_company_id', 'brand')
                ->orderBy('created_at')
                ->chunk(200, function($data) use($user_id){
                    $data = $data->toArray();

                    $existing_brands = DB::connection('marketplace')->table('marketplace_product_brand')
                        ->where('user_id',  $user_id)
                        ->pluck('brand_name')
                        ->toArray();

                    // $brands_unique_name = array_unique(array_column($data, 'brand'));
                    $brands_unique_name = array_unique(array_map('strtolower', array_column($data, 'brand')));
                    $data = array_intersect_key($data, $brands_unique_name);

                    $data = array_filter($data, function ($value) use ($existing_brands) {
                        return !in_array(strtolower($value->brand), array_map('strtolower', $existing_brands));
                    });

                    $data = array_values($data);
                    $insertData = [];

                    array_walk($data, function ($item) use (&$insertData) {
                        if($item->brand && !is_numeric($item->brand)){
                            $tempData = [];
                            $tempData['user_id'] = $item->user_id;
                            $tempData['brand_name'] = $item->brand;
                            $tempData['created_at'] = Carbon::now()->toDateTimeString();
                            $tempData['updated_at'] = Carbon::now()->toDateTimeString();

                            $insertData[] = $tempData;
                        }

                    });

                    if($insertData){
                        // var_dump($insertData);
                        // DB::connection('marketplace')->table('marketplace_product_brand')->insert($insertData);
                    }
                });
        }

    }

    public function marketplaceBrandUpdate(){
        $allUsersId = DB::connection('marketplace')->table('marketplace_products')
            ->distinct()
            ->pluck('delivery_company_id')
            ->toArray();

        foreach($allUsersId as $user_id){

            $existing_brands = DB::connection('marketplace')->table('marketplace_product_brand')
                ->where('user_id',  $user_id)
                ->select('id', 'brand_name')
                ->get();

            Product::where('user_id', $user_id)
                ->select('id', 'delivery_company_id', 'brand')
                ->orderBy('created_at')
                ->chunk(50, function($data) use($existing_brands){

                    $data->each(function ($item) use ($existing_brands) {
                        if($item->brand && !is_numeric($item->brand)){
                            $brand_info = $existing_brands->where('brand_name', $item->brand)->first();

                            if($brand_info){
                                $item->brand = $brand_info->id;
                                $item->save();
                            }
                        }
                    });

                });
        }

        dd("Updated");
    }
    public function traitSendMail(){
        // $order = \App\NewOrder::where('id', 12)->first()->toArray();
        // $user_id = $order['cms_user_id'];
        // unset($order['cms_user_id']);
        // $order['user_id'] = $user_id;

        // $this->insertMarketplaceOrderToDatabase($order);

        // $order = \App\NewOrder::where('id', 8)->first()->toArray();
        // $user_id = $order['cms_user_id'];
        // unset($order['cms_user_id']);
        // $order['user_id'] = $user_id;

        // $this->insertMarketplaceOrderToDatabase($order);

        $order = \App\NewOrder::where('id', 8)->first();
        // dd($order);
        $cart = [
            [
                "id" => 0,
                "product_name" => "Herzberg HG-8049EA: Erdbohrer",
                "description" => "Herzberg HG-8049EA: ErdbohrerDer ultimative Bohrer und Bagger mit erstaunlichen Innovationen ...",
                "qty" => "1",
                "rate" => 22,
                "tax" => "0.00",
                "image" => null,
                "product_discount" => 0,
                "ean" => "0634158798542",
                "amount" => "22"
            ]
        ];

        $this->placeOrderProduct($order, $cart, 618);

    }

    public function brokenImageProductRejected(){
        $rejectedProductId = [];
        $mpProducts = Product::where('is_image_check',0)->select('id','image')->take(2)->get();
        foreach($mpProducts->chunk(1) as $products){
            foreach($products as $product){
                $response = @getimagesize($product->image[0]);
                if($response == false){
                    $rejectedProductId[] = $product->id;
                    $product->update(['status'=>2]);
                }
                $product->update(['is_image_check'=>1]);
            }
        }
        foreach($rejectedProductId as $rejectedProduct){
            app(\App\Services\Marketplace\ProductService::class)->revokedProductDelete($rejectedProduct);
        }
        dd('destroy id',$rejectedProductId);
    }




    public function validationUi(\Illuminate\Http\Request $request)
    {



        $payload = $request->input();
        $transectionId = $request->header('X-Transaction-Id');
        $referer = $request->header('X-Transaction-Referer'); // $_SERVER['HTTP_REFERER']
        $token = 'a98ec94f1f6af5d2db42dbf34b15ef98fef8fa248617636bf7a88fca8c24be1a';

        return app(\App\Services\UiValidation\UiValidation::class)->validate($payload, $referer, $transectionId);

        dd($d);

        $curl = curl_init();
        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://endereco-service.de/rpc/v1',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($payload),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                "X-Auth-Key: {$token}",
                "X-Transaction-Id: {$transectionId}",
                "X-Transaction-Referer: {$referer}",
                "X-Agent: DRM v7.0.0",
            ],
        ));

        $response = curl_exec($curl);
        curl_close($curl);

        return $response;
        dd($response);
    }
    public function mailTest()
    {
        try {
            $campaigns = \App\EmailMarketing::has('tags')->has('steps')->where('status', 1)->where('user_id', CRUDBooster::myParentId())->get();
            // dd("Campaigns", $campaigns);
            if ($campaigns->isNotEmpty()) {
                // Log::channel('command')->info('Scheduler Send step mail function called');
                $command = 'handle loop called';
                foreach ($campaigns as $campaign) {
                    // Log::channel('command')->info('Campaign loop');
                    $this->sendStepEmail($campaign);
                }
            }
        } catch (Exception $exception) {
            $command = 'handle exception called';
            // Log::channel('command')->info('email campaign history email send error start');
            // Log::channel('command')->error($exception->getMessage());
            // Log::channel('command')->info('email campaign history email send error end');
        }
    }

    public function getDropmatixArchiveInvoice()
    {
        try {
            $user = User::find(2455);

            if ($user) {
                $orders = NewOrder::where('cms_user_id', 2455)->where('drm_customer_id', 66103)->pluck('id')->toArray();

                if (count($orders)) {
                    InvoiceArchiveJob::dispatch($orders, $user)->onQueue('file-archive');
                    return response()->json([
                        'success' => true,
                        'message' => 'Archive processing! After completing the process, we sent you notification!',
                    ]);
                } else {
                    throw new Exception("Invalid order selection. Please select only valid invoices.");
                }
            } else {
                throw new Exception("Error Processing Request");
            }
        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Archive failed. Error: ' . $e->getMessage(),
            ]);
        }
    }

    public function creditRemoveLogTransfer()
    {
        DB::table('drm_user_credit_remove_logs')->orderBy('id')
            ->chunk(100, function($logs){

                $data_insert = [];

                foreach($logs as $log){
                    $data_insert[] = [
                        'user_id' => $log->user_id,
                        'credit' => $log->credit,
                        'message' => $log->message,
                        'type' => $log->type,
                        'status' => -1,
                        'created_at' => $log->created_at,
                        'updated_at' => $log->updated_at,
                    ];
                }

                DB::table('drm_user_credit_add_logs')->insert($data_insert);

            });

        dd("DONE");
    }
    function roundUpToNearestFive($number) {
        if ($number % 5 == 3) {
            return $number + 2;
        } elseif ($number % 5 == 2) {
            return $number + 3;
        } else {
            return ceil($number / 5) * 5;
        }
    }
    public function script_test() {
        $shop = \App\Shop::where(['id' => 1134, 'status' => true])->first();

        $response = app(Colizey::class)->sendCsvToShop($shop,url('api/product/export?token=qhJOjCE6R5yA24McvXcANnmeQ4tm2X7YhZO1geuR'));
        dd($shop, $response);
        $shop = \App\Shop::where(['id' => 1094, 'status' => true])->first();
        $reports = app(Colizey::class)->productUpdateByFeedBack($shop);

        if ($reports['success']) {
            $skus = $reports['data']->where('sku', '!=', null);

            // Update ChannelProduct statuses and unpublish products in chunks of 100
            ChannelProduct::whereIn('item_number', $skus->pluck('sku')->toArray())
                ->where('channel', $reports['channel'])
                ->chunk(100, function ($chunkedSkus) use ($skus, $shop) {
                    foreach ($chunkedSkus as $sku) {
                        // Find the corresponding SKU in the original data
                        $originalSku = $skus->firstWhere('sku', $sku->item_number);

                        // Update ChannelProduct status
                        $sku->update([
                            'connection_status' => ChannelProductConnectedStatus::ERROR,
                            'error_response' => [$originalSku['error']]
                        ]);
                    }
                });

            // Update ChannelProduct statuses for remaining items
            ChannelProduct::where([
                'channel' => 20,
                'connection_status' => ChannelProductConnectedStatus::READY_TO_EXPORT
            ])->update(['connection_status' => ChannelProductConnectedStatus::CONNECTED]);

            ChannelProduct::where([
                'channel' => 20,
                'stock' => 0
            ])->update([
                'connection_status' => ChannelProductConnectedStatus::ERROR,
                'error_response' => ['Stock is not valid']
            ]);


                //     $skusToPublish = $channelProducts->pluck('item_number')->toArray();

                //     // app(Colizey::class)->publishProductsUsingSkus(
                //     //     json_encode($skusToPublish),
                //     //     $shop->password
                //     // );

                //     // Update the connection status to CONNECTED in bulk
                //     ChannelProduct::whereIn('id', $channelProducts->pluck('id')->toArray())
                //         ;
                // });


        }
        dd($shop,$reports);
        $source = request()->source;
        $analyses = AnalysisProduct::select('id', 'product_id')
            ->where('source', $source)
            ->whereDate('updated_at', '!=', now()->toDateString())
            ->whereNull('category_id')
            ->take(100000);


        $analyses->chunk(1000, function ($analysesChunk) use ($source) {
            DB::beginTransaction();

            try {
                $productIds = $analysesChunk->pluck('product_id')->toArray();
                $categories = [];

                if (in_array($source, [2, 3])) {
                    $categories = MarketplaceProducts::whereIn('id', $productIds)
                        ->pluck('category_id', 'id')
                        ->toArray();
                } else {
                    $categories = DB::table('drm_product_categories')
                        ->whereIn('product_id', $productIds)
                        ->pluck('id', 'product_id')
                        ->toArray();
                }

                foreach ($productIds as $productId) {
                    AnalysisProduct::where('product_id', $productId)
                        ->update(['category_id' => $categories[$productId]]);
                }

                DB::commit();
            } catch (Exception $e) {
                DB::rollBack();
                // Handle the exception (log, notify, etc.)
                // Optionally, you can throw the exception to stop the process or continue with the next chunk
            }
        });

        dd($analyses->count(),request()->source);
        // Retrieve cms_user_id and order_ids
        // $ordersByCmsUserId = NewOrder::select('cms_user_id', \DB::raw('GROUP_CONCAT(id) as order_ids'))
        //     ->where('invoice_number', '=', -1)
        //     ->orderBy('id','desc')
        //     ->groupBy('cms_user_id')
        //     ->get();



        // // Update invoice_numbers sequentially
        // foreach ($ordersByCmsUserId as $orders) {
        //     $orderIds = explode(',', $orders->order_ids);
        //     // Update invoice_numbers sequentially
        //     foreach ($orderIds as $index => $orderId) {
        //         NewOrder::where('id', $orderId)->update(['proforma_number' => $index + 1]);
        //     }
        // }
        dd("Hello");
        // $url = 'https://api.colizey.fr/merchant/orders';
        // $params = [
        //     'headers' => [
        //         'x-apikey' => '8f4526318fb54690a44010d328934f89'
        //     ],
        // ];

        // $client = new \GuzzleHttp\Client();

        // $response = $client->request('GET', $url, $params);

        // $data_json = $response->getBody()->getContents();

        // $orders = json_decode($data_json);

            $url = 'https://api.colizey.fr/merchant/orders';
            $params = [
                'headers' => [
                    'x-apikey' => '8f4526318fb54690a44010d328934f89'
                ],
            ];

            $query = [];

            $client = new \GuzzleHttp\Client();
            $offset = 0;
            $data = [];
            do {
                $query['offset'] = $offset;
                $query['limit'] = 25;
                $params['query'] = $query;

                $response = $client->request('GET', $url, $params);
                $data = json_decode($response->getBody()->getContents(), true);

                $all_orders = $data;
                $count = count($all_orders);
                dump("He");
                if ($all_orders) {
                    $orders[] = $all_orders;
                    $offset += count($all_orders);
                }

            } while ($offset < $count);

            cache()->put('orders',$orders);
             $orders = cache()->get('orders');

        $shop = Shop::find(4);

        dd($orders);
        collect($orders)->each(function($order) use($shop){
            // Assuming $order is the decoded JSON object
            if (isset($order->shippingAddress)) {
                $order->deliveryAddress = $order->shippingAddress; //update shipping address to deliveryAddress
                unset($order->shippingAddress);
            }
            $order->customerName = $order->deliveryAddress->firstName . ' ' . $order->deliveryAddress->lastName;
            $order->customerEmail = $this->_getEmail($order->deliveryAddress->email,$order->customerName);

            $currency = "EUR";
            $country = DB::table("all_country")->where("country_code",$order->deliveryAddress->countryCode)->first('country')->country;

            //  $exist_shop_id = DB::table('new_orders')->where('cms_user_id', $shop->user_id)->where('order_id_api', $order->id)->first();
            // if($exist_shop_id) continue;
            // ---------- customer insert -------------

            // list($total_sum, $currency) = explode(" ", $order->totalSum);

            // $country = $order->deliveryAddress->country ?? $order->billingAddress->country ?? $order->deliveryAddress->countryIsoCode ?? $order->billingAddress->countryIsoCode;

            $customer_info = $order_info = null;

            $customer_info = [
                "customer_full_name" => $order->customerName ?? $order->deliveryAddress->firstName . ' ' . $order->deliveryAddress->lastName,
                "company_name" => $order->deliveryAddress->company ?? $order->billingAddress->company,
                "currency" => $currency,
                'email' => $order->customerEmail,
                'address' => $order->deliveryAddress->additionalAddressInfo ?? $order->billingAddress->additionalAddressInfo,
                'country' => $country,
                'default_language' => $order->deliveryAddress->countryIsoCode ?? $order->billingAddress->countryIsoCode,
                'zip_code' => $order->deliveryAddress->postcode ?? $order->billingAddress->postcode,
                'state' => $order->deliveryAddress->state ?? $order->billingAddress->state,
                'insert_type' => 1,
                'phone' => $order->deliveryAddress->phoneNumber,
                'city' => $order->billingAddress->city,

                //shipping
                'street_shipping' => $order->deliveryAddress->street . ' ' . $order->deliveryAddress->houseNumber,
                'city_shipping' => $order->deliveryAddress->city,
                'state_shipping' => $order->deliveryAddress->state,
                'zipcode_shipping' => $order->deliveryAddress->postcode,
                'country_shipping' => $order->deliveryAddress->country ?? $order->billingAddress->countryIsoCode,

                //billing
                'street_billing' => $order->billingAddress->street . ' ' . $order->billingAddress->houseNumber,
                'city_billing' => $order->billingAddress->city,
                'state_billing' => $order->billingAddress->state,
                'zipcode_billing' => $order->billingAddress->postcode,
                'country_billing' => $order->billingAddress->country ?? $order->billingAddress->countryIsoCode,

                'user_id' => $shop->user_id,
                'source' => $shop->channel,
            ];

            $order_info['customer_info'] = $customer_info;

            // $customer_id = $this->insert_customer($customer_info);
            $customer_id = 11;

            $order_info['user_id'] = $shop->user_id;
            $order_info['drm_customer_id'] = $customer_id;
            $order_info['order_date'] = $order->date;
            $order_info['insert_type'] = 1;
            $order_info['total'] = $order->price;
            $order_info['shop_id'] = $shop->id;
            $order_info['order_id_api'] = $order->id;

            $order_info['sub_total'] = $order->price;
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = $order->paymentType->module;
            $order_info['currency'] = $currency;
            $order_info['shipping_cost'] = $order->shippingPrice;

            //customer info
            $order_info['customer_info'] = customerInfoJson($customer_info);
            $order->billingAddress->customer_full_name = $customer_info['customer_full_name'];
            $order->billingAddress->address_billing = $order->billingAddress->street.','.$order->billingAddress->street2.','.$order->billingAddress->street3;
            $order->billingAddress->zip_code = $order->billingAddress->postcode;
            $billingCountry = ($order->deliveryAddress->countryCode == $order->billingAddress->countryCode)? $customer_info['country'] : DB::table("all_country")->where("country_code",$order->billingAddress->countryCode)->first('country')->country;
            $order->billingAddress->country = $billingCountry;
            //billing
            $order_info['billing'] = billingInfoJson($customer_info);
            dd($order_info['billing'],billingInfoJson(collect($order->billingAddress)->toArray()));
            //shipping
            $order_info['shipping'] = shippingInfoJson($customer_info);

            $order_info['status'] = $order->status;
            $carts = [];

            collect($order->orderLines)->map(function($order){
                $order->name        = $order->sku;
                $order->model       = $order->productDescription;
                $order->price       = $order->itemPrice;
                $order->finalPrice  = round(($order->itemPrice * $order->quantity), 2);
                unset($order->itemPrice,$order->status,$order->productDescription);
                return $order;
            });
            foreach ((array)$order->orderLines as $item) {
                // dd($item);
                $cart_item = [];
                $cart_item['id'] = $item->id;
                $cart_item['product_name'] = preg_replace_array('/"/', [' '], $item->name);
                $cart_item['description'] = preg_replace_array('/"/', [' '], $item->model);
                $cart_item['qty'] = $item->quantity;
                $cart_item['rate'] = $item->price;
                $cart_item['unit'] = $item->quantityUnitName;
                $cart_item['tax'] = $item->tax ?? 0;
                $cart_item['item_number'] = $item->model ?? null;
                $cart_item['product_discount'] = $item->discount ?? 0;
                $cart_item['amount'] = $item->finalPrice;
                $carts[] = $cart_item;
            }
            $order_info['cart'] = json_encode($carts);
            dd($order_info,$order->orderLines,$order);
        });
        // dd($order_info);

    }
    private function _getEmail($email, $name){
        if (empty($email)) {
            //Generate email address
            $email_str = $name;
            $email_str = strtolower(preg_replace('/\s+/', '', $email_str));
            $email_str = preg_replace( '/[\W]/', '', $email_str);
            $email = $email_str . '@drmdtfake.com';
            $email = \App\Helper\Encoding::toUTF8($email);
        }
        return $email;
    }

    public function handle($user_id)
    {
        $analysisService = [
            Countdown::class,
            Rainforest::class,
            GoogleShopping::class,
        ];
        $category_ids = AnalysisCategory::where('user_id', $user_id)->get();
        $analysis = [];
        foreach ($analysisService as $service) {
            $analysisApi = new $service;
            $collection_id_column = $analysisApi->collectionColumnName();
            $column = $analysisApi->driver();
            $column_name = $column . "_collection_ids";
            $user_requests = CPAnalysisUserRequests::select($column_name)->where('user_id', $user_id)->first();
            $user_request_collections = unserialize($user_requests->$column_name);

            if(!empty($user_request_collections)){
                foreach ($user_request_collections as $collection) {
                    // dump($collection);
                    $analysis[$collection_id_column][] = $collection;
                    // $analysisApi->deleteCollection($collection);
                }
            }
        }
// dd("HEllo",$analysis);
        foreach ($category_ids as $category) {
            $category_product = [];
            $request_collections = [];
            $category_products = AnalysisProduct::select('id', 'ean')
                ->where('user_id', $user_id)
                ->where('archived', '!=', 1)
                ->where('category_id', $category->id)
                ->get()->map(function($cat) use($user_id){
                    return[
                        'id' => $cat->id,
                        'user_id' => $user_id,
                        'ean' => $cat->ean,
                        'item_number' => trim($cat->ean) . "-" . $user_id
                    ];
                });

            $collection_interval_data = [];
            if ($category->type == 'oneTime') {
                $collection_interval_data = [
                    "schedule_type" => "manual",
                ];
            } else if ($category->type == 'hours') {
                $collection_interval_data = [
                    "schedule_type" => "daily",
                    "schedule_hours" => $category->duration,
                ];
            } else if ($category->type == 'minutes') {
                $collection_interval_data = [
                    "schedule_type" => "minutes",
                    "schedule_hours" => $category->duration,
                ];
            }

            $collection_body = [
                "name" => $category->name,
                "enabled" => True,
                "priority" => "normal",
                "notification_as_csv" => True,
                "notification_as_json" => True,
                "include_html" => 'False'
            ];

            $collectionBody = array_merge($collection_body, $collection_interval_data);

            if ($category_products->count()) {
                $productChunks = sizeof($category_products) > 15000 ? array_chunk($category_products, 1000) : [$category_products];
dd($productChunks,$collectionBody,$category,$analysisApi);
                foreach ($productChunks as $apiProductChunk) {
                    $collection_id = $analysisApi->createCollection($collectionBody);
                    $request_collections[] = $collection_id;

                    $analysisApi->addProducts($apiProductChunk, $collection_id);

                    if (sizeof($apiProductChunk) > 1000) {
                        $productChunks2 = array_chunk($apiProductChunk, 1000);
                        foreach ($productChunks2 as $apiProducts) {
                            $analysisApi->addProducts($apiProducts, $collection_id);
                        }
                    }
                }

                $default = sizeof($category_products) <= 15000;
                $cp_request_model = CPAnalysisRequest::create([
                    'user_id' => $user_id,
                    $collection_id_column => $collection_id,
                    'default' => $default ? 0 : 1,
                ]);

                AnalysisProduct::where('user_id', $user_id)->where('category_id', $category->id)->update([
                    $column . '_id' => $cp_request_model->id,
                ]);

                CPAnalysisUserRequests::updateOrCreate(
                    [
                        'user_id' => $user_id
                    ],
                    [
                        $column . '_collection_ids' => serialize($request_collections)
                    ]
                );
            }


// dd($collection_body,$collection_interval_data);
            $collectionBody =  array_merge($collection_body, $collection_interval_data);

            if ($category_products->count()) {
                if (sizeof($category_product) > 15000) {
                    $productChunks1 = array_chunk($category_product, 15000);

                    foreach($productChunks1 as $apiProductChunk){
                        $collection_id = $analysisApi->createCollection($collectionBody);
                        $request_collections[] = $collection_id;
                        if(sizeof($apiProductChunk) > 1000){
                            $productChunks2 = array_chunk($apiProductChunk, 1000);
                            foreach($productChunks2 as $apiProducts){
                                $analysisApi->addProducts($apiProducts, $collection_id);
                            }
                        }
                        else{
                            $analysisApi->addProducts($apiProductChunk, $collection_id);
                        }
                    }
                } else {
                    $collection_id = $analysisApi->createCollection($collectionBody);

                    $cp_request_model = CPAnalysisRequest::create([
                        'user_id' => $user_id,
                        $collection_id_column => $collection_id,
                        'default' => 0,
                    ]);

                    AnalysisProduct::where('user_id', $user_id)->where('category_id', $category->id)->update([
                        $column . '_id' => $cp_request_model->id,
                    ]);

                    $request_collections[] = $collection_id;

                    if (sizeof($category_product) > 1000) {
                        $productChunks = array_chunk($category_product, 1000);
                            foreach($productChunks as $apiProducts){
                                $analysisApi->addProducts($apiProducts, $collection_id);
                            }
                    } else {
                        $analysisApi->addProducts($category_product, $collection_id);
                    }
                }
                CPAnalysisUserRequests::updateOrCreate(
                    [
                        'user_id' => $user_id
                    ],
                    [
                        $column . '_collection_ids' => serialize($request_collections)
                    ]
                );
            }
        }
    }
}
