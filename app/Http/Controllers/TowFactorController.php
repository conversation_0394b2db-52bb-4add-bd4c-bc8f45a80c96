<?php

namespace App\Http\Controllers;

use CRUDBooster;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use DigiStore24\DigiStoreApi;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;

class TowFactorController extends Controller
{

    public function verifyToken(){

        $payload = [];
        if(isset($_GET['relavida-login']) && isset($_GET['auth-token']) && $login_token = $_GET['auth-token'])
        {
            $login_token = base64_decode($login_token);
            $token = \DRM::stringEncryption($login_token, true);
            $payload = @json_decode($token, true) ?? [];
        }

        $data['email'] = Session::get('email');
        $data['pass'] = Session::get('password');

        if(empty($data['email']) && $payload['email'])
        {
            $data['email'] = $payload['email'];
            Session::put('email', $data['email']);
        }

        return view('auth.twofactor')->with($data);
    }

    public function store(Request $request)
    {

        $request->validate([
            'two_factor_code' => 'integer|required',
        ]);

        if(CRUDBooster::myId()){
            // $user = auth()->user();
            $user = CRUDBooster::me();
            if($user->two_factor_code != $_POST['two_factor_code']) $user = null;
        }else $user = DB::table(config('crudbooster.USER_TABLE'))->where("two_factor_code", $_POST['two_factor_code'])->first();


        if(empty($user)){
            return redirect()->back()->withErrors(['two_factor_code' => 'The two factor code you have entered does not match']);
        }

        // $expire_date = $user->two_factor_expires_at;
        // $expire_time = strtotime($expire_date);
        // $current_date = date('d-m-Y H:i:s');
        // $current_time = strtotime($current_date);
        // $time = $expire_time - $current_time;

        $expirationTime = Carbon::parse($user->two_factor_expires_at);
        //Two factor code
        if(Carbon::now()->isBefore($expirationTime)){
            // Login user - Verify 2FA
            authUserLogin($user->id);
            Session::put('2fa', $user->id);

            $this->setLogin($user);
            DB::table('cms_users')->where("id", $user->id)->update([
                'two_factor_code' => null
            ]);

            return redirect(CRUDBooster::adminPath());
        }else {
            return redirect()->back()->withErrors(['two_factor_code' => 'You have entered an expired two factor code!']);
        }
    }



    public function setLogin($users){
        // return (new \crocodicstudio\crudbooster\controllers\AdminController)->loginUser($users);
        return app(AdminCmsUsersController::class)->userlogin($users);
    }


    public function forgetPassword($id){
        $forget_password_token = $id;
        return view('auth.password-change',compact('forget_password_token'));
    }

    public function passwordChange(Request $request){

        // \Hash::make($rand_string);
        $password = $_POST['password'];
        $hashPassword = \Hash::make($password);
        // dd($password);

        $user = DB::table(config('crudbooster.USER_TABLE'))->where("forget_password_token", $_POST['forget_password_token'])->first();

        if($user!=null)
        {

            // Login user
            authUserLogin($user->id);

            $this->setLogin($user);
            DB::table(config('crudbooster.USER_TABLE'))->where("forget_password_token", $_POST['forget_password_token'])->update([
                'forget_password_token'=>null,
                'password' => $hashPassword,
                ]);

                // try {
                //     $cmsuserInfo = $user;

                //     $apiRequest['id'] = $cmsuserInfo->id;
                //     $apiRequest['db_host'] = '************';
                //     $apiRequest['db_user'] = 'forge';
                //     $apiRequest['db_password'] = 'EVlesfB2hRbs5VE3657S';
                //     $apiRequest['db_port'] = '3306';
                //     $apiRequest['name'] = $cmsuserInfo->name;
                //     $apiRequest['email'] = $cmsuserInfo->email;
                //     $apiRequest['password'] = $hashPassword;

                //     $url = "https://drm.network/api/forget-password";
                //     $client = new Client();

                //     $response  = $client->post($url,  ['form_params'=>$apiRequest]);
                //     $result = json_decode($response->getBody()->getContents(), TRUE);

                //   } catch (\Exception $e) {}
            return redirect(drm_login_url($user));
        }else{

            return redirect()->back()->withErrors(['two_factor_code' => 'The two factor code you have entered does not match']);

        }

    }

    public function twofactorlogin(Request $request){
        DB::table(config('crudbooster.USER_TABLE'))->where("id", $_POST['user_id'])->update([
            'two_fa_status'=>$_POST['status'],
            ]);

            return CRUDBooster::redirectBack("Two Factor Login is Update!", "success");

    }

}
