<?php

namespace App\Http\Controllers;

use AppStore;
use App\CustomerTag;
// use App\N ewCustomerTag;
use App\NewCustomer;
use App\DropfunnelCustomerTag;
use App\Notifications\DRMNotification;
use App\Notifications\DRMTelegramNotification;
use App\Notifications\NewOrderInsertNotification;
use App\User;
use App\VodTag;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class UscreenSyncController extends Controller
{
    //receive customers from uscreen
    public function syncCustomer()
    {
        try {
            $data = json_decode(request()->getContent(), true);
            $email = $data['email'];
            if(empty($email)) return;

            $customer_info = [
                "customer_full_name" => $data['name'],
                "company_name" => '',
                "currency" => 'EUR',
                'email' => $email,
                'address' => '',
                'country' => '',
                'default_language' => 'DE',
                'zip_code' => '',
                'state' => '',
                'insert_type' => 2, //int

                //shipping
                'street_shipping' => '',
                'city_shipping' => '',
                'state_shipping' => '',
                'zipcode_shipping' => '',
                'country_shipping' => '',

                //billing
                'street_billing' => '',
                'city_billing' => '',
                'state_billing' => '',
                'zipcode_billing' => '',
                'country_billing' => '',
                'user_id' => 2454,
                'status' => 1,
                'source' => 57,
            ];

            //Add customer to dropcampus account
            $customer_id = app('App\Http\Controllers\AdminDrmAllCustomersController')->add_customer($customer_info);
            $message_title = 'A customer is synced from userscreen!' . PHP_EOL . 'Email: ' . $data['email'];
            User::find(2454)->notify(new DRMNotification($message_title, 'CustomerSyncUserscreen'));

        } catch (\Exception $e) {}
    }

    //receive ransactions from uscreen
    public function syncTransaction()
    {
        try {

            $data = json_decode(request()->getContent(), true);

            // $validator = Validator::make($data, [
            //     'customer_email' => 'required',
            //     'total' => 'required',
            // ]);
            if (is_null($data)) {
                throw new \Exception("Invalid VOD data!");
            }

            $email = $data['customer_email'];
            if(empty($email)) return;

            $customer_info = [
                "full_name" => $data['customer_name'],
                "currency" => 'EUR',
                'default_language' => 'DE',
                'insert_type' => 2,
            ];

            //DropCampusCustomer
            $customer = NewCustomer::updateOrCreate([
                'email' => $email,
                'user_id' => 2454,
            ],
                $customer_info
            );

            $order_info['user_id'] = 2454;
            $total = isset($data['total']) ? (float)$data['total'] : 0;
            $product_price = $total;
            $sub_total = isset($data['amount']) ? (float)$data['amount'] : 0;
            $discount = isset($data['discount']) ? (float)$data['discount'] : 0;
            $total = ($discount > 0) ? ($total - $discount) : $total;

            if (isset($customer->id)) {
                $order_info['drm_customer_id'] = $customer->id;
            }

            $order_info['order_date'] = date("Y-m-d h:i:sa");
            $order_info['insert_type'] = 2;
            $order_info['total'] = $total;
            $order_info['shop_id'] = 108;
            $order_info['order_id_api'] = $data['id'];

            $order_info['sub_total'] = $sub_total;
            $order_info['discount'] = $discount;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = 'Stripe Card';
            $order_info['currency'] = 'EUR';
            $order_info['customer_info'] = vodCustomerInfo($customer);
            $order_info['billing'] = vodCustomerAddress($customer);
            $order_info['shipping'] = vodCustomerAddress($customer);

            $order_info['status'] = drmOrderLabelByGroupId('Succeeded');

            $cart_item = [[
                "id" => 1,
                "product_name" => trim($data['title']),
                'qty' => 1,
                'rate' => $product_price,
                'unit' => 1,
                'offer_id' => $data['offer_id'],
                'tax' => 0,
                'product_discount' => $discount,
                'amount' => $product_price
            ]];

            $order_info['cart'] = json_encode($cart_item);

            app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
            $message_title = 'A order paymnet received via userscreen!' . PHP_EOL . 'Customer Email :' . $email . PHP_EOL . 'Product Name:' . $data['title'] . PHP_EOL . 'Amount: ' . $data['total'];
            User::find(2454)->notify(new DRMNotification($message_title, 'USCREEN_TRANSECTION'));

        } catch (\Exception $e) {
            Log::channel('uscreen')->info($e->getMessage());
        }
    }

    //receive due transactions from uscreen
    public function syncTransactionDue()
    {
        try {

            $data = json_decode(request()->getContent(), true);

            if (is_null($data)) {
                throw new \Exception("Invalid Due Transection VOD data!");
            }

            $email = $data['email'];
            if(empty($email)) return;

            $customer_info = [
                "full_name" => $data['name'],
                "currency" => 'EUR',
                'default_language' => 'DE',
                'insert_type' => 2,
            ];

            //DropCampusCustomer
            $customer = NewCustomer::updateOrCreate([
                'email' => $email,
                'user_id' => 2454,
            ],
                $customer_info
            );

            $order_info['user_id'] = 2454;
            if (isset($customer->id)) {
                $order_info['drm_customer_id'] = $customer->id;
            }

            $order_info['order_date'] = date("Y-m-d h:i:sa");
            $order_info['insert_type'] = 2;
            $order_info['total'] = $data['final_price'];
            $order_info['shop_id'] = 108;
            $order_info['order_id_api'] = $data['invoice_id'];

            $order_info['sub_total'] = $data['final_price'];
            $order_info['discount'] = 0;
            $order_info['discount_type'] = "fixed";
            $order_info['adjustment'] = 0;
            $order_info['payment_type'] = 'Stripe Card';
            $order_info['currency'] = 'EUR';
            $order_info['customer_info'] = vodCustomerInfo($customer);
            $order_info['billing'] = vodCustomerAddress($customer);
            $order_info['shipping'] = vodCustomerAddress($customer);

            $order_info['status'] = 'inkasso';
            $order_info['remainder_date'] = now();

            $cart_item = [[
                "id" => 1,
                "product_name" => $data['title'],
                'qty' => 1,
                'rate' => $data['final_price'],
                'unit' => 1,
                'offer_id' => null,
                'tax' => 0,
                'product_discount' => 0,
                'amount' => $data['final_price']
            ]];

            $order_info['cart'] = json_encode($cart_item);

            app('App\Http\Controllers\AdminDrmAllOrdersController')->add_order($order_info);
            $message_title = 'A order paymnet userscreen failed!' . PHP_EOL . 'Customer Email :' . $data['email'] . PHP_EOL . 'Product Name:' . $data['title'] . PHP_EOL . 'Amount: ' . $data['final_price'];
            User::find(2454)->notify(new DRMNotification($message_title, 'USCREEN_TRANSECTION_DUE'));

        } catch (\Exception $e) {
            Log::channel('uscreen')->info($e->getMessage());
            User::find(71)->notify(new DRMNotification('Transection due error ' . $e->getMessage() . ' Line:' . $e->getLine(), 'USCREEN_TRANSECTION_DUE', '#'));
        }
    }

    //receive customers from uscreen
    public function uscreenWebhookOthers()
    {
        try {

            $data = json_decode(request()->getContent(), true);
            Log::channel('uscreen')->info($data);

        } catch (\Exception $e) {
        }
    }

    //receive watched video from uscreen
    public function uscreenVideoWatched()
    {
        try {
            $data = json_decode(request()->getContent(), true);

            if(empty($data)) return;

            //Insert to DropCampus
            $dropCampus_customer = NewCustomer::where(['email' => $data['email'], 'user_id' => 2454])->first();
            if ($dropCampus_customer) {
                $title = $data['title'];

                //insert tag
                if($title){
                    try{
                        DropfunnelCustomerTag::insertTag($title, 2454, $dropCampus_customer->id, 3);
                    }catch(\Exception $ev){}
                }

                $customer_url = \CRUDBooster::adminPath('drm_all_customers/detail/' . $dropCampus_customer->id);
                $message_title = $dropCampus_customer->full_name . ' watched ' . $title;
                User::find(2454)->notify(new DRMNotification($message_title, 'USCREEN_WATCHED_VIDEO', $customer_url));
            }

        } catch (\Exception $e) {
            User::find(71)->notify(new DRMNotification('watched video error ' . $e->getMessage() . ' Line:' . $e->getLine(), 'USCREEN_WATCHED_VIDEO', '#'));
        }
    }

    //webhook Added to Favorites uscreen
    public function uscreenAddedToFavorites()
    {
        try {
            $data = json_decode(request()->getContent(), true);
            if(empty($data)) return;

            //Insert to DropCampus
            $dropCampus_customer = NewCustomer::where(['email' => $data['email'], 'user_id' => 2454])->first();
            if ($dropCampus_customer) {
                $title = $data['title'];
                //insert tag
                if($title){
                    $title = $title.' + Favourite';
                    try{
                        DropfunnelCustomerTag::insertTag($title, 2454, $dropCampus_customer->id, 11);
                    }catch(\Exception $ev){}
                }
            }

        } catch (\Exception $e) {
            User::find(71)->notify(new DRMNotification('Favorites uscreen tag added error ' . $e->getMessage() . ' Line:' . $e->getLine(), 'USCREEN_WATCHED_VIDEO', '#'));
        }
    }

    public function botMessageTest()
    {
        dd(User::find(71)->notify(new NewOrderInsertNotification(71)));
    }
}
