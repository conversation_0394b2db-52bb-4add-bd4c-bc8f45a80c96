<?php namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\VideoWatch;
use App\Video;
use App\Traits\ProjectShare;

class VideoController extends Controller {
	use ProjectShare;

	public function storeDuration(Request $request){
		try{
			$request->validate([
				'user_id' => 'required',
				'video_id' => 'required',
				'session_id' => 'required',
				'duration' => 'nullable',
				'watch_video_lang' => 'nullable',
			]);

		  // $this->tagInsertToCustomer($request->user_id,'Help Video',9);
            $check = [
                'user_id' => $request->user_id,
                'video_id' => $request->video_id,
                'session_id' => $request->session_id
            ];

			$data = [
				'duration' => $request->duration,
				'watch_video_lang' => $request->watch_video_lang,
				'is_complete' => $request->completed,
			];

			if( VideoWatch::where($check)->where('is_complete', 1)->count() > 0){
				throw new \Exception('Video already played!');
			}

			$data = array_filter($data);
			VideoWatch::updateOrCreate($check, $data);

			return response()->json([
				'success' => true,
				'message' => 'Success'
			]);

		}catch(\Exception $e){
			return response()->json([
				'success' => false,
				'message' => $e->getMessage()
			]);
		}
	}
	public function helpVideoTag(Request $request){
		try{
			$request->validate([
				'user_id' => 'required',
			]);

			if (!empty($request->user_id) && $request->video_id) {
         $video_name = Video::find($request->video_id);
 				 $this->tagInsertToCustomer($request->user_id,$video_name->name,9);
 		  }

			return response()->json([
				'success' => true,
				'message' => 'Success'
			]);

		}catch(\Exception $e){
			return response()->json([
				'success' => false,
				'message' => $e->getMessage()
			]);
		}
	}

}
