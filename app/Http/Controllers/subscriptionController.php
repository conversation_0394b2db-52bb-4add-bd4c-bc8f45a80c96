<?php

namespace App\Http\Controllers;

use App\Bank;
use App\Models\Marketplace\CreditNote;
use App\Models\Marketplace\SupplierUploadInvoice;
use DB;
use PDF;
use App\User;
use ServiceKey;
use App\AgbLogs;
use App\BillingDetail;
use CRUDBooster;
use App\NewOrder;
use Carbon\Carbon;
use App\subscriptions;
use Illuminate\Support\Str;
use Illuminate\Http\Request;
use App\Notifications\DRMNotification;
use Illuminate\Support\Facades\Storage;
use crocodicstudio\crudbooster\helpers\CB;

class subscriptionController extends \crocodicstudio\crudbooster\controllers\CBController
{

    public function purchase_invoice(){
        $data['page_title'] = "Customer Purchase Invoice";

        $orders = NewOrder::where('cms_client',CRUDBooster::myId())
        ->where('client_locked', '<>', 1);

        if(in_array(CRUDBooster::myParentId(), [52]))
        {
            $orders->where('id', '>', 26566);
        }

        if(in_array(CRUDBooster::myParentId(), [1813]))
        {
            $orders->where('id', '>', 26814);
        }


        $data['orders'] = $orders->whereIn('cms_user_id', [98, 2455, 2454, 2439])->whereIn('insert_type', [3, 4, 8])->where('cms_client', CRUDBooster::myParentId())->orderBy('id','desc')->get();

        if(CRUDBooster::myId()){
            return view('subscription.purchase-invoice-view')->with($data);
        }
        else{
            CRUDBooster::redirect(CRUDBooster::adminPath('login'),"Login Please!","info");
        }
    }

    public function purchase_invoice_view($order_id){

        $order = DB::table('new_orders')
        ->where('client_locked', '<>', 1);

        if(in_array(CRUDBooster::myId(), [52]))
        {
            $order->where('id', '>', 26566);
        }

        $order = $order->where('id', '=', $order_id)->whereIn('cms_user_id', [98, 2455, 2454, 2439])->whereIn('insert_type', [3, 4, 8])->where('cms_client', CRUDBooster::myParentId())->select('credit_number', 'id')->first();
        if(empty($order)) {
            return CRUDBooster::redirectBack('Access denied!');
        }

        $is_credit = $order->credit_number != 0;
        if($is_credit){
            return app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_credit_note_pdf($order_id);
        }
        return app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_invoice_pdf($order_id);
    }

    public function purchase_invoice_download($order_id){
        $order = DB::table('new_orders')
        ->where('client_locked', '<>', 1);

        $order = $order->where('id', '=', $order_id)->whereIn('cms_user_id', [98, 2455, 2454, 2439])->whereIn('insert_type', [3, 4, 8, 9])->where('cms_client', CRUDBooster::myParentId())->select('credit_number', 'id')->first();
        if(empty($order)) {
            return CRUDBooster::redirectBack('Access denied!');
        }

        $is_credit = $order->credit_number != 0;
        if($is_credit){
            return app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_credit_note_pdf($order_id, false, true);
        }
        return app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_invoice_pdf($order_id, false, true);

        // $return_invoice = app('App\Http\Controllers\AdminDrmAllOrdersController')->getDeliveryNotes($order_id);

    }


    public function viewPlan(){

        $data['import']=DB::table('purchase_import_plans')
        	 		->join('import_plans','import_plans.id','=','purchase_import_plans.import_plan_id')
        	 		->where('purchase_import_plans.cms_user_id',CRUDBooster::myId())
                    ->select('purchase_import_plans.*','import_plans.amount','import_plans.plan','import_plans.interval')
        	 		->first();
        $data['page_title'] = __('billing_plans.PAGE_TITLE');

        $data['details']=DB::table('purchase_apps')
            ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
            // ->where('purchase_apps.type','!=','Free Trail')
            ->where('purchase_apps.cms_user_id',CRUDBooster::myId())
            ->where(function($query) {
                    $query->where('purchase_apps.subscription_date_end','>', date('Y-m-d'))
                    ->orwhere('purchase_apps.subscription_life_time',1);
            })
            ->select('app_stores.menu_name','app_stores.download_file','app_stores.icon','purchase_apps.*')
            ->get();

        if(CRUDBooster::myId()){

            if(CRUDBooster::isSupplier()) {
                $user = User::find(CRUDBooster::myId());

                $creditnotes = CreditNote::where('supplier_id',$user->id)->orderBy('month','desc')->get();
                return view('subscription.view-mp-plan', compact('user','creditnotes'))->with($data);
            } else {
                $user = User::find(CRUDBooster::myId());
                $data['user'] = $user;
                return view('subscription.view-plan', compact('user'))->with($data);
            }
        }
        else{
            CRUDBooster::redirect(CRUDBooster::adminPath('login'),"Login Please!","info");
        }

    }

    public function stripePurchaseView($order_id){

        $pdf_path = app('App\Http\Controllers\AdminDrmAllOrdersController')->generate_invoice_pdf($order_id);
        return redirect($pdf_path);

    }

    public function stripePurchaseInvoiceDownload($order_id){
        $pdf_path = app('App\Http\Controllers\AdminDrmAllOrdersController')->getDeliveryNotes($order_id);
        return redirect($pdf_path);

    }

    public function update(Request $request){
        $planID = $request->plan;
        $subscription = DB::table('subscriptions')->where('user_id', CRUDBooster::myId())->where('stripe_id',$planID)->get();
        $updateRow = $subscription[0]->stripe_id;
        \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
        $planValue = \Stripe\Subscription::update(
            $updateRow,
            ['metadata' => ['DRM Plan' => '2']]
        );
        return CRUDBooster::redirectBack("Your Subscription Plan is Updated!", "success");
    }


    //super admin
    public function subscriptionOrder(){
        $subList = DB::table('subscriptions')
                    ->select('stripe_id')
                    ->orderBy('id','desc');
        $total_subscription = DB::table('subscriptions')->count();
        foreach($subList->get() as $stripeID){
            $stripeIDs = $stripeID->stripe_id;
            \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
            $subscription = \Stripe\Subscription::retrieve(
                $stripeIDs
            );
            $invoiceID = $subscription->latest_invoice;
            $invoice = \Stripe\Invoice::retrieve($invoiceID);

            $totalInvoice[] = $invoice;
            $totalTurnover[] = $invoice->amount_paid;
        }

        $turnOver = null;
        foreach($totalTurnover as $TotalTurnOver)
        {
            $turnOver = $turnOver + $TotalTurnOver / 100;
        }
        $data = [
            'invoice'  => $totalInvoice,
            'subList'   => $subList->paginate(10),
            'total_subscription'    => $total_subscription,
            'totalTurnover' => $turnOver,
        ];
        $data['page_title'] = "Subscription Orders Information";
        return view('subscription.order-list')->with($data);
    }

    public function getInvoice(Request $request){
        \Stripe\Stripe::setApiKey(\DRM::stripeSecretKey('stripe_key_2455'));
        $invoice = \Stripe\Invoice::retrieve($request->id);
        $planDetails = \Stripe\Subscription::retrieve(
            $invoice->subscription
        );
        $customerID = $invoice->customer;
        $customerdetails = User::where('stripe_id',$customerID)->get();
        $planValue = $invoice->lines->data[0]->plan;
        $couponValue = $invoice->discount->coupon;
        $description = $invoice->lines->data[0];

        $data = [
            'totalInvoice'  => $invoice,
            'customerDetails'   => $customerdetails,
            'planValue' => $planValue,
            'couponValue'   => $couponValue,
            'description'   => $description,
            'planDetails'   => $planDetails,
        ];

        $data['page_title'] = "Customer Invoice";

        $pdf = PDF::loadView('subscription.invoice', $data);
        $fileName = "invoice-".$invoice->number.".pdf";
        return $pdf->download($fileName);

    }

    public function billingAddress(Request $request){

        $skipVatCheck = isset($request->country_id) && intval($request->country_id) === 83;
        $request->validate([
            'country_id' => 'required',
            'city' => 'required',
            'zip' => 'required',
            'address' => 'required',
            'company_name' => 'nullable',
            'email' => 'required',
            'phone' => 'nullable',
            'vat_id' => ['nullable', function ($attribute, $value, $fail) use ($skipVatCheck) {
                $vat_checker = \DRM::checkTaxNumber($value);
                if(!$skipVatCheck && !$vat_checker['success']){
                    $fail('The '.$attribute.' is invalid. '.$vat_checker['message']);
                }
            }],
        ]);


        // Validate Email and phone on API
        try {
            app(\App\Services\UiValidation\UiValidation::class)->validateEmail($request->email);                
        } catch (\Exception $e) {
            return response()->json([
                'errors' => [
                    'email' => [$e->getMessage()],
                ],
            ], 400);
        }

        try {
            if(isset($request->phone) && !empty($request->phone))
            {
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($request->phone);
            }                 
        } catch (\Exception $e) {
            return response()->json([
                'errors' => [
                    'phone' => [$e->getMessage()],
                ],
            ], 400);
        }


        $user = User::with('billing_detail')->find(CRUDBooster::myId());

        // if(CRUDBooster::isSupplier()) {
        //     if(!empty($request->file('pdf_link'))) {
        //         $file = $request->file('pdf_link');
        //         $ext = $file->getClientOriginalExtension();
        //         //Move file to storage
        //         $filename = md5(Str::random(5)).'.'.'pdf';
        //         $filename = str_replace(" ", "_",  $filename);
        //         // Upload csv to cloud
        //         $filePath = 'marketplace-collections/' . CB::myId().'/'.$filename;
        //         Storage::disk('spaces')->put($filePath, file_get_contents($file), 'public');
        //         if (Storage::disk('spaces')->exists($filePath)) {
        //             $pdf_link = Storage::disk('spaces')->url($filePath);
        //         }
        //     }
        // }

        try{
            if(empty($user->contact_number)){
                User::where('id',CRUDBooster::myId())->update([
                    'contact_number' => $request->phone
                ]);
            }
        }catch(\Exception $e){

        }



        $payload = $request->only(['country_id', 'city', 'zip', 'first_name', 'last_name', 'address', 'company_name', 'email', 'phone', 'vat_id']);

        if($user){
            $msg = 'update';
            $billing = null;
            if($user->billing_detail){

                // Agb Logfile
                // if(isLocal()){

                    if(\CRUDBooster::isSupplier()) {
                        $user_billing_old_info['first_name'] = $user->billing_detail->first_name;
                        $user_billing_old_info['last_name'] = $user->billing_detail->last_name;
                    }

                    $user_billing_old_info['company_name'] = $user->billing_detail->company_name;
                    $user_billing_old_info['country_id'] = $user->billing_detail->country_id;
                    $user_billing_old_info['city'] = $user->billing_detail->city;
                    $user_billing_old_info['zip'] = $user->billing_detail->zip;
                    $user_billing_old_info['address'] = $user->billing_detail->address;
                    $user_billing_old_info['email'] = $user->billing_detail->email;
                    $user_billing_old_info['phone'] = $user->billing_detail->phone;
                    $user_billing_old_info['vat_id'] = $user->billing_detail->vat_id;

                    $user_billing_new_info['company_name'] = $request->company_name;
                    $user_billing_new_info['country_id'] = $request->country_id;
                    $user_billing_new_info['city'] = $request->city;
                    $user_billing_new_info['zip'] = $request->zip;
                    $user_billing_new_info['address'] = $request->address;
                    $user_billing_new_info['email'] = $request->email;
                    $user_billing_new_info['phone'] = $request->phone;
                    $user_billing_new_info['vat_id'] = $request->vat_id;

                    $user_billing_info_update = array_diff($user_billing_old_info,$user_billing_new_info);

                    if(!empty($user_billing_info_update)){
                        $user_billing_info_update_agb_logs = [];
                        $user_billing_info_update_agb_logs['old_billing_info'] = $user_billing_old_info;
                        $user_billing_info_update_agb_logs['new_billing_info'] = $user_billing_new_info;
                        // dd($user->billing_detail->user_id, $user_billing_info_update_agb_logs);

                        $AgbLog = new AgbLogs;
                        $AgbLog->user_id = $user->billing_detail->user_id;

                        $user_billing_name = DB::table('cms_users')->where('id',$user->billing_detail->user_id)->first();

                        $AgbLog->message = $user_billing_name->name.' Updated Billing Information';

                        $AgbLog->change_log = json_encode($user_billing_info_update_agb_logs);
                        $AgbLog->ip_address = getIpAddress();
                        $AgbLog->save();
                    }

                // }
                // End Agb Logfile

                $request = $request->except(['_token']);
                //marketplace needs
                if(\CRUDBooster::isSupplier()) {
                    $payload['last_updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
                }
                if(isset($request['pdf_link']) && \CRUDBooster::isSupplier()) {
                    if(is_array($request['pdf_link'])) {
                        try {
                            $payload['pdf_link'] = array_merge($user->billing_detail->pdf_link, $request['pdf_link']);
                        } catch(\Exception $e) { }
                    }
                }

                $billing = $user->billing_detail()->update( $payload );

                // if($user_billing_new_info['vat_id'] != $user->billing_detail || $user_billing_new_info['country_id'] != $user->country_id)
                // {
                    usleep(3);
                    app(\App\Services\Payment\RemoveSubscriptionTax::class)->removeTaxByUserId($user->id);
                // }

            }else{

                $request = $request->except(['_token']);
                //marketplace needs
                if(\CRUDBooster::isSupplier()) {
                    $payload['last_updated_at'] = Carbon::now()->format('Y-m-d H:i:s');
                }

                $billing = $user->billing_detail()->create( $payload );
                $msg = 'create';
            }

            if($billing){
                if(\CRUDBooster::isSupplier()) {
                    $message_title = "Supplier Billing Details Updated: {$user->name} has updated billing details. <b>Supplier ID: {$user->id}</b>. Follow the below Link";
                    $url = url('/admin/users?q='.$user->id);
                    User::find(2455)->notify( new DRMNotification($message_title, "BILLING DETAILS UPDATED", $url) );
                }

                // Do accounting
                app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

                // if(isLocal() || in_array($user->id, [212, 2592])){
                    if(empty($user->billing_detail->billing_emails)){
                        $user->billing_detail()->update([
                            'billing_emails' => json_encode([$user->email])
                        ]);
                    }
                // }


                return response()->json([
                    'success' => true,
                    'message' => 'Billing address '.$msg.' success!',
                    'id'      => $user->billing_detail->id,
                    'vat_number' => $user->billing_detail->vat_id
                ]);
            }
        }

        return response()->json([
            'success' => false,
            'message' => 'Something went wrong!'
        ]);
    }

    public function userBillingAddress(){
        $user = User::find(CRUDBooster::myId());
        if($user){
            if(CRUDBooster::isSupplier()) {
                $update_at = $user->billing_detail->last_updated_at;
                $update_billing = true;
                $next_date = null;
                if($update_at) {
                    $next_date = Carbon::parse($update_at)->addDays(120)->format('d.m.Y');
                    $update_billing = ( (Carbon::parse($update_at)->addDays(120)) > Carbon::now() ) ? false : true;
                }

                return response()->json([
                    'success' => true,
                    'html' => view('subscription.mp-billing', compact('user'))->render(),
                    'update_billing' => $update_billing,
                    'next_date' => $next_date,
                    'last_updated_at' => Carbon::parse($update_at)->format('d.m.Y H:i'),
                    'pdf_link' => $user->billing_detail->pdf_link,
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'html' => view('subscription.billing', compact('user'))->render()
                ]);
            }

        }else{
            return response()->json([
                'success' => false,
                'message' => 'Invalid User'
            ]);
        }
    }

    public function insertCard(Request $request){
        $user = User::find(CRUDBooster::myId());
        if($user){
            if ($request->card_numbers) {
                $request->validate([
                    'card_names.*' => 'required|min:3',
                    'card_numbers.*' => 'required|min:16',
                    'card_cvcs.*' => 'required|min:3',
                    'card_months.*' => 'required|min:2',
                    'card_years.*' => 'required|min:4',
                ],
                [
                    'card_numbers.*.required' => 'Card number can not be empty!',
                    'card_numbers.*.min' => 'Invalid card number!',

                    'card_names.*' => 'Invalid name!',
                    'card_cvcs.*' => 'Invalid cvc number!',
                    'card_months.*' => 'Invalid month!',
                    'card_years.*' => 'Invalid year!',
                ]);
                foreach($request->card_numbers as $i => $number){
                    $user->cards()->create([
                        'number' => $number,
                        'name'  => $request->card_names[$i],
                        'cvc'   => $request->card_cvcs[$i],
                        'mm'    => $request->card_months[$i],
                        'yyyy'  => $request->card_years[$i]
                    ]);
                }
            }

            if ($request->card_numbers_old) {
                $request->validate([
                    'card_names_old.*' => 'required|min:3',
                    'card_numbers_old.*' => 'required|min:16',
                    'card_cvcs_old.*' => 'required|min:3',
                    'card_months_old.*' => 'required|min:2',
                    'card_years_old.*' => 'required|min:4',
                ],
                [
                    'card_numbers_old.*.required' => 'Card number can not be empty!',
                    'card_numbers_old.*.min' => 'Invalid card number!',

                    'card_names_old.*' => 'Invalid name!',
                    'card_cvcs_old.*' => 'Invalid cvc number!',
                    'card_months_old.*' => 'Invalid month!',
                    'card_years_old.*' => 'Invalid year!',
                ]);
                foreach($request->card_numbers_old as $k => $o_number){
                    $user->cards()->where('id', $k)->update([
                        'number' => $o_number ,
                        'name'  => $request->card_names_old[$k],
                        'cvc'   => $request->card_cvcs_old[$k],
                        'mm'    => $request->card_months_old[$k],
                        'yyyy'  => $request->card_years_old[$k]
                    ]);
                }
            }

            if($request->card_number_dels){
                $user->cards()->whereIn('id', $request->card_number_dels)->delete();
            }
            return response()->json([
                'success' => true,
                'message' => 'Cart save successfully!'
            ]);
        }
        return response()->json([
            'success' => false,
            'message' => 'Something went wrong!'
        ]);
    }

    public function storeBank(Request $request){
        $user = User::find(CRUDBooster::myId());
        if($user){

            if ($request->card_ibans) {
                $request->validate([
                    'card_names.*' => 'required|regex:/^[a-zA-Z ]+$/i|min:4|max:128|string',
                    'card_bank_names.*' => 'required|min:4|string|regex:/^[a-zA-Z ]+$/i',
                    'card_ibans.*' => 'required|string',
                    'card_ibcs.*' => 'required|min:4|max:15|string|regex:/^[a-zA-Z0-9]+$/u',
                    'card_days.*' => 'required',
                ],
                [
                    'card_names.*' => 'Invalid Name! Only letters accepeted',
                    'card_bank_names.*' => 'Invalid Bank Name! Only letters accepted',
                    'card_ibans.*.required' => 'Bank IBAN number can not be empty!',
                    'card_ibcs.*' => 'Invalid IBC Format! Only letters and numbers accepted',
                    'card_days.*' => 'Interval Days Required!',
                ]);

                // IBAN validate
                $request->validate([
                    'card_ibans.*' => function ($attribute, $value, $fail) {
                        try {
                            app(\App\Services\UiValidation\UiValidation::class)->validateIBAN($value);
                        } catch (\Exception $e) {
                            $fail($e->getMessage());
                        }
                    },
                ]);

                foreach($request->card_ibans as $i => $number){
                    $number = preg_replace('/[^A-Z0-9 ]/', '', $number);
                    $user->banks()->create([
                        'name'  => $request->card_names[$i],
                        'bank_name'   => $request->card_bank_names[$i],
                        'iban' => $number,
                        'bic'    => $request->card_bics[$i],
                        'interval'  => $request->card_days[$i]
                    ]);
                }
            }

            if ($request->card_ibans_old) {
                $request->validate([
                    'card_names_old.*' => 'required|regex:/^[a-zA-Z ]+$/i|min:3|max:128|string',
                    'card_bank_names_old.*' => 'required|min:5|string|regex:/^[a-zA-Z ]+$/i',
                    'card_ibans_old.*' => 'required|string',
                    'card_ibcs_old.*' => 'required|min:4|max:15|regex:/^[a-zA-Z0-9]+$/i|string',
                    'card_days_old.*' => 'required',
                ],
                [
                    'card_names_old.*' => 'Invalid Name! Only letters accepeted',
                    'card_bank_names_old.*' => 'Invalid Bank Name! Only letters accepted',
                    'card_ibans_old.*.required' => 'Bank IBAN number can not be empty!',
                    'card_ibcs_old.*' => 'Invalid IBC Format! only letters and numbers are accepted',
                    'card_days_old.*' => 'Interval Days Required!',
                ]);

                // IBAN validate
                $request->validate([
                    'card_ibans_old.*' => function ($attribute, $value, $fail) {
                        try {
                            app(\App\Services\UiValidation\UiValidation::class)->validateIBAN($value);
                        } catch (\Exception $e) {
                            $fail($e->getMessage());
                        }
                    },
                ]);

                foreach($request->card_ibans_old as $k => $o_number){
                    $o_number = preg_replace('/[^A-Z0-9 ]/', '', $o_number);
                    $user->banks()->where('id', $k)->update([
                        'iban' => $o_number ,
                        'name'  => $request->card_names_old[$k],
                        'bank_name'   => $request->card_bank_names_old[$k],
                        'bic'    => $request->card_bics_old[$k],
                        'interval'  => $request->card_days_old[$k]
                    ]);
                }
            }

            if($request->card_ibans_dels){
                $user->banks()->whereIn('id', $request->card_ibans_dels)->delete();
            }

            // Do accounting
            app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

            return response()->json([
                'success' => true,
                'message' => 'Bank Info save successfully!'
            ]);
        }
        return response()->json([
            'success' => false,
            'message' => 'Something went wrong!'
        ]);
    }

    public function userCard(){
        $user = User::find(CRUDBooster::myId());
        if($user){
            if(\CRUDBooster::isSupplier()) {

                $update_at = $user->banks->updated_at;
                $update_plan = true;
                $next_date = null;
                if($update_at) {
                    $next_date = Carbon::parse($update_at)->addDays(120)->format('d.m.Y');
                    $update_plan = ( (Carbon::parse($update_at)->addDays(120)) > Carbon::now() ) ? false : true;
                }
                return response()->json([
                    'success' => true,
                    'html' => view('subscription.mp-card', compact('user', 'update_plan', 'next_date'))->render(),
                    'update_plan' => $update_plan
                ]);
            } else {
                return response()->json([
                    'success' => true,
                    'html' => view('subscription.card', compact('user'))->render()
                ]);
            }
        }else{
            return response()->json([
                'success' => false,
                'message' => 'Invalid User'
            ]);
        }
    }

    public function billingAddressFill(User $user){
        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy)? $privacy->page_content : '';

        if($user->billing_detail){
            $billing = $user->billing_detail;

            $user_data = $billing->company_name.'<br>'.$billing->address.'<br>'.$billing->zip.' '.$billing->city.'<br>'.$billing->country->name;


            if (strpos($term, '{customer}') !== false) {
                $term = str_replace('{customer}', $user_data, $term);
            }

            return response()->json([
                'success' => true,
                'html' => view('app_store.term', compact('user', 'billing', 'term'))->render()
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'You have no billing address. Please create! '
        ]);
    }


    public function uploadPDFS(Request $request)
    {
        try {
            if ($request->hasFile('pdf_link')) {
                $pdf = $request->file('pdf_link');
                $pdf_type = strtolower($pdf->getClientOriginalExtension());
                if ($pdf_type == "pdf") {
                    $storing_path = request()->get('storing_path', 'marketplace-collection/pdf/');
                    $pdf_url = uploadImage($pdf, $storing_path . CRUDBooster::myId());

                    return response()->json(['success' => true, 'name' => $pdf_url], 200);
                }
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false], 422);
        }
        return response()->json(['success' => true]);
    }

    public function showPDFS($id){

        if(\CRUDBooster::isSupplier() || User::isSupplier($id)){
            if(\CRUDBooster::isSupplier()) {
                $user = User::find(\CRUDBooster::myId());
                $pdf_list = ($user->billing_detail->pdf_link) ? $user->billing_detail->pdf_link : null;
                return response()->json([
                    'success' => true,
                    'html' => view('subscription.billing-pdf', compact('pdf_list'))->render(),
                ]);
            }else if(User::isSupplier($id)) {
                $user = User::find($id);
                $pdf_list = ($user->billing_detail->pdf_link) ? $user->billing_detail->pdf_link : null;
                return response()->json([
                    'success' => true,
                    'html' => view('subscription.billing-pdf', compact('pdf_list'))->render(),
                ]);
            }
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Invalid User'
            ]);
        }
    }

    public function showBankInfo($id) {
        $user = User::find($id);

        if($user) {
            $bank_details = ($user->banks) ? $user->banks : null;
            return response()->json([
                'success' => true,
                'html' => view('subscription.mp-bankinfo', compact('bank_details'))->render(),
            ]);
        } else {
            return response()->json([
                'success' => false,
                'html' => view('subscription.mp-bankinfo', compact('bank_details'))->render(),
            ]);
        }
    }

    public function getBankInfo($id){
        $banks = Bank::where('user_id',$id)->get();
        if($banks) {
            $bank_details = ($banks) ? $banks : null;
            return response()->json([
                'success' => true,
                'html' => view('subscription.mp-bankinfo', compact('bank_details'))->render(),
            ]);
        } else {
            return response()->json([
                'success' => false,
                'html' => view('subscription.mp-bankinfo', compact('bank_details'))->render(),
            ]);
        }
    }
    public function supplierBankList(){
        $user_id = CRUDBooster::myId();
        $banks = Bank::where('user_id',$user_id)->get();

        return view('subscription.bank_list',compact('banks'));

    }

    public function createSetupIntend(Request $request)
    {
        $data = [];
        $data['name']       = CRUDBooster::myName();
        $data['email']      = CRUDBooster::me()->email;
        $data['user_id']    = CRUDBooster::myId();

        $setupCard = new \App\Services\Stripe\SetupCard($request->stripe_key);
        $setup_intend = $setupCard->ctreateSetupIntent($data);
        return view('app_store.card_setup', $setup_intend);
    }

    public function showSetupIntendModal(Request $request)
    {
        try{

            $data = [];
            $data['stripe_key'] = $request->stripe_key;
            $data['for_update_subscription'] = $request->input('for_update_subscription', 0);
            $data['name'] = CRUDBooster::myName();
            $data['email'] = CRUDBooster::me()->email;
            $data['user_id']    = CRUDBooster::myId();

            $setupCard = new \App\Services\Stripe\SetupCard($request->stripe_key);
            $res = $setupCard->stripeSetupModal($data);

            if($res && $res['client_secret']){
                $res['success'] = true;
                return response()->json($res);
            }

            throw new \Exception('Something went wrong!');

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function showAccessSetUpMpModal(Request $request)
    {
        try{

            $data = [];
            $data['stripe_key'] = $request->stripe_key;
            $data['name'] = CRUDBooster::myName();
            $data['email'] = CRUDBooster::me()->email;
            $data['user_id'] = CRUDBooster::myId();

            $setupCard = new \App\Services\Stripe\SetupCard($request->stripe_key);
            $res = $setupCard->stripeMpAccessSetupModal($data);

            if($res && $res['client_secret']){
                $res['success'] = true;
                return response()->json($res);
            }

            throw new \Exception('Something went wrong!');

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function saveSetupIntendMethod(Request $request){
        try{

            $user_id = CRUDBooster::myId();
            $setupCard = new \App\Services\Stripe\SetupCard($request->stripe_key);
            $success = $setupCard->savePaymentMethod($request->payment_method, $user_id);

            if($success){
                return response()->json([
                    'success' => true,
                    'message' => 'Payment method saved successfully!'
                ], 201);
            }

            throw new \Exception('Payment method saved failed!');
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }


    public function assignSetupCard(Request $request){
        $request->validate([
            'stripe_key' => 'required',
            'service_id' => 'required',
            'card_id'    => 'required'
        ]);

        try{

            $user_id = CRUDBooster::myId();
            $setupCard = new \App\Services\Stripe\SetupCard($request->stripe_key);
            $success = $setupCard->asignPaymentMethod($user_id, $request->card_id, $request->service_id);

            if($success){
                return response()->json([
                    'success' => true,
                    'message' => 'Payment method assign update successfully!'
                ], 201);
            }

            throw new \Exception('Payment method assign failed!');
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function removeSetupCard(Request $request){
        $request->validate([
            'card_id'    => 'required',
            'stripe_key' => 'required',
        ]);

        try{
            $data = [];
            $user_id = CRUDBooster::myId();

            // check already added cards count; if only 1 exists then dont remove
            $prev_cards_count = DB::table('stripe_setup_cards')
                ->where([
                    'user_id' => $user_id,
                    'stripe_key' => $request->stripe_key,
                ])
                ->count();
            $data['prev_cards_count'] = $prev_cards_count;

            if ($prev_cards_count <= 1) {
                return response()->json([
                    'success' => true,
                    'data' => $data,
                    'message' => __('marketplace.stripe_only_card_remove_msg')
                ], 201);
            }


            $setupCard = new \App\Services\Stripe\SetupCard($request->stripe_key);
            $success = $setupCard->removeSetupCard($user_id, $request->card_id);

            if($success){
                return response()->json([
                    'success' => true,
                    'data' => $data,
                    'message' => 'Card removed successfully!'
                ], 201);
            }

            throw new \Exception('Card removed failed!');
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function confirmSetupIntendPayment(Request $request){
        try{
            $payment_id = $request->payment_id;
            if(empty($payment_id)) throw new \Exception('Invalid action!');

            $payment = DB::table('stripe_pending_auto_payments')->where('id', $payment_id)->where('status', '=', 0)->whereNotNull('payment_method')->whereNotNull('client_secret')->first();
            if(empty($payment)) throw new \Exception('Invalid payment!');

            if($payment->user_id != CRUDBooster::myId()) throw new \Exception('Invalid access!');

            $payment_method = $payment->payment_method;
            $client_secret = $payment->client_secret;

            $stripe_key = $payment->stripe_key;
            $setupCard = new \App\Services\Stripe\SetupCard($stripe_key);
            $public_key = $setupCard->getPublicKey();

            return response()->json([
                'success' => true,
                'payment_method' => $payment_method,
                'client_secret'  => $client_secret,
                'stripe_key'    => $stripe_key,
                'public_key'    => $public_key,
                'id'            => $payment->id
            ], 200);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }


    public function successSetupIntendPayment(Request $request)
    {
        try{

            $setupCard = new \App\Services\Stripe\SetupCard($request->stripe_key);
            $success = $setupCard->paymentSuccess($request->payment_intent, $request->payment_id);

            if($success){
                return response()->json([
                    'success' => true,
                    'message' => 'Payment completed successfully!'
                ], 201);
            }

            throw new \Exception('Payment processing failed!');

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function getMonthlyReport(){
        try{
            $user_id = CRUDBooster::myId();
            $creditnote = CreditNote::where('supplier_id',$user_id)->take(5)->orderBy('month','desc')->get();

            return response()->json([
                'success' => true,
                'html' => view('subscription.monthly_report',compact('creditnote'))->render(),
            ]);

            throw new \Exception('Payment processing failed!');

        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        }
    }

    public function getMonthlyReportList(){
        $user_id = CRUDBooster::myId();
        $creditnotes = CreditNote::where('supplier_id',$user_id)->orderBy('month','desc')->get();
        return view('subscription.monthly_report_list',compact('creditnotes'));
    }

    public function getSupplierUploadedInvoceUpload($id){
        $creditnot_id = $id;
        $uploaded_invoices = SupplierUploadInvoice::where('credit_id',$id)->get();

        return response()->json([
            'success' => true,
            'html' => view('subscription.supplier_uploaded_invoice',compact('uploaded_invoices','creditnot_id'))->render(),
        ]);
    }
    public function supplierInvoceUpload(Request $request,$id){
        $request->validate([
          'invoice_url' => 'required',
       ]);

        try {
            if ($request->hasFile('invoice_url')) {
                $pdf = $request->file('invoice_url');
                $pdf_type = strtolower($pdf->getClientOriginalExtension());
                if ($pdf_type == "pdf") {
                    $pdf_url = uploadImage($pdf, 'supplier-upload-invoice/test/pdf/' .time(). $id);

                    $upload_invoice = new SupplierUploadInvoice();
                    $upload_invoice->credit_id = $id;
                    $upload_invoice->invoice_url = $pdf_url;
                    $upload_invoice->save();
                    return response()->json(['success' => true], 200);
                }
            }
        } catch (\Exception $e) {
            return response()->json(['success' => false], 422);
        }
    }

    public function genarateSupplierCreditNote($creditnot_id, $local = false, $download = false)
    {
        $creditnot = CreditNote::find($creditnot_id);
        $user_id = $creditnot->supplier_id;
        $date_data = explode('-',$creditnot->month);
        $year = isset($date_data[0])? $date_data[0] : date('Y');
        $month = isset($date_data[1])? $date_data[1] : date('m');

        $data['page_title'] = 'Supplier Credit Note';


        $data['user'] = User::with(['new_orders' => function($q) use($month, $year){
            return $q->whereYear('new_orders.order_date', $year)->whereMonth('new_orders.order_date', $month)->where('new_orders.invoice_number', '>', 0)->orderBy('new_orders.order_date');
        } , 'billing_detail'])->find($user_id);

        if(is_null($data['user']) || is_null($data['user']->id)) CRUDBooster::redirectBack("Invoice Not Avilable", 'error');

        $data['setting'] = BillingDetail::with('country')->where('user_id', 2455)->first();
        $data['invoice_month'] = $creditnot->month;
        $data['referenznummer'] = $creditnot->id;
        
        $pdf = PDF::loadView('admin.invoice.supplier_invoice', $data);
        return $pdf->stream();

    }

    public function saveBankInformation(Request $request){
        $user = CRUDBooster::myId();
        if($user){

                $request->validate([
                    'bank_name.*' => 'required',
                    'iban.*' => 'required',
                    'bic.*' => 'required',
                ],
                [
                    'bank_name.*' => 'Invalid Bank Name! Only letters accepted',
                    'iban.*.required' => 'Bank IBAN number can not be empty!',
                    'bic.*' => 'Invalid IBC Format! Only letters and numbers accepted',
                ]);


                // IBAN validate
                $request->validate([
                    'iban' => function ($attribute, $value, $fail) {
                        try {
                            app(\App\Services\UiValidation\UiValidation::class)->validateIBAN($value);
                        } catch (\Exception $e) {
                            $fail($e->getMessage());
                        }
                    },
                ]);

                Bank::create([
                    'user_id' => $user,
                    'name'  => 'none',
                    'bank_name'   => $request->bank_name,
                    'iban' => $request->iban,
                    'bic'    => $request->bic,
                    'interval'  => 0
                ]);

                // Do accounting
                app(\App\Services\UiValidation\UiValidation::class)->doAccounting();

                return response()->json(['success' => true],200);
        }
    }

    public function supplierBankInfoEdit($id){
        $bank = Bank::where('id',$id)->first();
        return response()->json([
            'success' => true,
            'html' => view('subscription.bank_info_edit',compact('bank'))->render(),
        ]);
    }

    public function supplierBankInfoUpdate(Request $request,$id){
        $bank = Bank::where('id',$id)->first();
        $bank->bank_name  = $request->bank_name;
        $bank->iban  = $request->iban;
        $bank->bic  = $request->bic;
        $bank->update();

        CRUDBooster::redirectBack("Bank Info updated", 'success');
    }

    public function supplierBankDelete($id){
        Bank::where('id',$id)->delete();

        CRUDBooster::redirectBack("Bank Info deleted", 'success');
    }

    public function supplierCreditNoteProductInfo($id){
        
        $creditnote = CreditNote::find($id);
        $user_id = $creditnote->supplier_id;
        $date_data = explode('-',$creditnote->month);
        $year = isset($date_data[0])? $date_data[0] : date('Y');
        $month = isset($date_data[1])? $date_data[1] : date('m');
        $product_array = [];
        $orders = NewOrder::where('cms_user_id',$user_id)->whereYear('order_date', $year)->whereMonth('order_date', $month)->where('invoice_number', '>', 0)->get();

        foreach($orders as $value){
            foreach(json_decode($value->cart) as $product){
                if(array_key_exists($product->ean,$product_array)){
                    $product_array[$product->ean]['product_quantity'] += $product->qty;
                    $product_array[$product->ean]['product_total'] += $product->qty * $product->rate;
                }else{
                    $product_array[$product->ean] = [
                        'product_quantity'  => $product->qty,
                        'product_total'     => $product->qty * $product->rate,
                        'item_number'       => $product->item_number,
                    ];
                }
            }
        }

        return view('marketplace.supplier.incoice_product_info',compact('product_array'));
    }

    public function billingAddressValidation()
    {
        $input_check_field = $_REQUEST['input_field'];

        if($input_check_field == 'email'){
            try {
                app(\App\Services\UiValidation\UiValidation::class)->validateEmail($_REQUEST['input_val']);
            } catch (\Exception $e) {
                return response()->json([
                    'message' => $e->getMessage()
                ], 400);
            }
        }else if($input_check_field == 'phone'){
            try {
                app(\App\Services\UiValidation\UiValidation::class)->validatePhone($_REQUEST['input_val']);
            } catch (\Exception $e) {
                return response()->json([
                    'message' => $e->getMessage(),
                ], 400);
            }
            
        }else if(in_array($input_check_field, ['address', 'city', 'zip', 'country'])){
            $address = [
                'country' => $_REQUEST['country_input_val'],
                'zip_code' => $_REQUEST['zip_input_val'],
                'city' => $_REQUEST['city_input_val'],
                'street' => $_REQUEST['address_input_val'],
            ];

            try {
                app(\App\Services\UiValidation\UiValidation::class)->validateAddress($address);
            } catch (\Exception $e) {
                return response()->json([
                    'errors' => [
                        'email' => [$e->getMessage()],
                    ],
                ], 400);
            }

        }
    }

}
