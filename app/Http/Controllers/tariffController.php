<?php

namespace App\Http\Controllers;

use App\Coupon;
use App\Enums\Apps;
use App\Models\Product\AnalysisProduct;
use App\NewOrder;
use App\User;
use Carbon\Carbon;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Http\Request;
use DB;
use App\Models\Marketplace\UserAccess;
use App\Models\Marketplace\MpVirtualCredit;
use App\Models\Marketplace\MpVirtualCreditLog;
use App\DrmUserCreditAddLog;
use App\DrmUserCreditRemoveLog;
use App\DrmUserCredit;
use App\Enums\CreditType;
use App\Enums\DroptiendaPlan;
use Exception;
use App\Jobs\DestroyProduct;
use App\Services\Tariff\Credit\ChargeCredit;
use App\Services\Tariff\Credit\CreditService;
use App\Services\Tariff\Credit\RefillCredit;
use App\Services\TariffService;
use App\StripePayment;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB as FacadesDB;
use App\ReturnLabelCredit;

class tariffController extends Controller
{

    private TariffService $tariffService;

    public function __construct(TariffService $tariff_service)
    {
        $this->tariffService = $tariff_service;
    }

    public function index()
    {

        // if ( !in_array(CRUDBooster::myParentId(), [212, 210, 2592, 179, 3159, 3160]) ) {
        //     CRUDBooster::redirect(CRUDBooster::adminPath('/'), trans("crudbooster.denied_access"));
        // }

        $user_id = CRUDBooster::myParentId();
        $data['billing'] = $this->userBillingAddress();

        $data['tariff_tab'] = $_REQUEST['tab'];

        // $data['user_import_plan'] = DB::table('purchase_import_plans')
        //     ->join('import_plans', 'import_plans.id', '=', 'purchase_import_plans.import_plan_id')
        //     ->where('purchase_import_plans.cms_user_id', $user_id)
        //     ->select('purchase_import_plans.id as purchase_id', 'purchase_import_plans.end_date as end_date', 'import_plans.plan', 'import_plans.interval', 'purchase_import_plans.is_renew_cancel')
        //     ->first();

        $data['is_dt_new_user'] = is_dt_user() && checkTariffEligibility($user_id);

        $data['user_import_plan'] = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);

        if(!$data['is_dt_new_user']){
            $data['import_plan'] = DB::table('purchase_import_plans')
                            ->join('import_plans','import_plans.id','=','purchase_import_plans.import_plan_id')
                            ->where('purchase_import_plans.cms_user_id', $user_id)
                            ->select('purchase_import_plans.*','import_plans.amount','import_plans.plan','import_plans.interval', 'import_plans.credit as total_credit')
                            ->first();

            $data['purchase_info'] = StripePayment::join('purchase_import_plans', 'stripe_payments.subscription_id', 'purchase_import_plans.stripe_subscription_id')
                ->where([
                    'stripe_payments.user_id' => $user_id,
                    'purchase_import_plans.cms_user_id' => $user_id,
                ])
                ->where('stripe_subscription_id', 'like', 'sub_%')
                ->exists();
        }else{
            $data['import_plan'] = DB::table('dt_tariff_purchases')
                            ->join('import_plans','import_plans.id','=','dt_tariff_purchases.plan_id')
                            ->where('dt_tariff_purchases.user_id', $user_id)
                            ->select('dt_tariff_purchases.*','dt_tariff_purchases.total as price','import_plans.amount','import_plans.plan','import_plans.interval')
                            ->first();

            $data['purchase_info'] = StripePayment::join('dt_tariff_purchases', 'stripe_payments.subscription_id', 'dt_tariff_purchases.subscription_id')
                ->where([
                    'stripe_payments.user_id' => $user_id,
                    'dt_tariff_purchases.user_id' => $user_id,
                ])
                ->where('stripe_payments.subscription_id', 'like', 'sub_%')
                ->exists();
        }

        // $recommend_plan_id = DB::table('purchase_import_plans')
        //     ->select('import_plan_id', DB::raw('count(*) as total'))
        //     ->where('id', '!=', $data['user_import_plan']->purchase_id)
        //     ->orderByRaw('count(*) DESC')
        //     ->groupBy('import_plan_id')
        //     ->value('import_plan_id');

        $user_total_products = drmTotalProduct($user_id);

        $mp_parent_cat_access = DB::connection('marketplace')->table('marketplace_user_accesses')->where('user_id', $user_id)->value('accessable_parent_categories');
        $user_total_mp_access = $mp_parent_cat_access ? count(array_filter( json_decode($mp_parent_cat_access, true) )) : 0;

        $creditService = new CreditService;
        if(checkTariffEligibility($user_id)){
            // $credit_detail = $this->getCreditInfo($user_id);
            $credit_detail = $creditService->credit();

            $data['credit_detail'] = $credit_detail; // 1
        }

        $data['total_mp_cat_access'] = $user_total_mp_access;
        $data['user_total_products'] = $user_total_products;

        $data['user_channel_add_detail'] = app('App\Http\Controllers\Product\DRMProductController')->userChannelAddition($user_id);

        $user_total_channel_added = $data['user_channel_add_detail']['channel_added'];
        $data['user_total_channel_added'] = $user_total_channel_added;

        $data['channel_add_progress_bar'] = 0;

        if($data['user_channel_add_detail']['total_channel'] == 9999999999){
            if($data['user_channel_add_detail']['channel_added'] > 0){
                $data['channel_add_progress_bar'] = 28;
            }else{
                $data['channel_add_progress_bar'] = 0;
            }
        }else if($data['user_channel_add_detail']['total_channel'] > 0){
            $data['channel_add_progress_bar'] = (int)(($data['user_channel_add_detail']['channel_added'] * 100) / $data['user_channel_add_detail']['total_channel']);
        }else{
            $data['channel_add_progress_bar'] = 0;
        }

        $data['mp_cat_limit'] = app('App\Http\Controllers\AdminMarketplaceCategoriesController')->userMpCategoryLimit($user_id);

        $mp_cat_progress_bar = 0;

        if($data['mp_cat_limit'] == 9999999999){
            if($data['total_mp_cat_access'] > 0){
                $mp_cat_progress_bar = 30;
            }else{
                $mp_cat_progress_bar = 0;
            }
        }else{
            $mp_cat_progress_bar = $data['mp_cat_limit'] > 0 ? (int)(($data['total_mp_cat_access'] * 100) / $data['mp_cat_limit']) : 0;
        }

        // $data['mp_cat_percentage'] = ($data['mp_cat_limit'] == 9999999999) ? 30 : (int)(($data['total_mp_cat_access'] * 100) / $data['mp_cat_limit']);
        $data['mp_cat_percentage'] = $mp_cat_progress_bar;

        $data['logs'] = DB::table('agb_logs')->where('user_id', $user_id)
            ->orderBy('id', 'DESC')
            ->get(['message','created_at']);

        $data['billing_emails'] = DB::table('billing_details')->where('user_id', $user_id)->value('billing_emails');

        // $data['recommend_plan'] = DB::table('import_plans')->where('id', $recommend_plan_id)->first();

        $recomended_import_plan = DB::table('import_plans')->where('status', 1);

        if(!empty($data['import_plan']) && $data['import_plan']->import_plan_id != 27){
            $recomended_import_plan = $recomended_import_plan->where('id', '<>', $data['import_plan']->import_plan_id);
        }

        if(checkTariffEligibility($user_id)){
            $recomended_import_plan = $recomended_import_plan->where(function($q) use($user_total_products){
                $q->where('product_amount', '=', $user_total_products);
                $q->orWhere('product_amount', '>', $user_total_products);
            });

            $recomended_import_plan = $recomended_import_plan->where('tariff_active', 1)
            ->where('dt_tariff_active', 0)
            ->where(function($q) use($user_total_mp_access){
                $q->where('mp_category', '=', $user_total_mp_access);
                $q->orWhere('mp_category', '>', $user_total_mp_access);
            });

            // if(in_array($data['user_import_plan']['plan'], ['Trial', '500 Free Products'])){
            //     $recomended_import_plan = $recomended_import_plan->whereRaw('CASE WHEN credit = 0 THEN credit = '.$credit_detail['used_credit'].' OR credit > '.$credit_detail['used_credit'].' ELSE credit > '.$credit_detail['used_credit']. ' END');
            // }else{
            //     $recomended_import_plan = $recomended_import_plan->whereRaw('CASE WHEN credit = 0 THEN credit = '.$credit_detail['remain_credit'].' OR credit > '.$credit_detail['remain_credit'].' ELSE credit > '.$credit_detail['remain_credit']. ' END');
            // }

            $recomended_import_plan = $recomended_import_plan->where(function($q) use($user_total_channel_added){
                $q->where('no_of_online_channels', '=', $user_total_channel_added);
                $q->orWhere('no_of_online_channels', '>', $user_total_channel_added);
            });
        }else{
            $recomended_import_plan = $recomended_import_plan->where('product_amount', '>', $user_total_products)->where('tariff_active', 0)->where('dt_tariff_active', 0);
        }

        $recomended_import_plan = $recomended_import_plan->orderBy('product_amount', 'asc')
        ->first();

        $data['recommend_plan'] = $recomended_import_plan;

        if(checkTariffEligibility($user_id)){
            if(!$data['is_dt_new_user']){
                $data['plans'] = DB::table('import_plans')->where('status', 1)->where('tariff_active', 1)->where('dt_tariff_active', 0)->orderBy('amount', 'asc')->get();
            }else{
                $data['plans'] = DB::table('import_plans')->where('status', 1)->where('tariff_active', 0)->where('dt_tariff_active', 1)->orderBy('amount', 'asc')->get();
            }
        }else{
            $data['plans'] = DB::table('import_plans')->where('status', 1)->where('tariff_active', 0)->where('dt_tariff_active', 0)->orderBy('amount', 'asc')->get();
        }
        $data['months'] = $this->getMonths();

        if(($user_id == 2592) || checkTariffEligibility($user_id)){
            // $data['analysis_info'] = $this->getAnalysisInfo();
            $user_credit_info = $credit_detail; // 2 

            // if(in_array($data['user_import_plan']['plan'], ['Free', 'Trial'])){
            //     if($user_credit_info['used_credit'] > 0){
            //         $user_credit_info['uesed_percentage'] = 45;
            //     }else{
            //         $user_credit_info['uesed_percentage'] = 0;
            //     }
            // }else if(in_array($data['user_import_plan']['plan'], ['500 Free Products'])){
            //     if($user_credit_info['used_credit'] > 0){
            //         $user_credit_info['uesed_percentage'] = 100;
            //     }else{
            //         $user_credit_info['uesed_percentage'] = 0;
            //     }
            // }else{
                $user_credit_info['uesed_percentage'] = ($user_credit_info['total_credit'] > 0) ? (int)( ($user_credit_info['used_credit'] * 100) / $user_credit_info['total_credit'] ) : 0;
            // }

            $data['credit_info'] = $user_credit_info; // 3

            //Marketplace monthly turnover credit info
            // $mp_turnover_credit_info = $this->getMarketplaceTurnoverCredit($user_id);
            $mp_turnover_credit_info = $creditService->mpCredit();
            
            $mp_turnover_credit_info['uesed_percentage'] = ($mp_turnover_credit_info['total_credit'] > 0) ? 
                (int)(($mp_turnover_credit_info['used_credit'] * 100) / $mp_turnover_credit_info['total_credit']) : 0;
            $data['mp_turnover_credit_info'] = $mp_turnover_credit_info;

            // $data['bonus_credit_used_percentage'] = ($mp_turnover_credit_info['total_credit'] > 0) ? 
            //     ( ($user_credit_info['used_credit'] - (int)$user_credit_info['credit_without_turnover'])
            //     / (int)$mp_turnover_credit_info['total_credit'] ) * 100 
            //     : 0;
            // $data['bonus_credit_used'] = $user_credit_info['used_credit'] - (int)$user_credit_info['credit_without_turnover'];

            $data['bonus_credit_used_percentage'] = ($user_credit_info['bonus_total_credit'] > 0) ? 
                (int)(($user_credit_info['bonus_used_credit'] * 100) / $user_credit_info['bonus_total_credit']) : 0;
            $data['bonus_credit_used'] = $user_credit_info['bonus_used_credit'];
            $data['bonus_credit_total'] = $user_credit_info['bonus_total_credit']; // 
        }

        $data['import'] = $this->importInfo();

        if(checkTariffEligibility($user_id)){
            $data['top_up'] = DB::table('drm_tariff_balance')->where('user_id', $user_id)->first();
        }

        $data['user_id'] = $user_id;

        $has_credit_setting = DB::table('cms_users')
        ->where('id', $user_id)
        ->value('marketplace_access');

        if($has_credit_setting)
        {
            $data['credit_setting'] = \App\Option::where([
                'option_key' => 'credit_setting',
                'option_group' => 'credit_setting',
                'user_id' => $user_id,
            ])->value('option_value');

            $data['has_credit_setting'] = $has_credit_setting;

            $data['virtual_credit_setting'] = \App\Option::where([
                'option_key' => 'virtual_credit_setting',
                'option_group' => 'virtual_credit_setting',
                'user_id' => $user_id,
            ])->value('option_value');
        }

        $data['all_purchase_app'] = DB::table('purchase_apps')
            ->join('app_stores','app_stores.id','=','purchase_apps.app_id')
            // ->where('purchase_apps.type','!=','Free Trail')
            ->where('purchase_apps.cms_user_id', $user_id)
            ->where(function($query) {
                    $query->where('purchase_apps.subscription_date_end','>', date('Y-m-d'))
                    ->orwhere('purchase_apps.subscription_life_time',1);
            })
            ->select('app_stores.menu_name','app_stores.download_file','app_stores.icon','app_stores.url','purchase_apps.*')
            ->get();

        $data['has_marketplace_access'] = CRUDBooster::hasMarketplaceAccess();
        $data['marketplace_categories'] = UserAccess::where('user_id', $user_id)
                                        ->first();
        $data['dropmatix_einkauf'] = CRUDBooster::isDropmatixEinkauf();
        $data['dropmatix'] = CRUDBooster::isDropMatrix();

        $data['has_dt_shop'] = \App\Shop::where(['channel' => 10, 'status' => true, 'user_id' => $user_id])->exists();

        //Assign apps id
        $assign_apps = DB::table('app_assigns')
        ->join('app_stores','app_stores.id','app_assigns.app_id')
	    ->where('app_assigns.user_id', $user_id)
        ->where(function($q){
            $q->whereNull('app_assigns.end_date')->orWhereDate('app_assigns.end_date', '>=', \Carbon\Carbon::now());
        })
        ->select('app_stores.url','app_stores.menu_name', 'app_assigns.*')
        ->get();

        //Assign apps id
        $assign_apps_ids = $assign_apps->pluck('app_id')->toArray();

        //Project app
        $project_app_id = config('global.project_app_id');

        //Menues may have combined with some assign app. Remove it next section
        $purchase_apps_ids = DB::table('purchase_apps')
        ->where('cms_user_id', $user_id)
        ->where('subscription_date_end', '>=', \Carbon\Carbon::now())
        ->pluck('app_id')
        ->toArray();

        $project_app_data = null;

        //Check project app data
        if(!in_array($project_app_id, $assign_apps_ids) && !in_array($project_app_id, $purchase_apps_ids)){
            $has_project = DB::table('drm_project_members')->where('cms_user_id', $user_id)->exists();
            if($has_project){
                $project_app_data = DB::table('app_stores')->where('id', $project_app_id)->first();
            }
        }

        $data['app_assigns'] = $assign_apps;
        $data['project_app_data'] = $project_app_data;

        $data['virtual_credit'] = MpVirtualCredit::where('user_id', $user_id)->sum('amount');
        $data['return_label_credit'] = ReturnLabelCredit::where('user_id', $user_id)->where('type', 1)
                ->whereBetween('created_at', [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()])
                ->selectRaw('COUNT(*) as total_return_credit, SUM(amount) as total_return_credit_amount')
                ->first();

        $data['sub_accounts'] = DB::table('cms_users')
        ->join('cms_privileges', 'cms_privileges.id', '=', 'cms_users.id_cms_privileges')
        ->whereNotNull('cms_users.parent_id')
        ->where('cms_users.parent_id', $user_id)
        ->whereIntegerNotInRaw('cms_users.id_cms_privileges', [9, 10, 11])
        ->select('cms_users.id', 'cms_users.name', 'cms_users.email', 'cms_users.id_cms_privileges', 'cms_users.photo', 'cms_users.status', 'cms_privileges.name as priv_name')
        ->get();

        $data['billing_address_exists'] = DB::table('billing_details')
        ->where('user_id', $user_id)
        ->where('email', '<>', '')
        ->where('address', '<>', '')
        ->where('city', '<>', '')
        ->where('zip', '<>', '')
        ->where('country_id', '<>', '')
        ->exists();

        $data['virtual_credit_log'] = DB::table('mp_virtual_credit')->leftjoin('mp_virtual_credit_log', 'mp_virtual_credit_log.mp_virtual_credit_id', '=', 'mp_virtual_credit.id')
            ->whereNotNull('mp_virtual_credit_log.id')
            ->where('mp_virtual_credit.user_id', $user_id)
            ->select('mp_virtual_credit_log.message', 'mp_virtual_credit_log.type', 'mp_virtual_credit_log.created_at')
            ->orderBy('mp_virtual_credit_log.id', 'desc')
            ->groupBy('mp_virtual_credit_log.id')
            ->get()
            ->toJson();

        $is_greater_id_user = checkTariffEligibility($user_id) ? 1 : 0;

        // $coupon_type = $is_greater_id_user ? 'onboarding' : 'regular';
        $coupon_type = '';
        $user_voucher_data = app('\App\Services\CouponService')->getUserVoucherData($user_id, $coupon_type, false);

        $onboarding_coupon_valid = 0;
        if (!empty($user_voucher_data)) {
            if ($user_voucher_data->coupon_cat == config('global.onboarding.coupon_cat')) {
                if ($is_greater_id_user) {

                    $valid_coupon_info = app('\App\Services\CouponService')->getValidCouponInfo($user_voucher_data->coupon_code);
                    if (!empty($valid_coupon_info)) {
                        $onboarding_coupon_valid = 1;
                    }
                } else {
                    $user_voucher_data = null;
                }
            }
        }

        $data['onboarding_coupon_valid'] = $onboarding_coupon_valid;
        $data['user_coupon']             = $user_voucher_data;

        $redeem_date = '';
        if ($is_greater_id_user && !empty($user_voucher_data)) {
            $redeem_date = app('\App\Services\CouponService')->getOnboardingCouponRedeemDate($user_voucher_data);
        }
        $data['redeem_date'] = $redeem_date;

        $data['dt_plan_detail'] = [
            'Ideal als Webvisitenkarte, Unternehmenswebseite oder Blog. Ideal für erste Sichtbarkeit im Netz',
            'Einstieg in die E-Commerce-Welt inkl. Shop, Google Shopping und unbegrenzt Produkten/Kategorien',
            'Großer Funktionsumfang für professionelle Seiten und Webshops inklusive Online-Marketing-Videokurs',
            'Alle Funktionen inklusive - großartig für mehrsprachige Webshops und Cooperate-Webseiten‌',
        ];

        $data['lang'] = app('App\Services\UserService')->getLang($user_id);
        $data['product_page_url'] = route('drm.product.index').'?lang='.$data['lang'];
        $data['mp_cat_page_url'] = url('admin/tariff?tab=mp_settings');

        $data['dt_eligibale_user'] = checkDtEligibleUser($user_id);



        $currentTariff = $data['import_plan'];
        $tariffExpiredMessage = '';
        if (!blank($currentTariff) && $currentTariff->is_renew_cancel == 1 && Carbon::parse($currentTariff->end_date)->gte(now()))
        {
            $tariffExpiredDays = Carbon::parse($currentTariff->end_date)->diffInDays(now());
            $tariffExpiredMessage = __("Backup data before tariff termination", ['day' => $tariffExpiredDays]);
        }

        $data['tariffExpiredMessage'] = $tariffExpiredMessage;

        $has_payment_contract = false;
        if (!empty($data['import_plan']->contract_end_at) && ($data['import_plan']->contract_end_at >= \Carbon\Carbon::now())) { 
            $has_payment_contract = true;
        }
        $data['has_payment_contract'] = $has_payment_contract;


        return view("admin.tariff.index", $data);
    }

    public function updateTopUpStatus()
    {
        $request = $_REQUEST;
        $user_id = CRUDBooster::myParentId();
        if ($request) {
            DB::table('drm_tariff_balance')->updateOrInsert([
                "user_id" => $user_id,
            ], [
                "use_top_up" => $request['top_up'],
                "created_at" => now(),
                "updated_at" => now()
            ]);
            return response()->json([
                'success' => true,
                'message' => 'Switch update successfully!',
            ]);
        }
    }


    public function insertBillingEmail(Request $request){
        $emails = $request->emails;
        $user_id = CRUDBooster::myParentId();
        if($emails){
            DB::table('billing_details')->where('user_id', $user_id)
                ->update([
                    'billing_emails' => json_encode($emails)
                ]);
            return response()->json([
                'success' => true,
                'message' => 'Update successfully!',
            ]);
        }
    }


    public function getInvoices()
    {
        $request = $_REQUEST;
        $orders = NewOrder::where('cms_client', CRUDBooster::myId());

        if (in_array(CRUDBooster::myId(), [52])) {
            $orders->where('id', '>', 26566);
        }


        if (!empty($request['date'])) {
            $dateYear = explode('-', $request['date']);
            $orders = $orders->whereMonth('created_at', $dateYear['0'])
                ->whereYear('created_at', $dateYear['1']);
        }


        $orders = $orders->whereIn('cms_user_id', [98, 2455, 2454, 2439])
            // ->whereIn('insert_type', [3, 4, 8, 9])
            ->whereIn('insert_type', [5, 9, 10])
            ->where('cms_client', CRUDBooster::myParentId())
            ->orderBy('id', 'desc')
            ->select('id', 'order_date', 'invoice_number', 'total')
            ->get();

        $html = 'No invoice found!';
        if ($orders) {
            $html = '';
            foreach ($orders as $order) {
                $html .= '
                        <div class="plan-card" id="invoice_list" style="padding: 6px;border: none; box-shadow: rgb(0 0 0 / 15%) 0px 5px 15px 0px;">
                            <div class="row" style=" display: flex;padding-top: 6px;">
                            <div class="plan-info" style="height: 100%;width: 70%;">
                                <div style="font-size: 120%;margin-bottom: 8px;"><span
                                        style="font-family: Montserrat, sans-serif; font-weight: 600;">INV: ' . $order->invoice_number . '</span><span
                                        style="font-family: Montserrat, sans-serif; font-weight: 500;">&nbsp;On ' . $order->order_date . '</span>
                                </div>

                                <a href="' . CRUDBooster::adminPath('purchase_invoice_download') . '/' . $order->id . '" target="_blank"><span
                                        style="font-family: Montserrat, sans-serif; font-weight: 600;"></span><span
                                        style="font-family: Montserrat, sans-serif; font-weight: 500;">Download PDF</span>
                                </a>
                            </div>
                            <div class="purchase-plan"
                                 style="text-align: right;height: 100%;width: 26%;">
                                <div style="font-size: 140%; font-weight: 500; font-family: Montserrat, sans-serif;margin-bottom: 6px;">
                                  <div class=" btn btn-plan" type="button" class="btn btn-drm">
                                    ' . number_format((float)$order->total, 2, ',', '.') . formatCurrency('EUR') . '
                                </div>
                                </div>
                            </div>
                        </div>
                        </div>
                        ';

            }
        }

        return response()->json([
            'success' => true,
            'html' => $html,
        ]);
    }


    public function userBillingAddress()
    {
        $user_id = CRUDBooster::myParentId();
        $user = User::find($user_id);
        if ($user) {
            return $user->billing_detail;
        }
    }

    public function getMonths()
    {

        $dates = NewOrder::where('cms_client', CRUDBooster::myParentId())
            ->whereIn('cms_user_id', [98, 2455, 2454, 2439])
            // ->whereIn('insert_type', [3, 4, 8])
            ->whereIn('insert_type', [9, 10])
            ->distinct()
            ->orderBy('created_at', 'desc')
            ->get([
                DB::raw('YEAR(`created_at`) AS `year`'),
                DB::raw('MONTH(`created_at`) AS `month`'),
            ]);
        return $dates;
    }

    public function getAnalysisInfo()
    {
        $user_id = CRUDBooster::myParentId();
        $query = AnalysisProduct::where('user_id', $user_id);
        $analysis['total'] = $query->count('id');
        $unanalysed = $query->whereNull('google_price')->whereNull('amazon_price')->whereNull('ebay_price')->count('id');
        $analysis['analysed'] = $analysed = $analysis['total'] - $unanalysed;
        $analysis['percentage'] = ($analysed) ? $analysis['analysed'] / $analysis['total'] * 100 : 0;
        return $analysis;
    }

    public function importInfo()
    {
        $user_id = CRUDBooster::myParentId();
        $is_user_dt = is_dt_user() && checkTariffEligibility($user_id);

        $plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
        $banner = true;
        $product = drmTotalProduct($user_id);
        if ($plan['days'] < 0 || $plan['days'] == null) {
            $plan['days'] = 0;
        }

        $message = __('menu.REMAIN_DAYS') . "<b>" . $plan['days'] . "</b>" . __('menu.DAYS');
        if ($plan['plan'] == "Expired") {
            $message = "Package : " . $plan['plan'];
        }

        if ($plan['plan'] == "Trial" || $plan['plan'] == "none") {
            $banner = false;
        } else {
            $limit_amount = $plan['plan_total'];
            if ($limit_amount < 0) {
                $limit_amount = 0;
            }
        }

        // return [
        //     'limit_amount' => $limit_amount,
        //     'product' => $product,
        //     'banner' => $banner,
        //     'message' => $message,
        //     'percentage' => ($product / $limit_amount) * 100
        // ];

        $import_info = [
            'product' => $product,
            'banner' => $banner,
            'message' => $message
        ];

        if ($plan['plan'] == "Trial"){
            $import_info['limit_amount'] = $plan['limit'];

            if(checkTariffEligibility($user_id)){
                if($product > 0){
                    if(!$is_user_dt){
                        $import_info['percentage'] = 20;
                    }else{
                        $import_info['percentage'] = ($product / $plan['limit']) * 100;
                    }
                }else{
                    $import_info['percentage'] = 0;
                }
            }else{
                $import_info['percentage'] = 20;
            }

        }else if ($plan['plan'] == "Free"){

            if(!$is_user_dt){
                $import_info['limit_amount'] = 0;
                $import_info['percentage'] = 0;
            }else{
                $import_info['limit_amount'] = $plan['limit'];
                $import_info['percentage'] = ($product / $plan['limit']) * 100;
            }

        }else if ($plan['plan'] == "500 Free Products"){
            if(checkTariffEligibility(CRUDBooster::myParentId())){
                if(!$is_user_dt){
                    if($product > 0){
                        $import_info['limit_amount'] = $product;
                        $import_info['percentage'] = 100;
                    }else{
                        $import_info['limit_amount'] = $product;
                        $import_info['percentage'] = 0;
                    }
                }else{
                    $import_info['limit_amount'] = $plan['plan_limit'];
                    $import_info['percentage'] = ($product / $plan['plan_limit']) * 100;
                }
            }else{
                $import_info['limit_amount'] = $limit_amount;
                $import_info['percentage'] = ($product / ($limit_amount ?? 1)) * 100;
            }
        }else{
            $import_info['limit_amount'] = $limit_amount;
            $import_info['percentage'] = ($product / ($limit_amount ?? 1)) * 100;
        }

        return $import_info;
    }

    // return credit total, used, remaining info
    public function getCreditInfo($user_id): array
    {
        // $total_credit = DrmUserCreditAddLog::where('user_id', $user_id)->where('status', 1)->sum('credit');
        $running_plan = userCurrentPlan($user_id);

        $total_credit = DrmUserCreditAddLog::where('user_id', $user_id)->where(['status' => 1])->where('type', '!=', \App\Enums\CreditType::CHEAPEST_PRICE_CALCULATION);
        if(!empty($running_plan['planStartAt'])){
            $total_credit = $total_credit->whereDate('created_at', '>=', $running_plan['planStartAt']);
        }
        $total_credit = $total_credit->sum('credit');

        $used_credit = DrmUserCreditAddLog::where('user_id', $user_id)->where(['status' => -1])->where('type', '!=', \App\Enums\CreditType::CREDIT_RESET);
        if(!empty($running_plan['planStartAt'])){
            $used_credit = $used_credit->whereDate('created_at', '>=', $running_plan['planStartAt']);
        }
        $used_credit = $used_credit->sum('credit');

        // $remain_credit = DrmUserCredit::where('user_id', $user_id)->value('credit');
        $credit_without_turnover = DrmUserCreditAddLog::where('user_id', $user_id)->where('type','!=',CreditType::MARKETPLACE_ORDER)->where('status', 1)->sum('credit');

        $credit_info['total_credit'] = $total_credit ?? 0;
        $credit_info['used_credit'] = $used_credit ?? 0;

        $credit_info['remain_credit'] = $credit_info['total_credit'] - $credit_info['used_credit'];

        $credit_info['credit_without_turnover'] = $credit_without_turnover ?? 0;

        // $check_trial_start = DB::table('app_trials')->where('user_id', $user_id)->exists(); // Check if user at trial period
        // $user_plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);

        // if(in_array($user_plan['plan'], ['Free', 'Trial', '500 Free Products'])){

        //     // $plan_purchase = DB::table('purchase_import_plans')->where('cms_user_id', $user_id)->exists();

        //     // if(!$plan_purchase){
        //         // $credit_info['used_credit'] = DrmUserCreditRemoveLog::where('user_id', $user_id)->sum('credit');
        //         $credit_info['used_credit'] = DrmUserCreditAddLog::where('user_id', $user_id)->where('status', -1)->sum('credit');
        //     // }else{
        //     //     $credit_info['used_credit'] = $credit_info['total_credit'] - $credit_info['remain_credit'];
        //     // }
        // }else {

            // $credit_info['used_credit'] = $credit_info['total_credit'] - $credit_info['remain_credit'];

        // }

        return $credit_info;
    }

    public function mpCreditSetting()
    {
        $credit_setting = $_REQUEST['credit_setting'];
        $user_id = CRUDBooster::myId();

        try {
            \App\Option::updateOrCreate([
                'option_key' => 'credit_setting',
                'option_group' => 'credit_setting',
                'user_id' => $user_id,
            ],
            [
                'option_value' => $credit_setting === 'yes' ? 'yes' : 'no',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Marketplace Credit Setting Successful !'
            ], 200);
        }catch (\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function mpVirtualCreditSetting()
    {
        $virtual_credit_setting = $_REQUEST['virtual_credit_setting'];
        $user_id = CRUDBooster::myId();

        try {
            \App\Option::updateOrCreate([
                'option_key' => 'virtual_credit_setting',
                'option_group' => 'virtual_credit_setting',
                'user_id' => $user_id,
            ],
            [
                'option_value' => $virtual_credit_setting === 'yes' ? 'yes' : 'no',
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Marketplace Virtual Credit Setting Successful!')
            ], 200);
        }catch (\Exception $e){
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function advContractDownload()
    {
        $user = User::find(CRUDBooster::myParentId());

        $privacy = DB::table('drm_pages')->where('page_name', 'app_store_privacy')->first();
        $term = ($privacy)? $privacy->page_content : '';

        $billing = $user->billing_detail;

        $user_data = $billing->company_name.'<br>'.$billing->address.'<br>'.$billing->zip.' '.$billing->city.'<br>'.$billing->country->name;

        if (strpos($term, '{customer}') !== false) {
            $term = str_replace('{customer}', $user_data, $term);
        }

        $pdf = \PDF::loadView('admin.tariff.app_contract_pdf', compact('term'));

        return $pdf->download(__('billing_plans.ORDER_DATA').'.pdf');
    }

    function drmUserCreditAdd($user_id, $credit, $message, $type, $status)
    {
        $insert_data = [
            'user_id' => $user_id,
            'credit' => $credit,
            'message' => $message,
            'type' => $type
        ];

        $insert_data['status'] = ($status == CreditType::CREDIT_ADD) ? CreditType::CREDIT_ADD  : CreditType::CREDIT_REMOVE;

        DrmUserCreditAddLog::create($insert_data);

        $message = '';

        if($status == CreditType::CREDIT_ADD){
            $message = 'Credit Addition Data Inserted';
        }else{
            $message = 'Credit Remove Data Inserted';
        }

        return $message;
    }

    function CreditUpdate($user_id, $credit, $flag)
    {
        $user_credit = DrmUserCredit::firstOrNew([
            'user_id' => $user_id
        ]);

        if(!empty($user_credit->credit)){
            if($flag == 'credit_add'){
                $user_credit->credit = $user_credit->credit + $credit;
            }else if($flag == 'credit_deduct'){
                $user_credit->credit = $user_credit->credit - $credit;
            }
        }else{
            $user_credit->credit = $credit;
        }

        $user_credit->save();

        $message = ($flag == 'credit_add') ? 'Credit Added' : 'credit_deduct';

        return $message;
    }

    function drmUserCreditRemove($user_id, $credit, $message, $type)
    {
        DrmUserCreditRemoveLog::create([
            'user_id' => $user_id,
            'credit' => $credit,
            'message' => $message,
            'type' => $type
        ]);

        return 'Credit Expense Log Inserted';
    }

    public function getMarketplaceTurnoverCredit($user_id): array
    {

        $total_credit = DrmUserCreditAddLog::where('user_id', $user_id)->where('type', 4)->where('status', 1)->sum('credit');
        // $used_credit = DrmUserCreditRemoveLog::where('user_id', $user_id)->where('type', 4)->sum('credit');
        $used_credit = DrmUserCreditAddLog::where('user_id', $user_id)->where('type', 4)->where('status', -1)->sum('credit');

        $mp_turnover_credit_info['total_credit'] = $total_credit ?? 0;
        $mp_turnover_credit_info['used_credit'] = $used_credit ?? 0;

        return $mp_turnover_credit_info;
    }

    public function getBonusProgramModal()
    {
        $content = view('admin.tariff.partials.bonus_program_modal')->render();

        return response()->json([
            'success' => true,
            'html' => $content
        ], 200);

    }
    public function creditReductionCheck($user_id, $count, $payPerAsYouGoRate, $type){  // if it costs 0.1 eur per unit when token isn't available, $payPerAsYouGoRate will be 0.1
        $tariff_available = 0;

        // $user_import_plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
        // in_array($user_import_plan['plan'], ['Free', 'Trial']) ||

        if(CRUDBooster::isDropMatrix() || CRUDBooster::isDropmatixSupport()){
            $tariff_available = 1;
        }

        else{
            // $credit = DB::table('drm_user_credits')->where('user_id', $user_id)->first();

            // if($credit->credit >= $count){
            $remaining_credit = (new CreditService())->remainingCredit();
            if ($remaining_credit >= $count) {
                $tariff_available = 1;
            }
            else{
                $top_up = DB::table('drm_tariff_balance')->where('user_id', $user_id)->first();

                if($top_up){
                    if($top_up->use_top_up == 1 && ($top_up->balance >= ($count * $payPerAsYouGoRate) ) ){
                        $tariff_available = 1;
                    }
                }
            }
        }

        return $tariff_available;
    }

    public function creditCharge($user_id, $count, $payPerAsYouGoRate, $type){

        // $user_import_plan = app('App\Http\Controllers\AdminDrmImportsController')->importProductCheck($user_id);
        $status = \App\Enums\CreditType::CREDIT_REMOVE;

        // in_array($user_import_plan['plan'], ['Free', 'Trial']) ||

        if(CRUDBooster::isDropMatrix() || CRUDBooster::isDropmatixSupport()){
            // $this->drmUserCreditAdd($user_id, $count, 'product_translation_credit_deduct', $type, $status);
            (new ChargeCredit)->charge($user_id, $count, \App\Services\Tariff\Credit\CreditType::PRODUCT_TRANSLATION_CREDIT_DEDUCT);
        }
        else{
            // $credit = DB::table('drm_user_credits')->where('user_id', $user_id)->first();

            // if($credit->credit >= $count){
            //     $this->CreditUpdate($user_id, $count, 'credit_deduct');
            //     $this->drmUserCreditAdd($user_id, $count, 'product_translation_credit_deduct', $type, $status);
            // }
            $remaining_credit = (new CreditService())->remainingCredit();
            if ($remaining_credit >= $count) {
                (new ChargeCredit)->charge($user_id, $count, \App\Services\Tariff\Credit\CreditType::PRODUCT_TRANSLATION_CREDIT_DEDUCT);
            }
            else{
                $top_up = DB::table('drm_tariff_balance')->where('user_id', $user_id)->first();

                if($top_up){
                    if($top_up->use_top_up == 1 && ($top_up->balance >= ($count * $payPerAsYouGoRate) ) ){
                        $balance = $top_up->balance - ($count * $payPerAsYouGoRate);

                        DB::table('drm_tariff_balance')->where('user_id', $user_id)->update([
                            'balance' => $balance
                        ]);
                    }
                }
            }
        }
        return true;
    }


    public function accountClearModal()
    {
        $data = [];
        $user_id = CRUDBooster::myParentId();

        $_GET['user_id'] = $user_id;

        if(isset($_GET['plan_token']) && empty($_GET['plan_token'])){
            $_GET['plan_token'] = 0;
        }

        $_GET['has_marketplace_access'] = CRUDBooster::hasMarketplaceAccess();
        $_GET['marketplace_categories'] = UserAccess::where('user_id', $user_id)->exists();
        $_GET['dropmatix_einkauf'] = CRUDBooster::isDropmatixEinkauf();
        $_GET['dropmatix'] = CRUDBooster::isDropMatrix();

        $data['plan_info'] = json_encode($_GET, true);

        $modal_content = view('admin.tariff.partials.acnt_clear_conf_modal', $data)->render();

        return response()->json([
            'success' => true,
            'html' => $modal_content
        ], 200);
    }

    public function creditDelete()
    {
        $user_id = $_REQUEST['user_id'];

        try{
            // $this->tariffService->creditDelete($user_id);  //No credit will be delete from now 23-10-23

            if(request()->ajax()){
                return response()->json([
                    'success' => true,
                    'message' => __("Data Successfully Deleted")
                ], 200);
            }

            return [
                "success" => true,
                'message' => __("Data Successfully Deleted")
            ];
        }catch(Exception $e){
            if(request()->ajax()){
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 400);
            }

            return [
                "success" => false,
                "message" => $e->getMessage()
            ];
        }

    }

    public function tariffAllDataDelete()
    {
        $user_id = CRUDBooster::myParentId();

        $tariff_name = '';
        if (is_dt_user()) {
            $user_tariff = FacadesDB::table('dt_tariff_purchases')->where('user_id', $user_id)->first();
            $plan_id = $user_tariff->plan_id;

            $dt_tariff_plans = config('global.dt_tariff_plans');
            $tariff_name = array_key_exists($plan_id, $dt_tariff_plans) ? $dt_tariff_plans[$plan_id] : $plan_id;
        } else {
            $user_tariff = FacadesDB::table('purchase_import_plans')->where('cms_user_id', $user_id)->first();
            $plan_id = $user_tariff->import_plan_id;

            $drm_tariff_plans = config('global.drm_tariff_plans');
            $tariff_name = array_key_exists($plan_id, $drm_tariff_plans) ? $drm_tariff_plans[$plan_id] : $plan_id;
        }

        // 1st All Products Delete
        $product_ids = app('App\Services\DRMProductService')->getSelectedIds($user_id, []);

        $languageId = app('App\Services\UserService')->getProductCountry($user_id);
        $lang = app('App\Services\UserService')->getProductLanguage($languageId);

        foreach(array_chunk($product_ids, 500) as $chunkIds){
            DestroyProduct::dispatch($chunkIds, $user_id, $lang, $languageId);
        }

        // 2nd All MP Categories Delete
        app('App\Http\Controllers\Marketplace\MarketPlaceController')->customerBulkParentCategoryRemove();

        // 3rd All Channels Delete
        $this->tariffService->userChannelStatusUpdate($user_id);

        // 4th All Credits/Token Delete
        // $this->tariffService->creditDelete($user_id); //No credit will be delete from now 23-10-23

        try {
            create_agb_log(
                $user_id,
                [
                    'tariff_info' => ['tariff_name' => $tariff_name, ],
                ], 
                [],
                'Delete all is selected when choosing tariff.',
            );
        } catch (\Exception $th) {}

        if(request()->ajax()){
            return response()->json([
                'success' => true,
                'message' => __('Tariff related data deletion process has been started. It may take some time. Please try to purchase your desire plan after some time.')
            ], 200);
        }
    }

    public function dtUserCacheClear($id)
    {
        // For old user, we need to clear cache from checkDtUser() helper function to migrate user to new tariff
        Cache::forget('check_is_dt_user_' . $id);
        echo "Cache Cleared !";
    }

    public function remainCreditSet($user_id, $credit)
    {
        DrmUserCredit::where('user_id', $user_id)
        ->update([
            'credit' => $credit
        ]);
    }

    public function creditReset($user_id)
    {
        // $remaining_credit = $this->tariffService->getRemainingCredit($user_id);
        $remaining_credit = (new CreditService)->remainingCredit($user_id);

        // if(!empty($remaining_credit['total_credit_remain'])){
        //     $credit = $remaining_credit['total_credit_remain'];
        if (!empty($remaining_credit)) {
            $message = 'Credit reset';
            $type = \App\Enums\CreditType::CREDIT_RESET;
            $status = \App\Enums\CreditType::CREDIT_REMOVE;

            // $this->drmUserCreditAdd($user_id, $credit, $message, $type, $status);
            (new RefillCredit)->resetTariffCredit($user_id, (float) $remaining_credit, \App\Services\Tariff\Credit\CreditType::CREDIT_RESET);

            // $this->remainCreditSet($user_id, 0);
        }
    }

    public function mpVirtualCreditInfoGetting()
    {
        $virtual_credit_logs = DB::table('mp_virtual_credit')
            ->leftJoin('mp_virtual_credit_log', 'mp_virtual_credit_log.mp_virtual_credit_id', '=', 'mp_virtual_credit.id')
            ->leftJoin('new_orders', 'new_orders.id', '=', 'mp_virtual_credit_log.order_id')
            ->whereNotNull('mp_virtual_credit_log.id')
            ->where('mp_virtual_credit.user_id', \CRUDBooster::myParentId())
            ->orderByDesc('mp_virtual_credit_log.created_at')
            ->select('mp_virtual_credit.*', 'mp_virtual_credit_log.*', 'new_orders.marketplace_order_ref', 'new_orders.credit_ref')
            ->get();

        return view('admin/tariff/partials/virtual_credit_info', compact('virtual_credit_logs'));
        
    } 

    public function returnLabelCreditInfo(Request $request)
    {

        $selected_month =$request->month;
        $current_month = Carbon::now()->month;
        if($selected_month > $current_month) $selected_month = $current_month;

        $return_credit_logs = DB::table('return_label_credit as rlc')
            ->leftJoin('order_trackings as ot', 'ot.id', '=', 'rlc.order_tracking_id')
            ->where('rlc.user_id', \CRUDBooster::myParentId())
            ->whereMonth('rlc.created_at', $selected_month)
            ->whereYear('rlc.created_at', Carbon::now()->year)
            ->orderByDesc('rlc.created_at')
            ->select('rlc.*', 'ot.shipment_data as shipment_data')
            ->get();
            
        return view('admin/tariff/partials/return_label_credit_info', compact('return_credit_logs'));

    } 

    private array $extensions = [
        'xls' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'csv' => 'text/csv',
    ];

    // Export data
    public function mpWalletExport(Request $request)
    {
        $ext = $request->export_type;
        $date_from = $request->date_from;
        $date_to = $request->date_to;

        $filename = 'wallet-'.date('Y-m-d-His').'.'.$ext;

        $rows = DB::table('mp_virtual_credit')
            ->leftJoin('mp_virtual_credit_log', 'mp_virtual_credit_log.mp_virtual_credit_id', '=', 'mp_virtual_credit.id')
            ->leftJoin('new_orders', 'new_orders.id', '=', 'mp_virtual_credit_log.order_id')
            ->whereNotNull('mp_virtual_credit_log.id')
            ->where('mp_virtual_credit.user_id', \CRUDBooster::myParentId());
          
        if (!empty($date_from) || !empty($date_to)) {
            $date_from = !empty($date_from) ? $date_from : '2023-07-07';
            $date_to = !empty($date_to) ? $date_to : now()->format('Y-m-d');

            $rows->whereBetween('mp_virtual_credit_log.created_at', [$date_from, $date_to]);
        }

        $rows = $rows->orderByDesc('mp_virtual_credit_log.created_at')
            ->select('mp_virtual_credit.*', 'mp_virtual_credit_log.*', 'new_orders.marketplace_order_ref', 'new_orders.credit_ref', 'new_orders.currency')
            ->get();

        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $this->setRow(1, [
                'created_at' => __('Date'),
                'amount' => __('Amount'),
                'message' => __('Message'),
                'order_id' => __('Order'),
            ], $sheet);

        $i = 2;

        if ($ext === 'xls') {
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        } else {
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Csv($spreadsheet);
        }

        return new \Symfony\Component\HttpFoundation\StreamedResponse(
            function () use ($writer, $rows, &$i, &$sheet) {
                foreach ($rows as $index => $log) {
                    $currency = $log->currency;
                    $logType = $log->type;
                    $order_id = $logType == 1 ? $log->credit_ref : $log->order_id;

                    $amount = $logType == 1 ? 
                        __('marketplace.credit_amount') . number_format($log->amount, 2, ',', '.') . ' ' . $currency . ' A+' : 
                        __('marketplace.payment_by_credit') . number_format($log->amount, 2, ',', '.') . ' ' . $currency;

                    $re_currency = " " . $currency;
                    $message = str_replace([$log->order_id, "&#8364;"], [$log->marketplace_order_ref, $re_currency], $log->message);

                    $this->setRow($i, [
                        'created_at' => $log->created_at,
                        'amount' => $amount, 
                        'message' => $message,
                        'order_id' => $order_id,
                    ], $sheet);
                    $i++;
                }
                $writer->save('php://output');
            },
            200,
            [
                'Content-Type' => $this->extensions[$ext],
                'Content-Disposition' => 'attachment;filename="'.$filename.'"',
                'Cache-Control' => 'no-cache',
            ]
        );
    }

    // Set rows
    private function setRow($i, array $row, &$sheet)
    {
        $sheet->setCellValue('A'.$i, $row['created_at']);
        $sheet->setCellValue('B'.$i, $row['amount']);
        $sheet->setCellValue('C'.$i, $row['message']);
        $sheet->setCellValue('D'.$i, $row['order_id']);
    }

}
