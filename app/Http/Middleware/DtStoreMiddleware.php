<?php
namespace App\Http\Middleware;


use Closure;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Session;
use Illuminate\Support\Facades\DB;

class DtStoreMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        //Check user has dt shop
        $has_dt_shop = \App\Shop::where('channel', '=', 10)->where('status', '=', 1)->where('user_id', CRUDBooster::myParentId())->exists();
        if(!($has_dt_shop || CRUDBooster::isSuperAdmin())){
            CRUDBooster::redirect(CRUDBooster::adminPath(), 'Sorry! you don\'t have Droptienda store!');
        }

        return $next($request);

    }
}