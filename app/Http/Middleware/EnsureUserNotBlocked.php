<?php

namespace App\Http\Middleware;

use Closure;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class EnsureUserNotBlocked
{
    protected $except = [
        'api/*',
        'admin',
        'admin/login',
        'admin/logout',
        'admin/forgot*',
        'admin/verification_code*',

        'admin/invoice-payment*',
        'admin/drm_all_orders/detail*',
        'admin/paywall-payment*',
        'admin/drm-subscription-payment*',

        'admin/header-notification-data*',

        'admin/dashboard*',
        'update-session-id',
        'admin/vod-watchlist-data',
        'admin/user-account-checklist-data',
        'admin/sidebar-product-import-info',
        'admin/card-setup*',
        'sidebar*',
        'admin/stripe*',
        'stripe*',
        'load-iframe*',

        'drop-funnel-count-down*',

        'marketplace/supplier/reg*',

        'drm-email-iframe',

        'registration',
        'supplier/registration*',

        'uscreen*', //Uscreen routes

        'admin/update-plan', //Update plan
        'uploads*',
        'admin/language*',
        'admin/paypal*',
    ];


    protected $dtEntryUserBlockingPath = [
        'admin/app-list*',
        'admin/upcoming_invoices',
        'admin/upcoming_invoices/*',
        // 'admin/upcoming_invoices_basic*',
        'admin/upcoming_invoices_basic/add*',
        'admin/drm_all_orders/add*',
        'admin/stripe-token-list*',
        'admin/competitive_analysis*',
        'admin/google-shopping-merchant*',
        'admin/update-merchant-id*',
        'admin/amazon-best-seller*',
        'admin/webshop-analysis*',
        // 'admin/drm_dropfunnel_customers*',
        'admin/email_marketings',
        'admin/email_marketings/dropfunnel-home*',
        'admin/email_marketings/add',

        'admin/opln_mails*',
        'admin/offer_form*',
        // 'admin/drop_funnel_signatures*',
        'admin/marketing_plan*',
        'admin/drop_funnel_count_downs*',
        // 'admin/contact_forms*',
        'admin/appointment_booking*',
        'admin/drm_projects',
        'admin/drm_projects/add',
        'admin/drm_projects/project',
        'admin/drm_projects/delete',
        'admin/drm_projects/edit',
        'admin/drm_projects/detail',
        'admin/drm_project_reports*',
    ];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {

        if(is_dt_entry_user() && $request->is($this->dtEntryUserBlockingPath))
        {
            return redirect(CRUDBooster::adminPath())->with(['message' => 'Access denied!', 'message_type' => 'warning', 'import_plan_popup' => 'yes' ])->send();
        }

        if( in_array(CRUDBooster::myId(),[2814,2822,2823])){
            $access_url = [ 'api/*',
                            'admin',
                            'uploads*',
                            'admin/login',
                            'admin/users*',
                            'admin/logout',
                            'admin/dashboard*',
                            'update-session-id',
                            'admin/user/logback',
                            'admin/language/set-lang*',
                            'admin/users/collapse-sidebar*',
                            'admin/header-notification-data',
                            'admin/marketplace/chat/dashboard-chat-histories',
                            'admin/marketplace/all-marketplace-api',
                            'admin/marketplace/import-api-category',
                            'admin/marketplace/export-api-category*',
                            'admin/marketplace/mapping-api-category*',
                            'admin/marketplace/edit-marketplace-api*',
                            'admin/marketplace/marketplace-api-save',
                            'uploads*',
                            'admin/paypal*',
                            ];

            if($request->is($access_url)){
                return $next($request);
            }else{
                return redirect(CRUDBooster::adminPath())->with(['message' => "Sorry! You are not authorized for this module", 'message_type' => 'warning' ]);
            }
        }

        if (checkTariffEligibility(CRUDBooster::myParentId())) {
            $import_plan_access_url = [ 
                'test/*',
                'api/*',
                'admin',
                'admin/login',
                'admin/logout',
                'admin/forgot*',
                'admin/verification_code*',
                'admin/users/logback',

                'admin/invoice-payment*',
                'admin/paywall-payment*',
                'admin/drm-subscription-payment*',

                'admin/header-notification-data*',

                'admin/dashboard*',
                'update-session-id',
                'admin/vod-watchlist-data',
                'admin/user-account-checklist-data',
                'admin/sidebar-product-import-info',
                'admin/card-setup*',
                'sidebar*',
                'admin/stripe*',
                'stripe*',
                'load-iframe*',

                'drop-funnel-count-down*',

                'marketplace/supplier/reg*',

                'drm-email-iframe',

                'registration',
                'supplier/registration*',

                'uscreen*', //Uscreen routes

                'admin/update-plan', //Update plan

                'admin/onboarding-content',
                'admin/update-onboarding-step',
                'admin/onboarding-email-communication',
                'admin/tariff',
                'admin/new-import-payment',
                'admin/get-import-plan-popup',
                'admin/get-contract-modal-popup',
                'admin/access-all-categories*',
                'admin/search-all-categories-customer',
                'admin/allow-or-cancel-marketplace-access-customer-first',
                'admin/access-user-categories-open*',
                'admin/allow-or-cancel-marketplace-access-customer',
                'admin/drm_products',
                'admin/drm_products/delete_product*',
                'admin/drm_products/select-all-products',
                'admin/drm_products/select-all-products-delete',
                'admin/drm_projects*',
                'uploads*',
                'admin/language*',

                'admin/dt-tariff-popup',
                'admin/dt-tariff-form',
                'admin/shop_setting',
                'admin/shop_setting/delete*',
                'admin/shop_setting/all-category',

                'admin/tariff/account-clear',
                'admin/tariff/credit-delete',
                'admin/tariff/all-tariff-data-delete',
                'admin/marketplace/customer-bulk-parent-category-remove',
                'admin/drm_products/connected-shops',
                'admin/drm_products/channel-progress',
                'admin/coupons*',
                'billing-address',
                'admin/paypal*',
                'admin/shop_setting*',
            ];
                     
            $user_id = CRUDBooster::myParentId();
            $plan_state     = app('\App\Services\CouponService')->check_user_import_plan_state($user_id);
            $plan_purchased = $plan_state['plan_purchased'] ? true : false;
            $trial_ends     = $plan_state['trial_ends'] ? true : false;

            $is_dt_user = is_dt_user() && (checkTariffEligibility($user_id));
            $dt_triff_purchased = Cache::remember('dt_user_tariff_plan_'.$user_id, now()->addMinutes(10), function () use ($user_id) {
                return dtTariffPurchased($user_id);
            });

            if($is_dt_user){
                if(!$dt_triff_purchased && $trial_ends){
                    if($request->is($import_plan_access_url)) {
                        return $next($request);
                    } else {
                        return redirect(CRUDBooster::adminPath('tariff'))->with(['message' => "Sorry! DT tariff plan expired!", 'message_type' => 'warning', 'dt_tariff_expired' => 'yes' ]);
                    }
                }
            }else{
                if (!$plan_purchased && $trial_ends) {
                    if($request->is($import_plan_access_url)) {
                        return $next($request);
                    } else {
                        return redirect(CRUDBooster::adminPath('tariff'))->with(['message' => "Sorry! Import plan expired!", 'message_type' => 'warning', 'drm_tariff_expired' => 'yes' ]);
                    }
                }
            }
        }

        $parent_id = CRUDBooster::myParentId();
        if(empty($parent_id)){
            return $next($request);
        }

        //Skip routes
        foreach($this->except as $skip) {
            if($request->is($skip)) {
                return $next($request);
            }
        }


        //Skip marian popup
        if(in_array(CRUDBooster::myId(), [2600, 485])){
            return $next($request);
        }

        //Due payments list
        $duePayments = \DRM::duePaymentInvoiceList();
        if(!empty($duePayments)){
            $message = CRUDBooster::isSubUser()? 'Sorry! Your parent user is blocked for due payments!' : 'Sorry! you have due payments!';
            if($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => $message,
                    'data' => $duePayments
                ], 403);
            }
            return redirect(CRUDBooster::adminPath())->with(['message' => $message, 'message_type' => 'warning', 'due_popup_data' => $duePayments ])->send();
        }

        return $next($request);
    }
}
