<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array
     */
    protected $except = [
        'stripe/*',
        'klicktipp/webhook',
        'uscreen/customer/webhook',
        'uscreen/transaction/webhook',
        'uscreen/transaction_due/webhook',
        'registration/sign-up',
        'supplier/registration*',
        'admin/login',
        'admin/drm_exports/save-response',
        'get-mailgun-data',
        'contact-form/save',
        'appointment-new-user-booking',
        'appointment-user-booking-store',
        '/admin/save-page-design',
        '/admin/save-offer-contact',
        '/save-offer-contact',
        '/campaign-unsubscribe/*',
        '/unsubscribe-campaign/*',
    ];
}
