<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class MarketplaceProductValidation extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            "name" => "required",
            "ean" => "required",
            "price"  => "required",
            "ek_price"  => "required",
            'vk_price' => 'required',
            'purchase_price' => 'required',
            'stock' => 'required',
            'image' => 'nullable|image',
            "collection_id" => 'required',
            "category_id" => 'required',
            'description' => 'required',
            'update_enabled' => 'required',
        ];
    }
}
