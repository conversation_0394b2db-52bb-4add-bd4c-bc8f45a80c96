<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class CustomerTagResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
      $date = ($this->updated_at)? $this->updated_at : $this->created_at;
      $tag_score = ($this->score > 1)? ' ( '.$this->score.')' : '';
      $tag = $this->tag;
      $title = $tag->tag.$tag_score;

      return [
        'id' => $this->id,
        'title' => $title,
        'input_type' => $this->insert_type,
        'date' => $date->format('Y-m-d H:i:s'),
      ];
    }
}