<?php

namespace App\Http\Resources;

use App\Models\ChannelProduct;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DTSyncResource extends JsonResource
{
    const CREATE = 'create';
    const UPDATE = 'update';
    const DELETE = 'delete';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $status = $this->mapStatus();
        $finishedAt = "<em>Not finished</em>";
        if ($this->synced_at) {
            $finishedAt = Carbon::createFromTimeString($this->synced_at)->diffForHumans();
        }
        $ean = $this->sync_type == 'product' ? ChannelProduct::where('id',$this->model_id)->value('ean') : "";

        return [
            'itemId' => $this->model_id,
            'ean' => $ean,
            'itemType' => ucfirst($this->sync_type),
            'event' => $this->mapEvent(),
            'status' => $status,
            'triggeredAt' => Carbon::createFromTimeString($this->created_at)->diffForHumans(),
            'finishedAt' => $finishedAt
        ];
    }

    private function mapEvent(): ?string
    {
        return [
                self::CREATE => '<span class="label label-success">CREATE</span>',
                self::UPDATE => '<span class="label label-info">UPDATE</span>',
                self::DELETE => '<span class="label label-warning">DELETE</span>',
            ][$this->sync_event] ?? "";
    }

    private function mapStatus(): string
    {
        if ($this->synced_at == NULL) {
            $status = "<strong style='color: #0a6aa1;font-size: smaller;'>WORKING</strong>";
            if ($this->exception != NULL || $this->tries == 3) {
                $status = "<strong style='color: darkred;font-size: smaller;'>FAILED</strong>";
            }
        } else {
            $status = "<strong style='color: #0B7500;font-size: smaller;'>SUCCESS</strong>";
        }
        return $status;
    }
}
