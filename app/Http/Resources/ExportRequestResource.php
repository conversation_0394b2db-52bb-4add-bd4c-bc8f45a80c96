<?php

namespace App\Http\Resources;

use App\Models\ChannelProduct;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExportRequestResource extends JsonResource
{
    const CREATE = 'create';
    const UPDATE = 'update';
    const DELETE = 'delete';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $status = $this->mapStatus();
        $finishedAt = "<em>Not finished</em>";
        if ($this->completed_at) {
            $finishedAt = Carbon::createFromTimeString($this->completed_at)->diffForHumans();
        }

        $triggeredAt = Carbon::createFromTimeString($this->created_at)->diffForHumans();

        $ean = ChannelProduct::where('id',$this->product_id)->value('ean');

        return [
            'itemId' => $this->product_id,
            'ean' => $ean,
            'event' => $this->event,
            'status' => $status,
            'tries' => $this->tries,
            'triggeredAt' => $this->created_at,
            'finishedAt' => $this->completed_at
        ];
    }

    private function mapEvent(): ?string
    {
        return [
                self::CREATE => '<span class="label label-success">CREATE</span>',
                self::UPDATE => '<span class="label label-info">UPDATE</span>',
                self::DELETE => '<span class="label label-warning">DELETE</span>',
            ][$this->sync_event] ?? "";
    }

    private function mapStatus(): string
    {
        if ($this->status == 0) {
            $status = "<strong style='color: #0a6aa1;font-size: smaller;'>PENDING</strong>";
        }
        elseif ($this->status == 1) {
            $status = "<strong style='color: #0a6aa1;font-size: smaller;'>WORKING</strong>";
        } elseif ($this->status == 3) {
            $status = "<strong style='color: darkred;font-size: smaller;'>FAILED</strong>";
        }else {
            $status = "<strong style='color: #0B7500;font-size: smaller;'>SUCCESS</strong>";
        }
        return $status;
    }
}
