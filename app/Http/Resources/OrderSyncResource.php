<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class OrderSyncResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {

        $app_interval = DRM_Inverval_App_Minute($this->user_id, config('global.interval_app_id'));

        $next = ($this->status != 0)? Carbon::createFromTimestamp($this->end_time)->addMinutes($app_interval)->diffForHumans() : '--';
        $end = ($this->status != 0)? Carbon::createFromTimestamp($this->end_time)->toDateTimeString() : '--';

        $retry_btn = ($this->status == 2)? '<a href="#" class="order_sync_retry" title="Retry" data-id="'.$this->id.'"><i class="fa fa fa-refresh"></i></a>' : '';
        $error_message = ($this->report)? $this->report : 'During the last sync. your store was not available as expected. Do not worry, we will try again soon.';
        $status_code = [
          0 => '<span class="label label-info">Working</span>',
          1 => '<span class="label label-success">Finish</span>',
          2 => '<span class="label label-danger" data-toggle="tooltip" title="'.$error_message.'">Failed</span> '.$retry_btn,
        ];

        $status_label = isset($status_code[$this->status]) ?  $status_code[$this->status] : '';

        return[
          'shop_name' => $this->shop_name,
          'start' => Carbon::createFromTimestamp($this->start_time)->toDateTimeString(),
          'end' =>  $end,
          'next' => $next,
          'fetch' => $this->item,
          'status' => $status_label,
          'sort' => $this->start_time,
        ];
    }
}
