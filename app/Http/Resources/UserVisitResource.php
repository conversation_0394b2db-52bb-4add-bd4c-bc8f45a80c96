<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class UserVisitResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'date' => \Carbon\Carbon::parse($this->created_at)->format('d-m-Y'),
            'country_city' => getCountry($this->geo_location),
            'ip_address' => $this->ip_address,
            'device' => $this->device . ' [ ' . $this->os . ' ] ',
            'browser' => $this->browser,
            'language' => config('global.languages')[substr($this->language, 0, 2)],
            'page_views' => getPageViews($this->user_id, $this->created_at),
            'last_activity' => getLastActivity($this->user_id, $this->created_at),
            'total_time_spent' => getTotalTimeSpent($this->user_id, $this->created_at),
        ];
    }
}
