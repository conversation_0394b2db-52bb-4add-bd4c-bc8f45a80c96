<?php

namespace App\Http\View\Composers;

use Illuminate\View\View;
use App\Http\View\Repository\GlobalComposerRepository;

class GlobalComposer
{

    private $repository;

    public function __construct(GlobalComposerRepository $repository)
    {
        $this->repository = $repository;
    }

    /**
     * Bind data to the view.
     *
     * @param  View  $view
     * @return void
     */
    public function compose(View $view)
    {
        $data = $this->repository->data;
        $view->with($data);
    }
}
