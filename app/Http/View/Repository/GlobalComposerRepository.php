<?php

namespace App\Http\View\Repository;

use Illuminate\Support\Facades\DB;
use Illuminate\View\View;
use crocodicstudio\crudbooster\helpers\CRUDBooster;
use Illuminate\Support\Facades\Cache;

class GlobalComposerRepository
{
	public $data = [];

	public function setData($data)
	{
		$this->data = $data;
	}

	public function data()
	{
		$drm_my_id = CRUDBooster::myId();
        //if not logged in return empty array
        if(empty($drm_my_id)) return [];

        //Has intro js
        $introjs_status = 'no';
        $has_intro_js = (bool) request()->is(['admin', 'admin/drm_projects', 'admin/app-list']);
        if($has_intro_js){
            $current_path = request()->path();
            $user_introjs = DB::table('user_introjs')->where('user_id', $drm_my_id)->where('path', $current_path)->exists();
            if(!$user_introjs){
                $introjs_status = 'yes';
            }
        }
        //Intro js check end

        $drm_my_parent_id = CRUDBooster::myParentId();
        //DRM channel lang
        $country_id = app('App\Services\UserService')->getProductCountry($drm_my_parent_id);
        $lang = app('App\Services\UserService')->getProductLanguage($country_id);

        $languages = in_array($drm_my_parent_id, [98, 216, 71]) ? ['de','en','es'] : ['de','en'];

        $all_languages = $languages;
        $all_languages[] = $lang;
        $all_languages = array_unique($all_languages);

        $langs = DB::table('countries')->whereIn('language_shortcode' , $all_languages)->get();
        $header_languages = collect($langs ?? [])->whereIn('language_shortcode', $languages)->whereIn('id',[1,2,8])->values();

        $current_header_lang = collect($header_languages)->firstWhere('language_shortcode', config('app.locale'));

        //Channel flag
        $channel_lang = collect($langs ?? [])->firstWhere('id', $country_id);

        $drm_channel_flag = $channel_lang && $channel_lang->flag ? url($channel_lang->flag) : null;
// dd($this->orderColorCss($drm_my_parent_id));
        return [
            'isSuperadmin'  => CRUDBooster::isSuperadmin(),
            'isKeyAccount'  => CRUDBooster::isKeyAccount(),
            'logged_as_old'   => session()->get('logged_as_old'),
            'logged_as'   => session()->get('logged_as'),
            'isLocal' => isLocal(),
            'drm_my_id' => $drm_my_id,
            'drm_my_parent_id' => $drm_my_parent_id,
            'myName' => CRUDBooster::myName(),
            'myPhoto' => CRUDBooster::myPhoto(),
            'myPrivilegeId' => CRUDBooster::myPrivilegeId(),

            //HEADER DATA
            'header_languages' => $header_languages,
            'current_header_lang' => $current_header_lang,
            'header_notification_count' => getHeaderNewNotification('count') ?? 0,
            'introjs_status' => $introjs_status,

            //PRODUCT_DATA
            'drm_channel_flag' => $drm_channel_flag,
            'drm_channel_language_shortcode' => $lang,

            //Due payment invoices
            'due_invoice_list' => \DRM::duePaymentInvoiceList(),

            'user_order_color_css' => $this->orderColorCss($drm_my_parent_id),

            'relavida_user_type' => session()->get('relavida_user_type'),
            'is_dt_entry_user' => is_dt_entry_user(),
            'video_play_switch' => is_video_play_switch_checked(),
        ];
	}


    private function orderColorCss($user_id)
    {
        return Cache::rememberForever('user_order_color_css_'.$user_id, function () use ($user_id) {
            $data = DB::table('order_colors')->where('user_id', $user_id)->whereNotNull('color')->pluck('color', 'status')->toArray();

            if(!isset($data['zustellproblem']))
            {
                $data['zustellproblem'] = 'red';
            }

            $css = '';
            foreach($data as $status => $color)
            {
                $css .= '.user_order_color_'.$status.'{background-color: '.$color.'!important;} ';
                $css .= '.order-status-option-color.'.$status.'{background-color: '.$color.';width: 10px;height: 10px;}';
            }

            return $css;
        });
    }
}
