<?php

namespace App\Interfaces;

interface BikeApiInterface {
    public function getAllProducts($pages);
    public function getProductById($id);
    public function productListArguments();
    public function productsPerSegment($segment);
    public function v2FetchChangedProductsForNDays($pagesArr, $days);
    public function fetchStockChangesInTheLastNMinutes($minutes = 5);
    public function fetchStockPerProductById($id);
    public function listAllTrackingCodeForUser();
    public function fetchTrackingCodeById($id);
    public function listAllOrdersForUser();
    public function fetchOrderById($id);
    public function submitAnOrder();
    public function fetchOrdersForlastNDays($days);
}
