<?php

namespace App\Jobs\ChannelManager;

use App\Models\ChannelProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

use App\Services\ChannelProductService;

class ChangeChannelProductConnectionStatus implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 0;
    public int $tries = 3;
    public ?int $product_id;
    public array $errors;
    public bool $sync;
    public array $data;

    /**
     * Create a new job instance.
     *
     * @param $product_id
     * @param array $errors
     * @param array $data
     * @param bool $sync
     */
    public function __construct($product_id, array $errors = [], array $data = array(),bool $sync = false)
    {
        if($product_id instanceof ChannelProduct){
            $this->product_id = $product_id->id;
        }else{
            $this->product_id = $product_id;
        }

        $this->errors = $errors;
        $this->sync = $sync;
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle()
    {
        app(ChannelProductService::class)->changeConnectionStatus($this->product_id,$this->errors,$this->data,$this->sync);
    }

    public function tags()
    {

    }
}
