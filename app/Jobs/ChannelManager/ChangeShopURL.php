<?php

namespace App\Jobs\ChannelManager;

use App\Models\ChannelProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ChangeShopURL implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $user_id;
    protected $shop;
    protected $old_url;

    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id,$shop,$old_url)
    {
        $this->shop = $shop;
        $this->old_url = $old_url;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $url = trim($this->shop->url,'/').'/'.'api/v1/get-all-product-images';

        $products = ChannelProduct::where([
            'user_id' => $this->user_id,
            'channel' => $this->shop->channel,
            'is_connected' => 1
        ])->cursor();

        foreach ($products->chunk(300) as $chunks) {
            $ids = $chunks->pluck('id')->toArray();
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => array('ids' => json_encode($ids)),
                CURLOPT_HTTPHEADER => array(
                    'userToken: '.$this->shop->username,
                    'userPassToken: '.$this->shop->password
                ),
            ));

            $response = json_decode(curl_exec($curl),true);
            curl_close($curl);
            if(!is_array($response['images'])){
                $response['images'] = json_decode($response['images'],true);
            }

            foreach($chunks as $product) {
                $productImages =  $response['images'][$product->id] ?? array();
                $metadata = $product->metadata;
                $metadata['images'] = $productImages;
                $metadata['url'] = str_replace(trim($this->old_url,'/'),trim($this->shop->url,'/'),$metadata['url']);
                $product->metadata = $metadata;
                $product->save();
            }
        }
    }

    public function tags()
    {

    }
}
