<?php

namespace App\Jobs\ChannelManager;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class Disconnect implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected int $user_id;
    protected int $shop_id;
    protected bool $incomplete;
    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $ids, int $shop_id, int $user_id, bool $incomplete = false)
    {
        $this->ids = $ids;
        $this->shop_id = $shop_id;
        $this->user_id = $user_id;
        $this->incomplete = $incomplete;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        app('\App\Services\ChannelProductService')->deleteFromShop(
            $this->ids,
            $this->shop_id,
            $this->user_id,
            $this->incomplete
        );
    }

    public function tags()
    {

    }
}
