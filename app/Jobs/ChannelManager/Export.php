<?php

namespace App\Jobs\ChannelManager;

use App\Enums\Channel;
use App\Shop;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class Export implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected int $user_id;
    protected int $shop_id;
    protected array $additional;
    protected string $event;
    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $ids, int $shop_id, int $user_id, string $event, array $additional)
    {
        $this->ids = $ids;
        $this->shop_id = $shop_id;
        $this->user_id = $user_id;
        $this->event = $event;
        $this->additional = $additional;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        $channel = Shop::where('id',$this->shop_id)->value('channel');
        if(in_array($channel,Channel::CSV_CHANNELS) && $this->event == 'update' && $channel!=Channel::TRADEBYTE && $channel!=Channel::LIMANGO && $channel != Channel::COLIZEY){
            return;
        }else{
            app('\App\Services\ChannelProductService')->exportToShop(
                $this->ids,
                $this->shop_id,
                $this->user_id,
                $this->event,
                $this->additional
            );
        }
    }

    public function tags(): array
    {
        return ['Export (Shop: '.$this->shop_id.' User: '.$this->user_id.')'];
    }
}
