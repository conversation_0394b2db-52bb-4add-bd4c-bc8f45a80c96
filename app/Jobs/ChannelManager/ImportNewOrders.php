<?php

namespace App\Jobs\ChannelManager;

use App\Http\Controllers\AdminDrmImportsController;
use App\Shop;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;

class ImportNewOrders implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $import_pid;
    
    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($import_pid)
    {
        $this->import_pid = $import_pid;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $import_info = DB::table('import_orders_histories')
            ->where('id', $this->import_pid)
            ->select('shop_id', 'file_path')
            ->first();
        
        $file = public_path($import_info->file_path);
        $type = pathinfo($file, PATHINFO_EXTENSION);

        $rows = (new AdminDrmImportsController)->csvToArray($file, $type, 'auto', false);

        if (!empty($rows) && is_array($rows)) {
            $this->prepareOrderFormat($import_info->shop_id, $rows);
        }

        return true;
    }

    public function prepareOrderFormat($shop_id, $rows) {
        $all_orders = [];

        foreach ($rows as $row) {
            $order = [];

            $order['AmazonOrderId'] = $row['order-id'];
            $order['OrderTotal']['Amount'] = $row['item-price'] * $row['quantity-purchased']; // OrderTotal
            $order['OrderTotal']['CurrencyCode'] = $row['currency'];

            if (!empty($row['buyer-email'])) {
                $order['BuyerEmail'] = $row['buyer-email'];

                // shipping address
                $ship_address_arr = [ 
                    $row['ship-address-1'],
                    $row['ship-address-2'],
                    $row['ship-address-3'],
                ];

                $shippingAddress['Name'] = $row['recipient-name'] ?? null;
                $shippingAddress['Street'] = $row['ship-address-1'];
                $shippingAddress['Address'] = implode(', ', array_filter($ship_address_arr));
                $shippingAddress['Phone'] = $row['ship-phone-number'] ?? null;
                $shippingAddress['City'] = $row['ship-city'];
                $shippingAddress['StateOrRegion'] = $row['ship-state'];
                $shippingAddress['PostalCode'] = $row['ship-postal-code'];
                $shippingAddress['CountryCode'] = $row['ship-country'];
                $order['ShippingAddress'] = $shippingAddress;


                // billing address
                $bill_address_arr = [ 
                    $row['bill-address-1'],
                    $row['bill-address-2'],
                    $row['bill-address-3'],
                ];

                $billingAddress['Name'] = $row['bill-name'] ?? null;
                $billingAddress['Street'] = $row['bill-address-1'];
                $billingAddress['Address'] = implode(', ', array_filter($bill_address_arr));
                $billingAddress['Phone'] = $row['bill-phone-number'] ?? null;
                $billingAddress['City'] = $row['bill-city'];
                $billingAddress['StateOrRegion'] = $row['bill-state'];
                $billingAddress['PostalCode'] = $row['bill-postal-code'];
                $billingAddress['CountryCode'] = $row['bill-country'];
                $order['BillingAddress'] = $billingAddress;
            }

            $order['PurchaseDate'] = $row['purchase-date'];
            $order['Subtotal']['value'] = $row['item-price'] * $row['quantity-purchased']; // Subtotal
            $order['PaymentMethodDetails']['PaymentMethodDetail'] = null; // PaymentMethodDetails
            $order['OrderStatus'] = null; // OrderStatus

            $orderItems['QuantityOrdered'] = $row['quantity-purchased'];
            $orderItems['ItemPrice']['Amount'] = $row['item-price'];
            $orderItems['OrderItemId'] = $row['order-item-id'];
            $orderItems['Title'] = $row['product-name'];
            $orderItems['ASIN'] = $row['asin'] ?? null; // ASIN
            $orderItems['SellerSKU'] = $row['sku'];
            $orderItems['ItemTax']['Amount'] =  $row['item-tax'];
            $orderItems['PromotionDiscount']['Amount'] = 0;

            $order['orderItems'][0] = $orderItems;
            $all_orders[] = $order;
        }

        if (!empty($all_orders)) {
            DB::table('import_orders_histories')->where('id', $this->import_pid)
                ->update([
                    'start_at' => now(),
                    'total_orders' => count($rows),
                ]);

            $shop = Shop::find($shop_id);
            app('App\Http\Controllers\NewShopSyncController')->bulkOrderInsertAmazon($shop, $all_orders, null, $this->import_pid);
        }
    }
}
