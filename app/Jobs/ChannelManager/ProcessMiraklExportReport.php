<?php

namespace App\Jobs\ChannelManager;

use App\Services\Modules\Export\Mirakl\MiraklChannelManager;
use Illuminate\Bus\Queueable;
use App\Models\ChannelProduct;
use Illuminate\Queue\SerializesModels;
use App\Services\Modules\Export\Colizey;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Enums\ChannelProductConnectedStatus;

class ProcessMiraklExportReport implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $shop_id;

    public int $import_id;

    public array $product_ids;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($shop_id,$import_id,$product_ids)
    {
        $this->shop_id = $shop_id;
        $this->import_id = $import_id;
        $this->product_ids = $product_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $mirakl = new MiraklChannelManager();
        $service = $mirakl($this->shop_id);
        $report = $service->getErrorReport($this->import_id);
        $service->processExportReport($this->product_ids,$report);
    }
}
