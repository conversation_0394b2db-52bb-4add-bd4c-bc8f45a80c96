<?php

namespace App\Jobs\ChannelManager;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use League\Csv\Exception;

class SyncDTCategories implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected array $ids;
    protected int $user_id;
    public int $timeout = 0;
    public int $tries = 3;
    protected int $shop_id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($ids, $shop_id,$user_id)
    {
        $this->ids = $ids;
        $this->user_id = $user_id;
        $this->shop_id = $shop_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Exception
     */
    public function handle()
    {
        app('\App\Services\ChannelProductService')->createSyncHistory($this->ids,$this->shop_id,$this->user_id);
    }

    public function tags()
    {

    }
}
