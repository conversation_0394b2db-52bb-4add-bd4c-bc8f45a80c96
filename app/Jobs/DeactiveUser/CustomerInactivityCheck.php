<?php

namespace App\Jobs\DeactiveUser;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\DeactiveUser\CheckUser;
use App\Services\DeactiveUser\ReportTrait;
use App\Notifications\DRMNotification;

class CustomerInactivityCheck implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ReportTrait;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{
            app(CheckUser::class)->inactiveAction(); 
        }catch(\Exception $e){
            \App\User::find(71)->notify(new DRMNotification('Inactive user action error: ' . $e->getMessage() . ' Line:' . $e->getLine(), '', '#'));
        }
        
    }

    //Tags
    public function tags()
    {
        return ['Inactive customer check'];
    }
}
