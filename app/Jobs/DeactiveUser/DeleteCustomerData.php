<?php

namespace App\Jobs\DeactiveUser;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\DeactiveUser\BackupUserData;
use App\Services\DeactiveUser\ReportTrait;

class DeleteCustomerData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ReportTrait;

    //user id
    protected $user_id;
    protected $uuid;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id, $uuid)
    {
        $this->user_id = $user_id;
        $this->uuid = $uuid;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{

            $backup = new BackupUserData($this->user_id, $this->uuid);
            $backup->removeSqlData();
            
        }catch(\Exception $e){
            $this->sendReport($this->user_id, $this->uuid, $e->getMessage(), 0);
        }
    }

    //Tags
    public function tags()
    {
        return ['Delete user data. ID: '.$this->user_id.' UUID: '. $this->uuid];
    }
}
