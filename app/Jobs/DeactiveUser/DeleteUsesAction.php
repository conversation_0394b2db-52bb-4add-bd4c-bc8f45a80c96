<?php

namespace App\Jobs\DeactiveUser;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\DeactiveUser\DeleteUser;
use App\Services\DeactiveUser\ReportTrait;

class DeleteUsesAction implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, ReportTrait;

    //user id
    protected $user_id;
    protected $uuid;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user_id, $uuid)
    {
        $this->user_id = $user_id;
        $this->uuid = $uuid;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try{

            $delete_user = new DeleteUser($this->user_id, $this->uuid);
            $delete_user->action();

        }catch(\Exception $e){
            $this->sendReport($this->user_id, $this->uuid, $e->getMessage(), 0);
        }

    }

    //Tags
    public function tags()
    {
        return ['Delete user action. ID: '.$this->user_id.' UUID: '. $this->uuid];
    }
}
