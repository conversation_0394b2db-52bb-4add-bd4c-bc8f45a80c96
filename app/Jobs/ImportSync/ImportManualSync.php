<?php

namespace App\Jobs\ImportSync;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class ImportManualSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user_id;
    protected $import;
    public $event;
    public $timeout = 0;
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @param $import
     * @param $user_id
     * @param string $event
     */
    public function __construct($import,$user_id,$event = 'auto')
    {
        $this->import = $import;
        $this->user_id = $user_id;
        $this->event = $event;
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws Throwable
     */
    public function handle()
    {
        try {
            if($this->event == 'auto')
            {
                $url = config('import.base_url').config('import.manual_url_sync');
            }
            else {
                $url = config('import.base_url').config('import.manual_file_sync');
            }

            $data = [
                'import_id' 	=> $this->import->id,
                'user_id'       => $this->user_id
            ];

            $ch = curl_init();
            $headers = array("accept: application/json","content-type: application/json");

            curl_setopt_array($ch, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_HEADER => 1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_HTTPHEADER => $headers,
                CURLOPT_POSTFIELDS => json_encode($data),
                CURLOPT_FOLLOWLOCATION => true
            ));

            $output = curl_exec($ch);

        } catch (Throwable $th) {
            throw $th;
        }
    }

    public function tags()
    {

    }
}
