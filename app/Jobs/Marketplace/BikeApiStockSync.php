<?php

namespace App\Jobs\Marketplace;

use App\Http\Controllers\Marketplace\BikeApiController;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class BikeApiStockSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $minutes;
    public $tries = 3;
    public $timeout = 3500;
    public function __construct($minutes = 5)
    {
        $this->minutes = $minutes;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        app(BikeApiController::class)
            ->fetchStockChangesInTheLastNMinutes($this->minutes);
    }
}
