<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Marketplace\Product;


class DuplicateEanProductActive implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $eans;
    protected $country_id;


    public function __construct($eans, $country_id)
    {
        $this->eans = $eans;
        $this->country_id = $country_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $duplicate_eans = $this->eans;
        $country_id = $this->country_id;

        // Batch update all products with duplicate EANs to mark them as duplicates
        Product::where('country_id', $country_id)
            ->whereIn('ean', $duplicate_eans)
            ->whereNotIn('status', [2, 7, 5])
            ->update([
                'status' => 0,
                'is_dublicate' => 1,
                'best_price' => 0
            ]);

        foreach ($duplicate_eans as $duplicate_ean) {
            $active_priority_product = Product::with('mainCategory:id,im_handel')
                ->where('country_id', $country_id)
                ->where('ean', $duplicate_ean)
                ->whereNotIn('status', [2, 7, 5])
                ->where('shipping_method', 2)
                ->where('internel_stock', '>=', 1)
                ->where('vk_price', '<>', 0)
                ->select('id','ean','best_price','vk_price','ek_price','shipping_cost','shipping_method','stock','internel_stock','status','category_id')
                ->orderByRaw('`ek_price` + `shipping_cost`')
                ->first();

            if (!$active_priority_product) {
                $active_priority_product = Product::with('mainCategory:id,im_handel')
                    ->where('country_id', $country_id)
                    ->where('ean', $duplicate_ean)
                    ->whereNotIn('status', [2, 7])
                    ->where('shipping_method', 1)
                    ->where('stock', '>=', 1)
                    ->where('vk_price', '<>', 0)
                    ->select('id','ean','best_price','vk_price','ek_price','shipping_cost','shipping_method','stock','internel_stock','status','category_id')
                    ->orderByRaw('`ek_price` + `shipping_cost`')
                    ->first();
            }

            if (!$active_priority_product) {
                continue;
            }

            $duplicate_ids = Product::where('country_id', $country_id)
                ->where('id', '<>', $active_priority_product->id)
                ->where('ean', $duplicate_ean)
                ->pluck('id')
                ->push($active_priority_product->id)
                ->toArray();

            Product::whereIn('id', $duplicate_ids)->update(['category_id' => $active_priority_product->category_id ?? 68]);
            dispatch(new MarketplaceIMHandelSync($duplicate_ids, $active_priority_product->mainCategory->im_handel ?? 0.00));

            // Update the best-priced product
            $active_priority_product->update([
                'status' => 1,
                'best_price' => 1
            ]);
        }
    }

}
