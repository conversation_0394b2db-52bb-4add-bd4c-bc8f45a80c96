<?php

namespace App\Jobs\Marketplace;

use App\Models\Marketplace\Product;
use App\Services\Marketplace\InternelSyncService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class InternelSyncJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public $productsIds;
    public function __construct(array $productsIds)
    {
        $this->productsIds = $productsIds;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $result = app( InternelSyncService::class )
            ->transferIncomingDeliveries($this->productsIds);
    }
}
