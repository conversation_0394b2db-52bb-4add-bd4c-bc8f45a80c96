<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use DB;
use App\Traits\PlaceOrder;
use App\Traits\InvoiceNumber;

class MarketplacePreOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    use InvoiceNumber,PlaceOrder;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $pre_orders = DB::connection('marketplace')->table('pre_orders')->where('product_availability',0)->where('payment_status',1)->get();
       //dd($pre_orders);
       
        foreach($pre_orders as $order){
           
            $products = DB::connection('marketplace')->table('marketplace_products')->where('id',$order->product_id)->where('stock','>=',$order->quantity)->first();
            if(!empty($products)){
                $profomaup = DB::table('new_orders')->where('id',$order->order_id)->update([
                    'invoice_number' => 1
                ]);
    
                $profoma = DB::table('new_orders')->where('id',$order->order_id)->first();
                
                if($profoma->invoice_number > 0){

                    app('App\Http\Controllers\AdminDrmAllOrdersController')->preOrderSupplier($profoma->id);
                    //$this->getOrderSupplier($profoma->id);
                }

                DB::connection('marketplace')->table('pre_orders')->where('id',$order->id)->update([
                    "product_availability"=>1
                ]);
    
            }
    
          
           
        }

        return true;
    }
}
