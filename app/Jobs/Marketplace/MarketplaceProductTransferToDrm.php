<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class MarketplaceProductTransferToDrm implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $tranferable_ids;
    protected $category_id;
    protected $is_trail;
    protected $user_id;
    protected $country_id;

    public function __construct($tranferable_ids = [], $user_id = null, $category_id = null, $is_trail = false, $country_id = 1)
    {
        $this->tranferable_ids = $tranferable_ids;
        $this->category_id = $category_id;
        $this->is_trail = $is_trail;
        $this->user_id = $user_id;
        $this->country_id = $country_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if( $this->is_trail ){
            app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferUnlimitedProductsToDrm($this->tranferable_ids, $this->user_id, $this->category_id);
        } else {
            app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->transferTarifLimitedProductsToDrm($this->tranferable_ids, $this->user_id, $this->category_id);
        }
    }
}
