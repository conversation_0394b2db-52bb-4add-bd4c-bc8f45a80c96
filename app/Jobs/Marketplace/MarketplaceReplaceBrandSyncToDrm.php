<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class MarketplaceReplaceBrandSyncToDrm implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $products;
    protected $brand;

    public function __construct($products = [], $brand = null)
    {
        $this->products = $products;
        $this->brand = $brand;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $mp_product_ids = [];
        foreach($this->products as $product){
            $mp_product_ids[] = $product->id;
            if(!empty($product->core_products)){
                foreach($product->core_products as $drm_product){
                    $user_id = $drm_product->user_id;
                    $drmBrand = \App\DropmatixProductBrand::where(\DB::raw('UPPER(brand_name)'), strtoupper($this->brand->brand_name) )->where('user_id', $user_id)->first() ?? \App\DropmatixProductBrand::create([
                        'brand_name' => $this->brand->brand_name,
                        'user_id' => $user_id,
                        'brand_logo' => ["https:\/\/drm.software\/Marketing_assets\/img\/no-product-image.jpg"]
                    ]);
        
                    $update_status = json_decode($drm_product->update_status, 1);
                    if($update_status['brand'] == 1 && $drm_product->brand != $drmBrand->id){
                        $updateableColumn['brand'] = $drmBrand->id;
                        app(\App\Services\DRMProductService::class)->update($drm_product->id, $updateableColumn);
                        Log::info('Update brand on this product ' . $drm_product->id);
                    }
                }
            }
        }
        \App\Models\Marketplace\Product::whereIn('id', $mp_product_ids)->update(['brand' => $this->brand->id]);
        Log::info('Update brand on products ' . count($mp_product_ids));
    }
}
