<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Models\Marketplace\Product;
use App\DrmProduct;

class MarketplaceZeroShippingProductSync implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    protected $user_id;
    protected $tariff_id;


    public function __construct($user_id, $tariff_id = null)
    {
        $this->user_id   = $user_id;
        $this->tariff_id = $tariff_id;
    }
   
    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $marketplace_products = [];
        $drm_shipping_condition = ($this->tariff_id == 27) ? '<>' : '=';

        $drmProducts = DrmProduct::where('user_id', $this->user_id)
            ->where('shipping_cost', $drm_shipping_condition, 0)
            ->whereNotNull('marketplace_product_id')
            ->pluck('marketplace_product_id', 'id')
            ->toArray();

        if ($drmProducts) {
            foreach (array_chunk($drmProducts, 10000, true) as $marketplace_ids) {
                $marketplace_products_chunk = Product::whereIn('id', $marketplace_ids)
                    ->where('real_shipping_cost', 0)
                    ->pluck('shipping_cost', 'id')
                    ->toArray();
                
                $marketplace_products += $marketplace_products_chunk;
            }

            if ($marketplace_products) {
                $convert_drm_products = array_flip($drmProducts);
                foreach ($marketplace_products as $marketplace_product_id => $shipping_cost) {
                    $updateableColumns['shipping_cost'] = ($this->tariff_id == 27) ? 0.00 : $shipping_cost;
                    app(\App\Services\DRMProductService::class)->update($convert_drm_products[$marketplace_product_id], $updateableColumns);
                }
            }
        }
    }
}
    