<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class OrderStatusUpdating<PERSON>ob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    /*
     * This job is for update the status of all synced Orders
     * */
    public function handle()
    {
        // $orders = DB::table('marketplace_synced_orders')->all();
        $orders = \App\Models\Marketplace\MarketplaceSyncedOrder::all();
    }
}
