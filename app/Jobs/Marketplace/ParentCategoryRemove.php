<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;

class ParentCategoryRemove implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    private $parentCategoryId;
    private $user_id;


    public function __construct($parentCategoryId, $user_id)
    {
        $this->parentCategoryId = $parentCategoryId;
        $this->user_id = $user_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        return app(\App\Http\Controllers\Marketplace\MarketPlaceController::class)->deleteSelectedParentCategory($this->parentCategoryId, $this->user_id);
    }
}
