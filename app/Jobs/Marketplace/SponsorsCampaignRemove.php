<?php

namespace App\Jobs\Marketplace;

use App\DrmProduct;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Models\Marketplace\MarketplaceSponsor;


class SponsorsCampaignRemove implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */


    public function __construct()
    {

    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        MarketplaceSponsor::where('status', 1)->whereDate('end_date', '<', date('Y-m-d'))->update([
            'status' => 0,
        ]);
    }
}
