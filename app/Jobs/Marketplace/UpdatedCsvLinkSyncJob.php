<?php

namespace App\Jobs\Marketplace;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class UpdatedCsvLinkSyncJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct()
    {

    }
    public function handle()
    {
        $collectionIds = \App\Models\Marketplace\Collection::whereNotNull('updated_original_csv_link')
                      ->pluck('id')
                      ->toArray();
        foreach ($collectionIds as $collectionId) {
            app(\App\Http\Controllers\AdminMarketplaceCollectionsController::class)
                ->manualUpdateSyncFromLink($collectionId);
        }
    }
}
