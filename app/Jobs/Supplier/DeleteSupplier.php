<?php

namespace App\Jobs\Supplier;

use App\Services\SupplierService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DeleteSupplier implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected int $supplier_id;
    protected int $user_id;

    public int $timeout = 0;
    public int $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($supplier_id,$user_id)
    {
        $this->user_id =   $user_id;
        $this->supplier_id = $supplier_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        app(SupplierService::class)->destroy($this->supplier_id,$this->user_id);
    }

    public function tags(): array
    {
        return ['Supplier delete'];
    }
}
