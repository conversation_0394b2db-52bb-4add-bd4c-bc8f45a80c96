<?php

namespace App\Jobs\UniversalExport;

use App\Services\UniversalExportService;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;


class SyncFeed implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 0;
    public $tries = 3;
    protected $user_id;
    protected $feed_id;

    /**
     * Create a new job instance.
     *
     * @param $user_id
     * @param $feed_id
     */
    public function __construct($user_id,$feed_id)
    {
        $this->user_id = $user_id;
        $this->feed_id = $feed_id;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        app(UniversalExportService::class)->syncFeed($this->user_id,$this->feed_id);
    }
}